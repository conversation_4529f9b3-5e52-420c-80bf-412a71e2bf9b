{"ast": null, "code": "import { QrCode, QrSegment } from \"../libs/qrcodegen\";\nimport { ERROR_LEVEL_MAP, getImageSettings, getMarginSize } from \"../utils\";\nimport { useMemo } from 'react';\nexport function useQRCode(_ref) {\n  var value = _ref.value,\n    level = _ref.level,\n    minVersion = _ref.minVersion,\n    includeMargin = _ref.includeMargin,\n    marginSize = _ref.marginSize,\n    imageSettings = _ref.imageSettings,\n    size = _ref.size;\n  var qrcode = useMemo(function () {\n    var segments = QrSegment.makeSegments(value);\n    return QrCode.encodeSegments(segments, ERROR_LEVEL_MAP[level], minVersion);\n  }, [value, level, minVersion]);\n  var _useMemo = useMemo(function () {\n      var cs = qrcode.getModules();\n      var mg = getMarginSize(includeMargin, marginSize);\n      var ncs = cs.length + mg * 2;\n      var cis = getImageSettings(cs, size, mg, imageSettings);\n      return {\n        cells: cs,\n        margin: mg,\n        numCells: ncs,\n        calculatedImageSettings: cis\n      };\n    }, [qrcode, size, imageSettings, includeMargin, marginSize]),\n    cells = _useMemo.cells,\n    margin = _useMemo.margin,\n    numCells = _useMemo.numCells,\n    calculatedImageSettings = _useMemo.calculatedImageSettings;\n  return {\n    qrcode: qrcode,\n    margin: margin,\n    cells: cells,\n    numCells: numCells,\n    calculatedImageSettings: calculatedImageSettings\n  };\n}", "map": {"version": 3, "names": ["QrCode", "QrSegment", "ERROR_LEVEL_MAP", "getImageSettings", "getMarginSize", "useMemo", "useQRCode", "_ref", "value", "level", "minVersion", "<PERSON><PERSON><PERSON><PERSON>", "marginSize", "imageSettings", "size", "qrcode", "segments", "makeSegments", "encodeSegments", "_useMemo", "cs", "getModules", "mg", "ncs", "length", "cis", "cells", "margin", "num<PERSON>ells", "calculatedImageSettings"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@rc-component/qrcode/es/hooks/useQRCode.js"], "sourcesContent": ["import { QrCode, QrSegment } from \"../libs/qrcodegen\";\nimport { ERROR_LEVEL_MAP, getImageSettings, getMarginSize } from \"../utils\";\nimport { useMemo } from 'react';\nexport function useQRCode(_ref) {\n  var value = _ref.value,\n    level = _ref.level,\n    minVersion = _ref.minVersion,\n    includeMargin = _ref.includeMargin,\n    marginSize = _ref.marginSize,\n    imageSettings = _ref.imageSettings,\n    size = _ref.size;\n  var qrcode = useMemo(function () {\n    var segments = QrSegment.makeSegments(value);\n    return QrCode.encodeSegments(segments, ERROR_LEVEL_MAP[level], minVersion);\n  }, [value, level, minVersion]);\n  var _useMemo = useMemo(function () {\n      var cs = qrcode.getModules();\n      var mg = getMarginSize(includeMargin, marginSize);\n      var ncs = cs.length + mg * 2;\n      var cis = getImageSettings(cs, size, mg, imageSettings);\n      return {\n        cells: cs,\n        margin: mg,\n        numCells: ncs,\n        calculatedImageSettings: cis\n      };\n    }, [qrcode, size, imageSettings, includeMargin, marginSize]),\n    cells = _useMemo.cells,\n    margin = _useMemo.margin,\n    numCells = _useMemo.numCells,\n    calculatedImageSettings = _useMemo.calculatedImageSettings;\n  return {\n    qrcode: qrcode,\n    margin: margin,\n    cells: cells,\n    numCells: numCells,\n    calculatedImageSettings: calculatedImageSettings\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,UAAU;AAC3E,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAO,SAASC,SAASA,CAACC,IAAI,EAAE;EAC9B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,UAAU,GAAGH,IAAI,CAACG,UAAU;IAC5BC,aAAa,GAAGJ,IAAI,CAACI,aAAa;IAClCC,UAAU,GAAGL,IAAI,CAACK,UAAU;IAC5BC,aAAa,GAAGN,IAAI,CAACM,aAAa;IAClCC,IAAI,GAAGP,IAAI,CAACO,IAAI;EAClB,IAAIC,MAAM,GAAGV,OAAO,CAAC,YAAY;IAC/B,IAAIW,QAAQ,GAAGf,SAAS,CAACgB,YAAY,CAACT,KAAK,CAAC;IAC5C,OAAOR,MAAM,CAACkB,cAAc,CAACF,QAAQ,EAAEd,eAAe,CAACO,KAAK,CAAC,EAAEC,UAAU,CAAC;EAC5E,CAAC,EAAE,CAACF,KAAK,EAAEC,KAAK,EAAEC,UAAU,CAAC,CAAC;EAC9B,IAAIS,QAAQ,GAAGd,OAAO,CAAC,YAAY;MAC/B,IAAIe,EAAE,GAAGL,MAAM,CAACM,UAAU,CAAC,CAAC;MAC5B,IAAIC,EAAE,GAAGlB,aAAa,CAACO,aAAa,EAAEC,UAAU,CAAC;MACjD,IAAIW,GAAG,GAAGH,EAAE,CAACI,MAAM,GAAGF,EAAE,GAAG,CAAC;MAC5B,IAAIG,GAAG,GAAGtB,gBAAgB,CAACiB,EAAE,EAAEN,IAAI,EAAEQ,EAAE,EAAET,aAAa,CAAC;MACvD,OAAO;QACLa,KAAK,EAAEN,EAAE;QACTO,MAAM,EAAEL,EAAE;QACVM,QAAQ,EAAEL,GAAG;QACbM,uBAAuB,EAAEJ;MAC3B,CAAC;IACH,CAAC,EAAE,CAACV,MAAM,EAAED,IAAI,EAAED,aAAa,EAAEF,aAAa,EAAEC,UAAU,CAAC,CAAC;IAC5Dc,KAAK,GAAGP,QAAQ,CAACO,KAAK;IACtBC,MAAM,GAAGR,QAAQ,CAACQ,MAAM;IACxBC,QAAQ,GAAGT,QAAQ,CAACS,QAAQ;IAC5BC,uBAAuB,GAAGV,QAAQ,CAACU,uBAAuB;EAC5D,OAAO;IACLd,MAAM,EAAEA,MAAM;IACdY,MAAM,EAAEA,MAAM;IACdD,KAAK,EAAEA,KAAK;IACZE,QAAQ,EAAEA,QAAQ;IAClBC,uBAAuB,EAAEA;EAC3B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
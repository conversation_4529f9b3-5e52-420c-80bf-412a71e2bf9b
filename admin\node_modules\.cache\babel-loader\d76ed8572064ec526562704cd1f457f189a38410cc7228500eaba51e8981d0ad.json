{"ast": null, "code": "import { isValidElement, useMemo } from 'react';\nconst useTooltipProps = (tooltip, editConfigText, children) => useMemo(() => {\n  if (tooltip === true) {\n    return {\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    };\n  }\n  if (/*#__PURE__*/isValidElement(tooltip)) {\n    return {\n      title: tooltip\n    };\n  }\n  if (typeof tooltip === 'object') {\n    return Object.assign({\n      title: editConfigText !== null && editConfigText !== void 0 ? editConfigText : children\n    }, tooltip);\n  }\n  return {\n    title: tooltip\n  };\n}, [tooltip, editConfigText, children]);\nexport default useTooltipProps;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
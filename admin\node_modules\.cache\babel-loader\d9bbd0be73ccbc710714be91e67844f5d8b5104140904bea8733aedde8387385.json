{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useLocale } from '../../locale';\nimport defaultLocale from '../../locale/en_US';\nimport extendsObject from '../extendsObject';\nexport function pickClosable(context) {\n  if (!context) {\n    return undefined;\n  }\n  return {\n    closable: context.closable,\n    closeIcon: context.closeIcon\n  };\n}\n/** Convert `closable` and `closeIcon` to config object */\nfunction useClosableConfig(closableCollection) {\n  const {\n    closable,\n    closeIcon\n  } = closableCollection || {};\n  return React.useMemo(() => {\n    if (\n    // If `closable`, whatever rest be should be true\n    !closable && (closable === false || closeIcon === false || closeIcon === null)) {\n      return false;\n    }\n    if (closable === undefined && closeIcon === undefined) {\n      return null;\n    }\n    let closableConfig = {\n      closeIcon: typeof closeIcon !== 'boolean' && closeIcon !== null ? closeIcon : undefined\n    };\n    if (closable && typeof closable === 'object') {\n      closableConfig = Object.assign(Object.assign({}, closableConfig), closable);\n    }\n    return closableConfig;\n  }, [closable, closeIcon]);\n}\n/** Use same object to support `useMemo` optimization */\nconst EmptyFallbackCloseCollection = {};\nexport default function useClosable(propCloseCollection, contextCloseCollection) {\n  let fallbackCloseCollection = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : EmptyFallbackCloseCollection;\n  // Align the `props`, `context` `fallback` to config object first\n  const propCloseConfig = useClosableConfig(propCloseCollection);\n  const contextCloseConfig = useClosableConfig(contextCloseCollection);\n  const [contextLocale] = useLocale('global', defaultLocale.global);\n  const closeBtnIsDisabled = typeof propCloseConfig !== 'boolean' ? !!(propCloseConfig === null || propCloseConfig === void 0 ? void 0 : propCloseConfig.disabled) : false;\n  const mergedFallbackCloseCollection = React.useMemo(() => Object.assign({\n    closeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null)\n  }, fallbackCloseCollection), [fallbackCloseCollection]);\n  // Use fallback logic to fill the config\n  const mergedClosableConfig = React.useMemo(() => {\n    // ================ Props First ================\n    // Skip if prop is disabled\n    if (propCloseConfig === false) {\n      return false;\n    }\n    if (propCloseConfig) {\n      return extendsObject(mergedFallbackCloseCollection, contextCloseConfig, propCloseConfig);\n    }\n    // =============== Context Second ==============\n    // Skip if context is disabled\n    if (contextCloseConfig === false) {\n      return false;\n    }\n    if (contextCloseConfig) {\n      return extendsObject(mergedFallbackCloseCollection, contextCloseConfig);\n    }\n    // ============= Fallback Default ==============\n    return !mergedFallbackCloseCollection.closable ? false : mergedFallbackCloseCollection;\n  }, [propCloseConfig, contextCloseConfig, mergedFallbackCloseCollection]);\n  // Calculate the final closeIcon\n  return React.useMemo(() => {\n    if (mergedClosableConfig === false) {\n      return [false, null, closeBtnIsDisabled, {}];\n    }\n    const {\n      closeIconRender\n    } = mergedFallbackCloseCollection;\n    const {\n      closeIcon\n    } = mergedClosableConfig;\n    let mergedCloseIcon = closeIcon;\n    // Wrap the closeIcon with aria props\n    const ariaOrDataProps = pickAttrs(mergedClosableConfig, true);\n    if (mergedCloseIcon !== null && mergedCloseIcon !== undefined) {\n      // Wrap the closeIcon if needed\n      if (closeIconRender) {\n        mergedCloseIcon = closeIconRender(closeIcon);\n      }\n      mergedCloseIcon = /*#__PURE__*/React.isValidElement(mergedCloseIcon) ? (/*#__PURE__*/React.cloneElement(mergedCloseIcon, Object.assign({\n        'aria-label': contextLocale.close\n      }, ariaOrDataProps))) : (/*#__PURE__*/React.createElement(\"span\", Object.assign({\n        \"aria-label\": contextLocale.close\n      }, ariaOrDataProps), mergedCloseIcon));\n    }\n    return [true, mergedCloseIcon, closeBtnIsDisabled, ariaOrDataProps];\n  }, [mergedClosableConfig, mergedFallbackCloseCollection]);\n}", "map": {"version": 3, "names": ["React", "CloseOutlined", "pickAttrs", "useLocale", "defaultLocale", "extendsObject", "pickClosable", "context", "undefined", "closable", "closeIcon", "useClosableConfig", "closableCollection", "useMemo", "closableConfig", "Object", "assign", "EmptyFallbackCloseCollection", "useClosable", "propCloseCollection", "contextCloseCollection", "fallbackCloseCollection", "arguments", "length", "propCloseConfig", "contextCloseConfig", "contextLocale", "global", "closeBtnIsDisabled", "disabled", "mergedFallbackCloseCollection", "createElement", "mergedClosableConfig", "closeIconRender", "mergedCloseIcon", "ariaOrDataProps", "isValidElement", "cloneElement", "close"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/_util/hooks/useClosable.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useLocale } from '../../locale';\nimport defaultLocale from '../../locale/en_US';\nimport extendsObject from '../extendsObject';\nexport function pickClosable(context) {\n  if (!context) {\n    return undefined;\n  }\n  return {\n    closable: context.closable,\n    closeIcon: context.closeIcon\n  };\n}\n/** Convert `closable` and `closeIcon` to config object */\nfunction useClosableConfig(closableCollection) {\n  const {\n    closable,\n    closeIcon\n  } = closableCollection || {};\n  return React.useMemo(() => {\n    if (\n    // If `closable`, whatever rest be should be true\n    !closable && (closable === false || closeIcon === false || closeIcon === null)) {\n      return false;\n    }\n    if (closable === undefined && closeIcon === undefined) {\n      return null;\n    }\n    let closableConfig = {\n      closeIcon: typeof closeIcon !== 'boolean' && closeIcon !== null ? closeIcon : undefined\n    };\n    if (closable && typeof closable === 'object') {\n      closableConfig = Object.assign(Object.assign({}, closableConfig), closable);\n    }\n    return closableConfig;\n  }, [closable, closeIcon]);\n}\n/** Use same object to support `useMemo` optimization */\nconst EmptyFallbackCloseCollection = {};\nexport default function useClosable(propCloseCollection, contextCloseCollection, fallbackCloseCollection = EmptyFallbackCloseCollection) {\n  // Align the `props`, `context` `fallback` to config object first\n  const propCloseConfig = useClosableConfig(propCloseCollection);\n  const contextCloseConfig = useClosableConfig(contextCloseCollection);\n  const [contextLocale] = useLocale('global', defaultLocale.global);\n  const closeBtnIsDisabled = typeof propCloseConfig !== 'boolean' ? !!(propCloseConfig === null || propCloseConfig === void 0 ? void 0 : propCloseConfig.disabled) : false;\n  const mergedFallbackCloseCollection = React.useMemo(() => Object.assign({\n    closeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null)\n  }, fallbackCloseCollection), [fallbackCloseCollection]);\n  // Use fallback logic to fill the config\n  const mergedClosableConfig = React.useMemo(() => {\n    // ================ Props First ================\n    // Skip if prop is disabled\n    if (propCloseConfig === false) {\n      return false;\n    }\n    if (propCloseConfig) {\n      return extendsObject(mergedFallbackCloseCollection, contextCloseConfig, propCloseConfig);\n    }\n    // =============== Context Second ==============\n    // Skip if context is disabled\n    if (contextCloseConfig === false) {\n      return false;\n    }\n    if (contextCloseConfig) {\n      return extendsObject(mergedFallbackCloseCollection, contextCloseConfig);\n    }\n    // ============= Fallback Default ==============\n    return !mergedFallbackCloseCollection.closable ? false : mergedFallbackCloseCollection;\n  }, [propCloseConfig, contextCloseConfig, mergedFallbackCloseCollection]);\n  // Calculate the final closeIcon\n  return React.useMemo(() => {\n    if (mergedClosableConfig === false) {\n      return [false, null, closeBtnIsDisabled, {}];\n    }\n    const {\n      closeIconRender\n    } = mergedFallbackCloseCollection;\n    const {\n      closeIcon\n    } = mergedClosableConfig;\n    let mergedCloseIcon = closeIcon;\n    // Wrap the closeIcon with aria props\n    const ariaOrDataProps = pickAttrs(mergedClosableConfig, true);\n    if (mergedCloseIcon !== null && mergedCloseIcon !== undefined) {\n      // Wrap the closeIcon if needed\n      if (closeIconRender) {\n        mergedCloseIcon = closeIconRender(closeIcon);\n      }\n      mergedCloseIcon = /*#__PURE__*/React.isValidElement(mergedCloseIcon) ? (/*#__PURE__*/React.cloneElement(mergedCloseIcon, Object.assign({\n        'aria-label': contextLocale.close\n      }, ariaOrDataProps))) : (/*#__PURE__*/React.createElement(\"span\", Object.assign({\n        \"aria-label\": contextLocale.close\n      }, ariaOrDataProps), mergedCloseIcon));\n    }\n    return [true, mergedCloseIcon, closeBtnIsDisabled, ariaOrDataProps];\n  }, [mergedClosableConfig, mergedFallbackCloseCollection]);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpC,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOC,SAAS;EAClB;EACA,OAAO;IACLC,QAAQ,EAAEF,OAAO,CAACE,QAAQ;IAC1BC,SAAS,EAAEH,OAAO,CAACG;EACrB,CAAC;AACH;AACA;AACA,SAASC,iBAAiBA,CAACC,kBAAkB,EAAE;EAC7C,MAAM;IACJH,QAAQ;IACRC;EACF,CAAC,GAAGE,kBAAkB,IAAI,CAAC,CAAC;EAC5B,OAAOZ,KAAK,CAACa,OAAO,CAAC,MAAM;IACzB;IACA;IACA,CAACJ,QAAQ,KAAKA,QAAQ,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,IAAI,CAAC,EAAE;MAC9E,OAAO,KAAK;IACd;IACA,IAAID,QAAQ,KAAKD,SAAS,IAAIE,SAAS,KAAKF,SAAS,EAAE;MACrD,OAAO,IAAI;IACb;IACA,IAAIM,cAAc,GAAG;MACnBJ,SAAS,EAAE,OAAOA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,IAAI,GAAGA,SAAS,GAAGF;IAChF,CAAC;IACD,IAAIC,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC5CK,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,cAAc,CAAC,EAAEL,QAAQ,CAAC;IAC7E;IACA,OAAOK,cAAc;EACvB,CAAC,EAAE,CAACL,QAAQ,EAAEC,SAAS,CAAC,CAAC;AAC3B;AACA;AACA,MAAMO,4BAA4B,GAAG,CAAC,CAAC;AACvC,eAAe,SAASC,WAAWA,CAACC,mBAAmB,EAAEC,sBAAsB,EAA0D;EAAA,IAAxDC,uBAAuB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAd,SAAA,GAAAc,SAAA,MAAGL,4BAA4B;EACrI;EACA,MAAMO,eAAe,GAAGb,iBAAiB,CAACQ,mBAAmB,CAAC;EAC9D,MAAMM,kBAAkB,GAAGd,iBAAiB,CAACS,sBAAsB,CAAC;EACpE,MAAM,CAACM,aAAa,CAAC,GAAGvB,SAAS,CAAC,QAAQ,EAAEC,aAAa,CAACuB,MAAM,CAAC;EACjE,MAAMC,kBAAkB,GAAG,OAAOJ,eAAe,KAAK,SAAS,GAAG,CAAC,EAAEA,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACK,QAAQ,CAAC,GAAG,KAAK;EACxK,MAAMC,6BAA6B,GAAG9B,KAAK,CAACa,OAAO,CAAC,MAAME,MAAM,CAACC,MAAM,CAAC;IACtEN,SAAS,EAAE,aAAaV,KAAK,CAAC+B,aAAa,CAAC9B,aAAa,EAAE,IAAI;EACjE,CAAC,EAAEoB,uBAAuB,CAAC,EAAE,CAACA,uBAAuB,CAAC,CAAC;EACvD;EACA,MAAMW,oBAAoB,GAAGhC,KAAK,CAACa,OAAO,CAAC,MAAM;IAC/C;IACA;IACA,IAAIW,eAAe,KAAK,KAAK,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,IAAIA,eAAe,EAAE;MACnB,OAAOnB,aAAa,CAACyB,6BAA6B,EAAEL,kBAAkB,EAAED,eAAe,CAAC;IAC1F;IACA;IACA;IACA,IAAIC,kBAAkB,KAAK,KAAK,EAAE;MAChC,OAAO,KAAK;IACd;IACA,IAAIA,kBAAkB,EAAE;MACtB,OAAOpB,aAAa,CAACyB,6BAA6B,EAAEL,kBAAkB,CAAC;IACzE;IACA;IACA,OAAO,CAACK,6BAA6B,CAACrB,QAAQ,GAAG,KAAK,GAAGqB,6BAA6B;EACxF,CAAC,EAAE,CAACN,eAAe,EAAEC,kBAAkB,EAAEK,6BAA6B,CAAC,CAAC;EACxE;EACA,OAAO9B,KAAK,CAACa,OAAO,CAAC,MAAM;IACzB,IAAImB,oBAAoB,KAAK,KAAK,EAAE;MAClC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAEJ,kBAAkB,EAAE,CAAC,CAAC,CAAC;IAC9C;IACA,MAAM;MACJK;IACF,CAAC,GAAGH,6BAA6B;IACjC,MAAM;MACJpB;IACF,CAAC,GAAGsB,oBAAoB;IACxB,IAAIE,eAAe,GAAGxB,SAAS;IAC/B;IACA,MAAMyB,eAAe,GAAGjC,SAAS,CAAC8B,oBAAoB,EAAE,IAAI,CAAC;IAC7D,IAAIE,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK1B,SAAS,EAAE;MAC7D;MACA,IAAIyB,eAAe,EAAE;QACnBC,eAAe,GAAGD,eAAe,CAACvB,SAAS,CAAC;MAC9C;MACAwB,eAAe,GAAG,aAAalC,KAAK,CAACoC,cAAc,CAACF,eAAe,CAAC,IAAI,aAAalC,KAAK,CAACqC,YAAY,CAACH,eAAe,EAAEnB,MAAM,CAACC,MAAM,CAAC;QACrI,YAAY,EAAEU,aAAa,CAACY;MAC9B,CAAC,EAAEH,eAAe,CAAC,CAAC,KAAK,aAAanC,KAAK,CAAC+B,aAAa,CAAC,MAAM,EAAEhB,MAAM,CAACC,MAAM,CAAC;QAC9E,YAAY,EAAEU,aAAa,CAACY;MAC9B,CAAC,EAAEH,eAAe,CAAC,EAAED,eAAe,CAAC,CAAC;IACxC;IACA,OAAO,CAAC,IAAI,EAAEA,eAAe,EAAEN,kBAAkB,EAAEO,eAAe,CAAC;EACrE,CAAC,EAAE,CAACH,oBAAoB,EAAEF,6BAA6B,CAAC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
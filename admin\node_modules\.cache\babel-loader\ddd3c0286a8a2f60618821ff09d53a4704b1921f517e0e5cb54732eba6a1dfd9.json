{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedBackTopStyle = token => {\n  const {\n    componentCls,\n    backTopFontSize,\n    backTopSize,\n    zIndexPopup\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'fixed',\n      insetInlineEnd: token.backTopInlineEnd,\n      insetBlockEnd: token.backTopBlockEnd,\n      zIndex: zIndexPopup,\n      width: 40,\n      height: 40,\n      cursor: 'pointer',\n      '&:empty': {\n        display: 'none'\n      },\n      [`${componentCls}-content`]: {\n        width: backTopSize,\n        height: backTopSize,\n        overflow: 'hidden',\n        color: token.backTopColor,\n        textAlign: 'center',\n        backgroundColor: token.backTopBackground,\n        borderRadius: backTopSize,\n        transition: `all ${token.motionDurationMid}`,\n        '&:hover': {\n          backgroundColor: token.backTopHoverBackground,\n          transition: `all ${token.motionDurationMid}`\n        }\n      },\n      // change to .backtop .backtop-icon\n      [`${componentCls}-icon`]: {\n        fontSize: backTopFontSize,\n        lineHeight: unit(backTopSize)\n      }\n    })\n  };\n};\nconst genMediaBackTopStyle = token => {\n  const {\n    componentCls,\n    screenMD,\n    screenXS,\n    backTopInlineEndMD,\n    backTopInlineEndXS\n  } = token;\n  return {\n    [`@media (max-width: ${unit(screenMD)})`]: {\n      [componentCls]: {\n        insetInlineEnd: backTopInlineEndMD\n      }\n    },\n    [`@media (max-width: ${unit(screenXS)})`]: {\n      [componentCls]: {\n        insetInlineEnd: backTopInlineEndXS\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexBase + 10\n});\n// ============================== Export ==============================\nexport default genStyleHooks('BackTop', token => {\n  const {\n    fontSizeHeading3,\n    colorTextDescription,\n    colorTextLightSolid,\n    colorText,\n    controlHeightLG,\n    calc\n  } = token;\n  const backTopToken = mergeToken(token, {\n    backTopBackground: colorTextDescription,\n    backTopColor: colorTextLightSolid,\n    backTopHoverBackground: colorText,\n    backTopFontSize: fontSizeHeading3,\n    backTopSize: controlHeightLG,\n    backTopBlockEnd: calc(controlHeightLG).mul(1.25).equal(),\n    backTopInlineEnd: calc(controlHeightLG).mul(2.5).equal(),\n    backTopInlineEndMD: calc(controlHeightLG).mul(1.5).equal(),\n    backTopInlineEndXS: calc(controlHeightLG).mul(0.5).equal()\n  });\n  return [genSharedBackTopStyle(backTopToken), genMediaBackTopStyle(backTopToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "resetComponent", "genStyleHooks", "mergeToken", "genSharedBackTopStyle", "token", "componentCls", "backTopFontSize", "backTopSize", "zIndexPopup", "Object", "assign", "position", "insetInlineEnd", "backTopInlineEnd", "insetBlockEnd", "backTopBlockEnd", "zIndex", "width", "height", "cursor", "display", "overflow", "color", "backTopColor", "textAlign", "backgroundColor", "backTopBackground", "borderRadius", "transition", "motionDurationMid", "backTopHoverBackground", "fontSize", "lineHeight", "genMediaBackTopStyle", "screenMD", "screenXS", "backTopInlineEndMD", "backTopInlineEndXS", "prepareComponentToken", "zIndexBase", "fontSizeHeading3", "colorTextDescription", "colorTextLightSolid", "colorText", "controlHeightLG", "calc", "backTopToken", "mul", "equal"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/back-top/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedBackTopStyle = token => {\n  const {\n    componentCls,\n    backTopFontSize,\n    backTopSize,\n    zIndexPopup\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'fixed',\n      insetInlineEnd: token.backTopInlineEnd,\n      insetBlockEnd: token.backTopBlockEnd,\n      zIndex: zIndexPopup,\n      width: 40,\n      height: 40,\n      cursor: 'pointer',\n      '&:empty': {\n        display: 'none'\n      },\n      [`${componentCls}-content`]: {\n        width: backTopSize,\n        height: backTopSize,\n        overflow: 'hidden',\n        color: token.backTopColor,\n        textAlign: 'center',\n        backgroundColor: token.backTopBackground,\n        borderRadius: backTopSize,\n        transition: `all ${token.motionDurationMid}`,\n        '&:hover': {\n          backgroundColor: token.backTopHoverBackground,\n          transition: `all ${token.motionDurationMid}`\n        }\n      },\n      // change to .backtop .backtop-icon\n      [`${componentCls}-icon`]: {\n        fontSize: backTopFontSize,\n        lineHeight: unit(backTopSize)\n      }\n    })\n  };\n};\nconst genMediaBackTopStyle = token => {\n  const {\n    componentCls,\n    screenMD,\n    screenXS,\n    backTopInlineEndMD,\n    backTopInlineEndXS\n  } = token;\n  return {\n    [`@media (max-width: ${unit(screenMD)})`]: {\n      [componentCls]: {\n        insetInlineEnd: backTopInlineEndMD\n      }\n    },\n    [`@media (max-width: ${unit(screenXS)})`]: {\n      [componentCls]: {\n        insetInlineEnd: backTopInlineEndXS\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexBase + 10\n});\n// ============================== Export ==============================\nexport default genStyleHooks('BackTop', token => {\n  const {\n    fontSizeHeading3,\n    colorTextDescription,\n    colorTextLightSolid,\n    colorText,\n    controlHeightLG,\n    calc\n  } = token;\n  const backTopToken = mergeToken(token, {\n    backTopBackground: colorTextDescription,\n    backTopColor: colorTextLightSolid,\n    backTopHoverBackground: colorText,\n    backTopFontSize: fontSizeHeading3,\n    backTopSize: controlHeightLG,\n    backTopBlockEnd: calc(controlHeightLG).mul(1.25).equal(),\n    backTopInlineEnd: calc(controlHeightLG).mul(2.5).equal(),\n    backTopInlineEndMD: calc(controlHeightLG).mul(1.5).equal(),\n    backTopInlineEndXS: calc(controlHeightLG).mul(0.5).equal()\n  });\n  return [genSharedBackTopStyle(backTopToken), genMediaBackTopStyle(backTopToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE;AACA,MAAMC,qBAAqB,GAAGC,KAAK,IAAI;EACrC,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,WAAW;IACXC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAGI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE;MACtEO,QAAQ,EAAE,OAAO;MACjBC,cAAc,EAAER,KAAK,CAACS,gBAAgB;MACtCC,aAAa,EAAEV,KAAK,CAACW,eAAe;MACpCC,MAAM,EAAER,WAAW;MACnBS,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE;QACTC,OAAO,EAAE;MACX,CAAC;MACD,CAAC,GAAGf,YAAY,UAAU,GAAG;QAC3BY,KAAK,EAAEV,WAAW;QAClBW,MAAM,EAAEX,WAAW;QACnBc,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAElB,KAAK,CAACmB,YAAY;QACzBC,SAAS,EAAE,QAAQ;QACnBC,eAAe,EAAErB,KAAK,CAACsB,iBAAiB;QACxCC,YAAY,EAAEpB,WAAW;QACzBqB,UAAU,EAAE,OAAOxB,KAAK,CAACyB,iBAAiB,EAAE;QAC5C,SAAS,EAAE;UACTJ,eAAe,EAAErB,KAAK,CAAC0B,sBAAsB;UAC7CF,UAAU,EAAE,OAAOxB,KAAK,CAACyB,iBAAiB;QAC5C;MACF,CAAC;MACD;MACA,CAAC,GAAGxB,YAAY,OAAO,GAAG;QACxB0B,QAAQ,EAAEzB,eAAe;QACzB0B,UAAU,EAAEjC,IAAI,CAACQ,WAAW;MAC9B;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,MAAM0B,oBAAoB,GAAG7B,KAAK,IAAI;EACpC,MAAM;IACJC,YAAY;IACZ6B,QAAQ;IACRC,QAAQ;IACRC,kBAAkB;IAClBC;EACF,CAAC,GAAGjC,KAAK;EACT,OAAO;IACL,CAAC,sBAAsBL,IAAI,CAACmC,QAAQ,CAAC,GAAG,GAAG;MACzC,CAAC7B,YAAY,GAAG;QACdO,cAAc,EAAEwB;MAClB;IACF,CAAC;IACD,CAAC,sBAAsBrC,IAAI,CAACoC,QAAQ,CAAC,GAAG,GAAG;MACzC,CAAC9B,YAAY,GAAG;QACdO,cAAc,EAAEyB;MAClB;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGlC,KAAK,KAAK;EAC7CI,WAAW,EAAEJ,KAAK,CAACmC,UAAU,GAAG;AAClC,CAAC,CAAC;AACF;AACA,eAAetC,aAAa,CAAC,SAAS,EAAEG,KAAK,IAAI;EAC/C,MAAM;IACJoC,gBAAgB;IAChBC,oBAAoB;IACpBC,mBAAmB;IACnBC,SAAS;IACTC,eAAe;IACfC;EACF,CAAC,GAAGzC,KAAK;EACT,MAAM0C,YAAY,GAAG5C,UAAU,CAACE,KAAK,EAAE;IACrCsB,iBAAiB,EAAEe,oBAAoB;IACvClB,YAAY,EAAEmB,mBAAmB;IACjCZ,sBAAsB,EAAEa,SAAS;IACjCrC,eAAe,EAAEkC,gBAAgB;IACjCjC,WAAW,EAAEqC,eAAe;IAC5B7B,eAAe,EAAE8B,IAAI,CAACD,eAAe,CAAC,CAACG,GAAG,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;IACxDnC,gBAAgB,EAAEgC,IAAI,CAACD,eAAe,CAAC,CAACG,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IACxDZ,kBAAkB,EAAES,IAAI,CAACD,eAAe,CAAC,CAACG,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IAC1DX,kBAAkB,EAAEQ,IAAI,CAACD,eAAe,CAAC,CAACG,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC;EAC3D,CAAC,CAAC;EACF,OAAO,CAAC7C,qBAAqB,CAAC2C,YAAY,CAAC,EAAEb,oBAAoB,CAACa,YAAY,CAAC,CAAC;AAClF,CAAC,EAAER,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
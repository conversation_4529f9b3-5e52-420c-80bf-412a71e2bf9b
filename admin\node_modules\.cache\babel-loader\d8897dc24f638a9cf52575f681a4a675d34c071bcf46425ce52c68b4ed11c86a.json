{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\ProductList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Card, Button, Space, Tag, Modal, message, Form, Input, Select, Popconfirm, Typography, Row, Col, Statistic, Drawer, Badge, Dropdown } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, PoweroffOutlined, ReloadOutlined, DownloadOutlined, UploadOutlined, MoreOutlined, SearchOutlined, FilterOutlined, ClearOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { productApi } from '@/services/api';\nimport ProductDetail from '@/components/ProductDetail';\nimport { PRODUCT_STATUS_OPTIONS, OPERATOR_OPTIONS, PAGINATION_CONFIG } from '@/utils/constants';\nimport { formatPrice, formatTraffic, formatMinutes, formatTags, downloadFile } from '@/utils/helpers';\nimport '@/styles/ProductList.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  Text\n} = Typography;\nconst ProductList = () => {\n  _s();\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [products, setProducts] = useState([]);\n  const [total, setTotal] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(PAGINATION_CONFIG.defaultPageSize);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [searchParams, setSearchParams] = useState({});\n\n  // 详情抽屉\n  const [detailVisible, setDetailVisible] = useState(false);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [detailLoading, setDetailLoading] = useState(false);\n\n  // 统计数据\n  const [stats, setStats] = useState({\n    total: 0,\n    online: 0,\n    offline: 0\n  });\n\n  // 获取产品列表\n  const fetchProducts = async params => {\n    setLoading(true);\n    try {\n      const response = await productApi.getList({\n        page: currentPage,\n        per_page: pageSize,\n        ...searchParams,\n        ...params\n      });\n      setProducts(response.data.list);\n      setTotal(response.data.pagination.total);\n      setCurrentPage(response.data.pagination.current_page);\n\n      // 计算统计数据\n      const onlineCount = response.data.list.filter(p => p.status === 'online').length;\n      const offlineCount = response.data.list.filter(p => p.status === 'offline').length;\n      setStats({\n        total: response.data.pagination.total,\n        online: onlineCount,\n        offline: offlineCount\n      });\n    } catch (error) {\n      console.error('获取产品列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n  }, [currentPage, pageSize]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    var _values$name;\n    const params = {\n      ...values,\n      name: (_values$name = values.name) === null || _values$name === void 0 ? void 0 : _values$name.trim()\n    };\n    setSearchParams(params);\n    setCurrentPage(1);\n    fetchProducts(params);\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    form.resetFields();\n    setSearchParams({});\n    setCurrentPage(1);\n    fetchProducts({});\n  };\n\n  // 删除产品\n  const handleDelete = async id => {\n    try {\n      await productApi.delete(id);\n      message.success('删除成功');\n      fetchProducts();\n    } catch (error) {\n      console.error('删除失败:', error);\n    }\n  };\n\n  // 更新状态\n  const handleStatusChange = async (id, status) => {\n    try {\n      await productApi.updateStatus(id, status);\n      message.success('状态更新成功');\n      fetchProducts();\n    } catch (error) {\n      console.error('状态更新失败:', error);\n    }\n  };\n\n  // 批量下架\n  const handleBatchOffline = async () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要下架的产品');\n      return;\n    }\n    try {\n      await productApi.batchOffline({\n        ids: selectedRowKeys\n      });\n      message.success('批量下架成功');\n      setSelectedRowKeys([]);\n      fetchProducts();\n    } catch (error) {\n      console.error('批量下架失败:', error);\n    }\n  };\n\n  // 批量删除\n  const handleBatchDelete = async () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要删除的产品');\n      return;\n    }\n    try {\n      await productApi.batchDelete({\n        ids: selectedRowKeys\n      });\n      message.success('批量删除成功');\n      setSelectedRowKeys([]);\n      fetchProducts();\n    } catch (error) {\n      console.error('批量删除失败:', error);\n    }\n  };\n\n  // 查看详情\n  const handleViewDetail = async id => {\n    setDetailLoading(true);\n    setDetailVisible(true);\n    try {\n      const response = await productApi.getDetail(id);\n      setCurrentProduct(response.data);\n    } catch (error) {\n      console.error('获取产品详情失败:', error);\n      message.error('获取产品详情失败');\n      setDetailVisible(false);\n    } finally {\n      setDetailLoading(false);\n    }\n  };\n\n  // 导出数据\n  const handleExport = () => {\n    const csvContent = [['ID', '产品名称', '月租', '通用流量', '定向流量', '免费通话', '运营商', '状态', '创建时间'].join(','), ...products.map(product => [product.id, `\"${product.name}\"`, product.monthly_fee, product.general_traffic, product.directional_traffic, product.free_minutes, `\"${product.operator || ''}\"`, product.status === 'online' ? '上架中' : '已下架', product.created_at].join(','))].join('\\n');\n    downloadFile(csvContent, `产品列表_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv;charset=utf-8;');\n    message.success('数据导出成功');\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '产品ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80,\n    sorter: true,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      style: {\n        fontFamily: 'monospace',\n        fontWeight: 'bold'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称/产品描述',\n    dataIndex: 'name',\n    key: 'name',\n    width: 280,\n    ellipsis: true,\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600,\n          marginBottom: 4,\n          color: '#1890ff',\n          cursor: 'pointer'\n        },\n        onClick: () => handleViewDetail(record.id),\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: record.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '月租',\n    dataIndex: 'monthly_fee',\n    key: 'monthly_fee',\n    width: 100,\n    render: price => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#f50'\n      },\n      children: formatPrice(price)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 9\n    }, this),\n    sorter: true\n  }, {\n    title: '通用流量',\n    dataIndex: 'general_traffic',\n    key: 'general_traffic',\n    width: 100,\n    render: traffic => formatTraffic(traffic),\n    sorter: true\n  }, {\n    title: '定向流量',\n    dataIndex: 'directional_traffic',\n    key: 'directional_traffic',\n    width: 100,\n    render: traffic => formatTraffic(traffic)\n  }, {\n    title: '免费通话',\n    dataIndex: 'free_minutes',\n    key: 'free_minutes',\n    width: 100,\n    render: minutes => formatMinutes(minutes)\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 120,\n    filters: OPERATOR_OPTIONS.map(op => ({\n      text: op.label,\n      value: op.value\n    })),\n    render: text => {\n      const colorMap = {\n        '中国移动': 'green',\n        '中国联通': 'blue',\n        '中国电信': 'red'\n      };\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: colorMap[text] || 'default',\n        style: {\n          fontWeight: 'bold'\n        },\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '归属地',\n    dataIndex: 'location',\n    key: 'location',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: '#666'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '标签',\n    key: 'tags',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: [0, 4],\n      wrap: true,\n      children: formatTags(record).map((tag, index) => /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: tag\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => {\n      const isOnline = status === 'online';\n      return /*#__PURE__*/_jsxDEV(Badge, {\n        status: isOnline ? 'success' : 'default',\n        text: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: isOnline ? '#52c41a' : '#999',\n            fontWeight: 'bold'\n          },\n          children: isOnline ? '上架' : '下架'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 11\n      }, this);\n    },\n    filters: PRODUCT_STATUS_OPTIONS.map(status => ({\n      text: status.label,\n      value: status.value\n    }))\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    width: 150,\n    sorter: true\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 180,\n    fixed: 'right',\n    render: (_, record) => {\n      const items = [{\n        key: 'view',\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 19\n        }, this),\n        label: '查看详情',\n        onClick: () => handleViewDetail(record.id)\n      }, {\n        key: 'edit',\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 19\n        }, this),\n        label: '编辑产品',\n        onClick: () => navigate(`/products/edit/${record.id}`)\n      }, {\n        key: 'status',\n        icon: /*#__PURE__*/_jsxDEV(PoweroffOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 19\n        }, this),\n        label: record.status === 'online' ? '下架产品' : '上架产品',\n        onClick: () => handleStatusChange(record.id, record.status === 'online' ? 'offline' : 'online')\n      }, {\n        type: 'divider'\n      }, {\n        key: 'delete',\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 19\n        }, this),\n        label: '删除产品',\n        danger: true,\n        onClick: () => {\n          Modal.confirm({\n            title: '确定要删除这个产品吗？',\n            content: '删除后无法恢复，请谨慎操作。',\n            okText: '确定删除',\n            cancelText: '取消',\n            okButtonProps: {\n              danger: true\n            },\n            onOk: () => handleDelete(record.id)\n          });\n        }\n      }];\n      return /*#__PURE__*/_jsxDEV(Space, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewDetail(record.id),\n          style: {\n            borderRadius: '4px'\n          },\n          children: \"\\u67E5\\u770B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 21\n          }, this),\n          onClick: () => navigate(`/products/edit/${record.id}`),\n          style: {\n            borderRadius: '4px'\n          },\n          children: \"\\u7F16\\u8F91\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items\n          },\n          trigger: ['click'],\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(MoreOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 23\n            }, this),\n            style: {\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: newSelectedRowKeys => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: record => ({\n      name: record.name\n    })\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px',\n      background: '#f5f5f5',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fff',\n        padding: '24px',\n        borderRadius: '8px',\n        marginBottom: '16px',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Typography.Title, {\n            level: 2,\n            style: {\n              margin: 0,\n              color: '#1890ff'\n            },\n            children: \"\\u4EA7\\u54C1\\u7BA1\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n            type: \"secondary\",\n            children: \"\\u7BA1\\u7406\\u548C\\u7EF4\\u62A4\\u6240\\u6709\\u4EA7\\u54C1\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 21\n            }, this),\n            onClick: () => fetchProducts(),\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u4EA7\\u54C1\\u6570\",\n              value: stats.total,\n              prefix: /*#__PURE__*/_jsxDEV(Badge, {\n                count: stats.total,\n                style: {\n                  backgroundColor: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#1890ff',\n                fontSize: '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u4E0A\\u67B6\\u4E2D\",\n              value: stats.online,\n              prefix: /*#__PURE__*/_jsxDEV(Badge, {\n                count: stats.online,\n                style: {\n                  backgroundColor: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#52c41a',\n                fontSize: '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u5DF2\\u4E0B\\u67B6\",\n              value: stats.offline,\n              prefix: /*#__PURE__*/_jsxDEV(Badge, {\n                count: stats.offline,\n                style: {\n                  backgroundColor: '#faad14'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#faad14',\n                fontSize: '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u4E0A\\u67B6\\u7387\",\n              value: stats.total > 0 ? (stats.online / stats.total * 100).toFixed(1) : 0,\n              suffix: \"%\",\n              valueStyle: {\n                color: stats.total > 0 && stats.online / stats.total > 0.8 ? '#52c41a' : '#faad14',\n                fontSize: '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"id\",\n          label: \"ID\\u7F16\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1ID\",\n            style: {\n              width: 120\n            },\n            prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u540D\\u79F0\",\n            style: {\n              width: 200\n            },\n            prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"operator\",\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n            style: {\n              width: 120\n            },\n            allowClear: true,\n            suffixIcon: /*#__PURE__*/_jsxDEV(FilterOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 27\n            }, this),\n            children: OPERATOR_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            allowClear: true,\n            suffixIcon: /*#__PURE__*/_jsxDEV(FilterOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 27\n            }, this),\n            children: PRODUCT_STATUS_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 23\n              }, this),\n              loading: loading,\n              children: \"\\u67E5\\u8BE2\\u7B5B\\u9009\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleReset,\n              icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 23\n              }, this),\n              children: \"\\u91CD\\u7F6E\\u6761\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16,\n          padding: '16px 0',\n          borderBottom: '1px solid #f0f0f0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 23\n              }, this),\n              onClick: () => navigate('/products/create'),\n              size: \"large\",\n              style: {\n                borderRadius: '6px'\n              },\n              children: \"\\u65B0\\u589E\\u4EA7\\u54C1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              disabled: selectedRowKeys.length === 0,\n              onClick: handleBatchOffline,\n              style: {\n                borderRadius: '6px'\n              },\n              children: \"\\u6279\\u91CF\\u4E0B\\u67B6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n              title: `确定要删除选中的 ${selectedRowKeys.length} 个产品吗？`,\n              description: \"\\u5220\\u9664\\u540E\\u65E0\\u6CD5\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\u3002\",\n              onConfirm: handleBatchDelete,\n              disabled: selectedRowKeys.length === 0,\n              okText: \"\\u786E\\u5B9A\\u5220\\u9664\",\n              cancelText: \"\\u53D6\\u6D88\",\n              okButtonProps: {\n                danger: true\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                danger: true,\n                disabled: selectedRowKeys.length === 0,\n                style: {\n                  borderRadius: '6px'\n                },\n                children: \"\\u6279\\u91CF\\u5220\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 23\n              }, this),\n              onClick: handleExport,\n              disabled: products.length === 0,\n              style: {\n                borderRadius: '6px'\n              },\n              children: \"\\u5BFC\\u51FA\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 23\n              }, this),\n              onClick: () => message.info('批量导入功能开发中'),\n              style: {\n                borderRadius: '6px'\n              },\n              children: \"\\u6279\\u91CF\\u5BFC\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this), selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              style: {\n                margin: 0\n              },\n              children: [\"\\u5DF2\\u9009\\u62E9 \", selectedRowKeys.length, \" \\u9879\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        rowSelection: rowSelection,\n        columns: columns,\n        dataSource: products,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1500\n        },\n        size: \"middle\",\n        bordered: true,\n        style: {\n          background: '#fff',\n          borderRadius: '8px'\n        },\n        rowClassName: (record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark',\n        pagination: {\n          current: currentPage,\n          pageSize: pageSize,\n          total: total,\n          showSizeChanger: PAGINATION_CONFIG.showSizeChanger,\n          showQuickJumper: PAGINATION_CONFIG.showQuickJumper,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          pageSizeOptions: PAGINATION_CONFIG.pageSizeOptions,\n          onChange: (page, size) => {\n            setCurrentPage(page);\n            setPageSize(size || PAGINATION_CONFIG.defaultPageSize);\n          },\n          style: {\n            marginTop: '16px',\n            textAlign: 'right'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 627,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      title: \"\\u4EA7\\u54C1\\u8BE6\\u60C5\",\n      placement: \"right\",\n      size: \"large\",\n      open: detailVisible,\n      onClose: () => {\n        setDetailVisible(false);\n        setCurrentProduct(null);\n      },\n      extra: currentProduct && /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 23\n          }, this),\n          onClick: () => {\n            setDetailVisible(false);\n            navigate(`/products/edit/${currentProduct.id}`);\n          },\n          children: \"\\u7F16\\u8F91\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(PoweroffOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 23\n          }, this),\n          onClick: () => {\n            handleStatusChange(currentProduct.id, currentProduct.status === 'online' ? 'offline' : 'online');\n            setDetailVisible(false);\n          },\n          children: currentProduct.status === 'online' ? '下架' : '上架'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 13\n      }, this),\n      children: detailLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u52A0\\u8F7D\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 11\n      }, this) : currentProduct ? /*#__PURE__*/_jsxDEV(ProductDetail, {\n        product: currentProduct\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 780,\n        columnNumber: 11\n      }, this) : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 737,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 478,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductList, \"7YaiXY09mykgqh8X+h2nOCWnDXc=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = ProductList;\nexport default ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "Card", "<PERSON><PERSON>", "Space", "Tag", "Modal", "message", "Form", "Input", "Select", "Popconfirm", "Typography", "Row", "Col", "Statistic", "Drawer", "Badge", "Dropdown", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "PoweroffOutlined", "ReloadOutlined", "DownloadOutlined", "UploadOutlined", "MoreOutlined", "SearchOutlined", "FilterOutlined", "ClearOutlined", "useNavigate", "productApi", "ProductDetail", "PRODUCT_STATUS_OPTIONS", "OPERATOR_OPTIONS", "PAGINATION_CONFIG", "formatPrice", "formatTraffic", "formatMinutes", "formatTags", "downloadFile", "jsxDEV", "_jsxDEV", "Search", "Option", "Text", "ProductList", "_s", "form", "useForm", "navigate", "loading", "setLoading", "products", "setProducts", "total", "setTotal", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "defaultPageSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "searchParams", "setSearchParams", "detailVisible", "setDetailVisible", "currentProduct", "setCurrentProduct", "detailLoading", "setDetailLoading", "stats", "setStats", "online", "offline", "fetchProducts", "params", "response", "getList", "page", "per_page", "data", "list", "pagination", "current_page", "onlineCount", "filter", "p", "status", "length", "offlineCount", "error", "console", "handleSearch", "values", "_values$name", "name", "trim", "handleReset", "resetFields", "handleDelete", "id", "delete", "success", "handleStatusChange", "updateStatus", "handleBatchOffline", "warning", "batchOffline", "ids", "handleBatchDelete", "batchDelete", "handleViewDetail", "getDetail", "handleExport", "csv<PERSON><PERSON>nt", "join", "map", "product", "monthly_fee", "general_traffic", "directional_traffic", "free_minutes", "operator", "created_at", "Date", "toISOString", "split", "columns", "title", "dataIndex", "key", "width", "sorter", "render", "text", "color", "style", "fontFamily", "fontWeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ellipsis", "record", "marginBottom", "cursor", "onClick", "type", "fontSize", "description", "price", "strong", "traffic", "minutes", "filters", "op", "label", "value", "colorMap", "_", "size", "wrap", "tag", "index", "isOnline", "fixed", "items", "icon", "danger", "confirm", "content", "okText", "cancelText", "okButtonProps", "onOk", "borderRadius", "menu", "trigger", "rowSelection", "onChange", "newSelectedRowKeys", "getCheckboxProps", "padding", "background", "minHeight", "boxShadow", "display", "justifyContent", "alignItems", "Title", "level", "margin", "gutter", "span", "textAlign", "prefix", "count", "backgroundColor", "valueStyle", "toFixed", "suffix", "layout", "onFinish", "<PERSON><PERSON>", "placeholder", "allowClear", "suffixIcon", "option", "htmlType", "borderBottom", "disabled", "onConfirm", "info", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "bordered", "rowClassName", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "pageSizeOptions", "marginTop", "placement", "open", "onClose", "extra", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/ProductList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Card,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  message,\n  Form,\n  Input,\n  Select,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Drawer,\n  Tooltip,\n  Badge,\n  Avatar,\n  Divider,\n  Dropdown,\n  MenuProps,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  PoweroffOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ReloadOutlined,\n  DownloadOutlined,\n  UploadOutlined,\n  MoreOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  ClearOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { TableRowSelection } from 'antd/es/table/interface';\nimport { useNavigate } from 'react-router-dom';\nimport { productApi } from '@/services/api';\nimport type { Product, ProductListParams } from '@/types/product';\nimport ProductDetail from '@/components/ProductDetail';\nimport {\n  PRODUCT_STATUS_OPTIONS,\n  OPERATOR_OPTIONS,\n  LOCATION_OPTIONS,\n  PAGINATION_CONFIG,\n} from '@/utils/constants';\nimport {\n  formatPrice,\n  formatTraffic,\n  formatMinutes,\n  getStatusColor,\n  formatTags,\n  downloadFile,\n} from '@/utils/helpers';\nimport '@/styles/ProductList.css';\n\nconst { Search } = Input;\nconst { Option } = Select;\nconst { Text } = Typography;\n\nconst ProductList: React.FC = () => {\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [products, setProducts] = useState<Product[]>([]);\n  const [total, setTotal] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(PAGINATION_CONFIG.defaultPageSize);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [searchParams, setSearchParams] = useState<ProductListParams>({});\n\n  // 详情抽屉\n  const [detailVisible, setDetailVisible] = useState(false);\n  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);\n  const [detailLoading, setDetailLoading] = useState(false);\n\n  // 统计数据\n  const [stats, setStats] = useState({\n    total: 0,\n    online: 0,\n    offline: 0,\n  });\n\n  // 获取产品列表\n  const fetchProducts = async (params?: ProductListParams) => {\n    setLoading(true);\n    try {\n      const response = await productApi.getList({\n        page: currentPage,\n        per_page: pageSize,\n        ...searchParams,\n        ...params,\n      });\n\n      setProducts(response.data.list);\n      setTotal(response.data.pagination.total);\n      setCurrentPage(response.data.pagination.current_page);\n\n      // 计算统计数据\n      const onlineCount = response.data.list.filter(p => p.status === 'online').length;\n      const offlineCount = response.data.list.filter(p => p.status === 'offline').length;\n      setStats({\n        total: response.data.pagination.total,\n        online: onlineCount,\n        offline: offlineCount,\n      });\n    } catch (error) {\n      console.error('获取产品列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n  }, [currentPage, pageSize]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    const params = {\n      ...values,\n      name: values.name?.trim(),\n    };\n    setSearchParams(params);\n    setCurrentPage(1);\n    fetchProducts(params);\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    form.resetFields();\n    setSearchParams({});\n    setCurrentPage(1);\n    fetchProducts({});\n  };\n\n  // 删除产品\n  const handleDelete = async (id: number) => {\n    try {\n      await productApi.delete(id);\n      message.success('删除成功');\n      fetchProducts();\n    } catch (error) {\n      console.error('删除失败:', error);\n    }\n  };\n\n  // 更新状态\n  const handleStatusChange = async (id: number, status: 'online' | 'offline') => {\n    try {\n      await productApi.updateStatus(id, status);\n      message.success('状态更新成功');\n      fetchProducts();\n    } catch (error) {\n      console.error('状态更新失败:', error);\n    }\n  };\n\n  // 批量下架\n  const handleBatchOffline = async () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要下架的产品');\n      return;\n    }\n\n    try {\n      await productApi.batchOffline({ ids: selectedRowKeys as number[] });\n      message.success('批量下架成功');\n      setSelectedRowKeys([]);\n      fetchProducts();\n    } catch (error) {\n      console.error('批量下架失败:', error);\n    }\n  };\n\n  // 批量删除\n  const handleBatchDelete = async () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要删除的产品');\n      return;\n    }\n\n    try {\n      await productApi.batchDelete({ ids: selectedRowKeys as number[] });\n      message.success('批量删除成功');\n      setSelectedRowKeys([]);\n      fetchProducts();\n    } catch (error) {\n      console.error('批量删除失败:', error);\n    }\n  };\n\n  // 查看详情\n  const handleViewDetail = async (id: number) => {\n    setDetailLoading(true);\n    setDetailVisible(true);\n    try {\n      const response = await productApi.getDetail(id);\n      setCurrentProduct(response.data);\n    } catch (error) {\n      console.error('获取产品详情失败:', error);\n      message.error('获取产品详情失败');\n      setDetailVisible(false);\n    } finally {\n      setDetailLoading(false);\n    }\n  };\n\n  // 导出数据\n  const handleExport = () => {\n    const csvContent = [\n      ['ID', '产品名称', '月租', '通用流量', '定向流量', '免费通话', '运营商', '状态', '创建时间'].join(','),\n      ...products.map(product => [\n        product.id,\n        `\"${product.name}\"`,\n        product.monthly_fee,\n        product.general_traffic,\n        product.directional_traffic,\n        product.free_minutes,\n        `\"${product.operator || ''}\"`,\n        product.status === 'online' ? '上架中' : '已下架',\n        product.created_at\n      ].join(','))\n    ].join('\\n');\n\n    downloadFile(csvContent, `产品列表_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv;charset=utf-8;');\n    message.success('数据导出成功');\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Product> = [\n    {\n      title: '产品ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n      sorter: true,\n      render: (text: number) => (\n        <Tag color=\"blue\" style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>\n          {text}\n        </Tag>\n      ),\n    },\n    {\n      title: '产品名称/产品描述',\n      dataIndex: 'name',\n      key: 'name',\n      width: 280,\n      ellipsis: true,\n      render: (text: string, record: Product) => (\n        <div>\n          <div style={{\n            fontWeight: 600,\n            marginBottom: 4,\n            color: '#1890ff',\n            cursor: 'pointer'\n          }}\n          onClick={() => handleViewDetail(record.id)}\n          >\n            {text}\n          </div>\n          <Typography.Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.description}\n          </Typography.Text>\n        </div>\n      ),\n    },\n    {\n      title: '月租',\n      dataIndex: 'monthly_fee',\n      key: 'monthly_fee',\n      width: 100,\n      render: (price: string) => (\n        <Text strong style={{ color: '#f50' }}>\n          {formatPrice(price)}\n        </Text>\n      ),\n      sorter: true,\n    },\n    {\n      title: '通用流量',\n      dataIndex: 'general_traffic',\n      key: 'general_traffic',\n      width: 100,\n      render: (traffic: string) => formatTraffic(traffic),\n      sorter: true,\n    },\n    {\n      title: '定向流量',\n      dataIndex: 'directional_traffic',\n      key: 'directional_traffic',\n      width: 100,\n      render: (traffic: string) => formatTraffic(traffic),\n    },\n    {\n      title: '免费通话',\n      dataIndex: 'free_minutes',\n      key: 'free_minutes',\n      width: 100,\n      render: (minutes: number) => formatMinutes(minutes),\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 120,\n      filters: OPERATOR_OPTIONS.map(op => ({ text: op.label, value: op.value })),\n      render: (text: string) => {\n        const colorMap: { [key: string]: string } = {\n          '中国移动': 'green',\n          '中国联通': 'blue',\n          '中国电信': 'red'\n        };\n        return (\n          <Tag color={colorMap[text] || 'default'} style={{ fontWeight: 'bold' }}>\n            {text}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '归属地',\n      dataIndex: 'location',\n      key: 'location',\n      width: 120,\n      render: (text: string) => (\n        <span style={{ color: '#666' }}>{text}</span>\n      ),\n    },\n    {\n      title: '标签',\n      key: 'tags',\n      width: 150,\n      render: (_, record) => (\n        <Space size={[0, 4]} wrap>\n          {formatTags(record).map((tag, index) => (\n            <Tag key={index} color=\"blue\">\n              {tag}\n            </Tag>\n          ))}\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => {\n        const isOnline = status === 'online';\n        return (\n          <Badge\n            status={isOnline ? 'success' : 'default'}\n            text={\n              <span style={{\n                color: isOnline ? '#52c41a' : '#999',\n                fontWeight: 'bold'\n              }}>\n                {isOnline ? '上架' : '下架'}\n              </span>\n            }\n          />\n        );\n      },\n      filters: PRODUCT_STATUS_OPTIONS.map(status => ({\n        text: status.label,\n        value: status.value,\n      })),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      width: 150,\n      sorter: true,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 180,\n      fixed: 'right',\n      render: (_, record) => {\n        const items: MenuProps['items'] = [\n          {\n            key: 'view',\n            icon: <EyeOutlined />,\n            label: '查看详情',\n            onClick: () => handleViewDetail(record.id),\n          },\n          {\n            key: 'edit',\n            icon: <EditOutlined />,\n            label: '编辑产品',\n            onClick: () => navigate(`/products/edit/${record.id}`),\n          },\n          {\n            key: 'status',\n            icon: <PoweroffOutlined />,\n            label: record.status === 'online' ? '下架产品' : '上架产品',\n            onClick: () => handleStatusChange(\n              record.id,\n              record.status === 'online' ? 'offline' : 'online'\n            ),\n          },\n          {\n            type: 'divider',\n          },\n          {\n            key: 'delete',\n            icon: <DeleteOutlined />,\n            label: '删除产品',\n            danger: true,\n            onClick: () => {\n              Modal.confirm({\n                title: '确定要删除这个产品吗？',\n                content: '删除后无法恢复，请谨慎操作。',\n                okText: '确定删除',\n                cancelText: '取消',\n                okButtonProps: { danger: true },\n                onOk: () => handleDelete(record.id),\n              });\n            },\n          },\n        ];\n\n        return (\n          <Space size=\"small\">\n            <Button\n              type=\"primary\"\n              size=\"small\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewDetail(record.id)}\n              style={{ borderRadius: '4px' }}\n            >\n              查看\n            </Button>\n            <Button\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => navigate(`/products/edit/${record.id}`)}\n              style={{ borderRadius: '4px' }}\n            >\n              编辑\n            </Button>\n            <Dropdown menu={{ items }} trigger={['click']}>\n              <Button\n                size=\"small\"\n                icon={<MoreOutlined />}\n                style={{ borderRadius: '4px' }}\n              />\n            </Dropdown>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection: TableRowSelection<Product> = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: (record: Product) => ({\n      name: record.name,\n    }),\n  };\n\n  return (\n    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>\n      {/* 页面头部 */}\n      <div style={{\n        background: '#fff',\n        padding: '24px',\n        borderRadius: '8px',\n        marginBottom: '16px',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n      }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>\n          <div>\n            <Typography.Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n              产品管理\n            </Typography.Title>\n            <Typography.Text type=\"secondary\">\n              管理和维护所有产品信息\n            </Typography.Text>\n          </div>\n          <Space>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={() => fetchProducts()}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Space>\n        </div>\n\n        {/* 统计卡片 */}\n        <Row gutter={16}>\n          <Col span={6}>\n            <Card size=\"small\" style={{ textAlign: 'center' }}>\n              <Statistic\n                title=\"总产品数\"\n                value={stats.total}\n                prefix={<Badge count={stats.total} style={{ backgroundColor: '#1890ff' }} />}\n                valueStyle={{ color: '#1890ff', fontSize: '20px' }}\n              />\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card size=\"small\" style={{ textAlign: 'center' }}>\n              <Statistic\n                title=\"上架中\"\n                value={stats.online}\n                prefix={<Badge count={stats.online} style={{ backgroundColor: '#52c41a' }} />}\n                valueStyle={{ color: '#52c41a', fontSize: '20px' }}\n              />\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card size=\"small\" style={{ textAlign: 'center' }}>\n              <Statistic\n                title=\"已下架\"\n                value={stats.offline}\n                prefix={<Badge count={stats.offline} style={{ backgroundColor: '#faad14' }} />}\n                valueStyle={{ color: '#faad14', fontSize: '20px' }}\n              />\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card size=\"small\" style={{ textAlign: 'center' }}>\n              <Statistic\n                title=\"上架率\"\n                value={stats.total > 0 ? ((stats.online / stats.total) * 100).toFixed(1) : 0}\n                suffix=\"%\"\n                valueStyle={{\n                  color: stats.total > 0 && (stats.online / stats.total) > 0.8 ? '#52c41a' : '#faad14',\n                  fontSize: '20px'\n                }}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </div>\n\n      {/* 搜索和筛选区域 */}\n      <Card style={{ marginBottom: '16px' }}>\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ marginBottom: 16 }}\n        >\n          <Form.Item name=\"id\" label=\"ID编号\">\n            <Input\n              placeholder=\"请输入产品ID\"\n              style={{ width: 120 }}\n              prefix={<SearchOutlined />}\n            />\n          </Form.Item>\n          <Form.Item name=\"name\" label=\"产品名称\">\n            <Input\n              placeholder=\"请输入产品名称\"\n              style={{ width: 200 }}\n              prefix={<SearchOutlined />}\n            />\n          </Form.Item>\n          <Form.Item name=\"operator\" label=\"运营商\">\n            <Select\n              placeholder=\"请选择运营商\"\n              style={{ width: 120 }}\n              allowClear\n              suffixIcon={<FilterOutlined />}\n            >\n              {OPERATOR_OPTIONS.map(option => (\n                <Select.Option key={option.value} value={option.value}>\n                  {option.label}\n                </Select.Option>\n              ))}\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"status\" label=\"状态\">\n            <Select\n              placeholder=\"请选择状态\"\n              style={{ width: 120 }}\n              allowClear\n              suffixIcon={<FilterOutlined />}\n            >\n              {PRODUCT_STATUS_OPTIONS.map(option => (\n                <Select.Option key={option.value} value={option.value}>\n                  {option.label}\n                </Select.Option>\n              ))}\n            </Select>\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                icon={<SearchOutlined />}\n                loading={loading}\n              >\n                查询筛选\n              </Button>\n              <Button\n                onClick={handleReset}\n                icon={<ClearOutlined />}\n              >\n                重置条件\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Card>\n\n      {/* 产品表格 */}\n      <Card>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16,\n          padding: '16px 0',\n          borderBottom: '1px solid #f0f0f0'\n        }}>\n          <div>\n            <Space wrap>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => navigate('/products/create')}\n                size=\"large\"\n                style={{ borderRadius: '6px' }}\n              >\n                新增产品\n              </Button>\n              <Button\n                disabled={selectedRowKeys.length === 0}\n                onClick={handleBatchOffline}\n                style={{ borderRadius: '6px' }}\n              >\n                批量下架\n              </Button>\n              <Popconfirm\n                title={`确定要删除选中的 ${selectedRowKeys.length} 个产品吗？`}\n                description=\"删除后无法恢复，请谨慎操作。\"\n                onConfirm={handleBatchDelete}\n                disabled={selectedRowKeys.length === 0}\n                okText=\"确定删除\"\n                cancelText=\"取消\"\n                okButtonProps={{ danger: true }}\n              >\n                <Button\n                  danger\n                  disabled={selectedRowKeys.length === 0}\n                  style={{ borderRadius: '6px' }}\n                >\n                  批量删除\n                </Button>\n              </Popconfirm>\n            </Space>\n          </div>\n          <div>\n            <Space>\n              <Button\n                icon={<DownloadOutlined />}\n                onClick={handleExport}\n                disabled={products.length === 0}\n                style={{ borderRadius: '6px' }}\n              >\n                导出数据\n              </Button>\n              <Button\n                icon={<UploadOutlined />}\n                onClick={() => message.info('批量导入功能开发中')}\n                style={{ borderRadius: '6px' }}\n              >\n                批量导入\n              </Button>\n              {selectedRowKeys.length > 0 && (\n                <Tag color=\"blue\" style={{ margin: 0 }}>\n                  已选择 {selectedRowKeys.length} 项\n                </Tag>\n              )}\n            </Space>\n          </div>\n        </div>\n\n        <Table\n          rowSelection={rowSelection}\n          columns={columns}\n          dataSource={products}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1500 }}\n          size=\"middle\"\n          bordered\n          style={{\n            background: '#fff',\n            borderRadius: '8px'\n          }}\n          rowClassName={(record, index) =>\n            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'\n          }\n          pagination={{\n            current: currentPage,\n            pageSize: pageSize,\n            total: total,\n            showSizeChanger: PAGINATION_CONFIG.showSizeChanger,\n            showQuickJumper: PAGINATION_CONFIG.showQuickJumper,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n            pageSizeOptions: PAGINATION_CONFIG.pageSizeOptions,\n            onChange: (page, size) => {\n              setCurrentPage(page);\n              setPageSize(size || PAGINATION_CONFIG.defaultPageSize);\n            },\n            style: {\n              marginTop: '16px',\n              textAlign: 'right'\n            }\n          }}\n        />\n      </Card>\n\n      {/* 产品详情抽屉 */}\n      <Drawer\n        title=\"产品详情\"\n        placement=\"right\"\n        size=\"large\"\n        open={detailVisible}\n        onClose={() => {\n          setDetailVisible(false);\n          setCurrentProduct(null);\n        }}\n        extra={\n          currentProduct && (\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<EditOutlined />}\n                onClick={() => {\n                  setDetailVisible(false);\n                  navigate(`/products/edit/${currentProduct.id}`);\n                }}\n              >\n                编辑\n              </Button>\n              <Button\n                icon={<PoweroffOutlined />}\n                onClick={() => {\n                  handleStatusChange(\n                    currentProduct.id,\n                    currentProduct.status === 'online' ? 'offline' : 'online'\n                  );\n                  setDetailVisible(false);\n                }}\n              >\n                {currentProduct.status === 'online' ? '下架' : '上架'}\n              </Button>\n            </Space>\n          )\n        }\n      >\n        {detailLoading ? (\n          <div style={{ textAlign: 'center', padding: '50px 0' }}>\n            <div>加载中...</div>\n          </div>\n        ) : currentProduct ? (\n          <ProductDetail product={currentProduct} />\n        ) : null}\n      </Drawer>\n    </div>\n  );\n};\n\nexport default ProductList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,MAAM,EAENC,KAAK,EAGLC,QAAQ,QAEH,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,gBAAgB,EAGhBC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,QACR,mBAAmB;AAG1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,gBAAgB;AAE3C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SACEC,sBAAsB,EACtBC,gBAAgB,EAEhBC,iBAAiB,QACZ,mBAAmB;AAC1B,SACEC,WAAW,EACXC,aAAa,EACbC,aAAa,EAEbC,UAAU,EACVC,YAAY,QACP,iBAAiB;AACxB,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC;AAAO,CAAC,GAAGnC,KAAK;AACxB,MAAM;EAAEoC;AAAO,CAAC,GAAGnC,MAAM;AACzB,MAAM;EAAEoC;AAAK,CAAC,GAAGlC,UAAU;AAE3B,MAAMmC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,IAAI,CAAC,GAAGzC,IAAI,CAAC0C,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAACqC,iBAAiB,CAAC0B,eAAe,CAAC;EAC3E,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAoB,CAAC,CAAC,CAAC;;EAEvE;EACA,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsE,cAAc,EAAEC,iBAAiB,CAAC,GAAGvE,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC;IACjCyD,KAAK,EAAE,CAAC;IACRmB,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAG,MAAOC,MAA0B,IAAK;IAC1DzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM0B,QAAQ,GAAG,MAAM/C,UAAU,CAACgD,OAAO,CAAC;QACxCC,IAAI,EAAEvB,WAAW;QACjBwB,QAAQ,EAAEtB,QAAQ;QAClB,GAAGK,YAAY;QACf,GAAGa;MACL,CAAC,CAAC;MAEFvB,WAAW,CAACwB,QAAQ,CAACI,IAAI,CAACC,IAAI,CAAC;MAC/B3B,QAAQ,CAACsB,QAAQ,CAACI,IAAI,CAACE,UAAU,CAAC7B,KAAK,CAAC;MACxCG,cAAc,CAACoB,QAAQ,CAACI,IAAI,CAACE,UAAU,CAACC,YAAY,CAAC;;MAErD;MACA,MAAMC,WAAW,GAAGR,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACC,MAAM;MAChF,MAAMC,YAAY,GAAGb,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACC,MAAM;MAClFjB,QAAQ,CAAC;QACPlB,KAAK,EAAEuB,QAAQ,CAACI,IAAI,CAACE,UAAU,CAAC7B,KAAK;QACrCmB,MAAM,EAAEY,WAAW;QACnBX,OAAO,EAAEgB;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACArD,SAAS,CAAC,MAAM;IACd6E,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACnB,WAAW,EAAEE,QAAQ,CAAC,CAAC;;EAE3B;EACA,MAAMmC,YAAY,GAAIC,MAAW,IAAK;IAAA,IAAAC,YAAA;IACpC,MAAMnB,MAAM,GAAG;MACb,GAAGkB,MAAM;MACTE,IAAI,GAAAD,YAAA,GAAED,MAAM,CAACE,IAAI,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,IAAI,CAAC;IAC1B,CAAC;IACDjC,eAAe,CAACY,MAAM,CAAC;IACvBnB,cAAc,CAAC,CAAC,CAAC;IACjBkB,aAAa,CAACC,MAAM,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsB,WAAW,GAAGA,CAAA,KAAM;IACxBnD,IAAI,CAACoD,WAAW,CAAC,CAAC;IAClBnC,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBP,cAAc,CAAC,CAAC,CAAC;IACjBkB,aAAa,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMyB,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMvE,UAAU,CAACwE,MAAM,CAACD,EAAE,CAAC;MAC3BhG,OAAO,CAACkG,OAAO,CAAC,MAAM,CAAC;MACvB5B,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMa,kBAAkB,GAAG,MAAAA,CAAOH,EAAU,EAAEb,MAA4B,KAAK;IAC7E,IAAI;MACF,MAAM1D,UAAU,CAAC2E,YAAY,CAACJ,EAAE,EAAEb,MAAM,CAAC;MACzCnF,OAAO,CAACkG,OAAO,CAAC,QAAQ,CAAC;MACzB5B,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMe,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI7C,eAAe,CAAC4B,MAAM,KAAK,CAAC,EAAE;MAChCpF,OAAO,CAACsG,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;IAEA,IAAI;MACF,MAAM7E,UAAU,CAAC8E,YAAY,CAAC;QAAEC,GAAG,EAAEhD;MAA4B,CAAC,CAAC;MACnExD,OAAO,CAACkG,OAAO,CAAC,QAAQ,CAAC;MACzBzC,kBAAkB,CAAC,EAAE,CAAC;MACtBa,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMmB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAIjD,eAAe,CAAC4B,MAAM,KAAK,CAAC,EAAE;MAChCpF,OAAO,CAACsG,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;IAEA,IAAI;MACF,MAAM7E,UAAU,CAACiF,WAAW,CAAC;QAAEF,GAAG,EAAEhD;MAA4B,CAAC,CAAC;MAClExD,OAAO,CAACkG,OAAO,CAAC,QAAQ,CAAC;MACzBzC,kBAAkB,CAAC,EAAE,CAAC;MACtBa,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMqB,gBAAgB,GAAG,MAAOX,EAAU,IAAK;IAC7C/B,gBAAgB,CAAC,IAAI,CAAC;IACtBJ,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAM/C,UAAU,CAACmF,SAAS,CAACZ,EAAE,CAAC;MAC/CjC,iBAAiB,CAACS,QAAQ,CAACI,IAAI,CAAC;IAClC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCtF,OAAO,CAACsF,KAAK,CAAC,UAAU,CAAC;MACzBzB,gBAAgB,CAAC,KAAK,CAAC;IACzB,CAAC,SAAS;MACRI,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM4C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG,CACjB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAC3E,GAAGhE,QAAQ,CAACiE,GAAG,CAACC,OAAO,IAAI,CACzBA,OAAO,CAACjB,EAAE,EACV,IAAIiB,OAAO,CAACtB,IAAI,GAAG,EACnBsB,OAAO,CAACC,WAAW,EACnBD,OAAO,CAACE,eAAe,EACvBF,OAAO,CAACG,mBAAmB,EAC3BH,OAAO,CAACI,YAAY,EACpB,IAAIJ,OAAO,CAACK,QAAQ,IAAI,EAAE,GAAG,EAC7BL,OAAO,CAAC9B,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK,EAC3C8B,OAAO,CAACM,UAAU,CACnB,CAACR,IAAI,CAAC,GAAG,CAAC,CAAC,CACb,CAACA,IAAI,CAAC,IAAI,CAAC;IAEZ7E,YAAY,CAAC4E,UAAU,EAAE,QAAQ,IAAIU,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,yBAAyB,CAAC;IACzG1H,OAAO,CAACkG,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyB,OAA6B,GAAG,CACpC;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAGC,IAAY,iBACnB9F,OAAA,CAACtC,GAAG;MAACqI,KAAK,EAAC,MAAM;MAACC,KAAK,EAAE;QAAEC,UAAU,EAAE,WAAW;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAC,QAAA,EACtEL;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEf,KAAK,EAAE,WAAW;IAClBC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVa,QAAQ,EAAE,IAAI;IACdX,MAAM,EAAEA,CAACC,IAAY,EAAEW,MAAe,kBACpCzG,OAAA;MAAAmG,QAAA,gBACEnG,OAAA;QAAKgG,KAAK,EAAE;UACVE,UAAU,EAAE,GAAG;UACfQ,YAAY,EAAE,CAAC;UACfX,KAAK,EAAE,SAAS;UAChBY,MAAM,EAAE;QACV,CAAE;QACFC,OAAO,EAAEA,CAAA,KAAMrC,gBAAgB,CAACkC,MAAM,CAAC7C,EAAE,CAAE;QAAAuC,QAAA,EAExCL;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNvG,OAAA,CAAC/B,UAAU,CAACkC,IAAI;QAAC0G,IAAI,EAAC,WAAW;QAACb,KAAK,EAAE;UAAEc,QAAQ,EAAE;QAAG,CAAE;QAAAX,QAAA,EACvDM,MAAM,CAACM;MAAW;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAET,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGmB,KAAa,iBACpBhH,OAAA,CAACG,IAAI;MAAC8G,MAAM;MAACjB,KAAK,EAAE;QAAED,KAAK,EAAE;MAAO,CAAE;MAAAI,QAAA,EACnCzG,WAAW,CAACsH,KAAK;IAAC;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACP;IACDX,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGqB,OAAe,IAAKvH,aAAa,CAACuH,OAAO,CAAC;IACnDtB,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,qBAAqB;IAChCC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGqB,OAAe,IAAKvH,aAAa,CAACuH,OAAO;EACpD,CAAC,EACD;IACE1B,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGsB,OAAe,IAAKvH,aAAa,CAACuH,OAAO;EACpD,CAAC,EACD;IACE3B,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVyB,OAAO,EAAE5H,gBAAgB,CAACoF,GAAG,CAACyC,EAAE,KAAK;MAAEvB,IAAI,EAAEuB,EAAE,CAACC,KAAK;MAAEC,KAAK,EAAEF,EAAE,CAACE;IAAM,CAAC,CAAC,CAAC;IAC1E1B,MAAM,EAAGC,IAAY,IAAK;MACxB,MAAM0B,QAAmC,GAAG;QAC1C,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,MAAM;QACd,MAAM,EAAE;MACV,CAAC;MACD,oBACExH,OAAA,CAACtC,GAAG;QAACqI,KAAK,EAAEyB,QAAQ,CAAC1B,IAAI,CAAC,IAAI,SAAU;QAACE,KAAK,EAAE;UAAEE,UAAU,EAAE;QAAO,CAAE;QAAAC,QAAA,EACpEL;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEV;EACF,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,IAAY,iBACnB9F,OAAA;MAAMgG,KAAK,EAAE;QAAED,KAAK,EAAE;MAAO,CAAE;MAAAI,QAAA,EAAEL;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAEhD,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAAC4B,CAAC,EAAEhB,MAAM,kBAChBzG,OAAA,CAACvC,KAAK;MAACiK,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;MAACC,IAAI;MAAAxB,QAAA,EACtBtG,UAAU,CAAC4G,MAAM,CAAC,CAAC7B,GAAG,CAAC,CAACgD,GAAG,EAAEC,KAAK,kBACjC7H,OAAA,CAACtC,GAAG;QAAaqI,KAAK,EAAC,MAAM;QAAAI,QAAA,EAC1ByB;MAAG,GADIC,KAAK;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAEX,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAG9C,MAAc,IAAK;MAC1B,MAAM+E,QAAQ,GAAG/E,MAAM,KAAK,QAAQ;MACpC,oBACE/C,OAAA,CAAC1B,KAAK;QACJyE,MAAM,EAAE+E,QAAQ,GAAG,SAAS,GAAG,SAAU;QACzChC,IAAI,eACF9F,OAAA;UAAMgG,KAAK,EAAE;YACXD,KAAK,EAAE+B,QAAQ,GAAG,SAAS,GAAG,MAAM;YACpC5B,UAAU,EAAE;UACd,CAAE;UAAAC,QAAA,EACC2B,QAAQ,GAAG,IAAI,GAAG;QAAI;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEN,CAAC;IACDa,OAAO,EAAE7H,sBAAsB,CAACqF,GAAG,CAAC7B,MAAM,KAAK;MAC7C+C,IAAI,EAAE/C,MAAM,CAACuE,KAAK;MAClBC,KAAK,EAAExE,MAAM,CAACwE;IAChB,CAAC,CAAC;EACJ,CAAC,EACD;IACE/B,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVoC,KAAK,EAAE,OAAO;IACdlC,MAAM,EAAEA,CAAC4B,CAAC,EAAEhB,MAAM,KAAK;MACrB,MAAMuB,KAAyB,GAAG,CAChC;QACEtC,GAAG,EAAE,MAAM;QACXuC,IAAI,eAAEjI,OAAA,CAACrB,WAAW;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACrBe,KAAK,EAAE,MAAM;QACbV,OAAO,EAAEA,CAAA,KAAMrC,gBAAgB,CAACkC,MAAM,CAAC7C,EAAE;MAC3C,CAAC,EACD;QACE8B,GAAG,EAAE,MAAM;QACXuC,IAAI,eAAEjI,OAAA,CAACvB,YAAY;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACtBe,KAAK,EAAE,MAAM;QACbV,OAAO,EAAEA,CAAA,KAAMpG,QAAQ,CAAC,kBAAkBiG,MAAM,CAAC7C,EAAE,EAAE;MACvD,CAAC,EACD;QACE8B,GAAG,EAAE,QAAQ;QACbuC,IAAI,eAAEjI,OAAA,CAACpB,gBAAgB;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC1Be,KAAK,EAAEb,MAAM,CAAC1D,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;QACnD6D,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAC/B0C,MAAM,CAAC7C,EAAE,EACT6C,MAAM,CAAC1D,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,QAC3C;MACF,CAAC,EACD;QACE8D,IAAI,EAAE;MACR,CAAC,EACD;QACEnB,GAAG,EAAE,QAAQ;QACbuC,IAAI,eAAEjI,OAAA,CAACtB,cAAc;UAAA0H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACxBe,KAAK,EAAE,MAAM;QACbY,MAAM,EAAE,IAAI;QACZtB,OAAO,EAAEA,CAAA,KAAM;UACbjJ,KAAK,CAACwK,OAAO,CAAC;YACZ3C,KAAK,EAAE,aAAa;YACpB4C,OAAO,EAAE,gBAAgB;YACzBC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,IAAI;YAChBC,aAAa,EAAE;cAAEL,MAAM,EAAE;YAAK,CAAC;YAC/BM,IAAI,EAAEA,CAAA,KAAM7E,YAAY,CAAC8C,MAAM,CAAC7C,EAAE;UACpC,CAAC,CAAC;QACJ;MACF,CAAC,CACF;MAED,oBACE5D,OAAA,CAACvC,KAAK;QAACiK,IAAI,EAAC,OAAO;QAAAvB,QAAA,gBACjBnG,OAAA,CAACxC,MAAM;UACLqJ,IAAI,EAAC,SAAS;UACda,IAAI,EAAC,OAAO;UACZO,IAAI,eAAEjI,OAAA,CAACrB,WAAW;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBK,OAAO,EAAEA,CAAA,KAAMrC,gBAAgB,CAACkC,MAAM,CAAC7C,EAAE,CAAE;UAC3CoC,KAAK,EAAE;YAAEyC,YAAY,EAAE;UAAM,CAAE;UAAAtC,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvG,OAAA,CAACxC,MAAM;UACLkK,IAAI,EAAC,OAAO;UACZO,IAAI,eAAEjI,OAAA,CAACvB,YAAY;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBK,OAAO,EAAEA,CAAA,KAAMpG,QAAQ,CAAC,kBAAkBiG,MAAM,CAAC7C,EAAE,EAAE,CAAE;UACvDoC,KAAK,EAAE;YAAEyC,YAAY,EAAE;UAAM,CAAE;UAAAtC,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvG,OAAA,CAACzB,QAAQ;UAACmK,IAAI,EAAE;YAAEV;UAAM,CAAE;UAACW,OAAO,EAAE,CAAC,OAAO,CAAE;UAAAxC,QAAA,eAC5CnG,OAAA,CAACxC,MAAM;YACLkK,IAAI,EAAC,OAAO;YACZO,IAAI,eAAEjI,OAAA,CAAChB,YAAY;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBP,KAAK,EAAE;cAAEyC,YAAY,EAAE;YAAM;UAAE;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAEZ;EACF,CAAC,CACF;;EAED;EACA,MAAMqC,YAAwC,GAAG;IAC/CxH,eAAe;IACfyH,QAAQ,EAAGC,kBAA+B,IAAK;MAC7CzH,kBAAkB,CAACyH,kBAAkB,CAAC;IACxC,CAAC;IACDC,gBAAgB,EAAGtC,MAAe,KAAM;MACtClD,IAAI,EAAEkD,MAAM,CAAClD;IACf,CAAC;EACH,CAAC;EAED,oBACEvD,OAAA;IAAKgG,KAAK,EAAE;MAAEgD,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAA/C,QAAA,gBAEzEnG,OAAA;MAAKgG,KAAK,EAAE;QACViD,UAAU,EAAE,MAAM;QAClBD,OAAO,EAAE,MAAM;QACfP,YAAY,EAAE,KAAK;QACnB/B,YAAY,EAAE,MAAM;QACpByC,SAAS,EAAE;MACb,CAAE;MAAAhD,QAAA,gBACAnG,OAAA;QAAKgG,KAAK,EAAE;UAAEoD,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAE5C,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,gBAC3GnG,OAAA;UAAAmG,QAAA,gBACEnG,OAAA,CAAC/B,UAAU,CAACsL,KAAK;YAACC,KAAK,EAAE,CAAE;YAACxD,KAAK,EAAE;cAAEyD,MAAM,EAAE,CAAC;cAAE1D,KAAK,EAAE;YAAU,CAAE;YAAAI,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB,CAAC,eACnBvG,OAAA,CAAC/B,UAAU,CAACkC,IAAI;YAAC0G,IAAI,EAAC,WAAW;YAAAV,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACNvG,OAAA,CAACvC,KAAK;UAAA0I,QAAA,eACJnG,OAAA,CAACxC,MAAM;YACLyK,IAAI,eAAEjI,OAAA,CAACnB,cAAc;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBK,OAAO,EAAEA,CAAA,KAAM1E,aAAa,CAAC,CAAE;YAC/BzB,OAAO,EAAEA,OAAQ;YAAA0F,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNvG,OAAA,CAAC9B,GAAG;QAACwL,MAAM,EAAE,EAAG;QAAAvD,QAAA,gBACdnG,OAAA,CAAC7B,GAAG;UAACwL,IAAI,EAAE,CAAE;UAAAxD,QAAA,eACXnG,OAAA,CAACzC,IAAI;YAACmK,IAAI,EAAC,OAAO;YAAC1B,KAAK,EAAE;cAAE4D,SAAS,EAAE;YAAS,CAAE;YAAAzD,QAAA,eAChDnG,OAAA,CAAC5B,SAAS;cACRoH,KAAK,EAAC,0BAAM;cACZ+B,KAAK,EAAEzF,KAAK,CAACjB,KAAM;cACnBgJ,MAAM,eAAE7J,OAAA,CAAC1B,KAAK;gBAACwL,KAAK,EAAEhI,KAAK,CAACjB,KAAM;gBAACmF,KAAK,EAAE;kBAAE+D,eAAe,EAAE;gBAAU;cAAE;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7EyD,UAAU,EAAE;gBAAEjE,KAAK,EAAE,SAAS;gBAAEe,QAAQ,EAAE;cAAO;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvG,OAAA,CAAC7B,GAAG;UAACwL,IAAI,EAAE,CAAE;UAAAxD,QAAA,eACXnG,OAAA,CAACzC,IAAI;YAACmK,IAAI,EAAC,OAAO;YAAC1B,KAAK,EAAE;cAAE4D,SAAS,EAAE;YAAS,CAAE;YAAAzD,QAAA,eAChDnG,OAAA,CAAC5B,SAAS;cACRoH,KAAK,EAAC,oBAAK;cACX+B,KAAK,EAAEzF,KAAK,CAACE,MAAO;cACpB6H,MAAM,eAAE7J,OAAA,CAAC1B,KAAK;gBAACwL,KAAK,EAAEhI,KAAK,CAACE,MAAO;gBAACgE,KAAK,EAAE;kBAAE+D,eAAe,EAAE;gBAAU;cAAE;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9EyD,UAAU,EAAE;gBAAEjE,KAAK,EAAE,SAAS;gBAAEe,QAAQ,EAAE;cAAO;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvG,OAAA,CAAC7B,GAAG;UAACwL,IAAI,EAAE,CAAE;UAAAxD,QAAA,eACXnG,OAAA,CAACzC,IAAI;YAACmK,IAAI,EAAC,OAAO;YAAC1B,KAAK,EAAE;cAAE4D,SAAS,EAAE;YAAS,CAAE;YAAAzD,QAAA,eAChDnG,OAAA,CAAC5B,SAAS;cACRoH,KAAK,EAAC,oBAAK;cACX+B,KAAK,EAAEzF,KAAK,CAACG,OAAQ;cACrB4H,MAAM,eAAE7J,OAAA,CAAC1B,KAAK;gBAACwL,KAAK,EAAEhI,KAAK,CAACG,OAAQ;gBAAC+D,KAAK,EAAE;kBAAE+D,eAAe,EAAE;gBAAU;cAAE;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/EyD,UAAU,EAAE;gBAAEjE,KAAK,EAAE,SAAS;gBAAEe,QAAQ,EAAE;cAAO;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvG,OAAA,CAAC7B,GAAG;UAACwL,IAAI,EAAE,CAAE;UAAAxD,QAAA,eACXnG,OAAA,CAACzC,IAAI;YAACmK,IAAI,EAAC,OAAO;YAAC1B,KAAK,EAAE;cAAE4D,SAAS,EAAE;YAAS,CAAE;YAAAzD,QAAA,eAChDnG,OAAA,CAAC5B,SAAS;cACRoH,KAAK,EAAC,oBAAK;cACX+B,KAAK,EAAEzF,KAAK,CAACjB,KAAK,GAAG,CAAC,GAAG,CAAEiB,KAAK,CAACE,MAAM,GAAGF,KAAK,CAACjB,KAAK,GAAI,GAAG,EAAEoJ,OAAO,CAAC,CAAC,CAAC,GAAG,CAAE;cAC7EC,MAAM,EAAC,GAAG;cACVF,UAAU,EAAE;gBACVjE,KAAK,EAAEjE,KAAK,CAACjB,KAAK,GAAG,CAAC,IAAKiB,KAAK,CAACE,MAAM,GAAGF,KAAK,CAACjB,KAAK,GAAI,GAAG,GAAG,SAAS,GAAG,SAAS;gBACpFiG,QAAQ,EAAE;cACZ;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvG,OAAA,CAACzC,IAAI;MAACyI,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,eACpCnG,OAAA,CAACnC,IAAI;QACHyC,IAAI,EAAEA,IAAK;QACX6J,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEhH,YAAa;QACvB4C,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,gBAE5BnG,OAAA,CAACnC,IAAI,CAACwM,IAAI;UAAC9G,IAAI,EAAC,IAAI;UAAC+D,KAAK,EAAC,gBAAM;UAAAnB,QAAA,eAC/BnG,OAAA,CAAClC,KAAK;YACJwM,WAAW,EAAC,kCAAS;YACrBtE,KAAK,EAAE;cAAEL,KAAK,EAAE;YAAI,CAAE;YACtBkE,MAAM,eAAE7J,OAAA,CAACf,cAAc;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eACZvG,OAAA,CAACnC,IAAI,CAACwM,IAAI;UAAC9G,IAAI,EAAC,MAAM;UAAC+D,KAAK,EAAC,0BAAM;UAAAnB,QAAA,eACjCnG,OAAA,CAAClC,KAAK;YACJwM,WAAW,EAAC,4CAAS;YACrBtE,KAAK,EAAE;cAAEL,KAAK,EAAE;YAAI,CAAE;YACtBkE,MAAM,eAAE7J,OAAA,CAACf,cAAc;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eACZvG,OAAA,CAACnC,IAAI,CAACwM,IAAI;UAAC9G,IAAI,EAAC,UAAU;UAAC+D,KAAK,EAAC,oBAAK;UAAAnB,QAAA,eACpCnG,OAAA,CAACjC,MAAM;YACLuM,WAAW,EAAC,sCAAQ;YACpBtE,KAAK,EAAE;cAAEL,KAAK,EAAE;YAAI,CAAE;YACtB4E,UAAU;YACVC,UAAU,eAAExK,OAAA,CAACd,cAAc;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAE9B3G,gBAAgB,CAACoF,GAAG,CAAC6F,MAAM,iBAC1BzK,OAAA,CAACjC,MAAM,CAACmC,MAAM;cAAoBqH,KAAK,EAAEkD,MAAM,CAAClD,KAAM;cAAApB,QAAA,EACnDsE,MAAM,CAACnD;YAAK,GADKmD,MAAM,CAAClD,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZvG,OAAA,CAACnC,IAAI,CAACwM,IAAI;UAAC9G,IAAI,EAAC,QAAQ;UAAC+D,KAAK,EAAC,cAAI;UAAAnB,QAAA,eACjCnG,OAAA,CAACjC,MAAM;YACLuM,WAAW,EAAC,gCAAO;YACnBtE,KAAK,EAAE;cAAEL,KAAK,EAAE;YAAI,CAAE;YACtB4E,UAAU;YACVC,UAAU,eAAExK,OAAA,CAACd,cAAc;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAE9B5G,sBAAsB,CAACqF,GAAG,CAAC6F,MAAM,iBAChCzK,OAAA,CAACjC,MAAM,CAACmC,MAAM;cAAoBqH,KAAK,EAAEkD,MAAM,CAAClD,KAAM;cAAApB,QAAA,EACnDsE,MAAM,CAACnD;YAAK,GADKmD,MAAM,CAAClD,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZvG,OAAA,CAACnC,IAAI,CAACwM,IAAI;UAAAlE,QAAA,eACRnG,OAAA,CAACvC,KAAK;YAAA0I,QAAA,gBACJnG,OAAA,CAACxC,MAAM;cACLqJ,IAAI,EAAC,SAAS;cACd6D,QAAQ,EAAC,QAAQ;cACjBzC,IAAI,eAAEjI,OAAA,CAACf,cAAc;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB9F,OAAO,EAAEA,OAAQ;cAAA0F,QAAA,EAClB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvG,OAAA,CAACxC,MAAM;cACLoJ,OAAO,EAAEnD,WAAY;cACrBwE,IAAI,eAAEjI,OAAA,CAACb,aAAa;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EACzB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvG,OAAA,CAACzC,IAAI;MAAA4I,QAAA,gBACHnG,OAAA;QAAKgG,KAAK,EAAE;UACVoD,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpB5C,YAAY,EAAE,EAAE;UAChBsC,OAAO,EAAE,QAAQ;UACjB2B,YAAY,EAAE;QAChB,CAAE;QAAAxE,QAAA,gBACAnG,OAAA;UAAAmG,QAAA,eACEnG,OAAA,CAACvC,KAAK;YAACkK,IAAI;YAAAxB,QAAA,gBACTnG,OAAA,CAACxC,MAAM;cACLqJ,IAAI,EAAC,SAAS;cACdoB,IAAI,eAAEjI,OAAA,CAACxB,YAAY;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBK,OAAO,EAAEA,CAAA,KAAMpG,QAAQ,CAAC,kBAAkB,CAAE;cAC5CkH,IAAI,EAAC,OAAO;cACZ1B,KAAK,EAAE;gBAAEyC,YAAY,EAAE;cAAM,CAAE;cAAAtC,QAAA,EAChC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvG,OAAA,CAACxC,MAAM;cACLoN,QAAQ,EAAExJ,eAAe,CAAC4B,MAAM,KAAK,CAAE;cACvC4D,OAAO,EAAE3C,kBAAmB;cAC5B+B,KAAK,EAAE;gBAAEyC,YAAY,EAAE;cAAM,CAAE;cAAAtC,QAAA,EAChC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvG,OAAA,CAAChC,UAAU;cACTwH,KAAK,EAAE,YAAYpE,eAAe,CAAC4B,MAAM,QAAS;cAClD+D,WAAW,EAAC,sFAAgB;cAC5B8D,SAAS,EAAExG,iBAAkB;cAC7BuG,QAAQ,EAAExJ,eAAe,CAAC4B,MAAM,KAAK,CAAE;cACvCqF,MAAM,EAAC,0BAAM;cACbC,UAAU,EAAC,cAAI;cACfC,aAAa,EAAE;gBAAEL,MAAM,EAAE;cAAK,CAAE;cAAA/B,QAAA,eAEhCnG,OAAA,CAACxC,MAAM;gBACL0K,MAAM;gBACN0C,QAAQ,EAAExJ,eAAe,CAAC4B,MAAM,KAAK,CAAE;gBACvCgD,KAAK,EAAE;kBAAEyC,YAAY,EAAE;gBAAM,CAAE;gBAAAtC,QAAA,EAChC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvG,OAAA;UAAAmG,QAAA,eACEnG,OAAA,CAACvC,KAAK;YAAA0I,QAAA,gBACJnG,OAAA,CAACxC,MAAM;cACLyK,IAAI,eAAEjI,OAAA,CAAClB,gBAAgB;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BK,OAAO,EAAEnC,YAAa;cACtBmG,QAAQ,EAAEjK,QAAQ,CAACqC,MAAM,KAAK,CAAE;cAChCgD,KAAK,EAAE;gBAAEyC,YAAY,EAAE;cAAM,CAAE;cAAAtC,QAAA,EAChC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvG,OAAA,CAACxC,MAAM;cACLyK,IAAI,eAAEjI,OAAA,CAACjB,cAAc;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBK,OAAO,EAAEA,CAAA,KAAMhJ,OAAO,CAACkN,IAAI,CAAC,WAAW,CAAE;cACzC9E,KAAK,EAAE;gBAAEyC,YAAY,EAAE;cAAM,CAAE;cAAAtC,QAAA,EAChC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRnF,eAAe,CAAC4B,MAAM,GAAG,CAAC,iBACzBhD,OAAA,CAACtC,GAAG;cAACqI,KAAK,EAAC,MAAM;cAACC,KAAK,EAAE;gBAAEyD,MAAM,EAAE;cAAE,CAAE;cAAAtD,QAAA,GAAC,qBAClC,EAAC/E,eAAe,CAAC4B,MAAM,EAAC,SAC9B;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvG,OAAA,CAAC1C,KAAK;QACJsL,YAAY,EAAEA,YAAa;QAC3BrD,OAAO,EAAEA,OAAQ;QACjBwF,UAAU,EAAEpK,QAAS;QACrBqK,MAAM,EAAC,IAAI;QACXvK,OAAO,EAAEA,OAAQ;QACjBwK,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBxD,IAAI,EAAC,QAAQ;QACbyD,QAAQ;QACRnF,KAAK,EAAE;UACLiD,UAAU,EAAE,MAAM;UAClBR,YAAY,EAAE;QAChB,CAAE;QACF2C,YAAY,EAAEA,CAAC3E,MAAM,EAAEoB,KAAK,KAC1BA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,iBAAiB,GAAG,gBACvC;QACDnF,UAAU,EAAE;UACV2I,OAAO,EAAEtK,WAAW;UACpBE,QAAQ,EAAEA,QAAQ;UAClBJ,KAAK,EAAEA,KAAK;UACZyK,eAAe,EAAE7L,iBAAiB,CAAC6L,eAAe;UAClDC,eAAe,EAAE9L,iBAAiB,CAAC8L,eAAe;UAClDC,SAAS,EAAEA,CAAC3K,KAAK,EAAE4K,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAAS5K,KAAK,IAAI;UAC7C6K,eAAe,EAAEjM,iBAAiB,CAACiM,eAAe;UAClD7C,QAAQ,EAAEA,CAACvG,IAAI,EAAEoF,IAAI,KAAK;YACxB1G,cAAc,CAACsB,IAAI,CAAC;YACpBpB,WAAW,CAACwG,IAAI,IAAIjI,iBAAiB,CAAC0B,eAAe,CAAC;UACxD,CAAC;UACD6E,KAAK,EAAE;YACL2F,SAAS,EAAE,MAAM;YACjB/B,SAAS,EAAE;UACb;QACF;MAAE;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPvG,OAAA,CAAC3B,MAAM;MACLmH,KAAK,EAAC,0BAAM;MACZoG,SAAS,EAAC,OAAO;MACjBlE,IAAI,EAAC,OAAO;MACZmE,IAAI,EAAErK,aAAc;MACpBsK,OAAO,EAAEA,CAAA,KAAM;QACbrK,gBAAgB,CAAC,KAAK,CAAC;QACvBE,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAE;MACFoK,KAAK,EACHrK,cAAc,iBACZ1B,OAAA,CAACvC,KAAK;QAAA0I,QAAA,gBACJnG,OAAA,CAACxC,MAAM;UACLqJ,IAAI,EAAC,SAAS;UACdoB,IAAI,eAAEjI,OAAA,CAACvB,YAAY;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBK,OAAO,EAAEA,CAAA,KAAM;YACbnF,gBAAgB,CAAC,KAAK,CAAC;YACvBjB,QAAQ,CAAC,kBAAkBkB,cAAc,CAACkC,EAAE,EAAE,CAAC;UACjD,CAAE;UAAAuC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvG,OAAA,CAACxC,MAAM;UACLyK,IAAI,eAAEjI,OAAA,CAACpB,gBAAgB;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BK,OAAO,EAAEA,CAAA,KAAM;YACb7C,kBAAkB,CAChBrC,cAAc,CAACkC,EAAE,EACjBlC,cAAc,CAACqB,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,QACnD,CAAC;YACDtB,gBAAgB,CAAC,KAAK,CAAC;UACzB,CAAE;UAAA0E,QAAA,EAEDzE,cAAc,CAACqB,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;QAAI;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAEV;MAAAJ,QAAA,EAEAvE,aAAa,gBACZ5B,OAAA;QAAKgG,KAAK,EAAE;UAAE4D,SAAS,EAAE,QAAQ;UAAEZ,OAAO,EAAE;QAAS,CAAE;QAAA7C,QAAA,eACrDnG,OAAA;UAAAmG,QAAA,EAAK;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,GACJ7E,cAAc,gBAChB1B,OAAA,CAACV,aAAa;QAACuF,OAAO,EAAEnD;MAAe;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACxC;IAAI;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAClG,EAAA,CA7sBID,WAAqB;EAAA,QACVvC,IAAI,CAAC0C,OAAO,EACVnB,WAAW;AAAA;AAAA4M,EAAA,GAFxB5L,WAAqB;AA+sB3B,eAAeA,WAAW;AAAC,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
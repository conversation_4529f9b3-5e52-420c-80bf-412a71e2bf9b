{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/* eslint-disable react/no-array-index-key */\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport BackTop from './BackTop';\nimport FloatButton, { floatButtonPrefixCls } from './FloatButton';\nimport FloatButtonGroup from './FloatButtonGroup';\nconst PureFloatButton = _a => {\n  var {\n      backTop\n    } = _a,\n    props = __rest(_a, [\"backTop\"]);\n  return backTop ? /*#__PURE__*/React.createElement(BackTop, Object.assign({}, props, {\n    visibilityHeight: 0\n  })) : /*#__PURE__*/React.createElement(FloatButton, Object.assign({}, props));\n};\n/** @private Internal Component. Do not use in your production. */\nconst PurePanel = _a => {\n  var {\n      className,\n      items\n    } = _a,\n    props = __rest(_a, [\"className\", \"items\"]);\n  const {\n    prefixCls: customizePrefixCls\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const pureCls = \"\".concat(prefixCls, \"-pure\");\n  if (items) {\n    return /*#__PURE__*/React.createElement(FloatButtonGroup, Object.assign({\n      className: classNames(className, pureCls)\n    }, props), items.map((item, index) => (/*#__PURE__*/React.createElement(PureFloatButton, Object.assign({\n      key: index\n    }, item)))));\n  }\n  return /*#__PURE__*/React.createElement(PureFloatButton, Object.assign({\n    className: classNames(className, pureCls)\n  }, props));\n};\nexport default PurePanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
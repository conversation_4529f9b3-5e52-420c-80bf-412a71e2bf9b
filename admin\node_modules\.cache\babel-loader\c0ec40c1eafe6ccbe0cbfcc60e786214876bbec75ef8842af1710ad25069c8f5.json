{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { CONTAINER_MAX_OFFSET } from '../../_util/hooks/useZIndex';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genMessageStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    boxShadow,\n    colorText,\n    colorSuccess,\n    colorError,\n    colorWarning,\n    colorInfo,\n    fontSizeLG,\n    motionEaseInOutCirc,\n    motionDurationSlow,\n    marginXS,\n    paddingXS,\n    borderRadiusLG,\n    zIndexPopup,\n    // Custom token\n    contentPadding,\n    contentBg\n  } = token;\n  const noticeCls = `${componentCls}-notice`;\n  const messageMoveIn = new Keyframes('MessageMoveIn', {\n    '0%': {\n      padding: 0,\n      transform: 'translateY(-100%)',\n      opacity: 0\n    },\n    '100%': {\n      padding: paddingXS,\n      transform: 'translateY(0)',\n      opacity: 1\n    }\n  });\n  const messageMoveOut = new Keyframes('MessageMoveOut', {\n    '0%': {\n      maxHeight: token.height,\n      padding: paddingXS,\n      opacity: 1\n    },\n    '100%': {\n      maxHeight: 0,\n      padding: 0,\n      opacity: 0\n    }\n  });\n  const noticeStyle = {\n    padding: paddingXS,\n    textAlign: 'center',\n    [`${componentCls}-custom-content`]: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    [`${componentCls}-custom-content > ${iconCls}`]: {\n      marginInlineEnd: marginXS,\n      // affected by ltr or rtl\n      fontSize: fontSizeLG\n    },\n    [`${noticeCls}-content`]: {\n      display: 'inline-block',\n      padding: contentPadding,\n      background: contentBg,\n      borderRadius: borderRadiusLG,\n      boxShadow,\n      pointerEvents: 'all'\n    },\n    [`${componentCls}-success > ${iconCls}`]: {\n      color: colorSuccess\n    },\n    [`${componentCls}-error > ${iconCls}`]: {\n      color: colorError\n    },\n    [`${componentCls}-warning > ${iconCls}`]: {\n      color: colorWarning\n    },\n    [`${componentCls}-info > ${iconCls},\n      ${componentCls}-loading > ${iconCls}`]: {\n      color: colorInfo\n    }\n  };\n  return [\n  // ============================ Holder ============================\n  {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      color: colorText,\n      position: 'fixed',\n      top: marginXS,\n      width: '100%',\n      pointerEvents: 'none',\n      zIndex: zIndexPopup,\n      [`${componentCls}-move-up`]: {\n        animationFillMode: 'forwards'\n      },\n      [`\n        ${componentCls}-move-up-appear,\n        ${componentCls}-move-up-enter\n      `]: {\n        animationName: messageMoveIn,\n        animationDuration: motionDurationSlow,\n        animationPlayState: 'paused',\n        animationTimingFunction: motionEaseInOutCirc\n      },\n      [`\n        ${componentCls}-move-up-appear${componentCls}-move-up-appear-active,\n        ${componentCls}-move-up-enter${componentCls}-move-up-enter-active\n      `]: {\n        animationPlayState: 'running'\n      },\n      [`${componentCls}-move-up-leave`]: {\n        animationName: messageMoveOut,\n        animationDuration: motionDurationSlow,\n        animationPlayState: 'paused',\n        animationTimingFunction: motionEaseInOutCirc\n      },\n      [`${componentCls}-move-up-leave${componentCls}-move-up-leave-active`]: {\n        animationPlayState: 'running'\n      },\n      '&-rtl': {\n        direction: 'rtl',\n        span: {\n          direction: 'rtl'\n        }\n      }\n    })\n  },\n  // ============================ Notice ============================\n  {\n    [componentCls]: {\n      [`${noticeCls}-wrapper`]: Object.assign({}, noticeStyle)\n    }\n  },\n  // ============================= Pure =============================\n  {\n    [`${componentCls}-notice-pure-panel`]: Object.assign(Object.assign({}, noticeStyle), {\n      padding: 0,\n      textAlign: 'start'\n    })\n  }];\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexPopupBase + CONTAINER_MAX_OFFSET + 10,\n  contentBg: token.colorBgElevated,\n  contentPadding: `${(token.controlHeightLG - token.fontSize * token.lineHeight) / 2}px ${token.paddingSM}px`\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Message', token => {\n  // Gen-style functions here\n  const combinedToken = mergeToken(token, {\n    height: 150\n  });\n  return [genMessageStyle(combinedToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["Keyframes", "CONTAINER_MAX_OFFSET", "resetComponent", "genStyleHooks", "mergeToken", "genMessageStyle", "token", "componentCls", "iconCls", "boxShadow", "colorText", "colorSuccess", "colorError", "colorWarning", "colorInfo", "fontSizeLG", "motionEaseInOutCirc", "motionDurationSlow", "marginXS", "paddingXS", "borderRadiusLG", "zIndexPopup", "contentPadding", "contentBg", "noticeCls", "messageMoveIn", "padding", "transform", "opacity", "messageMoveOut", "maxHeight", "height", "noticeStyle", "textAlign", "display", "alignItems", "marginInlineEnd", "fontSize", "background", "borderRadius", "pointerEvents", "color", "Object", "assign", "position", "top", "width", "zIndex", "animationFillMode", "animationName", "animationDuration", "animationPlayState", "animationTimingFunction", "direction", "span", "prepareComponentToken", "zIndexPopupBase", "colorBgElevated", "controlHeightLG", "lineHeight", "paddingSM", "combinedToken"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/message/style/index.js"], "sourcesContent": ["import { Keyframes } from '@ant-design/cssinjs';\nimport { CONTAINER_MAX_OFFSET } from '../../_util/hooks/useZIndex';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genMessageStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    boxShadow,\n    colorText,\n    colorSuccess,\n    colorError,\n    colorWarning,\n    colorInfo,\n    fontSizeLG,\n    motionEaseInOutCirc,\n    motionDurationSlow,\n    marginXS,\n    paddingXS,\n    borderRadiusLG,\n    zIndexPopup,\n    // Custom token\n    contentPadding,\n    contentBg\n  } = token;\n  const noticeCls = `${componentCls}-notice`;\n  const messageMoveIn = new Keyframes('MessageMoveIn', {\n    '0%': {\n      padding: 0,\n      transform: 'translateY(-100%)',\n      opacity: 0\n    },\n    '100%': {\n      padding: paddingXS,\n      transform: 'translateY(0)',\n      opacity: 1\n    }\n  });\n  const messageMoveOut = new Keyframes('MessageMoveOut', {\n    '0%': {\n      maxHeight: token.height,\n      padding: paddingXS,\n      opacity: 1\n    },\n    '100%': {\n      maxHeight: 0,\n      padding: 0,\n      opacity: 0\n    }\n  });\n  const noticeStyle = {\n    padding: paddingXS,\n    textAlign: 'center',\n    [`${componentCls}-custom-content`]: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    [`${componentCls}-custom-content > ${iconCls}`]: {\n      marginInlineEnd: marginXS,\n      // affected by ltr or rtl\n      fontSize: fontSizeLG\n    },\n    [`${noticeCls}-content`]: {\n      display: 'inline-block',\n      padding: contentPadding,\n      background: contentBg,\n      borderRadius: borderRadiusLG,\n      boxShadow,\n      pointerEvents: 'all'\n    },\n    [`${componentCls}-success > ${iconCls}`]: {\n      color: colorSuccess\n    },\n    [`${componentCls}-error > ${iconCls}`]: {\n      color: colorError\n    },\n    [`${componentCls}-warning > ${iconCls}`]: {\n      color: colorWarning\n    },\n    [`${componentCls}-info > ${iconCls},\n      ${componentCls}-loading > ${iconCls}`]: {\n      color: colorInfo\n    }\n  };\n  return [\n  // ============================ Holder ============================\n  {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      color: colorText,\n      position: 'fixed',\n      top: marginXS,\n      width: '100%',\n      pointerEvents: 'none',\n      zIndex: zIndexPopup,\n      [`${componentCls}-move-up`]: {\n        animationFillMode: 'forwards'\n      },\n      [`\n        ${componentCls}-move-up-appear,\n        ${componentCls}-move-up-enter\n      `]: {\n        animationName: messageMoveIn,\n        animationDuration: motionDurationSlow,\n        animationPlayState: 'paused',\n        animationTimingFunction: motionEaseInOutCirc\n      },\n      [`\n        ${componentCls}-move-up-appear${componentCls}-move-up-appear-active,\n        ${componentCls}-move-up-enter${componentCls}-move-up-enter-active\n      `]: {\n        animationPlayState: 'running'\n      },\n      [`${componentCls}-move-up-leave`]: {\n        animationName: messageMoveOut,\n        animationDuration: motionDurationSlow,\n        animationPlayState: 'paused',\n        animationTimingFunction: motionEaseInOutCirc\n      },\n      [`${componentCls}-move-up-leave${componentCls}-move-up-leave-active`]: {\n        animationPlayState: 'running'\n      },\n      '&-rtl': {\n        direction: 'rtl',\n        span: {\n          direction: 'rtl'\n        }\n      }\n    })\n  },\n  // ============================ Notice ============================\n  {\n    [componentCls]: {\n      [`${noticeCls}-wrapper`]: Object.assign({}, noticeStyle)\n    }\n  },\n  // ============================= Pure =============================\n  {\n    [`${componentCls}-notice-pure-panel`]: Object.assign(Object.assign({}, noticeStyle), {\n      padding: 0,\n      textAlign: 'start'\n    })\n  }];\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexPopupBase + CONTAINER_MAX_OFFSET + 10,\n  contentBg: token.colorBgElevated,\n  contentPadding: `${(token.controlHeightLG - token.fontSize * token.lineHeight) / 2}px ${token.paddingSM}px`\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Message', token => {\n  // Gen-style functions here\n  const combinedToken = mergeToken(token, {\n    height: 150\n  });\n  return [genMessageStyle(combinedToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC,OAAO;IACPC,SAAS;IACTC,SAAS;IACTC,YAAY;IACZC,UAAU;IACVC,YAAY;IACZC,SAAS;IACTC,UAAU;IACVC,mBAAmB;IACnBC,kBAAkB;IAClBC,QAAQ;IACRC,SAAS;IACTC,cAAc;IACdC,WAAW;IACX;IACAC,cAAc;IACdC;EACF,CAAC,GAAGjB,KAAK;EACT,MAAMkB,SAAS,GAAG,GAAGjB,YAAY,SAAS;EAC1C,MAAMkB,aAAa,GAAG,IAAIzB,SAAS,CAAC,eAAe,EAAE;IACnD,IAAI,EAAE;MACJ0B,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,mBAAmB;MAC9BC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNF,OAAO,EAAEP,SAAS;MAClBQ,SAAS,EAAE,eAAe;MAC1BC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMC,cAAc,GAAG,IAAI7B,SAAS,CAAC,gBAAgB,EAAE;IACrD,IAAI,EAAE;MACJ8B,SAAS,EAAExB,KAAK,CAACyB,MAAM;MACvBL,OAAO,EAAEP,SAAS;MAClBS,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNE,SAAS,EAAE,CAAC;MACZJ,OAAO,EAAE,CAAC;MACVE,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMI,WAAW,GAAG;IAClBN,OAAO,EAAEP,SAAS;IAClBc,SAAS,EAAE,QAAQ;IACnB,CAAC,GAAG1B,YAAY,iBAAiB,GAAG;MAClC2B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAC;IACD,CAAC,GAAG5B,YAAY,qBAAqBC,OAAO,EAAE,GAAG;MAC/C4B,eAAe,EAAElB,QAAQ;MACzB;MACAmB,QAAQ,EAAEtB;IACZ,CAAC;IACD,CAAC,GAAGS,SAAS,UAAU,GAAG;MACxBU,OAAO,EAAE,cAAc;MACvBR,OAAO,EAAEJ,cAAc;MACvBgB,UAAU,EAAEf,SAAS;MACrBgB,YAAY,EAAEnB,cAAc;MAC5BX,SAAS;MACT+B,aAAa,EAAE;IACjB,CAAC;IACD,CAAC,GAAGjC,YAAY,cAAcC,OAAO,EAAE,GAAG;MACxCiC,KAAK,EAAE9B;IACT,CAAC;IACD,CAAC,GAAGJ,YAAY,YAAYC,OAAO,EAAE,GAAG;MACtCiC,KAAK,EAAE7B;IACT,CAAC;IACD,CAAC,GAAGL,YAAY,cAAcC,OAAO,EAAE,GAAG;MACxCiC,KAAK,EAAE5B;IACT,CAAC;IACD,CAAC,GAAGN,YAAY,WAAWC,OAAO;AACtC,QAAQD,YAAY,cAAcC,OAAO,EAAE,GAAG;MACxCiC,KAAK,EAAE3B;IACT;EACF,CAAC;EACD,OAAO;EACP;EACA;IACE,CAACP,YAAY,GAAGmC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzC,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE;MACtEmC,KAAK,EAAE/B,SAAS;MAChBkC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE3B,QAAQ;MACb4B,KAAK,EAAE,MAAM;MACbN,aAAa,EAAE,MAAM;MACrBO,MAAM,EAAE1B,WAAW;MACnB,CAAC,GAAGd,YAAY,UAAU,GAAG;QAC3ByC,iBAAiB,EAAE;MACrB,CAAC;MACD,CAAC;AACP,UAAUzC,YAAY;AACtB,UAAUA,YAAY;AACtB,OAAO,GAAG;QACF0C,aAAa,EAAExB,aAAa;QAC5ByB,iBAAiB,EAAEjC,kBAAkB;QACrCkC,kBAAkB,EAAE,QAAQ;QAC5BC,uBAAuB,EAAEpC;MAC3B,CAAC;MACD,CAAC;AACP,UAAUT,YAAY,kBAAkBA,YAAY;AACpD,UAAUA,YAAY,iBAAiBA,YAAY;AACnD,OAAO,GAAG;QACF4C,kBAAkB,EAAE;MACtB,CAAC;MACD,CAAC,GAAG5C,YAAY,gBAAgB,GAAG;QACjC0C,aAAa,EAAEpB,cAAc;QAC7BqB,iBAAiB,EAAEjC,kBAAkB;QACrCkC,kBAAkB,EAAE,QAAQ;QAC5BC,uBAAuB,EAAEpC;MAC3B,CAAC;MACD,CAAC,GAAGT,YAAY,iBAAiBA,YAAY,uBAAuB,GAAG;QACrE4C,kBAAkB,EAAE;MACtB,CAAC;MACD,OAAO,EAAE;QACPE,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAE;UACJD,SAAS,EAAE;QACb;MACF;IACF,CAAC;EACH,CAAC;EACD;EACA;IACE,CAAC9C,YAAY,GAAG;MACd,CAAC,GAAGiB,SAAS,UAAU,GAAGkB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEX,WAAW;IACzD;EACF,CAAC;EACD;EACA;IACE,CAAC,GAAGzB,YAAY,oBAAoB,GAAGmC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEX,WAAW,CAAC,EAAE;MACnFN,OAAO,EAAE,CAAC;MACVO,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMsB,qBAAqB,GAAGjD,KAAK,KAAK;EAC7Ce,WAAW,EAAEf,KAAK,CAACkD,eAAe,GAAGvD,oBAAoB,GAAG,EAAE;EAC9DsB,SAAS,EAAEjB,KAAK,CAACmD,eAAe;EAChCnC,cAAc,EAAE,GAAG,CAAChB,KAAK,CAACoD,eAAe,GAAGpD,KAAK,CAAC+B,QAAQ,GAAG/B,KAAK,CAACqD,UAAU,IAAI,CAAC,MAAMrD,KAAK,CAACsD,SAAS;AACzG,CAAC,CAAC;AACF;AACA,eAAezD,aAAa,CAAC,SAAS,EAAEG,KAAK,IAAI;EAC/C;EACA,MAAMuD,aAAa,GAAGzD,UAAU,CAACE,KAAK,EAAE;IACtCyB,MAAM,EAAE;EACV,CAAC,CAAC;EACF,OAAO,CAAC1B,eAAe,CAACwD,aAAa,CAAC,CAAC;AACzC,CAAC,EAAEN,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
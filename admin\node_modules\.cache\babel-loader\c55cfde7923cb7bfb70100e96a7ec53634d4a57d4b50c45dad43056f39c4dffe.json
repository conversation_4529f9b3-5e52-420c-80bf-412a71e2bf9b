{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, Form, Input, Select, DatePicker, Row, Col, Statistic, message, Modal, Descriptions } from 'antd';\nimport { SearchOutlined, EyeOutlined, EditOutlined, ExportOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\n// 模拟订单数据 - 包含所有状态的订单\nconst mockOrders = [\n// 已激活订单\n{\n  id: 1,\n  orderNo: 'ORD202401150001',\n  customerName: '张三',\n  customerPhone: '13800138001',\n  customerIdCard: '110101199001011234',\n  productName: '中国移动5G畅享套餐',\n  operator: '中国移动',\n  deliveryAddress: '北京市朝阳区建国门外大街1号国贸大厦A座1001室',\n  status: 'activated',\n  priority: 'normal',\n  logisticsCompany: '顺丰速运',\n  trackingNumber: 'SF1234567890123',\n  shippedAt: '2024-01-15 10:45:00',\n  estimatedDelivery: '2024-01-16 18:00:00',\n  createdAt: '2024-01-15 10:30:00',\n  updatedAt: '2024-01-15 11:00:00'\n},\n// 待处理订单\n{\n  id: 2,\n  orderNo: 'ORD202401150002',\n  customerName: '李四',\n  customerPhone: '13800138002',\n  customerIdCard: '310101198505155678',\n  productName: '中国电信5G精选套餐',\n  operator: '中国电信',\n  deliveryAddress: '上海市浦东新区陆家嘴环路1000号恒生银行大厦50楼',\n  status: 'pending',\n  priority: 'high',\n  createdAt: '2024-01-15 11:15:00',\n  updatedAt: '2024-01-15 11:15:00'\n},\n// 开卡中订单\n{\n  id: 3,\n  orderNo: 'ORD202401150003',\n  customerName: '王五',\n  customerPhone: '13800138003',\n  customerIdCard: '******************',\n  productName: '中国联通青春套餐',\n  operator: '中国联通',\n  deliveryAddress: '广州市天河区珠江新城花城大道85号高德置地广场B2栋2001',\n  status: 'processing',\n  priority: 'normal',\n  createdAt: '2024-01-15 12:00:00',\n  updatedAt: '2024-01-15 12:00:00'\n},\n// 已发货订单\n{\n  id: 4,\n  orderNo: 'ORD202401150004',\n  customerName: '赵六',\n  customerPhone: '13800138004',\n  customerIdCard: '******************',\n  productName: '中国广电智慧套餐',\n  operator: '中国广电',\n  deliveryAddress: '深圳市南山区深南大道9988号华润置地大厦A座3501室',\n  status: 'shipped',\n  priority: 'normal',\n  logisticsCompany: '中通快递',\n  trackingNumber: 'ZTO9876543210987',\n  shippedAt: '2024-01-15 13:45:00',\n  estimatedDelivery: '2024-01-17 12:00:00',\n  createdAt: '2024-01-15 13:30:00',\n  updatedAt: '2024-01-15 14:00:00'\n},\n// 开卡中订单\n{\n  id: 5,\n  orderNo: 'ORD202401150005',\n  customerName: '钱七',\n  customerPhone: '13800138005',\n  customerIdCard: '320101199306154444',\n  productName: '中国移动5G尊享套餐',\n  operator: '中国移动',\n  deliveryAddress: '南京市鼓楼区中山路200号德基广场二期A座1801室',\n  status: 'processing',\n  priority: 'high',\n  createdAt: '2024-01-15 14:30:00',\n  updatedAt: '2024-01-15 14:30:00'\n},\n// 开卡失败订单\n{\n  id: 6,\n  orderNo: 'ORD202401150006',\n  customerName: '孙八',\n  customerPhone: '13800138006',\n  customerIdCard: '510101199408255555',\n  productName: '中国电信天翼套餐',\n  operator: '中国电信',\n  deliveryAddress: '成都市锦江区红星路三段1号IFS国际金融中心2号楼2501室',\n  status: 'failed',\n  priority: 'urgent',\n  createdAt: '2024-01-15 15:00:00',\n  updatedAt: '2024-01-15 16:00:00'\n},\n// 已取消订单\n{\n  id: 7,\n  orderNo: 'ORD202401150007',\n  customerName: '周九',\n  customerPhone: '13800138007',\n  customerIdCard: '******************',\n  productName: '中国联通青春套餐',\n  operator: '中国联通',\n  deliveryAddress: '长沙市岳麓区麓山南路932号中南大学科技园',\n  status: 'cancelled',\n  priority: 'low',\n  createdAt: '2024-01-15 16:20:00',\n  updatedAt: '2024-01-15 17:00:00'\n}];\nconst OrderList = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState(mockOrders);\n  const [filteredOrders, setFilteredOrders] = useState(mockOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const navigate = useNavigate();\n\n  // 状态颜色映射\n  const getStatusColor = status => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      pending_upload: 'purple',\n      shipped: 'cyan',\n      activated: 'green',\n      failed: 'red',\n      cancelled: 'default'\n    };\n    return colors[status] || 'default';\n  };\n\n  // 状态文本映射\n  const getStatusText = status => {\n    const texts = {\n      pending: '待处理',\n      processing: '开卡中',\n      pending_upload: '待上传三证',\n      shipped: '已发货',\n      activated: '已激活',\n      failed: '开卡失败',\n      cancelled: '已取消'\n    };\n    return texts[status] || status;\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = priority => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red'\n    };\n    return colors[priority] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = priority => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急'\n    };\n    return texts[priority] || '普通';\n  };\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    pending_upload: filteredOrders.filter(order => order.status === 'pending_upload').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    failed: filteredOrders.filter(order => order.status === 'failed').length,\n    cancelled: filteredOrders.filter(order => order.status === 'cancelled').length\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '订单状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物流信息',\n    key: 'logistics',\n    width: 200,\n    render: (_, record) => {\n      if (record.status === 'shipped' || record.status === 'activated') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 600,\n              fontSize: '12px'\n            },\n            children: record.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#666'\n            },\n            children: record.trackingNumber || '暂无快递单号'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this), record.shippedAt && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#999'\n            },\n            children: [\"\\u53D1\\u8D27: \", record.shippedAt.split(' ')[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: \"\\u672A\\u53D1\\u8D27\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditOrder(record.id),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleSearch = values => {\n    setLoading(true);\n    setTimeout(() => {\n      let filtered = [...orders];\n      if (values.orderNo) {\n        filtered = filtered.filter(order => order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase()));\n      }\n      if (values.customerName) {\n        filtered = filtered.filter(order => order.customerName.toLowerCase().includes(values.customerName.toLowerCase()));\n      }\n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      if (values.status) {\n        filtered = filtered.filter(order => order.status === values.status);\n      }\n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockOrders);\n      setFilteredOrders(mockOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n  const handleViewOrder = order => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n  const handleEditOrder = id => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#1890ff'\n        },\n        children: \"\\u8BA2\\u5355\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u7BA1\\u7406\\u6240\\u6709\\u8BA2\\u5355\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA2\\u5355\\u6570\",\n            value: stats.total,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\",\n            value: stats.pending,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5BA1\\u6838\\u4E2D\",\n            value: stats.reviewing,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F00\\u5361\\u4E2D\",\n            value: stats.processing,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u53D1\\u8D27\",\n            value: stats.shipped,\n            valueStyle: {\n              color: '#13c2c2'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u6FC0\\u6D3B\",\n            value: stats.activated,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"orderNo\",\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",\n            style: {\n              width: 150\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"customerName\",\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",\n            style: {\n              width: 120\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"operator\",\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u79FB\\u52A8\",\n              children: \"\\u4E2D\\u56FD\\u79FB\\u52A8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u7535\\u4FE1\",\n              children: \"\\u4E2D\\u56FD\\u7535\\u4FE1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u8054\\u901A\",\n              children: \"\\u4E2D\\u56FD\\u8054\\u901A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u5E7F\\u7535\",\n              children: \"\\u4E2D\\u56FD\\u5E7F\\u7535\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending\",\n              children: \"\\u5F85\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"reviewing\",\n              children: \"\\u5BA1\\u6838\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"processing\",\n              children: \"\\u5F00\\u5361\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"shipped\",\n              children: \"\\u5DF2\\u53D1\\u8D27\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"activated\",\n              children: \"\\u5DF2\\u6FC0\\u6D3B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"failed\",\n              children: \"\\u5F00\\u5361\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"cancelled\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 60\n            }, this),\n            children: \"\\u641C\\u7D22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", filteredOrders.length, \" \\u6761\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleExport,\n            icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 50\n            }, this),\n            children: \"\\u5BFC\\u51FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredOrders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BA2\\u5355\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          span: 2,\n          children: selectedOrder.orderNo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: selectedOrder.customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8054\\u7CFB\\u7535\\u8BDD\",\n          children: selectedOrder.customerPhone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n          span: 2,\n          children: selectedOrder.productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: selectedOrder.operator\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(selectedOrder.status),\n            children: getStatusText(selectedOrder.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 13\n        }, this), (selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7269\\u6D41\\u516C\\u53F8\",\n            children: selectedOrder.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5FEB\\u9012\\u5355\\u53F7\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              code: true,\n              children: selectedOrder.trackingNumber || '暂无'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u53D1\\u8D27\\u65F6\\u95F4\",\n            children: selectedOrder.shippedAt || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u9884\\u8BA1\\u9001\\u8FBE\",\n            children: selectedOrder.estimatedDelivery || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: selectedOrder.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: selectedOrder.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 420,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderList, \"2scKhS/yQSUqS+yPfN1bc24iLLU=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = OrderList;\nexport default OrderList;\nvar _c;\n$RefreshReg$(_c, \"OrderList\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Form", "Input", "Select", "DatePicker", "Row", "Col", "Statistic", "message", "Modal", "Descriptions", "SearchOutlined", "EyeOutlined", "EditOutlined", "ExportOutlined", "ReloadOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "RangePicker", "mockOrders", "id", "orderNo", "customerName", "customerPhone", "customerIdCard", "productName", "operator", "deliveryAddress", "status", "priority", "logisticsCompany", "trackingNumber", "shippedAt", "estimatedDelivery", "createdAt", "updatedAt", "OrderList", "_s", "form", "useForm", "orders", "setOrders", "filteredOrders", "setFilteredOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "detailModalVisible", "setDetailModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "navigate", "getStatusColor", "colors", "pending", "processing", "pending_upload", "shipped", "activated", "failed", "cancelled", "getStatusText", "texts", "getPriorityColor", "low", "normal", "high", "urgent", "getPriorityText", "stats", "total", "length", "filter", "order", "columns", "title", "dataIndex", "key", "width", "render", "text", "code", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "record", "fontWeight", "color", "ellipsis", "split", "type", "size", "icon", "onClick", "handleViewOrder", "handleEditOrder", "handleSearch", "values", "setTimeout", "filtered", "toLowerCase", "includes", "handleRefresh", "success", "info", "handleExport", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "reviewing", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "placeholder", "Option", "htmlType", "display", "justifyContent", "alignItems", "strong", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "footer", "column", "bordered", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Modal,\n  Descriptions,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  FilterOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { useNavigate } from 'react-router-dom';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\ninterface Order {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  customerIdCard: string;\n  productName: string;\n  operator: string;\n  deliveryAddress: string;\n  status: 'pending' | 'processing' | 'pending_upload' | 'shipped' | 'activated' | 'cancelled' | 'failed';\n  priority?: 'low' | 'normal' | 'high' | 'urgent';\n  logisticsCompany?: string; // 物流公司\n  trackingNumber?: string; // 快递单号\n  shippedAt?: string; // 发货时间\n  estimatedDelivery?: string; // 预计送达时间\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 模拟订单数据 - 包含所有状态的订单\nconst mockOrders: Order[] = [\n  // 已激活订单\n  {\n    id: 1,\n    orderNo: 'ORD202401150001',\n    customerName: '张三',\n    customerPhone: '13800138001',\n    customerIdCard: '110101199001011234',\n    productName: '中国移动5G畅享套餐',\n    operator: '中国移动',\n    deliveryAddress: '北京市朝阳区建国门外大街1号国贸大厦A座1001室',\n    status: 'activated',\n    priority: 'normal',\n    logisticsCompany: '顺丰速运',\n    trackingNumber: 'SF1234567890123',\n    shippedAt: '2024-01-15 10:45:00',\n    estimatedDelivery: '2024-01-16 18:00:00',\n    createdAt: '2024-01-15 10:30:00',\n    updatedAt: '2024-01-15 11:00:00',\n  },\n  // 待处理订单\n  {\n    id: 2,\n    orderNo: 'ORD202401150002',\n    customerName: '李四',\n    customerPhone: '13800138002',\n    customerIdCard: '310101198505155678',\n    productName: '中国电信5G精选套餐',\n    operator: '中国电信',\n    deliveryAddress: '上海市浦东新区陆家嘴环路1000号恒生银行大厦50楼',\n    status: 'pending',\n    priority: 'high',\n    createdAt: '2024-01-15 11:15:00',\n    updatedAt: '2024-01-15 11:15:00',\n  },\n  // 开卡中订单\n  {\n    id: 3,\n    orderNo: 'ORD202401150003',\n    customerName: '王五',\n    customerPhone: '13800138003',\n    customerIdCard: '******************',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n    deliveryAddress: '广州市天河区珠江新城花城大道85号高德置地广场B2栋2001',\n    status: 'processing',\n    priority: 'normal',\n    createdAt: '2024-01-15 12:00:00',\n    updatedAt: '2024-01-15 12:00:00',\n  },\n  // 已发货订单\n  {\n    id: 4,\n    orderNo: 'ORD202401150004',\n    customerName: '赵六',\n    customerPhone: '13800138004',\n    customerIdCard: '******************',\n    productName: '中国广电智慧套餐',\n    operator: '中国广电',\n    deliveryAddress: '深圳市南山区深南大道9988号华润置地大厦A座3501室',\n    status: 'shipped',\n    priority: 'normal',\n    logisticsCompany: '中通快递',\n    trackingNumber: 'ZTO9876543210987',\n    shippedAt: '2024-01-15 13:45:00',\n    estimatedDelivery: '2024-01-17 12:00:00',\n    createdAt: '2024-01-15 13:30:00',\n    updatedAt: '2024-01-15 14:00:00',\n  },\n  // 开卡中订单\n  {\n    id: 5,\n    orderNo: 'ORD202401150005',\n    customerName: '钱七',\n    customerPhone: '13800138005',\n    customerIdCard: '320101199306154444',\n    productName: '中国移动5G尊享套餐',\n    operator: '中国移动',\n    deliveryAddress: '南京市鼓楼区中山路200号德基广场二期A座1801室',\n    status: 'processing',\n    priority: 'high',\n    createdAt: '2024-01-15 14:30:00',\n    updatedAt: '2024-01-15 14:30:00',\n  },\n  // 开卡失败订单\n  {\n    id: 6,\n    orderNo: 'ORD202401150006',\n    customerName: '孙八',\n    customerPhone: '13800138006',\n    customerIdCard: '510101199408255555',\n    productName: '中国电信天翼套餐',\n    operator: '中国电信',\n    deliveryAddress: '成都市锦江区红星路三段1号IFS国际金融中心2号楼2501室',\n    status: 'failed',\n    priority: 'urgent',\n    createdAt: '2024-01-15 15:00:00',\n    updatedAt: '2024-01-15 16:00:00',\n  },\n  // 已取消订单\n  {\n    id: 7,\n    orderNo: 'ORD202401150007',\n    customerName: '周九',\n    customerPhone: '13800138007',\n    customerIdCard: '******************',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n    deliveryAddress: '长沙市岳麓区麓山南路932号中南大学科技园',\n    status: 'cancelled',\n    priority: 'low',\n    createdAt: '2024-01-15 16:20:00',\n    updatedAt: '2024-01-15 17:00:00',\n  },\n];\n\nconst OrderList: React.FC = () => {\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState<Order[]>(mockOrders);\n  const [filteredOrders, setFilteredOrders] = useState<Order[]>(mockOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const navigate = useNavigate();\n\n  // 状态颜色映射\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      pending_upload: 'purple',\n      shipped: 'cyan',\n      activated: 'green',\n      failed: 'red',\n      cancelled: 'default',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n\n\n  // 状态文本映射\n  const getStatusText = (status: string) => {\n    const texts = {\n      pending: '待处理',\n      processing: '开卡中',\n      pending_upload: '待上传三证',\n      shipped: '已发货',\n      activated: '已激活',\n      failed: '开卡失败',\n      cancelled: '已取消',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = (priority?: string) => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red',\n    };\n    return colors[priority as keyof typeof colors] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = (priority?: string) => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急',\n    };\n    return texts[priority as keyof typeof texts] || '普通';\n  };\n\n\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    pending_upload: filteredOrders.filter(order => order.status === 'pending_upload').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    failed: filteredOrders.filter(order => order.status === 'failed').length,\n    cancelled: filteredOrders.filter(order => order.status === 'cancelled').length,\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Order> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n    {\n      title: '订单状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '物流信息',\n      key: 'logistics',\n      width: 200,\n      render: (_, record) => {\n        if (record.status === 'shipped' || record.status === 'activated') {\n          return (\n            <div>\n              <div style={{ fontWeight: 600, fontSize: '12px' }}>\n                {record.logisticsCompany || '暂无'}\n              </div>\n              <div style={{ fontSize: '11px', color: '#666' }}>\n                {record.trackingNumber || '暂无快递单号'}\n              </div>\n              {record.shippedAt && (\n                <div style={{ fontSize: '11px', color: '#999' }}>\n                  发货: {record.shippedAt.split(' ')[0]}\n                </div>\n              )}\n            </div>\n          );\n        }\n        return <Text type=\"secondary\" style={{ fontSize: '12px' }}>未发货</Text>;\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditOrder(record.id)}\n          >\n            编辑\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleSearch = (values: any) => {\n    setLoading(true);\n    \n    setTimeout(() => {\n      let filtered = [...orders];\n      \n      if (values.orderNo) {\n        filtered = filtered.filter(order => \n          order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase())\n        );\n      }\n      \n      if (values.customerName) {\n        filtered = filtered.filter(order => \n          order.customerName.toLowerCase().includes(values.customerName.toLowerCase())\n        );\n      }\n      \n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      \n      if (values.status) {\n        filtered = filtered.filter(order => order.status === values.status);\n      }\n      \n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n\n\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockOrders);\n      setFilteredOrders(mockOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n\n  const handleViewOrder = (order: Order) => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n\n  const handleEditOrder = (id: number) => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n          订单列表\n        </Title>\n        <Text type=\"secondary\">\n          管理所有订单信息\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={stats.total}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"待处理\"\n              value={stats.pending}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"审核中\"\n              value={stats.reviewing}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"开卡中\"\n              value={stats.processing}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"已发货\"\n              value={stats.shipped}\n              valueStyle={{ color: '#13c2c2' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"已激活\"\n              value={stats.activated}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 搜索表单 */}\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ marginBottom: 16 }}\n        >\n          <Form.Item name=\"orderNo\" label=\"订单号\">\n            <Input placeholder=\"请输入订单号\" style={{ width: 150 }} />\n          </Form.Item>\n          <Form.Item name=\"customerName\" label=\"客户姓名\">\n            <Input placeholder=\"请输入客户姓名\" style={{ width: 120 }} />\n          </Form.Item>\n          <Form.Item name=\"operator\" label=\"运营商\">\n            <Select placeholder=\"请选择运营商\" style={{ width: 120 }}>\n              <Select.Option value=\"中国移动\">中国移动</Select.Option>\n              <Select.Option value=\"中国电信\">中国电信</Select.Option>\n              <Select.Option value=\"中国联通\">中国联通</Select.Option>\n              <Select.Option value=\"中国广电\">中国广电</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"status\" label=\"订单状态\">\n            <Select placeholder=\"请选择状态\" style={{ width: 120 }}>\n              <Select.Option value=\"pending\">待处理</Select.Option>\n              <Select.Option value=\"reviewing\">审核中</Select.Option>\n              <Select.Option value=\"processing\">开卡中</Select.Option>\n              <Select.Option value=\"shipped\">已发货</Select.Option>\n              <Select.Option value=\"activated\">已激活</Select.Option>\n              <Select.Option value=\"failed\">开卡失败</Select.Option>\n              <Select.Option value=\"cancelled\">已取消</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item>\n            <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n              搜索\n            </Button>\n          </Form.Item>\n        </Form>\n\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {filteredOrders.length} 条订单\n            </Text>\n          </div>\n          <Space>\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n            <Button onClick={handleExport} icon={<ExportOutlined />}>\n              导出\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={filteredOrders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 订单详情弹窗 */}\n      <Modal\n        title=\"订单详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={600}\n      >\n        {selectedOrder && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"订单号\" span={2}>\n              {selectedOrder.orderNo}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"客户姓名\">\n              {selectedOrder.customerName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"联系电话\">\n              {selectedOrder.customerPhone}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"产品名称\" span={2}>\n              {selectedOrder.productName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"运营商\">\n              <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"订单状态\">\n              <Tag color={getStatusColor(selectedOrder.status)}>\n                {getStatusText(selectedOrder.status)}\n              </Tag>\n            </Descriptions.Item>\n            {(selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && (\n              <>\n                <Descriptions.Item label=\"物流公司\">\n                  {selectedOrder.logisticsCompany || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"快递单号\">\n                  <Text code>{selectedOrder.trackingNumber || '暂无'}</Text>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"发货时间\">\n                  {selectedOrder.shippedAt || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"预计送达\">\n                  {selectedOrder.estimatedDelivery || '暂无'}\n                </Descriptions.Item>\n              </>\n            )}\n            <Descriptions.Item label=\"创建时间\">\n              {selectedOrder.createdAt}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"更新时间\">\n              {selectedOrder.updatedAt}\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,QACP,MAAM;AACb,SACEC,cAAc,EACdC,WAAW,EACXC,YAAY,EAEZC,cAAc,EACdC,cAAc,QAET,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGvB,UAAU;AAClC,MAAM;EAAEwB;AAAY,CAAC,GAAGnB,UAAU;AAqBlC;AACA,MAAMoB,UAAmB,GAAG;AAC1B;AACA;EACEC,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,eAAe,EAAE,2BAA2B;EAC5CC,MAAM,EAAE,WAAW;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,gBAAgB,EAAE,MAAM;EACxBC,cAAc,EAAE,iBAAiB;EACjCC,SAAS,EAAE,qBAAqB;EAChCC,iBAAiB,EAAE,qBAAqB;EACxCC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC;AACD;AACA;EACEf,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,eAAe,EAAE,4BAA4B;EAC7CC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,MAAM;EAChBK,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC;AACD;AACA;EACEf,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,eAAe,EAAE,gCAAgC;EACjDC,MAAM,EAAE,YAAY;EACpBC,QAAQ,EAAE,QAAQ;EAClBK,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC;AACD;AACA;EACEf,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,eAAe,EAAE,8BAA8B;EAC/CC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,gBAAgB,EAAE,MAAM;EACxBC,cAAc,EAAE,kBAAkB;EAClCC,SAAS,EAAE,qBAAqB;EAChCC,iBAAiB,EAAE,qBAAqB;EACxCC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC;AACD;AACA;EACEf,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,eAAe,EAAE,4BAA4B;EAC7CC,MAAM,EAAE,YAAY;EACpBC,QAAQ,EAAE,MAAM;EAChBK,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC;AACD;AACA;EACEf,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,eAAe,EAAE,gCAAgC;EACjDC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,QAAQ;EAClBK,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC;AACD;AACA;EACEf,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,eAAe,EAAE,uBAAuB;EACxCC,MAAM,EAAE,WAAW;EACnBC,QAAQ,EAAE,KAAK;EACfK,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,CACF;AAED,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAG1C,IAAI,CAAC2C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAU8B,UAAU,CAAC;EACzD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAU8B,UAAU,CAAC;EACzE,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC2D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAM+D,QAAQ,GAAGzC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM0C,cAAc,GAAIzB,MAAc,IAAK;IACzC,MAAM0B,MAAM,GAAG;MACbC,OAAO,EAAE,QAAQ;MACjBC,UAAU,EAAE,MAAM;MAClBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE;IACb,CAAC;IACD,OAAOP,MAAM,CAAC1B,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;;EAID;EACA,MAAMkC,aAAa,GAAIlC,MAAc,IAAK;IACxC,MAAMmC,KAAK,GAAG;MACZR,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,OAAO;MACvBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAACnC,MAAM,CAAuB,IAAIA,MAAM;EACtD,CAAC;;EAED;EACA,MAAMoC,gBAAgB,GAAInC,QAAiB,IAAK;IAC9C,MAAMyB,MAAM,GAAG;MACbW,GAAG,EAAE,SAAS;MACdC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAOd,MAAM,CAACzB,QAAQ,CAAwB,IAAI,SAAS;EAC7D,CAAC;;EAED;EACA,MAAMwC,eAAe,GAAIxC,QAAiB,IAAK;IAC7C,MAAMkC,KAAK,GAAG;MACZE,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE;IACV,CAAC;IACD,OAAOL,KAAK,CAAClC,QAAQ,CAAuB,IAAI,IAAI;EACtD,CAAC;;EAID;EACA,MAAMyC,KAAK,GAAG;IACZC,KAAK,EAAE7B,cAAc,CAAC8B,MAAM;IAC5BjB,OAAO,EAAEb,cAAc,CAAC+B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC9C,MAAM,KAAK,SAAS,CAAC,CAAC4C,MAAM;IAC1EhB,UAAU,EAAEd,cAAc,CAAC+B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC9C,MAAM,KAAK,YAAY,CAAC,CAAC4C,MAAM;IAChFf,cAAc,EAAEf,cAAc,CAAC+B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC9C,MAAM,KAAK,gBAAgB,CAAC,CAAC4C,MAAM;IACxFd,OAAO,EAAEhB,cAAc,CAAC+B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC9C,MAAM,KAAK,SAAS,CAAC,CAAC4C,MAAM;IAC1Eb,SAAS,EAAEjB,cAAc,CAAC+B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC9C,MAAM,KAAK,WAAW,CAAC,CAAC4C,MAAM;IAC9EZ,MAAM,EAAElB,cAAc,CAAC+B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC9C,MAAM,KAAK,QAAQ,CAAC,CAAC4C,MAAM;IACxEX,SAAS,EAAEnB,cAAc,CAAC+B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC9C,MAAM,KAAK,WAAW,CAAC,CAAC4C;EAC1E,CAAC;;EAED;EACA,MAAMG,OAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBpE,OAAA,CAACI,IAAI;MAACiE,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EACpCJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChB9E,OAAA;MAAAwE,QAAA,gBACExE,OAAA;QAAKsE,KAAK,EAAE;UAAES,UAAU,EAAE;QAAI,CAAE;QAAAP,QAAA,EAAEM,MAAM,CAACrE;MAAY;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5D5E,OAAA;QAAKsE,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAES,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,EAC7CM,MAAM,CAACpE;MAAa;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBgB,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBpE,OAAA,CAAClB,GAAG;MAACkG,KAAK,EAAC,MAAM;MAAAR,QAAA,EAAEJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGpD,MAAc,iBACrBf,OAAA,CAAClB,GAAG;MAACkG,KAAK,EAAExC,cAAc,CAACzB,MAAM,CAAE;MAAAyD,QAAA,EAChCvB,aAAa,CAAClC,MAAM;IAAC;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,KAAK;MACrB,IAAIA,MAAM,CAAC/D,MAAM,KAAK,SAAS,IAAI+D,MAAM,CAAC/D,MAAM,KAAK,WAAW,EAAE;QAChE,oBACEf,OAAA;UAAAwE,QAAA,gBACExE,OAAA;YAAKsE,KAAK,EAAE;cAAES,UAAU,EAAE,GAAG;cAAER,QAAQ,EAAE;YAAO,CAAE;YAAAC,QAAA,EAC/CM,MAAM,CAAC7D,gBAAgB,IAAI;UAAI;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACN5E,OAAA;YAAKsE,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,EAC7CM,MAAM,CAAC5D,cAAc,IAAI;UAAQ;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACLE,MAAM,CAAC3D,SAAS,iBACfnB,OAAA;YAAKsE,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAC,gBAC3C,EAACM,MAAM,CAAC3D,SAAS,CAAC+D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEV;MACA,oBAAO5E,OAAA,CAACI,IAAI;QAAC+E,IAAI,EAAC,WAAW;QAACb,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAC,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACvE;EACF,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBpE,OAAA;MAAKsE,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EAC9BJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChB9E,OAAA,CAACpB,KAAK;MAACwG,IAAI,EAAC,OAAO;MAAAZ,QAAA,gBACjBxE,OAAA,CAACrB,MAAM;QACLwG,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAErF,OAAA,CAACN,WAAW;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBU,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACT,MAAM,CAAE;QAAAN,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT5E,OAAA,CAACrB,MAAM;QACLwG,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAErF,OAAA,CAACL,YAAY;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBU,OAAO,EAAEA,CAAA,KAAME,eAAe,CAACV,MAAM,CAACvE,EAAE,CAAE;QAAAiE,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMa,YAAY,GAAIC,MAAW,IAAK;IACpC1D,UAAU,CAAC,IAAI,CAAC;IAEhB2D,UAAU,CAAC,MAAM;MACf,IAAIC,QAAQ,GAAG,CAAC,GAAGjE,MAAM,CAAC;MAE1B,IAAI+D,MAAM,CAAClF,OAAO,EAAE;QAClBoF,QAAQ,GAAGA,QAAQ,CAAChC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAACrD,OAAO,CAACqF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAAClF,OAAO,CAACqF,WAAW,CAAC,CAAC,CACnE,CAAC;MACH;MAEA,IAAIH,MAAM,CAACjF,YAAY,EAAE;QACvBmF,QAAQ,GAAGA,QAAQ,CAAChC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAACpD,YAAY,CAACoF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAACjF,YAAY,CAACoF,WAAW,CAAC,CAAC,CAC7E,CAAC;MACH;MAEA,IAAIH,MAAM,CAAC7E,QAAQ,EAAE;QACnB+E,QAAQ,GAAGA,QAAQ,CAAChC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChD,QAAQ,KAAK6E,MAAM,CAAC7E,QAAQ,CAAC;MACzE;MAEA,IAAI6E,MAAM,CAAC3E,MAAM,EAAE;QACjB6E,QAAQ,GAAGA,QAAQ,CAAChC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC9C,MAAM,KAAK2E,MAAM,CAAC3E,MAAM,CAAC;MACrE;MAEAe,iBAAiB,CAAC8D,QAAQ,CAAC;MAC3B5D,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAID,MAAM+D,aAAa,GAAGA,CAAA,KAAM;IAC1B/D,UAAU,CAAC,IAAI,CAAC;IAChB2D,UAAU,CAAC,MAAM;MACf/D,SAAS,CAACtB,UAAU,CAAC;MACrBwB,iBAAiB,CAACxB,UAAU,CAAC;MAC7B0B,UAAU,CAAC,KAAK,CAAC;MACjB1C,OAAO,CAAC0G,OAAO,CAAC,OAAO,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMT,eAAe,GAAI1B,KAAY,IAAK;IACxCvB,gBAAgB,CAACuB,KAAK,CAAC;IACvBzB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMoD,eAAe,GAAIjF,EAAU,IAAK;IACtCjB,OAAO,CAAC2G,IAAI,CAAC,QAAQ1F,EAAE,WAAW,CAAC;EACrC,CAAC;EAED,MAAM2F,YAAY,GAAGA,CAAA,KAAM;IACzB5G,OAAO,CAAC2G,IAAI,CAAC,YAAY,CAAC;EAC5B,CAAC;EAED,oBACEjG,OAAA;IAAAwE,QAAA,gBACExE,OAAA;MAAKsE,KAAK,EAAE;QAAE6B,YAAY,EAAE;MAAO,CAAE;MAAA3B,QAAA,gBACnCxE,OAAA,CAACG,KAAK;QAACiG,KAAK,EAAE,CAAE;QAAC9B,KAAK,EAAE;UAAE+B,MAAM,EAAE,CAAC;UAAErB,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR5E,OAAA,CAACI,IAAI;QAAC+E,IAAI,EAAC,WAAW;QAAAX,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN5E,OAAA,CAACb,GAAG;MAACmH,MAAM,EAAE,EAAG;MAAChC,KAAK,EAAE;QAAE6B,YAAY,EAAE;MAAO,CAAE;MAAA3B,QAAA,gBAC/CxE,OAAA,CAACZ,GAAG;QAACmH,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACXxE,OAAA,CAACvB,IAAI;UAAA+F,QAAA,eACHxE,OAAA,CAACX,SAAS;YACR0E,KAAK,EAAC,0BAAM;YACZyC,KAAK,EAAE/C,KAAK,CAACC,KAAM;YACnB+C,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5E,OAAA,CAACZ,GAAG;QAACmH,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACXxE,OAAA,CAACvB,IAAI;UAAA+F,QAAA,eACHxE,OAAA,CAACX,SAAS;YACR0E,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAE/C,KAAK,CAACf,OAAQ;YACrB+D,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5E,OAAA,CAACZ,GAAG;QAACmH,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACXxE,OAAA,CAACvB,IAAI;UAAA+F,QAAA,eACHxE,OAAA,CAACX,SAAS;YACR0E,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAE/C,KAAK,CAACiD,SAAU;YACvBD,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5E,OAAA,CAACZ,GAAG;QAACmH,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACXxE,OAAA,CAACvB,IAAI;UAAA+F,QAAA,eACHxE,OAAA,CAACX,SAAS;YACR0E,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAE/C,KAAK,CAACd,UAAW;YACxB8D,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5E,OAAA,CAACZ,GAAG;QAACmH,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACXxE,OAAA,CAACvB,IAAI;UAAA+F,QAAA,eACHxE,OAAA,CAACX,SAAS;YACR0E,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAE/C,KAAK,CAACZ,OAAQ;YACrB4D,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5E,OAAA,CAACZ,GAAG;QAACmH,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACXxE,OAAA,CAACvB,IAAI;UAAA+F,QAAA,eACHxE,OAAA,CAACX,SAAS;YACR0E,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAE/C,KAAK,CAACX,SAAU;YACvB2D,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5E,OAAA,CAACvB,IAAI;MAAA+F,QAAA,gBAEHxE,OAAA,CAACjB,IAAI;QACH0C,IAAI,EAAEA,IAAK;QACXkF,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEnB,YAAa;QACvBnB,KAAK,EAAE;UAAE6B,YAAY,EAAE;QAAG,CAAE;QAAA3B,QAAA,gBAE5BxE,OAAA,CAACjB,IAAI,CAAC8H,IAAI;UAACC,IAAI,EAAC,SAAS;UAACC,KAAK,EAAC,oBAAK;UAAAvC,QAAA,eACnCxE,OAAA,CAAChB,KAAK;YAACgI,WAAW,EAAC,sCAAQ;YAAC1C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACZ5E,OAAA,CAACjB,IAAI,CAAC8H,IAAI;UAACC,IAAI,EAAC,cAAc;UAACC,KAAK,EAAC,0BAAM;UAAAvC,QAAA,eACzCxE,OAAA,CAAChB,KAAK;YAACgI,WAAW,EAAC,4CAAS;YAAC1C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACZ5E,OAAA,CAACjB,IAAI,CAAC8H,IAAI;UAACC,IAAI,EAAC,UAAU;UAACC,KAAK,EAAC,oBAAK;UAAAvC,QAAA,eACpCxE,OAAA,CAACf,MAAM;YAAC+H,WAAW,EAAC,sCAAQ;YAAC1C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACjDxE,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,0BAAM;cAAAhC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,0BAAM;cAAAhC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,0BAAM;cAAAhC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,0BAAM;cAAAhC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ5E,OAAA,CAACjB,IAAI,CAAC8H,IAAI;UAACC,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC,0BAAM;UAAAvC,QAAA,eACnCxE,OAAA,CAACf,MAAM;YAAC+H,WAAW,EAAC,gCAAO;YAAC1C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBAChDxE,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,SAAS;cAAAhC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,WAAW;cAAAhC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACpD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,YAAY;cAAAhC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACrD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,SAAS;cAAAhC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,WAAW;cAAAhC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACpD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,QAAQ;cAAAhC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD5E,OAAA,CAACf,MAAM,CAACgI,MAAM;cAACT,KAAK,EAAC,WAAW;cAAAhC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ5E,OAAA,CAACjB,IAAI,CAAC8H,IAAI;UAAArC,QAAA,eACRxE,OAAA,CAACrB,MAAM;YAACwG,IAAI,EAAC,SAAS;YAAC+B,QAAQ,EAAC,QAAQ;YAAC7B,IAAI,eAAErF,OAAA,CAACP,cAAc;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEnE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGP5E,OAAA;QAAKsE,KAAK,EAAE;UACV6C,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBlB,YAAY,EAAE;QAChB,CAAE;QAAA3B,QAAA,gBACAxE,OAAA;UAAAwE,QAAA,eACExE,OAAA,CAACI,IAAI;YAACkH,MAAM;YAAA9C,QAAA,GAAC,SACT,EAAC3C,cAAc,CAAC8B,MAAM,EAAC,qBAC3B;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5E,OAAA,CAACpB,KAAK;UAAA4F,QAAA,gBACJxE,OAAA,CAACrB,MAAM;YAAC2G,OAAO,EAAES,aAAc;YAACV,IAAI,eAAErF,OAAA,CAACH,cAAc;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5E,OAAA,CAACrB,MAAM;YAAC2G,OAAO,EAAEY,YAAa;YAACb,IAAI,eAAErF,OAAA,CAACJ,cAAc;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN5E,OAAA,CAACtB,KAAK;QACJoF,OAAO,EAAEA,OAAQ;QACjByD,UAAU,EAAE1F,cAAe;QAC3B2F,MAAM,EAAC,IAAI;QACXzF,OAAO,EAAEA,OAAQ;QACjB0F,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACpE,KAAK,EAAEqE,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAASrE,KAAK;QAC3C,CAAE;QACFsE,YAAY,EAAE;UACZ/F,eAAe;UACfgG,QAAQ,EAAE/F;QACZ;MAAE;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP5E,OAAA,CAACT,KAAK;MACJwE,KAAK,EAAC,0BAAM;MACZmE,IAAI,EAAE/F,kBAAmB;MACzBgG,QAAQ,EAAEA,CAAA,KAAM/F,qBAAqB,CAAC,KAAK,CAAE;MAC7CgG,MAAM,EAAE,cACNpI,OAAA,CAACrB,MAAM;QAAa2G,OAAO,EAAEA,CAAA,KAAMlD,qBAAqB,CAAC,KAAK,CAAE;QAAAoC,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFV,KAAK,EAAE,GAAI;MAAAM,QAAA,EAEVnC,aAAa,iBACZrC,OAAA,CAACR,YAAY;QAAC6I,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAA9D,QAAA,gBAC/BxE,OAAA,CAACR,YAAY,CAACqH,IAAI;UAACE,KAAK,EAAC,oBAAK;UAACR,IAAI,EAAE,CAAE;UAAA/B,QAAA,EACpCnC,aAAa,CAAC7B;QAAO;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAvC,QAAA,EAC5BnC,aAAa,CAAC5B;QAAY;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAvC,QAAA,EAC5BnC,aAAa,CAAC3B;QAAa;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAACR,IAAI,EAAE,CAAE;UAAA/B,QAAA,EACrCnC,aAAa,CAACzB;QAAW;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;UAACE,KAAK,EAAC,oBAAK;UAAAvC,QAAA,eAC5BxE,OAAA,CAAClB,GAAG;YAACkG,KAAK,EAAC,MAAM;YAAAR,QAAA,EAAEnC,aAAa,CAACxB;UAAQ;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAvC,QAAA,eAC7BxE,OAAA,CAAClB,GAAG;YAACkG,KAAK,EAAExC,cAAc,CAACH,aAAa,CAACtB,MAAM,CAAE;YAAAyD,QAAA,EAC9CvB,aAAa,CAACZ,aAAa,CAACtB,MAAM;UAAC;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,EACnB,CAACvC,aAAa,CAACtB,MAAM,KAAK,SAAS,IAAIsB,aAAa,CAACtB,MAAM,KAAK,WAAW,kBAC1Ef,OAAA,CAAAE,SAAA;UAAAsE,QAAA,gBACExE,OAAA,CAACR,YAAY,CAACqH,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAvC,QAAA,EAC5BnC,aAAa,CAACpB,gBAAgB,IAAI;UAAI;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAvC,QAAA,eAC7BxE,OAAA,CAACI,IAAI;cAACiE,IAAI;cAAAG,QAAA,EAAEnC,aAAa,CAACnB,cAAc,IAAI;YAAI;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAvC,QAAA,EAC5BnC,aAAa,CAAClB,SAAS,IAAI;UAAI;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAvC,QAAA,EAC5BnC,aAAa,CAACjB,iBAAiB,IAAI;UAAI;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA,eACpB,CACH,eACD5E,OAAA,CAACR,YAAY,CAACqH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAvC,QAAA,EAC5BnC,aAAa,CAAChB;QAAS;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACpB5E,OAAA,CAACR,YAAY,CAACqH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAvC,QAAA,EAC5BnC,aAAa,CAACf;QAAS;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpD,EAAA,CA5cID,SAAmB;EAAA,QACRxC,IAAI,CAAC2C,OAAO,EAOV5B,WAAW;AAAA;AAAAyI,EAAA,GARxBhH,SAAmB;AA8czB,eAAeA,SAAS;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
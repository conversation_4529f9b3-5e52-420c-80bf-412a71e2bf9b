{"ast": null, "code": "import { genStyleHooks } from '../../theme/internal';\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    colorText,\n    fontSize,\n    lineHeight,\n    fontFamily\n  } = token;\n  return {\n    [componentCls]: {\n      color: colorText,\n      fontSize,\n      lineHeight,\n      fontFamily,\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      }\n    }\n  };\n};\nexport const prepareComponentToken = () => ({});\n// ============================== Export ==============================\nexport default genStyleHooks('App', genBaseStyle, prepareComponentToken);", "map": {"version": 3, "names": ["genStyleHooks", "genBaseStyle", "token", "componentCls", "colorText", "fontSize", "lineHeight", "fontFamily", "color", "direction", "prepareComponentToken"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/app/style/index.js"], "sourcesContent": ["import { genStyleHooks } from '../../theme/internal';\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    colorText,\n    fontSize,\n    lineHeight,\n    fontFamily\n  } = token;\n  return {\n    [componentCls]: {\n      color: colorText,\n      fontSize,\n      lineHeight,\n      fontFamily,\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      }\n    }\n  };\n};\nexport const prepareComponentToken = () => ({});\n// ============================== Export ==============================\nexport default genStyleHooks('App', genBaseStyle, prepareComponentToken);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,sBAAsB;AACpD;AACA,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACdK,KAAK,EAAEJ,SAAS;MAChBC,QAAQ;MACRC,UAAU;MACVC,UAAU;MACV,CAAC,IAAIJ,YAAY,MAAM,GAAG;QACxBM,SAAS,EAAE;MACb;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,MAAO,CAAC,CAAC,CAAC;AAC/C;AACA,eAAeV,aAAa,CAAC,KAAK,EAAEC,YAAY,EAAES,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
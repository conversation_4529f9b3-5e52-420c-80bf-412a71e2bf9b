{"ast": null, "code": "const genFixedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    colorSplit,\n    motionDurationSlow,\n    zIndexTableFixed,\n    tableBg,\n    zIndexTableSticky,\n    calc\n  } = token;\n  const shadowColor = colorSplit;\n  // Follow style is magic of shadow which should not follow token:\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\\n        \".concat(componentCls, \"-cell-fix-left,\\n        \").concat(componentCls, \"-cell-fix-right\\n      \")]: {\n        position: 'sticky !important',\n        zIndex: zIndexTableFixed,\n        background: tableBg\n      },\n      [\"\\n        \".concat(componentCls, \"-cell-fix-left-first::after,\\n        \").concat(componentCls, \"-cell-fix-left-last::after\\n      \")]: {\n        position: 'absolute',\n        top: 0,\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: calc(lineWidth).mul(-1).equal(),\n        width: 30,\n        transform: 'translateX(100%)',\n        transition: \"box-shadow \".concat(motionDurationSlow),\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [\"\".concat(componentCls, \"-cell-fix-left-all::after\")]: {\n        display: 'none'\n      },\n      [\"\\n        \".concat(componentCls, \"-cell-fix-right-first::after,\\n        \").concat(componentCls, \"-cell-fix-right-last::after\\n      \")]: {\n        position: 'absolute',\n        top: 0,\n        bottom: calc(lineWidth).mul(-1).equal(),\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        width: 30,\n        transform: 'translateX(-100%)',\n        transition: \"box-shadow \".concat(motionDurationSlow),\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [\"\".concat(componentCls, \"-container\")]: {\n        position: 'relative',\n        '&::before, &::after': {\n          position: 'absolute',\n          top: 0,\n          bottom: 0,\n          zIndex: calc(zIndexTableSticky).add(1).equal({\n            unit: false\n          }),\n          width: 30,\n          transition: \"box-shadow \".concat(motionDurationSlow),\n          content: '\"\"',\n          pointerEvents: 'none'\n        },\n        '&::before': {\n          insetInlineStart: 0\n        },\n        '&::after': {\n          insetInlineEnd: 0\n        }\n      },\n      [\"\".concat(componentCls, \"-ping-left\")]: {\n        [\"&:not(\".concat(componentCls, \"-has-fix-left) \").concat(componentCls, \"-container::before\")]: {\n          boxShadow: \"inset 10px 0 8px -8px \".concat(shadowColor)\n        },\n        [\"\\n          \".concat(componentCls, \"-cell-fix-left-first::after,\\n          \").concat(componentCls, \"-cell-fix-left-last::after\\n        \")]: {\n          boxShadow: \"inset 10px 0 8px -8px \".concat(shadowColor)\n        },\n        [\"\".concat(componentCls, \"-cell-fix-left-last::before\")]: {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [\"\".concat(componentCls, \"-ping-right\")]: {\n        [\"&:not(\".concat(componentCls, \"-has-fix-right) \").concat(componentCls, \"-container::after\")]: {\n          boxShadow: \"inset -10px 0 8px -8px \".concat(shadowColor)\n        },\n        [\"\\n          \".concat(componentCls, \"-cell-fix-right-first::after,\\n          \").concat(componentCls, \"-cell-fix-right-last::after\\n        \")]: {\n          boxShadow: \"inset -10px 0 8px -8px \".concat(shadowColor)\n        }\n      },\n      // Gapped fixed Columns do not show the shadow\n      [\"\".concat(componentCls, \"-fixed-column-gapped\")]: {\n        [\"\\n        \".concat(componentCls, \"-cell-fix-left-first::after,\\n        \").concat(componentCls, \"-cell-fix-left-last::after,\\n        \").concat(componentCls, \"-cell-fix-right-first::after,\\n        \").concat(componentCls, \"-cell-fix-right-last::after\\n      \")]: {\n          boxShadow: 'none'\n        }\n      }\n    }\n  };\n};\nexport default genFixedStyle;", "map": {"version": 3, "names": ["genFixedStyle", "token", "componentCls", "lineWidth", "colorSplit", "motionDurationSlow", "zIndexTableFixed", "tableBg", "zIndexTableSticky", "calc", "shadowColor", "concat", "position", "zIndex", "background", "top", "right", "_skip_check_", "value", "bottom", "mul", "equal", "width", "transform", "transition", "content", "pointerEvents", "display", "left", "add", "unit", "insetInlineStart", "insetInlineEnd", "boxShadow", "backgroundColor"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/table/style/fixed.js"], "sourcesContent": ["const genFixedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    colorSplit,\n    motionDurationSlow,\n    zIndexTableFixed,\n    tableBg,\n    zIndexTableSticky,\n    calc\n  } = token;\n  const shadowColor = colorSplit;\n  // Follow style is magic of shadow which should not follow token:\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`\n        ${componentCls}-cell-fix-left,\n        ${componentCls}-cell-fix-right\n      `]: {\n        position: 'sticky !important',\n        zIndex: zIndexTableFixed,\n        background: tableBg\n      },\n      [`\n        ${componentCls}-cell-fix-left-first::after,\n        ${componentCls}-cell-fix-left-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: calc(lineWidth).mul(-1).equal(),\n        width: 30,\n        transform: 'translateX(100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-cell-fix-left-all::after`]: {\n        display: 'none'\n      },\n      [`\n        ${componentCls}-cell-fix-right-first::after,\n        ${componentCls}-cell-fix-right-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        bottom: calc(lineWidth).mul(-1).equal(),\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        width: 30,\n        transform: 'translateX(-100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-container`]: {\n        position: 'relative',\n        '&::before, &::after': {\n          position: 'absolute',\n          top: 0,\n          bottom: 0,\n          zIndex: calc(zIndexTableSticky).add(1).equal({\n            unit: false\n          }),\n          width: 30,\n          transition: `box-shadow ${motionDurationSlow}`,\n          content: '\"\"',\n          pointerEvents: 'none'\n        },\n        '&::before': {\n          insetInlineStart: 0\n        },\n        '&::after': {\n          insetInlineEnd: 0\n        }\n      },\n      [`${componentCls}-ping-left`]: {\n        [`&:not(${componentCls}-has-fix-left) ${componentCls}-container::before`]: {\n          boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n        },\n        [`\n          ${componentCls}-cell-fix-left-first::after,\n          ${componentCls}-cell-fix-left-last::after\n        `]: {\n          boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n        },\n        [`${componentCls}-cell-fix-left-last::before`]: {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`${componentCls}-ping-right`]: {\n        [`&:not(${componentCls}-has-fix-right) ${componentCls}-container::after`]: {\n          boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n        },\n        [`\n          ${componentCls}-cell-fix-right-first::after,\n          ${componentCls}-cell-fix-right-last::after\n        `]: {\n          boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n        }\n      },\n      // Gapped fixed Columns do not show the shadow\n      [`${componentCls}-fixed-column-gapped`]: {\n        [`\n        ${componentCls}-cell-fix-left-first::after,\n        ${componentCls}-cell-fix-left-last::after,\n        ${componentCls}-cell-fix-right-first::after,\n        ${componentCls}-cell-fix-right-last::after\n      `]: {\n          boxShadow: 'none'\n        }\n      }\n    }\n  };\n};\nexport default genFixedStyle;"], "mappings": "AAAA,MAAMA,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,UAAU;IACVC,kBAAkB;IAClBC,gBAAgB;IAChBC,OAAO;IACPC,iBAAiB;IACjBC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,WAAW,GAAGN,UAAU;EAC9B;EACA,OAAO;IACL,IAAAO,MAAA,CAAIT,YAAY,gBAAa;MAC3B,cAAAS,MAAA,CACIT,YAAY,+BAAAS,MAAA,CACZT,YAAY,+BACZ;QACFU,QAAQ,EAAE,mBAAmB;QAC7BC,MAAM,EAAEP,gBAAgB;QACxBQ,UAAU,EAAEP;MACd,CAAC;MACD,cAAAI,MAAA,CACIT,YAAY,4CAAAS,MAAA,CACZT,YAAY,0CACZ;QACFU,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,KAAK,EAAE;UACLC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDC,MAAM,EAAEV,IAAI,CAACN,SAAS,CAAC,CAACiB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACvCC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,UAAU,gBAAAb,MAAA,CAAgBN,kBAAkB,CAAE;QAC9CoB,OAAO,EAAE,IAAI;QACbC,aAAa,EAAE;MACjB,CAAC;MACD,IAAAf,MAAA,CAAIT,YAAY,iCAA8B;QAC5CyB,OAAO,EAAE;MACX,CAAC;MACD,cAAAhB,MAAA,CACIT,YAAY,6CAAAS,MAAA,CACZT,YAAY,2CACZ;QACFU,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNI,MAAM,EAAEV,IAAI,CAACN,SAAS,CAAC,CAACiB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACvCO,IAAI,EAAE;UACJX,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDI,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,mBAAmB;QAC9BC,UAAU,gBAAAb,MAAA,CAAgBN,kBAAkB,CAAE;QAC9CoB,OAAO,EAAE,IAAI;QACbC,aAAa,EAAE;MACjB,CAAC;MACD,IAAAf,MAAA,CAAIT,YAAY,kBAAe;QAC7BU,QAAQ,EAAE,UAAU;QACpB,qBAAqB,EAAE;UACrBA,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNI,MAAM,EAAE,CAAC;UACTN,MAAM,EAAEJ,IAAI,CAACD,iBAAiB,CAAC,CAACqB,GAAG,CAAC,CAAC,CAAC,CAACR,KAAK,CAAC;YAC3CS,IAAI,EAAE;UACR,CAAC,CAAC;UACFR,KAAK,EAAE,EAAE;UACTE,UAAU,gBAAAb,MAAA,CAAgBN,kBAAkB,CAAE;UAC9CoB,OAAO,EAAE,IAAI;UACbC,aAAa,EAAE;QACjB,CAAC;QACD,WAAW,EAAE;UACXK,gBAAgB,EAAE;QACpB,CAAC;QACD,UAAU,EAAE;UACVC,cAAc,EAAE;QAClB;MACF,CAAC;MACD,IAAArB,MAAA,CAAIT,YAAY,kBAAe;QAC7B,UAAAS,MAAA,CAAUT,YAAY,qBAAAS,MAAA,CAAkBT,YAAY,0BAAuB;UACzE+B,SAAS,2BAAAtB,MAAA,CAA2BD,WAAW;QACjD,CAAC;QACD,gBAAAC,MAAA,CACIT,YAAY,8CAAAS,MAAA,CACZT,YAAY,4CACZ;UACF+B,SAAS,2BAAAtB,MAAA,CAA2BD,WAAW;QACjD,CAAC;QACD,IAAAC,MAAA,CAAIT,YAAY,mCAAgC;UAC9CgC,eAAe,EAAE;QACnB;MACF,CAAC;MACD,IAAAvB,MAAA,CAAIT,YAAY,mBAAgB;QAC9B,UAAAS,MAAA,CAAUT,YAAY,sBAAAS,MAAA,CAAmBT,YAAY,yBAAsB;UACzE+B,SAAS,4BAAAtB,MAAA,CAA4BD,WAAW;QAClD,CAAC;QACD,gBAAAC,MAAA,CACIT,YAAY,+CAAAS,MAAA,CACZT,YAAY,6CACZ;UACF+B,SAAS,4BAAAtB,MAAA,CAA4BD,WAAW;QAClD;MACF,CAAC;MACD;MACA,IAAAC,MAAA,CAAIT,YAAY,4BAAyB;QACvC,cAAAS,MAAA,CACET,YAAY,4CAAAS,MAAA,CACZT,YAAY,2CAAAS,MAAA,CACZT,YAAY,6CAAAS,MAAA,CACZT,YAAY,2CACZ;UACA+B,SAAS,EAAE;QACb;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAejC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
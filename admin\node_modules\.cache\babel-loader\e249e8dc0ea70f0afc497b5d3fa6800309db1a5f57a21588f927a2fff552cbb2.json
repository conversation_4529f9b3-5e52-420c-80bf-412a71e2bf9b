{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SlidersOutlinedSvg from \"@ant-design/icons-svg/es/asn/SlidersOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SlidersOutlined = function SlidersOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SlidersOutlinedSvg\n  }));\n};\n\n/**![sliders](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMyMCAyMjRoLTY2di01NmMwLTQuNC0zLjYtOC04LThoLTUyYy00LjQgMC04IDMuNi04IDh2NTZoLTY2Yy00LjQgMC04IDMuNi04IDh2NTYwYzAgNC40IDMuNiA4IDggOGg2NnY1NmMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04di01Nmg2NmM0LjQgMCA4LTMuNiA4LThWMjMyYzAtNC40LTMuNi04LTgtOHptLTYwIDUwOGgtODBWMjkyaDgwdjQ0MHptNjQ0LTQzNmgtNjZ2LTk2YzAtNC40LTMuNi04LTgtOGgtNTJjLTQuNCAwLTggMy42LTggOHY5NmgtNjZjLTQuNCAwLTggMy42LTggOHY0MTZjMCA0LjQgMy42IDggOCA4aDY2djk2YzAgNC40IDMuNiA4IDggOGg1MmM0LjQgMCA4LTMuNiA4LTh2LTk2aDY2YzQuNCAwIDgtMy42IDgtOFYzMDRjMC00LjQtMy42LTgtOC04em0tNjAgMzY0aC04MFYzNjRoODB2Mjk2ek02MTIgNDA0aC02NlYyMzJjMC00LjQtMy42LTgtOC04aC01MmMtNC40IDAtOCAzLjYtOCA4djE3MmgtNjZjLTQuNCAwLTggMy42LTggOHYyMDBjMCA0LjQgMy42IDggOCA4aDY2djE3MmMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04VjYyMGg2NmM0LjQgMCA4LTMuNiA4LThWNDEyYzAtNC40LTMuNi04LTgtOHptLTYwIDE0NWEzIDMgMCAwMS0zIDNoLTc0YTMgMyAwIDAxLTMtM3YtNzRhMyAzIDAgMDEzLTNoNzRhMyAzIDAgMDEzIDN2NzR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SlidersOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SlidersOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SlidersOutlinedSvg", "AntdIcon", "SlidersOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/SlidersOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SlidersOutlinedSvg from \"@ant-design/icons-svg/es/asn/SlidersOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SlidersOutlined = function SlidersOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SlidersOutlinedSvg\n  }));\n};\n\n/**![sliders](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMyMCAyMjRoLTY2di01NmMwLTQuNC0zLjYtOC04LThoLTUyYy00LjQgMC04IDMuNi04IDh2NTZoLTY2Yy00LjQgMC04IDMuNi04IDh2NTYwYzAgNC40IDMuNiA4IDggOGg2NnY1NmMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04di01Nmg2NmM0LjQgMCA4LTMuNiA4LThWMjMyYzAtNC40LTMuNi04LTgtOHptLTYwIDUwOGgtODBWMjkyaDgwdjQ0MHptNjQ0LTQzNmgtNjZ2LTk2YzAtNC40LTMuNi04LTgtOGgtNTJjLTQuNCAwLTggMy42LTggOHY5NmgtNjZjLTQuNCAwLTggMy42LTggOHY0MTZjMCA0LjQgMy42IDggOCA4aDY2djk2YzAgNC40IDMuNiA4IDggOGg1MmM0LjQgMCA4LTMuNiA4LTh2LTk2aDY2YzQuNCAwIDgtMy42IDgtOFYzMDRjMC00LjQtMy42LTgtOC04em0tNjAgMzY0aC04MFYzNjRoODB2Mjk2ek02MTIgNDA0aC02NlYyMzJjMC00LjQtMy42LTgtOC04aC01MmMtNC40IDAtOCAzLjYtOCA4djE3MmgtNjZjLTQuNCAwLTggMy42LTggOHYyMDBjMCA0LjQgMy42IDggOCA4aDY2djE3MmMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04VjYyMGg2NmM0LjQgMCA4LTMuNiA4LThWNDEyYzAtNC40LTMuNi04LTgtOHptLTYwIDE0NWEzIDMgMCAwMS0zIDNoLTc0YTMgMyAwIDAxLTMtM3YtNzRhMyAzIDAgMDEzLTNoNzRhMyAzIDAgMDEzIDN2NzR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SlidersOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SlidersOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "// 常量定义\n\n// 产品状态\nexport const PRODUCT_STATUS = {\n  ONLINE: 'online',\n  OFFLINE: 'offline'\n};\n\n// 产品状态选项\nexport const PRODUCT_STATUS_OPTIONS = [{\n  label: '上架中',\n  value: PRODUCT_STATUS.ONLINE,\n  color: 'success'\n}, {\n  label: '已下架',\n  value: PRODUCT_STATUS.OFFLINE,\n  color: 'default'\n}];\n\n// 运营商选项\nexport const OPERATOR_OPTIONS = [{\n  label: '中国移动',\n  value: '中国移动'\n}, {\n  label: '中国联通',\n  value: '中国联通'\n}, {\n  label: '中国电信',\n  value: '中国电信'\n}];\n\n// 归属地选项\nexport const LOCATION_OPTIONS = [{\n  label: '全国',\n  value: '全国'\n}, {\n  label: '北京',\n  value: '北京'\n}, {\n  label: '上海',\n  value: '上海'\n}, {\n  label: '广州',\n  value: '广州'\n}, {\n  label: '深圳',\n  value: '深圳'\n}, {\n  label: '随机',\n  value: '随机'\n}];\n\n// 物流方式选项\nexport const LOGISTICS_OPTIONS = [{\n  label: '京东快递',\n  value: '京东快递'\n}, {\n  label: '顺丰快递',\n  value: '顺丰快递'\n}, {\n  label: '申通快递',\n  value: '申通快递'\n}, {\n  label: '中通快递',\n  value: '中通快递'\n}, {\n  label: '圆通快递',\n  value: '圆通快递'\n}, {\n  label: '韵达快递',\n  value: '韵达快递'\n}];\n\n// 套餐时长选项\nexport const PACKAGE_DURATION_OPTIONS = [{\n  label: '月套餐',\n  value: '月套餐'\n}, {\n  label: '季度套餐',\n  value: '季度套餐'\n}, {\n  label: '半年套餐',\n  value: '半年套餐'\n}, {\n  label: '年套餐',\n  value: '年套餐'\n}];\n\n// 数据规则选项\nexport const DATA_RULE_OPTIONS = [{\n  label: '规则1',\n  value: '1'\n}, {\n  label: '规则2',\n  value: '2'\n}, {\n  label: '规则3',\n  value: '3'\n}];\n\n// 分页配置\nexport const PAGINATION_CONFIG = {\n  defaultPageSize: 15,\n  pageSizeOptions: ['10', '15', '20', '50', '100'],\n  showSizeChanger: true,\n  showQuickJumper: true,\n  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n};", "map": {"version": 3, "names": ["PRODUCT_STATUS", "ONLINE", "OFFLINE", "PRODUCT_STATUS_OPTIONS", "label", "value", "color", "OPERATOR_OPTIONS", "LOCATION_OPTIONS", "LOGISTICS_OPTIONS", "PACKAGE_DURATION_OPTIONS", "DATA_RULE_OPTIONS", "PAGINATION_CONFIG", "defaultPageSize", "pageSizeOptions", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range"], "sources": ["G:/phpstudy_pro/WWW/admin/src/utils/constants.ts"], "sourcesContent": ["// 常量定义\n\n// 产品状态\nexport const PRODUCT_STATUS = {\n  ONLINE: 'online',\n  OFFLINE: 'offline',\n} as const;\n\n// 产品状态选项\nexport const PRODUCT_STATUS_OPTIONS = [\n  { label: '上架中', value: PRODUCT_STATUS.ONLINE, color: 'success' },\n  { label: '已下架', value: PRODUCT_STATUS.OFFLINE, color: 'default' },\n];\n\n// 运营商选项\nexport const OPERATOR_OPTIONS = [\n  { label: '中国移动', value: '中国移动' },\n  { label: '中国联通', value: '中国联通' },\n  { label: '中国电信', value: '中国电信' },\n];\n\n// 归属地选项\nexport const LOCATION_OPTIONS = [\n  { label: '全国', value: '全国' },\n  { label: '北京', value: '北京' },\n  { label: '上海', value: '上海' },\n  { label: '广州', value: '广州' },\n  { label: '深圳', value: '深圳' },\n  { label: '随机', value: '随机' },\n];\n\n// 物流方式选项\nexport const LOGISTICS_OPTIONS = [\n  { label: '京东快递', value: '京东快递' },\n  { label: '顺丰快递', value: '顺丰快递' },\n  { label: '申通快递', value: '申通快递' },\n  { label: '中通快递', value: '中通快递' },\n  { label: '圆通快递', value: '圆通快递' },\n  { label: '韵达快递', value: '韵达快递' },\n];\n\n// 套餐时长选项\nexport const PACKAGE_DURATION_OPTIONS = [\n  { label: '月套餐', value: '月套餐' },\n  { label: '季度套餐', value: '季度套餐' },\n  { label: '半年套餐', value: '半年套餐' },\n  { label: '年套餐', value: '年套餐' },\n];\n\n// 数据规则选项\nexport const DATA_RULE_OPTIONS = [\n  { label: '规则1', value: '1' },\n  { label: '规则2', value: '2' },\n  { label: '规则3', value: '3' },\n];\n\n// 分页配置\nexport const PAGINATION_CONFIG = {\n  defaultPageSize: 15,\n  pageSizeOptions: ['10', '15', '20', '50', '100'],\n  showSizeChanger: true,\n  showQuickJumper: true,\n  showTotal: (total: number, range: [number, number]) =>\n    `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n};\n"], "mappings": "AAAA;;AAEA;AACA,OAAO,MAAMA,cAAc,GAAG;EAC5BC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE;AACX,CAAU;;AAEV;AACA,OAAO,MAAMC,sBAAsB,GAAG,CACpC;EAAEC,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAEL,cAAc,CAACC,MAAM;EAAEK,KAAK,EAAE;AAAU,CAAC,EAChE;EAAEF,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAEL,cAAc,CAACE,OAAO;EAAEI,KAAK,EAAE;AAAU,CAAC,CAClE;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG,CAC9B;EAAEH,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,CACjC;;AAED;AACA,OAAO,MAAMG,gBAAgB,GAAG,CAC9B;EAAEJ,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAK,CAAC,EAC5B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAK,CAAC,EAC5B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAK,CAAC,EAC5B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAK,CAAC,EAC5B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAK,CAAC,EAC5B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAK,CAAC,CAC7B;;AAED;AACA,OAAO,MAAMI,iBAAiB,GAAG,CAC/B;EAAEL,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,CACjC;;AAED;AACA,OAAO,MAAMK,wBAAwB,GAAG,CACtC;EAAEN,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAM,CAAC,EAC9B;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAM,CAAC,CAC/B;;AAED;AACA,OAAO,MAAMM,iBAAiB,GAAG,CAC/B;EAAEP,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAI,CAAC,EAC5B;EAAED,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAI,CAAC,EAC5B;EAAED,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAI,CAAC,CAC7B;;AAED;AACA,OAAO,MAAMO,iBAAiB,GAAG;EAC/BC,eAAe,EAAE,EAAE;EACnBC,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;EAChDC,eAAe,EAAE,IAAI;EACrBC,eAAe,EAAE,IAAI;EACrBC,SAAS,EAAEA,CAACC,KAAa,EAAEC,KAAuB,KAChD,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAASD,KAAK;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
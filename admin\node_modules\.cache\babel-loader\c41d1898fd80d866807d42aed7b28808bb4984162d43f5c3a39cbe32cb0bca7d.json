{"ast": null, "code": "import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst skeletonClsLoading = new Keyframes(\"ant-skeleton-loading\", {\n  '0%': {\n    backgroundPosition: '100% 50%'\n  },\n  '100%': {\n    backgroundPosition: '0 50%'\n  }\n});\nconst genSkeletonElementCommonSize = size => ({\n  height: size,\n  lineHeight: unit(size)\n});\nconst genSkeletonElementAvatarSize = size => Object.assign({\n  width: size\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonColor = token => ({\n  background: token.skeletonLoadingBackground,\n  backgroundSize: '400% 100%',\n  animationName: skeletonClsLoading,\n  animationDuration: token.skeletonLoadingMotionDuration,\n  animationTimingFunction: 'ease',\n  animationIterationCount: 'infinite'\n});\nconst genSkeletonElementInputSize = (size, calc) => Object.assign({\n  width: calc(size).mul(5).equal(),\n  minWidth: calc(size).mul(5).equal()\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementAvatar = token => {\n  const {\n    skeletonAvatarCls,\n    gradientFromColor,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM\n  } = token;\n  return {\n    [skeletonAvatarCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor\n    }, genSkeletonElementAvatarSize(controlHeight)),\n    [\"\".concat(skeletonAvatarCls).concat(skeletonAvatarCls, \"-circle\")]: {\n      borderRadius: '50%'\n    },\n    [\"\".concat(skeletonAvatarCls).concat(skeletonAvatarCls, \"-lg\")]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),\n    [\"\".concat(skeletonAvatarCls).concat(skeletonAvatarCls, \"-sm\")]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))\n  };\n};\nconst genSkeletonElementInput = token => {\n  const {\n    controlHeight,\n    borderRadiusSM,\n    skeletonInputCls,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    calc\n  } = token;\n  return {\n    [skeletonInputCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM\n    }, genSkeletonElementInputSize(controlHeight, calc)),\n    [\"\".concat(skeletonInputCls, \"-lg\")]: Object.assign({}, genSkeletonElementInputSize(controlHeightLG, calc)),\n    [\"\".concat(skeletonInputCls, \"-sm\")]: Object.assign({}, genSkeletonElementInputSize(controlHeightSM, calc))\n  };\n};\nconst genSkeletonElementImageSize = size => Object.assign({\n  width: size\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementImage = token => {\n  const {\n    skeletonImageCls,\n    imageSizeBase,\n    gradientFromColor,\n    borderRadiusSM,\n    calc\n  } = token;\n  return {\n    [skeletonImageCls]: Object.assign(Object.assign({\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      verticalAlign: 'middle',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM\n    }, genSkeletonElementImageSize(calc(imageSizeBase).mul(2).equal())), {\n      [\"\".concat(skeletonImageCls, \"-path\")]: {\n        fill: '#bfbfbf'\n      },\n      [\"\".concat(skeletonImageCls, \"-svg\")]: Object.assign(Object.assign({}, genSkeletonElementImageSize(imageSizeBase)), {\n        maxWidth: calc(imageSizeBase).mul(4).equal(),\n        maxHeight: calc(imageSizeBase).mul(4).equal()\n      }),\n      [\"\".concat(skeletonImageCls, \"-svg\").concat(skeletonImageCls, \"-svg-circle\")]: {\n        borderRadius: '50%'\n      }\n    }),\n    [\"\".concat(skeletonImageCls).concat(skeletonImageCls, \"-circle\")]: {\n      borderRadius: '50%'\n    }\n  };\n};\nconst genSkeletonElementButtonShape = (token, size, buttonCls) => {\n  const {\n    skeletonButtonCls\n  } = token;\n  return {\n    [\"\".concat(buttonCls).concat(skeletonButtonCls, \"-circle\")]: {\n      width: size,\n      minWidth: size,\n      borderRadius: '50%'\n    },\n    [\"\".concat(buttonCls).concat(skeletonButtonCls, \"-round\")]: {\n      borderRadius: size\n    }\n  };\n};\nconst genSkeletonElementButtonSize = (size, calc) => Object.assign({\n  width: calc(size).mul(2).equal(),\n  minWidth: calc(size).mul(2).equal()\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementButton = token => {\n  const {\n    borderRadiusSM,\n    skeletonButtonCls,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    calc\n  } = token;\n  return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n    [skeletonButtonCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM,\n      width: calc(controlHeight).mul(2).equal(),\n      minWidth: calc(controlHeight).mul(2).equal()\n    }, genSkeletonElementButtonSize(controlHeight, calc))\n  }, genSkeletonElementButtonShape(token, controlHeight, skeletonButtonCls)), {\n    [\"\".concat(skeletonButtonCls, \"-lg\")]: Object.assign({}, genSkeletonElementButtonSize(controlHeightLG, calc))\n  }), genSkeletonElementButtonShape(token, controlHeightLG, \"\".concat(skeletonButtonCls, \"-lg\"))), {\n    [\"\".concat(skeletonButtonCls, \"-sm\")]: Object.assign({}, genSkeletonElementButtonSize(controlHeightSM, calc))\n  }), genSkeletonElementButtonShape(token, controlHeightSM, \"\".concat(skeletonButtonCls, \"-sm\")));\n};\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    skeletonAvatarCls,\n    skeletonTitleCls,\n    skeletonParagraphCls,\n    skeletonButtonCls,\n    skeletonInputCls,\n    skeletonImageCls,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    padding,\n    marginSM,\n    borderRadius,\n    titleHeight,\n    blockRadius,\n    paragraphLiHeight,\n    controlHeightXS,\n    paragraphMarginTop\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'table',\n      width: '100%',\n      [\"\".concat(componentCls, \"-header\")]: {\n        display: 'table-cell',\n        paddingInlineEnd: padding,\n        verticalAlign: 'top',\n        // Avatar\n        [skeletonAvatarCls]: Object.assign({\n          display: 'inline-block',\n          verticalAlign: 'top',\n          background: gradientFromColor\n        }, genSkeletonElementAvatarSize(controlHeight)),\n        [\"\".concat(skeletonAvatarCls, \"-circle\")]: {\n          borderRadius: '50%'\n        },\n        [\"\".concat(skeletonAvatarCls, \"-lg\")]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),\n        [\"\".concat(skeletonAvatarCls, \"-sm\")]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))\n      },\n      [\"\".concat(componentCls, \"-content\")]: {\n        display: 'table-cell',\n        width: '100%',\n        verticalAlign: 'top',\n        // Title\n        [skeletonTitleCls]: {\n          width: '100%',\n          height: titleHeight,\n          background: gradientFromColor,\n          borderRadius: blockRadius,\n          [\"+ \".concat(skeletonParagraphCls)]: {\n            marginBlockStart: controlHeightSM\n          }\n        },\n        // paragraph\n        [skeletonParagraphCls]: {\n          padding: 0,\n          '> li': {\n            width: '100%',\n            height: paragraphLiHeight,\n            listStyle: 'none',\n            background: gradientFromColor,\n            borderRadius: blockRadius,\n            '+ li': {\n              marginBlockStart: controlHeightXS\n            }\n          }\n        },\n        [\"\".concat(skeletonParagraphCls, \"> li:last-child:not(:first-child):not(:nth-child(2))\")]: {\n          width: '61%'\n        }\n      },\n      [\"&-round \".concat(componentCls, \"-content\")]: {\n        [\"\".concat(skeletonTitleCls, \", \").concat(skeletonParagraphCls, \" > li\")]: {\n          borderRadius\n        }\n      }\n    },\n    [\"\".concat(componentCls, \"-with-avatar \").concat(componentCls, \"-content\")]: {\n      // Title\n      [skeletonTitleCls]: {\n        marginBlockStart: marginSM,\n        [\"+ \".concat(skeletonParagraphCls)]: {\n          marginBlockStart: paragraphMarginTop\n        }\n      }\n    },\n    // Skeleton element\n    [\"\".concat(componentCls).concat(componentCls, \"-element\")]: Object.assign(Object.assign(Object.assign(Object.assign({\n      display: 'inline-block',\n      width: 'auto'\n    }, genSkeletonElementButton(token)), genSkeletonElementAvatar(token)), genSkeletonElementInput(token)), genSkeletonElementImage(token)),\n    // Skeleton Block Button, Input\n    [\"\".concat(componentCls).concat(componentCls, \"-block\")]: {\n      width: '100%',\n      [skeletonButtonCls]: {\n        width: '100%'\n      },\n      [skeletonInputCls]: {\n        width: '100%'\n      }\n    },\n    // With active animation\n    [\"\".concat(componentCls).concat(componentCls, \"-active\")]: {\n      [\"\\n        \".concat(skeletonTitleCls, \",\\n        \").concat(skeletonParagraphCls, \" > li,\\n        \").concat(skeletonAvatarCls, \",\\n        \").concat(skeletonButtonCls, \",\\n        \").concat(skeletonInputCls, \",\\n        \").concat(skeletonImageCls, \"\\n      \")]: Object.assign({}, genSkeletonColor(token))\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    colorFillContent,\n    colorFill\n  } = token;\n  const gradientFromColor = colorFillContent;\n  const gradientToColor = colorFill;\n  return {\n    color: gradientFromColor,\n    colorGradientEnd: gradientToColor,\n    gradientFromColor,\n    gradientToColor,\n    titleHeight: token.controlHeight / 2,\n    blockRadius: token.borderRadiusSM,\n    paragraphMarginTop: token.marginLG + token.marginXXS,\n    paragraphLiHeight: token.controlHeight / 2\n  };\n};\nexport default genStyleHooks('Skeleton', token => {\n  const {\n    componentCls,\n    calc\n  } = token;\n  const skeletonToken = mergeToken(token, {\n    skeletonAvatarCls: \"\".concat(componentCls, \"-avatar\"),\n    skeletonTitleCls: \"\".concat(componentCls, \"-title\"),\n    skeletonParagraphCls: \"\".concat(componentCls, \"-paragraph\"),\n    skeletonButtonCls: \"\".concat(componentCls, \"-button\"),\n    skeletonInputCls: \"\".concat(componentCls, \"-input\"),\n    skeletonImageCls: \"\".concat(componentCls, \"-image\"),\n    imageSizeBase: calc(token.controlHeight).mul(1.5).equal(),\n    borderRadius: 100,\n    // Large number to make capsule shape\n    skeletonLoadingBackground: \"linear-gradient(90deg, \".concat(token.gradientFromColor, \" 25%, \").concat(token.gradientToColor, \" 37%, \").concat(token.gradientFromColor, \" 63%)\"),\n    skeletonLoadingMotionDuration: '1.4s'\n  });\n  return [genBaseStyle(skeletonToken)];\n}, prepareComponentToken, {\n  deprecatedTokens: [['color', 'gradientFromColor'], ['colorGradientEnd', 'gradientToColor']]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React from 'react';\nimport { cloneElement, isFragment } from '../_util/reactNode';\nimport { PresetColors } from '../theme/interface';\nconst rxTwoCNChar = /^[\\u4E00-\\u9FA5]{2}$/;\nexport const isTwoCNChar = rxTwoCNChar.test.bind(rxTwoCNChar);\nexport function convertLegacyProps(type) {\n  if (type === 'danger') {\n    return {\n      danger: true\n    };\n  }\n  return {\n    type\n  };\n}\nexport function isString(str) {\n  return typeof str === 'string';\n}\nexport function isUnBorderedButtonVariant(type) {\n  return type === 'text' || type === 'link';\n}\nfunction splitCNCharsBySpace(child, needInserted) {\n  if (child === null || child === undefined) {\n    return;\n  }\n  const SPACE = needInserted ? ' ' : '';\n  if (typeof child !== 'string' && typeof child !== 'number' && isString(child.type) && isTwoCNChar(child.props.children)) {\n    return cloneElement(child, {\n      children: child.props.children.split('').join(SPACE)\n    });\n  }\n  if (isString(child)) {\n    return isTwoCNChar(child) ? /*#__PURE__*/React.createElement(\"span\", null, child.split('').join(SPACE)) : /*#__PURE__*/React.createElement(\"span\", null, child);\n  }\n  if (isFragment(child)) {\n    return /*#__PURE__*/React.createElement(\"span\", null, child);\n  }\n  return child;\n}\nexport function spaceChildren(children, needInserted) {\n  let isPrevChildPure = false;\n  const childList = [];\n  React.Children.forEach(children, child => {\n    const type = typeof child;\n    const isCurrentChildPure = type === 'string' || type === 'number';\n    if (isPrevChildPure && isCurrentChildPure) {\n      const lastIndex = childList.length - 1;\n      const lastChild = childList[lastIndex];\n      childList[lastIndex] = \"\".concat(lastChild).concat(child);\n    } else {\n      childList.push(child);\n    }\n    isPrevChildPure = isCurrentChildPure;\n  });\n  return React.Children.map(childList, child => splitCNCharsBySpace(child, needInserted));\n}\nconst _ButtonTypes = ['default', 'primary', 'dashed', 'link', 'text'];\nconst _ButtonShapes = ['default', 'circle', 'round'];\nconst _ButtonHTMLTypes = ['submit', 'button', 'reset'];\nexport const _ButtonVariantTypes = ['outlined', 'dashed', 'solid', 'filled', 'text', 'link'];\nexport const _ButtonColorTypes = ['default', 'primary', 'danger'].concat(_toConsumableArray(PresetColors));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
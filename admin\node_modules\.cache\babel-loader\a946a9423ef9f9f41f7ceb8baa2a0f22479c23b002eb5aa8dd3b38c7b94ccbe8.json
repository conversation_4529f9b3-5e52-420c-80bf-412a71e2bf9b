{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n/* eslint-disable react/no-unknown-property */\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMobile from \"rc-util/es/hooks/useMobile\";\nimport raf from \"rc-util/es/raf\";\n\n/**\n * When click and hold on a button - the speed of auto changing the value.\n */\nvar STEP_INTERVAL = 200;\n\n/**\n * When click and hold on a button - the delay before auto changing the value.\n */\nvar STEP_DELAY = 600;\nexport default function StepHandler(_ref) {\n  var prefixCls = _ref.prefixCls,\n    upNode = _ref.upNode,\n    downNode = _ref.downNode,\n    upDisabled = _ref.upDisabled,\n    downDisabled = _ref.downDisabled,\n    onStep = _ref.onStep;\n  // ======================== Step ========================\n  var stepTimeoutRef = React.useRef();\n  var frameIds = React.useRef([]);\n  var onStepRef = React.useRef();\n  onStepRef.current = onStep;\n  var onStopStep = function onStopStep() {\n    clearTimeout(stepTimeoutRef.current);\n  };\n\n  // We will interval update step when hold mouse down\n  var onStepMouseDown = function onStepMouseDown(e, up) {\n    e.preventDefault();\n    onStopStep();\n    onStepRef.current(up);\n\n    // Loop step for interval\n    function loopStep() {\n      onStepRef.current(up);\n      stepTimeoutRef.current = setTimeout(loopStep, STEP_INTERVAL);\n    }\n\n    // First time press will wait some time to trigger loop step update\n    stepTimeoutRef.current = setTimeout(loopStep, STEP_DELAY);\n  };\n  React.useEffect(function () {\n    return function () {\n      onStopStep();\n      frameIds.current.forEach(function (id) {\n        return raf.cancel(id);\n      });\n    };\n  }, []);\n\n  // ======================= Render =======================\n  var isMobile = useMobile();\n  if (isMobile) {\n    return null;\n  }\n  var handlerClassName = \"\".concat(prefixCls, \"-handler\");\n  var upClassName = classNames(handlerClassName, \"\".concat(handlerClassName, \"-up\"), _defineProperty({}, \"\".concat(handlerClassName, \"-up-disabled\"), upDisabled));\n  var downClassName = classNames(handlerClassName, \"\".concat(handlerClassName, \"-down\"), _defineProperty({}, \"\".concat(handlerClassName, \"-down-disabled\"), downDisabled));\n\n  // fix: https://github.com/ant-design/ant-design/issues/43088\n  // In Safari, When we fire onmousedown and onmouseup events in quick succession, \n  // there may be a problem that the onmouseup events are executed first, \n  // resulting in a disordered program execution.\n  // So, we need to use requestAnimationFrame to ensure that the onmouseup event is executed after the onmousedown event.\n  var safeOnStopStep = function safeOnStopStep() {\n    return frameIds.current.push(raf(onStopStep));\n  };\n  var sharedHandlerProps = {\n    unselectable: 'on',\n    role: 'button',\n    onMouseUp: safeOnStopStep,\n    onMouseLeave: safeOnStopStep\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(handlerClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"span\", _extends({}, sharedHandlerProps, {\n    onMouseDown: function onMouseDown(e) {\n      onStepMouseDown(e, true);\n    },\n    \"aria-label\": \"Increase Value\",\n    \"aria-disabled\": upDisabled,\n    className: upClassName\n  }), upNode || /*#__PURE__*/React.createElement(\"span\", {\n    unselectable: \"on\",\n    className: \"\".concat(prefixCls, \"-handler-up-inner\")\n  })), /*#__PURE__*/React.createElement(\"span\", _extends({}, sharedHandlerProps, {\n    onMouseDown: function onMouseDown(e) {\n      onStepMouseDown(e, false);\n    },\n    \"aria-label\": \"Decrease Value\",\n    \"aria-disabled\": downDisabled,\n    className: downClassName\n  }), downNode || /*#__PURE__*/React.createElement(\"span\", {\n    unselectable: \"on\",\n    className: \"\".concat(prefixCls, \"-handler-down-inner\")\n  })));\n}", "map": {"version": 3, "names": ["_extends", "_defineProperty", "React", "classNames", "useMobile", "raf", "STEP_INTERVAL", "STEP_DELAY", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "prefixCls", "upNode", "downNode", "upDisabled", "downDisabled", "onStep", "stepTimeoutRef", "useRef", "frameIds", "onStepRef", "current", "onStopStep", "clearTimeout", "onStepMouseDown", "e", "up", "preventDefault", "loopStep", "setTimeout", "useEffect", "for<PERSON>ach", "id", "cancel", "isMobile", "handlerClassName", "concat", "upClassName", "downClassName", "safeOnStopStep", "push", "sharedHandlerProps", "unselectable", "role", "onMouseUp", "onMouseLeave", "createElement", "className", "onMouseDown"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-input-number/es/StepHandler.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n/* eslint-disable react/no-unknown-property */\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMobile from \"rc-util/es/hooks/useMobile\";\nimport raf from \"rc-util/es/raf\";\n\n/**\n * When click and hold on a button - the speed of auto changing the value.\n */\nvar STEP_INTERVAL = 200;\n\n/**\n * When click and hold on a button - the delay before auto changing the value.\n */\nvar STEP_DELAY = 600;\nexport default function StepHandler(_ref) {\n  var prefixCls = _ref.prefixCls,\n    upNode = _ref.upNode,\n    downNode = _ref.downNode,\n    upDisabled = _ref.upDisabled,\n    downDisabled = _ref.downDisabled,\n    onStep = _ref.onStep;\n  // ======================== Step ========================\n  var stepTimeoutRef = React.useRef();\n  var frameIds = React.useRef([]);\n  var onStepRef = React.useRef();\n  onStepRef.current = onStep;\n  var onStopStep = function onStopStep() {\n    clearTimeout(stepTimeoutRef.current);\n  };\n\n  // We will interval update step when hold mouse down\n  var onStepMouseDown = function onStepMouseDown(e, up) {\n    e.preventDefault();\n    onStopStep();\n    onStepRef.current(up);\n\n    // Loop step for interval\n    function loopStep() {\n      onStepRef.current(up);\n      stepTimeoutRef.current = setTimeout(loopStep, STEP_INTERVAL);\n    }\n\n    // First time press will wait some time to trigger loop step update\n    stepTimeoutRef.current = setTimeout(loopStep, STEP_DELAY);\n  };\n  React.useEffect(function () {\n    return function () {\n      onStopStep();\n      frameIds.current.forEach(function (id) {\n        return raf.cancel(id);\n      });\n    };\n  }, []);\n\n  // ======================= Render =======================\n  var isMobile = useMobile();\n  if (isMobile) {\n    return null;\n  }\n  var handlerClassName = \"\".concat(prefixCls, \"-handler\");\n  var upClassName = classNames(handlerClassName, \"\".concat(handlerClassName, \"-up\"), _defineProperty({}, \"\".concat(handlerClassName, \"-up-disabled\"), upDisabled));\n  var downClassName = classNames(handlerClassName, \"\".concat(handlerClassName, \"-down\"), _defineProperty({}, \"\".concat(handlerClassName, \"-down-disabled\"), downDisabled));\n\n  // fix: https://github.com/ant-design/ant-design/issues/43088\n  // In Safari, When we fire onmousedown and onmouseup events in quick succession, \n  // there may be a problem that the onmouseup events are executed first, \n  // resulting in a disordered program execution.\n  // So, we need to use requestAnimationFrame to ensure that the onmouseup event is executed after the onmousedown event.\n  var safeOnStopStep = function safeOnStopStep() {\n    return frameIds.current.push(raf(onStopStep));\n  };\n  var sharedHandlerProps = {\n    unselectable: 'on',\n    role: 'button',\n    onMouseUp: safeOnStopStep,\n    onMouseLeave: safeOnStopStep\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(handlerClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"span\", _extends({}, sharedHandlerProps, {\n    onMouseDown: function onMouseDown(e) {\n      onStepMouseDown(e, true);\n    },\n    \"aria-label\": \"Increase Value\",\n    \"aria-disabled\": upDisabled,\n    className: upClassName\n  }), upNode || /*#__PURE__*/React.createElement(\"span\", {\n    unselectable: \"on\",\n    className: \"\".concat(prefixCls, \"-handler-up-inner\")\n  })), /*#__PURE__*/React.createElement(\"span\", _extends({}, sharedHandlerProps, {\n    onMouseDown: function onMouseDown(e) {\n      onStepMouseDown(e, false);\n    },\n    \"aria-label\": \"Decrease Value\",\n    \"aria-disabled\": downDisabled,\n    className: downClassName\n  }), downNode || /*#__PURE__*/React.createElement(\"span\", {\n    unselectable: \"on\",\n    className: \"\".concat(prefixCls, \"-handler-down-inner\")\n  })));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,GAAG,MAAM,gBAAgB;;AAEhC;AACA;AACA;AACA,IAAIC,aAAa,GAAG,GAAG;;AAEvB;AACA;AACA;AACA,IAAIC,UAAU,GAAG,GAAG;AACpB,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,YAAY,GAAGL,IAAI,CAACK,YAAY;IAChCC,MAAM,GAAGN,IAAI,CAACM,MAAM;EACtB;EACA,IAAIC,cAAc,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC;EACnC,IAAIC,QAAQ,GAAGhB,KAAK,CAACe,MAAM,CAAC,EAAE,CAAC;EAC/B,IAAIE,SAAS,GAAGjB,KAAK,CAACe,MAAM,CAAC,CAAC;EAC9BE,SAAS,CAACC,OAAO,GAAGL,MAAM;EAC1B,IAAIM,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrCC,YAAY,CAACN,cAAc,CAACI,OAAO,CAAC;EACtC,CAAC;;EAED;EACA,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAEC,EAAE,EAAE;IACpDD,CAAC,CAACE,cAAc,CAAC,CAAC;IAClBL,UAAU,CAAC,CAAC;IACZF,SAAS,CAACC,OAAO,CAACK,EAAE,CAAC;;IAErB;IACA,SAASE,QAAQA,CAAA,EAAG;MAClBR,SAAS,CAACC,OAAO,CAACK,EAAE,CAAC;MACrBT,cAAc,CAACI,OAAO,GAAGQ,UAAU,CAACD,QAAQ,EAAErB,aAAa,CAAC;IAC9D;;IAEA;IACAU,cAAc,CAACI,OAAO,GAAGQ,UAAU,CAACD,QAAQ,EAAEpB,UAAU,CAAC;EAC3D,CAAC;EACDL,KAAK,CAAC2B,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBR,UAAU,CAAC,CAAC;MACZH,QAAQ,CAACE,OAAO,CAACU,OAAO,CAAC,UAAUC,EAAE,EAAE;QACrC,OAAO1B,GAAG,CAAC2B,MAAM,CAACD,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIE,QAAQ,GAAG7B,SAAS,CAAC,CAAC;EAC1B,IAAI6B,QAAQ,EAAE;IACZ,OAAO,IAAI;EACb;EACA,IAAIC,gBAAgB,GAAG,EAAE,CAACC,MAAM,CAACzB,SAAS,EAAE,UAAU,CAAC;EACvD,IAAI0B,WAAW,GAAGjC,UAAU,CAAC+B,gBAAgB,EAAE,EAAE,CAACC,MAAM,CAACD,gBAAgB,EAAE,KAAK,CAAC,EAAEjC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACkC,MAAM,CAACD,gBAAgB,EAAE,cAAc,CAAC,EAAErB,UAAU,CAAC,CAAC;EAChK,IAAIwB,aAAa,GAAGlC,UAAU,CAAC+B,gBAAgB,EAAE,EAAE,CAACC,MAAM,CAACD,gBAAgB,EAAE,OAAO,CAAC,EAAEjC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACkC,MAAM,CAACD,gBAAgB,EAAE,gBAAgB,CAAC,EAAEpB,YAAY,CAAC,CAAC;;EAExK;EACA;EACA;EACA;EACA;EACA,IAAIwB,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,OAAOpB,QAAQ,CAACE,OAAO,CAACmB,IAAI,CAAClC,GAAG,CAACgB,UAAU,CAAC,CAAC;EAC/C,CAAC;EACD,IAAImB,kBAAkB,GAAG;IACvBC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEL,cAAc;IACzBM,YAAY,EAAEN;EAChB,CAAC;EACD,OAAO,aAAapC,KAAK,CAAC2C,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACX,MAAM,CAACD,gBAAgB,EAAE,OAAO;EAChD,CAAC,EAAE,aAAahC,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE7C,QAAQ,CAAC,CAAC,CAAC,EAAEwC,kBAAkB,EAAE;IAC3EO,WAAW,EAAE,SAASA,WAAWA,CAACvB,CAAC,EAAE;MACnCD,eAAe,CAACC,CAAC,EAAE,IAAI,CAAC;IAC1B,CAAC;IACD,YAAY,EAAE,gBAAgB;IAC9B,eAAe,EAAEX,UAAU;IAC3BiC,SAAS,EAAEV;EACb,CAAC,CAAC,EAAEzB,MAAM,IAAI,aAAaT,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE;IACrDJ,YAAY,EAAE,IAAI;IAClBK,SAAS,EAAE,EAAE,CAACX,MAAM,CAACzB,SAAS,EAAE,mBAAmB;EACrD,CAAC,CAAC,CAAC,EAAE,aAAaR,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE7C,QAAQ,CAAC,CAAC,CAAC,EAAEwC,kBAAkB,EAAE;IAC7EO,WAAW,EAAE,SAASA,WAAWA,CAACvB,CAAC,EAAE;MACnCD,eAAe,CAACC,CAAC,EAAE,KAAK,CAAC;IAC3B,CAAC;IACD,YAAY,EAAE,gBAAgB;IAC9B,eAAe,EAAEV,YAAY;IAC7BgC,SAAS,EAAET;EACb,CAAC,CAAC,EAAEzB,QAAQ,IAAI,aAAaV,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE;IACvDJ,YAAY,EAAE,IAAI;IAClBK,SAAS,EAAE,EAAE,CAACX,MAAM,CAACzB,SAAS,EAAE,qBAAqB;EACvD,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
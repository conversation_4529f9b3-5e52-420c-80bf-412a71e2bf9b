{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nexport function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value !== undefined ? [value] : [];\n}\nexport var isClient = typeof window !== 'undefined' && window.document && window.document.documentElement;\n\n/** Is client side and not jsdom */\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && isClient;\nexport function hasValue(value) {\n  return value !== undefined && value !== null;\n}\n\n/** combo mode no value judgment function */\nexport function isComboNoValue(value) {\n  return !value && value !== 0;\n}\nfunction isTitleType(title) {\n  return ['string', 'number'].includes(_typeof(title));\n}\nexport function getTitle(item) {\n  var title = undefined;\n  if (item) {\n    if (isTitleType(item.title)) {\n      title = item.title.toString();\n    } else if (isTitleType(item.label)) {\n      title = item.label.toString();\n    }\n  }\n  return title;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
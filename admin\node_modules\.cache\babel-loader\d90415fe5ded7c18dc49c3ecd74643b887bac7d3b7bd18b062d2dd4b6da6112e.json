{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genFocusStyle, resetComponent } from '../../style';\nimport { initMoveMotion, initSlideMotion, initZoomMotion, slideDownIn, slideDownOut, slideUpIn, slideUpOut } from '../../style/motion';\nimport getArrowStyle, { getArrowOffsetToken } from '../../style/placementArrow';\nimport { getArrowToken } from '../../style/roundedArrow';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genStatusStyle from './status';\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    menuCls,\n    zIndexPopup,\n    dropdownArrowDistance,\n    sizePopupArrow,\n    antCls,\n    iconCls,\n    motionDurationMid,\n    paddingBlock,\n    fontSize,\n    dropdownEdgeChildPadding,\n    colorTextDisabled,\n    fontSizeIcon,\n    controlPaddingHorizontal,\n    colorBgElevated\n  } = token;\n  return [{\n    [componentCls]: {\n      position: 'absolute',\n      top: -9999,\n      left: {\n        _skip_check_: true,\n        value: -9999\n      },\n      zIndex: zIndexPopup,\n      display: 'block',\n      // A placeholder out of dropdown visible range to avoid close when user moving\n      '&::before': {\n        position: 'absolute',\n        insetBlock: token.calc(sizePopupArrow).div(2).sub(dropdownArrowDistance).equal(),\n        // insetInlineStart: -7, // FIXME: Seems not work for hidden element\n        zIndex: -9999,\n        opacity: 0.0001,\n        content: '\"\"'\n      },\n      // Makes vertical dropdowns have a scrollbar once they become taller than the viewport.\n      '&-menu-vertical': {\n        maxHeight: '100vh',\n        overflowY: 'auto'\n      },\n      [\"&-trigger\".concat(antCls, \"-btn\")]: {\n        [\"& > \".concat(iconCls, \"-down, & > \").concat(antCls, \"-btn-icon > \").concat(iconCls, \"-down\")]: {\n          fontSize: fontSizeIcon\n        }\n      },\n      [\"\".concat(componentCls, \"-wrap\")]: {\n        position: 'relative',\n        [\"\".concat(antCls, \"-btn > \").concat(iconCls, \"-down\")]: {\n          fontSize: fontSizeIcon\n        },\n        [\"\".concat(iconCls, \"-down::before\")]: {\n          transition: \"transform \".concat(motionDurationMid)\n        }\n      },\n      [\"\".concat(componentCls, \"-wrap-open\")]: {\n        [\"\".concat(iconCls, \"-down::before\")]: {\n          transform: \"rotate(180deg)\"\n        }\n      },\n      [\"\\n        &-hidden,\\n        &-menu-hidden,\\n        &-menu-submenu-hidden\\n      \"]: {\n        display: 'none'\n      },\n      // =============================================================\n      // ==                         Motion                          ==\n      // =============================================================\n      // When position is not enough for dropdown, the placement will revert.\n      // We will handle this with revert motion name.\n      [\"&\".concat(antCls, \"-slide-down-enter\").concat(antCls, \"-slide-down-enter-active\").concat(componentCls, \"-placement-bottomLeft,\\n          &\").concat(antCls, \"-slide-down-appear\").concat(antCls, \"-slide-down-appear-active\").concat(componentCls, \"-placement-bottomLeft,\\n          &\").concat(antCls, \"-slide-down-enter\").concat(antCls, \"-slide-down-enter-active\").concat(componentCls, \"-placement-bottom,\\n          &\").concat(antCls, \"-slide-down-appear\").concat(antCls, \"-slide-down-appear-active\").concat(componentCls, \"-placement-bottom,\\n          &\").concat(antCls, \"-slide-down-enter\").concat(antCls, \"-slide-down-enter-active\").concat(componentCls, \"-placement-bottomRight,\\n          &\").concat(antCls, \"-slide-down-appear\").concat(antCls, \"-slide-down-appear-active\").concat(componentCls, \"-placement-bottomRight\")]: {\n        animationName: slideUpIn\n      },\n      [\"&\".concat(antCls, \"-slide-up-enter\").concat(antCls, \"-slide-up-enter-active\").concat(componentCls, \"-placement-topLeft,\\n          &\").concat(antCls, \"-slide-up-appear\").concat(antCls, \"-slide-up-appear-active\").concat(componentCls, \"-placement-topLeft,\\n          &\").concat(antCls, \"-slide-up-enter\").concat(antCls, \"-slide-up-enter-active\").concat(componentCls, \"-placement-top,\\n          &\").concat(antCls, \"-slide-up-appear\").concat(antCls, \"-slide-up-appear-active\").concat(componentCls, \"-placement-top,\\n          &\").concat(antCls, \"-slide-up-enter\").concat(antCls, \"-slide-up-enter-active\").concat(componentCls, \"-placement-topRight,\\n          &\").concat(antCls, \"-slide-up-appear\").concat(antCls, \"-slide-up-appear-active\").concat(componentCls, \"-placement-topRight\")]: {\n        animationName: slideDownIn\n      },\n      [\"&\".concat(antCls, \"-slide-down-leave\").concat(antCls, \"-slide-down-leave-active\").concat(componentCls, \"-placement-bottomLeft,\\n          &\").concat(antCls, \"-slide-down-leave\").concat(antCls, \"-slide-down-leave-active\").concat(componentCls, \"-placement-bottom,\\n          &\").concat(antCls, \"-slide-down-leave\").concat(antCls, \"-slide-down-leave-active\").concat(componentCls, \"-placement-bottomRight\")]: {\n        animationName: slideUpOut\n      },\n      [\"&\".concat(antCls, \"-slide-up-leave\").concat(antCls, \"-slide-up-leave-active\").concat(componentCls, \"-placement-topLeft,\\n          &\").concat(antCls, \"-slide-up-leave\").concat(antCls, \"-slide-up-leave-active\").concat(componentCls, \"-placement-top,\\n          &\").concat(antCls, \"-slide-up-leave\").concat(antCls, \"-slide-up-leave-active\").concat(componentCls, \"-placement-topRight\")]: {\n        animationName: slideDownOut\n      }\n    }\n  },\n  // =============================================================\n  // ==                        Arrow style                      ==\n  // =============================================================\n  getArrowStyle(token, colorBgElevated, {\n    arrowPlacement: {\n      top: true,\n      bottom: true\n    }\n  }), {\n    // =============================================================\n    // ==                          Menu                           ==\n    // =============================================================\n    [\"\".concat(componentCls, \" \").concat(menuCls)]: {\n      position: 'relative',\n      margin: 0\n    },\n    [\"\".concat(menuCls, \"-submenu-popup\")]: {\n      position: 'absolute',\n      zIndex: zIndexPopup,\n      background: 'transparent',\n      boxShadow: 'none',\n      transformOrigin: '0 0',\n      'ul, li': {\n        listStyle: 'none',\n        margin: 0\n      }\n    },\n    [\"\".concat(componentCls, \", \").concat(componentCls, \"-menu-submenu\")]: Object.assign(Object.assign({}, resetComponent(token)), {\n      [menuCls]: Object.assign(Object.assign({\n        padding: dropdownEdgeChildPadding,\n        listStyleType: 'none',\n        backgroundColor: colorBgElevated,\n        backgroundClip: 'padding-box',\n        borderRadius: token.borderRadiusLG,\n        outline: 'none',\n        boxShadow: token.boxShadowSecondary\n      }, genFocusStyle(token)), {\n        '&:empty': {\n          padding: 0,\n          boxShadow: 'none'\n        },\n        [\"\".concat(menuCls, \"-item-group-title\")]: {\n          padding: \"\".concat(unit(paddingBlock), \" \").concat(unit(controlPaddingHorizontal)),\n          color: token.colorTextDescription,\n          transition: \"all \".concat(motionDurationMid)\n        },\n        // ======================= Item Content =======================\n        [\"\".concat(menuCls, \"-item\")]: {\n          position: 'relative',\n          display: 'flex',\n          alignItems: 'center'\n        },\n        [\"\".concat(menuCls, \"-item-icon\")]: {\n          minWidth: fontSize,\n          marginInlineEnd: token.marginXS,\n          fontSize: token.fontSizeSM\n        },\n        [\"\".concat(menuCls, \"-title-content\")]: {\n          flex: 'auto',\n          '&-with-extra': {\n            display: 'inline-flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          '> a': {\n            color: 'inherit',\n            transition: \"all \".concat(motionDurationMid),\n            '&:hover': {\n              color: 'inherit'\n            },\n            '&::after': {\n              position: 'absolute',\n              inset: 0,\n              content: '\"\"'\n            }\n          },\n          [\"\".concat(menuCls, \"-item-extra\")]: {\n            paddingInlineStart: token.padding,\n            marginInlineStart: 'auto',\n            fontSize: token.fontSizeSM,\n            color: token.colorTextDescription\n          }\n        },\n        // =========================== Item ===========================\n        [\"\".concat(menuCls, \"-item, \").concat(menuCls, \"-submenu-title\")]: Object.assign(Object.assign({\n          display: 'flex',\n          margin: 0,\n          padding: \"\".concat(unit(paddingBlock), \" \").concat(unit(controlPaddingHorizontal)),\n          color: token.colorText,\n          fontWeight: 'normal',\n          fontSize,\n          lineHeight: token.lineHeight,\n          cursor: 'pointer',\n          transition: \"all \".concat(motionDurationMid),\n          borderRadius: token.borderRadiusSM,\n          '&:hover, &-active': {\n            backgroundColor: token.controlItemBgHover\n          }\n        }, genFocusStyle(token)), {\n          '&-selected': {\n            color: token.colorPrimary,\n            backgroundColor: token.controlItemBgActive,\n            '&:hover, &-active': {\n              backgroundColor: token.controlItemBgActiveHover\n            }\n          },\n          '&-disabled': {\n            color: colorTextDisabled,\n            cursor: 'not-allowed',\n            '&:hover': {\n              color: colorTextDisabled,\n              backgroundColor: colorBgElevated,\n              cursor: 'not-allowed'\n            },\n            a: {\n              pointerEvents: 'none'\n            }\n          },\n          '&-divider': {\n            height: 1,\n            // By design\n            margin: \"\".concat(unit(token.marginXXS), \" 0\"),\n            overflow: 'hidden',\n            lineHeight: 0,\n            backgroundColor: token.colorSplit\n          },\n          [\"\".concat(componentCls, \"-menu-submenu-expand-icon\")]: {\n            position: 'absolute',\n            insetInlineEnd: token.paddingXS,\n            [\"\".concat(componentCls, \"-menu-submenu-arrow-icon\")]: {\n              marginInlineEnd: '0 !important',\n              color: token.colorIcon,\n              fontSize: fontSizeIcon,\n              fontStyle: 'normal'\n            }\n          }\n        }),\n        [\"\".concat(menuCls, \"-item-group-list\")]: {\n          margin: \"0 \".concat(unit(token.marginXS)),\n          padding: 0,\n          listStyle: 'none'\n        },\n        [\"\".concat(menuCls, \"-submenu-title\")]: {\n          paddingInlineEnd: token.calc(controlPaddingHorizontal).add(token.fontSizeSM).equal()\n        },\n        [\"\".concat(menuCls, \"-submenu-vertical\")]: {\n          position: 'relative'\n        },\n        [\"\".concat(menuCls, \"-submenu\").concat(menuCls, \"-submenu-disabled \").concat(componentCls, \"-menu-submenu-title\")]: {\n          [\"&, \".concat(componentCls, \"-menu-submenu-arrow-icon\")]: {\n            color: colorTextDisabled,\n            backgroundColor: colorBgElevated,\n            cursor: 'not-allowed'\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/19264\n        [\"\".concat(menuCls, \"-submenu-selected \").concat(componentCls, \"-menu-submenu-title\")]: {\n          color: token.colorPrimary\n        }\n      })\n    })\n  },\n  // Follow code may reuse in other components\n  [initSlideMotion(token, 'slide-up'), initSlideMotion(token, 'slide-down'), initMoveMotion(token, 'move-up'), initMoveMotion(token, 'move-down'), initZoomMotion(token, 'zoom-big')]];\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => Object.assign(Object.assign({\n  zIndexPopup: token.zIndexPopupBase + 50,\n  paddingBlock: (token.controlHeight - token.fontSize * token.lineHeight) / 2\n}, getArrowOffsetToken({\n  contentRadius: token.borderRadiusLG,\n  limitVerticalRadius: true\n})), getArrowToken(token));\nexport default genStyleHooks('Dropdown', token => {\n  const {\n    marginXXS,\n    sizePopupArrow,\n    paddingXXS,\n    componentCls\n  } = token;\n  const dropdownToken = mergeToken(token, {\n    menuCls: \"\".concat(componentCls, \"-menu\"),\n    dropdownArrowDistance: token.calc(sizePopupArrow).div(2).add(marginXXS).equal(),\n    dropdownEdgeChildPadding: paddingXXS\n  });\n  return [genBaseStyle(dropdownToken), genStatusStyle(dropdownToken)];\n}, prepareComponentToken, {\n  resetStyle: false\n});", "map": {"version": 3, "names": ["unit", "genFocusStyle", "resetComponent", "initMoveMotion", "initSlideMotion", "initZoomMotion", "slideDownIn", "slideDownOut", "slideUpIn", "slideUpOut", "getArrowStyle", "getArrowOffsetToken", "getArrowToken", "genStyleHooks", "mergeToken", "genStatusStyle", "genBaseStyle", "token", "componentCls", "menuCls", "zIndexPopup", "dropdownArrowDistance", "sizePopupArrow", "antCls", "iconCls", "motionDurationMid", "paddingBlock", "fontSize", "dropdownEdgeChildPadding", "colorTextDisabled", "fontSizeIcon", "controlPaddingHorizontal", "colorBgElevated", "position", "top", "left", "_skip_check_", "value", "zIndex", "display", "insetBlock", "calc", "div", "sub", "equal", "opacity", "content", "maxHeight", "overflowY", "concat", "transition", "transform", "animationName", "arrowPlacement", "bottom", "margin", "background", "boxShadow", "transform<PERSON><PERSON>in", "listStyle", "Object", "assign", "padding", "listStyleType", "backgroundColor", "backgroundClip", "borderRadius", "borderRadiusLG", "outline", "boxShadowSecondary", "color", "colorTextDescription", "alignItems", "min<PERSON><PERSON><PERSON>", "marginInlineEnd", "marginXS", "fontSizeSM", "flex", "width", "inset", "paddingInlineStart", "marginInlineStart", "colorText", "fontWeight", "lineHeight", "cursor", "borderRadiusSM", "controlItemBgHover", "colorPrimary", "controlItemBgActive", "controlItemBgActiveHover", "a", "pointerEvents", "height", "marginXXS", "overflow", "colorSplit", "insetInlineEnd", "paddingXS", "colorIcon", "fontStyle", "paddingInlineEnd", "add", "prepareComponentToken", "zIndexPopupBase", "controlHeight", "contentRadius", "limitVerticalRadius", "paddingXXS", "dropdownToken", "resetStyle"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/dropdown/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { genFocusStyle, resetComponent } from '../../style';\nimport { initMoveMotion, initSlideMotion, initZoomMotion, slideDownIn, slideDownOut, slideUpIn, slideUpOut } from '../../style/motion';\nimport getArrowStyle, { getArrowOffsetToken } from '../../style/placementArrow';\nimport { getArrowToken } from '../../style/roundedArrow';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genStatusStyle from './status';\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    menuCls,\n    zIndexPopup,\n    dropdownArrowDistance,\n    sizePopupArrow,\n    antCls,\n    iconCls,\n    motionDurationMid,\n    paddingBlock,\n    fontSize,\n    dropdownEdgeChildPadding,\n    colorTextDisabled,\n    fontSizeIcon,\n    controlPaddingHorizontal,\n    colorBgElevated\n  } = token;\n  return [{\n    [componentCls]: {\n      position: 'absolute',\n      top: -9999,\n      left: {\n        _skip_check_: true,\n        value: -9999\n      },\n      zIndex: zIndexPopup,\n      display: 'block',\n      // A placeholder out of dropdown visible range to avoid close when user moving\n      '&::before': {\n        position: 'absolute',\n        insetBlock: token.calc(sizePopupArrow).div(2).sub(dropdownArrowDistance).equal(),\n        // insetInlineStart: -7, // FIXME: Seems not work for hidden element\n        zIndex: -9999,\n        opacity: 0.0001,\n        content: '\"\"'\n      },\n      // Makes vertical dropdowns have a scrollbar once they become taller than the viewport.\n      '&-menu-vertical': {\n        maxHeight: '100vh',\n        overflowY: 'auto'\n      },\n      [`&-trigger${antCls}-btn`]: {\n        [`& > ${iconCls}-down, & > ${antCls}-btn-icon > ${iconCls}-down`]: {\n          fontSize: fontSizeIcon\n        }\n      },\n      [`${componentCls}-wrap`]: {\n        position: 'relative',\n        [`${antCls}-btn > ${iconCls}-down`]: {\n          fontSize: fontSizeIcon\n        },\n        [`${iconCls}-down::before`]: {\n          transition: `transform ${motionDurationMid}`\n        }\n      },\n      [`${componentCls}-wrap-open`]: {\n        [`${iconCls}-down::before`]: {\n          transform: `rotate(180deg)`\n        }\n      },\n      [`\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      `]: {\n        display: 'none'\n      },\n      // =============================================================\n      // ==                         Motion                          ==\n      // =============================================================\n      // When position is not enough for dropdown, the placement will revert.\n      // We will handle this with revert motion name.\n      [`&${antCls}-slide-down-enter${antCls}-slide-down-enter-active${componentCls}-placement-bottomLeft,\n          &${antCls}-slide-down-appear${antCls}-slide-down-appear-active${componentCls}-placement-bottomLeft,\n          &${antCls}-slide-down-enter${antCls}-slide-down-enter-active${componentCls}-placement-bottom,\n          &${antCls}-slide-down-appear${antCls}-slide-down-appear-active${componentCls}-placement-bottom,\n          &${antCls}-slide-down-enter${antCls}-slide-down-enter-active${componentCls}-placement-bottomRight,\n          &${antCls}-slide-down-appear${antCls}-slide-down-appear-active${componentCls}-placement-bottomRight`]: {\n        animationName: slideUpIn\n      },\n      [`&${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-placement-topLeft,\n          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-placement-topLeft,\n          &${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-placement-top,\n          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-placement-top,\n          &${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-placement-topRight,\n          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-placement-topRight`]: {\n        animationName: slideDownIn\n      },\n      [`&${antCls}-slide-down-leave${antCls}-slide-down-leave-active${componentCls}-placement-bottomLeft,\n          &${antCls}-slide-down-leave${antCls}-slide-down-leave-active${componentCls}-placement-bottom,\n          &${antCls}-slide-down-leave${antCls}-slide-down-leave-active${componentCls}-placement-bottomRight`]: {\n        animationName: slideUpOut\n      },\n      [`&${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-placement-topLeft,\n          &${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-placement-top,\n          &${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-placement-topRight`]: {\n        animationName: slideDownOut\n      }\n    }\n  },\n  // =============================================================\n  // ==                        Arrow style                      ==\n  // =============================================================\n  getArrowStyle(token, colorBgElevated, {\n    arrowPlacement: {\n      top: true,\n      bottom: true\n    }\n  }), {\n    // =============================================================\n    // ==                          Menu                           ==\n    // =============================================================\n    [`${componentCls} ${menuCls}`]: {\n      position: 'relative',\n      margin: 0\n    },\n    [`${menuCls}-submenu-popup`]: {\n      position: 'absolute',\n      zIndex: zIndexPopup,\n      background: 'transparent',\n      boxShadow: 'none',\n      transformOrigin: '0 0',\n      'ul, li': {\n        listStyle: 'none',\n        margin: 0\n      }\n    },\n    [`${componentCls}, ${componentCls}-menu-submenu`]: Object.assign(Object.assign({}, resetComponent(token)), {\n      [menuCls]: Object.assign(Object.assign({\n        padding: dropdownEdgeChildPadding,\n        listStyleType: 'none',\n        backgroundColor: colorBgElevated,\n        backgroundClip: 'padding-box',\n        borderRadius: token.borderRadiusLG,\n        outline: 'none',\n        boxShadow: token.boxShadowSecondary\n      }, genFocusStyle(token)), {\n        '&:empty': {\n          padding: 0,\n          boxShadow: 'none'\n        },\n        [`${menuCls}-item-group-title`]: {\n          padding: `${unit(paddingBlock)} ${unit(controlPaddingHorizontal)}`,\n          color: token.colorTextDescription,\n          transition: `all ${motionDurationMid}`\n        },\n        // ======================= Item Content =======================\n        [`${menuCls}-item`]: {\n          position: 'relative',\n          display: 'flex',\n          alignItems: 'center'\n        },\n        [`${menuCls}-item-icon`]: {\n          minWidth: fontSize,\n          marginInlineEnd: token.marginXS,\n          fontSize: token.fontSizeSM\n        },\n        [`${menuCls}-title-content`]: {\n          flex: 'auto',\n          '&-with-extra': {\n            display: 'inline-flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          '> a': {\n            color: 'inherit',\n            transition: `all ${motionDurationMid}`,\n            '&:hover': {\n              color: 'inherit'\n            },\n            '&::after': {\n              position: 'absolute',\n              inset: 0,\n              content: '\"\"'\n            }\n          },\n          [`${menuCls}-item-extra`]: {\n            paddingInlineStart: token.padding,\n            marginInlineStart: 'auto',\n            fontSize: token.fontSizeSM,\n            color: token.colorTextDescription\n          }\n        },\n        // =========================== Item ===========================\n        [`${menuCls}-item, ${menuCls}-submenu-title`]: Object.assign(Object.assign({\n          display: 'flex',\n          margin: 0,\n          padding: `${unit(paddingBlock)} ${unit(controlPaddingHorizontal)}`,\n          color: token.colorText,\n          fontWeight: 'normal',\n          fontSize,\n          lineHeight: token.lineHeight,\n          cursor: 'pointer',\n          transition: `all ${motionDurationMid}`,\n          borderRadius: token.borderRadiusSM,\n          '&:hover, &-active': {\n            backgroundColor: token.controlItemBgHover\n          }\n        }, genFocusStyle(token)), {\n          '&-selected': {\n            color: token.colorPrimary,\n            backgroundColor: token.controlItemBgActive,\n            '&:hover, &-active': {\n              backgroundColor: token.controlItemBgActiveHover\n            }\n          },\n          '&-disabled': {\n            color: colorTextDisabled,\n            cursor: 'not-allowed',\n            '&:hover': {\n              color: colorTextDisabled,\n              backgroundColor: colorBgElevated,\n              cursor: 'not-allowed'\n            },\n            a: {\n              pointerEvents: 'none'\n            }\n          },\n          '&-divider': {\n            height: 1,\n            // By design\n            margin: `${unit(token.marginXXS)} 0`,\n            overflow: 'hidden',\n            lineHeight: 0,\n            backgroundColor: token.colorSplit\n          },\n          [`${componentCls}-menu-submenu-expand-icon`]: {\n            position: 'absolute',\n            insetInlineEnd: token.paddingXS,\n            [`${componentCls}-menu-submenu-arrow-icon`]: {\n              marginInlineEnd: '0 !important',\n              color: token.colorIcon,\n              fontSize: fontSizeIcon,\n              fontStyle: 'normal'\n            }\n          }\n        }),\n        [`${menuCls}-item-group-list`]: {\n          margin: `0 ${unit(token.marginXS)}`,\n          padding: 0,\n          listStyle: 'none'\n        },\n        [`${menuCls}-submenu-title`]: {\n          paddingInlineEnd: token.calc(controlPaddingHorizontal).add(token.fontSizeSM).equal()\n        },\n        [`${menuCls}-submenu-vertical`]: {\n          position: 'relative'\n        },\n        [`${menuCls}-submenu${menuCls}-submenu-disabled ${componentCls}-menu-submenu-title`]: {\n          [`&, ${componentCls}-menu-submenu-arrow-icon`]: {\n            color: colorTextDisabled,\n            backgroundColor: colorBgElevated,\n            cursor: 'not-allowed'\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/19264\n        [`${menuCls}-submenu-selected ${componentCls}-menu-submenu-title`]: {\n          color: token.colorPrimary\n        }\n      })\n    })\n  },\n  // Follow code may reuse in other components\n  [initSlideMotion(token, 'slide-up'), initSlideMotion(token, 'slide-down'), initMoveMotion(token, 'move-up'), initMoveMotion(token, 'move-down'), initZoomMotion(token, 'zoom-big')]];\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => Object.assign(Object.assign({\n  zIndexPopup: token.zIndexPopupBase + 50,\n  paddingBlock: (token.controlHeight - token.fontSize * token.lineHeight) / 2\n}, getArrowOffsetToken({\n  contentRadius: token.borderRadiusLG,\n  limitVerticalRadius: true\n})), getArrowToken(token));\nexport default genStyleHooks('Dropdown', token => {\n  const {\n    marginXXS,\n    sizePopupArrow,\n    paddingXXS,\n    componentCls\n  } = token;\n  const dropdownToken = mergeToken(token, {\n    menuCls: `${componentCls}-menu`,\n    dropdownArrowDistance: token.calc(sizePopupArrow).div(2).add(marginXXS).equal(),\n    dropdownEdgeChildPadding: paddingXXS\n  });\n  return [genBaseStyle(dropdownToken), genStatusStyle(dropdownToken)];\n}, prepareComponentToken, {\n  resetStyle: false\n});"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,aAAa,EAAEC,cAAc,QAAQ,aAAa;AAC3D,SAASC,cAAc,EAAEC,eAAe,EAAEC,cAAc,EAAEC,WAAW,EAAEC,YAAY,EAAEC,SAAS,EAAEC,UAAU,QAAQ,oBAAoB;AACtI,OAAOC,aAAa,IAAIC,mBAAmB,QAAQ,4BAA4B;AAC/E,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,OAAOC,cAAc,MAAM,UAAU;AACrC;AACA,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,OAAO;IACPC,WAAW;IACXC,qBAAqB;IACrBC,cAAc;IACdC,MAAM;IACNC,OAAO;IACPC,iBAAiB;IACjBC,YAAY;IACZC,QAAQ;IACRC,wBAAwB;IACxBC,iBAAiB;IACjBC,YAAY;IACZC,wBAAwB;IACxBC;EACF,CAAC,GAAGf,KAAK;EACT,OAAO,CAAC;IACN,CAACC,YAAY,GAAG;MACde,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC,IAAI;MACVC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE,CAAC;MACV,CAAC;MACDC,MAAM,EAAElB,WAAW;MACnBmB,OAAO,EAAE,OAAO;MAChB;MACA,WAAW,EAAE;QACXN,QAAQ,EAAE,UAAU;QACpBO,UAAU,EAAEvB,KAAK,CAACwB,IAAI,CAACnB,cAAc,CAAC,CAACoB,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAACtB,qBAAqB,CAAC,CAACuB,KAAK,CAAC,CAAC;QAChF;QACAN,MAAM,EAAE,CAAC,IAAI;QACbO,OAAO,EAAE,MAAM;QACfC,OAAO,EAAE;MACX,CAAC;MACD;MACA,iBAAiB,EAAE;QACjBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE;MACb,CAAC;MACD,aAAAC,MAAA,CAAa1B,MAAM,YAAS;QAC1B,QAAA0B,MAAA,CAAQzB,OAAO,iBAAAyB,MAAA,CAAc1B,MAAM,kBAAA0B,MAAA,CAAezB,OAAO,aAAU;UACjEG,QAAQ,EAAEG;QACZ;MACF,CAAC;MACD,IAAAmB,MAAA,CAAI/B,YAAY,aAAU;QACxBe,QAAQ,EAAE,UAAU;QACpB,IAAAgB,MAAA,CAAI1B,MAAM,aAAA0B,MAAA,CAAUzB,OAAO,aAAU;UACnCG,QAAQ,EAAEG;QACZ,CAAC;QACD,IAAAmB,MAAA,CAAIzB,OAAO,qBAAkB;UAC3B0B,UAAU,eAAAD,MAAA,CAAexB,iBAAiB;QAC5C;MACF,CAAC;MACD,IAAAwB,MAAA,CAAI/B,YAAY,kBAAe;QAC7B,IAAA+B,MAAA,CAAIzB,OAAO,qBAAkB;UAC3B2B,SAAS;QACX;MACF,CAAC;MACD,wFAII;QACFZ,OAAO,EAAE;MACX,CAAC;MACD;MACA;MACA;MACA;MACA;MACA,KAAAU,MAAA,CAAK1B,MAAM,uBAAA0B,MAAA,CAAoB1B,MAAM,8BAAA0B,MAAA,CAA2B/B,YAAY,yCAAA+B,MAAA,CACrE1B,MAAM,wBAAA0B,MAAA,CAAqB1B,MAAM,+BAAA0B,MAAA,CAA4B/B,YAAY,yCAAA+B,MAAA,CACzE1B,MAAM,uBAAA0B,MAAA,CAAoB1B,MAAM,8BAAA0B,MAAA,CAA2B/B,YAAY,qCAAA+B,MAAA,CACvE1B,MAAM,wBAAA0B,MAAA,CAAqB1B,MAAM,+BAAA0B,MAAA,CAA4B/B,YAAY,qCAAA+B,MAAA,CACzE1B,MAAM,uBAAA0B,MAAA,CAAoB1B,MAAM,8BAAA0B,MAAA,CAA2B/B,YAAY,0CAAA+B,MAAA,CACvE1B,MAAM,wBAAA0B,MAAA,CAAqB1B,MAAM,+BAAA0B,MAAA,CAA4B/B,YAAY,8BAA2B;QACzGkC,aAAa,EAAE5C;MACjB,CAAC;MACD,KAAAyC,MAAA,CAAK1B,MAAM,qBAAA0B,MAAA,CAAkB1B,MAAM,4BAAA0B,MAAA,CAAyB/B,YAAY,sCAAA+B,MAAA,CACjE1B,MAAM,sBAAA0B,MAAA,CAAmB1B,MAAM,6BAAA0B,MAAA,CAA0B/B,YAAY,sCAAA+B,MAAA,CACrE1B,MAAM,qBAAA0B,MAAA,CAAkB1B,MAAM,4BAAA0B,MAAA,CAAyB/B,YAAY,kCAAA+B,MAAA,CACnE1B,MAAM,sBAAA0B,MAAA,CAAmB1B,MAAM,6BAAA0B,MAAA,CAA0B/B,YAAY,kCAAA+B,MAAA,CACrE1B,MAAM,qBAAA0B,MAAA,CAAkB1B,MAAM,4BAAA0B,MAAA,CAAyB/B,YAAY,uCAAA+B,MAAA,CACnE1B,MAAM,sBAAA0B,MAAA,CAAmB1B,MAAM,6BAAA0B,MAAA,CAA0B/B,YAAY,2BAAwB;QAClGkC,aAAa,EAAE9C;MACjB,CAAC;MACD,KAAA2C,MAAA,CAAK1B,MAAM,uBAAA0B,MAAA,CAAoB1B,MAAM,8BAAA0B,MAAA,CAA2B/B,YAAY,yCAAA+B,MAAA,CACrE1B,MAAM,uBAAA0B,MAAA,CAAoB1B,MAAM,8BAAA0B,MAAA,CAA2B/B,YAAY,qCAAA+B,MAAA,CACvE1B,MAAM,uBAAA0B,MAAA,CAAoB1B,MAAM,8BAAA0B,MAAA,CAA2B/B,YAAY,8BAA2B;QACvGkC,aAAa,EAAE3C;MACjB,CAAC;MACD,KAAAwC,MAAA,CAAK1B,MAAM,qBAAA0B,MAAA,CAAkB1B,MAAM,4BAAA0B,MAAA,CAAyB/B,YAAY,sCAAA+B,MAAA,CACjE1B,MAAM,qBAAA0B,MAAA,CAAkB1B,MAAM,4BAAA0B,MAAA,CAAyB/B,YAAY,kCAAA+B,MAAA,CACnE1B,MAAM,qBAAA0B,MAAA,CAAkB1B,MAAM,4BAAA0B,MAAA,CAAyB/B,YAAY,2BAAwB;QAChGkC,aAAa,EAAE7C;MACjB;IACF;EACF,CAAC;EACD;EACA;EACA;EACAG,aAAa,CAACO,KAAK,EAAEe,eAAe,EAAE;IACpCqB,cAAc,EAAE;MACdnB,GAAG,EAAE,IAAI;MACToB,MAAM,EAAE;IACV;EACF,CAAC,CAAC,EAAE;IACF;IACA;IACA;IACA,IAAAL,MAAA,CAAI/B,YAAY,OAAA+B,MAAA,CAAI9B,OAAO,IAAK;MAC9Bc,QAAQ,EAAE,UAAU;MACpBsB,MAAM,EAAE;IACV,CAAC;IACD,IAAAN,MAAA,CAAI9B,OAAO,sBAAmB;MAC5Bc,QAAQ,EAAE,UAAU;MACpBK,MAAM,EAAElB,WAAW;MACnBoC,UAAU,EAAE,aAAa;MACzBC,SAAS,EAAE,MAAM;MACjBC,eAAe,EAAE,KAAK;MACtB,QAAQ,EAAE;QACRC,SAAS,EAAE,MAAM;QACjBJ,MAAM,EAAE;MACV;IACF,CAAC;IACD,IAAAN,MAAA,CAAI/B,YAAY,QAAA+B,MAAA,CAAK/B,YAAY,qBAAkB0C,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3D,cAAc,CAACe,KAAK,CAAC,CAAC,EAAE;MACzG,CAACE,OAAO,GAAGyC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QACrCC,OAAO,EAAElC,wBAAwB;QACjCmC,aAAa,EAAE,MAAM;QACrBC,eAAe,EAAEhC,eAAe;QAChCiC,cAAc,EAAE,aAAa;QAC7BC,YAAY,EAAEjD,KAAK,CAACkD,cAAc;QAClCC,OAAO,EAAE,MAAM;QACfX,SAAS,EAAExC,KAAK,CAACoD;MACnB,CAAC,EAAEpE,aAAa,CAACgB,KAAK,CAAC,CAAC,EAAE;QACxB,SAAS,EAAE;UACT6C,OAAO,EAAE,CAAC;UACVL,SAAS,EAAE;QACb,CAAC;QACD,IAAAR,MAAA,CAAI9B,OAAO,yBAAsB;UAC/B2C,OAAO,KAAAb,MAAA,CAAKjD,IAAI,CAAC0B,YAAY,CAAC,OAAAuB,MAAA,CAAIjD,IAAI,CAAC+B,wBAAwB,CAAC,CAAE;UAClEuC,KAAK,EAAErD,KAAK,CAACsD,oBAAoB;UACjCrB,UAAU,SAAAD,MAAA,CAASxB,iBAAiB;QACtC,CAAC;QACD;QACA,IAAAwB,MAAA,CAAI9B,OAAO,aAAU;UACnBc,QAAQ,EAAE,UAAU;UACpBM,OAAO,EAAE,MAAM;UACfiC,UAAU,EAAE;QACd,CAAC;QACD,IAAAvB,MAAA,CAAI9B,OAAO,kBAAe;UACxBsD,QAAQ,EAAE9C,QAAQ;UAClB+C,eAAe,EAAEzD,KAAK,CAAC0D,QAAQ;UAC/BhD,QAAQ,EAAEV,KAAK,CAAC2D;QAClB,CAAC;QACD,IAAA3B,MAAA,CAAI9B,OAAO,sBAAmB;UAC5B0D,IAAI,EAAE,MAAM;UACZ,cAAc,EAAE;YACdtC,OAAO,EAAE,aAAa;YACtBiC,UAAU,EAAE,QAAQ;YACpBM,KAAK,EAAE;UACT,CAAC;UACD,KAAK,EAAE;YACLR,KAAK,EAAE,SAAS;YAChBpB,UAAU,SAAAD,MAAA,CAASxB,iBAAiB,CAAE;YACtC,SAAS,EAAE;cACT6C,KAAK,EAAE;YACT,CAAC;YACD,UAAU,EAAE;cACVrC,QAAQ,EAAE,UAAU;cACpB8C,KAAK,EAAE,CAAC;cACRjC,OAAO,EAAE;YACX;UACF,CAAC;UACD,IAAAG,MAAA,CAAI9B,OAAO,mBAAgB;YACzB6D,kBAAkB,EAAE/D,KAAK,CAAC6C,OAAO;YACjCmB,iBAAiB,EAAE,MAAM;YACzBtD,QAAQ,EAAEV,KAAK,CAAC2D,UAAU;YAC1BN,KAAK,EAAErD,KAAK,CAACsD;UACf;QACF,CAAC;QACD;QACA,IAAAtB,MAAA,CAAI9B,OAAO,aAAA8B,MAAA,CAAU9B,OAAO,sBAAmByC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;UACzEtB,OAAO,EAAE,MAAM;UACfgB,MAAM,EAAE,CAAC;UACTO,OAAO,KAAAb,MAAA,CAAKjD,IAAI,CAAC0B,YAAY,CAAC,OAAAuB,MAAA,CAAIjD,IAAI,CAAC+B,wBAAwB,CAAC,CAAE;UAClEuC,KAAK,EAAErD,KAAK,CAACiE,SAAS;UACtBC,UAAU,EAAE,QAAQ;UACpBxD,QAAQ;UACRyD,UAAU,EAAEnE,KAAK,CAACmE,UAAU;UAC5BC,MAAM,EAAE,SAAS;UACjBnC,UAAU,SAAAD,MAAA,CAASxB,iBAAiB,CAAE;UACtCyC,YAAY,EAAEjD,KAAK,CAACqE,cAAc;UAClC,mBAAmB,EAAE;YACnBtB,eAAe,EAAE/C,KAAK,CAACsE;UACzB;QACF,CAAC,EAAEtF,aAAa,CAACgB,KAAK,CAAC,CAAC,EAAE;UACxB,YAAY,EAAE;YACZqD,KAAK,EAAErD,KAAK,CAACuE,YAAY;YACzBxB,eAAe,EAAE/C,KAAK,CAACwE,mBAAmB;YAC1C,mBAAmB,EAAE;cACnBzB,eAAe,EAAE/C,KAAK,CAACyE;YACzB;UACF,CAAC;UACD,YAAY,EAAE;YACZpB,KAAK,EAAEzC,iBAAiB;YACxBwD,MAAM,EAAE,aAAa;YACrB,SAAS,EAAE;cACTf,KAAK,EAAEzC,iBAAiB;cACxBmC,eAAe,EAAEhC,eAAe;cAChCqD,MAAM,EAAE;YACV,CAAC;YACDM,CAAC,EAAE;cACDC,aAAa,EAAE;YACjB;UACF,CAAC;UACD,WAAW,EAAE;YACXC,MAAM,EAAE,CAAC;YACT;YACAtC,MAAM,KAAAN,MAAA,CAAKjD,IAAI,CAACiB,KAAK,CAAC6E,SAAS,CAAC,OAAI;YACpCC,QAAQ,EAAE,QAAQ;YAClBX,UAAU,EAAE,CAAC;YACbpB,eAAe,EAAE/C,KAAK,CAAC+E;UACzB,CAAC;UACD,IAAA/C,MAAA,CAAI/B,YAAY,iCAA8B;YAC5Ce,QAAQ,EAAE,UAAU;YACpBgE,cAAc,EAAEhF,KAAK,CAACiF,SAAS;YAC/B,IAAAjD,MAAA,CAAI/B,YAAY,gCAA6B;cAC3CwD,eAAe,EAAE,cAAc;cAC/BJ,KAAK,EAAErD,KAAK,CAACkF,SAAS;cACtBxE,QAAQ,EAAEG,YAAY;cACtBsE,SAAS,EAAE;YACb;UACF;QACF,CAAC,CAAC;QACF,IAAAnD,MAAA,CAAI9B,OAAO,wBAAqB;UAC9BoC,MAAM,OAAAN,MAAA,CAAOjD,IAAI,CAACiB,KAAK,CAAC0D,QAAQ,CAAC,CAAE;UACnCb,OAAO,EAAE,CAAC;UACVH,SAAS,EAAE;QACb,CAAC;QACD,IAAAV,MAAA,CAAI9B,OAAO,sBAAmB;UAC5BkF,gBAAgB,EAAEpF,KAAK,CAACwB,IAAI,CAACV,wBAAwB,CAAC,CAACuE,GAAG,CAACrF,KAAK,CAAC2D,UAAU,CAAC,CAAChC,KAAK,CAAC;QACrF,CAAC;QACD,IAAAK,MAAA,CAAI9B,OAAO,yBAAsB;UAC/Bc,QAAQ,EAAE;QACZ,CAAC;QACD,IAAAgB,MAAA,CAAI9B,OAAO,cAAA8B,MAAA,CAAW9B,OAAO,wBAAA8B,MAAA,CAAqB/B,YAAY,2BAAwB;UACpF,OAAA+B,MAAA,CAAO/B,YAAY,gCAA6B;YAC9CoD,KAAK,EAAEzC,iBAAiB;YACxBmC,eAAe,EAAEhC,eAAe;YAChCqD,MAAM,EAAE;UACV;QACF,CAAC;QACD;QACA,IAAApC,MAAA,CAAI9B,OAAO,wBAAA8B,MAAA,CAAqB/B,YAAY,2BAAwB;UAClEoD,KAAK,EAAErD,KAAK,CAACuE;QACf;MACF,CAAC;IACH,CAAC;EACH,CAAC;EACD;EACA,CAACpF,eAAe,CAACa,KAAK,EAAE,UAAU,CAAC,EAAEb,eAAe,CAACa,KAAK,EAAE,YAAY,CAAC,EAAEd,cAAc,CAACc,KAAK,EAAE,SAAS,CAAC,EAAEd,cAAc,CAACc,KAAK,EAAE,WAAW,CAAC,EAAEZ,cAAc,CAACY,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;AACtL,CAAC;AACD;AACA,OAAO,MAAMsF,qBAAqB,GAAGtF,KAAK,IAAI2C,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;EACxEzC,WAAW,EAAEH,KAAK,CAACuF,eAAe,GAAG,EAAE;EACvC9E,YAAY,EAAE,CAACT,KAAK,CAACwF,aAAa,GAAGxF,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACmE,UAAU,IAAI;AAC5E,CAAC,EAAEzE,mBAAmB,CAAC;EACrB+F,aAAa,EAAEzF,KAAK,CAACkD,cAAc;EACnCwC,mBAAmB,EAAE;AACvB,CAAC,CAAC,CAAC,EAAE/F,aAAa,CAACK,KAAK,CAAC,CAAC;AAC1B,eAAeJ,aAAa,CAAC,UAAU,EAAEI,KAAK,IAAI;EAChD,MAAM;IACJ6E,SAAS;IACTxE,cAAc;IACdsF,UAAU;IACV1F;EACF,CAAC,GAAGD,KAAK;EACT,MAAM4F,aAAa,GAAG/F,UAAU,CAACG,KAAK,EAAE;IACtCE,OAAO,KAAA8B,MAAA,CAAK/B,YAAY,UAAO;IAC/BG,qBAAqB,EAAEJ,KAAK,CAACwB,IAAI,CAACnB,cAAc,CAAC,CAACoB,GAAG,CAAC,CAAC,CAAC,CAAC4D,GAAG,CAACR,SAAS,CAAC,CAAClD,KAAK,CAAC,CAAC;IAC/EhB,wBAAwB,EAAEgF;EAC5B,CAAC,CAAC;EACF,OAAO,CAAC5F,YAAY,CAAC6F,aAAa,CAAC,EAAE9F,cAAc,CAAC8F,aAAa,CAAC,CAAC;AACrE,CAAC,EAAEN,qBAAqB,EAAE;EACxBO,UAAU,EAAE;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
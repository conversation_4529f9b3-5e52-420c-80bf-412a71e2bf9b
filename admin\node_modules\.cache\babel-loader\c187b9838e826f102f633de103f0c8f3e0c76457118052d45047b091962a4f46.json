{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst Element = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    size,\n    shape\n  } = props;\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const shapeCls = classNames({\n    [`${prefixCls}-circle`]: shape === 'circle',\n    [`${prefixCls}-square`]: shape === 'square',\n    [`${prefixCls}-round`]: shape === 'round'\n  });\n  const sizeStyle = React.useMemo(() => typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: `${size}px`\n  } : {}, [size]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: Object.assign(Object.assign({}, sizeStyle), style)\n  });\n};\nexport default Element;", "map": {"version": 3, "names": ["React", "classNames", "Element", "props", "prefixCls", "className", "style", "size", "shape", "sizeCls", "shapeCls", "sizeStyle", "useMemo", "width", "height", "lineHeight", "createElement", "Object", "assign"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/skeleton/Element.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst Element = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    size,\n    shape\n  } = props;\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const shapeCls = classNames({\n    [`${prefixCls}-circle`]: shape === 'circle',\n    [`${prefixCls}-square`]: shape === 'square',\n    [`${prefixCls}-round`]: shape === 'round'\n  });\n  const sizeStyle = React.useMemo(() => typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: `${size}px`\n  } : {}, [size]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: Object.assign(Object.assign({}, sizeStyle), style)\n  });\n};\nexport default Element;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,OAAO,GAAGC,KAAK,IAAI;EACvB,MAAM;IACJC,SAAS;IACTC,SAAS;IACTC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,OAAO,GAAGR,UAAU,CAAC;IACzB,CAAC,GAAGG,SAAS,KAAK,GAAGG,IAAI,KAAK,OAAO;IACrC,CAAC,GAAGH,SAAS,KAAK,GAAGG,IAAI,KAAK;EAChC,CAAC,CAAC;EACF,MAAMG,QAAQ,GAAGT,UAAU,CAAC;IAC1B,CAAC,GAAGG,SAAS,SAAS,GAAGI,KAAK,KAAK,QAAQ;IAC3C,CAAC,GAAGJ,SAAS,SAAS,GAAGI,KAAK,KAAK,QAAQ;IAC3C,CAAC,GAAGJ,SAAS,QAAQ,GAAGI,KAAK,KAAK;EACpC,CAAC,CAAC;EACF,MAAMG,SAAS,GAAGX,KAAK,CAACY,OAAO,CAAC,MAAM,OAAOL,IAAI,KAAK,QAAQ,GAAG;IAC/DM,KAAK,EAAEN,IAAI;IACXO,MAAM,EAAEP,IAAI;IACZQ,UAAU,EAAE,GAAGR,IAAI;EACrB,CAAC,GAAG,CAAC,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACf,OAAO,aAAaP,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAC9CX,SAAS,EAAEJ,UAAU,CAACG,SAAS,EAAEK,OAAO,EAAEC,QAAQ,EAAEL,SAAS,CAAC;IAC9DC,KAAK,EAAEW,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,SAAS,CAAC,EAAEL,KAAK;EAC1D,CAAC,CAAC;AACJ,CAAC;AACD,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { Children, useContext } from 'react';\nimport classNames from 'classnames';\nimport { cloneElement } from '../_util/reactNode';\nimport { ConfigContext } from '../config-provider';\nimport { Col } from '../grid';\nimport { ListContext } from './context';\nexport const Meta = _a => {\n  var {\n      prefixCls: customizePrefixCls,\n      className,\n      avatar,\n      title,\n      description\n    } = _a,\n    others = __rest(_a, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('list', customizePrefixCls);\n  const classString = classNames(\"\".concat(prefixCls, \"-item-meta\"), className);\n  const content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-content\")\n  }, title && /*#__PURE__*/React.createElement(\"h4\", {\n    className: \"\".concat(prefixCls, \"-item-meta-title\")\n  }, title), description && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-description\")\n  }, description));\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, others, {\n    className: classString\n  }), avatar && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-meta-avatar\")\n  }, avatar), (title || description) && content);\n};\nconst InternalItem = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      children,\n      actions,\n      extra,\n      styles,\n      className,\n      classNames: customizeClassNames,\n      colStyle\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"children\", \"actions\", \"extra\", \"styles\", \"className\", \"classNames\", \"colStyle\"]);\n  const {\n    grid,\n    itemLayout\n  } = useContext(ListContext);\n  const {\n    getPrefixCls,\n    list\n  } = useContext(ConfigContext);\n  const moduleClass = moduleName => {\n    var _a, _b;\n    return classNames((_b = (_a = list === null || list === void 0 ? void 0 : list.item) === null || _a === void 0 ? void 0 : _a.classNames) === null || _b === void 0 ? void 0 : _b[moduleName], customizeClassNames === null || customizeClassNames === void 0 ? void 0 : customizeClassNames[moduleName]);\n  };\n  const moduleStyle = moduleName => {\n    var _a, _b;\n    return Object.assign(Object.assign({}, (_b = (_a = list === null || list === void 0 ? void 0 : list.item) === null || _a === void 0 ? void 0 : _a.styles) === null || _b === void 0 ? void 0 : _b[moduleName]), styles === null || styles === void 0 ? void 0 : styles[moduleName]);\n  };\n  const isItemContainsTextNodeAndNotSingular = () => {\n    let result = false;\n    Children.forEach(children, element => {\n      if (typeof element === 'string') {\n        result = true;\n      }\n    });\n    return result && Children.count(children) > 1;\n  };\n  const isFlexMode = () => {\n    if (itemLayout === 'vertical') {\n      return !!extra;\n    }\n    return !isItemContainsTextNodeAndNotSingular();\n  };\n  const prefixCls = getPrefixCls('list', customizePrefixCls);\n  const actionsContent = actions && actions.length > 0 && (/*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(\"\".concat(prefixCls, \"-item-action\"), moduleClass('actions')),\n    key: \"actions\",\n    style: moduleStyle('actions')\n  }, actions.map((action, i) => (/*#__PURE__*/\n  // eslint-disable-next-line react/no-array-index-key\n  React.createElement(\"li\", {\n    key: \"\".concat(prefixCls, \"-item-action-\").concat(i)\n  }, action, i !== actions.length - 1 && /*#__PURE__*/React.createElement(\"em\", {\n    className: \"\".concat(prefixCls, \"-item-action-split\")\n  }))))));\n  const Element = grid ? 'div' : 'li';\n  const itemChildren = /*#__PURE__*/React.createElement(Element, Object.assign({}, others, !grid ? {\n    ref\n  } : {}, {\n    className: classNames(\"\".concat(prefixCls, \"-item\"), {\n      [\"\".concat(prefixCls, \"-item-no-flex\")]: !isFlexMode()\n    }, className)\n  }), itemLayout === 'vertical' && extra ? [/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-main\"),\n    key: \"content\"\n  }, children, actionsContent), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-item-extra\"), moduleClass('extra')),\n    key: \"extra\",\n    style: moduleStyle('extra')\n  }, extra)] : [children, actionsContent, cloneElement(extra, {\n    key: 'extra'\n  })]);\n  return grid ? (/*#__PURE__*/React.createElement(Col, {\n    ref: ref,\n    flex: 1,\n    style: colStyle\n  }, itemChildren)) : itemChildren;\n});\nconst Item = InternalItem;\nItem.Meta = Meta;\nexport default Item;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
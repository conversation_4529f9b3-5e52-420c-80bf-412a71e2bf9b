# 🚀 修正版部署指南 - 实际服务器路径

## 📋 **实际服务器路径结构**

根据您提供的信息，实际的服务器路径结构是：

```
/www/wwwroot/h5.haokajiyun.com/
├── api/
│   └── public/          # ← API运行目录 (Lara<PERSON>)
│       ├── index.php
│       ├── .htaccess
│       └── ...
└── admin/               # ← 后台管理前端目录 (需要创建)
    ├── index.html
    ├── static/
    └── ...
```

## 🎯 **部署目标**

- **API服务**: `/www/wwwroot/h5.haokajiyun.com/api/public` (已部署)
- **后台管理**: `/www/wwwroot/h5.haokajiyun.com/admin` (待部署)
- **访问地址**: https://h5.haokajiyun.com/admin
- **API地址**: https://h5.haokajiyun.com/api

## 📦 **第一步: 上传前端文件**

### **1. 创建admin目录**
```bash
mkdir -p /www/wwwroot/h5.haokajiyun.com/admin
```

### **2. 上传文件**
将当前部署包中的以下文件上传到 `/www/wwwroot/h5.haokajiyun.com/admin/`:

```
admin/
├── index.html
├── static/
│   ├── css/
│   │   └── main.1c68141c.css
│   └── js/
│       └── main.990c5350.js
├── asset-manifest.json
└── 其他测试文件...
```

### **3. 设置权限**
```bash
# 设置目录权限
chmod 755 /www/wwwroot/h5.haokajiyun.com/admin

# 设置文件权限
find /www/wwwroot/h5.haokajiyun.com/admin -type f -exec chmod 644 {} \;
find /www/wwwroot/h5.haokajiyun.com/admin -type d -exec chmod 755 {} \;

# 设置所有者
chown -R www:www /www/wwwroot/h5.haokajiyun.com/admin
```

## ⚙️ **第二步: 配置Nginx (修正版)**

### **使用修正版配置**
使用 `nginx-corrected.conf` 文件中的配置，关键修正点：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name h5.haokajiyun.com;
    
    # API服务器根目录 (修正后的路径)
    root /www/wwwroot/h5.haokajiyun.com/api/public;
    index index.php index.html;
    
    # API路由配置
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept";
            add_header Access-Control-Max-Age 86400;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
    
    # Admin后台管理系统
    location /admin/ {
        alias /www/wwwroot/h5.haokajiyun.com/admin/;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
        
        # 静态资源缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # HTML文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # PHP处理
    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass unix:/tmp/php-cgi-83.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### **宝塔面板配置步骤**
1. 登录宝塔面板
2. 进入"网站" → 找到 `h5.haokajiyun.com`
3. 点击"设置" → "配置文件"
4. 替换为 `nginx-corrected.conf` 中的配置
5. 点击"保存"

## 🔍 **第三步: 验证部署**

### **1. 检查文件结构**
```bash
# 检查API目录
ls -la /www/wwwroot/h5.haokajiyun.com/api/public/

# 检查Admin目录
ls -la /www/wwwroot/h5.haokajiyun.com/admin/
```

### **2. 测试访问**
- **后台管理**: https://h5.haokajiyun.com/admin
- **API测试**: https://h5.haokajiyun.com/api/v1/products
- **根目录**: https://h5.haokajiyun.com (应重定向到/admin)

### **3. 功能验证**
- ✅ 页面正常加载
- ✅ 左侧导航正常
- ✅ 产品管理功能
- ✅ 订单管理功能
- ✅ API接口调用

## 🛠️ **故障排除**

### **问题1: API请求404**
```bash
# 检查API目录是否正确
ls -la /www/wwwroot/h5.haokajiyun.com/api/public/index.php

# 检查Nginx配置
nginx -t

# 查看错误日志
tail -f /www/wwwlogs/h5.haokajiyun.com.error.log
```

### **问题2: Admin页面404**
```bash
# 检查admin目录
ls -la /www/wwwroot/h5.haokajiyun.com/admin/index.html

# 检查权限
ls -la /www/wwwroot/h5.haokajiyun.com/admin/
```

### **问题3: CORS错误**
确认Nginx配置中包含CORS头，并重载配置：
```bash
nginx -s reload
```

## 📊 **路径对照表**

| 服务 | 实际路径 | 访问URL |
|------|----------|---------|
| **API服务** | `/www/wwwroot/h5.haokajiyun.com/api/public` | `https://h5.haokajiyun.com/api` |
| **后台管理** | `/www/wwwroot/h5.haokajiyun.com/admin` | `https://h5.haokajiyun.com/admin` |
| **根目录** | 重定向到admin | `https://h5.haokajiyun.com/` |

## 🔧 **重要配置说明**

### **Nginx root指令**
```nginx
# 设置API服务的根目录
root /www/wwwroot/h5.haokajiyun.com/api/public;

# Admin使用alias指令
location /admin/ {
    alias /www/wwwroot/h5.haokajiyun.com/admin/;
}
```

### **Laravel存储文件**
```nginx
# 如果需要访问上传的文件
location /storage/ {
    alias /www/wwwroot/h5.haokajiyun.com/api/public/storage/;
}
```

## ✅ **部署完成检查**

- [ ] API服务正常 (测试: `curl https://h5.haokajiyun.com/api/v1/products`)
- [ ] Admin页面加载 (访问: `https://h5.haokajiyun.com/admin`)
- [ ] 静态资源正常 (CSS/JS文件加载)
- [ ] 功能测试通过 (产品管理、订单管理)
- [ ] 浏览器控制台无错误

## 📞 **技术支持**

### **关键文件**
- **Nginx配置**: `nginx-corrected.conf`
- **API目录**: `/www/wwwroot/h5.haokajiyun.com/api/public`
- **Admin目录**: `/www/wwwroot/h5.haokajiyun.com/admin`
- **错误日志**: `/www/wwwlogs/h5.haokajiyun.com.error.log`

### **测试命令**
```bash
# 测试Nginx配置
nginx -t

# 重载配置
nginx -s reload

# 测试API
curl -X GET https://h5.haokajiyun.com/api/v1/products

# 检查进程
ps aux | grep nginx
ps aux | grep php-fpm
```

---

## 🎉 **部署完成**

按照修正版配置完成部署后，您的系统将在以下地址可用：

**🔗 后台管理**: https://h5.haokajiyun.com/admin  
**🔗 API接口**: https://h5.haokajiyun.com/api  

**🎯 现在可以开始使用后台管理系统了！**

{"ast": null, "code": "import * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useFrameState(defaultValue) {\n  const [value, setValue] = React.useState(defaultValue);\n  const frameRef = React.useRef(null);\n  const batchRef = React.useRef([]);\n  const destroyRef = React.useRef(false);\n  React.useEffect(() => {\n    destroyRef.current = false;\n    return () => {\n      destroyRef.current = true;\n      raf.cancel(frameRef.current);\n      frameRef.current = null;\n    };\n  }, []);\n  function setFrameValue(updater) {\n    if (destroyRef.current) {\n      return;\n    }\n    if (frameRef.current === null) {\n      batchRef.current = [];\n      frameRef.current = raf(() => {\n        frameRef.current = null;\n        setValue(prevValue => {\n          let current = prevValue;\n          batchRef.current.forEach(func => {\n            current = func(current);\n          });\n          return current;\n        });\n      });\n    }\n    batchRef.current.push(updater);\n  }\n  return [value, setFrameValue];\n}", "map": {"version": 3, "names": ["React", "raf", "useFrameState", "defaultValue", "value", "setValue", "useState", "frameRef", "useRef", "batchRef", "destroyRef", "useEffect", "current", "cancel", "setFrameValue", "updater", "prevValue", "for<PERSON>ach", "func", "push"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/form/hooks/useFrameState.js"], "sourcesContent": ["import * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useFrameState(defaultValue) {\n  const [value, setValue] = React.useState(defaultValue);\n  const frameRef = React.useRef(null);\n  const batchRef = React.useRef([]);\n  const destroyRef = React.useRef(false);\n  React.useEffect(() => {\n    destroyRef.current = false;\n    return () => {\n      destroyRef.current = true;\n      raf.cancel(frameRef.current);\n      frameRef.current = null;\n    };\n  }, []);\n  function setFrameValue(updater) {\n    if (destroyRef.current) {\n      return;\n    }\n    if (frameRef.current === null) {\n      batchRef.current = [];\n      frameRef.current = raf(() => {\n        frameRef.current = null;\n        setValue(prevValue => {\n          let current = prevValue;\n          batchRef.current.forEach(func => {\n            current = func(current);\n          });\n          return current;\n        });\n      });\n    }\n    batchRef.current.push(updater);\n  }\n  return [value, setFrameValue];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,gBAAgB;AAChC,eAAe,SAASC,aAAaA,CAACC,YAAY,EAAE;EAClD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGL,KAAK,CAACM,QAAQ,CAACH,YAAY,CAAC;EACtD,MAAMI,QAAQ,GAAGP,KAAK,CAACQ,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,QAAQ,GAAGT,KAAK,CAACQ,MAAM,CAAC,EAAE,CAAC;EACjC,MAAME,UAAU,GAAGV,KAAK,CAACQ,MAAM,CAAC,KAAK,CAAC;EACtCR,KAAK,CAACW,SAAS,CAAC,MAAM;IACpBD,UAAU,CAACE,OAAO,GAAG,KAAK;IAC1B,OAAO,MAAM;MACXF,UAAU,CAACE,OAAO,GAAG,IAAI;MACzBX,GAAG,CAACY,MAAM,CAACN,QAAQ,CAACK,OAAO,CAAC;MAC5BL,QAAQ,CAACK,OAAO,GAAG,IAAI;IACzB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,SAASE,aAAaA,CAACC,OAAO,EAAE;IAC9B,IAAIL,UAAU,CAACE,OAAO,EAAE;MACtB;IACF;IACA,IAAIL,QAAQ,CAACK,OAAO,KAAK,IAAI,EAAE;MAC7BH,QAAQ,CAACG,OAAO,GAAG,EAAE;MACrBL,QAAQ,CAACK,OAAO,GAAGX,GAAG,CAAC,MAAM;QAC3BM,QAAQ,CAACK,OAAO,GAAG,IAAI;QACvBP,QAAQ,CAACW,SAAS,IAAI;UACpB,IAAIJ,OAAO,GAAGI,SAAS;UACvBP,QAAQ,CAACG,OAAO,CAACK,OAAO,CAACC,IAAI,IAAI;YAC/BN,OAAO,GAAGM,IAAI,CAACN,OAAO,CAAC;UACzB,CAAC,CAAC;UACF,OAAOA,OAAO;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACAH,QAAQ,CAACG,OAAO,CAACO,IAAI,CAACJ,OAAO,CAAC;EAChC;EACA,OAAO,CAACX,KAAK,EAAEU,aAAa,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
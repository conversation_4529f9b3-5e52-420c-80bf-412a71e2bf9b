{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"label\", \"children\", \"key\", \"type\", \"extra\"];\nimport * as React from 'react';\nimport Divider from \"../Divider\";\nimport MenuItem from \"../MenuItem\";\nimport MenuItemGroup from \"../MenuItemGroup\";\nimport SubMenu from \"../SubMenu\";\nimport { parseChildren } from \"./commonUtil\";\nfunction convertItemsToNodes(list, components, prefixCls) {\n  var MergedMenuItem = components.item,\n    MergedMenuItemGroup = components.group,\n    MergedSubMenu = components.submenu,\n    MergedDivider = components.divider;\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _ref = opt,\n        label = _ref.label,\n        children = _ref.children,\n        key = _ref.key,\n        type = _ref.type,\n        extra = _ref.extra,\n        restProps = _objectWithoutProperties(_ref, _excluded);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index);\n\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(MergedMenuItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children, components, prefixCls));\n        }\n\n        // Sub Menu\n        return /*#__PURE__*/React.createElement(MergedSubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children, components, prefixCls));\n      }\n\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(MergedDivider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MergedMenuItem, _extends({\n        key: mergedKey\n      }, restProps, {\n        extra: extra\n      }), label, (!!extra || extra === 0) && /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-item-extra\")\n      }, extra));\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\nexport function parseItems(children, items, keyPath, components, prefixCls) {\n  var childNodes = children;\n  var mergedComponents = _objectSpread({\n    divider: Divider,\n    item: MenuItem,\n    group: MenuItemGroup,\n    submenu: SubMenu\n  }, components);\n  if (items) {\n    childNodes = convertItemsToNodes(items, mergedComponents, prefixCls);\n  }\n  return parseChildren(childNodes, keyPath);\n}", "map": {"version": 3, "names": ["_objectSpread", "_extends", "_objectWithoutProperties", "_typeof", "_excluded", "React", "Divider", "MenuItem", "MenuItemGroup", "SubMenu", "parse<PERSON><PERSON><PERSON>n", "convertItemsToNodes", "list", "components", "prefixCls", "MergedMenuItem", "item", "MergedMenuItemGroup", "group", "MergedSubMenu", "submenu", "MergedDivider", "divider", "map", "opt", "index", "_ref", "label", "children", "key", "type", "extra", "restProps", "mergedKey", "concat", "createElement", "title", "className", "filter", "parseItems", "items", "keyP<PERSON>", "childNodes", "mergedComponents"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-menu/es/utils/nodeUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"label\", \"children\", \"key\", \"type\", \"extra\"];\nimport * as React from 'react';\nimport Divider from \"../Divider\";\nimport MenuItem from \"../MenuItem\";\nimport MenuItemGroup from \"../MenuItemGroup\";\nimport SubMenu from \"../SubMenu\";\nimport { parseChildren } from \"./commonUtil\";\nfunction convertItemsToNodes(list, components, prefixCls) {\n  var MergedMenuItem = components.item,\n    MergedMenuItemGroup = components.group,\n    MergedSubMenu = components.submenu,\n    MergedDivider = components.divider;\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _ref = opt,\n        label = _ref.label,\n        children = _ref.children,\n        key = _ref.key,\n        type = _ref.type,\n        extra = _ref.extra,\n        restProps = _objectWithoutProperties(_ref, _excluded);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index);\n\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(MergedMenuItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children, components, prefixCls));\n        }\n\n        // Sub Menu\n        return /*#__PURE__*/React.createElement(MergedSubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children, components, prefixCls));\n      }\n\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(MergedDivider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MergedMenuItem, _extends({\n        key: mergedKey\n      }, restProps, {\n        extra: extra\n      }), label, (!!extra || extra === 0) && /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-item-extra\")\n      }, extra));\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\nexport function parseItems(children, items, keyPath, components, prefixCls) {\n  var childNodes = children;\n  var mergedComponents = _objectSpread({\n    divider: Divider,\n    item: MenuItem,\n    group: MenuItemGroup,\n    submenu: SubMenu\n  }, components);\n  if (items) {\n    childNodes = convertItemsToNodes(items, mergedComponents, prefixCls);\n  }\n  return parseChildren(childNodes, keyPath);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;AAC7D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACxD,IAAIC,cAAc,GAAGF,UAAU,CAACG,IAAI;IAClCC,mBAAmB,GAAGJ,UAAU,CAACK,KAAK;IACtCC,aAAa,GAAGN,UAAU,CAACO,OAAO;IAClCC,aAAa,GAAGR,UAAU,CAACS,OAAO;EACpC,OAAO,CAACV,IAAI,IAAI,EAAE,EAAEW,GAAG,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC5C,IAAID,GAAG,IAAIrB,OAAO,CAACqB,GAAG,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAIE,IAAI,GAAGF,GAAG;QACZG,KAAK,GAAGD,IAAI,CAACC,KAAK;QAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;QACxBC,GAAG,GAAGH,IAAI,CAACG,GAAG;QACdC,IAAI,GAAGJ,IAAI,CAACI,IAAI;QAChBC,KAAK,GAAGL,IAAI,CAACK,KAAK;QAClBC,SAAS,GAAG9B,wBAAwB,CAACwB,IAAI,EAAEtB,SAAS,CAAC;MACvD,IAAI6B,SAAS,GAAGJ,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,MAAM,CAACK,MAAM,CAACT,KAAK,CAAC;;MAE3E;MACA,IAAIG,QAAQ,IAAIE,IAAI,KAAK,OAAO,EAAE;QAChC,IAAIA,IAAI,KAAK,OAAO,EAAE;UACpB;UACA,OAAO,aAAazB,KAAK,CAAC8B,aAAa,CAAClB,mBAAmB,EAAEhB,QAAQ,CAAC;YACpE4B,GAAG,EAAEI;UACP,CAAC,EAAED,SAAS,EAAE;YACZI,KAAK,EAAET;UACT,CAAC,CAAC,EAAEhB,mBAAmB,CAACiB,QAAQ,EAAEf,UAAU,EAAEC,SAAS,CAAC,CAAC;QAC3D;;QAEA;QACA,OAAO,aAAaT,KAAK,CAAC8B,aAAa,CAAChB,aAAa,EAAElB,QAAQ,CAAC;UAC9D4B,GAAG,EAAEI;QACP,CAAC,EAAED,SAAS,EAAE;UACZI,KAAK,EAAET;QACT,CAAC,CAAC,EAAEhB,mBAAmB,CAACiB,QAAQ,EAAEf,UAAU,EAAEC,SAAS,CAAC,CAAC;MAC3D;;MAEA;MACA,IAAIgB,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,aAAazB,KAAK,CAAC8B,aAAa,CAACd,aAAa,EAAEpB,QAAQ,CAAC;UAC9D4B,GAAG,EAAEI;QACP,CAAC,EAAED,SAAS,CAAC,CAAC;MAChB;MACA,OAAO,aAAa3B,KAAK,CAAC8B,aAAa,CAACpB,cAAc,EAAEd,QAAQ,CAAC;QAC/D4B,GAAG,EAAEI;MACP,CAAC,EAAED,SAAS,EAAE;QACZD,KAAK,EAAEA;MACT,CAAC,CAAC,EAAEJ,KAAK,EAAE,CAAC,CAAC,CAACI,KAAK,IAAIA,KAAK,KAAK,CAAC,KAAK,aAAa1B,KAAK,CAAC8B,aAAa,CAAC,MAAM,EAAE;QAC9EE,SAAS,EAAE,EAAE,CAACH,MAAM,CAACpB,SAAS,EAAE,aAAa;MAC/C,CAAC,EAAEiB,KAAK,CAAC,CAAC;IACZ;IACA,OAAO,IAAI;EACb,CAAC,CAAC,CAACO,MAAM,CAAC,UAAUd,GAAG,EAAE;IACvB,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ;AACA,OAAO,SAASe,UAAUA,CAACX,QAAQ,EAAEY,KAAK,EAAEC,OAAO,EAAE5B,UAAU,EAAEC,SAAS,EAAE;EAC1E,IAAI4B,UAAU,GAAGd,QAAQ;EACzB,IAAIe,gBAAgB,GAAG3C,aAAa,CAAC;IACnCsB,OAAO,EAAEhB,OAAO;IAChBU,IAAI,EAAET,QAAQ;IACdW,KAAK,EAAEV,aAAa;IACpBY,OAAO,EAAEX;EACX,CAAC,EAAEI,UAAU,CAAC;EACd,IAAI2B,KAAK,EAAE;IACTE,UAAU,GAAG/B,mBAAmB,CAAC6B,KAAK,EAAEG,gBAAgB,EAAE7B,SAAS,CAAC;EACtE;EACA,OAAOJ,aAAa,CAACgC,UAAU,EAAED,OAAO,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
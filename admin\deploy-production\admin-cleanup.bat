@echo off
chcp 65001 >nul
echo ========================================
echo     Admin前端项目清理工具
echo ========================================
echo.

set "ADMIN_DIR=G:\phpstudy_pro\WWW\admin"

echo 🔍 检查Admin目录: %ADMIN_DIR%
if not exist "%ADMIN_DIR%" (
    echo ❌ Admin目录不存在
    pause
    exit /b 1
)

echo ✅ Admin目录存在
echo.

echo 📋 开始清理多余文件...
echo.

REM 删除文档文件（保留README.md）
echo 🗑️ 清理文档文件...
if exist "%ADMIN_DIR%\BAOTA_DEPLOYMENT.md" (
    del "%ADMIN_DIR%\BAOTA_DEPLOYMENT.md"
    echo   ✅ 删除 BAOTA_DEPLOYMENT.md
)
if exist "%ADMIN_DIR%\BAOTA_QUICK_SETUP.md" (
    del "%ADMIN_DIR%\BAOTA_QUICK_SETUP.md"
    echo   ✅ 删除 BAOTA_QUICK_SETUP.md
)
if exist "%ADMIN_DIR%\BLANK_PAGE_SOLUTION.md" (
    del "%ADMIN_DIR%\BLANK_PAGE_SOLUTION.md"
    echo   ✅ 删除 BLANK_PAGE_SOLUTION.md
)
if exist "%ADMIN_DIR%\BROWSER_TROUBLESHOOTING.md" (
    del "%ADMIN_DIR%\BROWSER_TROUBLESHOOTING.md"
    echo   ✅ 删除 BROWSER_TROUBLESHOOTING.md
)
if exist "%ADMIN_DIR%\BUILD_COMPLETE.md" (
    del "%ADMIN_DIR%\BUILD_COMPLETE.md"
    echo   ✅ 删除 BUILD_COMPLETE.md
)
if exist "%ADMIN_DIR%\BUILD_SUCCESS_REPORT.md" (
    del "%ADMIN_DIR%\BUILD_SUCCESS_REPORT.md"
    echo   ✅ 删除 BUILD_SUCCESS_REPORT.md
)
if exist "%ADMIN_DIR%\BUILD_SUMMARY.md" (
    del "%ADMIN_DIR%\BUILD_SUMMARY.md"
    echo   ✅ 删除 BUILD_SUMMARY.md
)
if exist "%ADMIN_DIR%\DEPLOYMENT.md" (
    del "%ADMIN_DIR%\DEPLOYMENT.md"
    echo   ✅ 删除 DEPLOYMENT.md
)
if exist "%ADMIN_DIR%\DEPLOYMENT_SUCCESS.md" (
    del "%ADMIN_DIR%\DEPLOYMENT_SUCCESS.md"
    echo   ✅ 删除 DEPLOYMENT_SUCCESS.md
)
if exist "%ADMIN_DIR%\DEPLOYMENT_SUMMARY.md" (
    del "%ADMIN_DIR%\DEPLOYMENT_SUMMARY.md"
    echo   ✅ 删除 DEPLOYMENT_SUMMARY.md
)
if exist "%ADMIN_DIR%\ERROR_FIXES.md" (
    del "%ADMIN_DIR%\ERROR_FIXES.md"
    echo   ✅ 删除 ERROR_FIXES.md
)
if exist "%ADMIN_DIR%\SETUP.md" (
    del "%ADMIN_DIR%\SETUP.md"
    echo   ✅ 删除 SETUP.md
)
if exist "%ADMIN_DIR%\SOLUTION_SUMMARY.md" (
    del "%ADMIN_DIR%\SOLUTION_SUMMARY.md"
    echo   ✅ 删除 SOLUTION_SUMMARY.md
)
if exist "%ADMIN_DIR%\TROUBLESHOOTING.md" (
    del "%ADMIN_DIR%\TROUBLESHOOTING.md"
    echo   ✅ 删除 TROUBLESHOOTING.md
)

REM 删除批处理脚本（保留必要的）
echo 🗑️ 清理批处理脚本...
if exist "%ADMIN_DIR%\build-admin.bat" (
    del "%ADMIN_DIR%\build-admin.bat"
    echo   ✅ 删除 build-admin.bat
)
if exist "%ADMIN_DIR%\build-and-verify.bat" (
    del "%ADMIN_DIR%\build-and-verify.bat"
    echo   ✅ 删除 build-and-verify.bat
)
if exist "%ADMIN_DIR%\clean-test.bat" (
    del "%ADMIN_DIR%\clean-test.bat"
    echo   ✅ 删除 clean-test.bat
)
if exist "%ADMIN_DIR%\complete-build.bat" (
    del "%ADMIN_DIR%\complete-build.bat"
    echo   ✅ 删除 complete-build.bat
)
if exist "%ADMIN_DIR%\deploy.bat" (
    del "%ADMIN_DIR%\deploy.bat"
    echo   ✅ 删除 deploy.bat
)
if exist "%ADMIN_DIR%\deploy-production.bat" (
    del "%ADMIN_DIR%\deploy-production.bat"
    echo   ✅ 删除 deploy-production.bat
)
if exist "%ADMIN_DIR%\fix-blank-page.bat" (
    del "%ADMIN_DIR%\fix-blank-page.bat"
    echo   ✅ 删除 fix-blank-page.bat
)
if exist "%ADMIN_DIR%\fix-types.bat" (
    del "%ADMIN_DIR%\fix-types.bat"
    echo   ✅ 删除 fix-types.bat
)
if exist "%ADMIN_DIR%\prepare-baota-deploy.bat" (
    del "%ADMIN_DIR%\prepare-baota-deploy.bat"
    echo   ✅ 删除 prepare-baota-deploy.bat
)
if exist "%ADMIN_DIR%\quick-fix.bat" (
    del "%ADMIN_DIR%\quick-fix.bat"
    echo   ✅ 删除 quick-fix.bat
)
if exist "%ADMIN_DIR%\test-build.bat" (
    del "%ADMIN_DIR%\test-build.bat"
    echo   ✅ 删除 test-build.bat
)

REM 删除测试文件
echo 🗑️ 清理测试文件...
if exist "%ADMIN_DIR%\preview.html" (
    del "%ADMIN_DIR%\preview.html"
    echo   ✅ 删除 preview.html
)
if exist "%ADMIN_DIR%\simple-server.js" (
    del "%ADMIN_DIR%\simple-server.js"
    echo   ✅ 删除 simple-server.js
)
if exist "%ADMIN_DIR%\test-python-server.py" (
    del "%ADMIN_DIR%\test-python-server.py"
    echo   ✅ 删除 test-python-server.py
)
if exist "%ADMIN_DIR%\test-server.js" (
    del "%ADMIN_DIR%\test-server.js"
    echo   ✅ 删除 test-server.js
)

REM 删除多余的配置文件
echo 🗑️ 清理配置文件...
if exist "%ADMIN_DIR%\baota-nginx.conf" (
    del "%ADMIN_DIR%\baota-nginx.conf"
    echo   ✅ 删除 baota-nginx.conf
)
if exist "%ADMIN_DIR%\nginx.conf" (
    del "%ADMIN_DIR%\nginx.conf"
    echo   ✅ 删除 nginx.conf
)

REM 删除多余的目录
echo 🗑️ 清理多余目录...
if exist "%ADMIN_DIR%\deploy" (
    rmdir /s /q "%ADMIN_DIR%\deploy"
    echo   ✅ 删除 deploy 目录
)

REM 清理public目录中的测试文件
echo 🗑️ 清理public测试文件...
if exist "%ADMIN_DIR%\public\cors-test.html" (
    del "%ADMIN_DIR%\public\cors-test.html"
    echo   ✅ 删除 public/cors-test.html
)
if exist "%ADMIN_DIR%\public\failed-order-test.html" (
    del "%ADMIN_DIR%\public\failed-order-test.html"
    echo   ✅ 删除 public/failed-order-test.html
)
if exist "%ADMIN_DIR%\public\test-api.html" (
    del "%ADMIN_DIR%\public\test-api.html"
    echo   ✅ 删除 public/test-api.html
)
if exist "%ADMIN_DIR%\public\workflow-test.html" (
    del "%ADMIN_DIR%\public\workflow-test.html"
    echo   ✅ 删除 public/workflow-test.html
)

REM 清理build目录中的测试文件
echo 🗑️ 清理build测试文件...
if exist "%ADMIN_DIR%\build\cors-test.html" (
    del "%ADMIN_DIR%\build\cors-test.html"
    echo   ✅ 删除 build/cors-test.html
)
if exist "%ADMIN_DIR%\build\failed-order-test.html" (
    del "%ADMIN_DIR%\build\failed-order-test.html"
    echo   ✅ 删除 build/failed-order-test.html
)
if exist "%ADMIN_DIR%\build\test-api.html" (
    del "%ADMIN_DIR%\build\test-api.html"
    echo   ✅ 删除 build/test-api.html
)
if exist "%ADMIN_DIR%\build\workflow-test.html" (
    del "%ADMIN_DIR%\build\workflow-test.html"
    echo   ✅ 删除 build/workflow-test.html
)
if exist "%ADMIN_DIR%\build\build.rar" (
    del "%ADMIN_DIR%\build\build.rar"
    echo   ✅ 删除 build/build.rar
)

REM 删除Demo文件
echo 🗑️ 清理Demo文件...
if exist "%ADMIN_DIR%\src\Demo.tsx" (
    del "%ADMIN_DIR%\src\Demo.tsx"
    echo   ✅ 删除 src/Demo.tsx
)

echo.
echo 📊 清理完成统计...
echo.

echo 📁 保留的核心目录:
echo   ✅ src/ - 源代码
echo   ✅ public/ - 公共文件
echo   ✅ build/ - 构建输出
echo   ✅ node_modules/ - 依赖包
echo   ✅ deploy-production/ - 部署文件

echo.
echo 📄 保留的核心文件:
echo   ✅ package.json - 项目配置
echo   ✅ package-lock.json - 依赖锁定
echo   ✅ tsconfig.json - TypeScript配置
echo   ✅ README.md - 项目说明
echo   ✅ install.bat - 安装脚本
echo   ✅ start.bat - 启动脚本

echo.
echo 🗑️ 已删除的文件:
echo   ❌ 所有文档文件 (*.md 除README.md外)
echo   ❌ 开发批处理脚本 (*.bat 除install.bat和start.bat外)
echo   ❌ 测试文件 (test-*.*, preview.html等)
echo   ❌ 多余的配置文件 (nginx.conf等)
echo   ❌ 测试HTML文件
echo   ❌ Demo文件

echo.
echo 📊 目录大小统计:
for /f "tokens=3" %%a in ('dir "%ADMIN_DIR%" /s /-c ^| find "个文件"') do set filesize=%%a
echo   总大小: %filesize% 字节

echo.
echo 🎯 Admin项目现在只包含运行和部署必需的文件
echo.

echo ========================================
echo     清理完成！
echo ========================================
echo.
echo 📋 下一步建议:
echo   1. 检查 build/ 目录是否完整
echo   2. 测试 npm start 是否正常
echo   3. 验证生产构建是否正常
echo   4. 部署到服务器
echo.
pause

{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst Link = /*#__PURE__*/React.forwardRef((_a, ref) => {\n  var {\n      ellipsis,\n      rel\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Link');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object', 'usage', '`ellipsis` only supports boolean value.') : void 0;\n  }\n  const mergedProps = Object.assign(Object.assign({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  });\n  // @ts-expect-error: https://github.com/ant-design/ant-design/issues/26622\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({}, mergedProps, {\n    ref: ref,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n});\nexport default Link;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "devUseW<PERSON>ning", "Base", "Link", "forwardRef", "_a", "ref", "ellipsis", "rel", "restProps", "process", "env", "NODE_ENV", "warning", "mergedProps", "assign", "undefined", "target", "navigate", "createElement", "component"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/typography/Link.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport Base from './Base';\nconst Link = /*#__PURE__*/React.forwardRef((_a, ref) => {\n  var {\n      ellipsis,\n      rel\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography.Link');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object', 'usage', '`ellipsis` only supports boolean value.') : void 0;\n  }\n  const mergedProps = Object.assign(Object.assign({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  });\n  // @ts-expect-error: https://github.com/ant-design/ant-design/issues/26622\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({}, mergedProps, {\n    ref: ref,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n});\nexport default Link;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,IAAI,MAAM,QAAQ;AACzB,MAAMC,IAAI,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAACC,EAAE,EAAEC,GAAG,KAAK;EACtD,IAAI;MACAC,QAAQ;MACRC;IACF,CAAC,GAAGH,EAAE;IACNI,SAAS,GAAGvB,MAAM,CAACmB,EAAE,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;EAC7C,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGZ,aAAa,CAAC,iBAAiB,CAAC;IAChDS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,OAAON,QAAQ,KAAK,QAAQ,EAAE,OAAO,EAAE,yCAAyC,CAAC,GAAG,KAAK,CAAC;EAC5I;EACA,MAAMO,WAAW,GAAGvB,MAAM,CAACwB,MAAM,CAACxB,MAAM,CAACwB,MAAM,CAAC,CAAC,CAAC,EAAEN,SAAS,CAAC,EAAE;IAC9DD,GAAG,EAAEA,GAAG,KAAKQ,SAAS,IAAIP,SAAS,CAACQ,MAAM,KAAK,QAAQ,GAAG,qBAAqB,GAAGT;EACpF,CAAC,CAAC;EACF;EACA,OAAOM,WAAW,CAACI,QAAQ;EAC3B,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAACjB,IAAI,EAAEX,MAAM,CAACwB,MAAM,CAAC,CAAC,CAAC,EAAED,WAAW,EAAE;IAC3ER,GAAG,EAAEA,GAAG;IACRC,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBa,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAejB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
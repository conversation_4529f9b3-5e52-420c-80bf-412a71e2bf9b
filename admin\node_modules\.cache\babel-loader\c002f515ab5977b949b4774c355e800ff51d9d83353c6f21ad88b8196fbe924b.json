{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genBorderedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    tableHeaderBg,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  const tableBorder = \"\".concat(unit(lineWidth), \" \").concat(lineType, \" \").concat(tableBorderColor);\n  const getSizeBorderStyle = (size, paddingVertical, paddingHorizontal) => ({\n    [\"&\".concat(componentCls, \"-\").concat(size)]: {\n      [\"> \".concat(componentCls, \"-container\")]: {\n        [\"> \".concat(componentCls, \"-content, > \").concat(componentCls, \"-body\")]: {\n          [\"\\n            > table > tbody > tr > th,\\n            > table > tbody > tr > td\\n          \"]: {\n            [\"> \".concat(componentCls, \"-expanded-row-fixed\")]: {\n              margin: \"\".concat(unit(calc(paddingVertical).mul(-1).equal()), \"\\n              \").concat(unit(calc(calc(paddingHorizontal).add(lineWidth)).mul(-1).equal()))\n            }\n          }\n        }\n      }\n    }\n  });\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls).concat(componentCls, \"-bordered\")]: Object.assign(Object.assign(Object.assign({\n        // ============================ Title =============================\n        [\"> \".concat(componentCls, \"-title\")]: {\n          border: tableBorder,\n          borderBottom: 0\n        },\n        // ============================ Content ============================\n        [\"> \".concat(componentCls, \"-container\")]: {\n          borderInlineStart: tableBorder,\n          borderTop: tableBorder,\n          [\"\\n            > \".concat(componentCls, \"-content,\\n            > \").concat(componentCls, \"-header,\\n            > \").concat(componentCls, \"-body,\\n            > \").concat(componentCls, \"-summary\\n          \")]: {\n            '> table': {\n              // ============================= Cell =============================\n              [\"\\n                > thead > tr > th,\\n                > thead > tr > td,\\n                > tbody > tr > th,\\n                > tbody > tr > td,\\n                > tfoot > tr > th,\\n                > tfoot > tr > td\\n              \"]: {\n                borderInlineEnd: tableBorder\n              },\n              // ============================ Header ============================\n              '> thead': {\n                '> tr:not(:last-child) > th': {\n                  borderBottom: tableBorder\n                },\n                '> tr > th::before': {\n                  backgroundColor: 'transparent !important'\n                }\n              },\n              // Fixed right should provides additional border\n              [\"\\n                > thead > tr,\\n                > tbody > tr,\\n                > tfoot > tr\\n              \"]: {\n                [\"> \".concat(componentCls, \"-cell-fix-right-first::after\")]: {\n                  borderInlineEnd: tableBorder\n                }\n              },\n              // ========================== Expandable ==========================\n              [\"\\n                > tbody > tr > th,\\n                > tbody > tr > td\\n              \"]: {\n                [\"> \".concat(componentCls, \"-expanded-row-fixed\")]: {\n                  margin: \"\".concat(unit(calc(tablePaddingVertical).mul(-1).equal()), \" \").concat(unit(calc(calc(tablePaddingHorizontal).add(lineWidth)).mul(-1).equal())),\n                  '&::after': {\n                    position: 'absolute',\n                    top: 0,\n                    insetInlineEnd: lineWidth,\n                    bottom: 0,\n                    borderInlineEnd: tableBorder,\n                    content: '\"\"'\n                  }\n                }\n              }\n            }\n          }\n        },\n        // ============================ Scroll ============================\n        [\"&\".concat(componentCls, \"-scroll-horizontal\")]: {\n          [\"> \".concat(componentCls, \"-container > \").concat(componentCls, \"-body\")]: {\n            '> table > tbody': {\n              [\"\\n                > tr\".concat(componentCls, \"-expanded-row,\\n                > tr\").concat(componentCls, \"-placeholder\\n              \")]: {\n                '> th, > td': {\n                  borderInlineEnd: 0\n                }\n              }\n            }\n          }\n        }\n      }, getSizeBorderStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle)), getSizeBorderStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall)), {\n        // ============================ Footer ============================\n        [\"> \".concat(componentCls, \"-footer\")]: {\n          border: tableBorder,\n          borderTop: 0\n        }\n      }),\n      // ============================ Nested ============================\n      [\"\".concat(componentCls, \"-cell\")]: {\n        [\"\".concat(componentCls, \"-container:first-child\")]: {\n          // :first-child to avoid the case when bordered and title is set\n          borderTop: 0\n        },\n        // https://github.com/ant-design/ant-design/issues/35577\n        '&-scrollbar:not([rowspan])': {\n          boxShadow: \"0 \".concat(unit(lineWidth), \" 0 \").concat(unit(lineWidth), \" \").concat(tableHeaderBg)\n        }\n      },\n      [\"\".concat(componentCls, \"-bordered \").concat(componentCls, \"-cell-scrollbar\")]: {\n        borderInlineEnd: tableBorder\n      }\n    }\n  };\n};\nexport default genBorderedStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
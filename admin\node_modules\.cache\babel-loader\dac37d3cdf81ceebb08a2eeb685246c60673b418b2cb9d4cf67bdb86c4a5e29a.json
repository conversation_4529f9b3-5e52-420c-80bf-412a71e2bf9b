{"ast": null, "code": "\"use client\";\n\nimport React, { forwardRef } from 'react';\nimport classNames from 'classnames';\nexport const InternalPanel = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n    prefixCls,\n    className,\n    children,\n    size,\n    style = {}\n  } = props;\n  const panelClassName = classNames(\"\".concat(prefixCls, \"-panel\"), {\n    [\"\".concat(prefixCls, \"-panel-hidden\")]: size === 0\n  }, className);\n  const hasSize = size !== undefined;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: panelClassName,\n    style: Object.assign(Object.assign({}, style), {\n      // Use auto when start from ssr\n      flexBasis: hasSize ? size : 'auto',\n      flexGrow: hasSize ? 0 : 1\n    })\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  InternalPanel.displayName = 'Panel';\n}\nconst Panel = () => null;\nexport default Panel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
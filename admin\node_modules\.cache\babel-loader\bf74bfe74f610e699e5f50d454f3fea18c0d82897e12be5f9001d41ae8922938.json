{"ast": null, "code": "import * as React from 'react';\nimport { MenuContext } from \"../context/MenuContext\";\nexport default function useActive(eventKey, disabled, onMouseEnter, onMouseLeave) {\n  var _React$useContext = React.useContext(MenuContext),\n    activeKey = _React$useContext.activeKey,\n    onActive = _React$useContext.onActive,\n    onInactive = _React$useContext.onInactive;\n  var ret = {\n    active: activeKey === eventKey\n  };\n\n  // Skip when disabled\n  if (!disabled) {\n    ret.onMouseEnter = function (domEvent) {\n      onMouseEnter === null || onMouseEnter === void 0 || onMouseEnter({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onActive(eventKey);\n    };\n    ret.onMouseLeave = function (domEvent) {\n      onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onInactive(eventKey);\n    };\n  }\n  return ret;\n}", "map": {"version": 3, "names": ["React", "MenuContext", "useActive", "eventKey", "disabled", "onMouseEnter", "onMouseLeave", "_React$useContext", "useContext", "active<PERSON><PERSON>", "onActive", "onInactive", "ret", "active", "domEvent", "key"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-menu/es/hooks/useActive.js"], "sourcesContent": ["import * as React from 'react';\nimport { MenuContext } from \"../context/MenuContext\";\nexport default function useActive(eventKey, disabled, onMouseEnter, onMouseLeave) {\n  var _React$useContext = React.useContext(MenuContext),\n    activeKey = _React$useContext.activeKey,\n    onActive = _React$useContext.onActive,\n    onInactive = _React$useContext.onInactive;\n  var ret = {\n    active: activeKey === eventKey\n  };\n\n  // Skip when disabled\n  if (!disabled) {\n    ret.onMouseEnter = function (domEvent) {\n      onMouseEnter === null || onMouseEnter === void 0 || onMouseEnter({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onActive(eventKey);\n    };\n    ret.onMouseLeave = function (domEvent) {\n      onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onInactive(eventKey);\n    };\n  }\n  return ret;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,eAAe,SAASC,SAASA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,YAAY,EAAE;EAChF,IAAIC,iBAAiB,GAAGP,KAAK,CAACQ,UAAU,CAACP,WAAW,CAAC;IACnDQ,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,QAAQ,GAAGH,iBAAiB,CAACG,QAAQ;IACrCC,UAAU,GAAGJ,iBAAiB,CAACI,UAAU;EAC3C,IAAIC,GAAG,GAAG;IACRC,MAAM,EAAEJ,SAAS,KAAKN;EACxB,CAAC;;EAED;EACA,IAAI,CAACC,QAAQ,EAAE;IACbQ,GAAG,CAACP,YAAY,GAAG,UAAUS,QAAQ,EAAE;MACrCT,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC;QAC/DU,GAAG,EAAEZ,QAAQ;QACbW,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACFJ,QAAQ,CAACP,QAAQ,CAAC;IACpB,CAAC;IACDS,GAAG,CAACN,YAAY,GAAG,UAAUQ,QAAQ,EAAE;MACrCR,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC;QAC/DS,GAAG,EAAEZ,QAAQ;QACbW,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACFH,UAAU,CAACR,QAAQ,CAAC;IACtB,CAAC;EACH;EACA,OAAOS,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
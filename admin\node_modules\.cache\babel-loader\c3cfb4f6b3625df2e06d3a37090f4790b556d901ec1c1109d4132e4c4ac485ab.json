{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ChromeOutlinedSvg from \"@ant-design/icons-svg/es/asn/ChromeOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ChromeOutlined = function ChromeOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ChromeOutlinedSvg\n  }));\n};\n\n/**![chrome](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCA1MTIuM3YtLjNjMC0yMjkuOC0xODYuMi00MTYtNDE2LTQxNlM5NiAyODIuMiA5NiA1MTJ2LjRjMCAyMjkuOCAxODYuMiA0MTYgNDE2IDQxNnM0MTYtMTg2LjIgNDE2LTQxNnYtLjMuMnptLTYuNy03NC42bC42IDMuMy0uNi0zLjN6TTY3Ni43IDYzOC4yYzUzLjUtODIuMiA1Mi41LTE4OS40LTExLjEtMjYzLjdsMTYyLjQtOC40YzIwLjUgNDQuNCAzMiA5My44IDMyIDE0NS45IDAgMTg1LjItMTQ0LjYgMzM2LjYtMzI3LjEgMzQ3LjRsMTQzLjgtMjIxLjJ6TTUxMiA2NTIuM2MtNzcuNSAwLTE0MC4yLTYyLjctMTQwLjItMTQwLjIgMC03Ny43IDYyLjctMTQwLjIgMTQwLjItMTQwLjJTNjUyLjIgNDM0LjUgNjUyLjIgNTEyIDU4OS41IDY1Mi4zIDUxMiA2NTIuM3ptMzY5LjItMzMxLjdsLTMtNS43IDMgNS43ek01MTIgMTY0YzEyMS4zIDAgMjI4LjIgNjIuMSAyOTAuNCAxNTYuMmwtMjYzLjYtMTMuOWMtOTcuNS01LjctMTkwLjIgNDkuMi0yMjIuMyAxNDEuMUwyMjcuOCAzMTFjNjMuMS04OC45IDE2Ni45LTE0NyAyODQuMi0xNDd6TTEwMi41IDU4NS44YzI2IDE0NSAxMjcuMSAyNjQgMjYxLjYgMzE1LjFDMjI5LjYgODUwIDEyOC41IDczMSAxMDIuNSA1ODUuOHpNMTY0IDUxMmMwLTU1LjkgMTMuMi0xMDguNyAzNi42LTE1NS41bDExOS43IDIzNS40YzQ0LjEgODYuNyAxMzcuNCAxMzkuNyAyMzQgMTIxLjZsLTc0IDE0NS4xQzMwMi45IDg0Mi41IDE2NCA2OTMuNSAxNjQgNTEyem0zMjQuNyA0MTUuNGM0IC4yIDggLjQgMTIgLjUtNC0uMi04LS4zLTEyLS41eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ChromeOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ChromeOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "ChromeOutlinedSvg", "AntdIcon", "ChromeOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/ChromeOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ChromeOutlinedSvg from \"@ant-design/icons-svg/es/asn/ChromeOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ChromeOutlined = function ChromeOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ChromeOutlinedSvg\n  }));\n};\n\n/**![chrome](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCA1MTIuM3YtLjNjMC0yMjkuOC0xODYuMi00MTYtNDE2LTQxNlM5NiAyODIuMiA5NiA1MTJ2LjRjMCAyMjkuOCAxODYuMiA0MTYgNDE2IDQxNnM0MTYtMTg2LjIgNDE2LTQxNnYtLjMuMnptLTYuNy03NC42bC42IDMuMy0uNi0zLjN6TTY3Ni43IDYzOC4yYzUzLjUtODIuMiA1Mi41LTE4OS40LTExLjEtMjYzLjdsMTYyLjQtOC40YzIwLjUgNDQuNCAzMiA5My44IDMyIDE0NS45IDAgMTg1LjItMTQ0LjYgMzM2LjYtMzI3LjEgMzQ3LjRsMTQzLjgtMjIxLjJ6TTUxMiA2NTIuM2MtNzcuNSAwLTE0MC4yLTYyLjctMTQwLjItMTQwLjIgMC03Ny43IDYyLjctMTQwLjIgMTQwLjItMTQwLjJTNjUyLjIgNDM0LjUgNjUyLjIgNTEyIDU4OS41IDY1Mi4zIDUxMiA2NTIuM3ptMzY5LjItMzMxLjdsLTMtNS43IDMgNS43ek01MTIgMTY0YzEyMS4zIDAgMjI4LjIgNjIuMSAyOTAuNCAxNTYuMmwtMjYzLjYtMTMuOWMtOTcuNS01LjctMTkwLjIgNDkuMi0yMjIuMyAxNDEuMUwyMjcuOCAzMTFjNjMuMS04OC45IDE2Ni45LTE0NyAyODQuMi0xNDd6TTEwMi41IDU4NS44YzI2IDE0NSAxMjcuMSAyNjQgMjYxLjYgMzE1LjFDMjI5LjYgODUwIDEyOC41IDczMSAxMDIuNSA1ODUuOHpNMTY0IDUxMmMwLTU1LjkgMTMuMi0xMDguNyAzNi42LTE1NS41bDExOS43IDIzNS40YzQ0LjEgODYuNyAxMzcuNCAxMzkuNyAyMzQgMTIxLjZsLTc0IDE0NS4xQzMwMi45IDg0Mi41IDE2NCA2OTMuNSAxNjQgNTEyem0zMjQuNyA0MTUuNGM0IC4yIDggLjQgMTIgLjUtNC0uMi04LS4zLTEyLS41eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ChromeOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ChromeOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
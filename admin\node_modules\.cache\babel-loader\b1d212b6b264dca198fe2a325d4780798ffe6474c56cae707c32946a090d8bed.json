{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\AppConfig.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Tabs, Form, Input, Button, Typography, message, Spin } from 'antd';\nimport { SaveOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\n\n// API基础URL\nconst API_BASE_URL = 'http://localhost:8000/api/v1';\nconst AppConfig = () => {\n  _s();\n  const [customerServiceForm] = Form.useForm();\n  const [announcementForm] = Form.useForm();\n  const [privacyForm] = Form.useForm();\n  const [userAgreementForm] = Form.useForm();\n  const [bannerForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [dataLoading, setDataLoading] = useState(true);\n\n  // 加载配置数据\n  const loadConfigs = async () => {\n    setDataLoading(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/app-configs`);\n      if (response.data.success) {\n        const {\n          customer_service,\n          announcement,\n          privacy_policy,\n          user_agreement,\n          banner\n        } = response.data.data;\n\n        // 设置表单数据\n        customerServiceForm.setFieldsValue(customer_service);\n        announcementForm.setFieldsValue(announcement);\n        privacyForm.setFieldsValue(privacy_policy);\n        userAgreementForm.setFieldsValue(user_agreement);\n        bannerForm.setFieldsValue(banner);\n      } else {\n        message.error('加载配置失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('加载配置错误:', error);\n      message.error('加载配置失败，请检查网络连接');\n    } finally {\n      setDataLoading(false);\n    }\n  };\n\n  // 组件挂载时加载数据\n  useEffect(() => {\n    loadConfigs();\n  }, []);\n\n  // 客服设置保存\n  const handleCustomerServiceSave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/customer_service`, values);\n      if (response.data.success) {\n        message.success('客服设置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存客服设置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 公告弹出保存\n  const handleAnnouncementSave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/announcement`, values);\n      if (response.data.success) {\n        message.success('公告设置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存公告设置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 隐私政策保存\n  const handlePrivacySave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/privacy_policy`, values);\n      if (response.data.success) {\n        message.success('隐私政策保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存隐私政策错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 用户协议保存\n  const handleUserAgreementSave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/user_agreement`, values);\n      if (response.data.success) {\n        message.success('用户协议保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存用户协议错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 轮播图保存\n  const handleBannerSave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/banner`, values);\n      if (response.data.success) {\n        message.success('轮播图配置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存轮播图配置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 客服设置选项卡\n  const CustomerServiceTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u5BA2\\u670D\\u8BBE\\u7F6E\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: customerServiceForm,\n      layout: \"vertical\",\n      onFinish: handleCustomerServiceSave,\n      style: {\n        maxWidth: 600\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5FAE\\u4FE1\\u5BA2\\u670D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this),\n        name: \"wechatService\",\n        rules: [{\n          required: true,\n          message: '请输入微信客服号'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5FAE\\u4FE1\\u5BA2\\u670D\\u53F7\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5FAE\\u4FE1\\u5BA2\\u670D\\u4E8C\\u7EF4\\u7801\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this),\n        name: \"wechatQrCode\",\n        rules: [{\n          required: true,\n          message: '请输入微信客服二维码链接'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5FAE\\u4FE1\\u5BA2\\u670D\\u4E8C\\u7EF4\\u7801\\u94FE\\u63A5\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5BA2\\u670D\\u7535\\u8BDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this),\n        name: \"servicePhone\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u670D\\u7535\\u8BDD\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5BA2\\u670D\\u90AE\\u7BB1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this),\n        name: \"serviceEmail\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u670D\\u90AE\\u7BB1\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5DE5\\u4F5C\\u65F6\\u95F4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this),\n        name: \"workingHours\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F8B\\u5982\\uFF1A\\u5468\\u4E00\\u81F3\\u5468\\u4E94 9:00-18:00\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n\n  // 公告弹出选项卡\n  const AnnouncementTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u516C\\u544A\\u5F39\\u51FA\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: announcementForm,\n      layout: \"vertical\",\n      onFinish: handleAnnouncementSave,\n      style: {\n        maxWidth: 600\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u516C\\u544A\\u6807\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this),\n        name: \"title\",\n        rules: [{\n          required: true,\n          message: '请输入公告标题'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u516C\\u544A\\u6807\\u9898\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u516C\\u544A\\u5185\\u5BB9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this),\n        name: \"content\",\n        rules: [{\n          required: true,\n          message: '请输入公告内容'\n        }],\n        children: /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 6,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u516C\\u544A\\u5185\\u5BB9\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u662F\\u5426\\u542F\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this),\n        name: \"enabled\",\n        valuePropName: \"checked\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          ghost: true,\n          children: \"\\u542F\\u7528\\u516C\\u544A\\u5F39\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n\n  // 隐私政策选项卡\n  const PrivacyPolicyTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u9690\\u79C1\\u653F\\u7B56\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: privacyForm,\n      layout: \"vertical\",\n      onFinish: handlePrivacySave,\n      style: {\n        maxWidth: 800\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u9690\\u79C1\\u653F\\u7B56\\u6807\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 13\n        }, this),\n        name: \"title\",\n        rules: [{\n          required: true,\n          message: '请输入隐私政策标题'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u9690\\u79C1\\u653F\\u7B56\\u6807\\u9898\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u9690\\u79C1\\u653F\\u7B56\\u5185\\u5BB9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this),\n        name: \"content\",\n        rules: [{\n          required: true,\n          message: '请输入隐私政策内容'\n        }],\n        children: /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 12,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u9690\\u79C1\\u653F\\u7B56\\u5185\\u5BB9\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u751F\\u6548\\u65E5\\u671F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this),\n        name: \"effectiveDate\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F8B\\u5982\\uFF1A2024\\u5E741\\u67081\\u65E5\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 377,\n    columnNumber: 5\n  }, this);\n\n  // 用户协议选项卡\n  const UserAgreementTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u7528\\u6237\\u534F\\u8BAE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        color: '#999999',\n        fontSize: '14px',\n        display: 'block',\n        marginBottom: 24\n      },\n      children: \"\\u914D\\u7F6E\\u5E94\\u7528\\u7684\\u7528\\u6237\\u670D\\u52A1\\u534F\\u8BAE\\u5185\\u5BB9\\uFF0C\\u7528\\u6237\\u5728\\u6CE8\\u518C\\u6216\\u4F7F\\u7528\\u670D\\u52A1\\u65F6\\u9700\\u8981\\u540C\\u610F\\u7684\\u6761\\u6B3E\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: userAgreementForm,\n      layout: \"vertical\",\n      onFinish: handleUserAgreementSave,\n      style: {\n        maxWidth: '800px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u534F\\u8BAE\\u6807\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this),\n        name: \"title\",\n        rules: [{\n          required: true,\n          message: '请输入协议标题'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F8B\\u5982\\uFF1A\\u7528\\u6237\\u670D\\u52A1\\u534F\\u8BAE\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u534F\\u8BAE\\u5185\\u5BB9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 13\n        }, this),\n        name: \"content\",\n        rules: [{\n          required: true,\n          message: '请输入协议内容'\n        }],\n        children: /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 12,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BE6\\u7EC6\\u7684\\u7528\\u6237\\u534F\\u8BAE\\u5185\\u5BB9...\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '12px',\n            fontSize: '14px',\n            lineHeight: '1.6'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u751F\\u6548\\u65E5\\u671F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 13\n        }, this),\n        name: \"effectiveDate\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F8B\\u5982\\uFF1A2024\\u5E741\\u67081\\u65E5\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 473,\n    columnNumber: 5\n  }, this);\n\n  // 轮播图选项卡\n  const BannerTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u8F6E\\u64AD\\u56FE\\u914D\\u7F6E\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        color: '#999999',\n        fontSize: '14px',\n        display: 'block',\n        marginBottom: 24\n      },\n      children: \"\\u914D\\u7F6E\\u5E94\\u7528\\u9996\\u9875\\u7684\\u8F6E\\u64AD\\u56FE\\u5185\\u5BB9\\uFF0C\\u652F\\u63013\\u5F20\\u8F6E\\u64AD\\u56FE\\u7247\\u548C\\u76F8\\u5173\\u4FE1\\u606F\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: bannerForm,\n      layout: \"vertical\",\n      onFinish: handleBannerSave,\n      style: {\n        maxWidth: '800px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 32,\n          padding: 16,\n          border: '1px solid #f0f0f0',\n          borderRadius: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          style: {\n            color: '#666666',\n            marginBottom: 16\n          },\n          children: \"\\u8F6E\\u64AD\\u56FE 1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u56FE\\u7247URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 20\n          }, this),\n          name: ['banner1', 'imageUrl'],\n          rules: [{\n            required: true,\n            message: '请输入轮播图片链接'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u8DF3\\u8F6C\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 20\n          }, this),\n          name: ['banner1', 'linkUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 32,\n          padding: 16,\n          border: '1px solid #f0f0f0',\n          borderRadius: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          style: {\n            color: '#666666',\n            marginBottom: 16\n          },\n          children: \"\\u8F6E\\u64AD\\u56FE 2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u56FE\\u7247URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 20\n          }, this),\n          name: ['banner2', 'imageUrl'],\n          rules: [{\n            required: true,\n            message: '请输入轮播图片链接'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u8DF3\\u8F6C\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 20\n          }, this),\n          name: ['banner2', 'linkUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 32,\n          padding: 16,\n          border: '1px solid #f0f0f0',\n          borderRadius: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          style: {\n            color: '#666666',\n            marginBottom: 16\n          },\n          children: \"\\u8F6E\\u64AD\\u56FE 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u56FE\\u7247URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 20\n          }, this),\n          name: ['banner3', 'imageUrl'],\n          rules: [{\n            required: true,\n            message: '请输入轮播图片链接'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u8DF3\\u8F6C\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 20\n          }, this),\n          name: ['banner3', 'linkUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 574,\n    columnNumber: 5\n  }, this);\n\n  // 选项卡配置\n  const tabItems = [{\n    key: 'customerService',\n    label: '客服设置',\n    children: /*#__PURE__*/_jsxDEV(CustomerServiceTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 17\n    }, this)\n  }, {\n    key: 'announcement',\n    label: '公告弹出',\n    children: /*#__PURE__*/_jsxDEV(AnnouncementTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 17\n    }, this)\n  }, {\n    key: 'privacy',\n    label: '隐私政策',\n    children: /*#__PURE__*/_jsxDEV(PrivacyPolicyTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 17\n    }, this)\n  }, {\n    key: 'userAgreement',\n    label: '用户协议',\n    children: /*#__PURE__*/_jsxDEV(UserAgreementTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 691,\n      columnNumber: 17\n    }, this)\n  }, {\n    key: 'banner',\n    label: '轮播图',\n    children: /*#__PURE__*/_jsxDEV(BannerTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 17\n    }, this)\n  }];\n  if (dataLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '16px'\n        },\n        children: \"\\u52A0\\u8F7D\\u914D\\u7F6E\\u6570\\u636E\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 702,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Tabs, {\n      defaultActiveKey: \"customerService\",\n      items: tabItems,\n      size: \"large\",\n      style: {\n        background: '#ffffff',\n        borderRadius: '8px',\n        padding: '16px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 710,\n    columnNumber: 5\n  }, this);\n};\n_s(AppConfig, \"WCkcs8DXXmXk39mkdnUy8V3uM1A=\", false, function () {\n  return [Form.useForm, Form.useForm, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = AppConfig;\nexport default AppConfig;\nvar _c;\n$RefreshReg$(_c, \"AppConfig\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Tabs", "Form", "Input", "<PERSON><PERSON>", "Typography", "message", "Spin", "SaveOutlined", "axios", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "API_BASE_URL", "AppConfig", "_s", "customerServiceForm", "useForm", "announcementForm", "privacyForm", "userAgreementForm", "bannerForm", "loading", "setLoading", "dataLoading", "setDataLoading", "loadConfigs", "response", "get", "data", "success", "customer_service", "announcement", "privacy_policy", "user_agreement", "banner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "console", "handleCustomerServiceSave", "values", "put", "handleAnnouncementSave", "handlePrivacySave", "handleUserAgreementSave", "handleBannerSave", "CustomerServiceTab", "children", "level", "style", "color", "fontWeight", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "form", "layout", "onFinish", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "label", "name", "rules", "required", "placeholder", "backgroundColor", "border", "borderRadius", "padding", "marginTop", "type", "htmlType", "icon", "size", "AnnouncementTab", "rows", "valuePropName", "ghost", "PrivacyPolicyTab", "UserAgreementTab", "display", "lineHeight", "BannerTab", "tabItems", "key", "textAlign", "defaultActiveKey", "items", "background", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/AppConfig.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Tabs,\n  Form,\n  Input,\n  Button,\n  Typography,\n  message,\n  Spin,\n} from 'antd';\nimport { SaveOutlined } from '@ant-design/icons';\nimport type { TabsProps } from 'antd';\nimport axios from 'axios';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\n// API基础URL\nconst API_BASE_URL = 'http://localhost:8000/api/v1';\n\nconst AppConfig: React.FC = () => {\n  const [customerServiceForm] = Form.useForm();\n  const [announcementForm] = Form.useForm();\n  const [privacyForm] = Form.useForm();\n  const [userAgreementForm] = Form.useForm();\n  const [bannerForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [dataLoading, setDataLoading] = useState(true);\n\n  // 加载配置数据\n  const loadConfigs = async () => {\n    setDataLoading(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/app-configs`);\n      if (response.data.success) {\n        const { customer_service, announcement, privacy_policy, user_agreement, banner } = response.data.data;\n\n        // 设置表单数据\n        customerServiceForm.setFieldsValue(customer_service);\n        announcementForm.setFieldsValue(announcement);\n        privacyForm.setFieldsValue(privacy_policy);\n        userAgreementForm.setFieldsValue(user_agreement);\n        bannerForm.setFieldsValue(banner);\n      } else {\n        message.error('加载配置失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('加载配置错误:', error);\n      message.error('加载配置失败，请检查网络连接');\n    } finally {\n      setDataLoading(false);\n    }\n  };\n\n  // 组件挂载时加载数据\n  useEffect(() => {\n    loadConfigs();\n  }, []);\n\n  // 客服设置保存\n  const handleCustomerServiceSave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/customer_service`, values);\n      if (response.data.success) {\n        message.success('客服设置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存客服设置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 公告弹出保存\n  const handleAnnouncementSave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/announcement`, values);\n      if (response.data.success) {\n        message.success('公告设置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存公告设置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 隐私政策保存\n  const handlePrivacySave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/privacy_policy`, values);\n      if (response.data.success) {\n        message.success('隐私政策保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存隐私政策错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 用户协议保存\n  const handleUserAgreementSave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/user_agreement`, values);\n      if (response.data.success) {\n        message.success('用户协议保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存用户协议错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 轮播图保存\n  const handleBannerSave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/banner`, values);\n      if (response.data.success) {\n        message.success('轮播图配置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存轮播图配置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 客服设置选项卡\n  const CustomerServiceTab = () => (\n    <Card>\n      <Title \n        level={3} \n        style={{ \n          color: '#666666', \n          fontWeight: 'bold', \n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        客服设置\n      </Title>\n      \n      <Form\n        form={customerServiceForm}\n        layout=\"vertical\"\n        onFinish={handleCustomerServiceSave}\n        style={{ maxWidth: 600 }}\n      >\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              微信客服\n            </Text>\n          }\n          name=\"wechatService\"\n          rules={[{ required: true, message: '请输入微信客服号' }]}\n        >\n          <Input\n            placeholder=\"请输入微信客服号\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              微信客服二维码\n            </Text>\n          }\n          name=\"wechatQrCode\"\n          rules={[{ required: true, message: '请输入微信客服二维码链接' }]}\n        >\n          <Input\n            placeholder=\"请输入微信客服二维码链接\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              客服电话\n            </Text>\n          }\n          name=\"servicePhone\"\n        >\n          <Input\n            placeholder=\"请输入客服电话\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              客服邮箱\n            </Text>\n          }\n          name=\"serviceEmail\"\n        >\n          <Input\n            placeholder=\"请输入客服邮箱\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              工作时间\n            </Text>\n          }\n          name=\"workingHours\"\n        >\n          <Input\n            placeholder=\"例如：周一至周五 9:00-18:00\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 公告弹出选项卡\n  const AnnouncementTab = () => (\n    <Card>\n      <Title \n        level={3} \n        style={{ \n          color: '#666666', \n          fontWeight: 'bold', \n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        公告弹出\n      </Title>\n      \n      <Form\n        form={announcementForm}\n        layout=\"vertical\"\n        onFinish={handleAnnouncementSave}\n        style={{ maxWidth: 600 }}\n      >\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              公告标题\n            </Text>\n          }\n          name=\"title\"\n          rules={[{ required: true, message: '请输入公告标题' }]}\n        >\n          <Input\n            placeholder=\"请输入公告标题\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              公告内容\n            </Text>\n          }\n          name=\"content\"\n          rules={[{ required: true, message: '请输入公告内容' }]}\n        >\n          <TextArea\n            rows={6}\n            placeholder=\"请输入公告内容\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              是否启用\n            </Text>\n          }\n          name=\"enabled\"\n          valuePropName=\"checked\"\n        >\n          <Button type=\"primary\" ghost>\n            启用公告弹出\n          </Button>\n        </Form.Item>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 隐私政策选项卡\n  const PrivacyPolicyTab = () => (\n    <Card>\n      <Title \n        level={3} \n        style={{ \n          color: '#666666', \n          fontWeight: 'bold', \n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        隐私政策\n      </Title>\n      \n      <Form\n        form={privacyForm}\n        layout=\"vertical\"\n        onFinish={handlePrivacySave}\n        style={{ maxWidth: 800 }}\n      >\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              隐私政策标题\n            </Text>\n          }\n          name=\"title\"\n          rules={[{ required: true, message: '请输入隐私政策标题' }]}\n        >\n          <Input\n            placeholder=\"请输入隐私政策标题\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              隐私政策内容\n            </Text>\n          }\n          name=\"content\"\n          rules={[{ required: true, message: '请输入隐私政策内容' }]}\n        >\n          <TextArea\n            rows={12}\n            placeholder=\"请输入隐私政策内容\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              生效日期\n            </Text>\n          }\n          name=\"effectiveDate\"\n        >\n          <Input\n            placeholder=\"例如：2024年1月1日\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 用户协议选项卡\n  const UserAgreementTab = () => (\n    <Card>\n      <Title\n        level={3}\n        style={{\n          color: '#666666',\n          fontWeight: 'bold',\n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        用户协议\n      </Title>\n      <Text style={{ color: '#999999', fontSize: '14px', display: 'block', marginBottom: 24 }}>\n        配置应用的用户服务协议内容，用户在注册或使用服务时需要同意的条款。\n      </Text>\n\n      <Form\n        form={userAgreementForm}\n        layout=\"vertical\"\n        onFinish={handleUserAgreementSave}\n        style={{ maxWidth: '800px' }}\n      >\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              协议标题\n            </Text>\n          }\n          name=\"title\"\n          rules={[{ required: true, message: '请输入协议标题' }]}\n        >\n          <Input\n            placeholder=\"例如：用户服务协议\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              协议内容\n            </Text>\n          }\n          name=\"content\"\n          rules={[{ required: true, message: '请输入协议内容' }]}\n        >\n          <TextArea\n            rows={12}\n            placeholder=\"请输入详细的用户协议内容...\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '12px',\n              fontSize: '14px',\n              lineHeight: '1.6',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              生效日期\n            </Text>\n          }\n          name=\"effectiveDate\"\n        >\n          <Input\n            placeholder=\"例如：2024年1月1日\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 轮播图选项卡\n  const BannerTab = () => (\n    <Card>\n      <Title\n        level={3}\n        style={{\n          color: '#666666',\n          fontWeight: 'bold',\n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        轮播图配置\n      </Title>\n      <Text style={{ color: '#999999', fontSize: '14px', display: 'block', marginBottom: 24 }}>\n        配置应用首页的轮播图内容，支持3张轮播图片和相关信息。\n      </Text>\n\n      <Form\n        form={bannerForm}\n        layout=\"vertical\"\n        onFinish={handleBannerSave}\n        style={{ maxWidth: '800px' }}\n      >\n        {/* 轮播图1 */}\n        <div style={{ marginBottom: 32, padding: 16, border: '1px solid #f0f0f0', borderRadius: 8 }}>\n          <Title level={5} style={{ color: '#666666', marginBottom: 16 }}>轮播图 1</Title>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>图片URL</Text>}\n            name={['banner1', 'imageUrl']}\n            rules={[{ required: true, message: '请输入轮播图片链接' }]}\n          >\n            <Input placeholder=\"轮播图片链接\" />\n          </Form.Item>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>跳转链接</Text>}\n            name={['banner1', 'linkUrl']}\n          >\n            <Input placeholder=\"点击跳转的链接（可选）\" />\n          </Form.Item>\n        </div>\n\n        {/* 轮播图2 */}\n        <div style={{ marginBottom: 32, padding: 16, border: '1px solid #f0f0f0', borderRadius: 8 }}>\n          <Title level={5} style={{ color: '#666666', marginBottom: 16 }}>轮播图 2</Title>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>图片URL</Text>}\n            name={['banner2', 'imageUrl']}\n            rules={[{ required: true, message: '请输入轮播图片链接' }]}\n          >\n            <Input placeholder=\"轮播图片链接\" />\n          </Form.Item>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>跳转链接</Text>}\n            name={['banner2', 'linkUrl']}\n          >\n            <Input placeholder=\"点击跳转的链接（可选）\" />\n          </Form.Item>\n        </div>\n\n        {/* 轮播图3 */}\n        <div style={{ marginBottom: 32, padding: 16, border: '1px solid #f0f0f0', borderRadius: 8 }}>\n          <Title level={5} style={{ color: '#666666', marginBottom: 16 }}>轮播图 3</Title>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>图片URL</Text>}\n            name={['banner3', 'imageUrl']}\n            rules={[{ required: true, message: '请输入轮播图片链接' }]}\n          >\n            <Input placeholder=\"轮播图片链接\" />\n          </Form.Item>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>跳转链接</Text>}\n            name={['banner3', 'linkUrl']}\n          >\n            <Input placeholder=\"点击跳转的链接（可选）\" />\n          </Form.Item>\n        </div>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 选项卡配置\n  const tabItems: TabsProps['items'] = [\n    {\n      key: 'customerService',\n      label: '客服设置',\n      children: <CustomerServiceTab />,\n    },\n    {\n      key: 'announcement',\n      label: '公告弹出',\n      children: <AnnouncementTab />,\n    },\n    {\n      key: 'privacy',\n      label: '隐私政策',\n      children: <PrivacyPolicyTab />,\n    },\n    {\n      key: 'userAgreement',\n      label: '用户协议',\n      children: <UserAgreementTab />,\n    },\n    {\n      key: 'banner',\n      label: '轮播图',\n      children: <BannerTab />,\n    },\n  ];\n\n  if (dataLoading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: '16px' }}>加载配置数据中...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Tabs\n        defaultActiveKey=\"customerService\"\n        items={tabItems}\n        size=\"large\"\n        style={{\n          background: '#ffffff',\n          borderRadius: '8px',\n          padding: '16px',\n        }}\n      />\n    </div>\n  );\n};\n\nexport default AppConfig;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,IAAI,QACC,MAAM;AACb,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGR,UAAU;AAClC,MAAM;EAAES;AAAS,CAAC,GAAGX,KAAK;;AAE1B;AACA,MAAMY,YAAY,GAAG,8BAA8B;AAEnD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,mBAAmB,CAAC,GAAGhB,IAAI,CAACiB,OAAO,CAAC,CAAC;EAC5C,MAAM,CAACC,gBAAgB,CAAC,GAAGlB,IAAI,CAACiB,OAAO,CAAC,CAAC;EACzC,MAAM,CAACE,WAAW,CAAC,GAAGnB,IAAI,CAACiB,OAAO,CAAC,CAAC;EACpC,MAAM,CAACG,iBAAiB,CAAC,GAAGpB,IAAI,CAACiB,OAAO,CAAC,CAAC;EAC1C,MAAM,CAACI,UAAU,CAAC,GAAGrB,IAAI,CAACiB,OAAO,CAAC,CAAC;EACnC,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM8B,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BD,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMpB,KAAK,CAACqB,GAAG,CAAC,GAAGf,YAAY,cAAc,CAAC;MAC/D,IAAIc,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEC,gBAAgB;UAAEC,YAAY;UAAEC,cAAc;UAAEC,cAAc;UAAEC;QAAO,CAAC,GAAGR,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAErG;QACAb,mBAAmB,CAACoB,cAAc,CAACL,gBAAgB,CAAC;QACpDb,gBAAgB,CAACkB,cAAc,CAACJ,YAAY,CAAC;QAC7Cb,WAAW,CAACiB,cAAc,CAACH,cAAc,CAAC;QAC1Cb,iBAAiB,CAACgB,cAAc,CAACF,cAAc,CAAC;QAChDb,UAAU,CAACe,cAAc,CAACD,MAAM,CAAC;MACnC,CAAC,MAAM;QACL/B,OAAO,CAACiC,KAAK,CAAC,SAAS,GAAGV,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAClD;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjC,OAAO,CAACiC,KAAK,CAAC,gBAAgB,CAAC;IACjC,CAAC,SAAS;MACRZ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACd6B,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMa,yBAAyB,GAAG,MAAOC,MAAW,IAAK;IACvDjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACkC,GAAG,CAAC,GAAG5B,YAAY,+BAA+B,EAAE2B,MAAM,CAAC;MACxF,IAAIb,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL1B,OAAO,CAACiC,KAAK,CAAC,OAAO,GAAGV,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCjC,OAAO,CAACiC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmB,sBAAsB,GAAG,MAAOF,MAAW,IAAK;IACpDjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACkC,GAAG,CAAC,GAAG5B,YAAY,2BAA2B,EAAE2B,MAAM,CAAC;MACpF,IAAIb,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL1B,OAAO,CAACiC,KAAK,CAAC,OAAO,GAAGV,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCjC,OAAO,CAACiC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoB,iBAAiB,GAAG,MAAOH,MAAW,IAAK;IAC/CjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACkC,GAAG,CAAC,GAAG5B,YAAY,6BAA6B,EAAE2B,MAAM,CAAC;MACtF,IAAIb,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL1B,OAAO,CAACiC,KAAK,CAAC,OAAO,GAAGV,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCjC,OAAO,CAACiC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqB,uBAAuB,GAAG,MAAOJ,MAAW,IAAK;IACrDjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACkC,GAAG,CAAC,GAAG5B,YAAY,6BAA6B,EAAE2B,MAAM,CAAC;MACtF,IAAIb,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL1B,OAAO,CAACiC,KAAK,CAAC,OAAO,GAAGV,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCjC,OAAO,CAACiC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAG,MAAOL,MAAW,IAAK;IAC9CjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACkC,GAAG,CAAC,GAAG5B,YAAY,qBAAqB,EAAE2B,MAAM,CAAC;MAC9E,IAAIb,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,WAAW,CAAC;MAC9B,CAAC,MAAM;QACL1B,OAAO,CAACiC,KAAK,CAAC,OAAO,GAAGV,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOiC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCjC,OAAO,CAACiC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuB,kBAAkB,GAAGA,CAAA,kBACzBrC,OAAA,CAACX,IAAI;IAAAiD,QAAA,gBACHtC,OAAA,CAACC,KAAK;MACJsC,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERhD,OAAA,CAACT,IAAI;MACH0D,IAAI,EAAE1C,mBAAoB;MAC1B2C,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAErB,yBAA0B;MACpCU,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAI,CAAE;MAAAd,QAAA,gBAEzBtC,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,eAAe;QACpBC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9D,OAAO,EAAE;QAAW,CAAC,CAAE;QAAA2C,QAAA,eAEjDtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,kDAAU;UACtBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,cAAc;QACnBC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9D,OAAO,EAAE;QAAe,CAAC,CAAE;QAAA2C,QAAA,eAErDtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,0EAAc;UAC1BlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,cAAc;QAAAjB,QAAA,eAEnBtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,4CAAS;UACrBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,cAAc;QAAAjB,QAAA,eAEnBtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,4CAAS;UACrBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,cAAc;QAAAjB,QAAA,eAEnBtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,6DAAqB;UACjClB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCtC,OAAA,CAACP,MAAM;UACLuE,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBpD,OAAO,EAAEA,OAAQ;UACjBqD,IAAI,eAAElE,OAAA,CAACH,YAAY;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAMoB,eAAe,GAAGA,CAAA,kBACtBpE,OAAA,CAACX,IAAI;IAAAiD,QAAA,gBACHtC,OAAA,CAACC,KAAK;MACJsC,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERhD,OAAA,CAACT,IAAI;MACH0D,IAAI,EAAExC,gBAAiB;MACvByC,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAElB,sBAAuB;MACjCO,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAI,CAAE;MAAAd,QAAA,gBAEzBtC,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9D,OAAO,EAAE;QAAU,CAAC,CAAE;QAAA2C,QAAA,eAEhDtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,4CAAS;UACrBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,SAAS;QACdC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9D,OAAO,EAAE;QAAU,CAAC,CAAE;QAAA2C,QAAA,eAEhDtC,OAAA,CAACG,QAAQ;UACPkE,IAAI,EAAE,CAAE;UACRX,WAAW,EAAC,4CAAS;UACrBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,SAAS;QACde,aAAa,EAAC,SAAS;QAAAhC,QAAA,eAEvBtC,OAAA,CAACP,MAAM;UAACuE,IAAI,EAAC,SAAS;UAACO,KAAK;UAAAjC,QAAA,EAAC;QAE7B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCtC,OAAA,CAACP,MAAM;UACLuE,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBpD,OAAO,EAAEA,OAAQ;UACjBqD,IAAI,eAAElE,OAAA,CAACH,YAAY;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAMwB,gBAAgB,GAAGA,CAAA,kBACvBxE,OAAA,CAACX,IAAI;IAAAiD,QAAA,gBACHtC,OAAA,CAACC,KAAK;MACJsC,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERhD,OAAA,CAACT,IAAI;MACH0D,IAAI,EAAEvC,WAAY;MAClBwC,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAEjB,iBAAkB;MAC5BM,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAI,CAAE;MAAAd,QAAA,gBAEzBtC,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9D,OAAO,EAAE;QAAY,CAAC,CAAE;QAAA2C,QAAA,eAElDtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,wDAAW;UACvBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,SAAS;QACdC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9D,OAAO,EAAE;QAAY,CAAC,CAAE;QAAA2C,QAAA,eAElDtC,OAAA,CAACG,QAAQ;UACPkE,IAAI,EAAE,EAAG;UACTX,WAAW,EAAC,wDAAW;UACvBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,eAAe;QAAAjB,QAAA,eAEpBtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,4CAAc;UAC1BlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCtC,OAAA,CAACP,MAAM;UACLuE,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBpD,OAAO,EAAEA,OAAQ;UACjBqD,IAAI,eAAElE,OAAA,CAACH,YAAY;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAMyB,gBAAgB,GAAGA,CAAA,kBACvBzE,OAAA,CAACX,IAAI;IAAAiD,QAAA,gBACHtC,OAAA,CAACC,KAAK;MACJsC,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRhD,OAAA,CAACE,IAAI;MAACsC,KAAK,EAAE;QAAEC,KAAK,EAAE,SAAS;QAAEG,QAAQ,EAAE,MAAM;QAAE8B,OAAO,EAAE,OAAO;QAAE/B,YAAY,EAAE;MAAG,CAAE;MAAAL,QAAA,EAAC;IAEzF;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPhD,OAAA,CAACT,IAAI;MACH0D,IAAI,EAAEtC,iBAAkB;MACxBuC,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAEhB,uBAAwB;MAClCK,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAQ,CAAE;MAAAd,QAAA,gBAE7BtC,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9D,OAAO,EAAE;QAAU,CAAC,CAAE;QAAA2C,QAAA,eAEhDtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,wDAAW;UACvBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,SAAS;QACdC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9D,OAAO,EAAE;QAAU,CAAC,CAAE;QAAA2C,QAAA,eAEhDtC,OAAA,CAACG,QAAQ;UACPkE,IAAI,EAAE,EAAG;UACTX,WAAW,EAAC,6EAAiB;UAC7BlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,MAAM;YACflB,QAAQ,EAAE,MAAM;YAChB+B,UAAU,EAAE;UACd;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QACRC,KAAK,eACHtD,OAAA,CAACE,IAAI;UAACsC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,eAAe;QAAAjB,QAAA,eAEpBtC,OAAA,CAACR,KAAK;UACJkE,WAAW,EAAC,4CAAc;UAC1BlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCtC,OAAA,CAACP,MAAM;UACLuE,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBpD,OAAO,EAAEA,OAAQ;UACjBqD,IAAI,eAAElE,OAAA,CAACH,YAAY;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAM4B,SAAS,GAAGA,CAAA,kBAChB5E,OAAA,CAACX,IAAI;IAAAiD,QAAA,gBACHtC,OAAA,CAACC,KAAK;MACJsC,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRhD,OAAA,CAACE,IAAI;MAACsC,KAAK,EAAE;QAAEC,KAAK,EAAE,SAAS;QAAEG,QAAQ,EAAE,MAAM;QAAE8B,OAAO,EAAE,OAAO;QAAE/B,YAAY,EAAE;MAAG,CAAE;MAAAL,QAAA,EAAC;IAEzF;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPhD,OAAA,CAACT,IAAI;MACH0D,IAAI,EAAErC,UAAW;MACjBsC,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAEf,gBAAiB;MAC3BI,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAQ,CAAE;MAAAd,QAAA,gBAG7BtC,OAAA;QAAKwC,KAAK,EAAE;UAAEG,YAAY,EAAE,EAAE;UAAEmB,OAAO,EAAE,EAAE;UAAEF,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC1FtC,OAAA,CAACC,KAAK;UAACsC,KAAK,EAAE,CAAE;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEE,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAE7EhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;UACRC,KAAK,eAAEtD,OAAA,CAACE,IAAI;YAACsC,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACzEO,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAE;UAC9BC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9D,OAAO,EAAE;UAAY,CAAC,CAAE;UAAA2C,QAAA,eAElDtC,OAAA,CAACR,KAAK;YAACkE,WAAW,EAAC;UAAQ;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;UACRC,KAAK,eAAEtD,OAAA,CAACE,IAAI;YAACsC,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACxEO,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAE;UAAAjB,QAAA,eAE7BtC,OAAA,CAACR,KAAK;YAACkE,WAAW,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGNhD,OAAA;QAAKwC,KAAK,EAAE;UAAEG,YAAY,EAAE,EAAE;UAAEmB,OAAO,EAAE,EAAE;UAAEF,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC1FtC,OAAA,CAACC,KAAK;UAACsC,KAAK,EAAE,CAAE;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEE,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAE7EhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;UACRC,KAAK,eAAEtD,OAAA,CAACE,IAAI;YAACsC,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACzEO,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAE;UAC9BC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9D,OAAO,EAAE;UAAY,CAAC,CAAE;UAAA2C,QAAA,eAElDtC,OAAA,CAACR,KAAK;YAACkE,WAAW,EAAC;UAAQ;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;UACRC,KAAK,eAAEtD,OAAA,CAACE,IAAI;YAACsC,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACxEO,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAE;UAAAjB,QAAA,eAE7BtC,OAAA,CAACR,KAAK;YAACkE,WAAW,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGNhD,OAAA;QAAKwC,KAAK,EAAE;UAAEG,YAAY,EAAE,EAAE;UAAEmB,OAAO,EAAE,EAAE;UAAEF,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC1FtC,OAAA,CAACC,KAAK;UAACsC,KAAK,EAAE,CAAE;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEE,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAE7EhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;UACRC,KAAK,eAAEtD,OAAA,CAACE,IAAI;YAACsC,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACzEO,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAE;UAC9BC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9D,OAAO,EAAE;UAAY,CAAC,CAAE;UAAA2C,QAAA,eAElDtC,OAAA,CAACR,KAAK;YAACkE,WAAW,EAAC;UAAQ;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;UACRC,KAAK,eAAEtD,OAAA,CAACE,IAAI;YAACsC,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACxEO,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAE;UAAAjB,QAAA,eAE7BtC,OAAA,CAACR,KAAK;YAACkE,WAAW,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENhD,OAAA,CAACT,IAAI,CAAC8D,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCtC,OAAA,CAACP,MAAM;UACLuE,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBpD,OAAO,EAAEA,OAAQ;UACjBqD,IAAI,eAAElE,OAAA,CAACH,YAAY;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAM6B,QAA4B,GAAG,CACnC;IACEC,GAAG,EAAE,iBAAiB;IACtBxB,KAAK,EAAE,MAAM;IACbhB,QAAQ,eAAEtC,OAAA,CAACqC,kBAAkB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACjC,CAAC,EACD;IACE8B,GAAG,EAAE,cAAc;IACnBxB,KAAK,EAAE,MAAM;IACbhB,QAAQ,eAAEtC,OAAA,CAACoE,eAAe;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9B,CAAC,EACD;IACE8B,GAAG,EAAE,SAAS;IACdxB,KAAK,EAAE,MAAM;IACbhB,QAAQ,eAAEtC,OAAA,CAACwE,gBAAgB;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC/B,CAAC,EACD;IACE8B,GAAG,EAAE,eAAe;IACpBxB,KAAK,EAAE,MAAM;IACbhB,QAAQ,eAAEtC,OAAA,CAACyE,gBAAgB;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC/B,CAAC,EACD;IACE8B,GAAG,EAAE,QAAQ;IACbxB,KAAK,EAAE,KAAK;IACZhB,QAAQ,eAAEtC,OAAA,CAAC4E,SAAS;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACxB,CAAC,CACF;EAED,IAAIjC,WAAW,EAAE;IACf,oBACEf,OAAA;MAAKwC,KAAK,EAAE;QAAEuC,SAAS,EAAE,QAAQ;QAAEjB,OAAO,EAAE;MAAO,CAAE;MAAAxB,QAAA,gBACnDtC,OAAA,CAACJ,IAAI;QAACuE,IAAI,EAAC;MAAO;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBhD,OAAA;QAAKwC,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAO,CAAE;QAAAzB,QAAA,EAAC;MAAU;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACEhD,OAAA;IAAAsC,QAAA,eACEtC,OAAA,CAACV,IAAI;MACH0F,gBAAgB,EAAC,iBAAiB;MAClCC,KAAK,EAAEJ,QAAS;MAChBV,IAAI,EAAC,OAAO;MACZ3B,KAAK,EAAE;QACL0C,UAAU,EAAE,SAAS;QACrBrB,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE;MACX;IAAE;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA7rBID,SAAmB;EAAA,QACOd,IAAI,CAACiB,OAAO,EACfjB,IAAI,CAACiB,OAAO,EACjBjB,IAAI,CAACiB,OAAO,EACNjB,IAAI,CAACiB,OAAO,EACnBjB,IAAI,CAACiB,OAAO;AAAA;AAAA2E,EAAA,GAL7B9E,SAAmB;AA+rBzB,eAAeA,SAAS;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
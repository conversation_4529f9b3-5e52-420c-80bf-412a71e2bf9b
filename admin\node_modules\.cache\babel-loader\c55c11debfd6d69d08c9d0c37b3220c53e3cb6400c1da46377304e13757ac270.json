{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PictureFilledSvg from \"@ant-design/icons-svg/es/asn/PictureFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PictureFilled = function PictureFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PictureFilledSvg\n  }));\n};\n\n/**![picture](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PictureFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PictureFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function getRoot(ele) {\n  var _ele$getRootNode;\n  return ele === null || ele === void 0 || (_ele$getRootNode = ele.getRootNode) === null || _ele$getRootNode === void 0 ? void 0 : _ele$getRootNode.call(ele);\n}\n\n/**\n * Check if is in shadowRoot\n */\nexport function inShadow(ele) {\n  return getRoot(ele) instanceof ShadowRoot;\n}\n\n/**\n * Return shadowRoot if possible\n */\nexport function getShadowRoot(ele) {\n  return inShadow(ele) ? getRoot(ele) : null;\n}", "map": {"version": 3, "names": ["getRoot", "ele", "_ele$getRootNode", "getRootNode", "call", "inShadow", "ShadowRoot", "getShadowRoot"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-util/es/Dom/shadow.js"], "sourcesContent": ["function getRoot(ele) {\n  var _ele$getRootNode;\n  return ele === null || ele === void 0 || (_ele$getRootNode = ele.getRootNode) === null || _ele$getRootNode === void 0 ? void 0 : _ele$getRootNode.call(ele);\n}\n\n/**\n * Check if is in shadowRoot\n */\nexport function inShadow(ele) {\n  return getRoot(ele) instanceof ShadowRoot;\n}\n\n/**\n * Return shadowRoot if possible\n */\nexport function getShadowRoot(ele) {\n  return inShadow(ele) ? getRoot(ele) : null;\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EACpB,IAAIC,gBAAgB;EACpB,OAAOD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,IAAI,CAACC,gBAAgB,GAAGD,GAAG,CAACE,WAAW,MAAM,IAAI,IAAID,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACE,IAAI,CAACH,GAAG,CAAC;AAC7J;;AAEA;AACA;AACA;AACA,OAAO,SAASI,QAAQA,CAACJ,GAAG,EAAE;EAC5B,OAAOD,OAAO,CAACC,GAAG,CAAC,YAAYK,UAAU;AAC3C;;AAEA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACN,GAAG,EAAE;EACjC,OAAOI,QAAQ,CAACJ,GAAG,CAAC,GAAGD,OAAO,CAACC,GAAG,CAAC,GAAG,IAAI;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
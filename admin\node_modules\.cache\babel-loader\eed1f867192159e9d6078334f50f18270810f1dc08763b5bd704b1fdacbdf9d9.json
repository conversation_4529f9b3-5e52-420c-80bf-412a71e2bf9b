{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport StepBackwardFilledSvg from \"@ant-design/icons-svg/es/asn/StepBackwardFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar StepBackwardFilled = function StepBackwardFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: StepBackwardFilledSvg\n  }));\n};\n\n/**![step-backward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0Ny42IDUyOC45NWwzODMuMiAzMDEuMDJjMTQuMjUgMTEuMiAzNS4yIDEuMSAzNS4yLTE2Ljk1VjIxMC45N2MwLTE4LjA1LTIwLjk1LTI4LjE0LTM1LjItMTYuOTRMMzQ3LjYgNDk1LjA1YTIxLjUzIDIxLjUzIDAgMDAwIDMzLjlNMzMwIDg2NGgtNjRhOCA4IDAgMDEtOC04VjE2OGE4IDggMCAwMTgtOGg2NGE4IDggMCAwMTggOHY2ODhhOCA4IDAgMDEtOCA4IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(StepBackwardFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'StepBackwardFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
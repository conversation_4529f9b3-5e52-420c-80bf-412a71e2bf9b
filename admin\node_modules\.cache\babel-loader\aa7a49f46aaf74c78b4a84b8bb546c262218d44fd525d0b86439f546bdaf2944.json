{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport React from 'react';\nimport useItems from \"./hooks/useItems\";\nimport CollapsePanel from \"./Panel\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nfunction getActiveKeysArray(activeKey) {\n  var currentActiveKey = activeKey;\n  if (!Array.isArray(currentActiveKey)) {\n    var activeKeyType = _typeof(currentActiveKey);\n    currentActiveKey = activeKeyType === 'number' || activeKeyType === 'string' ? [currentActiveKey] : [];\n  }\n  return currentActiveKey.map(function (key) {\n    return String(key);\n  });\n}\nvar Collapse = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-collapse' : _props$prefixCls,\n    _props$destroyInactiv = props.destroyInactivePanel,\n    destroyInactivePanel = _props$destroyInactiv === void 0 ? false : _props$destroyInactiv,\n    style = props.style,\n    accordion = props.accordion,\n    className = props.className,\n    children = props.children,\n    collapsible = props.collapsible,\n    openMotion = props.openMotion,\n    expandIcon = props.expandIcon,\n    rawActiveKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    _onChange = props.onChange,\n    items = props.items;\n  var collapseClassName = classNames(prefixCls, className);\n  var _useMergedState = useMergedState([], {\n      value: rawActiveKey,\n      onChange: function onChange(v) {\n        return _onChange === null || _onChange === void 0 ? void 0 : _onChange(v);\n      },\n      defaultValue: defaultActiveKey,\n      postState: getActiveKeysArray\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    activeKey = _useMergedState2[0],\n    setActiveKey = _useMergedState2[1];\n  var onItemClick = function onItemClick(key) {\n    return setActiveKey(function () {\n      if (accordion) {\n        return activeKey[0] === key ? [] : [key];\n      }\n      var index = activeKey.indexOf(key);\n      var isActive = index > -1;\n      if (isActive) {\n        return activeKey.filter(function (item) {\n          return item !== key;\n        });\n      }\n      return [].concat(_toConsumableArray(activeKey), [key]);\n    });\n  };\n\n  // ======================== Children ========================\n  warning(!children, '[rc-collapse] `children` will be removed in next major version. Please use `items` instead.');\n  var mergedChildren = useItems(items, children, {\n    prefixCls: prefixCls,\n    accordion: accordion,\n    openMotion: openMotion,\n    expandIcon: expandIcon,\n    collapsible: collapsible,\n    destroyInactivePanel: destroyInactivePanel,\n    onItemClick: onItemClick,\n    activeKey: activeKey\n  });\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: collapseClassName,\n    style: style,\n    role: accordion ? 'tablist' : undefined\n  }, pickAttrs(props, {\n    aria: true,\n    data: true\n  })), mergedChildren);\n});\nexport default Object.assign(Collapse, {\n  /**\n   * @deprecated use `items` instead, will be removed in `v4.0.0`\n   */\n  Panel: CollapsePanel\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
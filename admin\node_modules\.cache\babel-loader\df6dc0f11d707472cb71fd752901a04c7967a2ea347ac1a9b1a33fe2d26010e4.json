{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nconst BreadcrumbSeparator = _ref => {\n  let {\n    children\n  } = _ref;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('breadcrumb');\n  return /*#__PURE__*/React.createElement(\"li\", {\n    className: \"\".concat(prefixCls, \"-separator\"),\n    \"aria-hidden\": \"true\"\n  }, children === '' ? children : children || '/');\n};\nBreadcrumbSeparator.__ANT_BREADCRUMB_SEPARATOR = true;\nexport default BreadcrumbSeparator;", "map": {"version": 3, "names": ["React", "ConfigContext", "BreadcrumbSeparator", "_ref", "children", "getPrefixCls", "useContext", "prefixCls", "createElement", "className", "concat", "__ANT_BREADCRUMB_SEPARATOR"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/breadcrumb/BreadcrumbSeparator.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nconst BreadcrumbSeparator = ({\n  children\n}) => {\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('breadcrumb');\n  return /*#__PURE__*/React.createElement(\"li\", {\n    className: `${prefixCls}-separator`,\n    \"aria-hidden\": \"true\"\n  }, children === '' ? children : children || '/');\n};\nBreadcrumbSeparator.__ANT_BREADCRUMB_SEPARATOR = true;\nexport default BreadcrumbSeparator;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,MAAMC,mBAAmB,GAAGC,IAAA,IAEtB;EAAA,IAFuB;IAC3BC;EACF,CAAC,GAAAD,IAAA;EACC,MAAM;IACJE;EACF,CAAC,GAAGL,KAAK,CAACM,UAAU,CAACL,aAAa,CAAC;EACnC,MAAMM,SAAS,GAAGF,YAAY,CAAC,YAAY,CAAC;EAC5C,OAAO,aAAaL,KAAK,CAACQ,aAAa,CAAC,IAAI,EAAE;IAC5CC,SAAS,KAAAC,MAAA,CAAKH,SAAS,eAAY;IACnC,aAAa,EAAE;EACjB,CAAC,EAAEH,QAAQ,KAAK,EAAE,GAAGA,QAAQ,GAAGA,QAAQ,IAAI,GAAG,CAAC;AAClD,CAAC;AACDF,mBAAmB,CAACS,0BAA0B,GAAG,IAAI;AACrD,eAAeT,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport { genTable } from 'rc-table';\n/**\n * Same as `rc-table` but we modify trigger children update logic instead.\n */\nconst RcTable = genTable((prev, next) => {\n  const {\n    _renderTimes: prevRenderTimes\n  } = prev;\n  const {\n    _renderTimes: nextRenderTimes\n  } = next;\n  return prevRenderTimes !== nextRenderTimes;\n});\nexport default RcTable;", "map": {"version": 3, "names": ["genTable", "RcTable", "prev", "next", "_renderTimes", "prevRenderTimes", "nextRenderTimes"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/table/RcTable/index.js"], "sourcesContent": ["\"use client\";\n\nimport { genTable } from 'rc-table';\n/**\n * Same as `rc-table` but we modify trigger children update logic instead.\n */\nconst RcTable = genTable((prev, next) => {\n  const {\n    _renderTimes: prevRenderTimes\n  } = prev;\n  const {\n    _renderTimes: nextRenderTimes\n  } = next;\n  return prevRenderTimes !== nextRenderTimes;\n});\nexport default RcTable;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,UAAU;AACnC;AACA;AACA;AACA,MAAMC,OAAO,GAAGD,QAAQ,CAAC,CAACE,IAAI,EAAEC,IAAI,KAAK;EACvC,MAAM;IACJC,YAAY,EAAEC;EAChB,CAAC,GAAGH,IAAI;EACR,MAAM;IACJE,YAAY,EAAEE;EAChB,CAAC,GAAGH,IAAI;EACR,OAAOE,eAAe,KAAKC,eAAe;AAC5C,CAAC,CAAC;AACF,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
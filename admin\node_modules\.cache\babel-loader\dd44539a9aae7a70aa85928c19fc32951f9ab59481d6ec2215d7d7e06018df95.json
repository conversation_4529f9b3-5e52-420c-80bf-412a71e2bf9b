{"ast": null, "code": "/* istanbul ignore next */\n/** This is a syntactic sugar for `columns` prop. So HOC will not work on this. */\nconst ColumnGroup = _ => null;\nexport default ColumnGroup;", "map": {"version": 3, "names": ["ColumnGroup", "_"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/table/ColumnGroup.js"], "sourcesContent": ["/* istanbul ignore next */\n/** This is a syntactic sugar for `columns` prop. So HOC will not work on this. */\nconst ColumnGroup = _ => null;\nexport default ColumnGroup;"], "mappings": "AAAA;AACA;AACA,MAAMA,WAAW,GAAGC,CAAC,IAAI,IAAI;AAC7B,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
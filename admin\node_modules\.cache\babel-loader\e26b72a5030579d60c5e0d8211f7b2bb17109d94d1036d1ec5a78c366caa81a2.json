{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"./warning\";\n\n/**\n * Deeply compares two object literals.\n * @param obj1 object 1\n * @param obj2 object 2\n * @param shallow shallow compare\n * @returns\n */\nfunction isEqual(obj1, obj2) {\n  var shallow = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  // https://github.com/mapbox/mapbox-gl-js/pull/5979/files#diff-fde7145050c47cc3a306856efd5f9c3016e86e859de9afbd02c879be5067e58f\n  var refSet = new Set();\n  function deepEqual(a, b) {\n    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n    var circular = refSet.has(a);\n    warning(!circular, 'Warning: There may be circular references');\n    if (circular) {\n      return false;\n    }\n    if (a === b) {\n      return true;\n    }\n    if (shallow && level > 1) {\n      return false;\n    }\n    refSet.add(a);\n    var newLevel = level + 1;\n    if (Array.isArray(a)) {\n      if (!Array.isArray(b) || a.length !== b.length) {\n        return false;\n      }\n      for (var i = 0; i < a.length; i++) {\n        if (!deepEqual(a[i], b[i], newLevel)) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (a && b && _typeof(a) === 'object' && _typeof(b) === 'object') {\n      var keys = Object.keys(a);\n      if (keys.length !== Object.keys(b).length) {\n        return false;\n      }\n      return keys.every(function (key) {\n        return deepEqual(a[key], b[key], newLevel);\n      });\n    }\n    // other\n    return false;\n  }\n  return deepEqual(obj1, obj2);\n}\nexport default isEqual;", "map": {"version": 3, "names": ["_typeof", "warning", "isEqual", "obj1", "obj2", "shallow", "arguments", "length", "undefined", "refSet", "Set", "deepEqual", "a", "b", "level", "circular", "has", "add", "newLevel", "Array", "isArray", "i", "keys", "Object", "every", "key"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-util/es/isEqual.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"./warning\";\n\n/**\n * Deeply compares two object literals.\n * @param obj1 object 1\n * @param obj2 object 2\n * @param shallow shallow compare\n * @returns\n */\nfunction isEqual(obj1, obj2) {\n  var shallow = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  // https://github.com/mapbox/mapbox-gl-js/pull/5979/files#diff-fde7145050c47cc3a306856efd5f9c3016e86e859de9afbd02c879be5067e58f\n  var refSet = new Set();\n  function deepEqual(a, b) {\n    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n    var circular = refSet.has(a);\n    warning(!circular, 'Warning: There may be circular references');\n    if (circular) {\n      return false;\n    }\n    if (a === b) {\n      return true;\n    }\n    if (shallow && level > 1) {\n      return false;\n    }\n    refSet.add(a);\n    var newLevel = level + 1;\n    if (Array.isArray(a)) {\n      if (!Array.isArray(b) || a.length !== b.length) {\n        return false;\n      }\n      for (var i = 0; i < a.length; i++) {\n        if (!deepEqual(a[i], b[i], newLevel)) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (a && b && _typeof(a) === 'object' && _typeof(b) === 'object') {\n      var keys = Object.keys(a);\n      if (keys.length !== Object.keys(b).length) {\n        return false;\n      }\n      return keys.every(function (key) {\n        return deepEqual(a[key], b[key], newLevel);\n      });\n    }\n    // other\n    return false;\n  }\n  return deepEqual(obj1, obj2);\n}\nexport default isEqual;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC3B,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACvF;EACA,IAAIG,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtB,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACvB,IAAIC,KAAK,GAAGR,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACjF,IAAIS,QAAQ,GAAGN,MAAM,CAACO,GAAG,CAACJ,CAAC,CAAC;IAC5BX,OAAO,CAAC,CAACc,QAAQ,EAAE,2CAA2C,CAAC;IAC/D,IAAIA,QAAQ,EAAE;MACZ,OAAO,KAAK;IACd;IACA,IAAIH,CAAC,KAAKC,CAAC,EAAE;MACX,OAAO,IAAI;IACb;IACA,IAAIR,OAAO,IAAIS,KAAK,GAAG,CAAC,EAAE;MACxB,OAAO,KAAK;IACd;IACAL,MAAM,CAACQ,GAAG,CAACL,CAAC,CAAC;IACb,IAAIM,QAAQ,GAAGJ,KAAK,GAAG,CAAC;IACxB,IAAIK,KAAK,CAACC,OAAO,CAACR,CAAC,CAAC,EAAE;MACpB,IAAI,CAACO,KAAK,CAACC,OAAO,CAACP,CAAC,CAAC,IAAID,CAAC,CAACL,MAAM,KAAKM,CAAC,CAACN,MAAM,EAAE;QAC9C,OAAO,KAAK;MACd;MACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,CAAC,CAACL,MAAM,EAAEc,CAAC,EAAE,EAAE;QACjC,IAAI,CAACV,SAAS,CAACC,CAAC,CAACS,CAAC,CAAC,EAAER,CAAC,CAACQ,CAAC,CAAC,EAAEH,QAAQ,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;IACA,IAAIN,CAAC,IAAIC,CAAC,IAAIb,OAAO,CAACY,CAAC,CAAC,KAAK,QAAQ,IAAIZ,OAAO,CAACa,CAAC,CAAC,KAAK,QAAQ,EAAE;MAChE,IAAIS,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACV,CAAC,CAAC;MACzB,IAAIU,IAAI,CAACf,MAAM,KAAKgB,MAAM,CAACD,IAAI,CAACT,CAAC,CAAC,CAACN,MAAM,EAAE;QACzC,OAAO,KAAK;MACd;MACA,OAAOe,IAAI,CAACE,KAAK,CAAC,UAAUC,GAAG,EAAE;QAC/B,OAAOd,SAAS,CAACC,CAAC,CAACa,GAAG,CAAC,EAAEZ,CAAC,CAACY,GAAG,CAAC,EAAEP,QAAQ,CAAC;MAC5C,CAAC,CAAC;IACJ;IACA;IACA,OAAO,KAAK;EACd;EACA,OAAOP,SAAS,CAACR,IAAI,EAAEC,IAAI,CAAC;AAC9B;AACA,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
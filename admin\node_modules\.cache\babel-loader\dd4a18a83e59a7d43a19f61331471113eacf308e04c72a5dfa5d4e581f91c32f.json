{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nexport function file2Obj(file) {\n  return Object.assign(Object.assign({}, file), {\n    lastModified: file.lastModified,\n    lastModifiedDate: file.lastModifiedDate,\n    name: file.name,\n    size: file.size,\n    type: file.type,\n    uid: file.uid,\n    percent: 0,\n    originFileObj: file\n  });\n}\n/** Upload fileList. Replace file if exist or just push into it. */\nexport function updateFileList(file, fileList) {\n  const nextFileList = _toConsumableArray(fileList);\n  const fileIndex = nextFileList.findIndex(_ref => {\n    let {\n      uid\n    } = _ref;\n    return uid === file.uid;\n  });\n  if (fileIndex === -1) {\n    nextFileList.push(file);\n  } else {\n    nextFileList[fileIndex] = file;\n  }\n  return nextFileList;\n}\nexport function getFileItem(file, fileList) {\n  const matchKey = file.uid !== undefined ? 'uid' : 'name';\n  return fileList.filter(item => item[matchKey] === file[matchKey])[0];\n}\nexport function removeFileItem(file, fileList) {\n  const matchKey = file.uid !== undefined ? 'uid' : 'name';\n  const removed = fileList.filter(item => item[matchKey] !== file[matchKey]);\n  if (removed.length === fileList.length) {\n    return null;\n  }\n  return removed;\n}\n// ==================== Default Image Preview ====================\nconst extname = function () {\n  let url = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  const temp = url.split('/');\n  const filename = temp[temp.length - 1];\n  const filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n  return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n};\nconst isImageFileType = type => type.indexOf('image/') === 0;\nexport const isImageUrl = file => {\n  if (file.type && !file.thumbUrl) {\n    return isImageFileType(file.type);\n  }\n  const url = file.thumbUrl || file.url || '';\n  const extension = extname(url);\n  if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(extension)) {\n    return true;\n  }\n  if (/^data:/.test(url)) {\n    // other file types of base64\n    return false;\n  }\n  if (extension) {\n    // other file types which have extension\n    return false;\n  }\n  return true;\n};\nconst MEASURE_SIZE = 200;\nexport function previewImage(file) {\n  return new Promise(resolve => {\n    if (!file.type || !isImageFileType(file.type)) {\n      resolve('');\n      return;\n    }\n    const canvas = document.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = \"position: fixed; left: 0; top: 0; width: \".concat(MEASURE_SIZE, \"px; height: \").concat(MEASURE_SIZE, \"px; z-index: 9999; display: none;\");\n    document.body.appendChild(canvas);\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    img.onload = () => {\n      const {\n        width,\n        height\n      } = img;\n      let drawWidth = MEASURE_SIZE;\n      let drawHeight = MEASURE_SIZE;\n      let offsetX = 0;\n      let offsetY = 0;\n      if (width > height) {\n        drawHeight = height * (MEASURE_SIZE / width);\n        offsetY = -(drawHeight - drawWidth) / 2;\n      } else {\n        drawWidth = width * (MEASURE_SIZE / height);\n        offsetX = -(drawWidth - drawHeight) / 2;\n      }\n      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n      const dataURL = canvas.toDataURL();\n      document.body.removeChild(canvas);\n      window.URL.revokeObjectURL(img.src);\n      resolve(dataURL);\n    };\n    img.crossOrigin = 'anonymous';\n    if (file.type.startsWith('image/svg+xml')) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (reader.result && typeof reader.result === 'string') {\n          img.src = reader.result;\n        }\n      };\n      reader.readAsDataURL(file);\n    } else if (file.type.startsWith('image/gif')) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (reader.result) {\n          resolve(reader.result);\n        }\n      };\n      reader.readAsDataURL(file);\n    } else {\n      img.src = window.URL.createObjectURL(file);\n    }\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
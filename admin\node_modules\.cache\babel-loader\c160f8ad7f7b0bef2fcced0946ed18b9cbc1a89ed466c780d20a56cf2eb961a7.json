{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, Form, Input, Select, DatePicker, Row, Col, Statistic, message, Modal, Descriptions } from 'antd';\nimport { SearchOutlined, EyeOutlined, EditOutlined, ExportOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\n// 订单数据将从API获取，不再使用模拟数据\n\nconst OrderList = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState([]);\n  const [filteredOrders, setFilteredOrders] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const navigate = useNavigate();\n\n  // 组件加载时获取订单数据\n  useEffect(() => {\n    loadOrders();\n  }, []);\n\n  // 加载订单数据\n  const loadOrders = async () => {\n    setLoading(true);\n    try {\n      // TODO: 从API获取订单数据\n      // const response = await orderService.getOrders();\n      // setOrders(response.data);\n      // setFilteredOrders(response.data);\n\n      // 暂时设置为空数组，等待API接入\n      setOrders([]);\n      setFilteredOrders([]);\n    } catch (error) {\n      message.error('获取订单数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 状态颜色映射\n  const getStatusColor = status => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      pending_upload: 'purple',\n      shipped: 'cyan',\n      activated: 'green',\n      failed: 'red',\n      cancelled: 'default'\n    };\n    return colors[status] || 'default';\n  };\n\n  // 状态文本映射\n  const getStatusText = status => {\n    const texts = {\n      pending: '待处理',\n      processing: '开卡中',\n      pending_upload: '待上传三证',\n      shipped: '已发货',\n      activated: '已激活',\n      failed: '开卡失败',\n      cancelled: '已取消'\n    };\n    return texts[status] || status;\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = priority => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red'\n    };\n    return colors[priority] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = priority => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急'\n    };\n    return texts[priority] || '普通';\n  };\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    pending_upload: filteredOrders.filter(order => order.status === 'pending_upload').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    failed: filteredOrders.filter(order => order.status === 'failed').length,\n    cancelled: filteredOrders.filter(order => order.status === 'cancelled').length\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '订单状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物流信息',\n    key: 'logistics',\n    width: 200,\n    render: (_, record) => {\n      if (record.status === 'shipped' || record.status === 'activated') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 600,\n              fontSize: '12px'\n            },\n            children: record.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#666'\n            },\n            children: record.trackingNumber || '暂无快递单号'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), record.shippedAt && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#999'\n            },\n            children: [\"\\u53D1\\u8D27: \", record.shippedAt.split(' ')[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: \"\\u672A\\u53D1\\u8D27\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditOrder(record.id),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleSearch = values => {\n    setLoading(true);\n    setTimeout(() => {\n      let filtered = [...orders];\n      if (values.orderNo) {\n        filtered = filtered.filter(order => order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase()));\n      }\n      if (values.customerName) {\n        filtered = filtered.filter(order => order.customerName.toLowerCase().includes(values.customerName.toLowerCase()));\n      }\n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      if (values.status) {\n        filtered = filtered.filter(order => order.status === values.status);\n      }\n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n  const handleRefresh = async () => {\n    await loadOrders();\n    message.success('数据已刷新');\n  };\n  const handleViewOrder = order => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n  const handleEditOrder = id => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#1890ff'\n        },\n        children: \"\\u8BA2\\u5355\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u7BA1\\u7406\\u6240\\u6709\\u8BA2\\u5355\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA2\\u5355\\u6570\",\n            value: stats.total,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\",\n            value: stats.pending,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\",\n            value: stats.pending_upload,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F00\\u5361\\u4E2D\",\n            value: stats.processing,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u53D1\\u8D27\",\n            value: stats.shipped,\n            valueStyle: {\n              color: '#13c2c2'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u6FC0\\u6D3B\",\n            value: stats.activated,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"orderNo\",\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",\n            style: {\n              width: 150\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"customerName\",\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",\n            style: {\n              width: 120\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"operator\",\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u79FB\\u52A8\",\n              children: \"\\u4E2D\\u56FD\\u79FB\\u52A8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u7535\\u4FE1\",\n              children: \"\\u4E2D\\u56FD\\u7535\\u4FE1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u8054\\u901A\",\n              children: \"\\u4E2D\\u56FD\\u8054\\u901A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u5E7F\\u7535\",\n              children: \"\\u4E2D\\u56FD\\u5E7F\\u7535\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending\",\n              children: \"\\u5F85\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"processing\",\n              children: \"\\u5F00\\u5361\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending_upload\",\n              children: \"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"shipped\",\n              children: \"\\u5DF2\\u53D1\\u8D27\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"activated\",\n              children: \"\\u5DF2\\u6FC0\\u6D3B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"failed\",\n              children: \"\\u5F00\\u5361\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"cancelled\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 60\n            }, this),\n            children: \"\\u641C\\u7D22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", filteredOrders.length, \" \\u6761\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleExport,\n            icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 50\n            }, this),\n            children: \"\\u5BFC\\u51FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredOrders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BA2\\u5355\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          span: 2,\n          children: selectedOrder.orderNo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: selectedOrder.customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8054\\u7CFB\\u7535\\u8BDD\",\n          children: selectedOrder.customerPhone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n          span: 2,\n          children: selectedOrder.productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: selectedOrder.operator\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(selectedOrder.status),\n            children: getStatusText(selectedOrder.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 13\n        }, this), (selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7269\\u6D41\\u516C\\u53F8\",\n            children: selectedOrder.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5FEB\\u9012\\u5355\\u53F7\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              code: true,\n              children: selectedOrder.trackingNumber || '暂无'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u53D1\\u8D27\\u65F6\\u95F4\",\n            children: selectedOrder.shippedAt || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u9884\\u8BA1\\u9001\\u8FBE\",\n            children: selectedOrder.estimatedDelivery || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: selectedOrder.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: selectedOrder.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 324,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderList, \"9r8/qT7Cb7v0I/GUyYY2ggapth0=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = OrderList;\nexport default OrderList;\nvar _c;\n$RefreshReg$(_c, \"OrderList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Form", "Input", "Select", "DatePicker", "Row", "Col", "Statistic", "message", "Modal", "Descriptions", "SearchOutlined", "EyeOutlined", "EditOutlined", "ExportOutlined", "ReloadOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "RangePicker", "OrderList", "_s", "form", "useForm", "orders", "setOrders", "filteredOrders", "setFilteredOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "detailModalVisible", "setDetailModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "navigate", "loadOrders", "error", "getStatusColor", "status", "colors", "pending", "processing", "pending_upload", "shipped", "activated", "failed", "cancelled", "getStatusText", "texts", "getPriorityColor", "priority", "low", "normal", "high", "urgent", "getPriorityText", "stats", "total", "length", "filter", "order", "columns", "title", "dataIndex", "key", "width", "render", "text", "code", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "record", "fontWeight", "customerName", "color", "customerPhone", "ellipsis", "logisticsCompany", "trackingNumber", "shippedAt", "split", "type", "size", "icon", "onClick", "handleViewOrder", "handleEditOrder", "id", "handleSearch", "values", "setTimeout", "filtered", "orderNo", "toLowerCase", "includes", "operator", "handleRefresh", "success", "info", "handleExport", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "placeholder", "Option", "htmlType", "display", "justifyContent", "alignItems", "strong", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "footer", "column", "bordered", "productName", "estimatedDelivery", "createdAt", "updatedAt", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Modal,\n  Descriptions,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  FilterOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { useNavigate } from 'react-router-dom';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\ninterface Order {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  customerIdCard: string;\n  productName: string;\n  operator: string;\n  deliveryAddress: string;\n  status: 'pending' | 'processing' | 'pending_upload' | 'shipped' | 'activated' | 'cancelled' | 'failed';\n  priority?: 'low' | 'normal' | 'high' | 'urgent';\n  logisticsCompany?: string; // 物流公司\n  trackingNumber?: string; // 快递单号\n  shippedAt?: string; // 发货时间\n  estimatedDelivery?: string; // 预计送达时间\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 订单数据将从API获取，不再使用模拟数据\n\nconst OrderList: React.FC = () => {\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const navigate = useNavigate();\n\n  // 组件加载时获取订单数据\n  useEffect(() => {\n    loadOrders();\n  }, []);\n\n  // 加载订单数据\n  const loadOrders = async () => {\n    setLoading(true);\n    try {\n      // TODO: 从API获取订单数据\n      // const response = await orderService.getOrders();\n      // setOrders(response.data);\n      // setFilteredOrders(response.data);\n\n      // 暂时设置为空数组，等待API接入\n      setOrders([]);\n      setFilteredOrders([]);\n    } catch (error) {\n      message.error('获取订单数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 状态颜色映射\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      pending_upload: 'purple',\n      shipped: 'cyan',\n      activated: 'green',\n      failed: 'red',\n      cancelled: 'default',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n\n\n  // 状态文本映射\n  const getStatusText = (status: string) => {\n    const texts = {\n      pending: '待处理',\n      processing: '开卡中',\n      pending_upload: '待上传三证',\n      shipped: '已发货',\n      activated: '已激活',\n      failed: '开卡失败',\n      cancelled: '已取消',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = (priority?: string) => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red',\n    };\n    return colors[priority as keyof typeof colors] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = (priority?: string) => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急',\n    };\n    return texts[priority as keyof typeof texts] || '普通';\n  };\n\n\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    pending_upload: filteredOrders.filter(order => order.status === 'pending_upload').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    failed: filteredOrders.filter(order => order.status === 'failed').length,\n    cancelled: filteredOrders.filter(order => order.status === 'cancelled').length,\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Order> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n    {\n      title: '订单状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '物流信息',\n      key: 'logistics',\n      width: 200,\n      render: (_, record) => {\n        if (record.status === 'shipped' || record.status === 'activated') {\n          return (\n            <div>\n              <div style={{ fontWeight: 600, fontSize: '12px' }}>\n                {record.logisticsCompany || '暂无'}\n              </div>\n              <div style={{ fontSize: '11px', color: '#666' }}>\n                {record.trackingNumber || '暂无快递单号'}\n              </div>\n              {record.shippedAt && (\n                <div style={{ fontSize: '11px', color: '#999' }}>\n                  发货: {record.shippedAt.split(' ')[0]}\n                </div>\n              )}\n            </div>\n          );\n        }\n        return <Text type=\"secondary\" style={{ fontSize: '12px' }}>未发货</Text>;\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditOrder(record.id)}\n          >\n            编辑\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleSearch = (values: any) => {\n    setLoading(true);\n    \n    setTimeout(() => {\n      let filtered = [...orders];\n      \n      if (values.orderNo) {\n        filtered = filtered.filter(order => \n          order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase())\n        );\n      }\n      \n      if (values.customerName) {\n        filtered = filtered.filter(order => \n          order.customerName.toLowerCase().includes(values.customerName.toLowerCase())\n        );\n      }\n      \n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      \n      if (values.status) {\n        filtered = filtered.filter(order => order.status === values.status);\n      }\n      \n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n\n\n\n  const handleRefresh = async () => {\n    await loadOrders();\n    message.success('数据已刷新');\n  };\n\n  const handleViewOrder = (order: Order) => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n\n  const handleEditOrder = (id: number) => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n          订单列表\n        </Title>\n        <Text type=\"secondary\">\n          管理所有订单信息\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={stats.total}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"待处理\"\n              value={stats.pending}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"待上传三证\"\n              value={stats.pending_upload}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"开卡中\"\n              value={stats.processing}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"已发货\"\n              value={stats.shipped}\n              valueStyle={{ color: '#13c2c2' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"已激活\"\n              value={stats.activated}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 搜索表单 */}\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ marginBottom: 16 }}\n        >\n          <Form.Item name=\"orderNo\" label=\"订单号\">\n            <Input placeholder=\"请输入订单号\" style={{ width: 150 }} />\n          </Form.Item>\n          <Form.Item name=\"customerName\" label=\"客户姓名\">\n            <Input placeholder=\"请输入客户姓名\" style={{ width: 120 }} />\n          </Form.Item>\n          <Form.Item name=\"operator\" label=\"运营商\">\n            <Select placeholder=\"请选择运营商\" style={{ width: 120 }}>\n              <Select.Option value=\"中国移动\">中国移动</Select.Option>\n              <Select.Option value=\"中国电信\">中国电信</Select.Option>\n              <Select.Option value=\"中国联通\">中国联通</Select.Option>\n              <Select.Option value=\"中国广电\">中国广电</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"status\" label=\"订单状态\">\n            <Select placeholder=\"请选择状态\" style={{ width: 120 }}>\n              <Select.Option value=\"pending\">待处理</Select.Option>\n              <Select.Option value=\"processing\">开卡中</Select.Option>\n              <Select.Option value=\"pending_upload\">待上传三证</Select.Option>\n              <Select.Option value=\"shipped\">已发货</Select.Option>\n              <Select.Option value=\"activated\">已激活</Select.Option>\n              <Select.Option value=\"failed\">开卡失败</Select.Option>\n              <Select.Option value=\"cancelled\">已取消</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item>\n            <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n              搜索\n            </Button>\n          </Form.Item>\n        </Form>\n\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {filteredOrders.length} 条订单\n            </Text>\n          </div>\n          <Space>\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n            <Button onClick={handleExport} icon={<ExportOutlined />}>\n              导出\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={filteredOrders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 订单详情弹窗 */}\n      <Modal\n        title=\"订单详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={600}\n      >\n        {selectedOrder && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"订单号\" span={2}>\n              {selectedOrder.orderNo}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"客户姓名\">\n              {selectedOrder.customerName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"联系电话\">\n              {selectedOrder.customerPhone}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"产品名称\" span={2}>\n              {selectedOrder.productName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"运营商\">\n              <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"订单状态\">\n              <Tag color={getStatusColor(selectedOrder.status)}>\n                {getStatusText(selectedOrder.status)}\n              </Tag>\n            </Descriptions.Item>\n            {(selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && (\n              <>\n                <Descriptions.Item label=\"物流公司\">\n                  {selectedOrder.logisticsCompany || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"快递单号\">\n                  <Text code>{selectedOrder.trackingNumber || '暂无'}</Text>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"发货时间\">\n                  {selectedOrder.shippedAt || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"预计送达\">\n                  {selectedOrder.estimatedDelivery || '暂无'}\n                </Descriptions.Item>\n              </>\n            )}\n            <Descriptions.Item label=\"创建时间\">\n              {selectedOrder.createdAt}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"更新时间\">\n              {selectedOrder.updatedAt}\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,QACP,MAAM;AACb,SACEC,cAAc,EACdC,WAAW,EACXC,YAAY,EAEZC,cAAc,EACdC,cAAc,QAET,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGvB,UAAU;AAClC,MAAM;EAAEwB;AAAY,CAAC,GAAGnB,UAAU;AAqBlC;;AAEA,MAAMoB,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAGzB,IAAI,CAAC0B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAU,EAAE,CAAC;EACjE,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC2C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAM+C,QAAQ,GAAGxB,WAAW,CAAC,CAAC;;EAE9B;EACAtB,SAAS,CAAC,MAAM;IACd+C,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BR,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;MACA;MACA;;MAEA;MACAJ,SAAS,CAAC,EAAE,CAAC;MACbE,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMU,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,MAAM,GAAG;MACbC,OAAO,EAAE,QAAQ;MACjBC,UAAU,EAAE,MAAM;MAClBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE;IACb,CAAC;IACD,OAAOP,MAAM,CAACD,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;;EAID;EACA,MAAMS,aAAa,GAAIT,MAAc,IAAK;IACxC,MAAMU,KAAK,GAAG;MACZR,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,OAAO;MACvBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAACV,MAAM,CAAuB,IAAIA,MAAM;EACtD,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAIC,QAAiB,IAAK;IAC9C,MAAMX,MAAM,GAAG;MACbY,GAAG,EAAE,SAAS;MACdC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAOf,MAAM,CAACW,QAAQ,CAAwB,IAAI,SAAS;EAC7D,CAAC;;EAED;EACA,MAAMK,eAAe,GAAIL,QAAiB,IAAK;IAC7C,MAAMF,KAAK,GAAG;MACZG,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE;IACV,CAAC;IACD,OAAON,KAAK,CAACE,QAAQ,CAAuB,IAAI,IAAI;EACtD,CAAC;;EAID;EACA,MAAMM,KAAK,GAAG;IACZC,KAAK,EAAEjC,cAAc,CAACkC,MAAM;IAC5BlB,OAAO,EAAEhB,cAAc,CAACmC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtB,MAAM,KAAK,SAAS,CAAC,CAACoB,MAAM;IAC1EjB,UAAU,EAAEjB,cAAc,CAACmC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtB,MAAM,KAAK,YAAY,CAAC,CAACoB,MAAM;IAChFhB,cAAc,EAAElB,cAAc,CAACmC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtB,MAAM,KAAK,gBAAgB,CAAC,CAACoB,MAAM;IACxFf,OAAO,EAAEnB,cAAc,CAACmC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtB,MAAM,KAAK,SAAS,CAAC,CAACoB,MAAM;IAC1Ed,SAAS,EAAEpB,cAAc,CAACmC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtB,MAAM,KAAK,WAAW,CAAC,CAACoB,MAAM;IAC9Eb,MAAM,EAAErB,cAAc,CAACmC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtB,MAAM,KAAK,QAAQ,CAAC,CAACoB,MAAM;IACxEZ,SAAS,EAAEtB,cAAc,CAACmC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtB,MAAM,KAAK,WAAW,CAAC,CAACoB;EAC1E,CAAC;;EAED;EACA,MAAMG,OAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBvD,OAAA,CAACI,IAAI;MAACoD,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EACpCJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChBjE,OAAA;MAAA2D,QAAA,gBACE3D,OAAA;QAAKyD,KAAK,EAAE;UAAES,UAAU,EAAE;QAAI,CAAE;QAAAP,QAAA,EAAEM,MAAM,CAACE;MAAY;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5D/D,OAAA;QAAKyD,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEU,KAAK,EAAE;QAAO,CAAE;QAAAT,QAAA,EAC7CM,MAAM,CAACI;MAAa;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBkB,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBvD,OAAA,CAAClB,GAAG;MAACsF,KAAK,EAAC,MAAM;MAAAT,QAAA,EAAEJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG5B,MAAc,iBACrB1B,OAAA,CAAClB,GAAG;MAACsF,KAAK,EAAE3C,cAAc,CAACC,MAAM,CAAE;MAAAiC,QAAA,EAChCxB,aAAa,CAACT,MAAM;IAAC;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,KAAK;MACrB,IAAIA,MAAM,CAACvC,MAAM,KAAK,SAAS,IAAIuC,MAAM,CAACvC,MAAM,KAAK,WAAW,EAAE;QAChE,oBACE1B,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAKyD,KAAK,EAAE;cAAES,UAAU,EAAE,GAAG;cAAER,QAAQ,EAAE;YAAO,CAAE;YAAAC,QAAA,EAC/CM,MAAM,CAACM,gBAAgB,IAAI;UAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACN/D,OAAA;YAAKyD,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEU,KAAK,EAAE;YAAO,CAAE;YAAAT,QAAA,EAC7CM,MAAM,CAACO,cAAc,IAAI;UAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACLE,MAAM,CAACQ,SAAS,iBACfzE,OAAA;YAAKyD,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEU,KAAK,EAAE;YAAO,CAAE;YAAAT,QAAA,GAAC,gBAC3C,EAACM,MAAM,CAACQ,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEV;MACA,oBAAO/D,OAAA,CAACI,IAAI;QAACuE,IAAI,EAAC,WAAW;QAAClB,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAC,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACvE;EACF,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBvD,OAAA;MAAKyD,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EAC9BJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChBjE,OAAA,CAACpB,KAAK;MAACgG,IAAI,EAAC,OAAO;MAAAjB,QAAA,gBACjB3D,OAAA,CAACrB,MAAM;QACLgG,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE7E,OAAA,CAACN,WAAW;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBe,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACd,MAAM,CAAE;QAAAN,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/D,OAAA,CAACrB,MAAM;QACLgG,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE7E,OAAA,CAACL,YAAY;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBe,OAAO,EAAEA,CAAA,KAAME,eAAe,CAACf,MAAM,CAACgB,EAAE,CAAE;QAAAtB,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMmB,YAAY,GAAIC,MAAW,IAAK;IACpCpE,UAAU,CAAC,IAAI,CAAC;IAEhBqE,UAAU,CAAC,MAAM;MACf,IAAIC,QAAQ,GAAG,CAAC,GAAG3E,MAAM,CAAC;MAE1B,IAAIyE,MAAM,CAACG,OAAO,EAAE;QAClBD,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAACsC,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,MAAM,CAACG,OAAO,CAACC,WAAW,CAAC,CAAC,CACnE,CAAC;MACH;MAEA,IAAIJ,MAAM,CAAChB,YAAY,EAAE;QACvBkB,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAACmB,YAAY,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,MAAM,CAAChB,YAAY,CAACoB,WAAW,CAAC,CAAC,CAC7E,CAAC;MACH;MAEA,IAAIJ,MAAM,CAACM,QAAQ,EAAE;QACnBJ,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACyC,QAAQ,KAAKN,MAAM,CAACM,QAAQ,CAAC;MACzE;MAEA,IAAIN,MAAM,CAACzD,MAAM,EAAE;QACjB2D,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtB,MAAM,KAAKyD,MAAM,CAACzD,MAAM,CAAC;MACrE;MAEAb,iBAAiB,CAACwE,QAAQ,CAAC;MAC3BtE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAID,MAAM2E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMnE,UAAU,CAAC,CAAC;IAClBjC,OAAO,CAACqG,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;EAED,MAAMZ,eAAe,GAAI/B,KAAY,IAAK;IACxC3B,gBAAgB,CAAC2B,KAAK,CAAC;IACvB7B,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM6D,eAAe,GAAIC,EAAU,IAAK;IACtC3F,OAAO,CAACsG,IAAI,CAAC,QAAQX,EAAE,WAAW,CAAC;EACrC,CAAC;EAED,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBvG,OAAO,CAACsG,IAAI,CAAC,YAAY,CAAC;EAC5B,CAAC;EAED,oBACE5F,OAAA;IAAA2D,QAAA,gBACE3D,OAAA;MAAKyD,KAAK,EAAE;QAAEqC,YAAY,EAAE;MAAO,CAAE;MAAAnC,QAAA,gBACnC3D,OAAA,CAACG,KAAK;QAAC4F,KAAK,EAAE,CAAE;QAACtC,KAAK,EAAE;UAAEuC,MAAM,EAAE,CAAC;UAAE5B,KAAK,EAAE;QAAU,CAAE;QAAAT,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/D,OAAA,CAACI,IAAI;QAACuE,IAAI,EAAC,WAAW;QAAAhB,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN/D,OAAA,CAACb,GAAG;MAAC8G,MAAM,EAAE,EAAG;MAACxC,KAAK,EAAE;QAAEqC,YAAY,EAAE;MAAO,CAAE;MAAAnC,QAAA,gBAC/C3D,OAAA,CAACZ,GAAG;QAAC8G,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACX3D,OAAA,CAACvB,IAAI;UAAAkF,QAAA,eACH3D,OAAA,CAACX,SAAS;YACR6D,KAAK,EAAC,0BAAM;YACZiD,KAAK,EAAEvD,KAAK,CAACC,KAAM;YACnBuD,UAAU,EAAE;cAAEhC,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/D,OAAA,CAACZ,GAAG;QAAC8G,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACX3D,OAAA,CAACvB,IAAI;UAAAkF,QAAA,eACH3D,OAAA,CAACX,SAAS;YACR6D,KAAK,EAAC,oBAAK;YACXiD,KAAK,EAAEvD,KAAK,CAAChB,OAAQ;YACrBwE,UAAU,EAAE;cAAEhC,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/D,OAAA,CAACZ,GAAG;QAAC8G,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACX3D,OAAA,CAACvB,IAAI;UAAAkF,QAAA,eACH3D,OAAA,CAACX,SAAS;YACR6D,KAAK,EAAC,gCAAO;YACbiD,KAAK,EAAEvD,KAAK,CAACd,cAAe;YAC5BsE,UAAU,EAAE;cAAEhC,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/D,OAAA,CAACZ,GAAG;QAAC8G,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACX3D,OAAA,CAACvB,IAAI;UAAAkF,QAAA,eACH3D,OAAA,CAACX,SAAS;YACR6D,KAAK,EAAC,oBAAK;YACXiD,KAAK,EAAEvD,KAAK,CAACf,UAAW;YACxBuE,UAAU,EAAE;cAAEhC,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/D,OAAA,CAACZ,GAAG;QAAC8G,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACX3D,OAAA,CAACvB,IAAI;UAAAkF,QAAA,eACH3D,OAAA,CAACX,SAAS;YACR6D,KAAK,EAAC,oBAAK;YACXiD,KAAK,EAAEvD,KAAK,CAACb,OAAQ;YACrBqE,UAAU,EAAE;cAAEhC,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/D,OAAA,CAACZ,GAAG;QAAC8G,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACX3D,OAAA,CAACvB,IAAI;UAAAkF,QAAA,eACH3D,OAAA,CAACX,SAAS;YACR6D,KAAK,EAAC,oBAAK;YACXiD,KAAK,EAAEvD,KAAK,CAACZ,SAAU;YACvBoE,UAAU,EAAE;cAAEhC,KAAK,EAAE;YAAU;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/D,OAAA,CAACvB,IAAI;MAAAkF,QAAA,gBAEH3D,OAAA,CAACjB,IAAI;QACHyB,IAAI,EAAEA,IAAK;QACX6F,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEpB,YAAa;QACvBzB,KAAK,EAAE;UAAEqC,YAAY,EAAE;QAAG,CAAE;QAAAnC,QAAA,gBAE5B3D,OAAA,CAACjB,IAAI,CAACwH,IAAI;UAACC,IAAI,EAAC,SAAS;UAACC,KAAK,EAAC,oBAAK;UAAA9C,QAAA,eACnC3D,OAAA,CAAChB,KAAK;YAAC0H,WAAW,EAAC,sCAAQ;YAACjD,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACZ/D,OAAA,CAACjB,IAAI,CAACwH,IAAI;UAACC,IAAI,EAAC,cAAc;UAACC,KAAK,EAAC,0BAAM;UAAA9C,QAAA,eACzC3D,OAAA,CAAChB,KAAK;YAAC0H,WAAW,EAAC,4CAAS;YAACjD,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACZ/D,OAAA,CAACjB,IAAI,CAACwH,IAAI;UAACC,IAAI,EAAC,UAAU;UAACC,KAAK,EAAC,oBAAK;UAAA9C,QAAA,eACpC3D,OAAA,CAACf,MAAM;YAACyH,WAAW,EAAC,sCAAQ;YAACjD,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACjD3D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAAxC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAAxC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAAxC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAAxC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ/D,OAAA,CAACjB,IAAI,CAACwH,IAAI;UAACC,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC,0BAAM;UAAA9C,QAAA,eACnC3D,OAAA,CAACf,MAAM;YAACyH,WAAW,EAAC,gCAAO;YAACjD,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBAChD3D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,SAAS;cAAAxC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,YAAY;cAAAxC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACrD/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,gBAAgB;cAAAxC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC3D/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,SAAS;cAAAxC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,WAAW;cAAAxC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACpD/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,QAAQ;cAAAxC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD/D,OAAA,CAACf,MAAM,CAAC0H,MAAM;cAACR,KAAK,EAAC,WAAW;cAAAxC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ/D,OAAA,CAACjB,IAAI,CAACwH,IAAI;UAAA5C,QAAA,eACR3D,OAAA,CAACrB,MAAM;YAACgG,IAAI,EAAC,SAAS;YAACiC,QAAQ,EAAC,QAAQ;YAAC/B,IAAI,eAAE7E,OAAA,CAACP,cAAc;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEnE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGP/D,OAAA;QAAKyD,KAAK,EAAE;UACVoD,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBjB,YAAY,EAAE;QAChB,CAAE;QAAAnC,QAAA,gBACA3D,OAAA;UAAA2D,QAAA,eACE3D,OAAA,CAACI,IAAI;YAAC4G,MAAM;YAAArD,QAAA,GAAC,SACT,EAAC/C,cAAc,CAACkC,MAAM,EAAC,qBAC3B;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN/D,OAAA,CAACpB,KAAK;UAAA+E,QAAA,gBACJ3D,OAAA,CAACrB,MAAM;YAACmG,OAAO,EAAEY,aAAc;YAACb,IAAI,eAAE7E,OAAA,CAACH,cAAc;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAACrB,MAAM;YAACmG,OAAO,EAAEe,YAAa;YAAChB,IAAI,eAAE7E,OAAA,CAACJ,cAAc;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN/D,OAAA,CAACtB,KAAK;QACJuE,OAAO,EAAEA,OAAQ;QACjBgE,UAAU,EAAErG,cAAe;QAC3BsG,MAAM,EAAC,IAAI;QACXpG,OAAO,EAAEA,OAAQ;QACjBqG,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC3E,KAAK,EAAE4E,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAAS5E,KAAK;QAC3C,CAAE;QACF6E,YAAY,EAAE;UACZ1G,eAAe;UACf2G,QAAQ,EAAE1G;QACZ;MAAE;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP/D,OAAA,CAACT,KAAK;MACJ2D,KAAK,EAAC,0BAAM;MACZ0E,IAAI,EAAE1G,kBAAmB;MACzB2G,QAAQ,EAAEA,CAAA,KAAM1G,qBAAqB,CAAC,KAAK,CAAE;MAC7C2G,MAAM,EAAE,cACN9H,OAAA,CAACrB,MAAM;QAAamG,OAAO,EAAEA,CAAA,KAAM3D,qBAAqB,CAAC,KAAK,CAAE;QAAAwC,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFV,KAAK,EAAE,GAAI;MAAAM,QAAA,EAEVvC,aAAa,iBACZpB,OAAA,CAACR,YAAY;QAACuI,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAArE,QAAA,gBAC/B3D,OAAA,CAACR,YAAY,CAAC+G,IAAI;UAACE,KAAK,EAAC,oBAAK;UAACP,IAAI,EAAE,CAAE;UAAAvC,QAAA,EACpCvC,aAAa,CAACkE;QAAO;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA9C,QAAA,EAC5BvC,aAAa,CAAC+C;QAAY;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA9C,QAAA,EAC5BvC,aAAa,CAACiD;QAAa;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAACP,IAAI,EAAE,CAAE;UAAAvC,QAAA,EACrCvC,aAAa,CAAC6G;QAAW;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;UAACE,KAAK,EAAC,oBAAK;UAAA9C,QAAA,eAC5B3D,OAAA,CAAClB,GAAG;YAACsF,KAAK,EAAC,MAAM;YAAAT,QAAA,EAAEvC,aAAa,CAACqE;UAAQ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA9C,QAAA,eAC7B3D,OAAA,CAAClB,GAAG;YAACsF,KAAK,EAAE3C,cAAc,CAACL,aAAa,CAACM,MAAM,CAAE;YAAAiC,QAAA,EAC9CxB,aAAa,CAACf,aAAa,CAACM,MAAM;UAAC;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,EACnB,CAAC3C,aAAa,CAACM,MAAM,KAAK,SAAS,IAAIN,aAAa,CAACM,MAAM,KAAK,WAAW,kBAC1E1B,OAAA,CAAAE,SAAA;UAAAyD,QAAA,gBACE3D,OAAA,CAACR,YAAY,CAAC+G,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAA9C,QAAA,EAC5BvC,aAAa,CAACmD,gBAAgB,IAAI;UAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAA9C,QAAA,eAC7B3D,OAAA,CAACI,IAAI;cAACoD,IAAI;cAAAG,QAAA,EAAEvC,aAAa,CAACoD,cAAc,IAAI;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAA9C,QAAA,EAC5BvC,aAAa,CAACqD,SAAS,IAAI;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAA9C,QAAA,EAC5BvC,aAAa,CAAC8G,iBAAiB,IAAI;UAAI;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA,eACpB,CACH,eACD/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA9C,QAAA,EAC5BvC,aAAa,CAAC+G;QAAS;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACpB/D,OAAA,CAACR,YAAY,CAAC+G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA9C,QAAA,EAC5BvC,aAAa,CAACgH;QAAS;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACxD,EAAA,CA/dID,SAAmB;EAAA,QACRvB,IAAI,CAAC0B,OAAO,EAOVX,WAAW;AAAA;AAAAuI,EAAA,GARxB/H,SAAmB;AAiezB,eAAeA,SAAS;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
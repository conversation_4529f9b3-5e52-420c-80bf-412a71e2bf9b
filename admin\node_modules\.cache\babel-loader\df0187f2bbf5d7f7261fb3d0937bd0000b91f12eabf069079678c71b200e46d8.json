{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport cls from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport Handles from \"./Handles\";\nimport Marks from \"./Marks\";\nimport Steps from \"./Steps\";\nimport Tracks from \"./Tracks\";\nimport SliderContext from \"./context\";\nimport useDrag from \"./hooks/useDrag\";\nimport useOffset from \"./hooks/useOffset\";\nimport useRange from \"./hooks/useRange\";\n\n/**\n * New:\n * - click mark to update range value\n * - handleRender\n * - Fix handle with count not correct\n * - Fix pushable not work in some case\n * - No more FindDOMNode\n * - Move all position related style into inline style\n * - Key: up is plus, down is minus\n * - fix Key with step = null not align with marks\n * - Change range should not trigger onChange\n * - keyboard support pushable\n */\n\nvar Slider = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-slider' : _props$prefixCls,\n    className = props.className,\n    style = props.style,\n    classNames = props.classNames,\n    styles = props.styles,\n    id = props.id,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    autoFocus = props.autoFocus,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    _props$min = props.min,\n    min = _props$min === void 0 ? 0 : _props$min,\n    _props$max = props.max,\n    max = _props$max === void 0 ? 100 : _props$max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    range = props.range,\n    count = props.count,\n    onChange = props.onChange,\n    onBeforeChange = props.onBeforeChange,\n    onAfterChange = props.onAfterChange,\n    onChangeComplete = props.onChangeComplete,\n    _props$allowCross = props.allowCross,\n    allowCross = _props$allowCross === void 0 ? true : _props$allowCross,\n    _props$pushable = props.pushable,\n    pushable = _props$pushable === void 0 ? false : _props$pushable,\n    reverse = props.reverse,\n    vertical = props.vertical,\n    _props$included = props.included,\n    included = _props$included === void 0 ? true : _props$included,\n    startPoint = props.startPoint,\n    trackStyle = props.trackStyle,\n    handleStyle = props.handleStyle,\n    railStyle = props.railStyle,\n    dotStyle = props.dotStyle,\n    activeDotStyle = props.activeDotStyle,\n    marks = props.marks,\n    dots = props.dots,\n    handleRender = props.handleRender,\n    activeHandleRender = props.activeHandleRender,\n    track = props.track,\n    _props$tabIndex = props.tabIndex,\n    tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n    ariaLabelForHandle = props.ariaLabelForHandle,\n    ariaLabelledByForHandle = props.ariaLabelledByForHandle,\n    ariaRequired = props.ariaRequired,\n    ariaValueTextFormatterForHandle = props.ariaValueTextFormatterForHandle;\n  var handlesRef = React.useRef(null);\n  var containerRef = React.useRef(null);\n  var direction = React.useMemo(function () {\n    if (vertical) {\n      return reverse ? 'ttb' : 'btt';\n    }\n    return reverse ? 'rtl' : 'ltr';\n  }, [reverse, vertical]);\n\n  // ============================ Range =============================\n  var _useRange = useRange(range),\n    _useRange2 = _slicedToArray(_useRange, 5),\n    rangeEnabled = _useRange2[0],\n    rangeEditable = _useRange2[1],\n    rangeDraggableTrack = _useRange2[2],\n    minCount = _useRange2[3],\n    maxCount = _useRange2[4];\n  var mergedMin = React.useMemo(function () {\n    return isFinite(min) ? min : 0;\n  }, [min]);\n  var mergedMax = React.useMemo(function () {\n    return isFinite(max) ? max : 100;\n  }, [max]);\n\n  // ============================= Step =============================\n  var mergedStep = React.useMemo(function () {\n    return step !== null && step <= 0 ? 1 : step;\n  }, [step]);\n\n  // ============================= Push =============================\n  var mergedPush = React.useMemo(function () {\n    if (typeof pushable === 'boolean') {\n      return pushable ? mergedStep : false;\n    }\n    return pushable >= 0 ? pushable : false;\n  }, [pushable, mergedStep]);\n\n  // ============================ Marks =============================\n  var markList = React.useMemo(function () {\n    return Object.keys(marks || {}).map(function (key) {\n      var mark = marks[key];\n      var markObj = {\n        value: Number(key)\n      };\n      if (mark && _typeof(mark) === 'object' && ! /*#__PURE__*/React.isValidElement(mark) && ('label' in mark || 'style' in mark)) {\n        markObj.style = mark.style;\n        markObj.label = mark.label;\n      } else {\n        markObj.label = mark;\n      }\n      return markObj;\n    }).filter(function (_ref) {\n      var label = _ref.label;\n      return label || typeof label === 'number';\n    }).sort(function (a, b) {\n      return a.value - b.value;\n    });\n  }, [marks]);\n\n  // ============================ Format ============================\n  var _useOffset = useOffset(mergedMin, mergedMax, mergedStep, markList, allowCross, mergedPush),\n    _useOffset2 = _slicedToArray(_useOffset, 2),\n    formatValue = _useOffset2[0],\n    offsetValues = _useOffset2[1];\n\n  // ============================ Values ============================\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var rawValues = React.useMemo(function () {\n    var valueList = mergedValue === null || mergedValue === undefined ? [] : Array.isArray(mergedValue) ? mergedValue : [mergedValue];\n    var _valueList = _slicedToArray(valueList, 1),\n      _valueList$ = _valueList[0],\n      val0 = _valueList$ === void 0 ? mergedMin : _valueList$;\n    var returnValues = mergedValue === null ? [] : [val0];\n\n    // Format as range\n    if (rangeEnabled) {\n      returnValues = _toConsumableArray(valueList);\n\n      // When count provided or value is `undefined`, we fill values\n      if (count || mergedValue === undefined) {\n        var pointCount = count >= 0 ? count + 1 : 2;\n        returnValues = returnValues.slice(0, pointCount);\n\n        // Fill with count\n        while (returnValues.length < pointCount) {\n          var _returnValues;\n          returnValues.push((_returnValues = returnValues[returnValues.length - 1]) !== null && _returnValues !== void 0 ? _returnValues : mergedMin);\n        }\n      }\n      returnValues.sort(function (a, b) {\n        return a - b;\n      });\n    }\n\n    // Align in range\n    returnValues.forEach(function (val, index) {\n      returnValues[index] = formatValue(val);\n    });\n    return returnValues;\n  }, [mergedValue, rangeEnabled, mergedMin, count, formatValue]);\n\n  // =========================== onChange ===========================\n  var getTriggerValue = function getTriggerValue(triggerValues) {\n    return rangeEnabled ? triggerValues : triggerValues[0];\n  };\n  var triggerChange = useEvent(function (nextValues) {\n    // Order first\n    var cloneNextValues = _toConsumableArray(nextValues).sort(function (a, b) {\n      return a - b;\n    });\n\n    // Trigger event if needed\n    if (onChange && !isEqual(cloneNextValues, rawValues, true)) {\n      onChange(getTriggerValue(cloneNextValues));\n    }\n\n    // We set this later since it will re-render component immediately\n    setValue(cloneNextValues);\n  });\n  var finishChange = useEvent(function (draggingDelete) {\n    // Trigger from `useDrag` will tell if it's a delete action\n    if (draggingDelete) {\n      handlesRef.current.hideHelp();\n    }\n    var finishValue = getTriggerValue(rawValues);\n    onAfterChange === null || onAfterChange === void 0 || onAfterChange(finishValue);\n    warning(!onAfterChange, '[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead.');\n    onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete(finishValue);\n  });\n  var onDelete = function onDelete(index) {\n    if (disabled || !rangeEditable || rawValues.length <= minCount) {\n      return;\n    }\n    var cloneNextValues = _toConsumableArray(rawValues);\n    cloneNextValues.splice(index, 1);\n    onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(cloneNextValues));\n    triggerChange(cloneNextValues);\n    var nextFocusIndex = Math.max(0, index - 1);\n    handlesRef.current.hideHelp();\n    handlesRef.current.focus(nextFocusIndex);\n  };\n  var _useDrag = useDrag(containerRef, direction, rawValues, mergedMin, mergedMax, formatValue, triggerChange, finishChange, offsetValues, rangeEditable, minCount),\n    _useDrag2 = _slicedToArray(_useDrag, 5),\n    draggingIndex = _useDrag2[0],\n    draggingValue = _useDrag2[1],\n    draggingDelete = _useDrag2[2],\n    cacheValues = _useDrag2[3],\n    onStartDrag = _useDrag2[4];\n\n  /**\n   * When `rangeEditable` will insert a new value in the values array.\n   * Else it will replace the value in the values array.\n   */\n  var changeToCloseValue = function changeToCloseValue(newValue, e) {\n    if (!disabled) {\n      // Create new values\n      var cloneNextValues = _toConsumableArray(rawValues);\n      var valueIndex = 0;\n      var valueBeforeIndex = 0; // Record the index which value < newValue\n      var valueDist = mergedMax - mergedMin;\n      rawValues.forEach(function (val, index) {\n        var dist = Math.abs(newValue - val);\n        if (dist <= valueDist) {\n          valueDist = dist;\n          valueIndex = index;\n        }\n        if (val < newValue) {\n          valueBeforeIndex = index;\n        }\n      });\n      var focusIndex = valueIndex;\n      if (rangeEditable && valueDist !== 0 && (!maxCount || rawValues.length < maxCount)) {\n        cloneNextValues.splice(valueBeforeIndex + 1, 0, newValue);\n        focusIndex = valueBeforeIndex + 1;\n      } else {\n        cloneNextValues[valueIndex] = newValue;\n      }\n\n      // Fill value to match default 2 (only when `rawValues` is empty)\n      if (rangeEnabled && !rawValues.length && count === undefined) {\n        cloneNextValues.push(newValue);\n      }\n      var nextValue = getTriggerValue(cloneNextValues);\n      onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(nextValue);\n      triggerChange(cloneNextValues);\n      if (e) {\n        var _document$activeEleme, _document$activeEleme2;\n        (_document$activeEleme = document.activeElement) === null || _document$activeEleme === void 0 || (_document$activeEleme2 = _document$activeEleme.blur) === null || _document$activeEleme2 === void 0 || _document$activeEleme2.call(_document$activeEleme);\n        handlesRef.current.focus(focusIndex);\n        onStartDrag(e, focusIndex, cloneNextValues);\n      } else {\n        // https://github.com/ant-design/ant-design/issues/49997\n        onAfterChange === null || onAfterChange === void 0 || onAfterChange(nextValue);\n        warning(!onAfterChange, '[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead.');\n        onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete(nextValue);\n      }\n    }\n  };\n\n  // ============================ Click =============================\n  var onSliderMouseDown = function onSliderMouseDown(e) {\n    e.preventDefault();\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      width = _containerRef$current.width,\n      height = _containerRef$current.height,\n      left = _containerRef$current.left,\n      top = _containerRef$current.top,\n      bottom = _containerRef$current.bottom,\n      right = _containerRef$current.right;\n    var clientX = e.clientX,\n      clientY = e.clientY;\n    var percent;\n    switch (direction) {\n      case 'btt':\n        percent = (bottom - clientY) / height;\n        break;\n      case 'ttb':\n        percent = (clientY - top) / height;\n        break;\n      case 'rtl':\n        percent = (right - clientX) / width;\n        break;\n      default:\n        percent = (clientX - left) / width;\n    }\n    var nextValue = mergedMin + percent * (mergedMax - mergedMin);\n    changeToCloseValue(formatValue(nextValue), e);\n  };\n\n  // =========================== Keyboard ===========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    keyboardValue = _React$useState2[0],\n    setKeyboardValue = _React$useState2[1];\n  var onHandleOffsetChange = function onHandleOffsetChange(offset, valueIndex) {\n    if (!disabled) {\n      var next = offsetValues(rawValues, offset, valueIndex);\n      onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(rawValues));\n      triggerChange(next.values);\n      setKeyboardValue(next.value);\n    }\n  };\n  React.useEffect(function () {\n    if (keyboardValue !== null) {\n      var valueIndex = rawValues.indexOf(keyboardValue);\n      if (valueIndex >= 0) {\n        handlesRef.current.focus(valueIndex);\n      }\n    }\n    setKeyboardValue(null);\n  }, [keyboardValue]);\n\n  // ============================= Drag =============================\n  var mergedDraggableTrack = React.useMemo(function () {\n    if (rangeDraggableTrack && mergedStep === null) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '`draggableTrack` is not supported when `step` is `null`.');\n      }\n      return false;\n    }\n    return rangeDraggableTrack;\n  }, [rangeDraggableTrack, mergedStep]);\n  var onStartMove = useEvent(function (e, valueIndex) {\n    onStartDrag(e, valueIndex);\n    onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(rawValues));\n  });\n\n  // Auto focus for updated handle\n  var dragging = draggingIndex !== -1;\n  React.useEffect(function () {\n    if (!dragging) {\n      var valueIndex = rawValues.lastIndexOf(draggingValue);\n      handlesRef.current.focus(valueIndex);\n    }\n  }, [dragging]);\n\n  // =========================== Included ===========================\n  var sortedCacheValues = React.useMemo(function () {\n    return _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n  }, [cacheValues]);\n\n  // Provide a range values with included [min, max]\n  // Used for Track, Mark & Dot\n  var _React$useMemo = React.useMemo(function () {\n      if (!rangeEnabled) {\n        return [mergedMin, sortedCacheValues[0]];\n      }\n      return [sortedCacheValues[0], sortedCacheValues[sortedCacheValues.length - 1]];\n    }, [sortedCacheValues, rangeEnabled, mergedMin]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    includedStart = _React$useMemo2[0],\n    includedEnd = _React$useMemo2[1];\n\n  // ============================= Refs =============================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        handlesRef.current.focus(0);\n      },\n      blur: function blur() {\n        var _containerRef$current2;\n        var _document = document,\n          activeElement = _document.activeElement;\n        if ((_containerRef$current2 = containerRef.current) !== null && _containerRef$current2 !== void 0 && _containerRef$current2.contains(activeElement)) {\n          activeElement === null || activeElement === void 0 || activeElement.blur();\n        }\n      }\n    };\n  });\n\n  // ========================== Auto Focus ==========================\n  React.useEffect(function () {\n    if (autoFocus) {\n      handlesRef.current.focus(0);\n    }\n  }, []);\n\n  // =========================== Context ============================\n  var context = React.useMemo(function () {\n    return {\n      min: mergedMin,\n      max: mergedMax,\n      direction: direction,\n      disabled: disabled,\n      keyboard: keyboard,\n      step: mergedStep,\n      included: included,\n      includedStart: includedStart,\n      includedEnd: includedEnd,\n      range: rangeEnabled,\n      tabIndex: tabIndex,\n      ariaLabelForHandle: ariaLabelForHandle,\n      ariaLabelledByForHandle: ariaLabelledByForHandle,\n      ariaRequired: ariaRequired,\n      ariaValueTextFormatterForHandle: ariaValueTextFormatterForHandle,\n      styles: styles || {},\n      classNames: classNames || {}\n    };\n  }, [mergedMin, mergedMax, direction, disabled, keyboard, mergedStep, included, includedStart, includedEnd, rangeEnabled, tabIndex, ariaLabelForHandle, ariaLabelledByForHandle, ariaRequired, ariaValueTextFormatterForHandle, styles, classNames]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(SliderContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: containerRef,\n    className: cls(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), \"\".concat(prefixCls, \"-vertical\"), vertical), \"\".concat(prefixCls, \"-horizontal\"), !vertical), \"\".concat(prefixCls, \"-with-marks\"), markList.length)),\n    style: style,\n    onMouseDown: onSliderMouseDown,\n    id: id\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls(\"\".concat(prefixCls, \"-rail\"), classNames === null || classNames === void 0 ? void 0 : classNames.rail),\n    style: _objectSpread(_objectSpread({}, railStyle), styles === null || styles === void 0 ? void 0 : styles.rail)\n  }), track !== false && /*#__PURE__*/React.createElement(Tracks, {\n    prefixCls: prefixCls,\n    style: trackStyle,\n    values: rawValues,\n    startPoint: startPoint,\n    onStartMove: mergedDraggableTrack ? onStartMove : undefined\n  }), /*#__PURE__*/React.createElement(Steps, {\n    prefixCls: prefixCls,\n    marks: markList,\n    dots: dots,\n    style: dotStyle,\n    activeStyle: activeDotStyle\n  }), /*#__PURE__*/React.createElement(Handles, {\n    ref: handlesRef,\n    prefixCls: prefixCls,\n    style: handleStyle,\n    values: cacheValues,\n    draggingIndex: draggingIndex,\n    draggingDelete: draggingDelete,\n    onStartMove: onStartMove,\n    onOffsetChange: onHandleOffsetChange,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    handleRender: handleRender,\n    activeHandleRender: activeHandleRender,\n    onChangeComplete: finishChange,\n    onDelete: rangeEditable ? onDelete : undefined\n  }), /*#__PURE__*/React.createElement(Marks, {\n    prefixCls: prefixCls,\n    marks: markList,\n    onClick: changeToCloseValue\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Slider.displayName = 'Slider';\n}\nexport default Slider;", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "_toConsumableArray", "_typeof", "_slicedToArray", "cls", "useEvent", "useMergedState", "isEqual", "warning", "React", "<PERSON><PERSON>", "Marks", "Steps", "Tracks", "SliderContext", "useDrag", "useOffset", "useRange", "Slide<PERSON>", "forwardRef", "props", "ref", "_props$prefixCls", "prefixCls", "className", "style", "classNames", "styles", "id", "_props$disabled", "disabled", "_props$keyboard", "keyboard", "autoFocus", "onFocus", "onBlur", "_props$min", "min", "_props$max", "max", "_props$step", "step", "value", "defaultValue", "range", "count", "onChange", "onBeforeChange", "onAfterChange", "onChangeComplete", "_props$allowCross", "allowCross", "_props$pushable", "pushable", "reverse", "vertical", "_props$included", "included", "startPoint", "trackStyle", "handleStyle", "railStyle", "dotStyle", "activeDotStyle", "marks", "dots", "handleRender", "activeHandleRender", "track", "_props$tabIndex", "tabIndex", "ariaLabelFor<PERSON>andle", "ariaLabelledByForHandle", "ariaRequired", "ariaValueTextFormatterForHandle", "handlesRef", "useRef", "containerRef", "direction", "useMemo", "_useRange", "_useRange2", "rangeEnabled", "rangeEditable", "rangeDraggableTrack", "minCount", "maxCount", "mergedMin", "isFinite", "mergedMax", "mergedStep", "mergedPush", "markList", "Object", "keys", "map", "key", "mark", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isValidElement", "label", "filter", "_ref", "sort", "a", "b", "_useOffset", "_useOffset2", "formatValue", "offsetValues", "_useMergedState", "_useMergedState2", "mergedValue", "setValue", "rawValues", "valueList", "undefined", "Array", "isArray", "_valueList", "_valueList$", "val0", "returnV<PERSON>ues", "pointCount", "slice", "length", "_returnValues", "push", "for<PERSON>ach", "val", "index", "getTriggerValue", "triggerValues", "trigger<PERSON>hange", "nextV<PERSON>ues", "cloneNextValues", "finishChange", "draggingDelete", "current", "hideHelp", "finishValue", "onDelete", "splice", "nextFocusIndex", "Math", "focus", "_useDrag", "_useDrag2", "draggingIndex", "draggingValue", "cacheValues", "onStartDrag", "changeToCloseValue", "newValue", "e", "valueIndex", "valueBeforeIndex", "valueDist", "dist", "abs", "focusIndex", "nextValue", "_document$activeEleme", "_document$activeEleme2", "document", "activeElement", "blur", "call", "onSliderMouseDown", "preventDefault", "_containerRef$current", "getBoundingClientRect", "width", "height", "left", "top", "bottom", "right", "clientX", "clientY", "percent", "_React$useState", "useState", "_React$useState2", "keyboardValue", "setKeyboardValue", "onHandleOffsetChange", "offset", "next", "values", "useEffect", "indexOf", "mergedDraggableTrack", "process", "env", "NODE_ENV", "onStartMove", "dragging", "lastIndexOf", "sortedCacheValues", "_React$useMemo", "_React$useMemo2", "includedStart", "includedEnd", "useImperativeHandle", "_containerRef$current2", "_document", "contains", "context", "createElement", "Provider", "concat", "onMouseDown", "rail", "activeStyle", "onOffsetChange", "onClick", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-slider/es/Slider.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport cls from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport Handles from \"./Handles\";\nimport Marks from \"./Marks\";\nimport Steps from \"./Steps\";\nimport Tracks from \"./Tracks\";\nimport SliderContext from \"./context\";\nimport useDrag from \"./hooks/useDrag\";\nimport useOffset from \"./hooks/useOffset\";\nimport useRange from \"./hooks/useRange\";\n\n/**\n * New:\n * - click mark to update range value\n * - handleRender\n * - Fix handle with count not correct\n * - Fix pushable not work in some case\n * - No more FindDOMNode\n * - Move all position related style into inline style\n * - Key: up is plus, down is minus\n * - fix Key with step = null not align with marks\n * - Change range should not trigger onChange\n * - keyboard support pushable\n */\n\nvar Slider = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-slider' : _props$prefixCls,\n    className = props.className,\n    style = props.style,\n    classNames = props.classNames,\n    styles = props.styles,\n    id = props.id,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    autoFocus = props.autoFocus,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    _props$min = props.min,\n    min = _props$min === void 0 ? 0 : _props$min,\n    _props$max = props.max,\n    max = _props$max === void 0 ? 100 : _props$max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    range = props.range,\n    count = props.count,\n    onChange = props.onChange,\n    onBeforeChange = props.onBeforeChange,\n    onAfterChange = props.onAfterChange,\n    onChangeComplete = props.onChangeComplete,\n    _props$allowCross = props.allowCross,\n    allowCross = _props$allowCross === void 0 ? true : _props$allowCross,\n    _props$pushable = props.pushable,\n    pushable = _props$pushable === void 0 ? false : _props$pushable,\n    reverse = props.reverse,\n    vertical = props.vertical,\n    _props$included = props.included,\n    included = _props$included === void 0 ? true : _props$included,\n    startPoint = props.startPoint,\n    trackStyle = props.trackStyle,\n    handleStyle = props.handleStyle,\n    railStyle = props.railStyle,\n    dotStyle = props.dotStyle,\n    activeDotStyle = props.activeDotStyle,\n    marks = props.marks,\n    dots = props.dots,\n    handleRender = props.handleRender,\n    activeHandleRender = props.activeHandleRender,\n    track = props.track,\n    _props$tabIndex = props.tabIndex,\n    tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n    ariaLabelForHandle = props.ariaLabelForHandle,\n    ariaLabelledByForHandle = props.ariaLabelledByForHandle,\n    ariaRequired = props.ariaRequired,\n    ariaValueTextFormatterForHandle = props.ariaValueTextFormatterForHandle;\n  var handlesRef = React.useRef(null);\n  var containerRef = React.useRef(null);\n  var direction = React.useMemo(function () {\n    if (vertical) {\n      return reverse ? 'ttb' : 'btt';\n    }\n    return reverse ? 'rtl' : 'ltr';\n  }, [reverse, vertical]);\n\n  // ============================ Range =============================\n  var _useRange = useRange(range),\n    _useRange2 = _slicedToArray(_useRange, 5),\n    rangeEnabled = _useRange2[0],\n    rangeEditable = _useRange2[1],\n    rangeDraggableTrack = _useRange2[2],\n    minCount = _useRange2[3],\n    maxCount = _useRange2[4];\n  var mergedMin = React.useMemo(function () {\n    return isFinite(min) ? min : 0;\n  }, [min]);\n  var mergedMax = React.useMemo(function () {\n    return isFinite(max) ? max : 100;\n  }, [max]);\n\n  // ============================= Step =============================\n  var mergedStep = React.useMemo(function () {\n    return step !== null && step <= 0 ? 1 : step;\n  }, [step]);\n\n  // ============================= Push =============================\n  var mergedPush = React.useMemo(function () {\n    if (typeof pushable === 'boolean') {\n      return pushable ? mergedStep : false;\n    }\n    return pushable >= 0 ? pushable : false;\n  }, [pushable, mergedStep]);\n\n  // ============================ Marks =============================\n  var markList = React.useMemo(function () {\n    return Object.keys(marks || {}).map(function (key) {\n      var mark = marks[key];\n      var markObj = {\n        value: Number(key)\n      };\n      if (mark && _typeof(mark) === 'object' && ! /*#__PURE__*/React.isValidElement(mark) && ('label' in mark || 'style' in mark)) {\n        markObj.style = mark.style;\n        markObj.label = mark.label;\n      } else {\n        markObj.label = mark;\n      }\n      return markObj;\n    }).filter(function (_ref) {\n      var label = _ref.label;\n      return label || typeof label === 'number';\n    }).sort(function (a, b) {\n      return a.value - b.value;\n    });\n  }, [marks]);\n\n  // ============================ Format ============================\n  var _useOffset = useOffset(mergedMin, mergedMax, mergedStep, markList, allowCross, mergedPush),\n    _useOffset2 = _slicedToArray(_useOffset, 2),\n    formatValue = _useOffset2[0],\n    offsetValues = _useOffset2[1];\n\n  // ============================ Values ============================\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var rawValues = React.useMemo(function () {\n    var valueList = mergedValue === null || mergedValue === undefined ? [] : Array.isArray(mergedValue) ? mergedValue : [mergedValue];\n    var _valueList = _slicedToArray(valueList, 1),\n      _valueList$ = _valueList[0],\n      val0 = _valueList$ === void 0 ? mergedMin : _valueList$;\n    var returnValues = mergedValue === null ? [] : [val0];\n\n    // Format as range\n    if (rangeEnabled) {\n      returnValues = _toConsumableArray(valueList);\n\n      // When count provided or value is `undefined`, we fill values\n      if (count || mergedValue === undefined) {\n        var pointCount = count >= 0 ? count + 1 : 2;\n        returnValues = returnValues.slice(0, pointCount);\n\n        // Fill with count\n        while (returnValues.length < pointCount) {\n          var _returnValues;\n          returnValues.push((_returnValues = returnValues[returnValues.length - 1]) !== null && _returnValues !== void 0 ? _returnValues : mergedMin);\n        }\n      }\n      returnValues.sort(function (a, b) {\n        return a - b;\n      });\n    }\n\n    // Align in range\n    returnValues.forEach(function (val, index) {\n      returnValues[index] = formatValue(val);\n    });\n    return returnValues;\n  }, [mergedValue, rangeEnabled, mergedMin, count, formatValue]);\n\n  // =========================== onChange ===========================\n  var getTriggerValue = function getTriggerValue(triggerValues) {\n    return rangeEnabled ? triggerValues : triggerValues[0];\n  };\n  var triggerChange = useEvent(function (nextValues) {\n    // Order first\n    var cloneNextValues = _toConsumableArray(nextValues).sort(function (a, b) {\n      return a - b;\n    });\n\n    // Trigger event if needed\n    if (onChange && !isEqual(cloneNextValues, rawValues, true)) {\n      onChange(getTriggerValue(cloneNextValues));\n    }\n\n    // We set this later since it will re-render component immediately\n    setValue(cloneNextValues);\n  });\n  var finishChange = useEvent(function (draggingDelete) {\n    // Trigger from `useDrag` will tell if it's a delete action\n    if (draggingDelete) {\n      handlesRef.current.hideHelp();\n    }\n    var finishValue = getTriggerValue(rawValues);\n    onAfterChange === null || onAfterChange === void 0 || onAfterChange(finishValue);\n    warning(!onAfterChange, '[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead.');\n    onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete(finishValue);\n  });\n  var onDelete = function onDelete(index) {\n    if (disabled || !rangeEditable || rawValues.length <= minCount) {\n      return;\n    }\n    var cloneNextValues = _toConsumableArray(rawValues);\n    cloneNextValues.splice(index, 1);\n    onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(cloneNextValues));\n    triggerChange(cloneNextValues);\n    var nextFocusIndex = Math.max(0, index - 1);\n    handlesRef.current.hideHelp();\n    handlesRef.current.focus(nextFocusIndex);\n  };\n  var _useDrag = useDrag(containerRef, direction, rawValues, mergedMin, mergedMax, formatValue, triggerChange, finishChange, offsetValues, rangeEditable, minCount),\n    _useDrag2 = _slicedToArray(_useDrag, 5),\n    draggingIndex = _useDrag2[0],\n    draggingValue = _useDrag2[1],\n    draggingDelete = _useDrag2[2],\n    cacheValues = _useDrag2[3],\n    onStartDrag = _useDrag2[4];\n\n  /**\n   * When `rangeEditable` will insert a new value in the values array.\n   * Else it will replace the value in the values array.\n   */\n  var changeToCloseValue = function changeToCloseValue(newValue, e) {\n    if (!disabled) {\n      // Create new values\n      var cloneNextValues = _toConsumableArray(rawValues);\n      var valueIndex = 0;\n      var valueBeforeIndex = 0; // Record the index which value < newValue\n      var valueDist = mergedMax - mergedMin;\n      rawValues.forEach(function (val, index) {\n        var dist = Math.abs(newValue - val);\n        if (dist <= valueDist) {\n          valueDist = dist;\n          valueIndex = index;\n        }\n        if (val < newValue) {\n          valueBeforeIndex = index;\n        }\n      });\n      var focusIndex = valueIndex;\n      if (rangeEditable && valueDist !== 0 && (!maxCount || rawValues.length < maxCount)) {\n        cloneNextValues.splice(valueBeforeIndex + 1, 0, newValue);\n        focusIndex = valueBeforeIndex + 1;\n      } else {\n        cloneNextValues[valueIndex] = newValue;\n      }\n\n      // Fill value to match default 2 (only when `rawValues` is empty)\n      if (rangeEnabled && !rawValues.length && count === undefined) {\n        cloneNextValues.push(newValue);\n      }\n      var nextValue = getTriggerValue(cloneNextValues);\n      onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(nextValue);\n      triggerChange(cloneNextValues);\n      if (e) {\n        var _document$activeEleme, _document$activeEleme2;\n        (_document$activeEleme = document.activeElement) === null || _document$activeEleme === void 0 || (_document$activeEleme2 = _document$activeEleme.blur) === null || _document$activeEleme2 === void 0 || _document$activeEleme2.call(_document$activeEleme);\n        handlesRef.current.focus(focusIndex);\n        onStartDrag(e, focusIndex, cloneNextValues);\n      } else {\n        // https://github.com/ant-design/ant-design/issues/49997\n        onAfterChange === null || onAfterChange === void 0 || onAfterChange(nextValue);\n        warning(!onAfterChange, '[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead.');\n        onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete(nextValue);\n      }\n    }\n  };\n\n  // ============================ Click =============================\n  var onSliderMouseDown = function onSliderMouseDown(e) {\n    e.preventDefault();\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      width = _containerRef$current.width,\n      height = _containerRef$current.height,\n      left = _containerRef$current.left,\n      top = _containerRef$current.top,\n      bottom = _containerRef$current.bottom,\n      right = _containerRef$current.right;\n    var clientX = e.clientX,\n      clientY = e.clientY;\n    var percent;\n    switch (direction) {\n      case 'btt':\n        percent = (bottom - clientY) / height;\n        break;\n      case 'ttb':\n        percent = (clientY - top) / height;\n        break;\n      case 'rtl':\n        percent = (right - clientX) / width;\n        break;\n      default:\n        percent = (clientX - left) / width;\n    }\n    var nextValue = mergedMin + percent * (mergedMax - mergedMin);\n    changeToCloseValue(formatValue(nextValue), e);\n  };\n\n  // =========================== Keyboard ===========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    keyboardValue = _React$useState2[0],\n    setKeyboardValue = _React$useState2[1];\n  var onHandleOffsetChange = function onHandleOffsetChange(offset, valueIndex) {\n    if (!disabled) {\n      var next = offsetValues(rawValues, offset, valueIndex);\n      onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(rawValues));\n      triggerChange(next.values);\n      setKeyboardValue(next.value);\n    }\n  };\n  React.useEffect(function () {\n    if (keyboardValue !== null) {\n      var valueIndex = rawValues.indexOf(keyboardValue);\n      if (valueIndex >= 0) {\n        handlesRef.current.focus(valueIndex);\n      }\n    }\n    setKeyboardValue(null);\n  }, [keyboardValue]);\n\n  // ============================= Drag =============================\n  var mergedDraggableTrack = React.useMemo(function () {\n    if (rangeDraggableTrack && mergedStep === null) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '`draggableTrack` is not supported when `step` is `null`.');\n      }\n      return false;\n    }\n    return rangeDraggableTrack;\n  }, [rangeDraggableTrack, mergedStep]);\n  var onStartMove = useEvent(function (e, valueIndex) {\n    onStartDrag(e, valueIndex);\n    onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(rawValues));\n  });\n\n  // Auto focus for updated handle\n  var dragging = draggingIndex !== -1;\n  React.useEffect(function () {\n    if (!dragging) {\n      var valueIndex = rawValues.lastIndexOf(draggingValue);\n      handlesRef.current.focus(valueIndex);\n    }\n  }, [dragging]);\n\n  // =========================== Included ===========================\n  var sortedCacheValues = React.useMemo(function () {\n    return _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n  }, [cacheValues]);\n\n  // Provide a range values with included [min, max]\n  // Used for Track, Mark & Dot\n  var _React$useMemo = React.useMemo(function () {\n      if (!rangeEnabled) {\n        return [mergedMin, sortedCacheValues[0]];\n      }\n      return [sortedCacheValues[0], sortedCacheValues[sortedCacheValues.length - 1]];\n    }, [sortedCacheValues, rangeEnabled, mergedMin]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    includedStart = _React$useMemo2[0],\n    includedEnd = _React$useMemo2[1];\n\n  // ============================= Refs =============================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        handlesRef.current.focus(0);\n      },\n      blur: function blur() {\n        var _containerRef$current2;\n        var _document = document,\n          activeElement = _document.activeElement;\n        if ((_containerRef$current2 = containerRef.current) !== null && _containerRef$current2 !== void 0 && _containerRef$current2.contains(activeElement)) {\n          activeElement === null || activeElement === void 0 || activeElement.blur();\n        }\n      }\n    };\n  });\n\n  // ========================== Auto Focus ==========================\n  React.useEffect(function () {\n    if (autoFocus) {\n      handlesRef.current.focus(0);\n    }\n  }, []);\n\n  // =========================== Context ============================\n  var context = React.useMemo(function () {\n    return {\n      min: mergedMin,\n      max: mergedMax,\n      direction: direction,\n      disabled: disabled,\n      keyboard: keyboard,\n      step: mergedStep,\n      included: included,\n      includedStart: includedStart,\n      includedEnd: includedEnd,\n      range: rangeEnabled,\n      tabIndex: tabIndex,\n      ariaLabelForHandle: ariaLabelForHandle,\n      ariaLabelledByForHandle: ariaLabelledByForHandle,\n      ariaRequired: ariaRequired,\n      ariaValueTextFormatterForHandle: ariaValueTextFormatterForHandle,\n      styles: styles || {},\n      classNames: classNames || {}\n    };\n  }, [mergedMin, mergedMax, direction, disabled, keyboard, mergedStep, included, includedStart, includedEnd, rangeEnabled, tabIndex, ariaLabelForHandle, ariaLabelledByForHandle, ariaRequired, ariaValueTextFormatterForHandle, styles, classNames]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(SliderContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: containerRef,\n    className: cls(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), \"\".concat(prefixCls, \"-vertical\"), vertical), \"\".concat(prefixCls, \"-horizontal\"), !vertical), \"\".concat(prefixCls, \"-with-marks\"), markList.length)),\n    style: style,\n    onMouseDown: onSliderMouseDown,\n    id: id\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls(\"\".concat(prefixCls, \"-rail\"), classNames === null || classNames === void 0 ? void 0 : classNames.rail),\n    style: _objectSpread(_objectSpread({}, railStyle), styles === null || styles === void 0 ? void 0 : styles.rail)\n  }), track !== false && /*#__PURE__*/React.createElement(Tracks, {\n    prefixCls: prefixCls,\n    style: trackStyle,\n    values: rawValues,\n    startPoint: startPoint,\n    onStartMove: mergedDraggableTrack ? onStartMove : undefined\n  }), /*#__PURE__*/React.createElement(Steps, {\n    prefixCls: prefixCls,\n    marks: markList,\n    dots: dots,\n    style: dotStyle,\n    activeStyle: activeDotStyle\n  }), /*#__PURE__*/React.createElement(Handles, {\n    ref: handlesRef,\n    prefixCls: prefixCls,\n    style: handleStyle,\n    values: cacheValues,\n    draggingIndex: draggingIndex,\n    draggingDelete: draggingDelete,\n    onStartMove: onStartMove,\n    onOffsetChange: onHandleOffsetChange,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    handleRender: handleRender,\n    activeHandleRender: activeHandleRender,\n    onChangeComplete: finishChange,\n    onDelete: rangeEditable ? onDelete : undefined\n  }), /*#__PURE__*/React.createElement(Marks, {\n    prefixCls: prefixCls,\n    marks: markList,\n    onClick: changeToCloseValue\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Slider.displayName = 'Slider';\n}\nexport default Slider;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,MAAM,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,gBAAgB;IACxEE,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,EAAE,GAAGR,KAAK,CAACQ,EAAE;IACbC,eAAe,GAAGT,KAAK,CAACU,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,eAAe,GAAGX,KAAK,CAACY,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,MAAM,GAAGf,KAAK,CAACe,MAAM;IACrBC,UAAU,GAAGhB,KAAK,CAACiB,GAAG;IACtBA,GAAG,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU;IAC5CE,UAAU,GAAGlB,KAAK,CAACmB,GAAG;IACtBA,GAAG,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,UAAU;IAC9CE,WAAW,GAAGpB,KAAK,CAACqB,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;IAC/CE,KAAK,GAAGtB,KAAK,CAACsB,KAAK;IACnBC,YAAY,GAAGvB,KAAK,CAACuB,YAAY;IACjCC,KAAK,GAAGxB,KAAK,CAACwB,KAAK;IACnBC,KAAK,GAAGzB,KAAK,CAACyB,KAAK;IACnBC,QAAQ,GAAG1B,KAAK,CAAC0B,QAAQ;IACzBC,cAAc,GAAG3B,KAAK,CAAC2B,cAAc;IACrCC,aAAa,GAAG5B,KAAK,CAAC4B,aAAa;IACnCC,gBAAgB,GAAG7B,KAAK,CAAC6B,gBAAgB;IACzCC,iBAAiB,GAAG9B,KAAK,CAAC+B,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,eAAe,GAAGhC,KAAK,CAACiC,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DE,OAAO,GAAGlC,KAAK,CAACkC,OAAO;IACvBC,QAAQ,GAAGnC,KAAK,CAACmC,QAAQ;IACzBC,eAAe,GAAGpC,KAAK,CAACqC,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,UAAU,GAAGtC,KAAK,CAACsC,UAAU;IAC7BC,UAAU,GAAGvC,KAAK,CAACuC,UAAU;IAC7BC,WAAW,GAAGxC,KAAK,CAACwC,WAAW;IAC/BC,SAAS,GAAGzC,KAAK,CAACyC,SAAS;IAC3BC,QAAQ,GAAG1C,KAAK,CAAC0C,QAAQ;IACzBC,cAAc,GAAG3C,KAAK,CAAC2C,cAAc;IACrCC,KAAK,GAAG5C,KAAK,CAAC4C,KAAK;IACnBC,IAAI,GAAG7C,KAAK,CAAC6C,IAAI;IACjBC,YAAY,GAAG9C,KAAK,CAAC8C,YAAY;IACjCC,kBAAkB,GAAG/C,KAAK,CAAC+C,kBAAkB;IAC7CC,KAAK,GAAGhD,KAAK,CAACgD,KAAK;IACnBC,eAAe,GAAGjD,KAAK,CAACkD,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;IAC3DE,kBAAkB,GAAGnD,KAAK,CAACmD,kBAAkB;IAC7CC,uBAAuB,GAAGpD,KAAK,CAACoD,uBAAuB;IACvDC,YAAY,GAAGrD,KAAK,CAACqD,YAAY;IACjCC,+BAA+B,GAAGtD,KAAK,CAACsD,+BAA+B;EACzE,IAAIC,UAAU,GAAGlE,KAAK,CAACmE,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,YAAY,GAAGpE,KAAK,CAACmE,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIE,SAAS,GAAGrE,KAAK,CAACsE,OAAO,CAAC,YAAY;IACxC,IAAIxB,QAAQ,EAAE;MACZ,OAAOD,OAAO,GAAG,KAAK,GAAG,KAAK;IAChC;IACA,OAAOA,OAAO,GAAG,KAAK,GAAG,KAAK;EAChC,CAAC,EAAE,CAACA,OAAO,EAAEC,QAAQ,CAAC,CAAC;;EAEvB;EACA,IAAIyB,SAAS,GAAG/D,QAAQ,CAAC2B,KAAK,CAAC;IAC7BqC,UAAU,GAAG9E,cAAc,CAAC6E,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;IAC7BG,mBAAmB,GAAGH,UAAU,CAAC,CAAC,CAAC;IACnCI,QAAQ,GAAGJ,UAAU,CAAC,CAAC,CAAC;IACxBK,QAAQ,GAAGL,UAAU,CAAC,CAAC,CAAC;EAC1B,IAAIM,SAAS,GAAG9E,KAAK,CAACsE,OAAO,CAAC,YAAY;IACxC,OAAOS,QAAQ,CAACnD,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC;EAChC,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EACT,IAAIoD,SAAS,GAAGhF,KAAK,CAACsE,OAAO,CAAC,YAAY;IACxC,OAAOS,QAAQ,CAACjD,GAAG,CAAC,GAAGA,GAAG,GAAG,GAAG;EAClC,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;;EAET;EACA,IAAImD,UAAU,GAAGjF,KAAK,CAACsE,OAAO,CAAC,YAAY;IACzC,OAAOtC,IAAI,KAAK,IAAI,IAAIA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAGA,IAAI;EAC9C,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,IAAIkD,UAAU,GAAGlF,KAAK,CAACsE,OAAO,CAAC,YAAY;IACzC,IAAI,OAAO1B,QAAQ,KAAK,SAAS,EAAE;MACjC,OAAOA,QAAQ,GAAGqC,UAAU,GAAG,KAAK;IACtC;IACA,OAAOrC,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,KAAK;EACzC,CAAC,EAAE,CAACA,QAAQ,EAAEqC,UAAU,CAAC,CAAC;;EAE1B;EACA,IAAIE,QAAQ,GAAGnF,KAAK,CAACsE,OAAO,CAAC,YAAY;IACvC,OAAOc,MAAM,CAACC,IAAI,CAAC9B,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC+B,GAAG,CAAC,UAAUC,GAAG,EAAE;MACjD,IAAIC,IAAI,GAAGjC,KAAK,CAACgC,GAAG,CAAC;MACrB,IAAIE,OAAO,GAAG;QACZxD,KAAK,EAAEyD,MAAM,CAACH,GAAG;MACnB,CAAC;MACD,IAAIC,IAAI,IAAI/F,OAAO,CAAC+F,IAAI,CAAC,KAAK,QAAQ,IAAI,EAAE,aAAaxF,KAAK,CAAC2F,cAAc,CAACH,IAAI,CAAC,KAAK,OAAO,IAAIA,IAAI,IAAI,OAAO,IAAIA,IAAI,CAAC,EAAE;QAC3HC,OAAO,CAACzE,KAAK,GAAGwE,IAAI,CAACxE,KAAK;QAC1ByE,OAAO,CAACG,KAAK,GAAGJ,IAAI,CAACI,KAAK;MAC5B,CAAC,MAAM;QACLH,OAAO,CAACG,KAAK,GAAGJ,IAAI;MACtB;MACA,OAAOC,OAAO;IAChB,CAAC,CAAC,CAACI,MAAM,CAAC,UAAUC,IAAI,EAAE;MACxB,IAAIF,KAAK,GAAGE,IAAI,CAACF,KAAK;MACtB,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;IAC3C,CAAC,CAAC,CAACG,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACtB,OAAOD,CAAC,CAAC/D,KAAK,GAAGgE,CAAC,CAAChE,KAAK;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACsB,KAAK,CAAC,CAAC;;EAEX;EACA,IAAI2C,UAAU,GAAG3F,SAAS,CAACuE,SAAS,EAAEE,SAAS,EAAEC,UAAU,EAAEE,QAAQ,EAAEzC,UAAU,EAAEwC,UAAU,CAAC;IAC5FiB,WAAW,GAAGzG,cAAc,CAACwG,UAAU,EAAE,CAAC,CAAC;IAC3CE,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC5BE,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC;;EAE/B;EACA,IAAIG,eAAe,GAAGzG,cAAc,CAACqC,YAAY,EAAE;MAC/CD,KAAK,EAAEA;IACT,CAAC,CAAC;IACFsE,gBAAgB,GAAG7G,cAAc,CAAC4G,eAAe,EAAE,CAAC,CAAC;IACrDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,SAAS,GAAG1G,KAAK,CAACsE,OAAO,CAAC,YAAY;IACxC,IAAIqC,SAAS,GAAGH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKI,SAAS,GAAG,EAAE,GAAGC,KAAK,CAACC,OAAO,CAACN,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;IACjI,IAAIO,UAAU,GAAGrH,cAAc,CAACiH,SAAS,EAAE,CAAC,CAAC;MAC3CK,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;MAC3BE,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAGlC,SAAS,GAAGkC,WAAW;IACzD,IAAIE,YAAY,GAAGV,WAAW,KAAK,IAAI,GAAG,EAAE,GAAG,CAACS,IAAI,CAAC;;IAErD;IACA,IAAIxC,YAAY,EAAE;MAChByC,YAAY,GAAG1H,kBAAkB,CAACmH,SAAS,CAAC;;MAE5C;MACA,IAAIvE,KAAK,IAAIoE,WAAW,KAAKI,SAAS,EAAE;QACtC,IAAIO,UAAU,GAAG/E,KAAK,IAAI,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC;QAC3C8E,YAAY,GAAGA,YAAY,CAACE,KAAK,CAAC,CAAC,EAAED,UAAU,CAAC;;QAEhD;QACA,OAAOD,YAAY,CAACG,MAAM,GAAGF,UAAU,EAAE;UACvC,IAAIG,aAAa;UACjBJ,YAAY,CAACK,IAAI,CAAC,CAACD,aAAa,GAAGJ,YAAY,CAACA,YAAY,CAACG,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIC,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGxC,SAAS,CAAC;QAC7I;MACF;MACAoC,YAAY,CAACnB,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAChC,OAAOD,CAAC,GAAGC,CAAC;MACd,CAAC,CAAC;IACJ;;IAEA;IACAiB,YAAY,CAACM,OAAO,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;MACzCR,YAAY,CAACQ,KAAK,CAAC,GAAGtB,WAAW,CAACqB,GAAG,CAAC;IACxC,CAAC,CAAC;IACF,OAAOP,YAAY;EACrB,CAAC,EAAE,CAACV,WAAW,EAAE/B,YAAY,EAAEK,SAAS,EAAE1C,KAAK,EAAEgE,WAAW,CAAC,CAAC;;EAE9D;EACA,IAAIuB,eAAe,GAAG,SAASA,eAAeA,CAACC,aAAa,EAAE;IAC5D,OAAOnD,YAAY,GAAGmD,aAAa,GAAGA,aAAa,CAAC,CAAC,CAAC;EACxD,CAAC;EACD,IAAIC,aAAa,GAAGjI,QAAQ,CAAC,UAAUkI,UAAU,EAAE;IACjD;IACA,IAAIC,eAAe,GAAGvI,kBAAkB,CAACsI,UAAU,CAAC,CAAC/B,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACxE,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC,CAAC;;IAEF;IACA,IAAI5D,QAAQ,IAAI,CAACvC,OAAO,CAACiI,eAAe,EAAErB,SAAS,EAAE,IAAI,CAAC,EAAE;MAC1DrE,QAAQ,CAACsF,eAAe,CAACI,eAAe,CAAC,CAAC;IAC5C;;IAEA;IACAtB,QAAQ,CAACsB,eAAe,CAAC;EAC3B,CAAC,CAAC;EACF,IAAIC,YAAY,GAAGpI,QAAQ,CAAC,UAAUqI,cAAc,EAAE;IACpD;IACA,IAAIA,cAAc,EAAE;MAClB/D,UAAU,CAACgE,OAAO,CAACC,QAAQ,CAAC,CAAC;IAC/B;IACA,IAAIC,WAAW,GAAGT,eAAe,CAACjB,SAAS,CAAC;IAC5CnE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAAC6F,WAAW,CAAC;IAChFrI,OAAO,CAAC,CAACwC,aAAa,EAAE,mFAAmF,CAAC;IAC5GC,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAAC4F,WAAW,CAAC;EAC3F,CAAC,CAAC;EACF,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACX,KAAK,EAAE;IACtC,IAAIrG,QAAQ,IAAI,CAACqD,aAAa,IAAIgC,SAAS,CAACW,MAAM,IAAIzC,QAAQ,EAAE;MAC9D;IACF;IACA,IAAImD,eAAe,GAAGvI,kBAAkB,CAACkH,SAAS,CAAC;IACnDqB,eAAe,CAACO,MAAM,CAACZ,KAAK,EAAE,CAAC,CAAC;IAChCpF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAACqF,eAAe,CAACI,eAAe,CAAC,CAAC;IACxGF,aAAa,CAACE,eAAe,CAAC;IAC9B,IAAIQ,cAAc,GAAGC,IAAI,CAAC1G,GAAG,CAAC,CAAC,EAAE4F,KAAK,GAAG,CAAC,CAAC;IAC3CxD,UAAU,CAACgE,OAAO,CAACC,QAAQ,CAAC,CAAC;IAC7BjE,UAAU,CAACgE,OAAO,CAACO,KAAK,CAACF,cAAc,CAAC;EAC1C,CAAC;EACD,IAAIG,QAAQ,GAAGpI,OAAO,CAAC8D,YAAY,EAAEC,SAAS,EAAEqC,SAAS,EAAE5B,SAAS,EAAEE,SAAS,EAAEoB,WAAW,EAAEyB,aAAa,EAAEG,YAAY,EAAE3B,YAAY,EAAE3B,aAAa,EAAEE,QAAQ,CAAC;IAC/J+D,SAAS,GAAGjJ,cAAc,CAACgJ,QAAQ,EAAE,CAAC,CAAC;IACvCE,aAAa,GAAGD,SAAS,CAAC,CAAC,CAAC;IAC5BE,aAAa,GAAGF,SAAS,CAAC,CAAC,CAAC;IAC5BV,cAAc,GAAGU,SAAS,CAAC,CAAC,CAAC;IAC7BG,WAAW,GAAGH,SAAS,CAAC,CAAC,CAAC;IAC1BI,WAAW,GAAGJ,SAAS,CAAC,CAAC,CAAC;;EAE5B;AACF;AACA;AACA;EACE,IAAIK,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAEC,CAAC,EAAE;IAChE,IAAI,CAAC7H,QAAQ,EAAE;MACb;MACA,IAAI0G,eAAe,GAAGvI,kBAAkB,CAACkH,SAAS,CAAC;MACnD,IAAIyC,UAAU,GAAG,CAAC;MAClB,IAAIC,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAC1B,IAAIC,SAAS,GAAGrE,SAAS,GAAGF,SAAS;MACrC4B,SAAS,CAACc,OAAO,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;QACtC,IAAI4B,IAAI,GAAGd,IAAI,CAACe,GAAG,CAACN,QAAQ,GAAGxB,GAAG,CAAC;QACnC,IAAI6B,IAAI,IAAID,SAAS,EAAE;UACrBA,SAAS,GAAGC,IAAI;UAChBH,UAAU,GAAGzB,KAAK;QACpB;QACA,IAAID,GAAG,GAAGwB,QAAQ,EAAE;UAClBG,gBAAgB,GAAG1B,KAAK;QAC1B;MACF,CAAC,CAAC;MACF,IAAI8B,UAAU,GAAGL,UAAU;MAC3B,IAAIzE,aAAa,IAAI2E,SAAS,KAAK,CAAC,KAAK,CAACxE,QAAQ,IAAI6B,SAAS,CAACW,MAAM,GAAGxC,QAAQ,CAAC,EAAE;QAClFkD,eAAe,CAACO,MAAM,CAACc,gBAAgB,GAAG,CAAC,EAAE,CAAC,EAAEH,QAAQ,CAAC;QACzDO,UAAU,GAAGJ,gBAAgB,GAAG,CAAC;MACnC,CAAC,MAAM;QACLrB,eAAe,CAACoB,UAAU,CAAC,GAAGF,QAAQ;MACxC;;MAEA;MACA,IAAIxE,YAAY,IAAI,CAACiC,SAAS,CAACW,MAAM,IAAIjF,KAAK,KAAKwE,SAAS,EAAE;QAC5DmB,eAAe,CAACR,IAAI,CAAC0B,QAAQ,CAAC;MAChC;MACA,IAAIQ,SAAS,GAAG9B,eAAe,CAACI,eAAe,CAAC;MAChDzF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAACmH,SAAS,CAAC;MACjF5B,aAAa,CAACE,eAAe,CAAC;MAC9B,IAAImB,CAAC,EAAE;QACL,IAAIQ,qBAAqB,EAAEC,sBAAsB;QACjD,CAACD,qBAAqB,GAAGE,QAAQ,CAACC,aAAa,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,IAAI,CAACC,sBAAsB,GAAGD,qBAAqB,CAACI,IAAI,MAAM,IAAI,IAAIH,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAACI,IAAI,CAACL,qBAAqB,CAAC;QAC1PxF,UAAU,CAACgE,OAAO,CAACO,KAAK,CAACe,UAAU,CAAC;QACpCT,WAAW,CAACG,CAAC,EAAEM,UAAU,EAAEzB,eAAe,CAAC;MAC7C,CAAC,MAAM;QACL;QACAxF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAACkH,SAAS,CAAC;QAC9E1J,OAAO,CAAC,CAACwC,aAAa,EAAE,mFAAmF,CAAC;QAC5GC,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAACiH,SAAS,CAAC;MACzF;IACF;EACF,CAAC;;EAED;EACA,IAAIO,iBAAiB,GAAG,SAASA,iBAAiBA,CAACd,CAAC,EAAE;IACpDA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClB,IAAIC,qBAAqB,GAAG9F,YAAY,CAAC8D,OAAO,CAACiC,qBAAqB,CAAC,CAAC;MACtEC,KAAK,GAAGF,qBAAqB,CAACE,KAAK;MACnCC,MAAM,GAAGH,qBAAqB,CAACG,MAAM;MACrCC,IAAI,GAAGJ,qBAAqB,CAACI,IAAI;MACjCC,GAAG,GAAGL,qBAAqB,CAACK,GAAG;MAC/BC,MAAM,GAAGN,qBAAqB,CAACM,MAAM;MACrCC,KAAK,GAAGP,qBAAqB,CAACO,KAAK;IACrC,IAAIC,OAAO,GAAGxB,CAAC,CAACwB,OAAO;MACrBC,OAAO,GAAGzB,CAAC,CAACyB,OAAO;IACrB,IAAIC,OAAO;IACX,QAAQvG,SAAS;MACf,KAAK,KAAK;QACRuG,OAAO,GAAG,CAACJ,MAAM,GAAGG,OAAO,IAAIN,MAAM;QACrC;MACF,KAAK,KAAK;QACRO,OAAO,GAAG,CAACD,OAAO,GAAGJ,GAAG,IAAIF,MAAM;QAClC;MACF,KAAK,KAAK;QACRO,OAAO,GAAG,CAACH,KAAK,GAAGC,OAAO,IAAIN,KAAK;QACnC;MACF;QACEQ,OAAO,GAAG,CAACF,OAAO,GAAGJ,IAAI,IAAIF,KAAK;IACtC;IACA,IAAIX,SAAS,GAAG3E,SAAS,GAAG8F,OAAO,IAAI5F,SAAS,GAAGF,SAAS,CAAC;IAC7DkE,kBAAkB,CAAC5C,WAAW,CAACqD,SAAS,CAAC,EAAEP,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,IAAI2B,eAAe,GAAG7K,KAAK,CAAC8K,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAGrL,cAAc,CAACmL,eAAe,EAAE,CAAC,CAAC;IACrDG,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,MAAM,EAAEhC,UAAU,EAAE;IAC3E,IAAI,CAAC9H,QAAQ,EAAE;MACb,IAAI+J,IAAI,GAAG/E,YAAY,CAACK,SAAS,EAAEyE,MAAM,EAAEhC,UAAU,CAAC;MACtD7G,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAACqF,eAAe,CAACjB,SAAS,CAAC,CAAC;MAClGmB,aAAa,CAACuD,IAAI,CAACC,MAAM,CAAC;MAC1BJ,gBAAgB,CAACG,IAAI,CAACnJ,KAAK,CAAC;IAC9B;EACF,CAAC;EACDjC,KAAK,CAACsL,SAAS,CAAC,YAAY;IAC1B,IAAIN,aAAa,KAAK,IAAI,EAAE;MAC1B,IAAI7B,UAAU,GAAGzC,SAAS,CAAC6E,OAAO,CAACP,aAAa,CAAC;MACjD,IAAI7B,UAAU,IAAI,CAAC,EAAE;QACnBjF,UAAU,CAACgE,OAAO,CAACO,KAAK,CAACU,UAAU,CAAC;MACtC;IACF;IACA8B,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,CAACD,aAAa,CAAC,CAAC;;EAEnB;EACA,IAAIQ,oBAAoB,GAAGxL,KAAK,CAACsE,OAAO,CAAC,YAAY;IACnD,IAAIK,mBAAmB,IAAIM,UAAU,KAAK,IAAI,EAAE;MAC9C,IAAIwG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC5L,OAAO,CAAC,KAAK,EAAE,0DAA0D,CAAC;MAC5E;MACA,OAAO,KAAK;IACd;IACA,OAAO4E,mBAAmB;EAC5B,CAAC,EAAE,CAACA,mBAAmB,EAAEM,UAAU,CAAC,CAAC;EACrC,IAAI2G,WAAW,GAAGhM,QAAQ,CAAC,UAAUsJ,CAAC,EAAEC,UAAU,EAAE;IAClDJ,WAAW,CAACG,CAAC,EAAEC,UAAU,CAAC;IAC1B7G,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAACqF,eAAe,CAACjB,SAAS,CAAC,CAAC;EACpG,CAAC,CAAC;;EAEF;EACA,IAAImF,QAAQ,GAAGjD,aAAa,KAAK,CAAC,CAAC;EACnC5I,KAAK,CAACsL,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACO,QAAQ,EAAE;MACb,IAAI1C,UAAU,GAAGzC,SAAS,CAACoF,WAAW,CAACjD,aAAa,CAAC;MACrD3E,UAAU,CAACgE,OAAO,CAACO,KAAK,CAACU,UAAU,CAAC;IACtC;EACF,CAAC,EAAE,CAAC0C,QAAQ,CAAC,CAAC;;EAEd;EACA,IAAIE,iBAAiB,GAAG/L,KAAK,CAACsE,OAAO,CAAC,YAAY;IAChD,OAAO9E,kBAAkB,CAACsJ,WAAW,CAAC,CAAC/C,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAC1D,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC6C,WAAW,CAAC,CAAC;;EAEjB;EACA;EACA,IAAIkD,cAAc,GAAGhM,KAAK,CAACsE,OAAO,CAAC,YAAY;MAC3C,IAAI,CAACG,YAAY,EAAE;QACjB,OAAO,CAACK,SAAS,EAAEiH,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC1C;MACA,OAAO,CAACA,iBAAiB,CAAC,CAAC,CAAC,EAAEA,iBAAiB,CAACA,iBAAiB,CAAC1E,MAAM,GAAG,CAAC,CAAC,CAAC;IAChF,CAAC,EAAE,CAAC0E,iBAAiB,EAAEtH,YAAY,EAAEK,SAAS,CAAC,CAAC;IAChDmH,eAAe,GAAGvM,cAAc,CAACsM,cAAc,EAAE,CAAC,CAAC;IACnDE,aAAa,GAAGD,eAAe,CAAC,CAAC,CAAC;IAClCE,WAAW,GAAGF,eAAe,CAAC,CAAC,CAAC;;EAElC;EACAjM,KAAK,CAACoM,mBAAmB,CAACxL,GAAG,EAAE,YAAY;IACzC,OAAO;MACL6H,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtBvE,UAAU,CAACgE,OAAO,CAACO,KAAK,CAAC,CAAC,CAAC;MAC7B,CAAC;MACDqB,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAIuC,sBAAsB;QAC1B,IAAIC,SAAS,GAAG1C,QAAQ;UACtBC,aAAa,GAAGyC,SAAS,CAACzC,aAAa;QACzC,IAAI,CAACwC,sBAAsB,GAAGjI,YAAY,CAAC8D,OAAO,MAAM,IAAI,IAAImE,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAACE,QAAQ,CAAC1C,aAAa,CAAC,EAAE;UACnJA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAACC,IAAI,CAAC,CAAC;QAC5E;MACF;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA9J,KAAK,CAACsL,SAAS,CAAC,YAAY;IAC1B,IAAI9J,SAAS,EAAE;MACb0C,UAAU,CAACgE,OAAO,CAACO,KAAK,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI+D,OAAO,GAAGxM,KAAK,CAACsE,OAAO,CAAC,YAAY;IACtC,OAAO;MACL1C,GAAG,EAAEkD,SAAS;MACdhD,GAAG,EAAEkD,SAAS;MACdX,SAAS,EAAEA,SAAS;MACpBhD,QAAQ,EAAEA,QAAQ;MAClBE,QAAQ,EAAEA,QAAQ;MAClBS,IAAI,EAAEiD,UAAU;MAChBjC,QAAQ,EAAEA,QAAQ;MAClBkJ,aAAa,EAAEA,aAAa;MAC5BC,WAAW,EAAEA,WAAW;MACxBhK,KAAK,EAAEsC,YAAY;MACnBZ,QAAQ,EAAEA,QAAQ;MAClBC,kBAAkB,EAAEA,kBAAkB;MACtCC,uBAAuB,EAAEA,uBAAuB;MAChDC,YAAY,EAAEA,YAAY;MAC1BC,+BAA+B,EAAEA,+BAA+B;MAChE/C,MAAM,EAAEA,MAAM,IAAI,CAAC,CAAC;MACpBD,UAAU,EAAEA,UAAU,IAAI,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,CAAC6D,SAAS,EAAEE,SAAS,EAAEX,SAAS,EAAEhD,QAAQ,EAAEE,QAAQ,EAAE0D,UAAU,EAAEjC,QAAQ,EAAEkJ,aAAa,EAAEC,WAAW,EAAE1H,YAAY,EAAEZ,QAAQ,EAAEC,kBAAkB,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,+BAA+B,EAAE/C,MAAM,EAAED,UAAU,CAAC,CAAC;;EAEnP;EACA,OAAO,aAAajB,KAAK,CAACyM,aAAa,CAACpM,aAAa,CAACqM,QAAQ,EAAE;IAC9DzK,KAAK,EAAEuK;EACT,CAAC,EAAE,aAAaxM,KAAK,CAACyM,aAAa,CAAC,KAAK,EAAE;IACzC7L,GAAG,EAAEwD,YAAY;IACjBrD,SAAS,EAAEpB,GAAG,CAACmB,SAAS,EAAEC,SAAS,EAAExB,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoN,MAAM,CAAC7L,SAAS,EAAE,WAAW,CAAC,EAAEO,QAAQ,CAAC,EAAE,EAAE,CAACsL,MAAM,CAAC7L,SAAS,EAAE,WAAW,CAAC,EAAEgC,QAAQ,CAAC,EAAE,EAAE,CAAC6J,MAAM,CAAC7L,SAAS,EAAE,aAAa,CAAC,EAAE,CAACgC,QAAQ,CAAC,EAAE,EAAE,CAAC6J,MAAM,CAAC7L,SAAS,EAAE,aAAa,CAAC,EAAEqE,QAAQ,CAACkC,MAAM,CAAC,CAAC;IAC5SrG,KAAK,EAAEA,KAAK;IACZ4L,WAAW,EAAE5C,iBAAiB;IAC9B7I,EAAE,EAAEA;EACN,CAAC,EAAE,aAAanB,KAAK,CAACyM,aAAa,CAAC,KAAK,EAAE;IACzC1L,SAAS,EAAEpB,GAAG,CAAC,EAAE,CAACgN,MAAM,CAAC7L,SAAS,EAAE,OAAO,CAAC,EAAEG,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC4L,IAAI,CAAC;IACtH7L,KAAK,EAAE1B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8D,SAAS,CAAC,EAAElC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2L,IAAI;EAChH,CAAC,CAAC,EAAElJ,KAAK,KAAK,KAAK,IAAI,aAAa3D,KAAK,CAACyM,aAAa,CAACrM,MAAM,EAAE;IAC9DU,SAAS,EAAEA,SAAS;IACpBE,KAAK,EAAEkC,UAAU;IACjBmI,MAAM,EAAE3E,SAAS;IACjBzD,UAAU,EAAEA,UAAU;IACtB2I,WAAW,EAAEJ,oBAAoB,GAAGI,WAAW,GAAGhF;EACpD,CAAC,CAAC,EAAE,aAAa5G,KAAK,CAACyM,aAAa,CAACtM,KAAK,EAAE;IAC1CW,SAAS,EAAEA,SAAS;IACpByC,KAAK,EAAE4B,QAAQ;IACf3B,IAAI,EAAEA,IAAI;IACVxC,KAAK,EAAEqC,QAAQ;IACfyJ,WAAW,EAAExJ;EACf,CAAC,CAAC,EAAE,aAAatD,KAAK,CAACyM,aAAa,CAACxM,OAAO,EAAE;IAC5CW,GAAG,EAAEsD,UAAU;IACfpD,SAAS,EAAEA,SAAS;IACpBE,KAAK,EAAEmC,WAAW;IAClBkI,MAAM,EAAEvC,WAAW;IACnBF,aAAa,EAAEA,aAAa;IAC5BX,cAAc,EAAEA,cAAc;IAC9B2D,WAAW,EAAEA,WAAW;IACxBmB,cAAc,EAAE7B,oBAAoB;IACpCzJ,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACd+B,YAAY,EAAEA,YAAY;IAC1BC,kBAAkB,EAAEA,kBAAkB;IACtClB,gBAAgB,EAAEwF,YAAY;IAC9BK,QAAQ,EAAE3D,aAAa,GAAG2D,QAAQ,GAAGzB;EACvC,CAAC,CAAC,EAAE,aAAa5G,KAAK,CAACyM,aAAa,CAACvM,KAAK,EAAE;IAC1CY,SAAS,EAAEA,SAAS;IACpByC,KAAK,EAAE4B,QAAQ;IACf6H,OAAO,EAAEhE;EACX,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,IAAIyC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClL,MAAM,CAACwM,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAexM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
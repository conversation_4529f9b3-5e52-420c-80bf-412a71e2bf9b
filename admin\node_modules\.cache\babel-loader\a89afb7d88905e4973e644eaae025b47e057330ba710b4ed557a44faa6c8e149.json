{"ast": null, "code": "\"use client\";\n\nimport omit from \"rc-util/es/omit\";\nimport genPurePanel from '../_util/PurePanel';\nimport Select from '../select';\nimport RefAutoComplete from './AutoComplete';\nconst {\n  Option\n} = Select;\n// We don't care debug panel\n/* istanbul ignore next */\nconst PurePanel = genPurePanel(RefAutoComplete, 'dropdownAlign', props => omit(props, ['visible']));\nconst AutoComplete = RefAutoComplete;\nAutoComplete.Option = Option;\nAutoComplete._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default AutoComplete;", "map": {"version": 3, "names": ["omit", "genPurePanel", "Select", "RefAutoComplete", "Option", "PurePanel", "props", "AutoComplete", "_InternalPanelDoNotUseOrYouWillBeFired"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/auto-complete/index.js"], "sourcesContent": ["\"use client\";\n\nimport omit from \"rc-util/es/omit\";\nimport genPurePanel from '../_util/PurePanel';\nimport Select from '../select';\nimport RefAutoComplete from './AutoComplete';\nconst {\n  Option\n} = Select;\n// We don't care debug panel\n/* istanbul ignore next */\nconst PurePanel = genPurePanel(RefAutoComplete, 'dropdownAlign', props => omit(props, ['visible']));\nconst AutoComplete = RefAutoComplete;\nAutoComplete.Option = Option;\nAutoComplete._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default AutoComplete;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,IAAI,MAAM,iBAAiB;AAClC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,eAAe,MAAM,gBAAgB;AAC5C,MAAM;EACJC;AACF,CAAC,GAAGF,MAAM;AACV;AACA;AACA,MAAMG,SAAS,GAAGJ,YAAY,CAACE,eAAe,EAAE,eAAe,EAAEG,KAAK,IAAIN,IAAI,CAACM,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;AACnG,MAAMC,YAAY,GAAGJ,eAAe;AACpCI,YAAY,CAACH,MAAM,GAAGA,MAAM;AAC5BG,YAAY,CAACC,sCAAsC,GAAGH,SAAS;AAC/D,eAAeE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
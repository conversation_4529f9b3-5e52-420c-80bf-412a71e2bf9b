{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Pagination from '../pagination';\nimport ListItem from './ListItem';\nexport const OmitProps = ['handleFilter', 'handleClear', 'checkedKeys'];\nconst parsePagination = pagination => {\n  const defaultPagination = {\n    simple: true,\n    showSizeChanger: false,\n    showLessItems: false\n  };\n  return Object.assign(Object.assign({}, defaultPagination), pagination);\n};\nconst TransferListBody = (props, ref) => {\n  const {\n    prefixCls,\n    filteredRenderItems,\n    selectedKeys,\n    disabled: globalDisabled,\n    showRemove,\n    pagination,\n    onScroll,\n    onItemSelect,\n    onItemRemove\n  } = props;\n  const [current, setCurrent] = React.useState(1);\n  const mergedPagination = React.useMemo(() => {\n    if (!pagination) {\n      return null;\n    }\n    const convertPagination = typeof pagination === 'object' ? pagination : {};\n    return parsePagination(convertPagination);\n  }, [pagination]);\n  const [pageSize, setPageSize] = useMergedState(10, {\n    value: mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize\n  });\n  React.useEffect(() => {\n    if (mergedPagination) {\n      const maxPageCount = Math.ceil(filteredRenderItems.length / pageSize);\n      setCurrent(Math.min(current, maxPageCount));\n    }\n  }, [filteredRenderItems, mergedPagination, pageSize]);\n  const onInternalClick = (item, e) => {\n    onItemSelect(item.key, !selectedKeys.includes(item.key), e);\n  };\n  const onRemove = item => {\n    onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove([item.key]);\n  };\n  const onPageChange = cur => {\n    setCurrent(cur);\n  };\n  const onSizeChange = (cur, size) => {\n    setCurrent(cur);\n    setPageSize(size);\n  };\n  const memoizedItems = React.useMemo(() => {\n    const displayItems = mergedPagination ? filteredRenderItems.slice((current - 1) * pageSize, current * pageSize) : filteredRenderItems;\n    return displayItems;\n  }, [current, filteredRenderItems, mergedPagination, pageSize]);\n  React.useImperativeHandle(ref, () => ({\n    items: memoizedItems\n  }));\n  const paginationNode = mergedPagination ? (/*#__PURE__*/React.createElement(Pagination, {\n    size: \"small\",\n    disabled: globalDisabled,\n    simple: mergedPagination.simple,\n    pageSize: pageSize,\n    showLessItems: mergedPagination.showLessItems,\n    showSizeChanger: mergedPagination.showSizeChanger,\n    className: `${prefixCls}-pagination`,\n    total: filteredRenderItems.length,\n    current: current,\n    onChange: onPageChange,\n    onShowSizeChange: onSizeChange\n  })) : null;\n  const cls = classNames(`${prefixCls}-content`, {\n    [`${prefixCls}-content-show-remove`]: showRemove\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"ul\", {\n    className: cls,\n    onScroll: onScroll\n  }, (memoizedItems || []).map(({\n    renderedEl,\n    renderedText,\n    item\n  }) => (/*#__PURE__*/React.createElement(ListItem, {\n    key: item.key,\n    item: item,\n    renderedText: renderedText,\n    renderedEl: renderedEl,\n    prefixCls: prefixCls,\n    showRemove: showRemove,\n    onClick: onInternalClick,\n    onRemove: onRemove,\n    checked: selectedKeys.includes(item.key),\n    disabled: globalDisabled || item.disabled\n  })))), paginationNode);\n};\nif (process.env.NODE_ENV !== 'production') {\n  TransferListBody.displayName = 'TransferListBody';\n}\nexport default /*#__PURE__*/React.forwardRef(TransferListBody);", "map": {"version": 3, "names": ["React", "classNames", "useMergedState", "Pagination", "ListItem", "OmitProps", "parsePagination", "pagination", "defaultPagination", "simple", "showSizeChanger", "showLessItems", "Object", "assign", "TransferListBody", "props", "ref", "prefixCls", "filteredRenderItems", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "globalDisabled", "showRemove", "onScroll", "onItemSelect", "onItemRemove", "current", "setCurrent", "useState", "mergedPagination", "useMemo", "convertPagination", "pageSize", "setPageSize", "value", "useEffect", "maxPageCount", "Math", "ceil", "length", "min", "onInternalClick", "item", "e", "key", "includes", "onRemove", "onPageChange", "cur", "onSizeChange", "size", "memoizedItems", "displayItems", "slice", "useImperativeHandle", "items", "paginationNode", "createElement", "className", "total", "onChange", "onShowSizeChange", "cls", "Fragment", "map", "renderedEl", "renderedText", "onClick", "checked", "process", "env", "NODE_ENV", "displayName", "forwardRef"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/transfer/ListBody.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Pagination from '../pagination';\nimport ListItem from './ListItem';\nexport const OmitProps = ['handleFilter', 'handleClear', 'checkedKeys'];\nconst parsePagination = pagination => {\n  const defaultPagination = {\n    simple: true,\n    showSizeChanger: false,\n    showLessItems: false\n  };\n  return Object.assign(Object.assign({}, defaultPagination), pagination);\n};\nconst TransferListBody = (props, ref) => {\n  const {\n    prefixCls,\n    filteredRenderItems,\n    selectedKeys,\n    disabled: globalDisabled,\n    showRemove,\n    pagination,\n    onScroll,\n    onItemSelect,\n    onItemRemove\n  } = props;\n  const [current, setCurrent] = React.useState(1);\n  const mergedPagination = React.useMemo(() => {\n    if (!pagination) {\n      return null;\n    }\n    const convertPagination = typeof pagination === 'object' ? pagination : {};\n    return parsePagination(convertPagination);\n  }, [pagination]);\n  const [pageSize, setPageSize] = useMergedState(10, {\n    value: mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize\n  });\n  React.useEffect(() => {\n    if (mergedPagination) {\n      const maxPageCount = Math.ceil(filteredRenderItems.length / pageSize);\n      setCurrent(Math.min(current, maxPageCount));\n    }\n  }, [filteredRenderItems, mergedPagination, pageSize]);\n  const onInternalClick = (item, e) => {\n    onItemSelect(item.key, !selectedKeys.includes(item.key), e);\n  };\n  const onRemove = item => {\n    onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove([item.key]);\n  };\n  const onPageChange = cur => {\n    setCurrent(cur);\n  };\n  const onSizeChange = (cur, size) => {\n    setCurrent(cur);\n    setPageSize(size);\n  };\n  const memoizedItems = React.useMemo(() => {\n    const displayItems = mergedPagination ? filteredRenderItems.slice((current - 1) * pageSize, current * pageSize) : filteredRenderItems;\n    return displayItems;\n  }, [current, filteredRenderItems, mergedPagination, pageSize]);\n  React.useImperativeHandle(ref, () => ({\n    items: memoizedItems\n  }));\n  const paginationNode = mergedPagination ? (/*#__PURE__*/React.createElement(Pagination, {\n    size: \"small\",\n    disabled: globalDisabled,\n    simple: mergedPagination.simple,\n    pageSize: pageSize,\n    showLessItems: mergedPagination.showLessItems,\n    showSizeChanger: mergedPagination.showSizeChanger,\n    className: `${prefixCls}-pagination`,\n    total: filteredRenderItems.length,\n    current: current,\n    onChange: onPageChange,\n    onShowSizeChange: onSizeChange\n  })) : null;\n  const cls = classNames(`${prefixCls}-content`, {\n    [`${prefixCls}-content-show-remove`]: showRemove\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"ul\", {\n    className: cls,\n    onScroll: onScroll\n  }, (memoizedItems || []).map(({\n    renderedEl,\n    renderedText,\n    item\n  }) => (/*#__PURE__*/React.createElement(ListItem, {\n    key: item.key,\n    item: item,\n    renderedText: renderedText,\n    renderedEl: renderedEl,\n    prefixCls: prefixCls,\n    showRemove: showRemove,\n    onClick: onInternalClick,\n    onRemove: onRemove,\n    checked: selectedKeys.includes(item.key),\n    disabled: globalDisabled || item.disabled\n  })))), paginationNode);\n};\nif (process.env.NODE_ENV !== 'production') {\n  TransferListBody.displayName = 'TransferListBody';\n}\nexport default /*#__PURE__*/React.forwardRef(TransferListBody);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAO,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC;AACvE,MAAMC,eAAe,GAAGC,UAAU,IAAI;EACpC,MAAMC,iBAAiB,GAAG;IACxBC,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE,KAAK;IACtBC,aAAa,EAAE;EACjB,CAAC;EACD,OAAOC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,iBAAiB,CAAC,EAAED,UAAU,CAAC;AACxE,CAAC;AACD,MAAMO,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EACvC,MAAM;IACJC,SAAS;IACTC,mBAAmB;IACnBC,YAAY;IACZC,QAAQ,EAAEC,cAAc;IACxBC,UAAU;IACVf,UAAU;IACVgB,QAAQ;IACRC,YAAY;IACZC;EACF,CAAC,GAAGV,KAAK;EACT,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMC,gBAAgB,GAAG7B,KAAK,CAAC8B,OAAO,CAAC,MAAM;IAC3C,IAAI,CAACvB,UAAU,EAAE;MACf,OAAO,IAAI;IACb;IACA,MAAMwB,iBAAiB,GAAG,OAAOxB,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,CAAC,CAAC;IAC1E,OAAOD,eAAe,CAACyB,iBAAiB,CAAC;EAC3C,CAAC,EAAE,CAACxB,UAAU,CAAC,CAAC;EAChB,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,cAAc,CAAC,EAAE,EAAE;IACjDgC,KAAK,EAAEL,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACG;EAC9F,CAAC,CAAC;EACFhC,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIN,gBAAgB,EAAE;MACpB,MAAMO,YAAY,GAAGC,IAAI,CAACC,IAAI,CAACpB,mBAAmB,CAACqB,MAAM,GAAGP,QAAQ,CAAC;MACrEL,UAAU,CAACU,IAAI,CAACG,GAAG,CAACd,OAAO,EAAEU,YAAY,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,CAAClB,mBAAmB,EAAEW,gBAAgB,EAAEG,QAAQ,CAAC,CAAC;EACrD,MAAMS,eAAe,GAAGA,CAACC,IAAI,EAAEC,CAAC,KAAK;IACnCnB,YAAY,CAACkB,IAAI,CAACE,GAAG,EAAE,CAACzB,YAAY,CAAC0B,QAAQ,CAACH,IAAI,CAACE,GAAG,CAAC,EAAED,CAAC,CAAC;EAC7D,CAAC;EACD,MAAMG,QAAQ,GAAGJ,IAAI,IAAI;IACvBjB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAACiB,IAAI,CAACE,GAAG,CAAC,CAAC;EACtF,CAAC;EACD,MAAMG,YAAY,GAAGC,GAAG,IAAI;IAC1BrB,UAAU,CAACqB,GAAG,CAAC;EACjB,CAAC;EACD,MAAMC,YAAY,GAAGA,CAACD,GAAG,EAAEE,IAAI,KAAK;IAClCvB,UAAU,CAACqB,GAAG,CAAC;IACff,WAAW,CAACiB,IAAI,CAAC;EACnB,CAAC;EACD,MAAMC,aAAa,GAAGnD,KAAK,CAAC8B,OAAO,CAAC,MAAM;IACxC,MAAMsB,YAAY,GAAGvB,gBAAgB,GAAGX,mBAAmB,CAACmC,KAAK,CAAC,CAAC3B,OAAO,GAAG,CAAC,IAAIM,QAAQ,EAAEN,OAAO,GAAGM,QAAQ,CAAC,GAAGd,mBAAmB;IACrI,OAAOkC,YAAY;EACrB,CAAC,EAAE,CAAC1B,OAAO,EAAER,mBAAmB,EAAEW,gBAAgB,EAAEG,QAAQ,CAAC,CAAC;EAC9DhC,KAAK,CAACsD,mBAAmB,CAACtC,GAAG,EAAE,OAAO;IACpCuC,KAAK,EAAEJ;EACT,CAAC,CAAC,CAAC;EACH,MAAMK,cAAc,GAAG3B,gBAAgB,IAAI,aAAa7B,KAAK,CAACyD,aAAa,CAACtD,UAAU,EAAE;IACtF+C,IAAI,EAAE,OAAO;IACb9B,QAAQ,EAAEC,cAAc;IACxBZ,MAAM,EAAEoB,gBAAgB,CAACpB,MAAM;IAC/BuB,QAAQ,EAAEA,QAAQ;IAClBrB,aAAa,EAAEkB,gBAAgB,CAAClB,aAAa;IAC7CD,eAAe,EAAEmB,gBAAgB,CAACnB,eAAe;IACjDgD,SAAS,EAAE,GAAGzC,SAAS,aAAa;IACpC0C,KAAK,EAAEzC,mBAAmB,CAACqB,MAAM;IACjCb,OAAO,EAAEA,OAAO;IAChBkC,QAAQ,EAAEb,YAAY;IACtBc,gBAAgB,EAAEZ;EACpB,CAAC,CAAC,IAAI,IAAI;EACV,MAAMa,GAAG,GAAG7D,UAAU,CAAC,GAAGgB,SAAS,UAAU,EAAE;IAC7C,CAAC,GAAGA,SAAS,sBAAsB,GAAGK;EACxC,CAAC,CAAC;EACF,OAAO,aAAatB,KAAK,CAACyD,aAAa,CAACzD,KAAK,CAAC+D,QAAQ,EAAE,IAAI,EAAE,aAAa/D,KAAK,CAACyD,aAAa,CAAC,IAAI,EAAE;IACnGC,SAAS,EAAEI,GAAG;IACdvC,QAAQ,EAAEA;EACZ,CAAC,EAAE,CAAC4B,aAAa,IAAI,EAAE,EAAEa,GAAG,CAAC,CAAC;IAC5BC,UAAU;IACVC,YAAY;IACZxB;EACF,CAAC,MAAM,aAAa1C,KAAK,CAACyD,aAAa,CAACrD,QAAQ,EAAE;IAChDwC,GAAG,EAAEF,IAAI,CAACE,GAAG;IACbF,IAAI,EAAEA,IAAI;IACVwB,YAAY,EAAEA,YAAY;IAC1BD,UAAU,EAAEA,UAAU;IACtBhD,SAAS,EAAEA,SAAS;IACpBK,UAAU,EAAEA,UAAU;IACtB6C,OAAO,EAAE1B,eAAe;IACxBK,QAAQ,EAAEA,QAAQ;IAClBsB,OAAO,EAAEjD,YAAY,CAAC0B,QAAQ,CAACH,IAAI,CAACE,GAAG,CAAC;IACxCxB,QAAQ,EAAEC,cAAc,IAAIqB,IAAI,CAACtB;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEoC,cAAc,CAAC;AACxB,CAAC;AACD,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCzD,gBAAgB,CAAC0D,WAAW,GAAG,kBAAkB;AACnD;AACA,eAAe,aAAaxE,KAAK,CAACyE,UAAU,CAAC3D,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst Element = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    size,\n    shape\n  } = props;\n  const sizeCls = classNames({\n    [\"\".concat(prefixCls, \"-lg\")]: size === 'large',\n    [\"\".concat(prefixCls, \"-sm\")]: size === 'small'\n  });\n  const shapeCls = classNames({\n    [\"\".concat(prefixCls, \"-circle\")]: shape === 'circle',\n    [\"\".concat(prefixCls, \"-square\")]: shape === 'square',\n    [\"\".concat(prefixCls, \"-round\")]: shape === 'round'\n  });\n  const sizeStyle = React.useMemo(() => typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: \"\".concat(size, \"px\")\n  } : {}, [size]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: Object.assign(Object.assign({}, sizeStyle), style)\n  });\n};\nexport default Element;", "map": {"version": 3, "names": ["React", "classNames", "Element", "props", "prefixCls", "className", "style", "size", "shape", "sizeCls", "concat", "shapeCls", "sizeStyle", "useMemo", "width", "height", "lineHeight", "createElement", "Object", "assign"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/skeleton/Element.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst Element = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    size,\n    shape\n  } = props;\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const shapeCls = classNames({\n    [`${prefixCls}-circle`]: shape === 'circle',\n    [`${prefixCls}-square`]: shape === 'square',\n    [`${prefixCls}-round`]: shape === 'round'\n  });\n  const sizeStyle = React.useMemo(() => typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: `${size}px`\n  } : {}, [size]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: Object.assign(Object.assign({}, sizeStyle), style)\n  });\n};\nexport default Element;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,OAAO,GAAGC,KAAK,IAAI;EACvB,MAAM;IACJC,SAAS;IACTC,SAAS;IACTC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,OAAO,GAAGR,UAAU,CAAC;IACzB,IAAAS,MAAA,CAAIN,SAAS,WAAQG,IAAI,KAAK,OAAO;IACrC,IAAAG,MAAA,CAAIN,SAAS,WAAQG,IAAI,KAAK;EAChC,CAAC,CAAC;EACF,MAAMI,QAAQ,GAAGV,UAAU,CAAC;IAC1B,IAAAS,MAAA,CAAIN,SAAS,eAAYI,KAAK,KAAK,QAAQ;IAC3C,IAAAE,MAAA,CAAIN,SAAS,eAAYI,KAAK,KAAK,QAAQ;IAC3C,IAAAE,MAAA,CAAIN,SAAS,cAAWI,KAAK,KAAK;EACpC,CAAC,CAAC;EACF,MAAMI,SAAS,GAAGZ,KAAK,CAACa,OAAO,CAAC,MAAM,OAAON,IAAI,KAAK,QAAQ,GAAG;IAC/DO,KAAK,EAAEP,IAAI;IACXQ,MAAM,EAAER,IAAI;IACZS,UAAU,KAAAN,MAAA,CAAKH,IAAI;EACrB,CAAC,GAAG,CAAC,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACf,OAAO,aAAaP,KAAK,CAACiB,aAAa,CAAC,MAAM,EAAE;IAC9CZ,SAAS,EAAEJ,UAAU,CAACG,SAAS,EAAEK,OAAO,EAAEE,QAAQ,EAAEN,SAAS,CAAC;IAC9DC,KAAK,EAAEY,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,SAAS,CAAC,EAAEN,KAAK;EAC1D,CAAC,CAAC;AACJ,CAAC;AACD,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
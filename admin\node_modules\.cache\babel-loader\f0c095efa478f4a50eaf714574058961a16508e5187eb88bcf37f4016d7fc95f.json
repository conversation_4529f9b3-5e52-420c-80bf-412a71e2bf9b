{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { CONTAINER_MAX_OFFSET } from '../../_util/hooks/useZIndex';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genMessageStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    boxShadow,\n    colorText,\n    colorSuccess,\n    colorError,\n    colorWarning,\n    colorInfo,\n    fontSizeLG,\n    motionEaseInOutCirc,\n    motionDurationSlow,\n    marginXS,\n    paddingXS,\n    borderRadiusLG,\n    zIndexPopup,\n    // Custom token\n    contentPadding,\n    contentBg\n  } = token;\n  const noticeCls = \"\".concat(componentCls, \"-notice\");\n  const messageMoveIn = new Keyframes('MessageMoveIn', {\n    '0%': {\n      padding: 0,\n      transform: 'translateY(-100%)',\n      opacity: 0\n    },\n    '100%': {\n      padding: paddingXS,\n      transform: 'translateY(0)',\n      opacity: 1\n    }\n  });\n  const messageMoveOut = new Keyframes('MessageMoveOut', {\n    '0%': {\n      maxHeight: token.height,\n      padding: paddingXS,\n      opacity: 1\n    },\n    '100%': {\n      maxHeight: 0,\n      padding: 0,\n      opacity: 0\n    }\n  });\n  const noticeStyle = {\n    padding: paddingXS,\n    textAlign: 'center',\n    [\"\".concat(componentCls, \"-custom-content\")]: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    [\"\".concat(componentCls, \"-custom-content > \").concat(iconCls)]: {\n      marginInlineEnd: marginXS,\n      // affected by ltr or rtl\n      fontSize: fontSizeLG\n    },\n    [\"\".concat(noticeCls, \"-content\")]: {\n      display: 'inline-block',\n      padding: contentPadding,\n      background: contentBg,\n      borderRadius: borderRadiusLG,\n      boxShadow,\n      pointerEvents: 'all'\n    },\n    [\"\".concat(componentCls, \"-success > \").concat(iconCls)]: {\n      color: colorSuccess\n    },\n    [\"\".concat(componentCls, \"-error > \").concat(iconCls)]: {\n      color: colorError\n    },\n    [\"\".concat(componentCls, \"-warning > \").concat(iconCls)]: {\n      color: colorWarning\n    },\n    [\"\".concat(componentCls, \"-info > \").concat(iconCls, \",\\n      \").concat(componentCls, \"-loading > \").concat(iconCls)]: {\n      color: colorInfo\n    }\n  };\n  return [\n  // ============================ Holder ============================\n  {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      color: colorText,\n      position: 'fixed',\n      top: marginXS,\n      width: '100%',\n      pointerEvents: 'none',\n      zIndex: zIndexPopup,\n      [\"\".concat(componentCls, \"-move-up\")]: {\n        animationFillMode: 'forwards'\n      },\n      [\"\\n        \".concat(componentCls, \"-move-up-appear,\\n        \").concat(componentCls, \"-move-up-enter\\n      \")]: {\n        animationName: messageMoveIn,\n        animationDuration: motionDurationSlow,\n        animationPlayState: 'paused',\n        animationTimingFunction: motionEaseInOutCirc\n      },\n      [\"\\n        \".concat(componentCls, \"-move-up-appear\").concat(componentCls, \"-move-up-appear-active,\\n        \").concat(componentCls, \"-move-up-enter\").concat(componentCls, \"-move-up-enter-active\\n      \")]: {\n        animationPlayState: 'running'\n      },\n      [\"\".concat(componentCls, \"-move-up-leave\")]: {\n        animationName: messageMoveOut,\n        animationDuration: motionDurationSlow,\n        animationPlayState: 'paused',\n        animationTimingFunction: motionEaseInOutCirc\n      },\n      [\"\".concat(componentCls, \"-move-up-leave\").concat(componentCls, \"-move-up-leave-active\")]: {\n        animationPlayState: 'running'\n      },\n      '&-rtl': {\n        direction: 'rtl',\n        span: {\n          direction: 'rtl'\n        }\n      }\n    })\n  },\n  // ============================ Notice ============================\n  {\n    [componentCls]: {\n      [\"\".concat(noticeCls, \"-wrapper\")]: Object.assign({}, noticeStyle)\n    }\n  },\n  // ============================= Pure =============================\n  {\n    [\"\".concat(componentCls, \"-notice-pure-panel\")]: Object.assign(Object.assign({}, noticeStyle), {\n      padding: 0,\n      textAlign: 'start'\n    })\n  }];\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexPopupBase + CONTAINER_MAX_OFFSET + 10,\n  contentBg: token.colorBgElevated,\n  contentPadding: \"\".concat((token.controlHeightLG - token.fontSize * token.lineHeight) / 2, \"px \").concat(token.paddingSM, \"px\")\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Message', token => {\n  // Gen-style functions here\n  const combinedToken = mergeToken(token, {\n    height: 150\n  });\n  return [genMessageStyle(combinedToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "const genButtonBorderStyle = (buttonTypeCls, borderColor) => ({\n  // Border\n  [`> span, > ${buttonTypeCls}`]: {\n    '&:not(:last-child)': {\n      [`&, & > ${buttonTypeCls}`]: {\n        '&:not(:disabled)': {\n          borderInlineEndColor: borderColor\n        }\n      }\n    },\n    '&:not(:first-child)': {\n      [`&, & > ${buttonTypeCls}`]: {\n        '&:not(:disabled)': {\n          borderInlineStartColor: borderColor\n        }\n      }\n    }\n  }\n});\nconst genGroupStyle = token => {\n  const {\n    componentCls,\n    fontSize,\n    lineWidth,\n    groupBorderColor,\n    colorErrorHover\n  } = token;\n  return {\n    [`${componentCls}-group`]: [{\n      position: 'relative',\n      display: 'inline-flex',\n      // Border\n      [`> span, > ${componentCls}`]: {\n        '&:not(:last-child)': {\n          [`&, & > ${componentCls}`]: {\n            borderStartEndRadius: 0,\n            borderEndEndRadius: 0\n          }\n        },\n        '&:not(:first-child)': {\n          marginInlineStart: token.calc(lineWidth).mul(-1).equal(),\n          [`&, & > ${componentCls}`]: {\n            borderStartStartRadius: 0,\n            borderEndStartRadius: 0\n          }\n        }\n      },\n      [componentCls]: {\n        position: 'relative',\n        zIndex: 1,\n        '&:hover, &:focus, &:active': {\n          zIndex: 2\n        },\n        '&[disabled]': {\n          zIndex: 0\n        }\n      },\n      [`${componentCls}-icon-only`]: {\n        fontSize\n      }\n    },\n    // Border Color\n    genButtonBorderStyle(`${componentCls}-primary`, groupBorderColor), genButtonBorderStyle(`${componentCls}-danger`, colorErrorHover)]\n  };\n};\nexport default genGroupStyle;", "map": {"version": 3, "names": ["genButtonBorderStyle", "buttonTypeCls", "borderColor", "borderInlineEndColor", "borderInlineStartColor", "genGroupStyle", "token", "componentCls", "fontSize", "lineWidth", "groupBorderColor", "colorErrorHover", "position", "display", "borderStartEndRadius", "borderEndEndRadius", "marginInlineStart", "calc", "mul", "equal", "borderStartStartRadius", "borderEndStartRadius", "zIndex"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/button/style/group.js"], "sourcesContent": ["const genButtonBorderStyle = (buttonTypeCls, borderColor) => ({\n  // Border\n  [`> span, > ${buttonTypeCls}`]: {\n    '&:not(:last-child)': {\n      [`&, & > ${buttonTypeCls}`]: {\n        '&:not(:disabled)': {\n          borderInlineEndColor: borderColor\n        }\n      }\n    },\n    '&:not(:first-child)': {\n      [`&, & > ${buttonTypeCls}`]: {\n        '&:not(:disabled)': {\n          borderInlineStartColor: borderColor\n        }\n      }\n    }\n  }\n});\nconst genGroupStyle = token => {\n  const {\n    componentCls,\n    fontSize,\n    lineWidth,\n    groupBorderColor,\n    colorErrorHover\n  } = token;\n  return {\n    [`${componentCls}-group`]: [{\n      position: 'relative',\n      display: 'inline-flex',\n      // Border\n      [`> span, > ${componentCls}`]: {\n        '&:not(:last-child)': {\n          [`&, & > ${componentCls}`]: {\n            borderStartEndRadius: 0,\n            borderEndEndRadius: 0\n          }\n        },\n        '&:not(:first-child)': {\n          marginInlineStart: token.calc(lineWidth).mul(-1).equal(),\n          [`&, & > ${componentCls}`]: {\n            borderStartStartRadius: 0,\n            borderEndStartRadius: 0\n          }\n        }\n      },\n      [componentCls]: {\n        position: 'relative',\n        zIndex: 1,\n        '&:hover, &:focus, &:active': {\n          zIndex: 2\n        },\n        '&[disabled]': {\n          zIndex: 0\n        }\n      },\n      [`${componentCls}-icon-only`]: {\n        fontSize\n      }\n    },\n    // Border Color\n    genButtonBorderStyle(`${componentCls}-primary`, groupBorderColor), genButtonBorderStyle(`${componentCls}-danger`, colorErrorHover)]\n  };\n};\nexport default genGroupStyle;"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGA,CAACC,aAAa,EAAEC,WAAW,MAAM;EAC5D;EACA,CAAC,aAAaD,aAAa,EAAE,GAAG;IAC9B,oBAAoB,EAAE;MACpB,CAAC,UAAUA,aAAa,EAAE,GAAG;QAC3B,kBAAkB,EAAE;UAClBE,oBAAoB,EAAED;QACxB;MACF;IACF,CAAC;IACD,qBAAqB,EAAE;MACrB,CAAC,UAAUD,aAAa,EAAE,GAAG;QAC3B,kBAAkB,EAAE;UAClBG,sBAAsB,EAAEF;QAC1B;MACF;IACF;EACF;AACF,CAAC,CAAC;AACF,MAAMG,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,YAAY;IACZC,QAAQ;IACRC,SAAS;IACTC,gBAAgB;IAChBC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,QAAQ,GAAG,CAAC;MAC1BK,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,aAAa;MACtB;MACA,CAAC,aAAaN,YAAY,EAAE,GAAG;QAC7B,oBAAoB,EAAE;UACpB,CAAC,UAAUA,YAAY,EAAE,GAAG;YAC1BO,oBAAoB,EAAE,CAAC;YACvBC,kBAAkB,EAAE;UACtB;QACF,CAAC;QACD,qBAAqB,EAAE;UACrBC,iBAAiB,EAAEV,KAAK,CAACW,IAAI,CAACR,SAAS,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;UACxD,CAAC,UAAUZ,YAAY,EAAE,GAAG;YAC1Ba,sBAAsB,EAAE,CAAC;YACzBC,oBAAoB,EAAE;UACxB;QACF;MACF,CAAC;MACD,CAACd,YAAY,GAAG;QACdK,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE,CAAC;QACT,4BAA4B,EAAE;UAC5BA,MAAM,EAAE;QACV,CAAC;QACD,aAAa,EAAE;UACbA,MAAM,EAAE;QACV;MACF,CAAC;MACD,CAAC,GAAGf,YAAY,YAAY,GAAG;QAC7BC;MACF;IACF,CAAC;IACD;IACAR,oBAAoB,CAAC,GAAGO,YAAY,UAAU,EAAEG,gBAAgB,CAAC,EAAEV,oBAAoB,CAAC,GAAGO,YAAY,SAAS,EAAEI,eAAe,CAAC;EACpI,CAAC;AACH,CAAC;AACD,eAAeN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
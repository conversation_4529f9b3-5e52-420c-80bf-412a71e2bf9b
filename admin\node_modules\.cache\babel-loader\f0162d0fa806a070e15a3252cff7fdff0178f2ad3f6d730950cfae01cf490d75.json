{"ast": null, "code": "import * as React from 'react';\nimport { ConfigContext } from '../../config-provider';\nfunction useBase(customizePrefixCls, direction) {\n  const {\n    getPrefixCls,\n    direction: rootDirection,\n    renderEmpty\n  } = React.useContext(ConfigContext);\n  const mergedDirection = direction || rootDirection;\n  const prefixCls = getPrefixCls('select', customizePrefixCls);\n  const cascaderPrefixCls = getPrefixCls('cascader', customizePrefixCls);\n  return [prefixCls, cascaderPrefixCls, mergedDirection, renderEmpty];\n}\nexport default useBase;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
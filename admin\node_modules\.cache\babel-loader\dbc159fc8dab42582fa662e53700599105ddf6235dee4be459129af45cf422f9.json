{"ast": null, "code": "import * as React from 'react';\nimport LocaleContext from './context';\nimport defaultLocaleData from './en_US';\nconst useLocale = (componentName, defaultLocale) => {\n  const fullLocale = React.useContext(LocaleContext);\n  const getLocale = React.useMemo(() => {\n    var _a;\n    const locale = defaultLocale || defaultLocaleData[componentName];\n    const localeFromContext = (_a = fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale[componentName]) !== null && _a !== void 0 ? _a : {};\n    return Object.assign(Object.assign({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, fullLocale]);\n  const getLocaleCode = React.useMemo(() => {\n    const localeCode = fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale.locale;\n    // Had use LocaleProvide but didn't set locale\n    if ((fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale.exist) && !localeCode) {\n      return defaultLocaleData.locale;\n    }\n    return localeCode;\n  }, [fullLocale]);\n  return [getLocale, getLocaleCode];\n};\nexport default useLocale;", "map": {"version": 3, "names": ["React", "LocaleContext", "defaultLocaleData", "useLocale", "componentName", "defaultLocale", "fullLocale", "useContext", "getLocale", "useMemo", "_a", "locale", "localeFromContext", "Object", "assign", "getLocaleCode", "localeCode", "exist"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/locale/useLocale.js"], "sourcesContent": ["import * as React from 'react';\nimport LocaleContext from './context';\nimport defaultLocaleData from './en_US';\nconst useLocale = (componentName, defaultLocale) => {\n  const fullLocale = React.useContext(LocaleContext);\n  const getLocale = React.useMemo(() => {\n    var _a;\n    const locale = defaultLocale || defaultLocaleData[componentName];\n    const localeFromContext = (_a = fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale[componentName]) !== null && _a !== void 0 ? _a : {};\n    return Object.assign(Object.assign({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, fullLocale]);\n  const getLocaleCode = React.useMemo(() => {\n    const localeCode = fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale.locale;\n    // Had use LocaleProvide but didn't set locale\n    if ((fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale.exist) && !localeCode) {\n      return defaultLocaleData.locale;\n    }\n    return localeCode;\n  }, [fullLocale]);\n  return [getLocale, getLocaleCode];\n};\nexport default useLocale;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,iBAAiB,MAAM,SAAS;AACvC,MAAMC,SAAS,GAAGA,CAACC,aAAa,EAAEC,aAAa,KAAK;EAClD,MAAMC,UAAU,GAAGN,KAAK,CAACO,UAAU,CAACN,aAAa,CAAC;EAClD,MAAMO,SAAS,GAAGR,KAAK,CAACS,OAAO,CAAC,MAAM;IACpC,IAAIC,EAAE;IACN,MAAMC,MAAM,GAAGN,aAAa,IAAIH,iBAAiB,CAACE,aAAa,CAAC;IAChE,MAAMQ,iBAAiB,GAAG,CAACF,EAAE,GAAGJ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACF,aAAa,CAAC,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACtJ,OAAOG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAOH,MAAM,KAAK,UAAU,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAEC,iBAAiB,IAAI,CAAC,CAAC,CAAC;EACpH,CAAC,EAAE,CAACR,aAAa,EAAEC,aAAa,EAAEC,UAAU,CAAC,CAAC;EAC9C,MAAMS,aAAa,GAAGf,KAAK,CAACS,OAAO,CAAC,MAAM;IACxC,MAAMO,UAAU,GAAGV,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACK,MAAM;IAC5F;IACA,IAAI,CAACL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACW,KAAK,KAAK,CAACD,UAAU,EAAE;MAC7F,OAAOd,iBAAiB,CAACS,MAAM;IACjC;IACA,OAAOK,UAAU;EACnB,CAAC,EAAE,CAACV,UAAU,CAAC,CAAC;EAChB,OAAO,CAACE,SAAS,EAAEO,aAAa,CAAC;AACnC,CAAC;AACD,eAAeZ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
# ✅ 部署检查清单 - 后台管理系统

## 📋 **部署前准备**

### **服务器环境检查**
- [ ] 服务器已安装Nginx
- [ ] 服务器已安装PHP 8.3
- [ ] 域名 `h5.haokajiyun.com` 已解析到服务器
- [ ] SSL证书已安装
- [ ] API服务已部署到 `/www/wwwroot/h5.haokajiyun.com/public`

### **本地文件准备**
- [ ] 前端项目已构建完成 (`npm run build`)
- [ ] API配置指向生产环境 (`https://h5.haokajiyun.com/api`)
- [ ] 静态资源路径配置正确 (`./static/`)

## 🚀 **部署步骤**

### **第一步: 上传文件**
- [ ] 创建目录 `/www/wwwroot/h5.haokajiyun.com/admin`
- [ ] 上传 `index.html` 到admin目录
- [ ] 上传 `static/` 目录到admin目录
- [ ] 上传 `asset-manifest.json` 到admin目录
- [ ] 设置文件权限 `chmod 644` 和目录权限 `chmod 755`

### **第二步: 配置Nginx**
- [ ] 备份原有Nginx配置
- [ ] 添加admin路由配置
- [ ] 配置API代理
- [ ] 设置CORS头
- [ ] 配置静态资源缓存
- [ ] 测试Nginx配置 (`nginx -t`)
- [ ] 重载Nginx配置 (`nginx -s reload`)

### **第三步: 验证部署**
- [ ] 访问 `https://h5.haokajiyun.com/admin` 页面正常加载
- [ ] 检查浏览器控制台无错误
- [ ] 测试左侧导航菜单
- [ ] 测试产品管理功能
- [ ] 测试订单管理功能
- [ ] 验证API接口调用

## 🔍 **功能测试清单**

### **页面加载测试**
- [ ] 首页正常显示
- [ ] 样式加载正确 (Ant Design组件显示正常)
- [ ] JavaScript文件加载成功
- [ ] 无404错误

### **导航测试**
- [ ] 左侧菜单展开/收起正常
- [ ] 产品管理菜单正常
- [ ] 订单管理菜单正常
- [ ] 面包屑导航正常

### **产品管理测试**
- [ ] 产品列表页面加载
- [ ] 产品搜索功能
- [ ] 产品添加功能
- [ ] 产品编辑功能
- [ ] 产品状态切换 (上架/下架)

### **订单管理测试**
- [ ] 待处理订单列表
- [ ] 审核中订单列表
- [ ] 失败订单列表
- [ ] 已发货订单列表
- [ ] 待上传三证订单列表

### **API接口测试**
- [ ] 产品列表API (`GET /api/v1/products`)
- [ ] 产品详情API (`GET /api/v1/products/{id}`)
- [ ] 产品创建API (`POST /api/v1/products`)
- [ ] 产品更新API (`PUT /api/v1/products/{id}`)
- [ ] 订单相关API正常

## 🛠️ **故障排除检查**

### **页面无法访问**
- [ ] 检查DNS解析 (`nslookup h5.haokajiyun.com`)
- [ ] 检查服务器防火墙设置
- [ ] 检查Nginx服务状态 (`systemctl status nginx`)
- [ ] 检查SSL证书有效性

### **页面空白或样式错误**
- [ ] 检查静态资源路径
- [ ] 检查文件权限
- [ ] 检查浏览器控制台错误
- [ ] 检查Nginx错误日志

### **API请求失败**
- [ ] 检查API服务状态
- [ ] 检查PHP-FPM状态
- [ ] 检查数据库连接
- [ ] 检查CORS配置

### **路由不工作**
- [ ] 检查Nginx try_files配置
- [ ] 确认SPA路由支持
- [ ] 检查index.html fallback配置

## 📊 **性能检查**

### **加载速度**
- [ ] 首次加载时间 < 3秒
- [ ] 静态资源启用缓存
- [ ] Gzip压缩已启用
- [ ] HTTP/2已启用

### **资源优化**
- [ ] CSS文件大小合理 (~1.7KB)
- [ ] JavaScript文件大小合理 (~450KB)
- [ ] 图片资源已优化
- [ ] 无未使用的资源

## 🔒 **安全检查**

### **HTTPS配置**
- [ ] 强制HTTPS重定向
- [ ] SSL证书有效
- [ ] 安全头设置正确
- [ ] 现代TLS协议

### **文件安全**
- [ ] 敏感文件访问被禁止 (.env, .log等)
- [ ] 目录浏览被禁用
- [ ] 上传文件大小限制
- [ ] 文件权限设置正确

## 📞 **部署完成确认**

### **最终验证**
- [ ] 所有页面可正常访问
- [ ] 所有功能正常工作
- [ ] 无JavaScript错误
- [ ] API接口响应正常
- [ ] 性能表现良好
- [ ] 安全配置正确

### **文档交付**
- [ ] 部署文档已提供
- [ ] Nginx配置文件已提供
- [ ] 故障排除指南已提供
- [ ] 联系方式已提供

## 🎯 **部署后任务**

### **监控设置**
- [ ] 设置服务器监控
- [ ] 配置日志轮转
- [ ] 设置备份策略
- [ ] 配置告警通知

### **用户培训**
- [ ] 管理员账号创建
- [ ] 功能使用培训
- [ ] 操作手册提供
- [ ] 技术支持联系方式

---

## ✅ **部署状态**

**部署日期**: ___________  
**部署人员**: ___________  
**验证人员**: ___________  
**部署状态**: [ ] 成功 [ ] 失败  

**访问地址**: https://h5.haokajiyun.com/admin  
**API地址**: https://h5.haokajiyun.com/api  

**备注**: ________________________________

---

**🎉 恭喜！后台管理系统部署完成！**

{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { useEvent, useMergedState } from 'rc-util';\nconst EMPTY_KEYS = [];\nfunction filterKeys(keys, dataKeys) {\n  const filteredKeys = keys.filter(key => dataKeys.has(key));\n  return keys.length === filteredKeys.length ? keys : filteredKeys;\n}\nfunction flattenKeys(keys) {\n  return Array.from(keys).join(';');\n}\nfunction useSelection(leftDataSource, rightDataSource, selectedKeys) {\n  // Prepare `dataSource` keys\n  const [leftKeys, rightKeys] = React.useMemo(() => [new Set(leftDataSource.map(src => src === null || src === void 0 ? void 0 : src.key)), new Set(rightDataSource.map(src => src === null || src === void 0 ? void 0 : src.key))], [leftDataSource, rightDataSource]);\n  // Selected Keys\n  const [mergedSelectedKeys, setMergedSelectedKeys] = useMergedState(EMPTY_KEYS, {\n    value: selectedKeys\n  });\n  const sourceSelectedKeys = React.useMemo(() => filterKeys(mergedSelectedKeys, leftKeys), [mergedSelectedKeys, leftKeys]);\n  const targetSelectedKeys = React.useMemo(() => filterKeys(mergedSelectedKeys, rightKeys), [mergedSelectedKeys, rightKeys]);\n  // // Reset when data changed\n  React.useEffect(() => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(filterKeys(mergedSelectedKeys, leftKeys)), _toConsumableArray(filterKeys(mergedSelectedKeys, rightKeys))));\n  }, [flattenKeys(leftKeys), flattenKeys(rightKeys)]);\n  // Update keys\n  const setSourceSelectedKeys = useEvent(nextSrcKeys => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(nextSrcKeys), _toConsumableArray(targetSelectedKeys)));\n  });\n  const setTargetSelectedKeys = useEvent(nextTargetKeys => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(sourceSelectedKeys), _toConsumableArray(nextTargetKeys)));\n  });\n  return [\n  // Keys\n  sourceSelectedKeys, targetSelectedKeys,\n  // Updater\n  setSourceSelectedKeys, setTargetSelectedKeys];\n}\nexport default useSelection;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "useEvent", "useMergedState", "EMPTY_KEYS", "filterKeys", "keys", "dataKeys", "filtered<PERSON>eys", "filter", "key", "has", "length", "flatten<PERSON>eys", "Array", "from", "join", "useSelection", "leftDataSource", "rightDataSource", "<PERSON><PERSON><PERSON><PERSON>", "leftKeys", "rightKeys", "useMemo", "Set", "map", "src", "mergedSelectedKeys", "setMergedSelectedKeys", "value", "sourceSelectedKeys", "targetSelectedKeys", "useEffect", "concat", "setSourceSelectedKeys", "nextSrc<PERSON>ey<PERSON>", "setTargetSelectedKeys", "next<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/transfer/hooks/useSelection.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { useEvent, useMergedState } from 'rc-util';\nconst EMPTY_KEYS = [];\nfunction filterKeys(keys, dataKeys) {\n  const filteredKeys = keys.filter(key => dataKeys.has(key));\n  return keys.length === filteredKeys.length ? keys : filteredKeys;\n}\nfunction flattenKeys(keys) {\n  return Array.from(keys).join(';');\n}\nfunction useSelection(leftDataSource, rightDataSource, selectedKeys) {\n  // Prepare `dataSource` keys\n  const [leftKeys, rightKeys] = React.useMemo(() => [new Set(leftDataSource.map(src => src === null || src === void 0 ? void 0 : src.key)), new Set(rightDataSource.map(src => src === null || src === void 0 ? void 0 : src.key))], [leftDataSource, rightDataSource]);\n  // Selected Keys\n  const [mergedSelectedKeys, setMergedSelectedKeys] = useMergedState(EMPTY_KEYS, {\n    value: selectedKeys\n  });\n  const sourceSelectedKeys = React.useMemo(() => filterKeys(mergedSelectedKeys, leftKeys), [mergedSelectedKeys, leftKeys]);\n  const targetSelectedKeys = React.useMemo(() => filterKeys(mergedSelectedKeys, rightKeys), [mergedSelectedKeys, rightKeys]);\n  // // Reset when data changed\n  React.useEffect(() => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(filterKeys(mergedSelectedKeys, leftKeys)), _toConsumableArray(filterKeys(mergedSelectedKeys, rightKeys))));\n  }, [flattenKeys(leftKeys), flattenKeys(rightKeys)]);\n  // Update keys\n  const setSourceSelectedKeys = useEvent(nextSrcKeys => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(nextSrcKeys), _toConsumableArray(targetSelectedKeys)));\n  });\n  const setTargetSelectedKeys = useEvent(nextTargetKeys => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(sourceSelectedKeys), _toConsumableArray(nextTargetKeys)));\n  });\n  return [\n  // Keys\n  sourceSelectedKeys, targetSelectedKeys,\n  // Updater\n  setSourceSelectedKeys, setTargetSelectedKeys];\n}\nexport default useSelection;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,cAAc,QAAQ,SAAS;AAClD,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,UAAUA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAClC,MAAMC,YAAY,GAAGF,IAAI,CAACG,MAAM,CAACC,GAAG,IAAIH,QAAQ,CAACI,GAAG,CAACD,GAAG,CAAC,CAAC;EAC1D,OAAOJ,IAAI,CAACM,MAAM,KAAKJ,YAAY,CAACI,MAAM,GAAGN,IAAI,GAAGE,YAAY;AAClE;AACA,SAASK,WAAWA,CAACP,IAAI,EAAE;EACzB,OAAOQ,KAAK,CAACC,IAAI,CAACT,IAAI,CAAC,CAACU,IAAI,CAAC,GAAG,CAAC;AACnC;AACA,SAASC,YAAYA,CAACC,cAAc,EAAEC,eAAe,EAAEC,YAAY,EAAE;EACnE;EACA,MAAM,CAACC,QAAQ,EAAEC,SAAS,CAAC,GAAGrB,KAAK,CAACsB,OAAO,CAAC,MAAM,CAAC,IAAIC,GAAG,CAACN,cAAc,CAACO,GAAG,CAACC,GAAG,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAChB,GAAG,CAAC,CAAC,EAAE,IAAIc,GAAG,CAACL,eAAe,CAACM,GAAG,CAACC,GAAG,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAChB,GAAG,CAAC,CAAC,CAAC,EAAE,CAACQ,cAAc,EAAEC,eAAe,CAAC,CAAC;EACrQ;EACA,MAAM,CAACQ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzB,cAAc,CAACC,UAAU,EAAE;IAC7EyB,KAAK,EAAET;EACT,CAAC,CAAC;EACF,MAAMU,kBAAkB,GAAG7B,KAAK,CAACsB,OAAO,CAAC,MAAMlB,UAAU,CAACsB,kBAAkB,EAAEN,QAAQ,CAAC,EAAE,CAACM,kBAAkB,EAAEN,QAAQ,CAAC,CAAC;EACxH,MAAMU,kBAAkB,GAAG9B,KAAK,CAACsB,OAAO,CAAC,MAAMlB,UAAU,CAACsB,kBAAkB,EAAEL,SAAS,CAAC,EAAE,CAACK,kBAAkB,EAAEL,SAAS,CAAC,CAAC;EAC1H;EACArB,KAAK,CAAC+B,SAAS,CAAC,MAAM;IACpBJ,qBAAqB,CAAC,EAAE,CAACK,MAAM,CAACjC,kBAAkB,CAACK,UAAU,CAACsB,kBAAkB,EAAEN,QAAQ,CAAC,CAAC,EAAErB,kBAAkB,CAACK,UAAU,CAACsB,kBAAkB,EAAEL,SAAS,CAAC,CAAC,CAAC,CAAC;EAC/J,CAAC,EAAE,CAACT,WAAW,CAACQ,QAAQ,CAAC,EAAER,WAAW,CAACS,SAAS,CAAC,CAAC,CAAC;EACnD;EACA,MAAMY,qBAAqB,GAAGhC,QAAQ,CAACiC,WAAW,IAAI;IACpDP,qBAAqB,CAAC,EAAE,CAACK,MAAM,CAACjC,kBAAkB,CAACmC,WAAW,CAAC,EAAEnC,kBAAkB,CAAC+B,kBAAkB,CAAC,CAAC,CAAC;EAC3G,CAAC,CAAC;EACF,MAAMK,qBAAqB,GAAGlC,QAAQ,CAACmC,cAAc,IAAI;IACvDT,qBAAqB,CAAC,EAAE,CAACK,MAAM,CAACjC,kBAAkB,CAAC8B,kBAAkB,CAAC,EAAE9B,kBAAkB,CAACqC,cAAc,CAAC,CAAC,CAAC;EAC9G,CAAC,CAAC;EACF,OAAO;EACP;EACAP,kBAAkB,EAAEC,kBAAkB;EACtC;EACAG,qBAAqB,EAAEE,qBAAqB,CAAC;AAC/C;AACA,eAAenB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useClosable from '../_util/hooks/useClosable';\nimport { withPureRenderTheme } from '../_util/PurePanel';\nimport { cloneElement } from '../_util/reactNode';\nimport { ConfigContext } from '../config-provider';\nimport { RawPurePanel as PopoverRawPurePanel } from '../popover/PurePanel';\nimport TourPanel from './panelRender';\nimport useStyle from './style';\nconst PurePanel = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      current = 0,\n      total = 6,\n      className,\n      style,\n      type,\n      closable,\n      closeIcon\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"current\", \"total\", \"className\", \"style\", \"type\", \"closable\", \"closeIcon\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('tour', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const [mergedClosable, mergedCloseIcon] = useClosable({\n    closable,\n    closeIcon\n  }, null, {\n    closable: true,\n    closeIconRender: icon => /*#__PURE__*/React.isValidElement(icon) ? cloneElement(icon, {\n      className: classNames(icon.props.className, \"\".concat(prefixCls, \"-close-icon\"))\n    }) : icon\n  });\n  return wrapCSSVar(/*#__PURE__*/React.createElement(PopoverRawPurePanel, {\n    prefixCls: prefixCls,\n    hashId: hashId,\n    className: classNames(className, \"\".concat(prefixCls, \"-pure\"), type && \"\".concat(prefixCls, \"-\").concat(type), cssVarCls),\n    style: style\n  }, /*#__PURE__*/React.createElement(TourPanel, {\n    stepProps: Object.assign(Object.assign({}, restProps), {\n      prefixCls,\n      total,\n      closable: mergedClosable ? {\n        closeIcon: mergedCloseIcon\n      } : undefined\n    }),\n    current: current,\n    type: type\n  })));\n};\nexport default withPureRenderTheme(PurePanel);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
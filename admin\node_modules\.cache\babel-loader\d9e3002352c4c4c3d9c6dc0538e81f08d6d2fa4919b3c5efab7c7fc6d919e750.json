{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport BreadcrumbItem, { InternalBreadcrumbItem } from './BreadcrumbItem';\nimport BreadcrumbSeparator from './BreadcrumbSeparator';\nimport useStyle from './style';\nimport useItemRender from './useItemRender';\nimport useItems from './useItems';\nconst getPath = (params, path) => {\n  if (path === undefined) {\n    return path;\n  }\n  let mergedPath = (path || '').replace(/^\\//, '');\n  Object.keys(params).forEach(key => {\n    mergedPath = mergedPath.replace(\":\".concat(key), params[key]);\n  });\n  return mergedPath;\n};\nconst Breadcrumb = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      separator = '/',\n      style,\n      className,\n      rootClassName,\n      routes: legacyRoutes,\n      items,\n      children,\n      itemRender,\n      params = {}\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"separator\", \"style\", \"className\", \"rootClassName\", \"routes\", \"items\", \"children\", \"itemRender\", \"params\"]);\n  const {\n    getPrefixCls,\n    direction,\n    breadcrumb\n  } = React.useContext(ConfigContext);\n  let crumbs;\n  const prefixCls = getPrefixCls('breadcrumb', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedItems = useItems(items, legacyRoutes);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Breadcrumb');\n    warning.deprecated(!legacyRoutes, 'routes', 'items');\n    // Deprecated warning for breadcrumb children\n    if (!mergedItems || mergedItems.length === 0) {\n      const childList = toArray(children);\n      warning.deprecated(childList.length === 0, 'Breadcrumb.Item and Breadcrumb.Separator', 'items');\n      childList.forEach(element => {\n        if (element) {\n          process.env.NODE_ENV !== \"production\" ? warning(element.type && (element.type.__ANT_BREADCRUMB_ITEM === true || element.type.__ANT_BREADCRUMB_SEPARATOR === true), 'usage', \"Only accepts Breadcrumb.Item and Breadcrumb.Separator as it's children\") : void 0;\n        }\n      });\n    }\n  }\n  const mergedItemRender = useItemRender(prefixCls, itemRender);\n  if (mergedItems && mergedItems.length > 0) {\n    // generated by route\n    const paths = [];\n    const itemRenderRoutes = items || legacyRoutes;\n    crumbs = mergedItems.map((item, index) => {\n      const {\n        path,\n        key,\n        type,\n        menu,\n        overlay,\n        onClick,\n        className: itemClassName,\n        separator: itemSeparator,\n        dropdownProps\n      } = item;\n      const mergedPath = getPath(params, path);\n      if (mergedPath !== undefined) {\n        paths.push(mergedPath);\n      }\n      const mergedKey = key !== null && key !== void 0 ? key : index;\n      if (type === 'separator') {\n        return /*#__PURE__*/React.createElement(BreadcrumbSeparator, {\n          key: mergedKey\n        }, itemSeparator);\n      }\n      const itemProps = {};\n      const isLastItem = index === mergedItems.length - 1;\n      if (menu) {\n        itemProps.menu = menu;\n      } else if (overlay) {\n        itemProps.overlay = overlay;\n      }\n      let {\n        href\n      } = item;\n      if (paths.length && mergedPath !== undefined) {\n        href = \"#/\".concat(paths.join('/'));\n      }\n      return /*#__PURE__*/React.createElement(InternalBreadcrumbItem, Object.assign({\n        key: mergedKey\n      }, itemProps, pickAttrs(item, {\n        data: true,\n        aria: true\n      }), {\n        className: itemClassName,\n        dropdownProps: dropdownProps,\n        href: href,\n        separator: isLastItem ? '' : separator,\n        onClick: onClick,\n        prefixCls: prefixCls\n      }), mergedItemRender(item, params, itemRenderRoutes, paths, href));\n    });\n  } else if (children) {\n    const childrenLength = toArray(children).length;\n    crumbs = toArray(children).map((element, index) => {\n      if (!element) {\n        return element;\n      }\n      const isLastItem = index === childrenLength - 1;\n      return cloneElement(element, {\n        separator: isLastItem ? '' : separator,\n        // eslint-disable-next-line react/no-array-index-key\n        key: index\n      });\n    });\n  }\n  const breadcrumbClassName = classNames(prefixCls, breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.className, {\n    [\"\".concat(prefixCls, \"-rtl\")]: direction === 'rtl'\n  }, className, rootClassName, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.style), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"nav\", Object.assign({\n    className: breadcrumbClassName,\n    style: mergedStyle\n  }, restProps), /*#__PURE__*/React.createElement(\"ol\", null, crumbs)));\n};\nBreadcrumb.Item = BreadcrumbItem;\nBreadcrumb.Separator = BreadcrumbSeparator;\nif (process.env.NODE_ENV !== 'production') {\n  Breadcrumb.displayName = 'Breadcrumb';\n}\nexport default Breadcrumb;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
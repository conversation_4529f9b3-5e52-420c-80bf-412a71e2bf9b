{"ast": null, "code": "// Countdown\nconst timeUnits = [['Y', 1000 * 60 * 60 * 24 * 365],\n// years\n['M', 1000 * 60 * 60 * 24 * 30],\n// months\n['D', 1000 * 60 * 60 * 24],\n// days\n['H', 1000 * 60 * 60],\n// hours\n['m', 1000 * 60],\n// minutes\n['s', 1000],\n// seconds\n['S', 1] // million seconds\n];\nexport function formatTimeStr(duration, format) {\n  let leftDuration = duration;\n  const escapeRegex = /\\[[^\\]]*]/g;\n  const keepList = (format.match(escapeRegex) || []).map(str => str.slice(1, -1));\n  const templateText = format.replace(escapeRegex, '[]');\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    if (current.includes(name)) {\n      const value = Math.floor(leftDuration / unit);\n      leftDuration -= value * unit;\n      return current.replace(new RegExp(`${name}+`, 'g'), match => {\n        const len = match.length;\n        return value.toString().padStart(len, '0');\n      });\n    }\n    return current;\n  }, templateText);\n  let index = 0;\n  return replacedText.replace(escapeRegex, () => {\n    const match = keepList[index];\n    index += 1;\n    return match;\n  });\n}\nexport function formatCounter(value, config, down) {\n  const {\n    format = ''\n  } = config;\n  const target = new Date(value).getTime();\n  const current = Date.now();\n  const diff = down ? Math.max(target - current, 0) : Math.max(current - target, 0);\n  return formatTimeStr(diff, format);\n}", "map": {"version": 3, "names": ["timeUnits", "formatTimeStr", "duration", "format", "leftDuration", "escapeRegex", "keepList", "match", "map", "str", "slice", "templateText", "replace", "replacedText", "reduce", "current", "name", "unit", "includes", "value", "Math", "floor", "RegExp", "len", "length", "toString", "padStart", "index", "formatCounter", "config", "down", "target", "Date", "getTime", "now", "diff", "max"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/statistic/utils.js"], "sourcesContent": ["// Countdown\nconst timeUnits = [['Y', 1000 * 60 * 60 * 24 * 365],\n// years\n['M', 1000 * 60 * 60 * 24 * 30],\n// months\n['D', 1000 * 60 * 60 * 24],\n// days\n['H', 1000 * 60 * 60],\n// hours\n['m', 1000 * 60],\n// minutes\n['s', 1000],\n// seconds\n['S', 1] // million seconds\n];\nexport function formatTimeStr(duration, format) {\n  let leftDuration = duration;\n  const escapeRegex = /\\[[^\\]]*]/g;\n  const keepList = (format.match(escapeRegex) || []).map(str => str.slice(1, -1));\n  const templateText = format.replace(escapeRegex, '[]');\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    if (current.includes(name)) {\n      const value = Math.floor(leftDuration / unit);\n      leftDuration -= value * unit;\n      return current.replace(new RegExp(`${name}+`, 'g'), match => {\n        const len = match.length;\n        return value.toString().padStart(len, '0');\n      });\n    }\n    return current;\n  }, templateText);\n  let index = 0;\n  return replacedText.replace(escapeRegex, () => {\n    const match = keepList[index];\n    index += 1;\n    return match;\n  });\n}\nexport function formatCounter(value, config, down) {\n  const {\n    format = ''\n  } = config;\n  const target = new Date(value).getTime();\n  const current = Date.now();\n  const diff = down ? Math.max(target - current, 0) : Math.max(current - target, 0);\n  return formatTimeStr(diff, format);\n}"], "mappings": "AAAA;AACA,MAAMA,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;AACnD;AACA,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;AACA,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC1B;AACA,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB;AACA,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC;AAChB;AACA,CAAC,GAAG,EAAE,IAAI,CAAC;AACX;AACA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,CACR;AACD,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAC9C,IAAIC,YAAY,GAAGF,QAAQ;EAC3B,MAAMG,WAAW,GAAG,YAAY;EAChC,MAAMC,QAAQ,GAAG,CAACH,MAAM,CAACI,KAAK,CAACF,WAAW,CAAC,IAAI,EAAE,EAAEG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/E,MAAMC,YAAY,GAAGR,MAAM,CAACS,OAAO,CAACP,WAAW,EAAE,IAAI,CAAC;EACtD,MAAMQ,YAAY,GAAGb,SAAS,CAACc,MAAM,CAAC,CAACC,OAAO,EAAE,CAACC,IAAI,EAAEC,IAAI,CAAC,KAAK;IAC/D,IAAIF,OAAO,CAACG,QAAQ,CAACF,IAAI,CAAC,EAAE;MAC1B,MAAMG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACjB,YAAY,GAAGa,IAAI,CAAC;MAC7Cb,YAAY,IAAIe,KAAK,GAAGF,IAAI;MAC5B,OAAOF,OAAO,CAACH,OAAO,CAAC,IAAIU,MAAM,CAAC,GAAGN,IAAI,GAAG,EAAE,GAAG,CAAC,EAAET,KAAK,IAAI;QAC3D,MAAMgB,GAAG,GAAGhB,KAAK,CAACiB,MAAM;QACxB,OAAOL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAACH,GAAG,EAAE,GAAG,CAAC;MAC5C,CAAC,CAAC;IACJ;IACA,OAAOR,OAAO;EAChB,CAAC,EAAEJ,YAAY,CAAC;EAChB,IAAIgB,KAAK,GAAG,CAAC;EACb,OAAOd,YAAY,CAACD,OAAO,CAACP,WAAW,EAAE,MAAM;IAC7C,MAAME,KAAK,GAAGD,QAAQ,CAACqB,KAAK,CAAC;IAC7BA,KAAK,IAAI,CAAC;IACV,OAAOpB,KAAK;EACd,CAAC,CAAC;AACJ;AACA,OAAO,SAASqB,aAAaA,CAACT,KAAK,EAAEU,MAAM,EAAEC,IAAI,EAAE;EACjD,MAAM;IACJ3B,MAAM,GAAG;EACX,CAAC,GAAG0B,MAAM;EACV,MAAME,MAAM,GAAG,IAAIC,IAAI,CAACb,KAAK,CAAC,CAACc,OAAO,CAAC,CAAC;EACxC,MAAMlB,OAAO,GAAGiB,IAAI,CAACE,GAAG,CAAC,CAAC;EAC1B,MAAMC,IAAI,GAAGL,IAAI,GAAGV,IAAI,CAACgB,GAAG,CAACL,MAAM,GAAGhB,OAAO,EAAE,CAAC,CAAC,GAAGK,IAAI,CAACgB,GAAG,CAACrB,OAAO,GAAGgB,MAAM,EAAE,CAAC,CAAC;EACjF,OAAO9B,aAAa,CAACkC,IAAI,EAAEhC,MAAM,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
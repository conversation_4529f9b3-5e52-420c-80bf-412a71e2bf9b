{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport DrawerContext from \"./context\";\nimport DrawerPanel from \"./DrawerPanel\";\nimport { parseWidthHeight } from \"./util\";\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none',\n  position: 'absolute'\n};\nfunction DrawerPopup(props, ref) {\n  var _ref, _pushConfig$distance, _pushConfig;\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    placement = props.placement,\n    inline = props.inline,\n    push = props.push,\n    forceRender = props.forceRender,\n    autoFocus = props.autoFocus,\n    keyboard = props.keyboard,\n    drawerClassNames = props.classNames,\n    rootClassName = props.rootClassName,\n    rootStyle = props.rootStyle,\n    zIndex = props.zIndex,\n    className = props.className,\n    id = props.id,\n    style = props.style,\n    motion = props.motion,\n    width = props.width,\n    height = props.height,\n    children = props.children,\n    mask = props.mask,\n    maskClosable = props.maskClosable,\n    maskMotion = props.maskMotion,\n    maskClassName = props.maskClassName,\n    maskStyle = props.maskStyle,\n    afterOpenChange = props.afterOpenChange,\n    onClose = props.onClose,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp,\n    styles = props.styles,\n    drawerRender = props.drawerRender;\n\n  // ================================ Refs ================================\n  var panelRef = React.useRef();\n  var sentinelStartRef = React.useRef();\n  var sentinelEndRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return panelRef.current;\n  });\n  var onPanelKeyDown = function onPanelKeyDown(event) {\n    var keyCode = event.keyCode,\n      shiftKey = event.shiftKey;\n    switch (keyCode) {\n      // Tab active\n      case KeyCode.TAB:\n        {\n          if (keyCode === KeyCode.TAB) {\n            if (!shiftKey && document.activeElement === sentinelEndRef.current) {\n              var _sentinelStartRef$cur;\n              (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 || _sentinelStartRef$cur.focus({\n                preventScroll: true\n              });\n            } else if (shiftKey && document.activeElement === sentinelStartRef.current) {\n              var _sentinelEndRef$curre;\n              (_sentinelEndRef$curre = sentinelEndRef.current) === null || _sentinelEndRef$curre === void 0 || _sentinelEndRef$curre.focus({\n                preventScroll: true\n              });\n            }\n          }\n          break;\n        }\n\n      // Close\n      case KeyCode.ESC:\n        {\n          if (onClose && keyboard) {\n            event.stopPropagation();\n            onClose(event);\n          }\n          break;\n        }\n    }\n  };\n\n  // ========================== Control ===========================\n  // Auto Focus\n  React.useEffect(function () {\n    if (open && autoFocus) {\n      var _panelRef$current;\n      (_panelRef$current = panelRef.current) === null || _panelRef$current === void 0 || _panelRef$current.focus({\n        preventScroll: true\n      });\n    }\n  }, [open]);\n\n  // ============================ Push ============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    pushed = _React$useState2[0],\n    setPushed = _React$useState2[1];\n  var parentContext = React.useContext(DrawerContext);\n\n  // Merge push distance\n  var pushConfig;\n  if (typeof push === 'boolean') {\n    pushConfig = push ? {} : {\n      distance: 0\n    };\n  } else {\n    pushConfig = push || {};\n  }\n  var pushDistance = (_ref = (_pushConfig$distance = (_pushConfig = pushConfig) === null || _pushConfig === void 0 ? void 0 : _pushConfig.distance) !== null && _pushConfig$distance !== void 0 ? _pushConfig$distance : parentContext === null || parentContext === void 0 ? void 0 : parentContext.pushDistance) !== null && _ref !== void 0 ? _ref : 180;\n  var mergedContext = React.useMemo(function () {\n    return {\n      pushDistance: pushDistance,\n      push: function push() {\n        setPushed(true);\n      },\n      pull: function pull() {\n        setPushed(false);\n      }\n    };\n  }, [pushDistance]);\n\n  // ========================= ScrollLock =========================\n  // Tell parent to push\n  React.useEffect(function () {\n    if (open) {\n      var _parentContext$push;\n      parentContext === null || parentContext === void 0 || (_parentContext$push = parentContext.push) === null || _parentContext$push === void 0 || _parentContext$push.call(parentContext);\n    } else {\n      var _parentContext$pull;\n      parentContext === null || parentContext === void 0 || (_parentContext$pull = parentContext.pull) === null || _parentContext$pull === void 0 || _parentContext$pull.call(parentContext);\n    }\n  }, [open]);\n\n  // Clean up\n  React.useEffect(function () {\n    return function () {\n      var _parentContext$pull2;\n      parentContext === null || parentContext === void 0 || (_parentContext$pull2 = parentContext.pull) === null || _parentContext$pull2 === void 0 || _parentContext$pull2.call(parentContext);\n    };\n  }, []);\n\n  // ============================ Mask ============================\n  var maskNode = /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"mask\"\n  }, maskMotion, {\n    visible: mask && open\n  }), function (_ref2, maskRef) {\n    var motionMaskClassName = _ref2.className,\n      motionMaskStyle = _ref2.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionMaskClassName, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.mask, maskClassName),\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionMaskStyle), maskStyle), styles === null || styles === void 0 ? void 0 : styles.mask),\n      onClick: maskClosable && open ? onClose : undefined,\n      ref: maskRef\n    });\n  });\n\n  // =========================== Panel ============================\n  var motionProps = typeof motion === 'function' ? motion(placement) : motion;\n  var wrapperStyle = {};\n  if (pushed && pushDistance) {\n    switch (placement) {\n      case 'top':\n        wrapperStyle.transform = \"translateY(\".concat(pushDistance, \"px)\");\n        break;\n      case 'bottom':\n        wrapperStyle.transform = \"translateY(\".concat(-pushDistance, \"px)\");\n        break;\n      case 'left':\n        wrapperStyle.transform = \"translateX(\".concat(pushDistance, \"px)\");\n        break;\n      default:\n        wrapperStyle.transform = \"translateX(\".concat(-pushDistance, \"px)\");\n        break;\n    }\n  }\n  if (placement === 'left' || placement === 'right') {\n    wrapperStyle.width = parseWidthHeight(width);\n  } else {\n    wrapperStyle.height = parseWidthHeight(height);\n  }\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  var panelNode = /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"panel\"\n  }, motionProps, {\n    visible: open,\n    forceRender: forceRender,\n    onVisibleChanged: function onVisibleChanged(nextVisible) {\n      afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(nextVisible);\n    },\n    removeOnLeave: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-wrapper-hidden\")\n  }), function (_ref3, motionRef) {\n    var motionClassName = _ref3.className,\n      motionStyle = _ref3.style;\n    var content = /*#__PURE__*/React.createElement(DrawerPanel, _extends({\n      id: id,\n      containerRef: motionRef,\n      prefixCls: prefixCls,\n      className: classNames(className, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.content),\n      style: _objectSpread(_objectSpread({}, style), styles === null || styles === void 0 ? void 0 : styles.content)\n    }, pickAttrs(props, {\n      aria: true\n    }), eventHandlers), children);\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      className: classNames(\"\".concat(prefixCls, \"-content-wrapper\"), drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.wrapper, motionClassName),\n      style: _objectSpread(_objectSpread(_objectSpread({}, wrapperStyle), motionStyle), styles === null || styles === void 0 ? void 0 : styles.wrapper)\n    }, pickAttrs(props, {\n      data: true\n    })), drawerRender ? drawerRender(content) : content);\n  });\n\n  // =========================== Render ===========================\n  var containerStyle = _objectSpread({}, rootStyle);\n  if (zIndex) {\n    containerStyle.zIndex = zIndex;\n  }\n  return /*#__PURE__*/React.createElement(DrawerContext.Provider, {\n    value: mergedContext\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), rootClassName, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-open\"), open), \"\".concat(prefixCls, \"-inline\"), inline)),\n    style: containerStyle,\n    tabIndex: -1,\n    ref: panelRef,\n    onKeyDown: onPanelKeyDown\n  }, maskNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelStartRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"start\"\n  }), panelNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelEndRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"end\"\n  })));\n}\nvar RefDrawerPopup = /*#__PURE__*/React.forwardRef(DrawerPopup);\nif (process.env.NODE_ENV !== 'production') {\n  RefDrawerPopup.displayName = 'DrawerPopup';\n}\nexport default RefDrawerPopup;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_objectSpread", "_slicedToArray", "classNames", "CSSMotion", "KeyCode", "pickAttrs", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>er<PERSON><PERSON><PERSON>", "parseWidthHeight", "sentinelStyle", "width", "height", "overflow", "outline", "position", "Drawer<PERSON><PERSON><PERSON>", "props", "ref", "_ref", "_pushConfig$distance", "_pushConfig", "prefixCls", "open", "placement", "inline", "push", "forceRender", "autoFocus", "keyboard", "drawerClassNames", "rootClassName", "rootStyle", "zIndex", "className", "id", "style", "motion", "children", "mask", "maskClosable", "maskMotion", "maskClassName", "maskStyle", "afterOpenChange", "onClose", "onMouseEnter", "onMouseOver", "onMouseLeave", "onClick", "onKeyDown", "onKeyUp", "styles", "drawerRender", "panelRef", "useRef", "sentinelStartRef", "sentinelEndRef", "useImperativeHandle", "current", "onPanelKeyDown", "event", "keyCode", "shift<PERSON>ey", "TAB", "document", "activeElement", "_sentinelStartRef$cur", "focus", "preventScroll", "_sentinelEndRef$curre", "ESC", "stopPropagation", "useEffect", "_panelRef$current", "_React$useState", "useState", "_React$useState2", "pushed", "setPushed", "parentContext", "useContext", "pushConfig", "distance", "pushDistance", "mergedContext", "useMemo", "pull", "_parentContext$push", "call", "_parentContext$pull", "_parentContext$pull2", "maskNode", "createElement", "key", "visible", "_ref2", "maskRef", "motionMaskClassName", "motionMaskStyle", "concat", "undefined", "motionProps", "wrapperStyle", "transform", "eventHandlers", "panelNode", "onVisibleChanged", "nextVisible", "removeOnLeave", "leavedClassName", "_ref3", "motionRef", "motionClassName", "motionStyle", "content", "containerRef", "aria", "wrapper", "data", "containerStyle", "Provider", "value", "tabIndex", "RefDrawerPopup", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-drawer/es/DrawerPopup.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport DrawerContext from \"./context\";\nimport DrawerPanel from \"./DrawerPanel\";\nimport { parseWidthHeight } from \"./util\";\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none',\n  position: 'absolute'\n};\nfunction DrawerPopup(props, ref) {\n  var _ref, _pushConfig$distance, _pushConfig;\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    placement = props.placement,\n    inline = props.inline,\n    push = props.push,\n    forceRender = props.forceRender,\n    autoFocus = props.autoFocus,\n    keyboard = props.keyboard,\n    drawerClassNames = props.classNames,\n    rootClassName = props.rootClassName,\n    rootStyle = props.rootStyle,\n    zIndex = props.zIndex,\n    className = props.className,\n    id = props.id,\n    style = props.style,\n    motion = props.motion,\n    width = props.width,\n    height = props.height,\n    children = props.children,\n    mask = props.mask,\n    maskClosable = props.maskClosable,\n    maskMotion = props.maskMotion,\n    maskClassName = props.maskClassName,\n    maskStyle = props.maskStyle,\n    afterOpenChange = props.afterOpenChange,\n    onClose = props.onClose,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp,\n    styles = props.styles,\n    drawerRender = props.drawerRender;\n\n  // ================================ Refs ================================\n  var panelRef = React.useRef();\n  var sentinelStartRef = React.useRef();\n  var sentinelEndRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return panelRef.current;\n  });\n  var onPanelKeyDown = function onPanelKeyDown(event) {\n    var keyCode = event.keyCode,\n      shiftKey = event.shiftKey;\n    switch (keyCode) {\n      // Tab active\n      case KeyCode.TAB:\n        {\n          if (keyCode === KeyCode.TAB) {\n            if (!shiftKey && document.activeElement === sentinelEndRef.current) {\n              var _sentinelStartRef$cur;\n              (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 || _sentinelStartRef$cur.focus({\n                preventScroll: true\n              });\n            } else if (shiftKey && document.activeElement === sentinelStartRef.current) {\n              var _sentinelEndRef$curre;\n              (_sentinelEndRef$curre = sentinelEndRef.current) === null || _sentinelEndRef$curre === void 0 || _sentinelEndRef$curre.focus({\n                preventScroll: true\n              });\n            }\n          }\n          break;\n        }\n\n      // Close\n      case KeyCode.ESC:\n        {\n          if (onClose && keyboard) {\n            event.stopPropagation();\n            onClose(event);\n          }\n          break;\n        }\n    }\n  };\n\n  // ========================== Control ===========================\n  // Auto Focus\n  React.useEffect(function () {\n    if (open && autoFocus) {\n      var _panelRef$current;\n      (_panelRef$current = panelRef.current) === null || _panelRef$current === void 0 || _panelRef$current.focus({\n        preventScroll: true\n      });\n    }\n  }, [open]);\n\n  // ============================ Push ============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    pushed = _React$useState2[0],\n    setPushed = _React$useState2[1];\n  var parentContext = React.useContext(DrawerContext);\n\n  // Merge push distance\n  var pushConfig;\n  if (typeof push === 'boolean') {\n    pushConfig = push ? {} : {\n      distance: 0\n    };\n  } else {\n    pushConfig = push || {};\n  }\n  var pushDistance = (_ref = (_pushConfig$distance = (_pushConfig = pushConfig) === null || _pushConfig === void 0 ? void 0 : _pushConfig.distance) !== null && _pushConfig$distance !== void 0 ? _pushConfig$distance : parentContext === null || parentContext === void 0 ? void 0 : parentContext.pushDistance) !== null && _ref !== void 0 ? _ref : 180;\n  var mergedContext = React.useMemo(function () {\n    return {\n      pushDistance: pushDistance,\n      push: function push() {\n        setPushed(true);\n      },\n      pull: function pull() {\n        setPushed(false);\n      }\n    };\n  }, [pushDistance]);\n\n  // ========================= ScrollLock =========================\n  // Tell parent to push\n  React.useEffect(function () {\n    if (open) {\n      var _parentContext$push;\n      parentContext === null || parentContext === void 0 || (_parentContext$push = parentContext.push) === null || _parentContext$push === void 0 || _parentContext$push.call(parentContext);\n    } else {\n      var _parentContext$pull;\n      parentContext === null || parentContext === void 0 || (_parentContext$pull = parentContext.pull) === null || _parentContext$pull === void 0 || _parentContext$pull.call(parentContext);\n    }\n  }, [open]);\n\n  // Clean up\n  React.useEffect(function () {\n    return function () {\n      var _parentContext$pull2;\n      parentContext === null || parentContext === void 0 || (_parentContext$pull2 = parentContext.pull) === null || _parentContext$pull2 === void 0 || _parentContext$pull2.call(parentContext);\n    };\n  }, []);\n\n  // ============================ Mask ============================\n  var maskNode = /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"mask\"\n  }, maskMotion, {\n    visible: mask && open\n  }), function (_ref2, maskRef) {\n    var motionMaskClassName = _ref2.className,\n      motionMaskStyle = _ref2.style;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionMaskClassName, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.mask, maskClassName),\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionMaskStyle), maskStyle), styles === null || styles === void 0 ? void 0 : styles.mask),\n      onClick: maskClosable && open ? onClose : undefined,\n      ref: maskRef\n    });\n  });\n\n  // =========================== Panel ============================\n  var motionProps = typeof motion === 'function' ? motion(placement) : motion;\n  var wrapperStyle = {};\n  if (pushed && pushDistance) {\n    switch (placement) {\n      case 'top':\n        wrapperStyle.transform = \"translateY(\".concat(pushDistance, \"px)\");\n        break;\n      case 'bottom':\n        wrapperStyle.transform = \"translateY(\".concat(-pushDistance, \"px)\");\n        break;\n      case 'left':\n        wrapperStyle.transform = \"translateX(\".concat(pushDistance, \"px)\");\n        break;\n      default:\n        wrapperStyle.transform = \"translateX(\".concat(-pushDistance, \"px)\");\n        break;\n    }\n  }\n  if (placement === 'left' || placement === 'right') {\n    wrapperStyle.width = parseWidthHeight(width);\n  } else {\n    wrapperStyle.height = parseWidthHeight(height);\n  }\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  var panelNode = /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    key: \"panel\"\n  }, motionProps, {\n    visible: open,\n    forceRender: forceRender,\n    onVisibleChanged: function onVisibleChanged(nextVisible) {\n      afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(nextVisible);\n    },\n    removeOnLeave: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-wrapper-hidden\")\n  }), function (_ref3, motionRef) {\n    var motionClassName = _ref3.className,\n      motionStyle = _ref3.style;\n    var content = /*#__PURE__*/React.createElement(DrawerPanel, _extends({\n      id: id,\n      containerRef: motionRef,\n      prefixCls: prefixCls,\n      className: classNames(className, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.content),\n      style: _objectSpread(_objectSpread({}, style), styles === null || styles === void 0 ? void 0 : styles.content)\n    }, pickAttrs(props, {\n      aria: true\n    }), eventHandlers), children);\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      className: classNames(\"\".concat(prefixCls, \"-content-wrapper\"), drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.wrapper, motionClassName),\n      style: _objectSpread(_objectSpread(_objectSpread({}, wrapperStyle), motionStyle), styles === null || styles === void 0 ? void 0 : styles.wrapper)\n    }, pickAttrs(props, {\n      data: true\n    })), drawerRender ? drawerRender(content) : content);\n  });\n\n  // =========================== Render ===========================\n  var containerStyle = _objectSpread({}, rootStyle);\n  if (zIndex) {\n    containerStyle.zIndex = zIndex;\n  }\n  return /*#__PURE__*/React.createElement(DrawerContext.Provider, {\n    value: mergedContext\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), rootClassName, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-open\"), open), \"\".concat(prefixCls, \"-inline\"), inline)),\n    style: containerStyle,\n    tabIndex: -1,\n    ref: panelRef,\n    onKeyDown: onPanelKeyDown\n  }, maskNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelStartRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"start\"\n  }), panelNode, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelEndRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\",\n    \"data-sentinel\": \"end\"\n  })));\n}\nvar RefDrawerPopup = /*#__PURE__*/React.forwardRef(DrawerPopup);\nif (process.env.NODE_ENV !== 'production') {\n  RefDrawerPopup.displayName = 'DrawerPopup';\n}\nexport default RefDrawerPopup;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,gBAAgB,QAAQ,QAAQ;AACzC,IAAIC,aAAa,GAAG;EAClBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE;AACZ,CAAC;AACD,SAASC,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/B,IAAIC,IAAI,EAAEC,oBAAoB,EAAEC,WAAW;EAC3C,IAAIC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC7BC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,gBAAgB,GAAGb,KAAK,CAACf,UAAU;IACnC6B,aAAa,GAAGd,KAAK,CAACc,aAAa;IACnCC,SAAS,GAAGf,KAAK,CAACe,SAAS;IAC3BC,MAAM,GAAGhB,KAAK,CAACgB,MAAM;IACrBC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,EAAE,GAAGlB,KAAK,CAACkB,EAAE;IACbC,KAAK,GAAGnB,KAAK,CAACmB,KAAK;IACnBC,MAAM,GAAGpB,KAAK,CAACoB,MAAM;IACrB1B,KAAK,GAAGM,KAAK,CAACN,KAAK;IACnBC,MAAM,GAAGK,KAAK,CAACL,MAAM;IACrB0B,QAAQ,GAAGrB,KAAK,CAACqB,QAAQ;IACzBC,IAAI,GAAGtB,KAAK,CAACsB,IAAI;IACjBC,YAAY,GAAGvB,KAAK,CAACuB,YAAY;IACjCC,UAAU,GAAGxB,KAAK,CAACwB,UAAU;IAC7BC,aAAa,GAAGzB,KAAK,CAACyB,aAAa;IACnCC,SAAS,GAAG1B,KAAK,CAAC0B,SAAS;IAC3BC,eAAe,GAAG3B,KAAK,CAAC2B,eAAe;IACvCC,OAAO,GAAG5B,KAAK,CAAC4B,OAAO;IACvBC,YAAY,GAAG7B,KAAK,CAAC6B,YAAY;IACjCC,WAAW,GAAG9B,KAAK,CAAC8B,WAAW;IAC/BC,YAAY,GAAG/B,KAAK,CAAC+B,YAAY;IACjCC,OAAO,GAAGhC,KAAK,CAACgC,OAAO;IACvBC,SAAS,GAAGjC,KAAK,CAACiC,SAAS;IAC3BC,OAAO,GAAGlC,KAAK,CAACkC,OAAO;IACvBC,MAAM,GAAGnC,KAAK,CAACmC,MAAM;IACrBC,YAAY,GAAGpC,KAAK,CAACoC,YAAY;;EAEnC;EACA,IAAIC,QAAQ,GAAGhD,KAAK,CAACiD,MAAM,CAAC,CAAC;EAC7B,IAAIC,gBAAgB,GAAGlD,KAAK,CAACiD,MAAM,CAAC,CAAC;EACrC,IAAIE,cAAc,GAAGnD,KAAK,CAACiD,MAAM,CAAC,CAAC;EACnCjD,KAAK,CAACoD,mBAAmB,CAACxC,GAAG,EAAE,YAAY;IACzC,OAAOoC,QAAQ,CAACK,OAAO;EACzB,CAAC,CAAC;EACF,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;IAClD,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;MACzBC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3B,QAAQD,OAAO;MACb;MACA,KAAK1D,OAAO,CAAC4D,GAAG;QACd;UACE,IAAIF,OAAO,KAAK1D,OAAO,CAAC4D,GAAG,EAAE;YAC3B,IAAI,CAACD,QAAQ,IAAIE,QAAQ,CAACC,aAAa,KAAKT,cAAc,CAACE,OAAO,EAAE;cAClE,IAAIQ,qBAAqB;cACzB,CAACA,qBAAqB,GAAGX,gBAAgB,CAACG,OAAO,MAAM,IAAI,IAAIQ,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,KAAK,CAAC;gBAC7HC,aAAa,EAAE;cACjB,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIN,QAAQ,IAAIE,QAAQ,CAACC,aAAa,KAAKV,gBAAgB,CAACG,OAAO,EAAE;cAC1E,IAAIW,qBAAqB;cACzB,CAACA,qBAAqB,GAAGb,cAAc,CAACE,OAAO,MAAM,IAAI,IAAIW,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACF,KAAK,CAAC;gBAC3HC,aAAa,EAAE;cACjB,CAAC,CAAC;YACJ;UACF;UACA;QACF;;MAEF;MACA,KAAKjE,OAAO,CAACmE,GAAG;QACd;UACE,IAAI1B,OAAO,IAAIhB,QAAQ,EAAE;YACvBgC,KAAK,CAACW,eAAe,CAAC,CAAC;YACvB3B,OAAO,CAACgB,KAAK,CAAC;UAChB;UACA;QACF;IACJ;EACF,CAAC;;EAED;EACA;EACAvD,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,IAAIlD,IAAI,IAAIK,SAAS,EAAE;MACrB,IAAI8C,iBAAiB;MACrB,CAACA,iBAAiB,GAAGpB,QAAQ,CAACK,OAAO,MAAM,IAAI,IAAIe,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACN,KAAK,CAAC;QACzGC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC9C,IAAI,CAAC,CAAC;;EAEV;EACA,IAAIoD,eAAe,GAAGrE,KAAK,CAACsE,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAG5E,cAAc,CAAC0E,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC,IAAIG,aAAa,GAAG1E,KAAK,CAAC2E,UAAU,CAAC1E,aAAa,CAAC;;EAEnD;EACA,IAAI2E,UAAU;EACd,IAAI,OAAOxD,IAAI,KAAK,SAAS,EAAE;IAC7BwD,UAAU,GAAGxD,IAAI,GAAG,CAAC,CAAC,GAAG;MACvByD,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC,MAAM;IACLD,UAAU,GAAGxD,IAAI,IAAI,CAAC,CAAC;EACzB;EACA,IAAI0D,YAAY,GAAG,CAACjE,IAAI,GAAG,CAACC,oBAAoB,GAAG,CAACC,WAAW,GAAG6D,UAAU,MAAM,IAAI,IAAI7D,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC8D,QAAQ,MAAM,IAAI,IAAI/D,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAG4D,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,YAAY,MAAM,IAAI,IAAIjE,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,GAAG;EACzV,IAAIkE,aAAa,GAAG/E,KAAK,CAACgF,OAAO,CAAC,YAAY;IAC5C,OAAO;MACLF,YAAY,EAAEA,YAAY;MAC1B1D,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpBqD,SAAS,CAAC,IAAI,CAAC;MACjB,CAAC;MACDQ,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpBR,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;EACH,CAAC,EAAE,CAACK,YAAY,CAAC,CAAC;;EAElB;EACA;EACA9E,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,IAAIlD,IAAI,EAAE;MACR,IAAIiE,mBAAmB;MACvBR,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAI,CAACQ,mBAAmB,GAAGR,aAAa,CAACtD,IAAI,MAAM,IAAI,IAAI8D,mBAAmB,KAAK,KAAK,CAAC,IAAIA,mBAAmB,CAACC,IAAI,CAACT,aAAa,CAAC;IACxL,CAAC,MAAM;MACL,IAAIU,mBAAmB;MACvBV,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAI,CAACU,mBAAmB,GAAGV,aAAa,CAACO,IAAI,MAAM,IAAI,IAAIG,mBAAmB,KAAK,KAAK,CAAC,IAAIA,mBAAmB,CAACD,IAAI,CAACT,aAAa,CAAC;IACxL;EACF,CAAC,EAAE,CAACzD,IAAI,CAAC,CAAC;;EAEV;EACAjB,KAAK,CAACmE,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjB,IAAIkB,oBAAoB;MACxBX,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAI,CAACW,oBAAoB,GAAGX,aAAa,CAACO,IAAI,MAAM,IAAI,IAAII,oBAAoB,KAAK,KAAK,CAAC,IAAIA,oBAAoB,CAACF,IAAI,CAACT,aAAa,CAAC;IAC3L,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIY,QAAQ,GAAG,aAAatF,KAAK,CAACuF,aAAa,CAAC1F,SAAS,EAAEJ,QAAQ,CAAC;IAClE+F,GAAG,EAAE;EACP,CAAC,EAAErD,UAAU,EAAE;IACbsD,OAAO,EAAExD,IAAI,IAAIhB;EACnB,CAAC,CAAC,EAAE,UAAUyE,KAAK,EAAEC,OAAO,EAAE;IAC5B,IAAIC,mBAAmB,GAAGF,KAAK,CAAC9D,SAAS;MACvCiE,eAAe,GAAGH,KAAK,CAAC5D,KAAK;IAC/B,OAAO,aAAa9B,KAAK,CAACuF,aAAa,CAAC,KAAK,EAAE;MAC7C3D,SAAS,EAAEhC,UAAU,CAAC,EAAE,CAACkG,MAAM,CAAC9E,SAAS,EAAE,OAAO,CAAC,EAAE4E,mBAAmB,EAAEpE,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACS,IAAI,EAAEG,aAAa,CAAC;MACnLN,KAAK,EAAEpC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmG,eAAe,CAAC,EAAExD,SAAS,CAAC,EAAES,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACb,IAAI,CAAC;MAC/IU,OAAO,EAAET,YAAY,IAAIjB,IAAI,GAAGsB,OAAO,GAAGwD,SAAS;MACnDnF,GAAG,EAAE+E;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,IAAIK,WAAW,GAAG,OAAOjE,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACb,SAAS,CAAC,GAAGa,MAAM;EAC3E,IAAIkE,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIzB,MAAM,IAAIM,YAAY,EAAE;IAC1B,QAAQ5D,SAAS;MACf,KAAK,KAAK;QACR+E,YAAY,CAACC,SAAS,GAAG,aAAa,CAACJ,MAAM,CAAChB,YAAY,EAAE,KAAK,CAAC;QAClE;MACF,KAAK,QAAQ;QACXmB,YAAY,CAACC,SAAS,GAAG,aAAa,CAACJ,MAAM,CAAC,CAAChB,YAAY,EAAE,KAAK,CAAC;QACnE;MACF,KAAK,MAAM;QACTmB,YAAY,CAACC,SAAS,GAAG,aAAa,CAACJ,MAAM,CAAChB,YAAY,EAAE,KAAK,CAAC;QAClE;MACF;QACEmB,YAAY,CAACC,SAAS,GAAG,aAAa,CAACJ,MAAM,CAAC,CAAChB,YAAY,EAAE,KAAK,CAAC;QACnE;IACJ;EACF;EACA,IAAI5D,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;IACjD+E,YAAY,CAAC5F,KAAK,GAAGF,gBAAgB,CAACE,KAAK,CAAC;EAC9C,CAAC,MAAM;IACL4F,YAAY,CAAC3F,MAAM,GAAGH,gBAAgB,CAACG,MAAM,CAAC;EAChD;EACA,IAAI6F,aAAa,GAAG;IAClB3D,YAAY,EAAEA,YAAY;IAC1BC,WAAW,EAAEA,WAAW;IACxBC,YAAY,EAAEA,YAAY;IAC1BC,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA;EACX,CAAC;EACD,IAAIuD,SAAS,GAAG,aAAapG,KAAK,CAACuF,aAAa,CAAC1F,SAAS,EAAEJ,QAAQ,CAAC;IACnE+F,GAAG,EAAE;EACP,CAAC,EAAEQ,WAAW,EAAE;IACdP,OAAO,EAAExE,IAAI;IACbI,WAAW,EAAEA,WAAW;IACxBgF,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,WAAW,EAAE;MACvDhE,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAACgE,WAAW,CAAC;IACxF,CAAC;IACDC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,EAAE,CAACV,MAAM,CAAC9E,SAAS,EAAE,yBAAyB;EACjE,CAAC,CAAC,EAAE,UAAUyF,KAAK,EAAEC,SAAS,EAAE;IAC9B,IAAIC,eAAe,GAAGF,KAAK,CAAC7E,SAAS;MACnCgF,WAAW,GAAGH,KAAK,CAAC3E,KAAK;IAC3B,IAAI+E,OAAO,GAAG,aAAa7G,KAAK,CAACuF,aAAa,CAACrF,WAAW,EAAET,QAAQ,CAAC;MACnEoC,EAAE,EAAEA,EAAE;MACNiF,YAAY,EAAEJ,SAAS;MACvB1F,SAAS,EAAEA,SAAS;MACpBY,SAAS,EAAEhC,UAAU,CAACgC,SAAS,EAAEJ,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACqF,OAAO,CAAC;MAC9H/E,KAAK,EAAEpC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,KAAK,CAAC,EAAEgB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC+D,OAAO;IAC/G,CAAC,EAAE9G,SAAS,CAACY,KAAK,EAAE;MAClBoG,IAAI,EAAE;IACR,CAAC,CAAC,EAAEZ,aAAa,CAAC,EAAEnE,QAAQ,CAAC;IAC7B,OAAO,aAAahC,KAAK,CAACuF,aAAa,CAAC,KAAK,EAAE9F,QAAQ,CAAC;MACtDmC,SAAS,EAAEhC,UAAU,CAAC,EAAE,CAACkG,MAAM,CAAC9E,SAAS,EAAE,kBAAkB,CAAC,EAAEQ,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACwF,OAAO,EAAEL,eAAe,CAAC;MAC9K7E,KAAK,EAAEpC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuG,YAAY,CAAC,EAAEW,WAAW,CAAC,EAAE9D,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACkE,OAAO;IAClJ,CAAC,EAAEjH,SAAS,CAACY,KAAK,EAAE;MAClBsG,IAAI,EAAE;IACR,CAAC,CAAC,CAAC,EAAElE,YAAY,GAAGA,YAAY,CAAC8D,OAAO,CAAC,GAAGA,OAAO,CAAC;EACtD,CAAC,CAAC;;EAEF;EACA,IAAIK,cAAc,GAAGxH,aAAa,CAAC,CAAC,CAAC,EAAEgC,SAAS,CAAC;EACjD,IAAIC,MAAM,EAAE;IACVuF,cAAc,CAACvF,MAAM,GAAGA,MAAM;EAChC;EACA,OAAO,aAAa3B,KAAK,CAACuF,aAAa,CAACtF,aAAa,CAACkH,QAAQ,EAAE;IAC9DC,KAAK,EAAErC;EACT,CAAC,EAAE,aAAa/E,KAAK,CAACuF,aAAa,CAAC,KAAK,EAAE;IACzC3D,SAAS,EAAEhC,UAAU,CAACoB,SAAS,EAAE,EAAE,CAAC8E,MAAM,CAAC9E,SAAS,EAAE,GAAG,CAAC,CAAC8E,MAAM,CAAC5E,SAAS,CAAC,EAAEO,aAAa,EAAEjC,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACsG,MAAM,CAAC9E,SAAS,EAAE,OAAO,CAAC,EAAEC,IAAI,CAAC,EAAE,EAAE,CAAC6E,MAAM,CAAC9E,SAAS,EAAE,SAAS,CAAC,EAAEG,MAAM,CAAC,CAAC;IAChNW,KAAK,EAAEoF,cAAc;IACrBG,QAAQ,EAAE,CAAC,CAAC;IACZzG,GAAG,EAAEoC,QAAQ;IACbJ,SAAS,EAAEU;EACb,CAAC,EAAEgC,QAAQ,EAAE,aAAatF,KAAK,CAACuF,aAAa,CAAC,KAAK,EAAE;IACnD8B,QAAQ,EAAE,CAAC;IACXzG,GAAG,EAAEsC,gBAAgB;IACrBpB,KAAK,EAAE1B,aAAa;IACpB,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE;EACnB,CAAC,CAAC,EAAEgG,SAAS,EAAE,aAAapG,KAAK,CAACuF,aAAa,CAAC,KAAK,EAAE;IACrD8B,QAAQ,EAAE,CAAC;IACXzG,GAAG,EAAEuC,cAAc;IACnBrB,KAAK,EAAE1B,aAAa;IACpB,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC;AACN;AACA,IAAIkH,cAAc,GAAG,aAAatH,KAAK,CAACuH,UAAU,CAAC7G,WAAW,CAAC;AAC/D,IAAI8G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,cAAc,CAACK,WAAW,GAAG,aAAa;AAC5C;AACA,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
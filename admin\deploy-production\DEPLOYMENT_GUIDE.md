# 🚀 后台管理系统部署教程

## 📋 **部署概览**

**目标服务器路径**: `/www/wwwroot/h5.haokajiyun.com/admin`  
**API服务器路径**: `/www/wwwroot/h5.haokajiyun.com/public` (已部署)  
**访问地址**: https://h5.haokajiyun.com/admin  
**API地址**: https://h5.haokajiyun.com/api  

## ✅ **部署前检查**

### **1. 服务器环境确认**
- ✅ API已部署到 `/www/wwwroot/h5.haokajiyun.com/public`
- ✅ 域名 `h5.haokajiyun.com` 已配置
- ✅ SSL证书已安装
- ✅ Web服务器(Nginx/Apache)已配置

### **2. 部署文件确认**
- ✅ `index.html` - 主页面文件
- ✅ `static/` - 静态资源目录
- ✅ `asset-manifest.json` - 资源清单
- ✅ API配置指向 `https://h5.haokajiyun.com/api`

## 📦 **部署步骤**

### **步骤1: 上传文件**

#### **方法A: 使用FTP/SFTP工具**
1. 打开FTP客户端(如FileZilla、WinSCP等)
2. 连接到您的服务器
3. 导航到 `/www/wwwroot/h5.haokajiyun.com/`
4. 创建 `admin` 目录(如果不存在)
5. 将当前目录下的所有文件上传到 `/www/wwwroot/h5.haokajiyun.com/admin/`

#### **方法B: 使用命令行(如果有SSH访问权限)**
```bash
# 在服务器上创建目录
mkdir -p /www/wwwroot/h5.haokajiyun.com/admin

# 使用scp上传文件
scp -r ./* user@server:/www/wwwroot/h5.haokajiyun.com/admin/
```

### **步骤2: 设置文件权限**
```bash
# 设置目录权限
chmod 755 /www/wwwroot/h5.haokajiyun.com/admin

# 设置文件权限
find /www/wwwroot/h5.haokajiyun.com/admin -type f -exec chmod 644 {} \;
find /www/wwwroot/h5.haokajiyun.com/admin -type d -exec chmod 755 {} \;
```

### **步骤3: 配置Web服务器**

#### **Nginx配置**
在 `/etc/nginx/sites-available/h5.haokajiyun.com` 或相应配置文件中添加：

```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name h5.haokajiyun.com;
    
    # SSL配置(如果使用HTTPS)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # API代理配置
    location /api/ {
        root /www/wwwroot/h5.haokajiyun.com/public;
        try_files $uri $uri/ /index.php?$query_string;
        
        # PHP配置
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
        }
    }
    
    # Admin后台配置
    location /admin/ {
        alias /www/wwwroot/h5.haokajiyun.com/admin/;
        try_files $uri $uri/ /admin/index.html;
        
        # 静态资源缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # HTML文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # 根目录重定向
    location = / {
        return 301 /admin/;
    }
}
```

#### **Apache配置**
在 `/etc/apache2/sites-available/h5.haokajiyun.com.conf` 中配置：

```apache
<VirtualHost *:80>
<VirtualHost *:443>
    ServerName h5.haokajiyun.com
    DocumentRoot /www/wwwroot/h5.haokajiyun.com/public
    
    # SSL配置(如果使用HTTPS)
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    
    # API配置
    <Directory "/www/wwwroot/h5.haokajiyun.com/public">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # Admin后台配置
    Alias /admin /www/wwwroot/h5.haokajiyun.com/admin
    <Directory "/www/wwwroot/h5.haokajiyun.com/admin">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteBase /admin/
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /admin/index.html [L]
        
        # 静态资源缓存
        <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
            ExpiresActive On
            ExpiresDefault "access plus 1 year"
        </FilesMatch>
        
        # HTML文件不缓存
        <FilesMatch "\.html$">
            ExpiresActive On
            ExpiresDefault "access plus 0 seconds"
        </FilesMatch>
    </Directory>
</VirtualHost>
```

### **步骤4: 重启Web服务器**
```bash
# Nginx
sudo systemctl reload nginx

# Apache
sudo systemctl reload apache2
```

## 🔍 **部署验证**

### **1. 基础访问测试**
- 访问: https://h5.haokajiyun.com/admin/
- 检查页面是否正常加载
- 确认没有404错误

### **2. 功能测试**
- ✅ 左侧导航菜单正常
- ✅ 产品管理页面加载
- ✅ 订单管理页面加载
- ✅ API接口调用正常

### **3. 浏览器控制台检查**
- 按F12打开开发者工具
- 检查Console标签页无错误
- 检查Network标签页API请求正常

### **4. API连接测试**
访问测试页面: https://h5.haokajiyun.com/admin/test-api.html

## 🛠️ **故障排除**

### **问题1: 页面显示空白**
**原因**: 静态资源路径错误或权限问题
**解决**:
```bash
# 检查文件权限
ls -la /www/wwwroot/h5.haokajiyun.com/admin/

# 检查Nginx/Apache错误日志
tail -f /var/log/nginx/error.log
tail -f /var/log/apache2/error.log
```

### **问题2: API请求失败**
**原因**: CORS配置或API服务问题
**解决**:
1. 检查API服务是否正常运行
2. 确认CORS配置正确
3. 检查防火墙设置

### **问题3: 路由不工作**
**原因**: Web服务器未配置SPA路由支持
**解决**: 确保Web服务器配置中包含fallback到index.html的规则

### **问题4: 静态资源404**
**原因**: 静态资源路径配置错误
**解决**: 检查static目录是否正确上传

## 📊 **性能优化建议**

### **1. 启用Gzip压缩**
```nginx
# Nginx
gzip on;
gzip_types text/css application/javascript application/json;
```

### **2. 设置缓存策略**
- 静态资源(CSS/JS): 1年缓存
- HTML文件: 不缓存
- API响应: 根据需要设置

### **3. 启用HTTP/2**
```nginx
listen 443 ssl http2;
```

## 🔒 **安全配置**

### **1. 安全头设置**
```nginx
add_header X-Frame-Options "SAMEORIGIN";
add_header X-Content-Type-Options "nosniff";
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

### **2. 隐藏服务器信息**
```nginx
server_tokens off;
```

## 📞 **技术支持**

### **联系信息**
- **部署文件位置**: 当前目录
- **服务器路径**: `/www/wwwroot/h5.haokajiyun.com/admin`
- **访问地址**: https://h5.haokajiyun.com/admin
- **API地址**: https://h5.haokajiyun.com/api

### **常用命令**
```bash
# 检查服务状态
systemctl status nginx
systemctl status apache2

# 查看日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 测试配置
nginx -t
apache2ctl configtest
```

---

## 🎉 **部署完成**

按照以上步骤完成部署后，您的后台管理系统将在以下地址可用：

**🔗 访问地址**: https://h5.haokajiyun.com/admin

**🎯 下一步**: 测试所有功能并进行用户培训

{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { toPathOptions } from \"../utils/treeUtil\";\nimport * as React from 'react';\nimport { toPathKey } from \"../utils/commonUtil\";\nexport default (function (rawValues, options, fieldNames, multiple, displayRender) {\n  return React.useMemo(function () {\n    var mergedDisplayRender = displayRender ||\n    // Default displayRender\n    function (labels) {\n      var mergedLabels = multiple ? labels.slice(-1) : labels;\n      var SPLIT = ' / ';\n      if (mergedLabels.every(function (label) {\n        return ['string', 'number'].includes(_typeof(label));\n      })) {\n        return mergedLabels.join(SPLIT);\n      }\n\n      // If exist non-string value, use ReactNode instead\n      return mergedLabels.reduce(function (list, label, index) {\n        var keyedLabel = /*#__PURE__*/React.isValidElement(label) ? /*#__PURE__*/React.cloneElement(label, {\n          key: index\n        }) : label;\n        if (index === 0) {\n          return [keyedLabel];\n        }\n        return [].concat(_toConsumableArray(list), [SPLIT, keyedLabel]);\n      }, []);\n    };\n    return rawValues.map(function (valueCells) {\n      var _valueOptions;\n      var valueOptions = toPathOptions(valueCells, options, fieldNames);\n      var label = mergedDisplayRender(valueOptions.map(function (_ref) {\n        var _option$fieldNames$la;\n        var option = _ref.option,\n          value = _ref.value;\n        return (_option$fieldNames$la = option === null || option === void 0 ? void 0 : option[fieldNames.label]) !== null && _option$fieldNames$la !== void 0 ? _option$fieldNames$la : value;\n      }), valueOptions.map(function (_ref2) {\n        var option = _ref2.option;\n        return option;\n      }));\n      var value = toPathKey(valueCells);\n      return {\n        label: label,\n        value: value,\n        key: value,\n        valueCells: valueCells,\n        disabled: (_valueOptions = valueOptions[valueOptions.length - 1]) === null || _valueOptions === void 0 || (_valueOptions = _valueOptions.option) === null || _valueOptions === void 0 ? void 0 : _valueOptions.disabled\n      };\n    });\n  }, [rawValues, options, fieldNames, displayRender, multiple]);\n});", "map": {"version": 3, "names": ["_toConsumableArray", "_typeof", "toPathOptions", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rawValues", "options", "fieldNames", "multiple", "displayRender", "useMemo", "mergedDisplayRender", "labels", "mergedLabels", "slice", "SPLIT", "every", "label", "includes", "join", "reduce", "list", "index", "keyed<PERSON><PERSON><PERSON>", "isValidElement", "cloneElement", "key", "concat", "map", "valueCells", "_valueOptions", "valueOptions", "_ref", "_option$fieldNames$la", "option", "value", "_ref2", "disabled", "length"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-cascader/es/hooks/useDisplayValues.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { toPathOptions } from \"../utils/treeUtil\";\nimport * as React from 'react';\nimport { toPathKey } from \"../utils/commonUtil\";\nexport default (function (rawValues, options, fieldNames, multiple, displayRender) {\n  return React.useMemo(function () {\n    var mergedDisplayRender = displayRender ||\n    // Default displayRender\n    function (labels) {\n      var mergedLabels = multiple ? labels.slice(-1) : labels;\n      var SPLIT = ' / ';\n      if (mergedLabels.every(function (label) {\n        return ['string', 'number'].includes(_typeof(label));\n      })) {\n        return mergedLabels.join(SPLIT);\n      }\n\n      // If exist non-string value, use ReactNode instead\n      return mergedLabels.reduce(function (list, label, index) {\n        var keyedLabel = /*#__PURE__*/React.isValidElement(label) ? /*#__PURE__*/React.cloneElement(label, {\n          key: index\n        }) : label;\n        if (index === 0) {\n          return [keyedLabel];\n        }\n        return [].concat(_toConsumableArray(list), [SPLIT, keyedLabel]);\n      }, []);\n    };\n    return rawValues.map(function (valueCells) {\n      var _valueOptions;\n      var valueOptions = toPathOptions(valueCells, options, fieldNames);\n      var label = mergedDisplayRender(valueOptions.map(function (_ref) {\n        var _option$fieldNames$la;\n        var option = _ref.option,\n          value = _ref.value;\n        return (_option$fieldNames$la = option === null || option === void 0 ? void 0 : option[fieldNames.label]) !== null && _option$fieldNames$la !== void 0 ? _option$fieldNames$la : value;\n      }), valueOptions.map(function (_ref2) {\n        var option = _ref2.option;\n        return option;\n      }));\n      var value = toPathKey(valueCells);\n      return {\n        label: label,\n        value: value,\n        key: value,\n        valueCells: valueCells,\n        disabled: (_valueOptions = valueOptions[valueOptions.length - 1]) === null || _valueOptions === void 0 || (_valueOptions = _valueOptions.option) === null || _valueOptions === void 0 ? void 0 : _valueOptions.disabled\n      };\n    });\n  }, [rawValues, options, fieldNames, displayRender, multiple]);\n});"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,gBAAgB,UAAUC,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EACjF,OAAON,KAAK,CAACO,OAAO,CAAC,YAAY;IAC/B,IAAIC,mBAAmB,GAAGF,aAAa;IACvC;IACA,UAAUG,MAAM,EAAE;MAChB,IAAIC,YAAY,GAAGL,QAAQ,GAAGI,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGF,MAAM;MACvD,IAAIG,KAAK,GAAG,KAAK;MACjB,IAAIF,YAAY,CAACG,KAAK,CAAC,UAAUC,KAAK,EAAE;QACtC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACjB,OAAO,CAACgB,KAAK,CAAC,CAAC;MACtD,CAAC,CAAC,EAAE;QACF,OAAOJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MACjC;;MAEA;MACA,OAAOF,YAAY,CAACO,MAAM,CAAC,UAAUC,IAAI,EAAEJ,KAAK,EAAEK,KAAK,EAAE;QACvD,IAAIC,UAAU,GAAG,aAAapB,KAAK,CAACqB,cAAc,CAACP,KAAK,CAAC,GAAG,aAAad,KAAK,CAACsB,YAAY,CAACR,KAAK,EAAE;UACjGS,GAAG,EAAEJ;QACP,CAAC,CAAC,GAAGL,KAAK;QACV,IAAIK,KAAK,KAAK,CAAC,EAAE;UACf,OAAO,CAACC,UAAU,CAAC;QACrB;QACA,OAAO,EAAE,CAACI,MAAM,CAAC3B,kBAAkB,CAACqB,IAAI,CAAC,EAAE,CAACN,KAAK,EAAEQ,UAAU,CAAC,CAAC;MACjE,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IACD,OAAOlB,SAAS,CAACuB,GAAG,CAAC,UAAUC,UAAU,EAAE;MACzC,IAAIC,aAAa;MACjB,IAAIC,YAAY,GAAG7B,aAAa,CAAC2B,UAAU,EAAEvB,OAAO,EAAEC,UAAU,CAAC;MACjE,IAAIU,KAAK,GAAGN,mBAAmB,CAACoB,YAAY,CAACH,GAAG,CAAC,UAAUI,IAAI,EAAE;QAC/D,IAAIC,qBAAqB;QACzB,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;UACtBC,KAAK,GAAGH,IAAI,CAACG,KAAK;QACpB,OAAO,CAACF,qBAAqB,GAAGC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3B,UAAU,CAACU,KAAK,CAAC,MAAM,IAAI,IAAIgB,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGE,KAAK;MACxL,CAAC,CAAC,EAAEJ,YAAY,CAACH,GAAG,CAAC,UAAUQ,KAAK,EAAE;QACpC,IAAIF,MAAM,GAAGE,KAAK,CAACF,MAAM;QACzB,OAAOA,MAAM;MACf,CAAC,CAAC,CAAC;MACH,IAAIC,KAAK,GAAG/B,SAAS,CAACyB,UAAU,CAAC;MACjC,OAAO;QACLZ,KAAK,EAAEA,KAAK;QACZkB,KAAK,EAAEA,KAAK;QACZT,GAAG,EAAES,KAAK;QACVN,UAAU,EAAEA,UAAU;QACtBQ,QAAQ,EAAE,CAACP,aAAa,GAAGC,YAAY,CAACA,YAAY,CAACO,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIR,aAAa,KAAK,KAAK,CAAC,IAAI,CAACA,aAAa,GAAGA,aAAa,CAACI,MAAM,MAAM,IAAI,IAAIJ,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO;MACjN,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChC,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAEE,aAAa,EAAED,QAAQ,CAAC,CAAC;AAC/D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { operationUnit } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nimport { getCopyableStyles, getEditableStyles, getEllipsisStyles, getLinkStyles, getResetStyles, getTitleStyles } from './mixins';\nconst genTypographyStyle = token => {\n  const {\n    componentCls,\n    titleMarginTop\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n      color: token.colorText,\n      wordBreak: 'break-word',\n      lineHeight: token.lineHeight,\n      [\"&\".concat(componentCls, \"-secondary\")]: {\n        color: token.colorTextDescription\n      },\n      [\"&\".concat(componentCls, \"-success\")]: {\n        color: token.colorSuccessText\n      },\n      [\"&\".concat(componentCls, \"-warning\")]: {\n        color: token.colorWarningText\n      },\n      [\"&\".concat(componentCls, \"-danger\")]: {\n        color: token.colorErrorText,\n        'a&:active, a&:focus': {\n          color: token.colorErrorTextActive\n        },\n        'a&:hover': {\n          color: token.colorErrorTextHover\n        }\n      },\n      [\"&\".concat(componentCls, \"-disabled\")]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        userSelect: 'none'\n      },\n      [\"\\n        div&,\\n        p\\n      \"]: {\n        marginBottom: '1em'\n      }\n    }, getTitleStyles(token)), {\n      [\"\\n      & + h1\".concat(componentCls, \",\\n      & + h2\").concat(componentCls, \",\\n      & + h3\").concat(componentCls, \",\\n      & + h4\").concat(componentCls, \",\\n      & + h5\").concat(componentCls, \"\\n      \")]: {\n        marginTop: titleMarginTop\n      },\n      [\"\\n      div,\\n      ul,\\n      li,\\n      p,\\n      h1,\\n      h2,\\n      h3,\\n      h4,\\n      h5\"]: {\n        [\"\\n        + h1,\\n        + h2,\\n        + h3,\\n        + h4,\\n        + h5\\n        \"]: {\n          marginTop: titleMarginTop\n        }\n      }\n    }), getResetStyles(token)), getLinkStyles(token)), {\n      // Operation\n      [\"\\n        \".concat(componentCls, \"-expand,\\n        \").concat(componentCls, \"-collapse,\\n        \").concat(componentCls, \"-edit,\\n        \").concat(componentCls, \"-copy\\n      \")]: Object.assign(Object.assign({}, operationUnit(token)), {\n        marginInlineStart: token.marginXXS\n      })\n    }), getEditableStyles(token)), getCopyableStyles(token)), getEllipsisStyles()), {\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    })\n  };\n};\nexport const prepareComponentToken = () => ({\n  titleMarginTop: '1.2em',\n  titleMarginBottom: '0.5em'\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Typography', token => [genTypographyStyle(token)], prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\n/* eslint-disable react/no-array-index-key */\nimport React, { useState } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useItems from './hooks/useItems';\nimport useResizable from './hooks/useResizable';\nimport useResize from './hooks/useResize';\nimport useSizes from './hooks/useSizes';\nimport { InternalPanel } from './Panel';\nimport SplitBar from './SplitBar';\nimport useStyle from './style';\nconst Splitter = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    style,\n    layout = 'horizontal',\n    children,\n    rootClassName,\n    onResizeStart,\n    onResize,\n    onResizeEnd,\n    lazy\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('splitter');\n  const prefixCls = getPrefixCls('splitter', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  // ======================== Direct ========================\n  const isVertical = layout === 'vertical';\n  const isRTL = direction === 'rtl';\n  const reverse = !isVertical && isRTL;\n  // ====================== Items Data ======================\n  const items = useItems(children);\n  // >>> Warning for uncontrolled\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Splitter');\n    let existSize = false;\n    let existUndefinedSize = false;\n    items.forEach(item => {\n      if (item.size !== undefined) {\n        existSize = true;\n      } else {\n        existUndefinedSize = true;\n      }\n    });\n    if (existSize && existUndefinedSize && !onResize) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', 'When part of `Splitter.Panel` has `size`, `onResize` is required or change `size` to `defaultSize`.') : void 0;\n    }\n  }\n  // ====================== Container =======================\n  const [containerSize, setContainerSize] = useState();\n  const onContainerResize = size => {\n    const {\n      offsetWidth,\n      offsetHeight\n    } = size;\n    const containerSize = isVertical ? offsetHeight : offsetWidth;\n    // Skip when container has no size, Such as nested in a hidden tab panel\n    // to fix: https://github.com/ant-design/ant-design/issues/51106\n    if (containerSize === 0) {\n      return;\n    }\n    setContainerSize(containerSize);\n  };\n  // ========================= Size =========================\n  const [panelSizes, itemPxSizes, itemPtgSizes, itemPtgMinSizes, itemPtgMaxSizes, updateSizes] = useSizes(items, containerSize);\n  // ====================== Resizable =======================\n  const resizableInfos = useResizable(items, itemPxSizes, isRTL);\n  const [onOffsetStart, onOffsetUpdate, onOffsetEnd, onCollapse, movingIndex] = useResize(items, resizableInfos, itemPtgSizes, containerSize, updateSizes, isRTL);\n  // ======================== Events ========================\n  const onInternalResizeStart = useEvent(index => {\n    onOffsetStart(index);\n    onResizeStart === null || onResizeStart === void 0 ? void 0 : onResizeStart(itemPxSizes);\n  });\n  const onInternalResizeUpdate = useEvent((index, offset, lazyEnd) => {\n    const nextSizes = onOffsetUpdate(index, offset);\n    if (lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n    } else {\n      onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    }\n  });\n  const onInternalResizeEnd = useEvent(lazyEnd => {\n    onOffsetEnd();\n    if (!lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(itemPxSizes);\n    }\n  });\n  const onInternalCollapse = useEvent((index, type) => {\n    const nextSizes = onCollapse(index, type);\n    onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n  });\n  // ======================== Styles ========================\n  const containerClassName = classNames(prefixCls, className, \"\".concat(prefixCls, \"-\").concat(layout), {\n    [\"\".concat(prefixCls, \"-rtl\")]: isRTL\n  }, rootClassName, contextClassName, cssVarCls, rootCls, hashId);\n  // ======================== Render ========================\n  const maskCls = \"\".concat(prefixCls, \"-mask\");\n  const stackSizes = React.useMemo(() => {\n    const mergedSizes = [];\n    let stack = 0;\n    for (let i = 0; i < items.length; i += 1) {\n      stack += itemPtgSizes[i];\n      mergedSizes.push(stack);\n    }\n    return mergedSizes;\n  }, [itemPtgSizes]);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onContainerResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: mergedStyle,\n    className: containerClassName\n  }, items.map((item, idx) => {\n    // Panel\n    const panel = /*#__PURE__*/React.createElement(InternalPanel, Object.assign({}, item, {\n      prefixCls: prefixCls,\n      size: panelSizes[idx]\n    }));\n    // Split Bar\n    let splitBar = null;\n    const resizableInfo = resizableInfos[idx];\n    if (resizableInfo) {\n      const ariaMinStart = (stackSizes[idx - 1] || 0) + itemPtgMinSizes[idx];\n      const ariaMinEnd = (stackSizes[idx + 1] || 100) - itemPtgMaxSizes[idx + 1];\n      const ariaMaxStart = (stackSizes[idx - 1] || 0) + itemPtgMaxSizes[idx];\n      const ariaMaxEnd = (stackSizes[idx + 1] || 100) - itemPtgMinSizes[idx + 1];\n      splitBar = /*#__PURE__*/React.createElement(SplitBar, {\n        lazy: lazy,\n        index: idx,\n        active: movingIndex === idx,\n        prefixCls: prefixCls,\n        vertical: isVertical,\n        resizable: resizableInfo.resizable,\n        ariaNow: stackSizes[idx] * 100,\n        ariaMin: Math.max(ariaMinStart, ariaMinEnd) * 100,\n        ariaMax: Math.min(ariaMaxStart, ariaMaxEnd) * 100,\n        startCollapsible: resizableInfo.startCollapsible,\n        endCollapsible: resizableInfo.endCollapsible,\n        onOffsetStart: onInternalResizeStart,\n        onOffsetUpdate: (index, offsetX, offsetY, lazyEnd) => {\n          let offset = isVertical ? offsetY : offsetX;\n          if (reverse) {\n            offset = -offset;\n          }\n          onInternalResizeUpdate(index, offset, lazyEnd);\n        },\n        onOffsetEnd: onInternalResizeEnd,\n        onCollapse: onInternalCollapse,\n        containerSize: containerSize || 0\n      });\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: \"split-panel-\".concat(idx)\n    }, panel, splitBar);\n  }), typeof movingIndex === 'number' && (/*#__PURE__*/React.createElement(\"div\", {\n    \"aria-hidden\": true,\n    className: classNames(maskCls, \"\".concat(maskCls, \"-\").concat(layout))\n  })))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Splitter.displayName = 'Splitter';\n}\nexport default Splitter;", "map": {"version": 3, "names": ["React", "useState", "classNames", "ResizeObserver", "useEvent", "devUseW<PERSON>ning", "useComponentConfig", "useCSSVarCls", "useItems", "useResizable", "useResize", "useSizes", "InternalPanel", "SplitBar", "useStyle", "Splitter", "props", "prefixCls", "customizePrefixCls", "className", "style", "layout", "children", "rootClassName", "onResizeStart", "onResize", "onResizeEnd", "lazy", "getPrefixCls", "direction", "contextClassName", "contextStyle", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "isVertical", "isRTL", "reverse", "items", "process", "env", "NODE_ENV", "warning", "existSize", "existUndefinedSize", "for<PERSON>ach", "item", "size", "undefined", "containerSize", "setContainerSize", "onContainerResize", "offsetWidth", "offsetHeight", "panelSizes", "itemPxSizes", "itemPtgSizes", "itemPtgMinSizes", "itemPtgMaxSizes", "updateSizes", "resizableInfos", "onOffsetStart", "onOffsetUpdate", "onOffsetEnd", "onCollapse", "movingIndex", "onInternalResizeStart", "index", "onInternalResizeUpdate", "offset", "lazyEnd", "nextSizes", "onInternalResizeEnd", "onInternalCollapse", "type", "containerClassName", "concat", "maskCls", "stackSizes", "useMemo", "mergedSizes", "stack", "i", "length", "push", "mergedStyle", "Object", "assign", "createElement", "map", "idx", "panel", "splitBar", "resizableInfo", "ariaMinStart", "ariaMinEnd", "ariaMaxStart", "ariaMaxEnd", "active", "vertical", "resizable", "ariaNow", "aria<PERSON><PERSON>", "Math", "max", "ariaMax", "min", "startCollapsible", "endCollapsible", "offsetX", "offsetY", "Fragment", "key", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/splitter/Splitter.js"], "sourcesContent": ["\"use client\";\n\n/* eslint-disable react/no-array-index-key */\nimport React, { useState } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useItems from './hooks/useItems';\nimport useResizable from './hooks/useResizable';\nimport useResize from './hooks/useResize';\nimport useSizes from './hooks/useSizes';\nimport { InternalPanel } from './Panel';\nimport SplitBar from './SplitBar';\nimport useStyle from './style';\nconst Splitter = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    style,\n    layout = 'horizontal',\n    children,\n    rootClassName,\n    onResizeStart,\n    onResize,\n    onResizeEnd,\n    lazy\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('splitter');\n  const prefixCls = getPrefixCls('splitter', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  // ======================== Direct ========================\n  const isVertical = layout === 'vertical';\n  const isRTL = direction === 'rtl';\n  const reverse = !isVertical && isRTL;\n  // ====================== Items Data ======================\n  const items = useItems(children);\n  // >>> Warning for uncontrolled\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Splitter');\n    let existSize = false;\n    let existUndefinedSize = false;\n    items.forEach(item => {\n      if (item.size !== undefined) {\n        existSize = true;\n      } else {\n        existUndefinedSize = true;\n      }\n    });\n    if (existSize && existUndefinedSize && !onResize) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', 'When part of `Splitter.Panel` has `size`, `onResize` is required or change `size` to `defaultSize`.') : void 0;\n    }\n  }\n  // ====================== Container =======================\n  const [containerSize, setContainerSize] = useState();\n  const onContainerResize = size => {\n    const {\n      offsetWidth,\n      offsetHeight\n    } = size;\n    const containerSize = isVertical ? offsetHeight : offsetWidth;\n    // Skip when container has no size, Such as nested in a hidden tab panel\n    // to fix: https://github.com/ant-design/ant-design/issues/51106\n    if (containerSize === 0) {\n      return;\n    }\n    setContainerSize(containerSize);\n  };\n  // ========================= Size =========================\n  const [panelSizes, itemPxSizes, itemPtgSizes, itemPtgMinSizes, itemPtgMaxSizes, updateSizes] = useSizes(items, containerSize);\n  // ====================== Resizable =======================\n  const resizableInfos = useResizable(items, itemPxSizes, isRTL);\n  const [onOffsetStart, onOffsetUpdate, onOffsetEnd, onCollapse, movingIndex] = useResize(items, resizableInfos, itemPtgSizes, containerSize, updateSizes, isRTL);\n  // ======================== Events ========================\n  const onInternalResizeStart = useEvent(index => {\n    onOffsetStart(index);\n    onResizeStart === null || onResizeStart === void 0 ? void 0 : onResizeStart(itemPxSizes);\n  });\n  const onInternalResizeUpdate = useEvent((index, offset, lazyEnd) => {\n    const nextSizes = onOffsetUpdate(index, offset);\n    if (lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n    } else {\n      onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    }\n  });\n  const onInternalResizeEnd = useEvent(lazyEnd => {\n    onOffsetEnd();\n    if (!lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(itemPxSizes);\n    }\n  });\n  const onInternalCollapse = useEvent((index, type) => {\n    const nextSizes = onCollapse(index, type);\n    onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n  });\n  // ======================== Styles ========================\n  const containerClassName = classNames(prefixCls, className, `${prefixCls}-${layout}`, {\n    [`${prefixCls}-rtl`]: isRTL\n  }, rootClassName, contextClassName, cssVarCls, rootCls, hashId);\n  // ======================== Render ========================\n  const maskCls = `${prefixCls}-mask`;\n  const stackSizes = React.useMemo(() => {\n    const mergedSizes = [];\n    let stack = 0;\n    for (let i = 0; i < items.length; i += 1) {\n      stack += itemPtgSizes[i];\n      mergedSizes.push(stack);\n    }\n    return mergedSizes;\n  }, [itemPtgSizes]);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onContainerResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: mergedStyle,\n    className: containerClassName\n  }, items.map((item, idx) => {\n    // Panel\n    const panel = /*#__PURE__*/React.createElement(InternalPanel, Object.assign({}, item, {\n      prefixCls: prefixCls,\n      size: panelSizes[idx]\n    }));\n    // Split Bar\n    let splitBar = null;\n    const resizableInfo = resizableInfos[idx];\n    if (resizableInfo) {\n      const ariaMinStart = (stackSizes[idx - 1] || 0) + itemPtgMinSizes[idx];\n      const ariaMinEnd = (stackSizes[idx + 1] || 100) - itemPtgMaxSizes[idx + 1];\n      const ariaMaxStart = (stackSizes[idx - 1] || 0) + itemPtgMaxSizes[idx];\n      const ariaMaxEnd = (stackSizes[idx + 1] || 100) - itemPtgMinSizes[idx + 1];\n      splitBar = /*#__PURE__*/React.createElement(SplitBar, {\n        lazy: lazy,\n        index: idx,\n        active: movingIndex === idx,\n        prefixCls: prefixCls,\n        vertical: isVertical,\n        resizable: resizableInfo.resizable,\n        ariaNow: stackSizes[idx] * 100,\n        ariaMin: Math.max(ariaMinStart, ariaMinEnd) * 100,\n        ariaMax: Math.min(ariaMaxStart, ariaMaxEnd) * 100,\n        startCollapsible: resizableInfo.startCollapsible,\n        endCollapsible: resizableInfo.endCollapsible,\n        onOffsetStart: onInternalResizeStart,\n        onOffsetUpdate: (index, offsetX, offsetY, lazyEnd) => {\n          let offset = isVertical ? offsetY : offsetX;\n          if (reverse) {\n            offset = -offset;\n          }\n          onInternalResizeUpdate(index, offset, lazyEnd);\n        },\n        onOffsetEnd: onInternalResizeEnd,\n        onCollapse: onInternalCollapse,\n        containerSize: containerSize || 0\n      });\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: `split-panel-${idx}`\n    }, panel, splitBar);\n  }), typeof movingIndex === 'number' && (/*#__PURE__*/React.createElement(\"div\", {\n    \"aria-hidden\": true,\n    className: classNames(maskCls, `${maskCls}-${layout}`)\n  })))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Splitter.displayName = 'Splitter';\n}\nexport default Splitter;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,KAAK;IACLC,MAAM,GAAG,YAAY;IACrBC,QAAQ;IACRC,aAAa;IACbC,aAAa;IACbC,QAAQ;IACRC,WAAW;IACXC;EACF,CAAC,GAAGX,KAAK;EACT,MAAM;IACJY,YAAY;IACZC,SAAS;IACTV,SAAS,EAAEW,gBAAgB;IAC3BV,KAAK,EAAEW;EACT,CAAC,GAAGzB,kBAAkB,CAAC,UAAU,CAAC;EAClC,MAAMW,SAAS,GAAGW,YAAY,CAAC,UAAU,EAAEV,kBAAkB,CAAC;EAC9D,MAAMc,OAAO,GAAGzB,YAAY,CAACU,SAAS,CAAC;EACvC,MAAM,CAACgB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAACG,SAAS,EAAEe,OAAO,CAAC;EACpE;EACA,MAAMI,UAAU,GAAGf,MAAM,KAAK,UAAU;EACxC,MAAMgB,KAAK,GAAGR,SAAS,KAAK,KAAK;EACjC,MAAMS,OAAO,GAAG,CAACF,UAAU,IAAIC,KAAK;EACpC;EACA,MAAME,KAAK,GAAG/B,QAAQ,CAACc,QAAQ,CAAC;EAChC;EACA,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGtC,aAAa,CAAC,UAAU,CAAC;IACzC,IAAIuC,SAAS,GAAG,KAAK;IACrB,IAAIC,kBAAkB,GAAG,KAAK;IAC9BN,KAAK,CAACO,OAAO,CAACC,IAAI,IAAI;MACpB,IAAIA,IAAI,CAACC,IAAI,KAAKC,SAAS,EAAE;QAC3BL,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM;QACLC,kBAAkB,GAAG,IAAI;MAC3B;IACF,CAAC,CAAC;IACF,IAAID,SAAS,IAAIC,kBAAkB,IAAI,CAACpB,QAAQ,EAAE;MAChDe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,qGAAqG,CAAC,GAAG,KAAK,CAAC;IACjL;EACF;EACA;EACA,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,CAAC;EACpD,MAAMmD,iBAAiB,GAAGJ,IAAI,IAAI;IAChC,MAAM;MACJK,WAAW;MACXC;IACF,CAAC,GAAGN,IAAI;IACR,MAAME,aAAa,GAAGd,UAAU,GAAGkB,YAAY,GAAGD,WAAW;IAC7D;IACA;IACA,IAAIH,aAAa,KAAK,CAAC,EAAE;MACvB;IACF;IACAC,gBAAgB,CAACD,aAAa,CAAC;EACjC,CAAC;EACD;EACA,MAAM,CAACK,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC4B,KAAK,EAAEW,aAAa,CAAC;EAC7H;EACA,MAAMW,cAAc,GAAGpD,YAAY,CAAC8B,KAAK,EAAEiB,WAAW,EAAEnB,KAAK,CAAC;EAC9D,MAAM,CAACyB,aAAa,EAAEC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,WAAW,CAAC,GAAGxD,SAAS,CAAC6B,KAAK,EAAEsB,cAAc,EAAEJ,YAAY,EAAEP,aAAa,EAAEU,WAAW,EAAEvB,KAAK,CAAC;EAC/J;EACA,MAAM8B,qBAAqB,GAAG/D,QAAQ,CAACgE,KAAK,IAAI;IAC9CN,aAAa,CAACM,KAAK,CAAC;IACpB5C,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgC,WAAW,CAAC;EAC1F,CAAC,CAAC;EACF,MAAMa,sBAAsB,GAAGjE,QAAQ,CAAC,CAACgE,KAAK,EAAEE,MAAM,EAAEC,OAAO,KAAK;IAClE,MAAMC,SAAS,GAAGT,cAAc,CAACK,KAAK,EAAEE,MAAM,CAAC;IAC/C,IAAIC,OAAO,EAAE;MACX7C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC8C,SAAS,CAAC;IAClF,CAAC,MAAM;MACL/C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+C,SAAS,CAAC;IACzE;EACF,CAAC,CAAC;EACF,MAAMC,mBAAmB,GAAGrE,QAAQ,CAACmE,OAAO,IAAI;IAC9CP,WAAW,CAAC,CAAC;IACb,IAAI,CAACO,OAAO,EAAE;MACZ7C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC8B,WAAW,CAAC;IACpF;EACF,CAAC,CAAC;EACF,MAAMkB,kBAAkB,GAAGtE,QAAQ,CAAC,CAACgE,KAAK,EAAEO,IAAI,KAAK;IACnD,MAAMH,SAAS,GAAGP,UAAU,CAACG,KAAK,EAAEO,IAAI,CAAC;IACzClD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+C,SAAS,CAAC;IACvE9C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC8C,SAAS,CAAC;EAClF,CAAC,CAAC;EACF;EACA,MAAMI,kBAAkB,GAAG1E,UAAU,CAACe,SAAS,EAAEE,SAAS,KAAA0D,MAAA,CAAK5D,SAAS,OAAA4D,MAAA,CAAIxD,MAAM,GAAI;IACpF,IAAAwD,MAAA,CAAI5D,SAAS,YAASoB;EACxB,CAAC,EAAEd,aAAa,EAAEO,gBAAgB,EAAEK,SAAS,EAAEH,OAAO,EAAEE,MAAM,CAAC;EAC/D;EACA,MAAM4C,OAAO,MAAAD,MAAA,CAAM5D,SAAS,UAAO;EACnC,MAAM8D,UAAU,GAAG/E,KAAK,CAACgF,OAAO,CAAC,MAAM;IACrC,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5C,KAAK,CAAC6C,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACxCD,KAAK,IAAIzB,YAAY,CAAC0B,CAAC,CAAC;MACxBF,WAAW,CAACI,IAAI,CAACH,KAAK,CAAC;IACzB;IACA,OAAOD,WAAW;EACpB,CAAC,EAAE,CAACxB,YAAY,CAAC,CAAC;EAClB,MAAM6B,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzD,YAAY,CAAC,EAAEX,KAAK,CAAC;EACzE,OAAOa,UAAU,CAAC,aAAajC,KAAK,CAACyF,aAAa,CAACtF,cAAc,EAAE;IACjEsB,QAAQ,EAAE2B;EACZ,CAAC,EAAE,aAAapD,KAAK,CAACyF,aAAa,CAAC,KAAK,EAAE;IACzCrE,KAAK,EAAEkE,WAAW;IAClBnE,SAAS,EAAEyD;EACb,CAAC,EAAErC,KAAK,CAACmD,GAAG,CAAC,CAAC3C,IAAI,EAAE4C,GAAG,KAAK;IAC1B;IACA,MAAMC,KAAK,GAAG,aAAa5F,KAAK,CAACyF,aAAa,CAAC7E,aAAa,EAAE2E,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzC,IAAI,EAAE;MACpF9B,SAAS,EAAEA,SAAS;MACpB+B,IAAI,EAAEO,UAAU,CAACoC,GAAG;IACtB,CAAC,CAAC,CAAC;IACH;IACA,IAAIE,QAAQ,GAAG,IAAI;IACnB,MAAMC,aAAa,GAAGjC,cAAc,CAAC8B,GAAG,CAAC;IACzC,IAAIG,aAAa,EAAE;MACjB,MAAMC,YAAY,GAAG,CAAChB,UAAU,CAACY,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAIjC,eAAe,CAACiC,GAAG,CAAC;MACtE,MAAMK,UAAU,GAAG,CAACjB,UAAU,CAACY,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAIhC,eAAe,CAACgC,GAAG,GAAG,CAAC,CAAC;MAC1E,MAAMM,YAAY,GAAG,CAAClB,UAAU,CAACY,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAIhC,eAAe,CAACgC,GAAG,CAAC;MACtE,MAAMO,UAAU,GAAG,CAACnB,UAAU,CAACY,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAIjC,eAAe,CAACiC,GAAG,GAAG,CAAC,CAAC;MAC1EE,QAAQ,GAAG,aAAa7F,KAAK,CAACyF,aAAa,CAAC5E,QAAQ,EAAE;QACpDc,IAAI,EAAEA,IAAI;QACVyC,KAAK,EAAEuB,GAAG;QACVQ,MAAM,EAAEjC,WAAW,KAAKyB,GAAG;QAC3B1E,SAAS,EAAEA,SAAS;QACpBmF,QAAQ,EAAEhE,UAAU;QACpBiE,SAAS,EAAEP,aAAa,CAACO,SAAS;QAClCC,OAAO,EAAEvB,UAAU,CAACY,GAAG,CAAC,GAAG,GAAG;QAC9BY,OAAO,EAAEC,IAAI,CAACC,GAAG,CAACV,YAAY,EAAEC,UAAU,CAAC,GAAG,GAAG;QACjDU,OAAO,EAAEF,IAAI,CAACG,GAAG,CAACV,YAAY,EAAEC,UAAU,CAAC,GAAG,GAAG;QACjDU,gBAAgB,EAAEd,aAAa,CAACc,gBAAgB;QAChDC,cAAc,EAAEf,aAAa,CAACe,cAAc;QAC5C/C,aAAa,EAAEK,qBAAqB;QACpCJ,cAAc,EAAEA,CAACK,KAAK,EAAE0C,OAAO,EAAEC,OAAO,EAAExC,OAAO,KAAK;UACpD,IAAID,MAAM,GAAGlC,UAAU,GAAG2E,OAAO,GAAGD,OAAO;UAC3C,IAAIxE,OAAO,EAAE;YACXgC,MAAM,GAAG,CAACA,MAAM;UAClB;UACAD,sBAAsB,CAACD,KAAK,EAAEE,MAAM,EAAEC,OAAO,CAAC;QAChD,CAAC;QACDP,WAAW,EAAES,mBAAmB;QAChCR,UAAU,EAAES,kBAAkB;QAC9BxB,aAAa,EAAEA,aAAa,IAAI;MAClC,CAAC,CAAC;IACJ;IACA,OAAO,aAAalD,KAAK,CAACyF,aAAa,CAACzF,KAAK,CAACgH,QAAQ,EAAE;MACtDC,GAAG,iBAAApC,MAAA,CAAiBc,GAAG;IACzB,CAAC,EAAEC,KAAK,EAAEC,QAAQ,CAAC;EACrB,CAAC,CAAC,EAAE,OAAO3B,WAAW,KAAK,QAAQ,KAAK,aAAalE,KAAK,CAACyF,aAAa,CAAC,KAAK,EAAE;IAC9E,aAAa,EAAE,IAAI;IACnBtE,SAAS,EAAEjB,UAAU,CAAC4E,OAAO,KAAAD,MAAA,CAAKC,OAAO,OAAAD,MAAA,CAAIxD,MAAM,CAAE;EACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AACD,IAAImB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC3B,QAAQ,CAACmG,WAAW,GAAG,UAAU;AACnC;AACA,eAAenG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
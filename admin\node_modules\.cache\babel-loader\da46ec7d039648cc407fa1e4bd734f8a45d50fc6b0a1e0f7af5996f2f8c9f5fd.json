{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Tag,Typography,Row,Col,Statistic,Input,Select,DatePicker,Modal,Form,message,Descriptions,Radio}from'antd';import{SearchOutlined,ReloadOutlined,CheckOutlined,CloseOutlined,EyeOutlined,ClockCircleOutlined,ExclamationCircleOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Option}=Select;const{RangePicker}=DatePicker;// 审核中订单接口定义\n// 模拟开卡中订单数据（从待处理订单转入）\nconst mockReviewingOrders=[{id:1,orderNo:'ORD202401150005',customerName:'钱七',customerPhone:'13800138005',customerIdCard:'320101199306154444',productName:'中国移动5G尊享套餐',operator:'中国移动',deliveryAddress:'南京市鼓楼区中山路200号德基广场二期A座1801室',submitTime:'2024-01-15 09:30:00',reviewDays:1,priority:'high',riskLevel:'medium',reviewNotes:'正在为客户开通卡片服务，预计2-3个工作日完成'},{id:2,orderNo:'ORD202401150007',customerName:'周九',customerPhone:'13800138007',customerIdCard:'******************',productName:'中国电信天翼套餐',operator:'中国电信',deliveryAddress:'长沙市岳麓区麓山南路932号中南大学科技园',submitTime:'2024-01-15 14:20:00',reviewDays:0.5,priority:'urgent',riskLevel:'low',reviewNotes:'加急处理，正在联系运营商开通服务'},{id:3,orderNo:'ORD202401150011',customerName:'郑十三',customerPhone:'13800138011',customerIdCard:'320101199306154444',productName:'中国联通青春套餐',operator:'中国联通',deliveryAddress:'南京市鼓楼区中山路200号德基广场二期A座1801室',submitTime:'2024-01-15 16:45:00',reviewDays:0.2,priority:'normal',riskLevel:'low',reviewNotes:'开卡流程进行中，等待运营商系统响应'}];const OrderReviewing=()=>{const[orders,setOrders]=useState([]);const[loading,setLoading]=useState(false);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[reviewModalVisible,setReviewModalVisible]=useState(false);const[viewModalVisible,setViewModalVisible]=useState(false);const[selectedOrder,setSelectedOrder]=useState(null);const[form]=Form.useForm();// 页面加载时获取数据\nuseEffect(()=>{handleRefresh();},[]);// 转换API数据格式为前端格式\nconst transformApiData=apiData=>{return{id:apiData.id,orderNo:apiData.order_no,customerName:apiData.customer_name,customerPhone:apiData.customer_phone,customerIdCard:apiData.customer_id_card,productName:apiData.product_name,operator:apiData.operator,priority:apiData.priority||'normal',deliveryAddress:apiData.delivery_address,submitTime:apiData.created_at?new Date(apiData.created_at).toLocaleString('zh-CN'):'',reviewDays:apiData.created_at?Math.max(0.1,(new Date().getTime()-new Date(apiData.created_at).getTime())/(1000*60*60*24)):0.1,riskLevel:'low',// 默认低风险，实际项目中应该根据业务逻辑计算\nreviewNotes:apiData.process_notes||''};};// 刷新数据\nconst handleRefresh=async()=>{setLoading(true);try{const response=await fetch('http://localhost:8000/api/v1/orders?status=processing',{method:'GET',headers:{'Accept':'application/json'}});const result=await response.json();if(result.code===200){// 转换API数据格式\nconst transformedOrders=result.data.list.map(transformApiData);setOrders(transformedOrders);message.success(\"\\u6570\\u636E\\u5DF2\\u5237\\u65B0\\uFF0C\\u83B7\\u53D6\\u5230 \".concat(transformedOrders.length,\" \\u4E2A\\u5F00\\u5361\\u4E2D\\u8BA2\\u5355\"));}else{message.error(result.message||'刷新失败');}}catch(error){console.error('刷新失败:',error);message.error('网络错误，请稍后重试');}finally{setLoading(false);}};// 获取优先级颜色\nconst getPriorityColor=priority=>{const colors={low:'default',normal:'blue',high:'orange',urgent:'red'};return colors[priority]||'default';};// 获取优先级文本\nconst getPriorityText=priority=>{const texts={low:'低',normal:'普通',high:'高',urgent:'紧急'};return texts[priority]||priority;};// 获取风险等级颜色\nconst getRiskLevelColor=riskLevel=>{const colors={low:'green',medium:'orange',high:'red'};return colors[riskLevel]||'default';};// 获取风险等级文本\nconst getRiskLevelText=riskLevel=>{const texts={low:'低风险',medium:'中风险',high:'高风险'};return texts[riskLevel]||riskLevel;};// 统计数据\nconst stats={total:orders.length,urgent:orders.filter(order=>order.priority==='urgent').length,highRisk:orders.filter(order=>order.riskLevel==='high').length,overdue:orders.filter(order=>order.reviewDays>1).length// 审核超过1天的订单\n};// 查看订单详情\nconst handleViewOrder=order=>{setSelectedOrder(order);setViewModalVisible(true);};// 处理开卡订单\nconst handleReviewOrder=order=>{setSelectedOrder(order);setReviewModalVisible(true);form.resetFields();};// 提交开卡结果\nconst handleSubmitReview=async()=>{try{const values=await form.validateFields();if(!selectedOrder){message.error('未选择订单');return;}// 确定目标状态\nconst targetStatus=values.result==='approved'?'shipped':'failed';// 准备请求数据\nconst requestData={status:targetStatus,process_notes:values.notes,processed_by:'admin'};// 如果开卡成功，添加快递信息\nif(values.result==='approved'){requestData.logistics_company=values.logistics_company;requestData.tracking_number=values.tracking_number;if(values.card_number){requestData.card_number=values.card_number;}// 设置发货时间为当前时间\nrequestData.shipped_at=new Date().toISOString();}console.log('提交的数据:',requestData);// 调试信息\n// 调用API更新订单状态\nconst response=await fetch(\"http://localhost:8000/api/v1/orders/\".concat(selectedOrder.id,\"/status\"),{method:'PATCH',headers:{'Accept':'application/json','Content-Type':'application/json'},body:JSON.stringify(requestData)});const result=await response.json();if(result.code===200){if(values.result==='approved'){// 开卡成功，移动到已发货订单\nmessage.success(\"\\u5F00\\u5361\\u6210\\u529F\\uFF01\\u5FEB\\u9012\\u5355\\u53F7\\uFF1A\".concat(values.tracking_number,\"\\uFF0C\\u8BA2\\u5355\\u5DF2\\u8F6C\\u5165\\u53D1\\u8D27\\u6D41\\u7A0B\"));}else{// 开卡失败，移动到失败订单\nmessage.success('开卡失败，订单已转入失败订单列表');}// 从开卡中订单列表移除\nsetOrders(orders.filter(order=>order.id!==selectedOrder.id));setReviewModalVisible(false);setSelectedOrder(null);form.resetFields();}else{message.error(result.message||'处理失败');}}catch(error){console.error('开卡处理失败:',error);if(error instanceof Error){message.error(\"\\u5904\\u7406\\u5931\\u8D25: \".concat(error.message));}else{message.error('网络错误，请稍后重试');}}};// 批量审核通过\nconst handleBatchApprove=()=>{if(selectedRowKeys.length===0){message.warning('请选择要审核的订单');return;}Modal.confirm({title:'批量审核通过',content:\"\\u786E\\u5B9A\\u8981\\u5C06\\u9009\\u4E2D\\u7684 \".concat(selectedRowKeys.length,\" \\u4E2A\\u8BA2\\u5355\\u5BA1\\u6838\\u901A\\u8FC7\\u5417\\uFF1F\"),onOk:()=>{setOrders(orders.filter(order=>!selectedRowKeys.includes(order.id)));setSelectedRowKeys([]);message.success(\"\\u6210\\u529F\\u5BA1\\u6838\\u901A\\u8FC7 \".concat(selectedRowKeys.length,\" \\u4E2A\\u8BA2\\u5355\"));}});};// 表格列定义\nconst columns=[{title:'优先级',dataIndex:'priority',key:'priority',width:80,render:priority=>/*#__PURE__*/_jsx(Tag,{color:getPriorityColor(priority),children:getPriorityText(priority)}),sorter:(a,b)=>{const priorityOrder={urgent:4,high:3,normal:2,low:1};return priorityOrder[a.priority]-priorityOrder[b.priority];}},{title:'订单号',dataIndex:'orderNo',key:'orderNo',width:160,render:text=>/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:'12px'},children:text})},{title:'客户信息',key:'customer',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600,fontSize:'13px'},children:record.customerName}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#666'},children:record.customerPhone})]})},{title:'产品名称',dataIndex:'productName',key:'productName',ellipsis:true,render:text=>/*#__PURE__*/_jsx(Text,{style:{fontSize:'12px'},children:text})},{title:'运营商',dataIndex:'operator',key:'operator',width:100,render:text=>/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:text})},{title:'风险等级',dataIndex:'riskLevel',key:'riskLevel',width:100,render:riskLevel=>/*#__PURE__*/_jsx(Tag,{color:getRiskLevelColor(riskLevel),children:getRiskLevelText(riskLevel)})},{title:'审核时长',dataIndex:'reviewDays',key:'reviewDays',width:100,render:days=>/*#__PURE__*/_jsx(Text,{style:{color:days>1?'#f5222d':'#52c41a'},children:days<1?\"\".concat(Math.round(days*24),\"\\u5C0F\\u65F6\"):\"\".concat(days.toFixed(1),\"\\u5929\")}),sorter:(a,b)=>a.reviewDays-b.reviewDays},{title:'提交时间',dataIndex:'submitTime',key:'submitTime',width:150,render:text=>/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px'},children:text})},{title:'操作',key:'action',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewOrder(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"small\",icon:/*#__PURE__*/_jsx(CheckOutlined,{}),onClick:()=>handleReviewOrder(record),children:\"\\u5904\\u7406\"})]})}];return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px'},children:[/*#__PURE__*/_jsxs(Title,{level:2,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(ClockCircleOutlined,{style:{marginRight:'8px',color:'#1890ff'}}),\"\\u5BA1\\u6838\\u4E2D\\u8BA2\\u5355\\uFF08\\u5F00\\u5361\\u4E2D\\uFF09\"]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F00\\u5361\\u4E2D\\u8BA2\\u5355\",value:stats.total,prefix:/*#__PURE__*/_jsx(ClockCircleOutlined,{}),valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7D27\\u6025\\u8BA2\\u5355\",value:stats.urgent,prefix:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{}),valueStyle:{color:'#f5222d'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u9AD8\\u98CE\\u9669\\u8BA2\\u5355\",value:stats.highRisk,prefix:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{}),valueStyle:{color:'#fa8c16'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8D85\\u65F6\\u8BA2\\u5355\",value:stats.overdue,prefix:/*#__PURE__*/_jsx(ClockCircleOutlined,{}),valueStyle:{color:'#722ed1'}})})})]}),/*#__PURE__*/_jsx(Card,{style:{marginBottom:'16px'},children:/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Input,{placeholder:\"\\u641C\\u7D22\\u8BA2\\u5355\\u53F7\\u3001\\u5BA2\\u6237\\u59D3\\u540D\",prefix:/*#__PURE__*/_jsx(SearchOutlined,{}),style:{width:250}}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u98CE\\u9669\\u7B49\\u7EA7\",style:{width:120},children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"\\u5168\\u90E8\"}),/*#__PURE__*/_jsx(Option,{value:\"low\",children:\"\\u4F4E\\u98CE\\u9669\"}),/*#__PURE__*/_jsx(Option,{value:\"medium\",children:\"\\u4E2D\\u98CE\\u9669\"}),/*#__PURE__*/_jsx(Option,{value:\"high\",children:\"\\u9AD8\\u98CE\\u9669\"})]}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u4F18\\u5148\\u7EA7\",style:{width:120},children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"\\u5168\\u90E8\"}),/*#__PURE__*/_jsx(Option,{value:\"urgent\",children:\"\\u7D27\\u6025\"}),/*#__PURE__*/_jsx(Option,{value:\"high\",children:\"\\u9AD8\"}),/*#__PURE__*/_jsx(Option,{value:\"normal\",children:\"\\u666E\\u901A\"}),/*#__PURE__*/_jsx(Option,{value:\"low\",children:\"\\u4F4E\"})]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(SearchOutlined,{}),type:\"primary\",children:\"\\u641C\\u7D22\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:handleRefresh,children:\"\\u5237\\u65B0\"})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Space,{children:/*#__PURE__*/_jsxs(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(CheckOutlined,{}),onClick:handleBatchApprove,disabled:selectedRowKeys.length===0,children:[\"\\u6279\\u91CF\\u901A\\u8FC7 (\",selectedRowKeys.length,\")\"]})})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:orders,rowKey:\"id\",loading:loading,scroll:{x:1400},rowSelection:{selectedRowKeys,onChange:setSelectedRowKeys,getCheckboxProps:record=>({disabled:record.riskLevel==='high'// 高风险订单不允许批量操作\n})},pagination:{total:orders.length,pageSize:10,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5F00\\u5361\\u5904\\u7406\",open:reviewModalVisible,onCancel:()=>setReviewModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setReviewModalVisible(false),children:\"\\u53D6\\u6D88\"},\"cancel\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:handleSubmitReview,children:\"\\u63D0\\u4EA4\\u7ED3\\u679C\"},\"submit\")],width:800,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{title:\"\\u8BA2\\u5355\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Descriptions,{column:2,size:\"small\",children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u53F7\",children:selectedOrder.orderNo}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:selectedOrder.customerName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u624B\\u673A\\u53F7\",children:selectedOrder.customerPhone}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8EAB\\u4EFD\\u8BC1\\u53F7\",children:selectedOrder.customerIdCard}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u540D\\u79F0\",children:selectedOrder.productName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u98CE\\u9669\\u7B49\\u7EA7\",children:/*#__PURE__*/_jsx(Tag,{color:getRiskLevelColor(selectedOrder.riskLevel),children:getRiskLevelText(selectedOrder.riskLevel)})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u63D0\\u4EA4\\u65F6\\u95F4\",children:selectedOrder.submitTime})]}),/*#__PURE__*/_jsxs(Descriptions,{column:1,size:\"small\",children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6536\\u8D27\\u5730\\u5740\",children:selectedOrder.deliveryAddress}),selectedOrder.reviewNotes&&/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA1\\u6838\\u5907\\u6CE8\",children:selectedOrder.reviewNotes})]})]}),/*#__PURE__*/_jsx(Card,{title:\"\\u5F00\\u5361\\u7ED3\\u679C\",size:\"small\",children:/*#__PURE__*/_jsxs(Form,{form:form,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"result\",label:\"\\u5F00\\u5361\\u7ED3\\u679C\",rules:[{required:true,message:'请选择开卡结果'}],children:/*#__PURE__*/_jsxs(Radio.Group,{children:[/*#__PURE__*/_jsxs(Radio,{value:\"approved\",style:{color:'#52c41a'},children:[/*#__PURE__*/_jsx(CheckOutlined,{}),\" \\u5F00\\u5361\\u6210\\u529F\"]}),/*#__PURE__*/_jsxs(Radio,{value:\"rejected\",style:{color:'#f5222d'},children:[/*#__PURE__*/_jsx(CloseOutlined,{}),\" \\u5F00\\u5361\\u5931\\u8D25\"]})]})}),/*#__PURE__*/_jsx(Form.Item,{noStyle:true,shouldUpdate:(prevValues,currentValues)=>prevValues.result!==currentValues.result,children:_ref=>{let{getFieldValue}=_ref;const result=getFieldValue('result');return result==='approved'?/*#__PURE__*/_jsxs(\"div\",{style:{background:'#f6ffed',border:'1px solid #b7eb8f',borderRadius:'6px',padding:'16px',marginBottom:'16px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'16px',color:'#52c41a',fontWeight:'bold'},children:\"\\uD83D\\uDCE6 \\u53D1\\u8D27\\u4FE1\\u606F\\uFF08\\u5F00\\u5361\\u6210\\u529F\\u5FC5\\u586B\\uFF09\"}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"logistics_company\",label:\"\\u5FEB\\u9012\\u516C\\u53F8\",rules:[{required:true,message:'请选择快递公司'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u5FEB\\u9012\\u516C\\u53F8\",children:[/*#__PURE__*/_jsx(Option,{value:\"\\u987A\\u4E30\\u901F\\u8FD0\",children:\"\\u987A\\u4E30\\u901F\\u8FD0\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u4E2D\\u901A\\u5FEB\\u9012\",children:\"\\u4E2D\\u901A\\u5FEB\\u9012\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u5706\\u901A\\u901F\\u9012\",children:\"\\u5706\\u901A\\u901F\\u9012\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u7533\\u901A\\u5FEB\\u9012\",children:\"\\u7533\\u901A\\u5FEB\\u9012\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u97F5\\u8FBE\\u901F\\u9012\",children:\"\\u97F5\\u8FBE\\u901F\\u9012\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u767E\\u4E16\\u5FEB\\u9012\",children:\"\\u767E\\u4E16\\u5FEB\\u9012\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u5FB7\\u90A6\\u5FEB\\u9012\",children:\"\\u5FB7\\u90A6\\u5FEB\\u9012\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u4EAC\\u4E1C\\u7269\\u6D41\",children:\"\\u4EAC\\u4E1C\\u7269\\u6D41\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u90AE\\u653FEMS\",children:\"\\u90AE\\u653FEMS\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u5929\\u5929\\u5FEB\\u9012\",children:\"\\u5929\\u5929\\u5FEB\\u9012\"})]})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"tracking_number\",label:\"\\u5FEB\\u9012\\u5355\\u53F7\",rules:[{required:true,message:'请输入快递单号'},{pattern:/^[A-Za-z0-9]{8,20}$/,message:'快递单号格式不正确'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5FEB\\u9012\\u5355\\u53F7\"})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"card_number\",label:\"\\u5F00\\u5361\\u53F7\\u7801\",rules:[{pattern:/^1[3-9]\\d{9}$/,message:'手机号码格式不正确'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5F00\\u5361\\u53F7\\u7801\\uFF08\\u9009\\u586B\\uFF09\"})})]}):null;}}),/*#__PURE__*/_jsx(Form.Item,{name:\"notes\",label:\"\\u5904\\u7406\\u5907\\u6CE8\",rules:[{required:true,message:'请填写处理备注'}],children:/*#__PURE__*/_jsx(Input.TextArea,{rows:4,placeholder:\"\\u8BF7\\u8BE6\\u7EC6\\u8BF4\\u660E\\u5F00\\u5361\\u5904\\u7406\\u60C5\\u51B5\\u3001\\u6CE8\\u610F\\u4E8B\\u9879\\u6216\\u5931\\u8D25\\u539F\\u56E0...\",maxLength:500,showCount:true})})]})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u8BA2\\u5355\\u8BE6\\u60C5\",open:viewModalVisible,onCancel:()=>setViewModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setViewModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Card,{title:\"\\u5BA2\\u6237\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u59D3\\u540D\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.customerName})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u624B\\u673A\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.customerPhone})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8EAB\\u4EFD\\u8BC1\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.customerIdCard})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u4EA7\\u54C1\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4EA7\\u54C1\\u540D\\u79F0\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.productName})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8FD0\\u8425\\u5546\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u6536\\u8D27\\u5730\\u5740\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsx(\"div\",{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(Text,{children:selectedOrder.deliveryAddress})})}),/*#__PURE__*/_jsxs(Card,{title:\"\\u5BA1\\u6838\\u4FE1\\u606F\",size:\"small\",children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BA2\\u5355\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.orderNo})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4F18\\u5148\\u7EA7\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:getPriorityColor(selectedOrder.priority),children:getPriorityText(selectedOrder.priority)})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u98CE\\u9669\\u7B49\\u7EA7\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:getRiskLevelColor(selectedOrder.riskLevel),children:getRiskLevelText(selectedOrder.riskLevel)})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u63D0\\u4EA4\\u65F6\\u95F4\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.submitTime})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5BA1\\u6838\\u65F6\\u957F\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{style:{color:selectedOrder.reviewDays>1?'#f5222d':'#52c41a'},children:selectedOrder.reviewDays<1?\"\".concat(Math.round(selectedOrder.reviewDays*24),\"\\u5C0F\\u65F6\"):\"\".concat(selectedOrder.reviewDays.toFixed(1),\"\\u5929\")})]})})]}),selectedOrder.reviewNotes&&/*#__PURE__*/_jsx(Row,{gutter:16,children:/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5BA1\\u6838\\u5907\\u6CE8\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4,color:'#666',lineHeight:1.5},children:selectedOrder.reviewNotes})]})})})]})]})})]});};export default OrderReviewing;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
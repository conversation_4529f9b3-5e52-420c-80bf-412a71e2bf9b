{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genBorderedStyle = token => {\n  const {\n    componentCls,\n    labelBg\n  } = token;\n  return {\n    [\"&\".concat(componentCls, \"-bordered\")]: {\n      [\"> \".concat(componentCls, \"-view\")]: {\n        border: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorSplit),\n        '> table': {\n          tableLayout: 'auto'\n        },\n        [\"\".concat(componentCls, \"-row\")]: {\n          borderBottom: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorSplit),\n          '&:first-child': {\n            '> th:first-child, > td:first-child': {\n              borderStartStartRadius: token.borderRadiusLG\n            }\n          },\n          '&:last-child': {\n            borderBottom: 'none',\n            '> th:first-child, > td:first-child': {\n              borderEndStartRadius: token.borderRadiusLG\n            }\n          },\n          [\"> \".concat(componentCls, \"-item-label, > \").concat(componentCls, \"-item-content\")]: {\n            padding: \"\".concat(unit(token.padding), \" \").concat(unit(token.paddingLG)),\n            borderInlineEnd: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorSplit),\n            '&:last-child': {\n              borderInlineEnd: 'none'\n            }\n          },\n          [\"> \".concat(componentCls, \"-item-label\")]: {\n            color: token.colorTextSecondary,\n            backgroundColor: labelBg,\n            '&::after': {\n              display: 'none'\n            }\n          }\n        }\n      },\n      [\"&\".concat(componentCls, \"-middle\")]: {\n        [\"\".concat(componentCls, \"-row\")]: {\n          [\"> \".concat(componentCls, \"-item-label, > \").concat(componentCls, \"-item-content\")]: {\n            padding: \"\".concat(unit(token.paddingSM), \" \").concat(unit(token.paddingLG))\n          }\n        }\n      },\n      [\"&\".concat(componentCls, \"-small\")]: {\n        [\"\".concat(componentCls, \"-row\")]: {\n          [\"> \".concat(componentCls, \"-item-label, > \").concat(componentCls, \"-item-content\")]: {\n            padding: \"\".concat(unit(token.paddingXS), \" \").concat(unit(token.padding))\n          }\n        }\n      }\n    }\n  };\n};\nconst genDescriptionStyles = token => {\n  const {\n    componentCls,\n    extraColor,\n    itemPaddingBottom,\n    itemPaddingEnd,\n    colonMarginRight,\n    colonMarginLeft,\n    titleMarginBottom\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genBorderedStyle(token)), {\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      [\"\".concat(componentCls, \"-header\")]: {\n        display: 'flex',\n        alignItems: 'center',\n        marginBottom: titleMarginBottom\n      },\n      [\"\".concat(componentCls, \"-title\")]: Object.assign(Object.assign({}, textEllipsis), {\n        flex: 'auto',\n        color: token.titleColor,\n        fontWeight: token.fontWeightStrong,\n        fontSize: token.fontSizeLG,\n        lineHeight: token.lineHeightLG\n      }),\n      [\"\".concat(componentCls, \"-extra\")]: {\n        marginInlineStart: 'auto',\n        color: extraColor,\n        fontSize: token.fontSize\n      },\n      [\"\".concat(componentCls, \"-view\")]: {\n        width: '100%',\n        borderRadius: token.borderRadiusLG,\n        table: {\n          width: '100%',\n          tableLayout: 'fixed',\n          borderCollapse: 'collapse'\n        }\n      },\n      [\"\".concat(componentCls, \"-row\")]: {\n        '> th, > td': {\n          paddingBottom: itemPaddingBottom,\n          paddingInlineEnd: itemPaddingEnd\n        },\n        '> th:last-child, > td:last-child': {\n          paddingInlineEnd: 0\n        },\n        '&:last-child': {\n          borderBottom: 'none',\n          '> th, > td': {\n            paddingBottom: 0\n          }\n        }\n      },\n      [\"\".concat(componentCls, \"-item-label\")]: {\n        color: token.labelColor,\n        fontWeight: 'normal',\n        fontSize: token.fontSize,\n        lineHeight: token.lineHeight,\n        textAlign: 'start',\n        '&::after': {\n          content: '\":\"',\n          position: 'relative',\n          top: -0.5,\n          // magic for position\n          marginInline: \"\".concat(unit(colonMarginLeft), \" \").concat(unit(colonMarginRight))\n        },\n        [\"&\".concat(componentCls, \"-item-no-colon::after\")]: {\n          content: '\"\"'\n        }\n      },\n      [\"\".concat(componentCls, \"-item-no-label\")]: {\n        '&::after': {\n          margin: 0,\n          content: '\"\"'\n        }\n      },\n      [\"\".concat(componentCls, \"-item-content\")]: {\n        display: 'table-cell',\n        flex: 1,\n        color: token.contentColor,\n        fontSize: token.fontSize,\n        lineHeight: token.lineHeight,\n        wordBreak: 'break-word',\n        overflowWrap: 'break-word'\n      },\n      [\"\".concat(componentCls, \"-item\")]: {\n        paddingBottom: 0,\n        verticalAlign: 'top',\n        '&-container': {\n          display: 'flex',\n          [\"\".concat(componentCls, \"-item-label\")]: {\n            display: 'inline-flex',\n            alignItems: 'baseline'\n          },\n          [\"\".concat(componentCls, \"-item-content\")]: {\n            display: 'inline-flex',\n            alignItems: 'baseline',\n            minWidth: '1em'\n          }\n        }\n      },\n      '&-middle': {\n        [\"\".concat(componentCls, \"-row\")]: {\n          '> th, > td': {\n            paddingBottom: token.paddingSM\n          }\n        }\n      },\n      '&-small': {\n        [\"\".concat(componentCls, \"-row\")]: {\n          '> th, > td': {\n            paddingBottom: token.paddingXS\n          }\n        }\n      }\n    })\n  };\n};\nexport const prepareComponentToken = token => ({\n  labelBg: token.colorFillAlter,\n  labelColor: token.colorTextTertiary,\n  titleColor: token.colorText,\n  titleMarginBottom: token.fontSizeSM * token.lineHeightSM,\n  itemPaddingBottom: token.padding,\n  itemPaddingEnd: token.padding,\n  colonMarginRight: token.marginXS,\n  colonMarginLeft: token.marginXXS / 2,\n  contentColor: token.colorText,\n  extraColor: token.colorText\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Descriptions', token => {\n  const descriptionToken = mergeToken(token, {});\n  return genDescriptionStyles(descriptionToken);\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "resetComponent", "textEllipsis", "genStyleHooks", "mergeToken", "genBorderedStyle", "token", "componentCls", "labelBg", "concat", "border", "lineWidth", "lineType", "colorSplit", "tableLayout", "borderBottom", "borderStartStartRadius", "borderRadiusLG", "borderEndStartRadius", "padding", "paddingLG", "borderInlineEnd", "color", "colorTextSecondary", "backgroundColor", "display", "paddingSM", "paddingXS", "genDescriptionStyles", "extraColor", "itemPaddingBottom", "itemPaddingEnd", "colonMarginRight", "colonMarginLeft", "titleMarginBottom", "Object", "assign", "direction", "alignItems", "marginBottom", "flex", "titleColor", "fontWeight", "fontWeightStrong", "fontSize", "fontSizeLG", "lineHeight", "lineHeightLG", "marginInlineStart", "width", "borderRadius", "table", "borderCollapse", "paddingBottom", "paddingInlineEnd", "labelColor", "textAlign", "content", "position", "top", "marginInline", "margin", "contentColor", "wordBreak", "overflowWrap", "verticalAlign", "min<PERSON><PERSON><PERSON>", "prepareComponentToken", "colorFillAlter", "colorTextTertiary", "colorText", "fontSizeSM", "lineHeightSM", "marginXS", "marginXXS", "descriptionToken"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/descriptions/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genBorderedStyle = token => {\n  const {\n    componentCls,\n    labelBg\n  } = token;\n  return {\n    [`&${componentCls}-bordered`]: {\n      [`> ${componentCls}-view`]: {\n        border: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`,\n        '> table': {\n          tableLayout: 'auto'\n        },\n        [`${componentCls}-row`]: {\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`,\n          '&:first-child': {\n            '> th:first-child, > td:first-child': {\n              borderStartStartRadius: token.borderRadiusLG\n            }\n          },\n          '&:last-child': {\n            borderBottom: 'none',\n            '> th:first-child, > td:first-child': {\n              borderEndStartRadius: token.borderRadiusLG\n            }\n          },\n          [`> ${componentCls}-item-label, > ${componentCls}-item-content`]: {\n            padding: `${unit(token.padding)} ${unit(token.paddingLG)}`,\n            borderInlineEnd: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`,\n            '&:last-child': {\n              borderInlineEnd: 'none'\n            }\n          },\n          [`> ${componentCls}-item-label`]: {\n            color: token.colorTextSecondary,\n            backgroundColor: labelBg,\n            '&::after': {\n              display: 'none'\n            }\n          }\n        }\n      },\n      [`&${componentCls}-middle`]: {\n        [`${componentCls}-row`]: {\n          [`> ${componentCls}-item-label, > ${componentCls}-item-content`]: {\n            padding: `${unit(token.paddingSM)} ${unit(token.paddingLG)}`\n          }\n        }\n      },\n      [`&${componentCls}-small`]: {\n        [`${componentCls}-row`]: {\n          [`> ${componentCls}-item-label, > ${componentCls}-item-content`]: {\n            padding: `${unit(token.paddingXS)} ${unit(token.padding)}`\n          }\n        }\n      }\n    }\n  };\n};\nconst genDescriptionStyles = token => {\n  const {\n    componentCls,\n    extraColor,\n    itemPaddingBottom,\n    itemPaddingEnd,\n    colonMarginRight,\n    colonMarginLeft,\n    titleMarginBottom\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genBorderedStyle(token)), {\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      [`${componentCls}-header`]: {\n        display: 'flex',\n        alignItems: 'center',\n        marginBottom: titleMarginBottom\n      },\n      [`${componentCls}-title`]: Object.assign(Object.assign({}, textEllipsis), {\n        flex: 'auto',\n        color: token.titleColor,\n        fontWeight: token.fontWeightStrong,\n        fontSize: token.fontSizeLG,\n        lineHeight: token.lineHeightLG\n      }),\n      [`${componentCls}-extra`]: {\n        marginInlineStart: 'auto',\n        color: extraColor,\n        fontSize: token.fontSize\n      },\n      [`${componentCls}-view`]: {\n        width: '100%',\n        borderRadius: token.borderRadiusLG,\n        table: {\n          width: '100%',\n          tableLayout: 'fixed',\n          borderCollapse: 'collapse'\n        }\n      },\n      [`${componentCls}-row`]: {\n        '> th, > td': {\n          paddingBottom: itemPaddingBottom,\n          paddingInlineEnd: itemPaddingEnd\n        },\n        '> th:last-child, > td:last-child': {\n          paddingInlineEnd: 0\n        },\n        '&:last-child': {\n          borderBottom: 'none',\n          '> th, > td': {\n            paddingBottom: 0\n          }\n        }\n      },\n      [`${componentCls}-item-label`]: {\n        color: token.labelColor,\n        fontWeight: 'normal',\n        fontSize: token.fontSize,\n        lineHeight: token.lineHeight,\n        textAlign: 'start',\n        '&::after': {\n          content: '\":\"',\n          position: 'relative',\n          top: -0.5,\n          // magic for position\n          marginInline: `${unit(colonMarginLeft)} ${unit(colonMarginRight)}`\n        },\n        [`&${componentCls}-item-no-colon::after`]: {\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-item-no-label`]: {\n        '&::after': {\n          margin: 0,\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-item-content`]: {\n        display: 'table-cell',\n        flex: 1,\n        color: token.contentColor,\n        fontSize: token.fontSize,\n        lineHeight: token.lineHeight,\n        wordBreak: 'break-word',\n        overflowWrap: 'break-word'\n      },\n      [`${componentCls}-item`]: {\n        paddingBottom: 0,\n        verticalAlign: 'top',\n        '&-container': {\n          display: 'flex',\n          [`${componentCls}-item-label`]: {\n            display: 'inline-flex',\n            alignItems: 'baseline'\n          },\n          [`${componentCls}-item-content`]: {\n            display: 'inline-flex',\n            alignItems: 'baseline',\n            minWidth: '1em'\n          }\n        }\n      },\n      '&-middle': {\n        [`${componentCls}-row`]: {\n          '> th, > td': {\n            paddingBottom: token.paddingSM\n          }\n        }\n      },\n      '&-small': {\n        [`${componentCls}-row`]: {\n          '> th, > td': {\n            paddingBottom: token.paddingXS\n          }\n        }\n      }\n    })\n  };\n};\nexport const prepareComponentToken = token => ({\n  labelBg: token.colorFillAlter,\n  labelColor: token.colorTextTertiary,\n  titleColor: token.colorText,\n  titleMarginBottom: token.fontSizeSM * token.lineHeightSM,\n  itemPaddingBottom: token.padding,\n  itemPaddingEnd: token.padding,\n  colonMarginRight: token.marginXS,\n  colonMarginLeft: token.marginXXS / 2,\n  contentColor: token.colorText,\n  extraColor: token.colorText\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Descriptions', token => {\n  const descriptionToken = mergeToken(token, {});\n  return genDescriptionStyles(descriptionToken);\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,cAAc,EAAEC,YAAY,QAAQ,aAAa;AAC1D,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,KAAAG,MAAA,CAAKF,YAAY,iBAAc;MAC7B,MAAAE,MAAA,CAAMF,YAAY,aAAU;QAC1BG,MAAM,KAAAD,MAAA,CAAKT,IAAI,CAACM,KAAK,CAACK,SAAS,CAAC,OAAAF,MAAA,CAAIH,KAAK,CAACM,QAAQ,OAAAH,MAAA,CAAIH,KAAK,CAACO,UAAU,CAAE;QACxE,SAAS,EAAE;UACTC,WAAW,EAAE;QACf,CAAC;QACD,IAAAL,MAAA,CAAIF,YAAY,YAAS;UACvBQ,YAAY,KAAAN,MAAA,CAAKT,IAAI,CAACM,KAAK,CAACK,SAAS,CAAC,OAAAF,MAAA,CAAIH,KAAK,CAACM,QAAQ,OAAAH,MAAA,CAAIH,KAAK,CAACO,UAAU,CAAE;UAC9E,eAAe,EAAE;YACf,oCAAoC,EAAE;cACpCG,sBAAsB,EAAEV,KAAK,CAACW;YAChC;UACF,CAAC;UACD,cAAc,EAAE;YACdF,YAAY,EAAE,MAAM;YACpB,oCAAoC,EAAE;cACpCG,oBAAoB,EAAEZ,KAAK,CAACW;YAC9B;UACF,CAAC;UACD,MAAAR,MAAA,CAAMF,YAAY,qBAAAE,MAAA,CAAkBF,YAAY,qBAAkB;YAChEY,OAAO,KAAAV,MAAA,CAAKT,IAAI,CAACM,KAAK,CAACa,OAAO,CAAC,OAAAV,MAAA,CAAIT,IAAI,CAACM,KAAK,CAACc,SAAS,CAAC,CAAE;YAC1DC,eAAe,KAAAZ,MAAA,CAAKT,IAAI,CAACM,KAAK,CAACK,SAAS,CAAC,OAAAF,MAAA,CAAIH,KAAK,CAACM,QAAQ,OAAAH,MAAA,CAAIH,KAAK,CAACO,UAAU,CAAE;YACjF,cAAc,EAAE;cACdQ,eAAe,EAAE;YACnB;UACF,CAAC;UACD,MAAAZ,MAAA,CAAMF,YAAY,mBAAgB;YAChCe,KAAK,EAAEhB,KAAK,CAACiB,kBAAkB;YAC/BC,eAAe,EAAEhB,OAAO;YACxB,UAAU,EAAE;cACViB,OAAO,EAAE;YACX;UACF;QACF;MACF,CAAC;MACD,KAAAhB,MAAA,CAAKF,YAAY,eAAY;QAC3B,IAAAE,MAAA,CAAIF,YAAY,YAAS;UACvB,MAAAE,MAAA,CAAMF,YAAY,qBAAAE,MAAA,CAAkBF,YAAY,qBAAkB;YAChEY,OAAO,KAAAV,MAAA,CAAKT,IAAI,CAACM,KAAK,CAACoB,SAAS,CAAC,OAAAjB,MAAA,CAAIT,IAAI,CAACM,KAAK,CAACc,SAAS,CAAC;UAC5D;QACF;MACF,CAAC;MACD,KAAAX,MAAA,CAAKF,YAAY,cAAW;QAC1B,IAAAE,MAAA,CAAIF,YAAY,YAAS;UACvB,MAAAE,MAAA,CAAMF,YAAY,qBAAAE,MAAA,CAAkBF,YAAY,qBAAkB;YAChEY,OAAO,KAAAV,MAAA,CAAKT,IAAI,CAACM,KAAK,CAACqB,SAAS,CAAC,OAAAlB,MAAA,CAAIT,IAAI,CAACM,KAAK,CAACa,OAAO,CAAC;UAC1D;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMS,oBAAoB,GAAGtB,KAAK,IAAI;EACpC,MAAM;IACJC,YAAY;IACZsB,UAAU;IACVC,iBAAiB;IACjBC,cAAc;IACdC,gBAAgB;IAChBC,eAAe;IACfC;EACF,CAAC,GAAG5B,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG4B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnC,cAAc,CAACK,KAAK,CAAC,CAAC,EAAED,gBAAgB,CAACC,KAAK,CAAC,CAAC,EAAE;MAC9G,OAAO,EAAE;QACP+B,SAAS,EAAE;MACb,CAAC;MACD,IAAA5B,MAAA,CAAIF,YAAY,eAAY;QAC1BkB,OAAO,EAAE,MAAM;QACfa,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAEL;MAChB,CAAC;MACD,IAAAzB,MAAA,CAAIF,YAAY,cAAW4B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElC,YAAY,CAAC,EAAE;QACxEsC,IAAI,EAAE,MAAM;QACZlB,KAAK,EAAEhB,KAAK,CAACmC,UAAU;QACvBC,UAAU,EAAEpC,KAAK,CAACqC,gBAAgB;QAClCC,QAAQ,EAAEtC,KAAK,CAACuC,UAAU;QAC1BC,UAAU,EAAExC,KAAK,CAACyC;MACpB,CAAC,CAAC;MACF,IAAAtC,MAAA,CAAIF,YAAY,cAAW;QACzByC,iBAAiB,EAAE,MAAM;QACzB1B,KAAK,EAAEO,UAAU;QACjBe,QAAQ,EAAEtC,KAAK,CAACsC;MAClB,CAAC;MACD,IAAAnC,MAAA,CAAIF,YAAY,aAAU;QACxB0C,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE5C,KAAK,CAACW,cAAc;QAClCkC,KAAK,EAAE;UACLF,KAAK,EAAE,MAAM;UACbnC,WAAW,EAAE,OAAO;UACpBsC,cAAc,EAAE;QAClB;MACF,CAAC;MACD,IAAA3C,MAAA,CAAIF,YAAY,YAAS;QACvB,YAAY,EAAE;UACZ8C,aAAa,EAAEvB,iBAAiB;UAChCwB,gBAAgB,EAAEvB;QACpB,CAAC;QACD,kCAAkC,EAAE;UAClCuB,gBAAgB,EAAE;QACpB,CAAC;QACD,cAAc,EAAE;UACdvC,YAAY,EAAE,MAAM;UACpB,YAAY,EAAE;YACZsC,aAAa,EAAE;UACjB;QACF;MACF,CAAC;MACD,IAAA5C,MAAA,CAAIF,YAAY,mBAAgB;QAC9Be,KAAK,EAAEhB,KAAK,CAACiD,UAAU;QACvBb,UAAU,EAAE,QAAQ;QACpBE,QAAQ,EAAEtC,KAAK,CAACsC,QAAQ;QACxBE,UAAU,EAAExC,KAAK,CAACwC,UAAU;QAC5BU,SAAS,EAAE,OAAO;QAClB,UAAU,EAAE;UACVC,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC,GAAG;UACT;UACAC,YAAY,KAAAnD,MAAA,CAAKT,IAAI,CAACiC,eAAe,CAAC,OAAAxB,MAAA,CAAIT,IAAI,CAACgC,gBAAgB,CAAC;QAClE,CAAC;QACD,KAAAvB,MAAA,CAAKF,YAAY,6BAA0B;UACzCkD,OAAO,EAAE;QACX;MACF,CAAC;MACD,IAAAhD,MAAA,CAAIF,YAAY,sBAAmB;QACjC,UAAU,EAAE;UACVsD,MAAM,EAAE,CAAC;UACTJ,OAAO,EAAE;QACX;MACF,CAAC;MACD,IAAAhD,MAAA,CAAIF,YAAY,qBAAkB;QAChCkB,OAAO,EAAE,YAAY;QACrBe,IAAI,EAAE,CAAC;QACPlB,KAAK,EAAEhB,KAAK,CAACwD,YAAY;QACzBlB,QAAQ,EAAEtC,KAAK,CAACsC,QAAQ;QACxBE,UAAU,EAAExC,KAAK,CAACwC,UAAU;QAC5BiB,SAAS,EAAE,YAAY;QACvBC,YAAY,EAAE;MAChB,CAAC;MACD,IAAAvD,MAAA,CAAIF,YAAY,aAAU;QACxB8C,aAAa,EAAE,CAAC;QAChBY,aAAa,EAAE,KAAK;QACpB,aAAa,EAAE;UACbxC,OAAO,EAAE,MAAM;UACf,IAAAhB,MAAA,CAAIF,YAAY,mBAAgB;YAC9BkB,OAAO,EAAE,aAAa;YACtBa,UAAU,EAAE;UACd,CAAC;UACD,IAAA7B,MAAA,CAAIF,YAAY,qBAAkB;YAChCkB,OAAO,EAAE,aAAa;YACtBa,UAAU,EAAE,UAAU;YACtB4B,QAAQ,EAAE;UACZ;QACF;MACF,CAAC;MACD,UAAU,EAAE;QACV,IAAAzD,MAAA,CAAIF,YAAY,YAAS;UACvB,YAAY,EAAE;YACZ8C,aAAa,EAAE/C,KAAK,CAACoB;UACvB;QACF;MACF,CAAC;MACD,SAAS,EAAE;QACT,IAAAjB,MAAA,CAAIF,YAAY,YAAS;UACvB,YAAY,EAAE;YACZ8C,aAAa,EAAE/C,KAAK,CAACqB;UACvB;QACF;MACF;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,OAAO,MAAMwC,qBAAqB,GAAG7D,KAAK,KAAK;EAC7CE,OAAO,EAAEF,KAAK,CAAC8D,cAAc;EAC7Bb,UAAU,EAAEjD,KAAK,CAAC+D,iBAAiB;EACnC5B,UAAU,EAAEnC,KAAK,CAACgE,SAAS;EAC3BpC,iBAAiB,EAAE5B,KAAK,CAACiE,UAAU,GAAGjE,KAAK,CAACkE,YAAY;EACxD1C,iBAAiB,EAAExB,KAAK,CAACa,OAAO;EAChCY,cAAc,EAAEzB,KAAK,CAACa,OAAO;EAC7Ba,gBAAgB,EAAE1B,KAAK,CAACmE,QAAQ;EAChCxC,eAAe,EAAE3B,KAAK,CAACoE,SAAS,GAAG,CAAC;EACpCZ,YAAY,EAAExD,KAAK,CAACgE,SAAS;EAC7BzC,UAAU,EAAEvB,KAAK,CAACgE;AACpB,CAAC,CAAC;AACF;AACA,eAAenE,aAAa,CAAC,cAAc,EAAEG,KAAK,IAAI;EACpD,MAAMqE,gBAAgB,GAAGvE,UAAU,CAACE,KAAK,EAAE,CAAC,CAAC,CAAC;EAC9C,OAAOsB,oBAAoB,CAAC+C,gBAAgB,CAAC;AAC/C,CAAC,EAAER,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { MenuContext } from \"../context/MenuContext\";\nimport { placements, placementsRtl } from \"../placements\";\nimport { getMotion } from \"../utils/motionUtil\";\nvar popupPlacementMap = {\n  horizontal: 'bottomLeft',\n  vertical: 'rightTop',\n  'vertical-left': 'rightTop',\n  'vertical-right': 'leftTop'\n};\nexport default function PopupTrigger(_ref) {\n  var prefixCls = _ref.prefixCls,\n    visible = _ref.visible,\n    children = _ref.children,\n    popup = _ref.popup,\n    popupStyle = _ref.popupStyle,\n    popupClassName = _ref.popupClassName,\n    popupOffset = _ref.popupOffset,\n    disabled = _ref.disabled,\n    mode = _ref.mode,\n    onVisibleChange = _ref.onVisibleChange;\n  var _React$useContext = React.useContext(MenuContext),\n    getPopupContainer = _React$useContext.getPopupContainer,\n    rtl = _React$useContext.rtl,\n    subMenuOpenDelay = _React$useContext.subMenuOpenDelay,\n    subMenuCloseDelay = _React$useContext.subMenuCloseDelay,\n    builtinPlacements = _React$useContext.builtinPlacements,\n    triggerSubMenuAction = _React$useContext.triggerSubMenuAction,\n    forceSubMenuRender = _React$useContext.forceSubMenuRender,\n    rootClassName = _React$useContext.rootClassName,\n    motion = _React$useContext.motion,\n    defaultMotions = _React$useContext.defaultMotions;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerVisible = _React$useState2[0],\n    setInnerVisible = _React$useState2[1];\n  var placement = rtl ? _objectSpread(_objectSpread({}, placementsRtl), builtinPlacements) : _objectSpread(_objectSpread({}, placements), builtinPlacements);\n  var popupPlacement = popupPlacementMap[mode];\n  var targetMotion = getMotion(mode, motion, defaultMotions);\n  var targetMotionRef = React.useRef(targetMotion);\n  if (mode !== 'inline') {\n    /**\n     * PopupTrigger is only used for vertical and horizontal types.\n     * When collapsed is unfolded, the inline animation will destroy the vertical animation.\n     */\n    targetMotionRef.current = targetMotion;\n  }\n  var mergedMotion = _objectSpread(_objectSpread({}, targetMotionRef.current), {}, {\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\"),\n    removeOnLeave: false,\n    motionAppear: true\n  });\n\n  // Delay to change visible\n  var visibleRef = React.useRef();\n  React.useEffect(function () {\n    visibleRef.current = raf(function () {\n      setInnerVisible(visible);\n    });\n    return function () {\n      raf.cancel(visibleRef.current);\n    };\n  }, [visible]);\n  return /*#__PURE__*/React.createElement(Trigger, {\n    prefixCls: prefixCls,\n    popupClassName: classNames(\"\".concat(prefixCls, \"-popup\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), rtl), popupClassName, rootClassName),\n    stretch: mode === 'horizontal' ? 'minWidth' : null,\n    getPopupContainer: getPopupContainer,\n    builtinPlacements: placement,\n    popupPlacement: popupPlacement,\n    popupVisible: innerVisible,\n    popup: popup,\n    popupStyle: popupStyle,\n    popupAlign: popupOffset && {\n      offset: popupOffset\n    },\n    action: disabled ? [] : [triggerSubMenuAction],\n    mouseEnterDelay: subMenuOpenDelay,\n    mouseLeaveDelay: subMenuCloseDelay,\n    onPopupVisibleChange: onVisibleChange,\n    forceRender: forceSubMenuRender,\n    popupMotion: mergedMotion,\n    fresh: true\n  }, children);\n}", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_slicedToArray", "React", "<PERSON><PERSON>", "classNames", "raf", "MenuContext", "placements", "placementsRtl", "getMotion", "popupPlacementMap", "horizontal", "vertical", "PopupTrigger", "_ref", "prefixCls", "visible", "children", "popup", "popupStyle", "popupClassName", "popupOffset", "disabled", "mode", "onVisibleChange", "_React$useContext", "useContext", "getPopupContainer", "rtl", "subMenuOpenDelay", "subMenuCloseDelay", "builtinPlacements", "triggerSubMenuAction", "forceSubMenuRender", "rootClassName", "motion", "defaultMotions", "_React$useState", "useState", "_React$useState2", "innerVisible", "setInnerVisible", "placement", "popupPlacement", "targetMotion", "targetMotionRef", "useRef", "current", "mergedMotion", "leavedClassName", "concat", "removeOnLeave", "motionAppear", "visibleRef", "useEffect", "cancel", "createElement", "stretch", "popupVisible", "popupAlign", "offset", "action", "mouseEnterDelay", "mouseLeaveDelay", "onPopupVisibleChange", "forceRender", "popupMotion", "fresh"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-menu/es/SubMenu/PopupTrigger.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { MenuContext } from \"../context/MenuContext\";\nimport { placements, placementsRtl } from \"../placements\";\nimport { getMotion } from \"../utils/motionUtil\";\nvar popupPlacementMap = {\n  horizontal: 'bottomLeft',\n  vertical: 'rightTop',\n  'vertical-left': 'rightTop',\n  'vertical-right': 'leftTop'\n};\nexport default function PopupTrigger(_ref) {\n  var prefixCls = _ref.prefixCls,\n    visible = _ref.visible,\n    children = _ref.children,\n    popup = _ref.popup,\n    popupStyle = _ref.popupStyle,\n    popupClassName = _ref.popupClassName,\n    popupOffset = _ref.popupOffset,\n    disabled = _ref.disabled,\n    mode = _ref.mode,\n    onVisibleChange = _ref.onVisibleChange;\n  var _React$useContext = React.useContext(MenuContext),\n    getPopupContainer = _React$useContext.getPopupContainer,\n    rtl = _React$useContext.rtl,\n    subMenuOpenDelay = _React$useContext.subMenuOpenDelay,\n    subMenuCloseDelay = _React$useContext.subMenuCloseDelay,\n    builtinPlacements = _React$useContext.builtinPlacements,\n    triggerSubMenuAction = _React$useContext.triggerSubMenuAction,\n    forceSubMenuRender = _React$useContext.forceSubMenuRender,\n    rootClassName = _React$useContext.rootClassName,\n    motion = _React$useContext.motion,\n    defaultMotions = _React$useContext.defaultMotions;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerVisible = _React$useState2[0],\n    setInnerVisible = _React$useState2[1];\n  var placement = rtl ? _objectSpread(_objectSpread({}, placementsRtl), builtinPlacements) : _objectSpread(_objectSpread({}, placements), builtinPlacements);\n  var popupPlacement = popupPlacementMap[mode];\n  var targetMotion = getMotion(mode, motion, defaultMotions);\n  var targetMotionRef = React.useRef(targetMotion);\n  if (mode !== 'inline') {\n    /**\n     * PopupTrigger is only used for vertical and horizontal types.\n     * When collapsed is unfolded, the inline animation will destroy the vertical animation.\n     */\n    targetMotionRef.current = targetMotion;\n  }\n  var mergedMotion = _objectSpread(_objectSpread({}, targetMotionRef.current), {}, {\n    leavedClassName: \"\".concat(prefixCls, \"-hidden\"),\n    removeOnLeave: false,\n    motionAppear: true\n  });\n\n  // Delay to change visible\n  var visibleRef = React.useRef();\n  React.useEffect(function () {\n    visibleRef.current = raf(function () {\n      setInnerVisible(visible);\n    });\n    return function () {\n      raf.cancel(visibleRef.current);\n    };\n  }, [visible]);\n  return /*#__PURE__*/React.createElement(Trigger, {\n    prefixCls: prefixCls,\n    popupClassName: classNames(\"\".concat(prefixCls, \"-popup\"), _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), rtl), popupClassName, rootClassName),\n    stretch: mode === 'horizontal' ? 'minWidth' : null,\n    getPopupContainer: getPopupContainer,\n    builtinPlacements: placement,\n    popupPlacement: popupPlacement,\n    popupVisible: innerVisible,\n    popup: popup,\n    popupStyle: popupStyle,\n    popupAlign: popupOffset && {\n      offset: popupOffset\n    },\n    action: disabled ? [] : [triggerSubMenuAction],\n    mouseEnterDelay: subMenuOpenDelay,\n    mouseLeaveDelay: subMenuCloseDelay,\n    onPopupVisibleChange: onVisibleChange,\n    forceRender: forceSubMenuRender,\n    popupMotion: mergedMotion,\n    fresh: true\n  }, children);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,UAAU,EAAEC,aAAa,QAAQ,eAAe;AACzD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,iBAAiB,GAAG;EACtBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpB,eAAe,EAAE,UAAU;EAC3B,gBAAgB,EAAE;AACpB,CAAC;AACD,eAAe,SAASC,YAAYA,CAACC,IAAI,EAAE;EACzC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,UAAU,GAAGL,IAAI,CAACK,UAAU;IAC5BC,cAAc,GAAGN,IAAI,CAACM,cAAc;IACpCC,WAAW,GAAGP,IAAI,CAACO,WAAW;IAC9BC,QAAQ,GAAGR,IAAI,CAACQ,QAAQ;IACxBC,IAAI,GAAGT,IAAI,CAACS,IAAI;IAChBC,eAAe,GAAGV,IAAI,CAACU,eAAe;EACxC,IAAIC,iBAAiB,GAAGvB,KAAK,CAACwB,UAAU,CAACpB,WAAW,CAAC;IACnDqB,iBAAiB,GAAGF,iBAAiB,CAACE,iBAAiB;IACvDC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,gBAAgB,GAAGJ,iBAAiB,CAACI,gBAAgB;IACrDC,iBAAiB,GAAGL,iBAAiB,CAACK,iBAAiB;IACvDC,iBAAiB,GAAGN,iBAAiB,CAACM,iBAAiB;IACvDC,oBAAoB,GAAGP,iBAAiB,CAACO,oBAAoB;IAC7DC,kBAAkB,GAAGR,iBAAiB,CAACQ,kBAAkB;IACzDC,aAAa,GAAGT,iBAAiB,CAACS,aAAa;IAC/CC,MAAM,GAAGV,iBAAiB,CAACU,MAAM;IACjCC,cAAc,GAAGX,iBAAiB,CAACW,cAAc;EACnD,IAAIC,eAAe,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGtC,cAAc,CAACoC,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,SAAS,GAAGd,GAAG,GAAG5B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEQ,aAAa,CAAC,EAAEuB,iBAAiB,CAAC,GAAG/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEO,UAAU,CAAC,EAAEwB,iBAAiB,CAAC;EAC1J,IAAIY,cAAc,GAAGjC,iBAAiB,CAACa,IAAI,CAAC;EAC5C,IAAIqB,YAAY,GAAGnC,SAAS,CAACc,IAAI,EAAEY,MAAM,EAAEC,cAAc,CAAC;EAC1D,IAAIS,eAAe,GAAG3C,KAAK,CAAC4C,MAAM,CAACF,YAAY,CAAC;EAChD,IAAIrB,IAAI,KAAK,QAAQ,EAAE;IACrB;AACJ;AACA;AACA;IACIsB,eAAe,CAACE,OAAO,GAAGH,YAAY;EACxC;EACA,IAAII,YAAY,GAAGhD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6C,eAAe,CAACE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IAC/EE,eAAe,EAAE,EAAE,CAACC,MAAM,CAACnC,SAAS,EAAE,SAAS,CAAC;IAChDoC,aAAa,EAAE,KAAK;IACpBC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,IAAIC,UAAU,GAAGnD,KAAK,CAAC4C,MAAM,CAAC,CAAC;EAC/B5C,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1BD,UAAU,CAACN,OAAO,GAAG1C,GAAG,CAAC,YAAY;MACnCoC,eAAe,CAACzB,OAAO,CAAC;IAC1B,CAAC,CAAC;IACF,OAAO,YAAY;MACjBX,GAAG,CAACkD,MAAM,CAACF,UAAU,CAACN,OAAO,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAAC/B,OAAO,CAAC,CAAC;EACb,OAAO,aAAad,KAAK,CAACsD,aAAa,CAACrD,OAAO,EAAE;IAC/CY,SAAS,EAAEA,SAAS;IACpBK,cAAc,EAAEhB,UAAU,CAAC,EAAE,CAAC8C,MAAM,CAACnC,SAAS,EAAE,QAAQ,CAAC,EAAEhB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmD,MAAM,CAACnC,SAAS,EAAE,MAAM,CAAC,EAAEa,GAAG,CAAC,EAAER,cAAc,EAAEc,aAAa,CAAC;IACjJuB,OAAO,EAAElC,IAAI,KAAK,YAAY,GAAG,UAAU,GAAG,IAAI;IAClDI,iBAAiB,EAAEA,iBAAiB;IACpCI,iBAAiB,EAAEW,SAAS;IAC5BC,cAAc,EAAEA,cAAc;IAC9Be,YAAY,EAAElB,YAAY;IAC1BtB,KAAK,EAAEA,KAAK;IACZC,UAAU,EAAEA,UAAU;IACtBwC,UAAU,EAAEtC,WAAW,IAAI;MACzBuC,MAAM,EAAEvC;IACV,CAAC;IACDwC,MAAM,EAAEvC,QAAQ,GAAG,EAAE,GAAG,CAACU,oBAAoB,CAAC;IAC9C8B,eAAe,EAAEjC,gBAAgB;IACjCkC,eAAe,EAAEjC,iBAAiB;IAClCkC,oBAAoB,EAAExC,eAAe;IACrCyC,WAAW,EAAEhC,kBAAkB;IAC/BiC,WAAW,EAAElB,YAAY;IACzBmB,KAAK,EAAE;EACT,CAAC,EAAElD,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import React,{useState}from'react';import{Card,Table,Button,Space,Typography,Tag,Form,Input,Select,// DatePicker, // 预留用于日期选择功能\nRow,Col,Statistic,message,Modal,Descriptions,Rate}from'antd';import{SearchOutlined,EyeOutlined,ExportOutlined,ReloadOutlined,ClearOutlined,StarOutlined,DownloadOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;// const { RangePicker } = DatePicker; // 预留用于日期范围选择\n// 模拟已激活订单数据\nconst mockActivatedOrders=[{id:1,orderNo:'ORD202401150001',customerName:'张三',customerPhone:'13800138001',productName:'中国移动5G畅享套餐',operator:'中国移动',completedAt:'2024-01-15 11:00:00',processingTime:0.5,rating:5,feedback:'服务很好，办理很快',processor:'李客服'},{id:2,orderNo:'ORD202401140008',customerName:'陈九',customerPhone:'13800138009',productName:'中国电信5G精选套餐',operator:'中国电信',completedAt:'2024-01-14 16:30:00',processingTime:1.2,rating:4,feedback:'整体满意，价格合理',processor:'王客服'},{id:3,orderNo:'ORD202401140007',customerName:'周十',customerPhone:'13800138010',productName:'中国联通青春套餐',operator:'中国联通',completedAt:'2024-01-14 14:15:00',processingTime:0.8,rating:5,feedback:'非常满意，推荐朋友',processor:'张客服'},{id:4,orderNo:'ORD202401140006',customerName:'吴十一',customerPhone:'13800138011',productName:'中国广电智慧套餐',operator:'中国广电',completedAt:'2024-01-14 10:45:00',processingTime:2.1,rating:3,feedback:'办理时间稍长，但结果满意',processor:'李客服'},{id:5,orderNo:'ORD202401130012',customerName:'郑十二',customerPhone:'13800138012',productName:'中国移动商务套餐',operator:'中国移动',completedAt:'2024-01-13 17:20:00',processingTime:0.3,rating:5,feedback:'专业高效，值得信赖',processor:'王客服'}];const OrderCompleted=()=>{const[form]=Form.useForm();const[orders,setOrders]=useState(mockActivatedOrders);const[filteredOrders,setFilteredOrders]=useState(mockActivatedOrders);const[loading,setLoading]=useState(false);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[detailModalVisible,setDetailModalVisible]=useState(false);const[selectedOrder,setSelectedOrder]=useState(null);// 统计数据\nconst stats={total:filteredOrders.length,highRating:filteredOrders.filter(order=>order.rating>=4).length,avgRating:filteredOrders.reduce((sum,order)=>sum+order.rating,0)/filteredOrders.length,avgProcessingTime:filteredOrders.reduce((sum,order)=>sum+order.processingTime,0)/filteredOrders.length};// 表格列定义\nconst columns=[{title:'订单号',dataIndex:'orderNo',key:'orderNo',width:160,render:text=>/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:'12px'},children:text})},{title:'客户信息',key:'customer',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600},children:record.customerName}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#666'},children:record.customerPhone})]})},{title:'产品名称',dataIndex:'productName',key:'productName',ellipsis:true},{title:'运营商',dataIndex:'operator',key:'operator',width:100,render:text=>/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:text})},{title:'客户评分',dataIndex:'rating',key:'rating',width:120,render:rating=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Rate,{disabled:true,defaultValue:rating,style:{fontSize:'14px'}}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[rating,\".0\\u5206\"]})]}),sorter:(a,b)=>a.rating-b.rating},{title:'处理时长',dataIndex:'processingTime',key:'processingTime',width:100,render:time=>/*#__PURE__*/_jsx(Text,{style:{color:time>2?'#f5222d':'#52c41a'},children:time<1?\"\".concat(Math.round(time*60),\"\\u5206\\u949F\"):\"\".concat(time.toFixed(1),\"\\u5C0F\\u65F6\")}),sorter:(a,b)=>a.processingTime-b.processingTime},{title:'处理人员',dataIndex:'processor',key:'processor',width:100},{title:'完成时间',dataIndex:'completedAt',key:'completedAt',width:150,render:text=>/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px'},children:text}),sorter:(a,b)=>new Date(a.completedAt).getTime()-new Date(b.completedAt).getTime()},{title:'操作',key:'action',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewOrder(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>handleDownloadReceipt(record.id),children:\"\\u51ED\\u8BC1\"})]})}];// 事件处理函数\nconst handleSearch=values=>{setLoading(true);setTimeout(()=>{let filtered=[...orders];if(values.orderNo){filtered=filtered.filter(order=>order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase()));}if(values.customerName){filtered=filtered.filter(order=>order.customerName.toLowerCase().includes(values.customerName.toLowerCase()));}if(values.operator){filtered=filtered.filter(order=>order.operator===values.operator);}if(values.processor){filtered=filtered.filter(order=>order.processor===values.processor);}if(values.rating){filtered=filtered.filter(order=>order.rating>=values.rating);}setFilteredOrders(filtered);setLoading(false);},500);};const handleReset=()=>{form.resetFields();setFilteredOrders(orders);};const handleRefresh=()=>{setLoading(true);setTimeout(()=>{setOrders(mockActivatedOrders);setFilteredOrders(mockActivatedOrders);setLoading(false);message.success('数据已刷新');},1000);};const handleViewOrder=order=>{setSelectedOrder(order);setDetailModalVisible(true);};const handleDownloadReceipt=id=>{message.info(\"\\u4E0B\\u8F7D\\u8BA2\\u5355 \".concat(id,\" \\u51ED\\u8BC1\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\"));};const handleExport=()=>{message.info('导出功能开发中...');};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Title,{level:2,style:{margin:0,color:'#52c41a'},children:\"\\u5DF2\\u6FC0\\u6D3B\\u8BA2\\u5355\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u67E5\\u770B\\u5DF2\\u6FC0\\u6D3B\\u7684\\u8BA2\\u5355\\u8BB0\\u5F55\\u548C\\u5BA2\\u6237\\u53CD\\u9988\"})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6FC0\\u6D3B\\u8BA2\\u5355\\u6570\",value:stats.total,valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u9AD8\\u8BC4\\u5206\\u8BA2\\u5355\",value:stats.highRating,suffix:\"\\u4E2A\",valueStyle:{color:'#f5222d'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E73\\u5747\\u8BC4\\u5206\",value:stats.avgRating,precision:1,suffix:/*#__PURE__*/_jsx(StarOutlined,{style:{color:'#faad14'}}),valueStyle:{color:'#faad14'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E73\\u5747\\u5904\\u7406\\u65F6\\u957F\",value:stats.avgProcessingTime,precision:1,suffix:\"\\u5C0F\\u65F6\",valueStyle:{color:'#1890ff'}})})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Form,{form:form,layout:\"inline\",onFinish:handleSearch,style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Form.Item,{name:\"orderNo\",label:\"\\u8BA2\\u5355\\u53F7\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",style:{width:150}})}),/*#__PURE__*/_jsx(Form.Item,{name:\"customerName\",label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",style:{width:120}})}),/*#__PURE__*/_jsx(Form.Item,{name:\"operator\",label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",style:{width:120},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u79FB\\u52A8\",children:\"\\u4E2D\\u56FD\\u79FB\\u52A8\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u7535\\u4FE1\",children:\"\\u4E2D\\u56FD\\u7535\\u4FE1\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u8054\\u901A\",children:\"\\u4E2D\\u56FD\\u8054\\u901A\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u5E7F\\u7535\",children:\"\\u4E2D\\u56FD\\u5E7F\\u7535\"})]})}),/*#__PURE__*/_jsx(Form.Item,{name:\"processor\",label:\"\\u5904\\u7406\\u4EBA\\u5458\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u5904\\u7406\\u4EBA\\u5458\",style:{width:120},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"\\u674E\\u5BA2\\u670D\",children:\"\\u674E\\u5BA2\\u670D\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u738B\\u5BA2\\u670D\",children:\"\\u738B\\u5BA2\\u670D\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u5F20\\u5BA2\\u670D\",children:\"\\u5F20\\u5BA2\\u670D\"})]})}),/*#__PURE__*/_jsx(Form.Item,{name:\"rating\",label:\"\\u6700\\u4F4E\\u8BC4\\u5206\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8BC4\\u5206\",style:{width:100},children:[/*#__PURE__*/_jsx(Select.Option,{value:5,children:\"5\\u661F\"}),/*#__PURE__*/_jsx(Select.Option,{value:4,children:\"4\\u661F\\u53CA\\u4EE5\\u4E0A\"}),/*#__PURE__*/_jsx(Select.Option,{value:3,children:\"3\\u661F\\u53CA\\u4EE5\\u4E0A\"}),/*#__PURE__*/_jsx(Select.Option,{value:2,children:\"2\\u661F\\u53CA\\u4EE5\\u4E0A\"}),/*#__PURE__*/_jsx(Select.Option,{value:1,children:\"1\\u661F\\u53CA\\u4EE5\\u4E0A\"})]})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",icon:/*#__PURE__*/_jsx(SearchOutlined,{}),children:\"\\u641C\\u7D22\"}),/*#__PURE__*/_jsx(Button,{onClick:handleReset,icon:/*#__PURE__*/_jsx(ClearOutlined,{}),children:\"\\u91CD\\u7F6E\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:16},children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Text,{strong:true,children:[\"\\u5171 \",filteredOrders.length,\" \\u6761\\u5DF2\\u6FC0\\u6D3B\\u8BA2\\u5355\"]})}),/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleRefresh,icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),children:\"\\u5237\\u65B0\"}),/*#__PURE__*/_jsx(Button,{onClick:handleExport,icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\"})]})]}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:filteredOrders,rowKey:\"id\",loading:loading,scroll:{x:1400},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u603B\\u5171 \").concat(total,\" \\u6761\")},rowSelection:{selectedRowKeys,onChange:setSelectedRowKeys}})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5DF2\\u6FC0\\u6D3B\\u8BA2\\u5355\\u8BE6\\u60C5\",open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:700,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Descriptions,{column:2,bordered:true,style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u53F7\",span:2,children:selectedOrder.orderNo}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:selectedOrder.customerName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8054\\u7CFB\\u7535\\u8BDD\",children:selectedOrder.customerPhone}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u540D\\u79F0\",span:2,children:selectedOrder.productName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5904\\u7406\\u4EBA\\u5458\",children:selectedOrder.processor}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5904\\u7406\\u65F6\\u957F\",children:selectedOrder.processingTime<1?\"\".concat(Math.round(selectedOrder.processingTime*60),\"\\u5206\\u949F\"):\"\".concat(selectedOrder.processingTime.toFixed(1),\"\\u5C0F\\u65F6\")}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5B8C\\u6210\\u65F6\\u95F4\",span:2,children:selectedOrder.completedAt})]}),/*#__PURE__*/_jsxs(Card,{title:\"\\u5BA2\\u6237\\u53CD\\u9988\",size:\"small\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:12},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BC4\\u5206\\uFF1A\"}),/*#__PURE__*/_jsx(Rate,{disabled:true,defaultValue:selectedOrder.rating,style:{marginLeft:8}}),/*#__PURE__*/_jsxs(Text,{style:{marginLeft:8},children:[selectedOrder.rating,\".0\\u5206\"]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u53CD\\u9988\\u5185\\u5BB9\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8,padding:12,background:'#f5f5f5',borderRadius:6,minHeight:60},children:selectedOrder.feedback||'客户未留下反馈'})]})]})]})})]});};export default OrderCompleted;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
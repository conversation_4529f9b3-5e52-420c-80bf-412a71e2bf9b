{"ast": null, "code": "const genControlHeight = token => {\n  const {\n    controlHeight\n  } = token;\n  return {\n    controlHeightSM: controlHeight * 0.75,\n    controlHeightXS: controlHeight * 0.5,\n    controlHeightLG: controlHeight * 1.25\n  };\n};\nexport default genControlHeight;", "map": {"version": 3, "names": ["genControlHeight", "token", "controlHeight", "controlHeightSM", "controlHeightXS", "controlHeightLG"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/theme/themes/shared/genControlHeight.js"], "sourcesContent": ["const genControlHeight = token => {\n  const {\n    controlHeight\n  } = token;\n  return {\n    controlHeightSM: controlHeight * 0.75,\n    controlHeightXS: controlHeight * 0.5,\n    controlHeightLG: controlHeight * 1.25\n  };\n};\nexport default genControlHeight;"], "mappings": "AAAA,MAAMA,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACLE,eAAe,EAAED,aAAa,GAAG,IAAI;IACrCE,eAAe,EAAEF,aAAa,GAAG,GAAG;IACpCG,eAAe,EAAEH,aAAa,GAAG;EACnC,CAAC;AACH,CAAC;AACD,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genStepsProgressDotStyle = token => {\n  const {\n    componentCls,\n    descriptionMaxWidth,\n    lineHeight,\n    dotCurrentSize,\n    dotSize,\n    motionDurationSlow\n  } = token;\n  return {\n    [\"&\".concat(componentCls, \"-dot, &\").concat(componentCls, \"-dot\").concat(componentCls, \"-small\")]: {\n      [\"\".concat(componentCls, \"-item\")]: {\n        '&-title': {\n          lineHeight\n        },\n        '&-tail': {\n          // Math.floor((token.size - token.lineWidth * 3) / 2)\n          top: token.calc(token.dotSize).sub(token.calc(token.lineWidth).mul(3).equal()).div(2).equal(),\n          width: '100%',\n          marginTop: 0,\n          marginBottom: 0,\n          marginInline: \"\".concat(unit(token.calc(descriptionMaxWidth).div(2).equal()), \" 0\"),\n          padding: 0,\n          '&::after': {\n            width: \"calc(100% - \".concat(unit(token.calc(token.marginSM).mul(2).equal()), \")\"),\n            height: token.calc(token.lineWidth).mul(3).equal(),\n            marginInlineStart: token.marginSM\n          }\n        },\n        '&-icon': {\n          width: dotSize,\n          height: dotSize,\n          marginInlineStart: token.calc(token.descriptionMaxWidth).sub(dotSize).div(2).equal(),\n          paddingInlineEnd: 0,\n          lineHeight: unit(dotSize),\n          background: 'transparent',\n          border: 0,\n          [\"\".concat(componentCls, \"-icon-dot\")]: {\n            position: 'relative',\n            float: 'left',\n            width: '100%',\n            height: '100%',\n            borderRadius: 100,\n            // very large number\n            transition: \"all \".concat(motionDurationSlow),\n            /* expand hover area */\n            '&::after': {\n              position: 'absolute',\n              top: token.calc(token.marginSM).mul(-1).equal(),\n              insetInlineStart: token.calc(dotSize).sub(token.calc(token.controlHeightLG).mul(1.5).equal()).div(2).equal(),\n              width: token.calc(token.controlHeightLG).mul(1.5).equal(),\n              height: token.controlHeight,\n              background: 'transparent',\n              content: '\"\"'\n            }\n          }\n        },\n        '&-content': {\n          width: descriptionMaxWidth\n        },\n        [\"&-process \".concat(componentCls, \"-item-icon\")]: {\n          position: 'relative',\n          top: token.calc(dotSize).sub(dotCurrentSize).div(2).equal(),\n          width: dotCurrentSize,\n          height: dotCurrentSize,\n          lineHeight: unit(dotCurrentSize),\n          background: 'none',\n          marginInlineStart: token.calc(token.descriptionMaxWidth).sub(dotCurrentSize).div(2).equal()\n        },\n        [\"&-process \".concat(componentCls, \"-icon\")]: {\n          [\"&:first-child \".concat(componentCls, \"-icon-dot\")]: {\n            insetInlineStart: 0\n          }\n        }\n      }\n    },\n    [\"&\".concat(componentCls, \"-vertical\").concat(componentCls, \"-dot\")]: {\n      [\"\".concat(componentCls, \"-item-icon\")]: {\n        marginTop: token.calc(token.controlHeight).sub(dotSize).div(2).equal(),\n        marginInlineStart: 0,\n        background: 'none'\n      },\n      [\"\".concat(componentCls, \"-item-process \").concat(componentCls, \"-item-icon\")]: {\n        marginTop: token.calc(token.controlHeight).sub(dotCurrentSize).div(2).equal(),\n        top: 0,\n        insetInlineStart: token.calc(dotSize).sub(dotCurrentSize).div(2).equal(),\n        marginInlineStart: 0\n      },\n      // https://github.com/ant-design/ant-design/issues/18354\n      [\"\".concat(componentCls, \"-item > \").concat(componentCls, \"-item-container > \").concat(componentCls, \"-item-tail\")]: {\n        top: token.calc(token.controlHeight).sub(dotSize).div(2).equal(),\n        insetInlineStart: 0,\n        margin: 0,\n        padding: \"\".concat(unit(token.calc(dotSize).add(token.paddingXS).equal()), \" 0 \").concat(unit(token.paddingXS)),\n        '&::after': {\n          marginInlineStart: token.calc(dotSize).sub(token.lineWidth).div(2).equal()\n        }\n      },\n      [\"&\".concat(componentCls, \"-small\")]: {\n        [\"\".concat(componentCls, \"-item-icon\")]: {\n          marginTop: token.calc(token.controlHeightSM).sub(dotSize).div(2).equal()\n        },\n        [\"\".concat(componentCls, \"-item-process \").concat(componentCls, \"-item-icon\")]: {\n          marginTop: token.calc(token.controlHeightSM).sub(dotCurrentSize).div(2).equal()\n        },\n        [\"\".concat(componentCls, \"-item > \").concat(componentCls, \"-item-container > \").concat(componentCls, \"-item-tail\")]: {\n          top: token.calc(token.controlHeightSM).sub(dotSize).div(2).equal()\n        }\n      },\n      [\"\".concat(componentCls, \"-item:first-child \").concat(componentCls, \"-icon-dot\")]: {\n        insetInlineStart: 0\n      },\n      [\"\".concat(componentCls, \"-item-content\")]: {\n        width: 'inherit'\n      }\n    }\n  };\n};\nexport default genStepsProgressDotStyle;", "map": {"version": 3, "names": ["unit", "genStepsProgressDotStyle", "token", "componentCls", "descriptionMaxWidth", "lineHeight", "dotCurrentSize", "dotSize", "motionDurationSlow", "concat", "top", "calc", "sub", "lineWidth", "mul", "equal", "div", "width", "marginTop", "marginBottom", "marginInline", "padding", "marginSM", "height", "marginInlineStart", "paddingInlineEnd", "background", "border", "position", "float", "borderRadius", "transition", "insetInlineStart", "controlHeightLG", "controlHeight", "content", "margin", "add", "paddingXS", "controlHeightSM"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/steps/style/progress-dot.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genStepsProgressDotStyle = token => {\n  const {\n    componentCls,\n    descriptionMaxWidth,\n    lineHeight,\n    dotCurrentSize,\n    dotSize,\n    motionDurationSlow\n  } = token;\n  return {\n    [`&${componentCls}-dot, &${componentCls}-dot${componentCls}-small`]: {\n      [`${componentCls}-item`]: {\n        '&-title': {\n          lineHeight\n        },\n        '&-tail': {\n          // Math.floor((token.size - token.lineWidth * 3) / 2)\n          top: token.calc(token.dotSize).sub(token.calc(token.lineWidth).mul(3).equal()).div(2).equal(),\n          width: '100%',\n          marginTop: 0,\n          marginBottom: 0,\n          marginInline: `${unit(token.calc(descriptionMaxWidth).div(2).equal())} 0`,\n          padding: 0,\n          '&::after': {\n            width: `calc(100% - ${unit(token.calc(token.marginSM).mul(2).equal())})`,\n            height: token.calc(token.lineWidth).mul(3).equal(),\n            marginInlineStart: token.marginSM\n          }\n        },\n        '&-icon': {\n          width: dotSize,\n          height: dotSize,\n          marginInlineStart: token.calc(token.descriptionMaxWidth).sub(dotSize).div(2).equal(),\n          paddingInlineEnd: 0,\n          lineHeight: unit(dotSize),\n          background: 'transparent',\n          border: 0,\n          [`${componentCls}-icon-dot`]: {\n            position: 'relative',\n            float: 'left',\n            width: '100%',\n            height: '100%',\n            borderRadius: 100,\n            // very large number\n            transition: `all ${motionDurationSlow}`,\n            /* expand hover area */\n            '&::after': {\n              position: 'absolute',\n              top: token.calc(token.marginSM).mul(-1).equal(),\n              insetInlineStart: token.calc(dotSize).sub(token.calc(token.controlHeightLG).mul(1.5).equal()).div(2).equal(),\n              width: token.calc(token.controlHeightLG).mul(1.5).equal(),\n              height: token.controlHeight,\n              background: 'transparent',\n              content: '\"\"'\n            }\n          }\n        },\n        '&-content': {\n          width: descriptionMaxWidth\n        },\n        [`&-process ${componentCls}-item-icon`]: {\n          position: 'relative',\n          top: token.calc(dotSize).sub(dotCurrentSize).div(2).equal(),\n          width: dotCurrentSize,\n          height: dotCurrentSize,\n          lineHeight: unit(dotCurrentSize),\n          background: 'none',\n          marginInlineStart: token.calc(token.descriptionMaxWidth).sub(dotCurrentSize).div(2).equal()\n        },\n        [`&-process ${componentCls}-icon`]: {\n          [`&:first-child ${componentCls}-icon-dot`]: {\n            insetInlineStart: 0\n          }\n        }\n      }\n    },\n    [`&${componentCls}-vertical${componentCls}-dot`]: {\n      [`${componentCls}-item-icon`]: {\n        marginTop: token.calc(token.controlHeight).sub(dotSize).div(2).equal(),\n        marginInlineStart: 0,\n        background: 'none'\n      },\n      [`${componentCls}-item-process ${componentCls}-item-icon`]: {\n        marginTop: token.calc(token.controlHeight).sub(dotCurrentSize).div(2).equal(),\n        top: 0,\n        insetInlineStart: token.calc(dotSize).sub(dotCurrentSize).div(2).equal(),\n        marginInlineStart: 0\n      },\n      // https://github.com/ant-design/ant-design/issues/18354\n      [`${componentCls}-item > ${componentCls}-item-container > ${componentCls}-item-tail`]: {\n        top: token.calc(token.controlHeight).sub(dotSize).div(2).equal(),\n        insetInlineStart: 0,\n        margin: 0,\n        padding: `${unit(token.calc(dotSize).add(token.paddingXS).equal())} 0 ${unit(token.paddingXS)}`,\n        '&::after': {\n          marginInlineStart: token.calc(dotSize).sub(token.lineWidth).div(2).equal()\n        }\n      },\n      [`&${componentCls}-small`]: {\n        [`${componentCls}-item-icon`]: {\n          marginTop: token.calc(token.controlHeightSM).sub(dotSize).div(2).equal()\n        },\n        [`${componentCls}-item-process ${componentCls}-item-icon`]: {\n          marginTop: token.calc(token.controlHeightSM).sub(dotCurrentSize).div(2).equal()\n        },\n        [`${componentCls}-item > ${componentCls}-item-container > ${componentCls}-item-tail`]: {\n          top: token.calc(token.controlHeightSM).sub(dotSize).div(2).equal()\n        }\n      },\n      [`${componentCls}-item:first-child ${componentCls}-icon-dot`]: {\n        insetInlineStart: 0\n      },\n      [`${componentCls}-item-content`]: {\n        width: 'inherit'\n      }\n    }\n  };\n};\nexport default genStepsProgressDotStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,wBAAwB,GAAGC,KAAK,IAAI;EACxC,MAAM;IACJC,YAAY;IACZC,mBAAmB;IACnBC,UAAU;IACVC,cAAc;IACdC,OAAO;IACPC;EACF,CAAC,GAAGN,KAAK;EACT,OAAO;IACL,KAAAO,MAAA,CAAKN,YAAY,aAAAM,MAAA,CAAUN,YAAY,UAAAM,MAAA,CAAON,YAAY,cAAW;MACnE,IAAAM,MAAA,CAAIN,YAAY,aAAU;QACxB,SAAS,EAAE;UACTE;QACF,CAAC;QACD,QAAQ,EAAE;UACR;UACAK,GAAG,EAAER,KAAK,CAACS,IAAI,CAACT,KAAK,CAACK,OAAO,CAAC,CAACK,GAAG,CAACV,KAAK,CAACS,IAAI,CAACT,KAAK,CAACW,SAAS,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;UAC7FE,KAAK,EAAE,MAAM;UACbC,SAAS,EAAE,CAAC;UACZC,YAAY,EAAE,CAAC;UACfC,YAAY,KAAAX,MAAA,CAAKT,IAAI,CAACE,KAAK,CAACS,IAAI,CAACP,mBAAmB,CAAC,CAACY,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC,CAAC,OAAI;UACzEM,OAAO,EAAE,CAAC;UACV,UAAU,EAAE;YACVJ,KAAK,iBAAAR,MAAA,CAAiBT,IAAI,CAACE,KAAK,CAACS,IAAI,CAACT,KAAK,CAACoB,QAAQ,CAAC,CAACR,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,MAAG;YACxEQ,MAAM,EAAErB,KAAK,CAACS,IAAI,CAACT,KAAK,CAACW,SAAS,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YAClDS,iBAAiB,EAAEtB,KAAK,CAACoB;UAC3B;QACF,CAAC;QACD,QAAQ,EAAE;UACRL,KAAK,EAAEV,OAAO;UACdgB,MAAM,EAAEhB,OAAO;UACfiB,iBAAiB,EAAEtB,KAAK,CAACS,IAAI,CAACT,KAAK,CAACE,mBAAmB,CAAC,CAACQ,GAAG,CAACL,OAAO,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;UACpFU,gBAAgB,EAAE,CAAC;UACnBpB,UAAU,EAAEL,IAAI,CAACO,OAAO,CAAC;UACzBmB,UAAU,EAAE,aAAa;UACzBC,MAAM,EAAE,CAAC;UACT,IAAAlB,MAAA,CAAIN,YAAY,iBAAc;YAC5ByB,QAAQ,EAAE,UAAU;YACpBC,KAAK,EAAE,MAAM;YACbZ,KAAK,EAAE,MAAM;YACbM,MAAM,EAAE,MAAM;YACdO,YAAY,EAAE,GAAG;YACjB;YACAC,UAAU,SAAAtB,MAAA,CAASD,kBAAkB,CAAE;YACvC;YACA,UAAU,EAAE;cACVoB,QAAQ,EAAE,UAAU;cACpBlB,GAAG,EAAER,KAAK,CAACS,IAAI,CAACT,KAAK,CAACoB,QAAQ,CAAC,CAACR,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;cAC/CiB,gBAAgB,EAAE9B,KAAK,CAACS,IAAI,CAACJ,OAAO,CAAC,CAACK,GAAG,CAACV,KAAK,CAACS,IAAI,CAACT,KAAK,CAAC+B,eAAe,CAAC,CAACnB,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;cAC5GE,KAAK,EAAEf,KAAK,CAACS,IAAI,CAACT,KAAK,CAAC+B,eAAe,CAAC,CAACnB,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;cACzDQ,MAAM,EAAErB,KAAK,CAACgC,aAAa;cAC3BR,UAAU,EAAE,aAAa;cACzBS,OAAO,EAAE;YACX;UACF;QACF,CAAC;QACD,WAAW,EAAE;UACXlB,KAAK,EAAEb;QACT,CAAC;QACD,cAAAK,MAAA,CAAcN,YAAY,kBAAe;UACvCyB,QAAQ,EAAE,UAAU;UACpBlB,GAAG,EAAER,KAAK,CAACS,IAAI,CAACJ,OAAO,CAAC,CAACK,GAAG,CAACN,cAAc,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;UAC3DE,KAAK,EAAEX,cAAc;UACrBiB,MAAM,EAAEjB,cAAc;UACtBD,UAAU,EAAEL,IAAI,CAACM,cAAc,CAAC;UAChCoB,UAAU,EAAE,MAAM;UAClBF,iBAAiB,EAAEtB,KAAK,CAACS,IAAI,CAACT,KAAK,CAACE,mBAAmB,CAAC,CAACQ,GAAG,CAACN,cAAc,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QAC5F,CAAC;QACD,cAAAN,MAAA,CAAcN,YAAY,aAAU;UAClC,kBAAAM,MAAA,CAAkBN,YAAY,iBAAc;YAC1C6B,gBAAgB,EAAE;UACpB;QACF;MACF;IACF,CAAC;IACD,KAAAvB,MAAA,CAAKN,YAAY,eAAAM,MAAA,CAAYN,YAAY,YAAS;MAChD,IAAAM,MAAA,CAAIN,YAAY,kBAAe;QAC7Be,SAAS,EAAEhB,KAAK,CAACS,IAAI,CAACT,KAAK,CAACgC,aAAa,CAAC,CAACtB,GAAG,CAACL,OAAO,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;QACtES,iBAAiB,EAAE,CAAC;QACpBE,UAAU,EAAE;MACd,CAAC;MACD,IAAAjB,MAAA,CAAIN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,kBAAe;QAC1De,SAAS,EAAEhB,KAAK,CAACS,IAAI,CAACT,KAAK,CAACgC,aAAa,CAAC,CAACtB,GAAG,CAACN,cAAc,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;QAC7EL,GAAG,EAAE,CAAC;QACNsB,gBAAgB,EAAE9B,KAAK,CAACS,IAAI,CAACJ,OAAO,CAAC,CAACK,GAAG,CAACN,cAAc,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;QACxES,iBAAiB,EAAE;MACrB,CAAC;MACD;MACA,IAAAf,MAAA,CAAIN,YAAY,cAAAM,MAAA,CAAWN,YAAY,wBAAAM,MAAA,CAAqBN,YAAY,kBAAe;QACrFO,GAAG,EAAER,KAAK,CAACS,IAAI,CAACT,KAAK,CAACgC,aAAa,CAAC,CAACtB,GAAG,CAACL,OAAO,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;QAChEiB,gBAAgB,EAAE,CAAC;QACnBI,MAAM,EAAE,CAAC;QACTf,OAAO,KAAAZ,MAAA,CAAKT,IAAI,CAACE,KAAK,CAACS,IAAI,CAACJ,OAAO,CAAC,CAAC8B,GAAG,CAACnC,KAAK,CAACoC,SAAS,CAAC,CAACvB,KAAK,CAAC,CAAC,CAAC,SAAAN,MAAA,CAAMT,IAAI,CAACE,KAAK,CAACoC,SAAS,CAAC,CAAE;QAC/F,UAAU,EAAE;UACVd,iBAAiB,EAAEtB,KAAK,CAACS,IAAI,CAACJ,OAAO,CAAC,CAACK,GAAG,CAACV,KAAK,CAACW,SAAS,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QAC3E;MACF,CAAC;MACD,KAAAN,MAAA,CAAKN,YAAY,cAAW;QAC1B,IAAAM,MAAA,CAAIN,YAAY,kBAAe;UAC7Be,SAAS,EAAEhB,KAAK,CAACS,IAAI,CAACT,KAAK,CAACqC,eAAe,CAAC,CAAC3B,GAAG,CAACL,OAAO,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QACzE,CAAC;QACD,IAAAN,MAAA,CAAIN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,kBAAe;UAC1De,SAAS,EAAEhB,KAAK,CAACS,IAAI,CAACT,KAAK,CAACqC,eAAe,CAAC,CAAC3B,GAAG,CAACN,cAAc,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QAChF,CAAC;QACD,IAAAN,MAAA,CAAIN,YAAY,cAAAM,MAAA,CAAWN,YAAY,wBAAAM,MAAA,CAAqBN,YAAY,kBAAe;UACrFO,GAAG,EAAER,KAAK,CAACS,IAAI,CAACT,KAAK,CAACqC,eAAe,CAAC,CAAC3B,GAAG,CAACL,OAAO,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QACnE;MACF,CAAC;MACD,IAAAN,MAAA,CAAIN,YAAY,wBAAAM,MAAA,CAAqBN,YAAY,iBAAc;QAC7D6B,gBAAgB,EAAE;MACpB,CAAC;MACD,IAAAvB,MAAA,CAAIN,YAAY,qBAAkB;QAChCc,KAAK,EAAE;MACT;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAehB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
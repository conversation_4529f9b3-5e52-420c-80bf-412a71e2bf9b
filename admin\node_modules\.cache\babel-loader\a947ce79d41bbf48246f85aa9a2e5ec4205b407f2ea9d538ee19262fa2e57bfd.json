{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RestTwoToneSvg from \"@ant-design/icons-svg/es/asn/RestTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RestTwoTone = function RestTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RestTwoToneSvg\n  }));\n};\n\n/**![rest](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMyNi40IDg0NGgzNjMuMmw0NC4zLTUyMEgyODJsNDQuNCA1MjB6TTUwOCA0MTZjNzkuNSAwIDE0NCA2NC41IDE0NCAxNDRzLTY0LjUgMTQ0LTE0NCAxNDQtMTQ0LTY0LjUtMTQ0LTE0NCA2NC41LTE0NCAxNDQtMTQ0eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNTA4IDcwNGM3OS41IDAgMTQ0LTY0LjUgMTQ0LTE0NHMtNjQuNS0xNDQtMTQ0LTE0NC0xNDQgNjQuNS0xNDQgMTQ0IDY0LjUgMTQ0IDE0NCAxNDR6bTAtMjI0YzQ0LjIgMCA4MCAzNS44IDgwIDgwcy0zNS44IDgwLTgwIDgwLTgwLTM1LjgtODAtODAgMzUuOC04MCA4MC04MHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTgzMiAyNTZoLTI4LjFsLTM1LjctMTIwLjljLTQtMTMuNy0xNi41LTIzLjEtMzAuNy0yMy4xaC00NTFjLTE0LjMgMC0yNi44IDkuNC0zMC43IDIzLjFMMjIwLjEgMjU2SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjhjMCA0LjQgMy42IDggOCA4aDQ1LjhsNDcuNyA1NTguN2EzMiAzMiAwIDAwMzEuOSAyOS4zaDQyOS4yYTMyIDMyIDAgMDAzMS45LTI5LjNMODAyLjIgMzI0SDg1NmM0LjQgMCA4LTMuNiA4LTh2LTI4YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNTE4LjYtNzZoMzk3LjJsMjIuNCA3NkgyOTFsMjIuNC03NnptMzc2LjIgNjY0SDMyNi40TDI4MiAzMjRoNDUxLjlsLTQ0LjMgNTIweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RestTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RestTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "RestTwoToneSvg", "AntdIcon", "RestTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/RestTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RestTwoToneSvg from \"@ant-design/icons-svg/es/asn/RestTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RestTwoTone = function RestTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RestTwoToneSvg\n  }));\n};\n\n/**![rest](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMyNi40IDg0NGgzNjMuMmw0NC4zLTUyMEgyODJsNDQuNCA1MjB6TTUwOCA0MTZjNzkuNSAwIDE0NCA2NC41IDE0NCAxNDRzLTY0LjUgMTQ0LTE0NCAxNDQtMTQ0LTY0LjUtMTQ0LTE0NCA2NC41LTE0NCAxNDQtMTQ0eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNTA4IDcwNGM3OS41IDAgMTQ0LTY0LjUgMTQ0LTE0NHMtNjQuNS0xNDQtMTQ0LTE0NC0xNDQgNjQuNS0xNDQgMTQ0IDY0LjUgMTQ0IDE0NCAxNDR6bTAtMjI0YzQ0LjIgMCA4MCAzNS44IDgwIDgwcy0zNS44IDgwLTgwIDgwLTgwLTM1LjgtODAtODAgMzUuOC04MCA4MC04MHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTgzMiAyNTZoLTI4LjFsLTM1LjctMTIwLjljLTQtMTMuNy0xNi41LTIzLjEtMzAuNy0yMy4xaC00NTFjLTE0LjMgMC0yNi44IDkuNC0zMC43IDIzLjFMMjIwLjEgMjU2SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjhjMCA0LjQgMy42IDggOCA4aDQ1LjhsNDcuNyA1NTguN2EzMiAzMiAwIDAwMzEuOSAyOS4zaDQyOS4yYTMyIDMyIDAgMDAzMS45LTI5LjNMODAyLjIgMzI0SDg1NmM0LjQgMCA4LTMuNiA4LTh2LTI4YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNTE4LjYtNzZoMzk3LjJsMjIuNCA3NkgyOTFsMjIuNC03NnptMzc2LjIgNjY0SDMyNi40TDI4MiAzMjRoNDUxLjlsLTQ0LjMgNTIweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RestTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RestTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
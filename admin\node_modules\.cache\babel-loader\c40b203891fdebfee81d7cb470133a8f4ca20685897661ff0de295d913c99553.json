{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport BreadcrumbItem, { InternalBreadcrumbItem } from './BreadcrumbItem';\nimport BreadcrumbSeparator from './BreadcrumbSeparator';\nimport useStyle from './style';\nimport useItemRender from './useItemRender';\nimport useItems from './useItems';\nconst getPath = (params, path) => {\n  if (path === undefined) {\n    return path;\n  }\n  let mergedPath = (path || '').replace(/^\\//, '');\n  Object.keys(params).forEach(key => {\n    mergedPath = mergedPath.replace(`:${key}`, params[key]);\n  });\n  return mergedPath;\n};\nconst Breadcrumb = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      separator = '/',\n      style,\n      className,\n      rootClassName,\n      routes: legacyRoutes,\n      items,\n      children,\n      itemRender,\n      params = {}\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"separator\", \"style\", \"className\", \"rootClassName\", \"routes\", \"items\", \"children\", \"itemRender\", \"params\"]);\n  const {\n    getPrefixCls,\n    direction,\n    breadcrumb\n  } = React.useContext(ConfigContext);\n  let crumbs;\n  const prefixCls = getPrefixCls('breadcrumb', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedItems = useItems(items, legacyRoutes);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Breadcrumb');\n    warning.deprecated(!legacyRoutes, 'routes', 'items');\n    // Deprecated warning for breadcrumb children\n    if (!mergedItems || mergedItems.length === 0) {\n      const childList = toArray(children);\n      warning.deprecated(childList.length === 0, 'Breadcrumb.Item and Breadcrumb.Separator', 'items');\n      childList.forEach(element => {\n        if (element) {\n          process.env.NODE_ENV !== \"production\" ? warning(element.type && (element.type.__ANT_BREADCRUMB_ITEM === true || element.type.__ANT_BREADCRUMB_SEPARATOR === true), 'usage', \"Only accepts Breadcrumb.Item and Breadcrumb.Separator as it's children\") : void 0;\n        }\n      });\n    }\n  }\n  const mergedItemRender = useItemRender(prefixCls, itemRender);\n  if (mergedItems && mergedItems.length > 0) {\n    // generated by route\n    const paths = [];\n    const itemRenderRoutes = items || legacyRoutes;\n    crumbs = mergedItems.map((item, index) => {\n      const {\n        path,\n        key,\n        type,\n        menu,\n        overlay,\n        onClick,\n        className: itemClassName,\n        separator: itemSeparator,\n        dropdownProps\n      } = item;\n      const mergedPath = getPath(params, path);\n      if (mergedPath !== undefined) {\n        paths.push(mergedPath);\n      }\n      const mergedKey = key !== null && key !== void 0 ? key : index;\n      if (type === 'separator') {\n        return /*#__PURE__*/React.createElement(BreadcrumbSeparator, {\n          key: mergedKey\n        }, itemSeparator);\n      }\n      const itemProps = {};\n      const isLastItem = index === mergedItems.length - 1;\n      if (menu) {\n        itemProps.menu = menu;\n      } else if (overlay) {\n        itemProps.overlay = overlay;\n      }\n      let {\n        href\n      } = item;\n      if (paths.length && mergedPath !== undefined) {\n        href = `#/${paths.join('/')}`;\n      }\n      return /*#__PURE__*/React.createElement(InternalBreadcrumbItem, Object.assign({\n        key: mergedKey\n      }, itemProps, pickAttrs(item, {\n        data: true,\n        aria: true\n      }), {\n        className: itemClassName,\n        dropdownProps: dropdownProps,\n        href: href,\n        separator: isLastItem ? '' : separator,\n        onClick: onClick,\n        prefixCls: prefixCls\n      }), mergedItemRender(item, params, itemRenderRoutes, paths, href));\n    });\n  } else if (children) {\n    const childrenLength = toArray(children).length;\n    crumbs = toArray(children).map((element, index) => {\n      if (!element) {\n        return element;\n      }\n      const isLastItem = index === childrenLength - 1;\n      return cloneElement(element, {\n        separator: isLastItem ? '' : separator,\n        // eslint-disable-next-line react/no-array-index-key\n        key: index\n      });\n    });\n  }\n  const breadcrumbClassName = classNames(prefixCls, breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.className, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.style), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"nav\", Object.assign({\n    className: breadcrumbClassName,\n    style: mergedStyle\n  }, restProps), /*#__PURE__*/React.createElement(\"ol\", null, crumbs)));\n};\nBreadcrumb.Item = BreadcrumbItem;\nBreadcrumb.Separator = BreadcrumbSeparator;\nif (process.env.NODE_ENV !== 'production') {\n  Breadcrumb.displayName = 'Breadcrumb';\n}\nexport default Breadcrumb;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "toArray", "pickAttrs", "cloneElement", "devUseW<PERSON>ning", "ConfigContext", "BreadcrumbItem", "InternalBreadcrumbItem", "BreadcrumbSeparator", "useStyle", "useItemRender", "useItems", "<PERSON><PERSON><PERSON>", "params", "path", "undefined", "mergedPath", "replace", "keys", "for<PERSON>ach", "key", "Breadcrumb", "props", "prefixCls", "customizePrefixCls", "separator", "style", "className", "rootClassName", "routes", "legacyRoutes", "items", "children", "itemRender", "restProps", "getPrefixCls", "direction", "breadcrumb", "useContext", "crumbs", "wrapCSSVar", "hashId", "cssVarCls", "mergedItems", "process", "env", "NODE_ENV", "warning", "deprecated", "childList", "element", "type", "__ANT_BREADCRUMB_ITEM", "__ANT_BREADCRUMB_SEPARATOR", "mergedItemRender", "paths", "itemRenderRoutes", "map", "item", "index", "menu", "overlay", "onClick", "itemClassName", "itemSeparator", "dropdownProps", "push", "mergedKey", "createElement", "itemProps", "isLastItem", "href", "join", "assign", "data", "aria", "<PERSON><PERSON><PERSON><PERSON>", "breadcrumbClassName", "mergedStyle", "<PERSON><PERSON>", "Separator", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/breadcrumb/Breadcrumb.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport BreadcrumbItem, { InternalBreadcrumbItem } from './BreadcrumbItem';\nimport BreadcrumbSeparator from './BreadcrumbSeparator';\nimport useStyle from './style';\nimport useItemRender from './useItemRender';\nimport useItems from './useItems';\nconst getPath = (params, path) => {\n  if (path === undefined) {\n    return path;\n  }\n  let mergedPath = (path || '').replace(/^\\//, '');\n  Object.keys(params).forEach(key => {\n    mergedPath = mergedPath.replace(`:${key}`, params[key]);\n  });\n  return mergedPath;\n};\nconst Breadcrumb = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      separator = '/',\n      style,\n      className,\n      rootClassName,\n      routes: legacyRoutes,\n      items,\n      children,\n      itemRender,\n      params = {}\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"separator\", \"style\", \"className\", \"rootClassName\", \"routes\", \"items\", \"children\", \"itemRender\", \"params\"]);\n  const {\n    getPrefixCls,\n    direction,\n    breadcrumb\n  } = React.useContext(ConfigContext);\n  let crumbs;\n  const prefixCls = getPrefixCls('breadcrumb', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedItems = useItems(items, legacyRoutes);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Breadcrumb');\n    warning.deprecated(!legacyRoutes, 'routes', 'items');\n    // Deprecated warning for breadcrumb children\n    if (!mergedItems || mergedItems.length === 0) {\n      const childList = toArray(children);\n      warning.deprecated(childList.length === 0, 'Breadcrumb.Item and Breadcrumb.Separator', 'items');\n      childList.forEach(element => {\n        if (element) {\n          process.env.NODE_ENV !== \"production\" ? warning(element.type && (element.type.__ANT_BREADCRUMB_ITEM === true || element.type.__ANT_BREADCRUMB_SEPARATOR === true), 'usage', \"Only accepts Breadcrumb.Item and Breadcrumb.Separator as it's children\") : void 0;\n        }\n      });\n    }\n  }\n  const mergedItemRender = useItemRender(prefixCls, itemRender);\n  if (mergedItems && mergedItems.length > 0) {\n    // generated by route\n    const paths = [];\n    const itemRenderRoutes = items || legacyRoutes;\n    crumbs = mergedItems.map((item, index) => {\n      const {\n        path,\n        key,\n        type,\n        menu,\n        overlay,\n        onClick,\n        className: itemClassName,\n        separator: itemSeparator,\n        dropdownProps\n      } = item;\n      const mergedPath = getPath(params, path);\n      if (mergedPath !== undefined) {\n        paths.push(mergedPath);\n      }\n      const mergedKey = key !== null && key !== void 0 ? key : index;\n      if (type === 'separator') {\n        return /*#__PURE__*/React.createElement(BreadcrumbSeparator, {\n          key: mergedKey\n        }, itemSeparator);\n      }\n      const itemProps = {};\n      const isLastItem = index === mergedItems.length - 1;\n      if (menu) {\n        itemProps.menu = menu;\n      } else if (overlay) {\n        itemProps.overlay = overlay;\n      }\n      let {\n        href\n      } = item;\n      if (paths.length && mergedPath !== undefined) {\n        href = `#/${paths.join('/')}`;\n      }\n      return /*#__PURE__*/React.createElement(InternalBreadcrumbItem, Object.assign({\n        key: mergedKey\n      }, itemProps, pickAttrs(item, {\n        data: true,\n        aria: true\n      }), {\n        className: itemClassName,\n        dropdownProps: dropdownProps,\n        href: href,\n        separator: isLastItem ? '' : separator,\n        onClick: onClick,\n        prefixCls: prefixCls\n      }), mergedItemRender(item, params, itemRenderRoutes, paths, href));\n    });\n  } else if (children) {\n    const childrenLength = toArray(children).length;\n    crumbs = toArray(children).map((element, index) => {\n      if (!element) {\n        return element;\n      }\n      const isLastItem = index === childrenLength - 1;\n      return cloneElement(element, {\n        separator: isLastItem ? '' : separator,\n        // eslint-disable-next-line react/no-array-index-key\n        key: index\n      });\n    });\n  }\n  const breadcrumbClassName = classNames(prefixCls, breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.className, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.style), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"nav\", Object.assign({\n    className: breadcrumbClassName,\n    style: mergedStyle\n  }, restProps), /*#__PURE__*/React.createElement(\"ol\", null, crumbs)));\n};\nBreadcrumb.Item = BreadcrumbItem;\nBreadcrumb.Separator = BreadcrumbSeparator;\nif (process.env.NODE_ENV !== 'production') {\n  Breadcrumb.displayName = 'Breadcrumb';\n}\nexport default Breadcrumb;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,IAAIC,sBAAsB,QAAQ,kBAAkB;AACzE,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,MAAMC,OAAO,GAAGA,CAACC,MAAM,EAAEC,IAAI,KAAK;EAChC,IAAIA,IAAI,KAAKC,SAAS,EAAE;IACtB,OAAOD,IAAI;EACb;EACA,IAAIE,UAAU,GAAG,CAACF,IAAI,IAAI,EAAE,EAAEG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAChD3B,MAAM,CAAC4B,IAAI,CAACL,MAAM,CAAC,CAACM,OAAO,CAACC,GAAG,IAAI;IACjCJ,UAAU,GAAGA,UAAU,CAACC,OAAO,CAAC,IAAIG,GAAG,EAAE,EAAEP,MAAM,CAACO,GAAG,CAAC,CAAC;EACzD,CAAC,CAAC;EACF,OAAOJ,UAAU;AACnB,CAAC;AACD,MAAMK,UAAU,GAAGC,KAAK,IAAI;EAC1B,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS,GAAG,GAAG;MACfC,KAAK;MACLC,SAAS;MACTC,aAAa;MACbC,MAAM,EAAEC,YAAY;MACpBC,KAAK;MACLC,QAAQ;MACRC,UAAU;MACVpB,MAAM,GAAG,CAAC;IACZ,CAAC,GAAGS,KAAK;IACTY,SAAS,GAAGjD,MAAM,CAACqC,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;EACrJ,MAAM;IACJa,YAAY;IACZC,SAAS;IACTC;EACF,CAAC,GAAGtC,KAAK,CAACuC,UAAU,CAACjC,aAAa,CAAC;EACnC,IAAIkC,MAAM;EACV,MAAMhB,SAAS,GAAGY,YAAY,CAAC,YAAY,EAAEX,kBAAkB,CAAC;EAChE,MAAM,CAACgB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAACc,SAAS,CAAC;EAC3D,MAAMoB,WAAW,GAAGhC,QAAQ,CAACoB,KAAK,EAAED,YAAY,CAAC;EACjD,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAG3C,aAAa,CAAC,YAAY,CAAC;IAC3C2C,OAAO,CAACC,UAAU,CAAC,CAAClB,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC;IACpD;IACA,IAAI,CAACa,WAAW,IAAIA,WAAW,CAAC9C,MAAM,KAAK,CAAC,EAAE;MAC5C,MAAMoD,SAAS,GAAGhD,OAAO,CAAC+B,QAAQ,CAAC;MACnCe,OAAO,CAACC,UAAU,CAACC,SAAS,CAACpD,MAAM,KAAK,CAAC,EAAE,0CAA0C,EAAE,OAAO,CAAC;MAC/FoD,SAAS,CAAC9B,OAAO,CAAC+B,OAAO,IAAI;QAC3B,IAAIA,OAAO,EAAE;UACXN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAACG,OAAO,CAACC,IAAI,KAAKD,OAAO,CAACC,IAAI,CAACC,qBAAqB,KAAK,IAAI,IAAIF,OAAO,CAACC,IAAI,CAACE,0BAA0B,KAAK,IAAI,CAAC,EAAE,OAAO,EAAE,wEAAwE,CAAC,GAAG,KAAK,CAAC;QAChQ;MACF,CAAC,CAAC;IACJ;EACF;EACA,MAAMC,gBAAgB,GAAG5C,aAAa,CAACa,SAAS,EAAEU,UAAU,CAAC;EAC7D,IAAIU,WAAW,IAAIA,WAAW,CAAC9C,MAAM,GAAG,CAAC,EAAE;IACzC;IACA,MAAM0D,KAAK,GAAG,EAAE;IAChB,MAAMC,gBAAgB,GAAGzB,KAAK,IAAID,YAAY;IAC9CS,MAAM,GAAGI,WAAW,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MACxC,MAAM;QACJ7C,IAAI;QACJM,GAAG;QACH+B,IAAI;QACJS,IAAI;QACJC,OAAO;QACPC,OAAO;QACPnC,SAAS,EAAEoC,aAAa;QACxBtC,SAAS,EAAEuC,aAAa;QACxBC;MACF,CAAC,GAAGP,IAAI;MACR,MAAM1C,UAAU,GAAGJ,OAAO,CAACC,MAAM,EAAEC,IAAI,CAAC;MACxC,IAAIE,UAAU,KAAKD,SAAS,EAAE;QAC5BwC,KAAK,CAACW,IAAI,CAAClD,UAAU,CAAC;MACxB;MACA,MAAMmD,SAAS,GAAG/C,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAGuC,KAAK;MAC9D,IAAIR,IAAI,KAAK,WAAW,EAAE;QACxB,OAAO,aAAapD,KAAK,CAACqE,aAAa,CAAC5D,mBAAmB,EAAE;UAC3DY,GAAG,EAAE+C;QACP,CAAC,EAAEH,aAAa,CAAC;MACnB;MACA,MAAMK,SAAS,GAAG,CAAC,CAAC;MACpB,MAAMC,UAAU,GAAGX,KAAK,KAAKhB,WAAW,CAAC9C,MAAM,GAAG,CAAC;MACnD,IAAI+D,IAAI,EAAE;QACRS,SAAS,CAACT,IAAI,GAAGA,IAAI;MACvB,CAAC,MAAM,IAAIC,OAAO,EAAE;QAClBQ,SAAS,CAACR,OAAO,GAAGA,OAAO;MAC7B;MACA,IAAI;QACFU;MACF,CAAC,GAAGb,IAAI;MACR,IAAIH,KAAK,CAAC1D,MAAM,IAAImB,UAAU,KAAKD,SAAS,EAAE;QAC5CwD,IAAI,GAAG,KAAKhB,KAAK,CAACiB,IAAI,CAAC,GAAG,CAAC,EAAE;MAC/B;MACA,OAAO,aAAazE,KAAK,CAACqE,aAAa,CAAC7D,sBAAsB,EAAEjB,MAAM,CAACmF,MAAM,CAAC;QAC5ErD,GAAG,EAAE+C;MACP,CAAC,EAAEE,SAAS,EAAEnE,SAAS,CAACwD,IAAI,EAAE;QAC5BgB,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;MACR,CAAC,CAAC,EAAE;QACFhD,SAAS,EAAEoC,aAAa;QACxBE,aAAa,EAAEA,aAAa;QAC5BM,IAAI,EAAEA,IAAI;QACV9C,SAAS,EAAE6C,UAAU,GAAG,EAAE,GAAG7C,SAAS;QACtCqC,OAAO,EAAEA,OAAO;QAChBvC,SAAS,EAAEA;MACb,CAAC,CAAC,EAAE+B,gBAAgB,CAACI,IAAI,EAAE7C,MAAM,EAAE2C,gBAAgB,EAAED,KAAK,EAAEgB,IAAI,CAAC,CAAC;IACpE,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIvC,QAAQ,EAAE;IACnB,MAAM4C,cAAc,GAAG3E,OAAO,CAAC+B,QAAQ,CAAC,CAACnC,MAAM;IAC/C0C,MAAM,GAAGtC,OAAO,CAAC+B,QAAQ,CAAC,CAACyB,GAAG,CAAC,CAACP,OAAO,EAAES,KAAK,KAAK;MACjD,IAAI,CAACT,OAAO,EAAE;QACZ,OAAOA,OAAO;MAChB;MACA,MAAMoB,UAAU,GAAGX,KAAK,KAAKiB,cAAc,GAAG,CAAC;MAC/C,OAAOzE,YAAY,CAAC+C,OAAO,EAAE;QAC3BzB,SAAS,EAAE6C,UAAU,GAAG,EAAE,GAAG7C,SAAS;QACtC;QACAL,GAAG,EAAEuC;MACP,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,MAAMkB,mBAAmB,GAAG7E,UAAU,CAACuB,SAAS,EAAEc,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACV,SAAS,EAAE;IAC9H,CAAC,GAAGJ,SAAS,MAAM,GAAGa,SAAS,KAAK;EACtC,CAAC,EAAET,SAAS,EAAEC,aAAa,EAAEa,MAAM,EAAEC,SAAS,CAAC;EAC/C,MAAMoC,WAAW,GAAGxF,MAAM,CAACmF,MAAM,CAACnF,MAAM,CAACmF,MAAM,CAAC,CAAC,CAAC,EAAEpC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACX,KAAK,CAAC,EAAEA,KAAK,CAAC;EACrI,OAAOc,UAAU,CAAC,aAAazC,KAAK,CAACqE,aAAa,CAAC,KAAK,EAAE9E,MAAM,CAACmF,MAAM,CAAC;IACtE9C,SAAS,EAAEkD,mBAAmB;IAC9BnD,KAAK,EAAEoD;EACT,CAAC,EAAE5C,SAAS,CAAC,EAAE,aAAanC,KAAK,CAACqE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE7B,MAAM,CAAC,CAAC,CAAC;AACvE,CAAC;AACDlB,UAAU,CAAC0D,IAAI,GAAGzE,cAAc;AAChCe,UAAU,CAAC2D,SAAS,GAAGxE,mBAAmB;AAC1C,IAAIoC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCzB,UAAU,CAAC4D,WAAW,GAAG,YAAY;AACvC;AACA,eAAe5D,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
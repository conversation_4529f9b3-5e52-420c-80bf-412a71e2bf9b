{"ast": null, "code": "import * as React from 'react';\nimport rcWarning, { resetWarned as rcResetWarned } from \"rc-util/es/warning\";\nexport function noop() {}\nlet deprecatedWarnList = null;\nexport function resetWarned() {\n  deprecatedWarnList = null;\n  rcResetWarned();\n}\nlet warning = noop;\nif (process.env.NODE_ENV !== 'production') {\n  warning = (valid, component, message) => {\n    rcWarning(valid, \"[antd: \".concat(component, \"] \").concat(message));\n    // StrictMode will inject console which will not throw warning in React 17.\n    if (process.env.NODE_ENV === 'test') {\n      resetWarned();\n    }\n  };\n}\nexport const WarningContext = /*#__PURE__*/React.createContext({});\n/**\n * This is a hook but we not named as `useWarning`\n * since this is only used in development.\n * We should always wrap this in `if (process.env.NODE_ENV !== 'production')` condition\n */\nexport const devUseWarning = process.env.NODE_ENV !== 'production' ? component => {\n  const {\n    strict\n  } = React.useContext(WarningContext);\n  const typeWarning = (valid, type, message) => {\n    if (!valid) {\n      if (strict === false && type === 'deprecated') {\n        const existWarning = deprecatedWarnList;\n        if (!deprecatedWarnList) {\n          deprecatedWarnList = {};\n        }\n        deprecatedWarnList[component] = deprecatedWarnList[component] || [];\n        if (!deprecatedWarnList[component].includes(message || '')) {\n          deprecatedWarnList[component].push(message || '');\n        }\n        // Warning for the first time\n        if (!existWarning) {\n          console.warn('[antd] There exists deprecated usage in your code:', deprecatedWarnList);\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(valid, component, message) : void 0;\n      }\n    }\n  };\n  typeWarning.deprecated = (valid, oldProp, newProp, message) => {\n    typeWarning(valid, 'deprecated', \"`\".concat(oldProp, \"` is deprecated. Please use `\").concat(newProp, \"` instead.\").concat(message ? \" \".concat(message) : ''));\n  };\n  return typeWarning;\n} : () => {\n  const noopWarning = () => {};\n  noopWarning.deprecated = noop;\n  return noopWarning;\n};\nexport default warning;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
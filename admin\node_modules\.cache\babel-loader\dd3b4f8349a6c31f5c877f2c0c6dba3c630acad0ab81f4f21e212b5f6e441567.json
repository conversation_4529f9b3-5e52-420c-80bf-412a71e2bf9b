{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FireOutlinedSvg from \"@ant-design/icons-svg/es/asn/FireOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FireOutlined = function FireOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FireOutlinedSvg\n  }));\n};\n\n/**![fire](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzNC4xIDQ2OS4yQTM0Ny40OSAzNDcuNDkgMCAwMDc1MS4yIDM1NGwtMjkuMS0yNi43YTguMDkgOC4wOSAwIDAwLTEzIDMuM2wtMTMgMzcuM2MtOC4xIDIzLjQtMjMgNDcuMy00NC4xIDcwLjgtMS40IDEuNS0zIDEuOS00LjEgMi0xLjEuMS0yLjgtLjEtNC4zLTEuNS0xLjQtMS4yLTIuMS0zLTItNC44IDMuNy02MC4yLTE0LjMtMTI4LjEtNTMuNy0yMDJDNTU1LjMgMTcxIDUxMCAxMjMuMSA0NTMuNCA4OS43bC00MS4zLTI0LjNjLTUuNC0zLjItMTIuMyAxLTEyIDcuM2wyLjIgNDhjMS41IDMyLjgtMi4zIDYxLjgtMTEuMyA4NS45LTExIDI5LjUtMjYuOCA1Ni45LTQ3IDgxLjVhMjk1LjY0IDI5NS42NCAwIDAxLTQ3LjUgNDYuMSAzNTIuNiAzNTIuNiAwIDAwLTEwMC4zIDEyMS41QTM0Ny43NSAzNDcuNzUgMCAwMDE2MCA2MTBjMCA0Ny4yIDkuMyA5Mi45IDI3LjcgMTM2YTM0OS40IDM0OS40IDAgMDA3NS41IDExMC45YzMyLjQgMzIgNzAgNTcuMiAxMTEuOSA3NC43QzQxOC41IDk0OS44IDQ2NC41IDk1OSA1MTIgOTU5czkzLjUtOS4yIDEzNi45LTI3LjNBMzQ4LjYgMzQ4LjYgMCAwMDc2MC44IDg1N2MzMi40LTMyIDU3LjgtNjkuNCA3NS41LTExMC45YTM0NC4yIDM0NC4yIDAgMDAyNy43LTEzNmMwLTQ4LjgtMTAtOTYuMi0yOS45LTE0MC45ek03MTMgODA4LjVjLTUzLjcgNTMuMi0xMjUgODIuNC0yMDEgODIuNHMtMTQ3LjMtMjkuMi0yMDEtODIuNGMtNTMuNS01My4xLTgzLTEyMy41LTgzLTE5OC40IDAtNDMuNSA5LjgtODUuMiAyOS4xLTEyNCAxOC44LTM3LjkgNDYuOC03MS44IDgwLjgtOTcuOWEzNDkuNiAzNDkuNiAwIDAwNTguNi01Ni44YzI1LTMwLjUgNDQuNi02NC41IDU4LjItMTAxYTI0MCAyNDAgMCAwMDEyLjEtNDYuNWMyNC4xIDIyLjIgNDQuMyA0OSA2MS4yIDgwLjQgMzMuNCA2Mi42IDQ4LjggMTE4LjMgNDUuOCAxNjUuN2E3NC4wMSA3NC4wMSAwIDAwMjQuNCA1OS44IDczLjM2IDczLjM2IDAgMDA1My40IDE4LjhjMTkuNy0xIDM3LjgtOS43IDUxLTI0LjQgMTMuMy0xNC45IDI0LjgtMzAuMSAzNC40LTQ1LjYgMTQgMTcuOSAyNS43IDM3LjQgMzUgNTguNCAxNS45IDM1LjggMjQgNzMuOSAyNCAxMTMuMSAwIDc0LjktMjkuNSAxNDUuNC04MyAxOTguNHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FireOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FireOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FireOutlinedSvg", "AntdIcon", "FireOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/FireOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FireOutlinedSvg from \"@ant-design/icons-svg/es/asn/FireOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FireOutlined = function FireOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FireOutlinedSvg\n  }));\n};\n\n/**![fire](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzNC4xIDQ2OS4yQTM0Ny40OSAzNDcuNDkgMCAwMDc1MS4yIDM1NGwtMjkuMS0yNi43YTguMDkgOC4wOSAwIDAwLTEzIDMuM2wtMTMgMzcuM2MtOC4xIDIzLjQtMjMgNDcuMy00NC4xIDcwLjgtMS40IDEuNS0zIDEuOS00LjEgMi0xLjEuMS0yLjgtLjEtNC4zLTEuNS0xLjQtMS4yLTIuMS0zLTItNC44IDMuNy02MC4yLTE0LjMtMTI4LjEtNTMuNy0yMDJDNTU1LjMgMTcxIDUxMCAxMjMuMSA0NTMuNCA4OS43bC00MS4zLTI0LjNjLTUuNC0zLjItMTIuMyAxLTEyIDcuM2wyLjIgNDhjMS41IDMyLjgtMi4zIDYxLjgtMTEuMyA4NS45LTExIDI5LjUtMjYuOCA1Ni45LTQ3IDgxLjVhMjk1LjY0IDI5NS42NCAwIDAxLTQ3LjUgNDYuMSAzNTIuNiAzNTIuNiAwIDAwLTEwMC4zIDEyMS41QTM0Ny43NSAzNDcuNzUgMCAwMDE2MCA2MTBjMCA0Ny4yIDkuMyA5Mi45IDI3LjcgMTM2YTM0OS40IDM0OS40IDAgMDA3NS41IDExMC45YzMyLjQgMzIgNzAgNTcuMiAxMTEuOSA3NC43QzQxOC41IDk0OS44IDQ2NC41IDk1OSA1MTIgOTU5czkzLjUtOS4yIDEzNi45LTI3LjNBMzQ4LjYgMzQ4LjYgMCAwMDc2MC44IDg1N2MzMi40LTMyIDU3LjgtNjkuNCA3NS41LTExMC45YTM0NC4yIDM0NC4yIDAgMDAyNy43LTEzNmMwLTQ4LjgtMTAtOTYuMi0yOS45LTE0MC45ek03MTMgODA4LjVjLTUzLjcgNTMuMi0xMjUgODIuNC0yMDEgODIuNHMtMTQ3LjMtMjkuMi0yMDEtODIuNGMtNTMuNS01My4xLTgzLTEyMy41LTgzLTE5OC40IDAtNDMuNSA5LjgtODUuMiAyOS4xLTEyNCAxOC44LTM3LjkgNDYuOC03MS44IDgwLjgtOTcuOWEzNDkuNiAzNDkuNiAwIDAwNTguNi01Ni44YzI1LTMwLjUgNDQuNi02NC41IDU4LjItMTAxYTI0MCAyNDAgMCAwMDEyLjEtNDYuNWMyNC4xIDIyLjIgNDQuMyA0OSA2MS4yIDgwLjQgMzMuNCA2Mi42IDQ4LjggMTE4LjMgNDUuOCAxNjUuN2E3NC4wMSA3NC4wMSAwIDAwMjQuNCA1OS44IDczLjM2IDczLjM2IDAgMDA1My40IDE4LjhjMTkuNy0xIDM3LjgtOS43IDUxLTI0LjQgMTMuMy0xNC45IDI0LjgtMzAuMSAzNC40LTQ1LjYgMTQgMTcuOSAyNS43IDM3LjQgMzUgNTguNCAxNS45IDM1LjggMjQgNzMuOSAyNCAxMTMuMSAwIDc0LjktMjkuNSAxNDUuNC04MyAxOTguNHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FireOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FireOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC;AACzD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,cAAc;AACtC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
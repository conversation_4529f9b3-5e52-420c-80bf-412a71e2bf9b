# 🔧 API 404错误修复指南

## 🔍 **问题分析**

### **当前状态**
- **访问**: `https://h5.haokajiyun.com/api/` 
- **错误**: `404 Not Found`
- **原因**: Nginx配置路径处理问题

### **路径结构**
```
/www/wwwroot/h5.haokajiyun.com/
├── api/
│   ├── public/          # Laravel入口目录
│   │   ├── index.php    # Laravel入口文件
│   │   └── .htaccess
│   ├── app/
│   ├── config/
│   └── ...
└── admin/               # 前端管理后台
    ├── index.html
    └── static/
```

### **问题根源**
当前配置中：
```nginx
root /www/wwwroot/h5.haokajiyun.com/api/public;

location /api/ {
    try_files $uri $uri/ /index.php?$query_string;
}
```

访问 `/api/` 时，实际查找路径变成：
`/www/wwwroot/h5.haokajiyun.com/api/public/api/` ❌

## 🛠️ **解决方案**

### **方案一：推荐使用 (nginx-simple.conf)**

```nginx
server {
    listen 443 ssl;
    http2 on;
    server_name h5.haokajiyun.com;
    
    # 设置根目录为API的public目录
    root /www/wwwroot/h5.haokajiyun.com/api/public;
    index index.php index.html;
    
    # Admin后台 - 使用alias
    location /admin/ {
        alias /www/wwwroot/h5.haokajiyun.com/admin/;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
    }
    
    # API路由 - 重写URL去掉/api前缀
    location /api/ {
        rewrite ^/api/(.*)$ /$1 break;
        try_files $uri $uri/ /index.php?$query_string;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept" always;
    }
    
    # PHP处理
    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass unix:/tmp/php-cgi-83.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### **方案二：使用alias (nginx-fixed.conf)**

```nginx
location /api/ {
    alias /www/wwwroot/h5.haokajiyun.com/api/public/;
    try_files $uri $uri/ @api_fallback;
    
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-83.sock;
        fastcgi_param SCRIPT_FILENAME /www/wwwroot/h5.haokajiyun.com/api/public$fastcgi_script_name;
        include fastcgi_params;
    }
}

location @api_fallback {
    fastcgi_pass unix:/tmp/php-cgi-83.sock;
    fastcgi_param SCRIPT_FILENAME /www/wwwroot/h5.haokajiyun.com/api/public/index.php;
    include fastcgi_params;
}
```

## 🚀 **修复步骤**

### **第一步：备份当前配置**
```bash
# 在宝塔面板中备份当前配置
# 或使用命令行
cp /etc/nginx/sites-available/h5.haokajiyun.com /etc/nginx/sites-available/h5.haokajiyun.com.backup
```

### **第二步：应用新配置**

#### **使用宝塔面板**
1. 登录宝塔面板
2. 进入"网站" → 找到 `h5.haokajiyun.com`
3. 点击"设置" → "配置文件"
4. 替换为 `nginx-simple.conf` 中的配置
5. 点击"保存"

#### **使用命令行**
```bash
# 编辑配置文件
nano /etc/nginx/sites-available/h5.haokajiyun.com

# 测试配置
nginx -t

# 重载配置
nginx -s reload
```

### **第三步：验证修复**

#### **测试API访问**
```bash
# 测试API根路径
curl -I https://h5.haokajiyun.com/api/

# 测试具体API端点
curl https://h5.haokajiyun.com/api/v1/products

# 测试CORS
curl -H "Origin: https://h5.haokajiyun.com" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     https://h5.haokajiyun.com/api/v1/products
```

#### **浏览器测试**
1. 访问: `https://h5.haokajiyun.com/api/`
2. 应该看到Laravel欢迎页面或API响应
3. 不应该再显示404错误

## 🔍 **故障排除**

### **如果仍然404**

#### **检查文件是否存在**
```bash
# 检查Laravel入口文件
ls -la /www/wwwroot/h5.haokajiyun.com/api/public/index.php

# 检查权限
ls -la /www/wwwroot/h5.haokajiyun.com/api/public/
```

#### **检查PHP-FPM**
```bash
# 检查PHP-FPM状态
systemctl status php8.3-fpm

# 检查socket文件
ls -la /tmp/php-cgi-83.sock
```

#### **查看错误日志**
```bash
# 查看Nginx错误日志
tail -f /www/wwwlogs/h5.haokajiyun.com.error.log

# 查看Laravel日志
tail -f /www/wwwroot/h5.haokajiyun.com/api/storage/logs/laravel.log
```

### **如果PHP文件下载而不是执行**

检查PHP配置：
```bash
# 确认PHP-FPM socket路径
ps aux | grep php-fpm
ls -la /tmp/php-cgi-*.sock
```

可能需要调整fastcgi_pass路径：
```nginx
# 常见的PHP-FPM socket路径
fastcgi_pass unix:/tmp/php-cgi-83.sock;          # 宝塔面板
fastcgi_pass unix:/var/run/php/php8.3-fpm.sock; # 标准安装
fastcgi_pass 127.0.0.1:9000;                    # TCP连接
```

## ✅ **验证清单**

修复完成后，确认以下项目：

- [ ] `https://h5.haokajiyun.com/api/` 不再返回404
- [ ] `https://h5.haokajiyun.com/api/v1/products` 返回API数据
- [ ] `https://h5.haokajiyun.com/admin/` 前端正常访问
- [ ] CORS头正确设置
- [ ] 浏览器控制台无错误

## 📞 **技术支持**

### **推荐配置文件**
- **简化版**: `nginx-simple.conf` ✅ 推荐
- **完整版**: `nginx-fixed.conf`

### **关键检查点**
1. **root路径**: `/www/wwwroot/h5.haokajiyun.com/api/public`
2. **API重写**: `rewrite ^/api/(.*)$ /$1 break;`
3. **PHP处理**: `fastcgi_pass unix:/tmp/php-cgi-83.sock;`
4. **CORS配置**: 确保包含必要的头信息

### **常用命令**
```bash
# 测试Nginx配置
nginx -t

# 重载Nginx
nginx -s reload

# 查看进程
ps aux | grep nginx
ps aux | grep php-fpm

# 测试API
curl -v https://h5.haokajiyun.com/api/
```

---

## 🎯 **预期结果**

修复完成后：
- ✅ `https://h5.haokajiyun.com/api/` 正常访问
- ✅ `https://h5.haokajiyun.com/admin/` 正常访问  
- ✅ API接口正常工作
- ✅ 前端可以正常调用API

**🎉 API 404问题应该得到解决！**

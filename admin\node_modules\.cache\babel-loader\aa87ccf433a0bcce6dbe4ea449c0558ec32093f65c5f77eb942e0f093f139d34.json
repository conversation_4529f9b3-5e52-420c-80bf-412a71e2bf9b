{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nexport const DotDuration = '--dot-duration';\nconst genCarouselStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      '.slick-slider': {\n        position: 'relative',\n        display: 'block',\n        boxSizing: 'border-box',\n        touchAction: 'pan-y',\n        WebkitTouchCallout: 'none',\n        WebkitTapHighlightColor: 'transparent',\n        '.slick-track, .slick-list': {\n          transform: 'translate3d(0, 0, 0)',\n          touchAction: 'pan-y'\n        }\n      },\n      '.slick-list': {\n        position: 'relative',\n        display: 'block',\n        margin: 0,\n        padding: 0,\n        overflow: 'hidden',\n        '&:focus': {\n          outline: 'none'\n        },\n        '&.dragging': {\n          cursor: 'pointer'\n        },\n        '.slick-slide': {\n          pointerEvents: 'none',\n          // https://github.com/ant-design/ant-design/issues/23294\n          [\"input\".concat(antCls, \"-radio-input, input\").concat(antCls, \"-checkbox-input\")]: {\n            visibility: 'hidden'\n          },\n          '&.slick-active': {\n            pointerEvents: 'auto',\n            [\"input\".concat(antCls, \"-radio-input, input\").concat(antCls, \"-checkbox-input\")]: {\n              visibility: 'visible'\n            }\n          },\n          // fix Carousel content height not match parent node\n          // when children is empty node\n          // https://github.com/ant-design/ant-design/issues/25878\n          '> div > div': {\n            verticalAlign: 'bottom'\n          }\n        }\n      },\n      '.slick-track': {\n        position: 'relative',\n        top: 0,\n        insetInlineStart: 0,\n        display: 'block',\n        '&::before, &::after': {\n          display: 'table',\n          content: '\"\"'\n        },\n        '&::after': {\n          clear: 'both'\n        }\n      },\n      '.slick-slide': {\n        display: 'none',\n        float: 'left',\n        height: '100%',\n        minHeight: 1,\n        img: {\n          display: 'block'\n        },\n        '&.dragging img': {\n          pointerEvents: 'none'\n        }\n      },\n      '.slick-initialized .slick-slide': {\n        display: 'block'\n      },\n      '.slick-vertical .slick-slide': {\n        display: 'block',\n        height: 'auto'\n      }\n    })\n  };\n};\nconst genArrowsStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    arrowSize,\n    arrowOffset\n  } = token;\n  const arrowLength = token.calc(arrowSize).div(Math.SQRT2).equal();\n  return {\n    [componentCls]: {\n      // Arrows\n      '.slick-prev, .slick-next': {\n        position: 'absolute',\n        top: '50%',\n        width: arrowSize,\n        height: arrowSize,\n        transform: 'translateY(-50%)',\n        color: '#fff',\n        opacity: 0.4,\n        background: 'transparent',\n        padding: 0,\n        lineHeight: 0,\n        border: 0,\n        outline: 'none',\n        cursor: 'pointer',\n        zIndex: 1,\n        transition: \"opacity \".concat(motionDurationSlow),\n        '&:hover, &:focus': {\n          opacity: 1\n        },\n        '&.slick-disabled': {\n          pointerEvents: 'none',\n          opacity: 0\n        },\n        '&::after': {\n          boxSizing: 'border-box',\n          position: 'absolute',\n          top: token.calc(arrowSize).sub(arrowLength).div(2).equal(),\n          insetInlineStart: token.calc(arrowSize).sub(arrowLength).div(2).equal(),\n          display: 'inline-block',\n          width: arrowLength,\n          height: arrowLength,\n          border: \"0 solid currentcolor\",\n          borderInlineStartWidth: 2,\n          borderBlockStartWidth: 2,\n          borderRadius: 1,\n          content: '\"\"'\n        }\n      },\n      '.slick-prev': {\n        insetInlineStart: arrowOffset,\n        '&::after': {\n          transform: 'rotate(-45deg)'\n        }\n      },\n      '.slick-next': {\n        insetInlineEnd: arrowOffset,\n        '&::after': {\n          transform: 'rotate(135deg)'\n        }\n      }\n    }\n  };\n};\nconst genDotsStyle = token => {\n  const {\n    componentCls,\n    dotOffset,\n    dotWidth,\n    dotHeight,\n    dotGap,\n    colorBgContainer,\n    motionDurationSlow\n  } = token;\n  return {\n    [componentCls]: {\n      '.slick-dots': {\n        position: 'absolute',\n        insetInlineEnd: 0,\n        bottom: 0,\n        insetInlineStart: 0,\n        zIndex: 15,\n        display: 'flex !important',\n        justifyContent: 'center',\n        paddingInlineStart: 0,\n        margin: 0,\n        listStyle: 'none',\n        '&-bottom': {\n          bottom: dotOffset\n        },\n        '&-top': {\n          top: dotOffset,\n          bottom: 'auto'\n        },\n        li: {\n          position: 'relative',\n          display: 'inline-block',\n          flex: '0 1 auto',\n          boxSizing: 'content-box',\n          width: dotWidth,\n          height: dotHeight,\n          marginInline: dotGap,\n          padding: 0,\n          textAlign: 'center',\n          textIndent: -999,\n          verticalAlign: 'top',\n          transition: \"all \".concat(motionDurationSlow),\n          borderRadius: dotHeight,\n          overflow: 'hidden',\n          '&::after': {\n            display: 'block',\n            position: 'absolute',\n            top: 0,\n            insetInlineStart: 0,\n            width: '100%',\n            height: dotHeight,\n            content: '\"\"',\n            background: colorBgContainer,\n            borderRadius: dotHeight,\n            opacity: 1,\n            outline: 'none',\n            cursor: 'pointer',\n            overflow: 'hidden',\n            transform: 'translate3d(-100%, 0, 0)'\n          },\n          button: {\n            position: 'relative',\n            display: 'block',\n            width: '100%',\n            height: dotHeight,\n            padding: 0,\n            color: 'transparent',\n            fontSize: 0,\n            background: colorBgContainer,\n            border: 0,\n            borderRadius: dotHeight,\n            outline: 'none',\n            cursor: 'pointer',\n            opacity: 0.2,\n            transition: \"all \".concat(motionDurationSlow),\n            overflow: 'hidden',\n            '&:hover': {\n              opacity: 0.75\n            },\n            '&::after': {\n              position: 'absolute',\n              inset: token.calc(dotGap).mul(-1).equal(),\n              content: '\"\"'\n            }\n          },\n          '&.slick-active': {\n            width: token.dotActiveWidth,\n            position: 'relative',\n            '&:hover': {\n              opacity: 1\n            },\n            '&::after': {\n              transform: 'translate3d(0, 0, 0)',\n              transition: \"transform var(\".concat(DotDuration, \") ease-out\")\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genCarouselVerticalStyle = token => {\n  const {\n    componentCls,\n    dotOffset,\n    arrowOffset,\n    marginXXS\n  } = token;\n  const reverseSizeOfDot = {\n    width: token.dotHeight,\n    height: token.dotWidth\n  };\n  return {\n    [\"\".concat(componentCls, \"-vertical\")]: {\n      '.slick-prev, .slick-next': {\n        insetInlineStart: '50%',\n        marginBlockStart: 'unset',\n        transform: 'translateX(-50%)'\n      },\n      '.slick-prev': {\n        insetBlockStart: arrowOffset,\n        insetInlineStart: '50%',\n        '&::after': {\n          transform: 'rotate(45deg)'\n        }\n      },\n      '.slick-next': {\n        insetBlockStart: 'auto',\n        insetBlockEnd: arrowOffset,\n        '&::after': {\n          transform: 'rotate(-135deg)'\n        }\n      },\n      '.slick-dots': {\n        top: '50%',\n        bottom: 'auto',\n        flexDirection: 'column',\n        width: token.dotHeight,\n        height: 'auto',\n        margin: 0,\n        transform: 'translateY(-50%)',\n        '&-left': {\n          insetInlineEnd: 'auto',\n          insetInlineStart: dotOffset\n        },\n        '&-right': {\n          insetInlineEnd: dotOffset,\n          insetInlineStart: 'auto'\n        },\n        li: Object.assign(Object.assign({}, reverseSizeOfDot), {\n          margin: \"\".concat(unit(marginXXS), \" 0\"),\n          verticalAlign: 'baseline',\n          button: reverseSizeOfDot,\n          '&::after': Object.assign(Object.assign({}, reverseSizeOfDot), {\n            height: 0\n          }),\n          '&.slick-active': Object.assign(Object.assign({}, reverseSizeOfDot), {\n            button: reverseSizeOfDot,\n            '&::after': Object.assign(Object.assign({}, reverseSizeOfDot), {\n              transition: \"height var(\".concat(DotDuration, \") ease-out\")\n            })\n          })\n        })\n      }\n    }\n  };\n};\nconst genCarouselRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return [{\n    [\"\".concat(componentCls, \"-rtl\")]: {\n      direction: 'rtl',\n      // Dots\n      '.slick-dots': {\n        [\"\".concat(componentCls, \"-rtl&\")]: {\n          flexDirection: 'row-reverse'\n        }\n      }\n    }\n  }, {\n    [\"\".concat(componentCls, \"-vertical\")]: {\n      '.slick-dots': {\n        [\"\".concat(componentCls, \"-rtl&\")]: {\n          flexDirection: 'column'\n        }\n      }\n    }\n  }];\n};\nexport const prepareComponentToken = token => {\n  const dotActiveWidth = 24;\n  return {\n    arrowSize: 16,\n    arrowOffset: token.marginXS,\n    dotWidth: 16,\n    dotHeight: 3,\n    dotGap: token.marginXXS,\n    dotOffset: 12,\n    dotWidthActive: dotActiveWidth,\n    dotActiveWidth\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Carousel', token => [genCarouselStyle(token), genArrowsStyle(token), genDotsStyle(token), genCarouselVerticalStyle(token), genCarouselRtlStyle(token)], prepareComponentToken, {\n  deprecatedTokens: [['dotWidthActive', 'dotActiveWidth']]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
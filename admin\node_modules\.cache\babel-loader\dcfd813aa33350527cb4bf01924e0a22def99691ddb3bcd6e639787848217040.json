{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Tabs,Form,Input,Button,Typography,message,Spin}from'antd';import{SaveOutlined}from'@ant-design/icons';import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{TextArea}=Input;// API基础URL\nconst API_BASE_URL=process.env.REACT_APP_API_BASE_URL||'http://localhost:8000/api';const AppConfig=()=>{const[customerServiceForm]=Form.useForm();const[announcementForm]=Form.useForm();const[privacyForm]=Form.useForm();const[userAgreementForm]=Form.useForm();const[bannerForm]=Form.useForm();const[loading,setLoading]=useState(false);const[dataLoading,setDataLoading]=useState(true);// 加载配置数据\nconst loadConfigs=async()=>{setDataLoading(true);try{const response=await axios.get(\"\".concat(API_BASE_URL,\"/v1/app-configs\"));if(response.data.success){const{customer_service,announcement,privacy_policy,user_agreement,banner}=response.data.data;// 设置表单数据\ncustomerServiceForm.setFieldsValue(customer_service||{});announcementForm.setFieldsValue(announcement||{});privacyForm.setFieldsValue(privacy_policy||{});userAgreementForm.setFieldsValue(user_agreement||{});// 特殊处理轮播图数据\nconsole.log('轮播图原始数据:',banner);if(banner){const bannerFormData={banner1:{imageUrl:banner['banner1.imageUrl']||'',linkUrl:banner['banner1.linkUrl']||''},banner2:{imageUrl:banner['banner2.imageUrl']||'',linkUrl:banner['banner2.linkUrl']||''},banner3:{imageUrl:banner['banner3.imageUrl']||'',linkUrl:banner['banner3.linkUrl']||''}};console.log('轮播图表单数据:',bannerFormData);bannerForm.setFieldsValue(bannerFormData);}}else{message.error('加载配置失败：'+response.data.message);}}catch(error){console.error('加载配置错误:',error);message.error('加载配置失败，请检查网络连接');}finally{setDataLoading(false);}};// 组件挂载时加载数据\nuseEffect(()=>{loadConfigs();},[]);// 客服设置保存\nconst handleCustomerServiceSave=async values=>{setLoading(true);try{const response=await axios.put(\"\".concat(API_BASE_URL,\"/v1/app-configs/customer_service\"),values);if(response.data.success){message.success('客服设置保存成功');}else{message.error('保存失败：'+response.data.message);}}catch(error){console.error('保存客服设置错误:',error);message.error('保存失败，请重试');}finally{setLoading(false);}};// 公告弹出保存\nconst handleAnnouncementSave=async values=>{setLoading(true);try{const response=await axios.put(\"\".concat(API_BASE_URL,\"/v1/app-configs/announcement\"),values);if(response.data.success){message.success('公告设置保存成功');}else{message.error('保存失败：'+response.data.message);}}catch(error){console.error('保存公告设置错误:',error);message.error('保存失败，请重试');}finally{setLoading(false);}};// 隐私政策保存\nconst handlePrivacySave=async values=>{setLoading(true);try{const response=await axios.put(\"\".concat(API_BASE_URL,\"/v1/app-configs/privacy_policy\"),values);if(response.data.success){message.success('隐私政策保存成功');}else{message.error('保存失败：'+response.data.message);}}catch(error){console.error('保存隐私政策错误:',error);message.error('保存失败，请重试');}finally{setLoading(false);}};// 用户协议保存\nconst handleUserAgreementSave=async values=>{setLoading(true);try{const response=await axios.put(\"\".concat(API_BASE_URL,\"/v1/app-configs/user_agreement\"),values);if(response.data.success){message.success('用户协议保存成功');}else{message.error('保存失败：'+response.data.message);}}catch(error){console.error('保存用户协议错误:',error);message.error('保存失败，请重试');}finally{setLoading(false);}};// 轮播图保存\nconst handleBannerSave=async values=>{setLoading(true);try{var _values$banner,_values$banner2,_values$banner3,_values$banner4,_values$banner5,_values$banner6;console.log('保存轮播图数据:',values);// 转换数据格式以匹配后端期望的格式\nconst bannerData={'banner1.imageUrl':((_values$banner=values.banner1)===null||_values$banner===void 0?void 0:_values$banner.imageUrl)||'','banner1.linkUrl':((_values$banner2=values.banner1)===null||_values$banner2===void 0?void 0:_values$banner2.linkUrl)||'','banner2.imageUrl':((_values$banner3=values.banner2)===null||_values$banner3===void 0?void 0:_values$banner3.imageUrl)||'','banner2.linkUrl':((_values$banner4=values.banner2)===null||_values$banner4===void 0?void 0:_values$banner4.linkUrl)||'','banner3.imageUrl':((_values$banner5=values.banner3)===null||_values$banner5===void 0?void 0:_values$banner5.imageUrl)||'','banner3.linkUrl':((_values$banner6=values.banner3)===null||_values$banner6===void 0?void 0:_values$banner6.linkUrl)||''};console.log('转换后的轮播图数据:',bannerData);const response=await axios.put(\"\".concat(API_BASE_URL,\"/v1/app-configs/banner\"),bannerData);console.log('保存响应:',response.data);if(response.data.success){message.success('轮播图配置保存成功');// 重新加载数据以确保表单状态正确\nloadConfigs();}else{console.error('保存失败响应:',response.data);message.error('保存失败：'+(response.data.message||'未知错误'));if(response.data.errors){console.error('验证错误:',response.data.errors);}}}catch(error){var _error$response,_error$response2,_error$response2$data;console.error('保存轮播图配置错误:',error);console.error('错误详情:',(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.data);message.error('保存失败：'+(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||error.message||'请重试'));}finally{setLoading(false);}};// 客服设置选项卡\nconst CustomerServiceTab=()=>/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{color:'#666666',fontWeight:'bold',marginBottom:24,fontSize:'20px'},children:\"\\u5BA2\\u670D\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsxs(Form,{form:customerServiceForm,layout:\"vertical\",onFinish:handleCustomerServiceSave,style:{maxWidth:600},children:[/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u5FAE\\u4FE1\\u5BA2\\u670D\"}),name:\"wechatService\",rules:[{required:true,message:'请输入微信客服号'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5FAE\\u4FE1\\u5BA2\\u670D\\u53F7\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u5FAE\\u4FE1\\u5BA2\\u670D\\u4E8C\\u7EF4\\u7801\"}),name:\"wechatQrCode\",rules:[{required:true,message:'请输入微信客服二维码链接'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5FAE\\u4FE1\\u5BA2\\u670D\\u4E8C\\u7EF4\\u7801\\u94FE\\u63A5\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u5BA2\\u670D\\u7535\\u8BDD\"}),name:\"servicePhone\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BA2\\u670D\\u7535\\u8BDD\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u5BA2\\u670D\\u90AE\\u7BB1\"}),name:\"serviceEmail\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BA2\\u670D\\u90AE\\u7BB1\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u5DE5\\u4F5C\\u65F6\\u95F4\"}),name:\"workingHours\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982\\uFF1A\\u5468\\u4E00\\u81F3\\u5468\\u4E94 9:00-18:00\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{style:{marginTop:32},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(SaveOutlined,{}),size:\"large\",children:\"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"})})]})]});// 公告弹出选项卡\nconst AnnouncementTab=()=>/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{color:'#666666',fontWeight:'bold',marginBottom:24,fontSize:'20px'},children:\"\\u516C\\u544A\\u5F39\\u51FA\"}),/*#__PURE__*/_jsxs(Form,{form:announcementForm,layout:\"vertical\",onFinish:handleAnnouncementSave,style:{maxWidth:600},children:[/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u516C\\u544A\\u6807\\u9898\"}),name:\"title\",rules:[{required:true,message:'请输入公告标题'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u516C\\u544A\\u6807\\u9898\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u516C\\u544A\\u5185\\u5BB9\"}),name:\"content\",rules:[{required:true,message:'请输入公告内容'}],children:/*#__PURE__*/_jsx(TextArea,{rows:6,placeholder:\"\\u8BF7\\u8F93\\u5165\\u516C\\u544A\\u5185\\u5BB9\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u662F\\u5426\\u542F\\u7528\"}),name:\"enabled\",valuePropName:\"checked\",children:/*#__PURE__*/_jsx(Button,{type:\"primary\",ghost:true,children:\"\\u542F\\u7528\\u516C\\u544A\\u5F39\\u51FA\"})}),/*#__PURE__*/_jsx(Form.Item,{style:{marginTop:32},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(SaveOutlined,{}),size:\"large\",children:\"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"})})]})]});// 隐私政策选项卡\nconst PrivacyPolicyTab=()=>/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{color:'#666666',fontWeight:'bold',marginBottom:24,fontSize:'20px'},children:\"\\u9690\\u79C1\\u653F\\u7B56\"}),/*#__PURE__*/_jsxs(Form,{form:privacyForm,layout:\"vertical\",onFinish:handlePrivacySave,style:{maxWidth:800},children:[/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u9690\\u79C1\\u653F\\u7B56\\u6807\\u9898\"}),name:\"title\",rules:[{required:true,message:'请输入隐私政策标题'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u9690\\u79C1\\u653F\\u7B56\\u6807\\u9898\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u9690\\u79C1\\u653F\\u7B56\\u5185\\u5BB9\"}),name:\"content\",rules:[{required:true,message:'请输入隐私政策内容'}],children:/*#__PURE__*/_jsx(TextArea,{rows:12,placeholder:\"\\u8BF7\\u8F93\\u5165\\u9690\\u79C1\\u653F\\u7B56\\u5185\\u5BB9\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u751F\\u6548\\u65E5\\u671F\"}),name:\"effectiveDate\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982\\uFF1A2024\\u5E741\\u67081\\u65E5\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{style:{marginTop:32},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(SaveOutlined,{}),size:\"large\",children:\"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"})})]})]});// 用户协议选项卡\nconst UserAgreementTab=()=>/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{color:'#666666',fontWeight:'bold',marginBottom:24,fontSize:'20px'},children:\"\\u7528\\u6237\\u534F\\u8BAE\"}),/*#__PURE__*/_jsx(Text,{style:{color:'#999999',fontSize:'14px',display:'block',marginBottom:24},children:\"\\u914D\\u7F6E\\u5E94\\u7528\\u7684\\u7528\\u6237\\u670D\\u52A1\\u534F\\u8BAE\\u5185\\u5BB9\\uFF0C\\u7528\\u6237\\u5728\\u6CE8\\u518C\\u6216\\u4F7F\\u7528\\u670D\\u52A1\\u65F6\\u9700\\u8981\\u540C\\u610F\\u7684\\u6761\\u6B3E\\u3002\"}),/*#__PURE__*/_jsxs(Form,{form:userAgreementForm,layout:\"vertical\",onFinish:handleUserAgreementSave,style:{maxWidth:'800px'},children:[/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u534F\\u8BAE\\u6807\\u9898\"}),name:\"title\",rules:[{required:true,message:'请输入协议标题'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982\\uFF1A\\u7528\\u6237\\u670D\\u52A1\\u534F\\u8BAE\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u534F\\u8BAE\\u5185\\u5BB9\"}),name:\"content\",rules:[{required:true,message:'请输入协议内容'}],children:/*#__PURE__*/_jsx(TextArea,{rows:12,placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BE6\\u7EC6\\u7684\\u7528\\u6237\\u534F\\u8BAE\\u5185\\u5BB9...\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'12px',fontSize:'14px',lineHeight:'1.6'}})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u751F\\u6548\\u65E5\\u671F\"}),name:\"effectiveDate\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982\\uFF1A2024\\u5E741\\u67081\\u65E5\",style:{backgroundColor:'#ffffff',border:'1px solid #d9d9d9',borderRadius:'6px',padding:'8px 12px'}})}),/*#__PURE__*/_jsx(Form.Item,{style:{marginTop:32},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(SaveOutlined,{}),size:\"large\",children:\"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"})})]})]});// 轮播图选项卡\nconst BannerTab=()=>/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{color:'#666666',fontWeight:'bold',marginBottom:24,fontSize:'20px'},children:\"\\u8F6E\\u64AD\\u56FE\\u914D\\u7F6E\"}),/*#__PURE__*/_jsx(Text,{style:{color:'#999999',fontSize:'14px',display:'block',marginBottom:24},children:\"\\u914D\\u7F6E\\u5E94\\u7528\\u9996\\u9875\\u7684\\u8F6E\\u64AD\\u56FE\\u5185\\u5BB9\\uFF0C\\u652F\\u63013\\u5F20\\u8F6E\\u64AD\\u56FE\\u7247\\u548C\\u76F8\\u5173\\u4FE1\\u606F\\u3002\"}),/*#__PURE__*/_jsxs(Form,{form:bannerForm,layout:\"vertical\",onFinish:handleBannerSave,style:{maxWidth:'800px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:32,padding:16,border:'1px solid #f0f0f0',borderRadius:8},children:[/*#__PURE__*/_jsx(Title,{level:5,style:{color:'#666666',marginBottom:16},children:\"\\u8F6E\\u64AD\\u56FE 1\"}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u56FE\\u7247URL\"}),name:['banner1','imageUrl'],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u8DF3\\u8F6C\\u94FE\\u63A5\"}),name:['banner1','linkUrl'],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:32,padding:16,border:'1px solid #f0f0f0',borderRadius:8},children:[/*#__PURE__*/_jsx(Title,{level:5,style:{color:'#666666',marginBottom:16},children:\"\\u8F6E\\u64AD\\u56FE 2\"}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u56FE\\u7247URL\"}),name:['banner2','imageUrl'],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u8DF3\\u8F6C\\u94FE\\u63A5\"}),name:['banner2','linkUrl'],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:32,padding:16,border:'1px solid #f0f0f0',borderRadius:8},children:[/*#__PURE__*/_jsx(Title,{level:5,style:{color:'#666666',marginBottom:16},children:\"\\u8F6E\\u64AD\\u56FE 3\"}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u56FE\\u7247URL\"}),name:['banner3','imageUrl'],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"})}),/*#__PURE__*/_jsx(Form.Item,{label:/*#__PURE__*/_jsx(Text,{style:{color:'#666666',fontSize:'14px'},children:\"\\u8DF3\\u8F6C\\u94FE\\u63A5\"}),name:['banner3','linkUrl'],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"})})]}),/*#__PURE__*/_jsx(Form.Item,{style:{marginTop:32},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(SaveOutlined,{}),size:\"large\",children:\"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"})})]})]});// 选项卡配置\nconst tabItems=[{key:'customerService',label:'客服设置',children:/*#__PURE__*/_jsx(CustomerServiceTab,{})},{key:'announcement',label:'公告弹出',children:/*#__PURE__*/_jsx(AnnouncementTab,{})},{key:'privacy',label:'隐私政策',children:/*#__PURE__*/_jsx(PrivacyPolicyTab,{})},{key:'userAgreement',label:'用户协议',children:/*#__PURE__*/_jsx(UserAgreementTab,{})},{key:'banner',label:'轮播图',children:/*#__PURE__*/_jsx(BannerTab,{})}];if(dataLoading){return/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'50px'},children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'16px'},children:\"\\u52A0\\u8F7D\\u914D\\u7F6E\\u6570\\u636E\\u4E2D...\"})]});}return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Tabs,{defaultActiveKey:\"customerService\",items:tabItems,size:\"large\",style:{background:'#ffffff',borderRadius:'8px',padding:'16px'}})});};export default AppConfig;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
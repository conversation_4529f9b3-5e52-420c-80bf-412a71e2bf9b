{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { PresetColors } from '../theme/interface';\nconst inverseColors = PresetColors.map(color => \"\".concat(color, \"-inverse\"));\nexport const PresetStatusColorTypes = ['success', 'processing', 'error', 'default', 'warning'];\n/**\n * determine if the color keyword belongs to the `Ant Design` {@link PresetColors}.\n * @param color color to be judged\n * @param includeInverse whether to include reversed colors\n */\nexport function isPresetColor(color) {\n  let includeInverse = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (includeInverse) {\n    return [].concat(_toConsumableArray(inverseColors), _toConsumableArray(PresetColors)).includes(color);\n  }\n  return PresetColors.includes(color);\n}\nexport function isPresetStatusColor(color) {\n  return PresetStatusColorTypes.includes(color);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport initCollapseMotion from '../_util/motion';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { FormItemPrefixContext } from './context';\nimport useDebounce from './hooks/useDebounce';\nimport useStyle from './style';\nconst EMPTY_LIST = [];\nfunction toErrorEntity(error, prefix, errorStatus) {\n  let index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  return {\n    key: typeof error === 'string' ? error : \"\".concat(prefix, \"-\").concat(index),\n    error,\n    errorStatus\n  };\n}\nconst ErrorList = _ref => {\n  let {\n    help,\n    helpStatus,\n    errors = EMPTY_LIST,\n    warnings = EMPTY_LIST,\n    className: rootClassName,\n    fieldId,\n    onVisibleChanged\n  } = _ref;\n  const {\n    prefixCls\n  } = React.useContext(FormItemPrefixContext);\n  const baseClassName = \"\".concat(prefixCls, \"-item-explain\");\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const collapseMotion = React.useMemo(() => initCollapseMotion(prefixCls), [prefixCls]);\n  // We have to debounce here again since somewhere use ErrorList directly still need no shaking\n  // ref: https://github.com/ant-design/ant-design/issues/36336\n  const debounceErrors = useDebounce(errors);\n  const debounceWarnings = useDebounce(warnings);\n  const fullKeyList = React.useMemo(() => {\n    if (help !== undefined && help !== null) {\n      return [toErrorEntity(help, 'help', helpStatus)];\n    }\n    return [].concat(_toConsumableArray(debounceErrors.map((error, index) => toErrorEntity(error, 'error', 'error', index))), _toConsumableArray(debounceWarnings.map((warning, index) => toErrorEntity(warning, 'warning', 'warning', index))));\n  }, [help, helpStatus, debounceErrors, debounceWarnings]);\n  const filledKeyFullKeyList = React.useMemo(() => {\n    const keysCount = {};\n    fullKeyList.forEach(_ref2 => {\n      let {\n        key\n      } = _ref2;\n      keysCount[key] = (keysCount[key] || 0) + 1;\n    });\n    return fullKeyList.map((entity, index) => Object.assign(Object.assign({}, entity), {\n      key: keysCount[entity.key] > 1 ? \"\".concat(entity.key, \"-fallback-\").concat(index) : entity.key\n    }));\n  }, [fullKeyList]);\n  const helpProps = {};\n  if (fieldId) {\n    helpProps.id = \"\".concat(fieldId, \"_help\");\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    motionDeadline: collapseMotion.motionDeadline,\n    motionName: \"\".concat(prefixCls, \"-show-help\"),\n    visible: !!filledKeyFullKeyList.length,\n    onVisibleChanged: onVisibleChanged\n  }, holderProps => {\n    const {\n      className: holderClassName,\n      style: holderStyle\n    } = holderProps;\n    return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, helpProps, {\n      className: classNames(baseClassName, holderClassName, cssVarCls, rootCls, rootClassName, hashId),\n      style: holderStyle\n    }), /*#__PURE__*/React.createElement(CSSMotionList, Object.assign({\n      keys: filledKeyFullKeyList\n    }, initCollapseMotion(prefixCls), {\n      motionName: \"\".concat(prefixCls, \"-show-help-item\"),\n      component: false\n    }), itemProps => {\n      const {\n        key,\n        error,\n        errorStatus,\n        className: itemClassName,\n        style: itemStyle\n      } = itemProps;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: key,\n        className: classNames(itemClassName, {\n          [\"\".concat(baseClassName, \"-\").concat(errorStatus)]: errorStatus\n        }),\n        style: itemStyle\n      }, error);\n    }));\n  }));\n};\nexport default ErrorList;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "classNames", "CSSMotion", "CSSMotionList", "initCollapseMotion", "useCSSVarCls", "FormItemPrefixContext", "useDebounce", "useStyle", "EMPTY_LIST", "toErrorEntity", "error", "prefix", "errorStatus", "index", "arguments", "length", "undefined", "key", "concat", "ErrorList", "_ref", "help", "helpStatus", "errors", "warnings", "className", "rootClassName", "fieldId", "onVisibleChanged", "prefixCls", "useContext", "baseClassName", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "collapseMotion", "useMemo", "debounceErrors", "debounce<PERSON><PERSON><PERSON>s", "fullKeyList", "map", "warning", "filledKeyFullKeyList", "keysCount", "for<PERSON>ach", "_ref2", "entity", "Object", "assign", "helpProps", "id", "createElement", "motionDeadline", "motionName", "visible", "holderProps", "holderClassName", "style", "holder<PERSON>tyle", "keys", "component", "itemProps", "itemClassName", "itemStyle"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/form/ErrorList.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion, { CSSMotionList } from 'rc-motion';\nimport initCollapseMotion from '../_util/motion';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { FormItemPrefixContext } from './context';\nimport useDebounce from './hooks/useDebounce';\nimport useStyle from './style';\nconst EMPTY_LIST = [];\nfunction toErrorEntity(error, prefix, errorStatus, index = 0) {\n  return {\n    key: typeof error === 'string' ? error : `${prefix}-${index}`,\n    error,\n    errorStatus\n  };\n}\nconst ErrorList = ({\n  help,\n  helpStatus,\n  errors = EMPTY_LIST,\n  warnings = EMPTY_LIST,\n  className: rootClassName,\n  fieldId,\n  onVisibleChanged\n}) => {\n  const {\n    prefixCls\n  } = React.useContext(FormItemPrefixContext);\n  const baseClassName = `${prefixCls}-item-explain`;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const collapseMotion = React.useMemo(() => initCollapseMotion(prefixCls), [prefixCls]);\n  // We have to debounce here again since somewhere use ErrorList directly still need no shaking\n  // ref: https://github.com/ant-design/ant-design/issues/36336\n  const debounceErrors = useDebounce(errors);\n  const debounceWarnings = useDebounce(warnings);\n  const fullKeyList = React.useMemo(() => {\n    if (help !== undefined && help !== null) {\n      return [toErrorEntity(help, 'help', helpStatus)];\n    }\n    return [].concat(_toConsumableArray(debounceErrors.map((error, index) => toErrorEntity(error, 'error', 'error', index))), _toConsumableArray(debounceWarnings.map((warning, index) => toErrorEntity(warning, 'warning', 'warning', index))));\n  }, [help, helpStatus, debounceErrors, debounceWarnings]);\n  const filledKeyFullKeyList = React.useMemo(() => {\n    const keysCount = {};\n    fullKeyList.forEach(({\n      key\n    }) => {\n      keysCount[key] = (keysCount[key] || 0) + 1;\n    });\n    return fullKeyList.map((entity, index) => Object.assign(Object.assign({}, entity), {\n      key: keysCount[entity.key] > 1 ? `${entity.key}-fallback-${index}` : entity.key\n    }));\n  }, [fullKeyList]);\n  const helpProps = {};\n  if (fieldId) {\n    helpProps.id = `${fieldId}_help`;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(CSSMotion, {\n    motionDeadline: collapseMotion.motionDeadline,\n    motionName: `${prefixCls}-show-help`,\n    visible: !!filledKeyFullKeyList.length,\n    onVisibleChanged: onVisibleChanged\n  }, holderProps => {\n    const {\n      className: holderClassName,\n      style: holderStyle\n    } = holderProps;\n    return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, helpProps, {\n      className: classNames(baseClassName, holderClassName, cssVarCls, rootCls, rootClassName, hashId),\n      style: holderStyle\n    }), /*#__PURE__*/React.createElement(CSSMotionList, Object.assign({\n      keys: filledKeyFullKeyList\n    }, initCollapseMotion(prefixCls), {\n      motionName: `${prefixCls}-show-help-item`,\n      component: false\n    }), itemProps => {\n      const {\n        key,\n        error,\n        errorStatus,\n        className: itemClassName,\n        style: itemStyle\n      } = itemProps;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        key: key,\n        className: classNames(itemClassName, {\n          [`${baseClassName}-${errorStatus}`]: errorStatus\n        }),\n        style: itemStyle\n      }, error);\n    }));\n  }));\n};\nexport default ErrorList;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,IAAIC,aAAa,QAAQ,WAAW;AACpD,OAAOC,kBAAkB,MAAM,iBAAiB;AAChD,OAAOC,YAAY,MAAM,uCAAuC;AAChE,SAASC,qBAAqB,QAAQ,WAAW;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAa;EAAA,IAAXC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC1D,OAAO;IACLG,GAAG,EAAE,OAAOP,KAAK,KAAK,QAAQ,GAAGA,KAAK,MAAAQ,MAAA,CAAMP,MAAM,OAAAO,MAAA,CAAIL,KAAK,CAAE;IAC7DH,KAAK;IACLE;EACF,CAAC;AACH;AACA,MAAMO,SAAS,GAAGC,IAAA,IAQZ;EAAA,IARa;IACjBC,IAAI;IACJC,UAAU;IACVC,MAAM,GAAGf,UAAU;IACnBgB,QAAQ,GAAGhB,UAAU;IACrBiB,SAAS,EAAEC,aAAa;IACxBC,OAAO;IACPC;EACF,CAAC,GAAAR,IAAA;EACC,MAAM;IACJS;EACF,CAAC,GAAG9B,KAAK,CAAC+B,UAAU,CAACzB,qBAAqB,CAAC;EAC3C,MAAM0B,aAAa,MAAAb,MAAA,CAAMW,SAAS,kBAAe;EACjD,MAAMG,OAAO,GAAG5B,YAAY,CAACyB,SAAS,CAAC;EACvC,MAAM,CAACI,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAACsB,SAAS,EAAEG,OAAO,CAAC;EACpE,MAAMI,cAAc,GAAGrC,KAAK,CAACsC,OAAO,CAAC,MAAMlC,kBAAkB,CAAC0B,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACtF;EACA;EACA,MAAMS,cAAc,GAAGhC,WAAW,CAACiB,MAAM,CAAC;EAC1C,MAAMgB,gBAAgB,GAAGjC,WAAW,CAACkB,QAAQ,CAAC;EAC9C,MAAMgB,WAAW,GAAGzC,KAAK,CAACsC,OAAO,CAAC,MAAM;IACtC,IAAIhB,IAAI,KAAKL,SAAS,IAAIK,IAAI,KAAK,IAAI,EAAE;MACvC,OAAO,CAACZ,aAAa,CAACY,IAAI,EAAE,MAAM,EAAEC,UAAU,CAAC,CAAC;IAClD;IACA,OAAO,EAAE,CAACJ,MAAM,CAACpB,kBAAkB,CAACwC,cAAc,CAACG,GAAG,CAAC,CAAC/B,KAAK,EAAEG,KAAK,KAAKJ,aAAa,CAACC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAEG,KAAK,CAAC,CAAC,CAAC,EAAEf,kBAAkB,CAACyC,gBAAgB,CAACE,GAAG,CAAC,CAACC,OAAO,EAAE7B,KAAK,KAAKJ,aAAa,CAACiC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE7B,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9O,CAAC,EAAE,CAACQ,IAAI,EAAEC,UAAU,EAAEgB,cAAc,EAAEC,gBAAgB,CAAC,CAAC;EACxD,MAAMI,oBAAoB,GAAG5C,KAAK,CAACsC,OAAO,CAAC,MAAM;IAC/C,MAAMO,SAAS,GAAG,CAAC,CAAC;IACpBJ,WAAW,CAACK,OAAO,CAACC,KAAA,IAEd;MAAA,IAFe;QACnB7B;MACF,CAAC,GAAA6B,KAAA;MACCF,SAAS,CAAC3B,GAAG,CAAC,GAAG,CAAC2B,SAAS,CAAC3B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5C,CAAC,CAAC;IACF,OAAOuB,WAAW,CAACC,GAAG,CAAC,CAACM,MAAM,EAAElC,KAAK,KAAKmC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,EAAE;MACjF9B,GAAG,EAAE2B,SAAS,CAACG,MAAM,CAAC9B,GAAG,CAAC,GAAG,CAAC,MAAAC,MAAA,CAAM6B,MAAM,CAAC9B,GAAG,gBAAAC,MAAA,CAAaL,KAAK,IAAKkC,MAAM,CAAC9B;IAC9E,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACuB,WAAW,CAAC,CAAC;EACjB,MAAMU,SAAS,GAAG,CAAC,CAAC;EACpB,IAAIvB,OAAO,EAAE;IACXuB,SAAS,CAACC,EAAE,MAAAjC,MAAA,CAAMS,OAAO,UAAO;EAClC;EACA,OAAOM,UAAU,CAAC,aAAalC,KAAK,CAACqD,aAAa,CAACnD,SAAS,EAAE;IAC5DoD,cAAc,EAAEjB,cAAc,CAACiB,cAAc;IAC7CC,UAAU,KAAApC,MAAA,CAAKW,SAAS,eAAY;IACpC0B,OAAO,EAAE,CAAC,CAACZ,oBAAoB,CAAC5B,MAAM;IACtCa,gBAAgB,EAAEA;EACpB,CAAC,EAAE4B,WAAW,IAAI;IAChB,MAAM;MACJ/B,SAAS,EAAEgC,eAAe;MAC1BC,KAAK,EAAEC;IACT,CAAC,GAAGH,WAAW;IACf,OAAO,aAAazD,KAAK,CAACqD,aAAa,CAAC,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,EAAE;MAC1EzB,SAAS,EAAEzB,UAAU,CAAC+B,aAAa,EAAE0B,eAAe,EAAEtB,SAAS,EAAEH,OAAO,EAAEN,aAAa,EAAEQ,MAAM,CAAC;MAChGwB,KAAK,EAAEC;IACT,CAAC,CAAC,EAAE,aAAa5D,KAAK,CAACqD,aAAa,CAAClD,aAAa,EAAE8C,MAAM,CAACC,MAAM,CAAC;MAChEW,IAAI,EAAEjB;IACR,CAAC,EAAExC,kBAAkB,CAAC0B,SAAS,CAAC,EAAE;MAChCyB,UAAU,KAAApC,MAAA,CAAKW,SAAS,oBAAiB;MACzCgC,SAAS,EAAE;IACb,CAAC,CAAC,EAAEC,SAAS,IAAI;MACf,MAAM;QACJ7C,GAAG;QACHP,KAAK;QACLE,WAAW;QACXa,SAAS,EAAEsC,aAAa;QACxBL,KAAK,EAAEM;MACT,CAAC,GAAGF,SAAS;MACb,OAAO,aAAa/D,KAAK,CAACqD,aAAa,CAAC,KAAK,EAAE;QAC7CnC,GAAG,EAAEA,GAAG;QACRQ,SAAS,EAAEzB,UAAU,CAAC+D,aAAa,EAAE;UACnC,IAAA7C,MAAA,CAAIa,aAAa,OAAAb,MAAA,CAAIN,WAAW,IAAKA;QACvC,CAAC,CAAC;QACF8C,KAAK,EAAEM;MACT,CAAC,EAAEtD,KAAK,CAAC;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeS,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
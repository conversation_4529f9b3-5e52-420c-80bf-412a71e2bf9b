{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nconst viewSize = 100;\nconst borderWidth = viewSize / 5;\nconst radius = viewSize / 2 - borderWidth / 2;\nconst circumference = radius * 2 * Math.PI;\nconst position = 50;\nconst CustomCircle = props => {\n  const {\n    dotClassName,\n    style,\n    hasCircleCls\n  } = props;\n  return /*#__PURE__*/React.createElement(\"circle\", {\n    className: classNames(\"\".concat(dotClassName, \"-circle\"), {\n      [\"\".concat(dotClassName, \"-circle-bg\")]: hasCircleCls\n    }),\n    r: radius,\n    cx: position,\n    cy: position,\n    strokeWidth: borderWidth,\n    style: style\n  });\n};\nconst Progress = _ref => {\n  let {\n    percent,\n    prefixCls\n  } = _ref;\n  const dotClassName = \"\".concat(prefixCls, \"-dot\");\n  const holderClassName = \"\".concat(dotClassName, \"-holder\");\n  const hideClassName = \"\".concat(holderClassName, \"-hidden\");\n  const [render, setRender] = React.useState(false);\n  // ==================== Visible =====================\n  useLayoutEffect(() => {\n    if (percent !== 0) {\n      setRender(true);\n    }\n  }, [percent !== 0]);\n  // ==================== Progress ====================\n  const safePtg = Math.max(Math.min(percent, 100), 0);\n  // ===================== Render =====================\n  if (!render) {\n    return null;\n  }\n  const circleStyle = {\n    strokeDashoffset: \"\".concat(circumference / 4),\n    strokeDasharray: \"\".concat(circumference * safePtg / 100, \" \").concat(circumference * (100 - safePtg) / 100)\n  };\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(holderClassName, \"\".concat(dotClassName, \"-progress\"), safePtg <= 0 && hideClassName)\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: \"0 0 \".concat(viewSize, \" \").concat(viewSize),\n    // biome-ignore lint/a11y/noNoninteractiveElementToInteractiveRole: progressbar could be readonly\n    role: \"progressbar\",\n    \"aria-valuemin\": 0,\n    \"aria-valuemax\": 100,\n    \"aria-valuenow\": safePtg\n  }, /*#__PURE__*/React.createElement(CustomCircle, {\n    dotClassName: dotClassName,\n    hasCircleCls: true\n  }), /*#__PURE__*/React.createElement(CustomCircle, {\n    dotClassName: dotClassName,\n    style: circleStyle\n  })));\n};\nexport default Progress;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
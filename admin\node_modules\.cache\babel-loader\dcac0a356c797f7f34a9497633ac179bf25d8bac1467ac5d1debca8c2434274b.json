{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport DownCircleFilledSvg from \"@ant-design/icons-svg/es/asn/DownCircleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar DownCircleFilled = function DownCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: DownCircleFilledSvg\n  }));\n};\n\n/**![down-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xODQuNSAzNTMuN2wtMTc4IDI0NmE3Ljk1IDcuOTUgMCAwMS0xMi45IDBsLTE3OC0yNDZjLTMuOC01LjMgMC0xMi43IDYuNS0xMi43SDM4MWMxMC4yIDAgMTkuOSA0LjkgMjUuOSAxMy4yTDUxMiA1NjMuNmwxMDUuMi0xNDUuNGM2LTguMyAxNS42LTEzLjIgMjUuOS0xMy4ySDY5MGM2LjUgMCAxMC4zIDcuNCA2LjUgMTIuN3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(DownCircleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'DownCircleFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
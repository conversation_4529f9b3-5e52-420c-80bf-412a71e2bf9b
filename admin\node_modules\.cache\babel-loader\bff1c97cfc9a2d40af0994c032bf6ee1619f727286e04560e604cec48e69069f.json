{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\ProductEdit.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Form, message, Typography, Button, Space, Spin } from 'antd';\nimport { ArrowLeftOutlined } from '@ant-design/icons';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport ProductForm from '../components/ProductForm';\nimport { productApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst ProductEdit = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [fetchLoading, setFetchLoading] = useState(true);\n  const [product, setProduct] = useState(null);\n  const navigate = useNavigate();\n  const {\n    id\n  } = useParams();\n\n  // 获取产品详情\n  const fetchProduct = useCallback(async () => {\n    if (!id) {\n      message.error('产品ID不存在');\n      navigate('/products/list');\n      return;\n    }\n    setFetchLoading(true);\n    try {\n      const response = await productApi.getDetail(parseInt(id));\n      setProduct(response.data);\n    } catch (error) {\n      console.error('获取产品详情失败:', error);\n      message.error('获取产品详情失败');\n      navigate('/products/list');\n    } finally {\n      setFetchLoading(false);\n    }\n  }, [id, navigate]);\n\n  // 处理表单提交\n  const handleSubmit = async values => {\n    if (!id) return;\n    setLoading(true);\n    try {\n      const response = await productApi.update(parseInt(id), values);\n      message.success('产品更新成功！');\n\n      // 跳转到产品详情页\n      navigate(`/products/detail/${response.data.id}`);\n    } catch (error) {\n      console.error('更新产品失败:', error);\n      message.error('更新产品失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchProduct();\n  }, [fetchProduct]);\n  if (fetchLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u52A0\\u8F7D\\u4EA7\\u54C1\\u4FE1\\u606F\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this);\n  }\n  if (!product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u4EA7\\u54C1\\u4E0D\\u5B58\\u5728\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        style: {\n          marginTop: 16\n        },\n        onClick: () => navigate('/products/list'),\n        children: \"\\u8FD4\\u56DE\\u4EA7\\u54C1\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate('/products/list'),\n          children: \"\\u8FD4\\u56DE\\u4EA7\\u54C1\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: '16px 0'\n        },\n        children: [\"\\u7F16\\u8F91\\u4EA7\\u54C1 - \", product.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProductForm, {\n      form: form,\n      initialValues: product,\n      onSubmit: handleSubmit,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductEdit, \"ME2gYvZ1QcGnJMVsMNVp4c1uHcI=\", false, function () {\n  return [Form.useForm, useNavigate, useParams];\n});\n_c = ProductEdit;\nexport default ProductEdit;\nvar _c;\n$RefreshReg$(_c, \"ProductEdit\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Form", "message", "Typography", "<PERSON><PERSON>", "Space", "Spin", "ArrowLeftOutlined", "useNavigate", "useParams", "ProductForm", "productApi", "jsxDEV", "_jsxDEV", "Title", "ProductEdit", "_s", "form", "useForm", "loading", "setLoading", "fetchLoading", "setFetchLoading", "product", "setProduct", "navigate", "id", "fetchProduct", "error", "response", "getDetail", "parseInt", "data", "console", "handleSubmit", "values", "update", "success", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "type", "onClick", "marginBottom", "icon", "level", "margin", "name", "initialValues", "onSubmit", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/ProductEdit.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Form, message, Typography, Button, Space, Spin } from 'antd';\nimport { ArrowLeftOutlined } from '@ant-design/icons';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport ProductForm from '../components/ProductForm';\nimport { productApi } from '../services/api';\nimport type { Product, ProductRequest } from '../types/product';\n\nconst { Title } = Typography;\n\nconst ProductEdit: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [fetchLoading, setFetchLoading] = useState(true);\n  const [product, setProduct] = useState<Product | null>(null);\n  const navigate = useNavigate();\n  const { id } = useParams<{ id: string }>();\n\n  // 获取产品详情\n  const fetchProduct = useCallback(async () => {\n    if (!id) {\n      message.error('产品ID不存在');\n      navigate('/products/list');\n      return;\n    }\n\n    setFetchLoading(true);\n    try {\n      const response = await productApi.getDetail(parseInt(id));\n      setProduct(response.data);\n    } catch (error) {\n      console.error('获取产品详情失败:', error);\n      message.error('获取产品详情失败');\n      navigate('/products/list');\n    } finally {\n      setFetchLoading(false);\n    }\n  }, [id, navigate]);\n\n  // 处理表单提交\n  const handleSubmit = async (values: ProductRequest) => {\n    if (!id) return;\n\n    setLoading(true);\n    try {\n      const response = await productApi.update(parseInt(id), values);\n      message.success('产品更新成功！');\n      \n      // 跳转到产品详情页\n      navigate(`/products/detail/${response.data.id}`);\n    } catch (error) {\n      console.error('更新产品失败:', error);\n      message.error('更新产品失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchProduct();\n  }, [fetchProduct]);\n\n  if (fetchLoading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>加载产品信息中...</div>\n      </div>\n    );\n  }\n\n  if (!product) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <div>产品不存在</div>\n        <Button\n          type=\"primary\"\n          style={{ marginTop: 16 }}\n          onClick={() => navigate('/products/list')}\n        >\n          返回产品列表\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* 页面头部 */}\n      <div style={{ marginBottom: 24 }}>\n        <Space>\n          <Button\n            type=\"text\"\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate('/products/list')}\n          >\n            返回产品列表\n          </Button>\n        </Space>\n        <Title level={2} style={{ margin: '16px 0' }}>\n          编辑产品 - {product.name}\n        </Title>\n      </div>\n\n      {/* 产品表单 */}\n      <ProductForm\n        form={form}\n        initialValues={product}\n        onSubmit={handleSubmit}\n        loading={loading}\n      />\n    </div>\n  );\n};\n\nexport default ProductEdit;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AACrE,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,UAAU,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG7C,MAAM;EAAEC;AAAM,CAAC,GAAGX,UAAU;AAE5B,MAAMY,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,IAAI,CAAC,GAAGhB,IAAI,CAACiB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM2B,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAG,CAAC,GAAGjB,SAAS,CAAiB,CAAC;;EAE1C;EACA,MAAMkB,YAAY,GAAG3B,WAAW,CAAC,YAAY;IAC3C,IAAI,CAAC0B,EAAE,EAAE;MACPxB,OAAO,CAAC0B,KAAK,CAAC,SAAS,CAAC;MACxBH,QAAQ,CAAC,gBAAgB,CAAC;MAC1B;IACF;IAEAH,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMlB,UAAU,CAACmB,SAAS,CAACC,QAAQ,CAACL,EAAE,CAAC,CAAC;MACzDF,UAAU,CAACK,QAAQ,CAACG,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,CAAC;MACzBH,QAAQ,CAAC,gBAAgB,CAAC;IAC5B,CAAC,SAAS;MACRH,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACI,EAAE,EAAED,QAAQ,CAAC,CAAC;;EAElB;EACA,MAAMS,YAAY,GAAG,MAAOC,MAAsB,IAAK;IACrD,IAAI,CAACT,EAAE,EAAE;IAETN,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMlB,UAAU,CAACyB,MAAM,CAACL,QAAQ,CAACL,EAAE,CAAC,EAAES,MAAM,CAAC;MAC9DjC,OAAO,CAACmC,OAAO,CAAC,SAAS,CAAC;;MAE1B;MACAZ,QAAQ,CAAC,oBAAoBI,QAAQ,CAACG,IAAI,CAACN,EAAE,EAAE,CAAC;IAClD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B1B,OAAO,CAAC0B,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACd4B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,IAAIN,YAAY,EAAE;IAChB,oBACER,OAAA;MAAKyB,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrD5B,OAAA,CAACP,IAAI;QAACoC,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBjC,OAAA;QAAKyB,KAAK,EAAE;UAAES,SAAS,EAAE;QAAG,CAAE;QAAAN,QAAA,EAAC;MAAU;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAEV;EAEA,IAAI,CAACvB,OAAO,EAAE;IACZ,oBACEV,OAAA;MAAKyB,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrD5B,OAAA;QAAA4B,QAAA,EAAK;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChBjC,OAAA,CAACT,MAAM;QACL4C,IAAI,EAAC,SAAS;QACdV,KAAK,EAAE;UAAES,SAAS,EAAE;QAAG,CAAE;QACzBE,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC,gBAAgB,CAAE;QAAAgB,QAAA,EAC3C;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjC,OAAA;IAAA4B,QAAA,gBAEE5B,OAAA;MAAKyB,KAAK,EAAE;QAAEY,YAAY,EAAE;MAAG,CAAE;MAAAT,QAAA,gBAC/B5B,OAAA,CAACR,KAAK;QAAAoC,QAAA,eACJ5B,OAAA,CAACT,MAAM;UACL4C,IAAI,EAAC,MAAM;UACXG,IAAI,eAAEtC,OAAA,CAACN,iBAAiB;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BG,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC,gBAAgB,CAAE;UAAAgB,QAAA,EAC3C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRjC,OAAA,CAACC,KAAK;QAACsC,KAAK,EAAE,CAAE;QAACd,KAAK,EAAE;UAAEe,MAAM,EAAE;QAAS,CAAE;QAAAZ,QAAA,GAAC,6BACrC,EAAClB,OAAO,CAAC+B,IAAI;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjC,OAAA,CAACH,WAAW;MACVO,IAAI,EAAEA,IAAK;MACXsC,aAAa,EAAEhC,OAAQ;MACvBiC,QAAQ,EAAEtB,YAAa;MACvBf,OAAO,EAAEA;IAAQ;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAvGID,WAAqB;EAAA,QACVd,IAAI,CAACiB,OAAO,EAIVV,WAAW,EACbC,SAAS;AAAA;AAAAgD,EAAA,GANpB1C,WAAqB;AAyG3B,eAAeA,WAAW;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
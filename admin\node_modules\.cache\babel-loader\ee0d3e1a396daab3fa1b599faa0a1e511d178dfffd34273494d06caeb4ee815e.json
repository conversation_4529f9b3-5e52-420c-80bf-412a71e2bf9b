{"ast": null, "code": "export const groupKeysMap = keys => {\n  const map = new Map();\n  keys.forEach((key, index) => {\n    map.set(key, index);\n  });\n  return map;\n};\nexport const groupDisabledKeysMap = dataSource => {\n  const map = new Map();\n  dataSource.forEach((_ref, index) => {\n    let {\n      disabled,\n      key\n    } = _ref;\n    if (disabled) {\n      map.set(key, index);\n    }\n  });\n  return map;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
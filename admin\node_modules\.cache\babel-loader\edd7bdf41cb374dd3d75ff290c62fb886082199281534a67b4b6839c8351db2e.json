{"ast": null, "code": "import * as React from 'react';\nconst fullClone = Object.assign({}, React);\nconst {\n  useId\n} = fullClone;\nconst useEmptyId = () => '';\nconst useThemeKey = typeof useId === 'undefined' ? useEmptyId : useId;\nexport default useThemeKey;", "map": {"version": 3, "names": ["React", "fullClone", "Object", "assign", "useId", "useEmptyId", "useThemeKey"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/config-provider/hooks/useThemeKey.js"], "sourcesContent": ["import * as React from 'react';\nconst fullClone = Object.assign({}, React);\nconst {\n  useId\n} = fullClone;\nconst useEmptyId = () => '';\nconst useThemeKey = typeof useId === 'undefined' ? useEmptyId : useId;\nexport default useThemeKey;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC;AAC1C,MAAM;EACJI;AACF,CAAC,GAAGH,SAAS;AACb,MAAMI,UAAU,GAAGA,CAAA,KAAM,EAAE;AAC3B,MAAMC,WAAW,GAAG,OAAOF,KAAK,KAAK,WAAW,GAAGC,UAAU,GAAGD,KAAK;AACrE,eAAeE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
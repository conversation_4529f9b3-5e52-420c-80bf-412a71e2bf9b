{"ast": null, "code": "import axios from 'axios';\nimport { message } from 'antd';\n// 创建 axios 实例\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE_URL || '/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 可以在这里添加 token 等认证信息\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  const {\n    data\n  } = response;\n\n  // 检查业务状态码\n  if (data.code !== 200 && data.code !== 201) {\n    message.error(data.message || '请求失败');\n    return Promise.reject(new Error(data.message || '请求失败'));\n  }\n  return response;\n}, error => {\n  var _data$data;\n  console.error('API Error:', error);\n  if (error.response) {\n    const {\n      status,\n      data\n    } = error.response;\n    switch (status) {\n      case 400:\n        message.error('请求参数错误');\n        break;\n      case 401:\n        message.error('未授权，请重新登录');\n        break;\n      case 403:\n        message.error('拒绝访问');\n        break;\n      case 404:\n        message.error('请求的资源不存在');\n        break;\n      case 422:\n        if (data !== null && data !== void 0 && (_data$data = data.data) !== null && _data$data !== void 0 && _data$data.errors) {\n          const errors = Object.values(data.data.errors).flat();\n          message.error(errors.join(', '));\n        } else {\n          message.error((data === null || data === void 0 ? void 0 : data.message) || '数据验证失败');\n        }\n        break;\n      case 500:\n        message.error('服务器内部错误');\n        break;\n      default:\n        message.error((data === null || data === void 0 ? void 0 : data.message) || '请求失败');\n    }\n  } else if (error.request) {\n    message.error('网络错误，请检查网络连接');\n  } else {\n    message.error('请求配置错误');\n  }\n  return Promise.reject(error);\n});\n\n// 产品 API\nexport const productApi = {\n  // 获取产品列表\n  getList: params => {\n    return api.get('/v1/products', {\n      params\n    }).then(res => res.data);\n  },\n  // 获取产品详情\n  getDetail: id => {\n    return api.get(`/v1/products/${id}`).then(res => res.data);\n  },\n  // 创建产品\n  create: data => {\n    return api.post('/v1/products', data).then(res => res.data);\n  },\n  // 更新产品\n  update: (id, data) => {\n    return api.put(`/v1/products/${id}`, data).then(res => res.data);\n  },\n  // 删除产品\n  delete: id => {\n    return api.delete(`/v1/products/${id}`).then(res => res.data);\n  },\n  // 更新产品状态\n  updateStatus: (id, status) => {\n    return api.patch(`/v1/products/${id}/status`, {\n      status\n    }).then(res => res.data);\n  },\n  // 批量下架\n  batchOffline: data => {\n    return api.post('/v1/products/batch/offline', data).then(res => res.data);\n  },\n  // 批量删除\n  batchDelete: data => {\n    return api.delete('/v1/products/batch/delete', {\n      data\n    }).then(res => res.data);\n  },\n  // 获取统计数据\n  getStats: () => {\n    return api.get('/v1/products/stats').then(res => res.data);\n  },\n  // 复制产品\n  duplicate: id => {\n    return api.post(`/v1/products/${id}/duplicate`).then(res => res.data);\n  },\n  // 批量上架\n  batchOnline: data => {\n    return api.post('/v1/products/batch/online', data).then(res => res.data);\n  },\n  // 导出数据\n  export: params => {\n    return api.get('/v1/products/export', {\n      params,\n      responseType: 'blob'\n    }).then(res => res.data);\n  },\n  // 批量导入\n  import: file => {\n    const formData = new FormData();\n    formData.append('file', file);\n    return api.post('/v1/products/import', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    }).then(res => res.data);\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "message", "api", "create", "baseURL", "process", "env", "REACT_APP_API_BASE_URL", "timeout", "headers", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "data", "code", "Error", "_data$data", "console", "status", "errors", "Object", "values", "flat", "join", "productApi", "getList", "params", "get", "then", "res", "getDetail", "id", "post", "update", "put", "delete", "updateStatus", "patch", "batchOffline", "batchDelete", "getStats", "duplicate", "batchOnline", "export", "responseType", "import", "file", "formData", "FormData", "append"], "sources": ["G:/phpstudy_pro/WWW/admin/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\nimport { message } from 'antd';\nimport type {\n  Product,\n  ProductRequest,\n  ProductListParams,\n  ProductListResponse,\n  ApiResponse,\n  BatchOperationRequest,\n} from '../types/product';\nimport type {\n  Order,\n  OrderRequest,\n  OrderListParams,\n  OrderListResponse,\n  OrderStatusUpdateRequest,\n  OrderStats,\n  OrderBatchOperationRequest,\n  OrderDetailResponse,\n} from '../types/order';\n\n// 创建 axios 实例\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE_URL || '/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config: any) => {\n    // 可以在这里添加 token 等认证信息\n    return config;\n  },\n  (error: any) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response: AxiosResponse<ApiResponse>) => {\n    const { data } = response;\n    \n    // 检查业务状态码\n    if (data.code !== 200 && data.code !== 201) {\n      message.error(data.message || '请求失败');\n      return Promise.reject(new Error(data.message || '请求失败'));\n    }\n    \n    return response;\n  },\n  (error: any) => {\n    console.error('API Error:', error);\n\n    if (error.response) {\n      const { status, data } = error.response;\n\n      switch (status) {\n        case 400:\n          message.error('请求参数错误');\n          break;\n        case 401:\n          message.error('未授权，请重新登录');\n          break;\n        case 403:\n          message.error('拒绝访问');\n          break;\n        case 404:\n          message.error('请求的资源不存在');\n          break;\n        case 422:\n          if (data?.data?.errors) {\n            const errors = Object.values(data.data.errors).flat() as string[];\n            message.error(errors.join(', '));\n          } else {\n            message.error(data?.message || '数据验证失败');\n          }\n          break;\n        case 500:\n          message.error('服务器内部错误');\n          break;\n        default:\n          message.error(data?.message || '请求失败');\n      }\n    } else if (error.request) {\n      message.error('网络错误，请检查网络连接');\n    } else {\n      message.error('请求配置错误');\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// 产品 API\nexport const productApi = {\n  // 获取产品列表\n  getList: (params?: ProductListParams): Promise<ApiResponse<ProductListResponse>> => {\n    return api.get('/v1/products', { params }).then((res: any) => res.data);\n  },\n\n  // 获取产品详情\n  getDetail: (id: number): Promise<ApiResponse<Product>> => {\n    return api.get(`/v1/products/${id}`).then((res: any) => res.data);\n  },\n\n  // 创建产品\n  create: (data: ProductRequest): Promise<ApiResponse<Product>> => {\n    return api.post('/v1/products', data).then((res: any) => res.data);\n  },\n\n  // 更新产品\n  update: (id: number, data: ProductRequest): Promise<ApiResponse<Product>> => {\n    return api.put(`/v1/products/${id}`, data).then((res: any) => res.data);\n  },\n\n  // 删除产品\n  delete: (id: number): Promise<ApiResponse<null>> => {\n    return api.delete(`/v1/products/${id}`).then((res: any) => res.data);\n  },\n\n  // 更新产品状态\n  updateStatus: (id: number, status: 'online' | 'offline'): Promise<ApiResponse<Product>> => {\n    return api.patch(`/v1/products/${id}/status`, { status }).then((res: any) => res.data);\n  },\n\n  // 批量下架\n  batchOffline: (data: BatchOperationRequest): Promise<ApiResponse<{ count: number }>> => {\n    return api.post('/v1/products/batch/offline', data).then((res: any) => res.data);\n  },\n\n  // 批量删除\n  batchDelete: (data: BatchOperationRequest): Promise<ApiResponse<{ count: number }>> => {\n    return api.delete('/v1/products/batch/delete', { data }).then((res: any) => res.data);\n  },\n\n  // 获取统计数据\n  getStats: (): Promise<ApiResponse<{\n    total: number;\n    online: number;\n    offline: number;\n    operators: { [key: string]: number };\n    recent: Product[];\n  }>> => {\n    return api.get('/v1/products/stats').then((res: any) => res.data);\n  },\n\n  // 复制产品\n  duplicate: (id: number): Promise<ApiResponse<Product>> => {\n    return api.post(`/v1/products/${id}/duplicate`).then((res: any) => res.data);\n  },\n\n  // 批量上架\n  batchOnline: (data: BatchOperationRequest): Promise<ApiResponse<{ count: number }>> => {\n    return api.post('/v1/products/batch/online', data).then((res: any) => res.data);\n  },\n\n  // 导出数据\n  export: (params?: ProductListParams): Promise<Blob> => {\n    return api.get('/v1/products/export', {\n      params,\n      responseType: 'blob'\n    }).then((res: any) => res.data);\n  },\n\n  // 批量导入\n  import: (file: File): Promise<ApiResponse<{ success: number; failed: number; errors: string[] }>> => {\n    const formData = new FormData();\n    formData.append('file', file);\n    return api.post('/v1/products/import', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    }).then((res: any) => res.data);\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;AAC5C,SAASC,OAAO,QAAQ,MAAM;AAoB9B;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,MAAM;EACrDC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAW,IAAK;EACf;EACA,OAAOA,MAAM;AACf,CAAC,EACAC,KAAU,IAAK;EACd,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACQ,YAAY,CAACO,QAAQ,CAACL,GAAG,CAC1BK,QAAoC,IAAK;EACxC,MAAM;IAAEC;EAAK,CAAC,GAAGD,QAAQ;;EAEzB;EACA,IAAIC,IAAI,CAACC,IAAI,KAAK,GAAG,IAAID,IAAI,CAACC,IAAI,KAAK,GAAG,EAAE;IAC1ClB,OAAO,CAACa,KAAK,CAACI,IAAI,CAACjB,OAAO,IAAI,MAAM,CAAC;IACrC,OAAOc,OAAO,CAACC,MAAM,CAAC,IAAII,KAAK,CAACF,IAAI,CAACjB,OAAO,IAAI,MAAM,CAAC,CAAC;EAC1D;EAEA,OAAOgB,QAAQ;AACjB,CAAC,EACAH,KAAU,IAAK;EAAA,IAAAO,UAAA;EACdC,OAAO,CAACR,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EAElC,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB,MAAM;MAAEM,MAAM;MAAEL;IAAK,CAAC,GAAGJ,KAAK,CAACG,QAAQ;IAEvC,QAAQM,MAAM;MACZ,KAAK,GAAG;QACNtB,OAAO,CAACa,KAAK,CAAC,QAAQ,CAAC;QACvB;MACF,KAAK,GAAG;QACNb,OAAO,CAACa,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF,KAAK,GAAG;QACNb,OAAO,CAACa,KAAK,CAAC,MAAM,CAAC;QACrB;MACF,KAAK,GAAG;QACNb,OAAO,CAACa,KAAK,CAAC,UAAU,CAAC;QACzB;MACF,KAAK,GAAG;QACN,IAAII,IAAI,aAAJA,IAAI,gBAAAG,UAAA,GAAJH,IAAI,CAAEA,IAAI,cAAAG,UAAA,eAAVA,UAAA,CAAYG,MAAM,EAAE;UACtB,MAAMA,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACR,IAAI,CAACA,IAAI,CAACM,MAAM,CAAC,CAACG,IAAI,CAAC,CAAa;UACjE1B,OAAO,CAACa,KAAK,CAACU,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,MAAM;UACL3B,OAAO,CAACa,KAAK,CAAC,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjB,OAAO,KAAI,QAAQ,CAAC;QAC1C;QACA;MACF,KAAK,GAAG;QACNA,OAAO,CAACa,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;QACEb,OAAO,CAACa,KAAK,CAAC,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjB,OAAO,KAAI,MAAM,CAAC;IAC1C;EACF,CAAC,MAAM,IAAIa,KAAK,CAACH,OAAO,EAAE;IACxBV,OAAO,CAACa,KAAK,CAAC,cAAc,CAAC;EAC/B,CAAC,MAAM;IACLb,OAAO,CAACa,KAAK,CAAC,QAAQ,CAAC;EACzB;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMe,UAAU,GAAG;EACxB;EACAC,OAAO,EAAGC,MAA0B,IAAgD;IAClF,OAAO7B,GAAG,CAAC8B,GAAG,CAAC,cAAc,EAAE;MAAED;IAAO,CAAC,CAAC,CAACE,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACzE,CAAC;EAED;EACAiB,SAAS,EAAGC,EAAU,IAAoC;IACxD,OAAOlC,GAAG,CAAC8B,GAAG,CAAC,gBAAgBI,EAAE,EAAE,CAAC,CAACH,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACnE,CAAC;EAED;EACAf,MAAM,EAAGe,IAAoB,IAAoC;IAC/D,OAAOhB,GAAG,CAACmC,IAAI,CAAC,cAAc,EAAEnB,IAAI,CAAC,CAACe,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACpE,CAAC;EAED;EACAoB,MAAM,EAAEA,CAACF,EAAU,EAAElB,IAAoB,KAAoC;IAC3E,OAAOhB,GAAG,CAACqC,GAAG,CAAC,gBAAgBH,EAAE,EAAE,EAAElB,IAAI,CAAC,CAACe,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACzE,CAAC;EAED;EACAsB,MAAM,EAAGJ,EAAU,IAAiC;IAClD,OAAOlC,GAAG,CAACsC,MAAM,CAAC,gBAAgBJ,EAAE,EAAE,CAAC,CAACH,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACtE,CAAC;EAED;EACAuB,YAAY,EAAEA,CAACL,EAAU,EAAEb,MAA4B,KAAoC;IACzF,OAAOrB,GAAG,CAACwC,KAAK,CAAC,gBAAgBN,EAAE,SAAS,EAAE;MAAEb;IAAO,CAAC,CAAC,CAACU,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACxF,CAAC;EAED;EACAyB,YAAY,EAAGzB,IAA2B,IAA8C;IACtF,OAAOhB,GAAG,CAACmC,IAAI,CAAC,4BAA4B,EAAEnB,IAAI,CAAC,CAACe,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EAClF,CAAC;EAED;EACA0B,WAAW,EAAG1B,IAA2B,IAA8C;IACrF,OAAOhB,GAAG,CAACsC,MAAM,CAAC,2BAA2B,EAAE;MAAEtB;IAAK,CAAC,CAAC,CAACe,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACvF,CAAC;EAED;EACA2B,QAAQ,EAAEA,CAAA,KAMH;IACL,OAAO3C,GAAG,CAAC8B,GAAG,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACnE,CAAC;EAED;EACA4B,SAAS,EAAGV,EAAU,IAAoC;IACxD,OAAOlC,GAAG,CAACmC,IAAI,CAAC,gBAAgBD,EAAE,YAAY,CAAC,CAACH,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EAC9E,CAAC;EAED;EACA6B,WAAW,EAAG7B,IAA2B,IAA8C;IACrF,OAAOhB,GAAG,CAACmC,IAAI,CAAC,2BAA2B,EAAEnB,IAAI,CAAC,CAACe,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACjF,CAAC;EAED;EACA8B,MAAM,EAAGjB,MAA0B,IAAoB;IACrD,OAAO7B,GAAG,CAAC8B,GAAG,CAAC,qBAAqB,EAAE;MACpCD,MAAM;MACNkB,YAAY,EAAE;IAChB,CAAC,CAAC,CAAChB,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACjC,CAAC;EAED;EACAgC,MAAM,EAAGC,IAAU,IAAkF;IACnG,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAC7B,OAAOjD,GAAG,CAACmC,IAAI,CAAC,qBAAqB,EAAEe,QAAQ,EAAE;MAC/C3C,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC,CAACwB,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAAChB,IAAI,CAAC;EACjC;AACF,CAAC;AAED,eAAehB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
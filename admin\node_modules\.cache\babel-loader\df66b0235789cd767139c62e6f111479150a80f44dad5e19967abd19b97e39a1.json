{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { formatValue } from \"../../utils/dateUtil\";\nimport { PanelContext, useInfo } from \"../context\";\nimport PanelHeader from \"../PanelHeader\";\nimport TimePanelBody from \"./TimePanelBody\";\nexport default function TimePanel(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    locale = props.locale,\n    generateConfig = props.generateConfig,\n    showTime = props.showTime;\n  var _ref = showTime || {},\n    format = _ref.format;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n\n  // ========================== Base ==========================\n  var _useInfo = useInfo(props, 'time'),\n    _useInfo2 = _slicedToArray(_useInfo, 1),\n    info = _useInfo2[0];\n\n  // ========================= Render =========================\n  return /*#__PURE__*/React.createElement(PanelContext.Provider, {\n    value: info\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(panelPrefixCls)\n  }, /*#__PURE__*/React.createElement(PanelHeader, null, value ? formatValue(value, {\n    locale: locale,\n    format: format,\n    generateConfig: generateConfig\n  }) : \"\\xA0\"), /*#__PURE__*/React.createElement(TimePanelBody, showTime)));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
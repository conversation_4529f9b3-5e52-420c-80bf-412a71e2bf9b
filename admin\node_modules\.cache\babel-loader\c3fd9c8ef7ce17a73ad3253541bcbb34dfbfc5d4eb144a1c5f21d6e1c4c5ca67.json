{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SaveTwoToneSvg from \"@ant-design/icons-svg/es/asn/SaveTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SaveTwoTone = function SaveTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SaveTwoToneSvg\n  }));\n};\n\n/**![save](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNCAzMjBjMCAxNy43LTE0LjMgMzItMzIgMzJIMzUyYy0xNy43IDAtMzItMTQuMy0zMi0zMlYxODRIMTg0djY1Nmg2NTZWMzQxLjhsLTEzNi0xMzZWMzIwek01MTIgNzMwYy03OS41IDAtMTQ0LTY0LjUtMTQ0LTE0NHM2NC41LTE0NCAxNDQtMTQ0IDE0NCA2NC41IDE0NCAxNDQtNjQuNSAxNDQtMTQ0IDE0NHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxMiA0NDJjLTc5LjUgMC0xNDQgNjQuNS0xNDQgMTQ0czY0LjUgMTQ0IDE0NCAxNDQgMTQ0LTY0LjUgMTQ0LTE0NC02NC41LTE0NC0xNDQtMTQ0em0wIDIyNGMtNDQuMiAwLTgwLTM1LjgtODAtODBzMzUuOC04MCA4MC04MCA4MCAzNS44IDgwIDgwLTM1LjggODAtODAgODB6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik04OTMuMyAyOTMuM0w3MzAuNyAxMzAuN2MtLjctLjctMS40LTEuMy0yLjEtMi0uMS0uMS0uMy0uMi0uNC0uMy0uNy0uNy0xLjUtMS4zLTIuMi0xLjlhNjQgNjQgMCAwMC0yMi0xMS43VjExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzM4LjVjMC0xNy02LjctMzMuMi0xOC43LTQ1LjJ6TTM4NCAxODRoMjU2djEwNEgzODRWMTg0em00NTYgNjU2SDE4NFYxODRoMTM2djEzNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzMjBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMjA1LjhsMTM2IDEzNlY4NDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SaveTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SaveTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SaveTwoToneSvg", "AntdIcon", "SaveTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/SaveTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SaveTwoToneSvg from \"@ant-design/icons-svg/es/asn/SaveTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SaveTwoTone = function SaveTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SaveTwoToneSvg\n  }));\n};\n\n/**![save](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNCAzMjBjMCAxNy43LTE0LjMgMzItMzIgMzJIMzUyYy0xNy43IDAtMzItMTQuMy0zMi0zMlYxODRIMTg0djY1Nmg2NTZWMzQxLjhsLTEzNi0xMzZWMzIwek01MTIgNzMwYy03OS41IDAtMTQ0LTY0LjUtMTQ0LTE0NHM2NC41LTE0NCAxNDQtMTQ0IDE0NCA2NC41IDE0NCAxNDQtNjQuNSAxNDQtMTQ0IDE0NHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxMiA0NDJjLTc5LjUgMC0xNDQgNjQuNS0xNDQgMTQ0czY0LjUgMTQ0IDE0NCAxNDQgMTQ0LTY0LjUgMTQ0LTE0NC02NC41LTE0NC0xNDQtMTQ0em0wIDIyNGMtNDQuMiAwLTgwLTM1LjgtODAtODBzMzUuOC04MCA4MC04MCA4MCAzNS44IDgwIDgwLTM1LjggODAtODAgODB6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik04OTMuMyAyOTMuM0w3MzAuNyAxMzAuN2MtLjctLjctMS40LTEuMy0yLjEtMi0uMS0uMS0uMy0uMi0uNC0uMy0uNy0uNy0xLjUtMS4zLTIuMi0xLjlhNjQgNjQgMCAwMC0yMi0xMS43VjExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzM4LjVjMC0xNy02LjctMzMuMi0xOC43LTQ1LjJ6TTM4NCAxODRoMjU2djEwNEgzODRWMTg0em00NTYgNjU2SDE4NFYxODRoMTM2djEzNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzMjBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMjA1LjhsMTM2IDEzNlY4NDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SaveTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SaveTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
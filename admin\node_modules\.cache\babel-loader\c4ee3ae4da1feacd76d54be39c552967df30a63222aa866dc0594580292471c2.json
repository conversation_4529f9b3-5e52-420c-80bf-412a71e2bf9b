{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{Form,message,Typography,Button,Space,Spin}from'antd';import{ArrowLeftOutlined}from'@ant-design/icons';import{useNavigate,useParams}from'react-router-dom';import ProductForm from'../components/ProductForm';import{productApi}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const ProductEdit=()=>{const[form]=Form.useForm();const[loading,setLoading]=useState(false);const[fetchLoading,setFetchLoading]=useState(true);const[product,setProduct]=useState(null);const navigate=useNavigate();const{id}=useParams();// 获取产品详情\nconst fetchProduct=useCallback(async()=>{if(!id){message.error('产品ID不存在');navigate('/products/list');return;}setFetchLoading(true);try{const response=await productApi.getDetail(parseInt(id));setProduct(response.data);}catch(error){console.error('获取产品详情失败:',error);message.error('获取产品详情失败');navigate('/products/list');}finally{setFetchLoading(false);}},[id,navigate]);// 处理表单提交\nconst handleSubmit=async values=>{if(!id)return;setLoading(true);try{const response=await productApi.update(parseInt(id),values);message.success('产品更新成功！');// 跳转到产品详情页\nnavigate(\"/products/detail/\".concat(response.data.id));}catch(error){console.error('更新产品失败:',error);message.error('更新产品失败，请重试');}finally{setLoading(false);}};useEffect(()=>{fetchProduct();},[fetchProduct]);if(fetchLoading){return/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'50px 0'},children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:\"\\u52A0\\u8F7D\\u4EA7\\u54C1\\u4FE1\\u606F\\u4E2D...\"})]});}if(!product){return/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'50px 0'},children:[/*#__PURE__*/_jsx(\"div\",{children:\"\\u4EA7\\u54C1\\u4E0D\\u5B58\\u5728\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",style:{marginTop:16},onClick:()=>navigate('/products/list'),children:\"\\u8FD4\\u56DE\\u4EA7\\u54C1\\u5217\\u8868\"})]});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Space,{children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(ArrowLeftOutlined,{}),onClick:()=>navigate('/products/list'),children:\"\\u8FD4\\u56DE\\u4EA7\\u54C1\\u5217\\u8868\"})}),/*#__PURE__*/_jsxs(Title,{level:2,style:{margin:'16px 0'},children:[\"\\u7F16\\u8F91\\u4EA7\\u54C1 - \",product.name]})]}),/*#__PURE__*/_jsx(ProductForm,{form:form,initialValues:product,onSubmit:handleSubmit,loading:loading})]});};export default ProductEdit;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin系统测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Admin系统诊断测试</h1>
        
        <div class="test-item info">
            <h3>📋 基础信息</h3>
            <p><strong>当前URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>用户代理:</strong> <span id="userAgent"></span></p>
            <p><strong>时间:</strong> <span id="currentTime"></span></p>
        </div>

        <div class="test-item">
            <h3>🌐 网络连接测试</h3>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="testCORS()">测试CORS</button>
            <button onclick="testStaticResources()">测试静态资源</button>
        </div>

        <div class="test-item">
            <h3>🔍 JavaScript环境测试</h3>
            <button onclick="testJavaScript()">测试JavaScript</button>
            <button onclick="testLocalStorage()">测试LocalStorage</button>
            <button onclick="testConsole()">测试Console</button>
        </div>

        <div class="test-item">
            <h3>📊 React环境测试</h3>
            <button onclick="testReact()">检查React</button>
            <button onclick="testAntd()">检查Ant Design</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // 初始化页面信息
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('currentTime').textContent = new Date().toLocaleString();

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(logDiv);
            console.log(message);
        }

        // 测试API连接
        async function testAPI() {
            log('🔄 开始测试API连接...', 'info');
            
            try {
                // 测试API根路径
                const response1 = await fetch('https://h5.haokajiyun.com/api/');
                log(`API根路径状态: ${response1.status} ${response1.statusText}`, response1.ok ? 'success' : 'error');
                
                // 测试产品API
                const response2 = await fetch('https://h5.haokajiyun.com/api/v1/products');
                log(`产品API状态: ${response2.status} ${response2.statusText}`, response2.ok ? 'success' : 'error');
                
                if (response2.ok) {
                    const data = await response2.json();
                    log(`API响应数据: ${JSON.stringify(data).substring(0, 200)}...`, 'success');
                }
            } catch (error) {
                log(`API测试失败: ${error.message}`, 'error');
            }
        }

        // 测试CORS
        async function testCORS() {
            log('🔄 开始测试CORS...', 'info');
            
            try {
                const response = await fetch('https://h5.haokajiyun.com/api/v1/products', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                log(`CORS测试状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                // 检查CORS头
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                log(`CORS头信息: ${JSON.stringify(corsHeaders)}`, 'info');
                
            } catch (error) {
                log(`CORS测试失败: ${error.message}`, 'error');
            }
        }

        // 测试静态资源
        async function testStaticResources() {
            log('🔄 开始测试静态资源...', 'info');
            
            const resources = [
                'https://h5.haokajiyun.com/admin/static/css/main.1c68141c.css',
                'https://h5.haokajiyun.com/admin/static/js/main.990c5350.js'
            ];
            
            for (const url of resources) {
                try {
                    const response = await fetch(url);
                    log(`${url.split('/').pop()}: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                } catch (error) {
                    log(`${url.split('/').pop()}: 加载失败 - ${error.message}`, 'error');
                }
            }
        }

        // 测试JavaScript环境
        function testJavaScript() {
            log('🔄 开始测试JavaScript环境...', 'info');
            
            try {
                // 测试基本功能
                log(`ES6支持: ${typeof Promise !== 'undefined' ? '✅' : '❌'}`, 'info');
                log(`Fetch API: ${typeof fetch !== 'undefined' ? '✅' : '❌'}`, 'info');
                log(`LocalStorage: ${typeof localStorage !== 'undefined' ? '✅' : '❌'}`, 'info');
                log(`JSON支持: ${typeof JSON !== 'undefined' ? '✅' : '❌'}`, 'info');
                
                // 测试错误处理
                try {
                    throw new Error('测试错误处理');
                } catch (e) {
                    log('错误处理: ✅ 正常', 'success');
                }
                
            } catch (error) {
                log(`JavaScript测试失败: ${error.message}`, 'error');
            }
        }

        // 测试LocalStorage
        function testLocalStorage() {
            log('🔄 开始测试LocalStorage...', 'info');
            
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                log(`LocalStorage测试: ${value === 'value' ? '✅ 正常' : '❌ 异常'}`, value === 'value' ? 'success' : 'error');
            } catch (error) {
                log(`LocalStorage测试失败: ${error.message}`, 'error');
            }
        }

        // 测试Console
        function testConsole() {
            log('🔄 开始测试Console...', 'info');
            
            console.log('Console.log测试');
            console.warn('Console.warn测试');
            console.error('Console.error测试');
            
            log('Console测试: ✅ 请检查浏览器控制台', 'success');
        }

        // 测试React环境
        function testReact() {
            log('🔄 检查React环境...', 'info');
            
            log(`React: ${typeof React !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}`, 'info');
            log(`ReactDOM: ${typeof ReactDOM !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}`, 'info');
            
            // 检查是否有React根元素
            const rootElement = document.getElementById('root');
            log(`React根元素: ${rootElement ? '✅ 存在' : '❌ 不存在'}`, rootElement ? 'success' : 'error');
            
            if (rootElement) {
                log(`根元素内容: ${rootElement.innerHTML || '(空)'}`, 'info');
            }
        }

        // 测试Ant Design
        function testAntd() {
            log('🔄 检查Ant Design...', 'info');
            
            log(`Antd: ${typeof antd !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}`, 'info');
            
            // 检查CSS是否加载
            const styles = document.querySelectorAll('link[rel="stylesheet"]');
            let antdCssFound = false;
            styles.forEach(style => {
                if (style.href.includes('antd') || style.href.includes('main')) {
                    antdCssFound = true;
                }
            });
            
            log(`Ant Design CSS: ${antdCssFound ? '✅ 已加载' : '❌ 未找到'}`, antdCssFound ? 'success' : 'error');
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            log('📄 页面加载完成，开始自动测试...', 'info');
            setTimeout(() => {
                testJavaScript();
                testReact();
            }, 1000);
        });

        // 捕获全局错误
        window.addEventListener('error', function(event) {
            log(`全局错误: ${event.error ? event.error.message : event.message}`, 'error');
        });

        // 捕获Promise错误
        window.addEventListener('unhandledrejection', function(event) {
            log(`Promise错误: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>

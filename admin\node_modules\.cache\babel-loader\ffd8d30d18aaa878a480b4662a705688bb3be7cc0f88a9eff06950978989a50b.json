{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\AppConfig.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Tabs, Form, Input, Button, Typography, message, Spin } from 'antd';\nimport { SaveOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\n\n// API基础URL\nconst API_BASE_URL = 'http://localhost:8000/api/v1';\nconst AppConfig = () => {\n  _s();\n  const [customerServiceForm] = Form.useForm();\n  const [announcementForm] = Form.useForm();\n  const [privacyForm] = Form.useForm();\n  const [userAgreementForm] = Form.useForm();\n  const [bannerForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [dataLoading, setDataLoading] = useState(true);\n\n  // 加载配置数据\n  const loadConfigs = async () => {\n    setDataLoading(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/app-configs`);\n      if (response.data.success) {\n        const {\n          customer_service,\n          announcement,\n          privacy_policy,\n          user_agreement,\n          banner\n        } = response.data.data;\n\n        // 设置表单数据\n        customerServiceForm.setFieldsValue(customer_service || {});\n        announcementForm.setFieldsValue(announcement || {});\n        privacyForm.setFieldsValue(privacy_policy || {});\n        userAgreementForm.setFieldsValue(user_agreement || {});\n\n        // 特殊处理轮播图数据\n        console.log('轮播图原始数据:', banner);\n        if (banner) {\n          const bannerFormData = {\n            banner1: {\n              imageUrl: banner['banner1.imageUrl'] || '',\n              linkUrl: banner['banner1.linkUrl'] || ''\n            },\n            banner2: {\n              imageUrl: banner['banner2.imageUrl'] || '',\n              linkUrl: banner['banner2.linkUrl'] || ''\n            },\n            banner3: {\n              imageUrl: banner['banner3.imageUrl'] || '',\n              linkUrl: banner['banner3.linkUrl'] || ''\n            }\n          };\n          console.log('轮播图表单数据:', bannerFormData);\n          bannerForm.setFieldsValue(bannerFormData);\n        }\n      } else {\n        message.error('加载配置失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('加载配置错误:', error);\n      message.error('加载配置失败，请检查网络连接');\n    } finally {\n      setDataLoading(false);\n    }\n  };\n\n  // 组件挂载时加载数据\n  useEffect(() => {\n    loadConfigs();\n  }, []);\n\n  // 客服设置保存\n  const handleCustomerServiceSave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/customer_service`, values);\n      if (response.data.success) {\n        message.success('客服设置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存客服设置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 公告弹出保存\n  const handleAnnouncementSave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/announcement`, values);\n      if (response.data.success) {\n        message.success('公告设置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存公告设置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 隐私政策保存\n  const handlePrivacySave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/privacy_policy`, values);\n      if (response.data.success) {\n        message.success('隐私政策保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存隐私政策错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 用户协议保存\n  const handleUserAgreementSave = async values => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/user_agreement`, values);\n      if (response.data.success) {\n        message.success('用户协议保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error) {\n      console.error('保存用户协议错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 轮播图保存\n  const handleBannerSave = async values => {\n    setLoading(true);\n    try {\n      var _values$banner, _values$banner2, _values$banner3, _values$banner4, _values$banner5, _values$banner6;\n      console.log('保存轮播图数据:', values);\n\n      // 转换数据格式以匹配后端期望的格式\n      const bannerData = {\n        'banner1.imageUrl': ((_values$banner = values.banner1) === null || _values$banner === void 0 ? void 0 : _values$banner.imageUrl) || '',\n        'banner1.linkUrl': ((_values$banner2 = values.banner1) === null || _values$banner2 === void 0 ? void 0 : _values$banner2.linkUrl) || '',\n        'banner2.imageUrl': ((_values$banner3 = values.banner2) === null || _values$banner3 === void 0 ? void 0 : _values$banner3.imageUrl) || '',\n        'banner2.linkUrl': ((_values$banner4 = values.banner2) === null || _values$banner4 === void 0 ? void 0 : _values$banner4.linkUrl) || '',\n        'banner3.imageUrl': ((_values$banner5 = values.banner3) === null || _values$banner5 === void 0 ? void 0 : _values$banner5.imageUrl) || '',\n        'banner3.linkUrl': ((_values$banner6 = values.banner3) === null || _values$banner6 === void 0 ? void 0 : _values$banner6.linkUrl) || ''\n      };\n      console.log('转换后的轮播图数据:', bannerData);\n      const response = await axios.put(`${API_BASE_URL}/app-configs/banner`, bannerData);\n      console.log('保存响应:', response.data);\n      if (response.data.success) {\n        message.success('轮播图配置保存成功');\n        // 重新加载数据以确保表单状态正确\n        loadConfigs();\n      } else {\n        console.error('保存失败响应:', response.data);\n        message.error('保存失败：' + (response.data.message || '未知错误'));\n        if (response.data.errors) {\n          console.error('验证错误:', response.data.errors);\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      console.error('保存轮播图配置错误:', error);\n      console.error('错误详情:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      message.error('保存失败：' + (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || '请重试'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 客服设置选项卡\n  const CustomerServiceTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u5BA2\\u670D\\u8BBE\\u7F6E\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: customerServiceForm,\n      layout: \"vertical\",\n      onFinish: handleCustomerServiceSave,\n      style: {\n        maxWidth: 600\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5FAE\\u4FE1\\u5BA2\\u670D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this),\n        name: \"wechatService\",\n        rules: [{\n          required: true,\n          message: '请输入微信客服号'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5FAE\\u4FE1\\u5BA2\\u670D\\u53F7\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5FAE\\u4FE1\\u5BA2\\u670D\\u4E8C\\u7EF4\\u7801\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this),\n        name: \"wechatQrCode\",\n        rules: [{\n          required: true,\n          message: '请输入微信客服二维码链接'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5FAE\\u4FE1\\u5BA2\\u670D\\u4E8C\\u7EF4\\u7801\\u94FE\\u63A5\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5BA2\\u670D\\u7535\\u8BDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this),\n        name: \"servicePhone\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u670D\\u7535\\u8BDD\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5BA2\\u670D\\u90AE\\u7BB1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this),\n        name: \"serviceEmail\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u670D\\u90AE\\u7BB1\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u5DE5\\u4F5C\\u65F6\\u95F4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this),\n        name: \"workingHours\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F8B\\u5982\\uFF1A\\u5468\\u4E00\\u81F3\\u5468\\u4E94 9:00-18:00\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n\n  // 公告弹出选项卡\n  const AnnouncementTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u516C\\u544A\\u5F39\\u51FA\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: announcementForm,\n      layout: \"vertical\",\n      onFinish: handleAnnouncementSave,\n      style: {\n        maxWidth: 600\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u516C\\u544A\\u6807\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this),\n        name: \"title\",\n        rules: [{\n          required: true,\n          message: '请输入公告标题'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u516C\\u544A\\u6807\\u9898\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u516C\\u544A\\u5185\\u5BB9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this),\n        name: \"content\",\n        rules: [{\n          required: true,\n          message: '请输入公告内容'\n        }],\n        children: /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 6,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u516C\\u544A\\u5185\\u5BB9\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u662F\\u5426\\u542F\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this),\n        name: \"enabled\",\n        valuePropName: \"checked\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          ghost: true,\n          children: \"\\u542F\\u7528\\u516C\\u544A\\u5F39\\u51FA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 5\n  }, this);\n\n  // 隐私政策选项卡\n  const PrivacyPolicyTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u9690\\u79C1\\u653F\\u7B56\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: privacyForm,\n      layout: \"vertical\",\n      onFinish: handlePrivacySave,\n      style: {\n        maxWidth: 800\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u9690\\u79C1\\u653F\\u7B56\\u6807\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this),\n        name: \"title\",\n        rules: [{\n          required: true,\n          message: '请输入隐私政策标题'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u9690\\u79C1\\u653F\\u7B56\\u6807\\u9898\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u9690\\u79C1\\u653F\\u7B56\\u5185\\u5BB9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 13\n        }, this),\n        name: \"content\",\n        rules: [{\n          required: true,\n          message: '请输入隐私政策内容'\n        }],\n        children: /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 12,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u9690\\u79C1\\u653F\\u7B56\\u5185\\u5BB9\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u751F\\u6548\\u65E5\\u671F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 13\n        }, this),\n        name: \"effectiveDate\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F8B\\u5982\\uFF1A2024\\u5E741\\u67081\\u65E5\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 420,\n    columnNumber: 5\n  }, this);\n\n  // 用户协议选项卡\n  const UserAgreementTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u7528\\u6237\\u534F\\u8BAE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        color: '#999999',\n        fontSize: '14px',\n        display: 'block',\n        marginBottom: 24\n      },\n      children: \"\\u914D\\u7F6E\\u5E94\\u7528\\u7684\\u7528\\u6237\\u670D\\u52A1\\u534F\\u8BAE\\u5185\\u5BB9\\uFF0C\\u7528\\u6237\\u5728\\u6CE8\\u518C\\u6216\\u4F7F\\u7528\\u670D\\u52A1\\u65F6\\u9700\\u8981\\u540C\\u610F\\u7684\\u6761\\u6B3E\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: userAgreementForm,\n      layout: \"vertical\",\n      onFinish: handleUserAgreementSave,\n      style: {\n        maxWidth: '800px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u534F\\u8BAE\\u6807\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 13\n        }, this),\n        name: \"title\",\n        rules: [{\n          required: true,\n          message: '请输入协议标题'\n        }],\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F8B\\u5982\\uFF1A\\u7528\\u6237\\u670D\\u52A1\\u534F\\u8BAE\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u534F\\u8BAE\\u5185\\u5BB9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 13\n        }, this),\n        name: \"content\",\n        rules: [{\n          required: true,\n          message: '请输入协议内容'\n        }],\n        children: /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 12,\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BE6\\u7EC6\\u7684\\u7528\\u6237\\u534F\\u8BAE\\u5185\\u5BB9...\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '12px',\n            fontSize: '14px',\n            lineHeight: '1.6'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: '#666666',\n            fontSize: '14px'\n          },\n          children: \"\\u751F\\u6548\\u65E5\\u671F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 13\n        }, this),\n        name: \"effectiveDate\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F8B\\u5982\\uFF1A2024\\u5E741\\u67081\\u65E5\",\n          style: {\n            backgroundColor: '#ffffff',\n            border: '1px solid #d9d9d9',\n            borderRadius: '6px',\n            padding: '8px 12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 516,\n    columnNumber: 5\n  }, this);\n\n  // 轮播图选项卡\n  const BannerTab = () => /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        color: '#666666',\n        fontWeight: 'bold',\n        marginBottom: 24,\n        fontSize: '20px'\n      },\n      children: \"\\u8F6E\\u64AD\\u56FE\\u914D\\u7F6E\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        color: '#999999',\n        fontSize: '14px',\n        display: 'block',\n        marginBottom: 24\n      },\n      children: \"\\u914D\\u7F6E\\u5E94\\u7528\\u9996\\u9875\\u7684\\u8F6E\\u64AD\\u56FE\\u5185\\u5BB9\\uFF0C\\u652F\\u63013\\u5F20\\u8F6E\\u64AD\\u56FE\\u7247\\u548C\\u76F8\\u5173\\u4FE1\\u606F\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: bannerForm,\n      layout: \"vertical\",\n      onFinish: handleBannerSave,\n      style: {\n        maxWidth: '800px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 32,\n          padding: 16,\n          border: '1px solid #f0f0f0',\n          borderRadius: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          style: {\n            color: '#666666',\n            marginBottom: 16\n          },\n          children: \"\\u8F6E\\u64AD\\u56FE 1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u56FE\\u7247URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 20\n          }, this),\n          name: ['banner1', 'imageUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u8DF3\\u8F6C\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 20\n          }, this),\n          name: ['banner1', 'linkUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 32,\n          padding: 16,\n          border: '1px solid #f0f0f0',\n          borderRadius: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          style: {\n            color: '#666666',\n            marginBottom: 16\n          },\n          children: \"\\u8F6E\\u64AD\\u56FE 2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u56FE\\u7247URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 20\n          }, this),\n          name: ['banner2', 'imageUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u8DF3\\u8F6C\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 20\n          }, this),\n          name: ['banner2', 'linkUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 32,\n          padding: 16,\n          border: '1px solid #f0f0f0',\n          borderRadius: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          style: {\n            color: '#666666',\n            marginBottom: 16\n          },\n          children: \"\\u8F6E\\u64AD\\u56FE 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u56FE\\u7247URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 20\n          }, this),\n          name: ['banner3', 'imageUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8F6E\\u64AD\\u56FE\\u7247\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: '#666666',\n              fontSize: '14px'\n            },\n            children: \"\\u8DF3\\u8F6C\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 20\n          }, this),\n          name: ['banner3', 'linkUrl'],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u70B9\\u51FB\\u8DF3\\u8F6C\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        style: {\n          marginTop: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loading,\n          icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 19\n          }, this),\n          size: \"large\",\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 617,\n    columnNumber: 5\n  }, this);\n\n  // 选项卡配置\n  const tabItems = [{\n    key: 'customerService',\n    label: '客服设置',\n    children: /*#__PURE__*/_jsxDEV(CustomerServiceTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 17\n    }, this)\n  }, {\n    key: 'announcement',\n    label: '公告弹出',\n    children: /*#__PURE__*/_jsxDEV(AnnouncementTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 17\n    }, this)\n  }, {\n    key: 'privacy',\n    label: '隐私政策',\n    children: /*#__PURE__*/_jsxDEV(PrivacyPolicyTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 17\n    }, this)\n  }, {\n    key: 'userAgreement',\n    label: '用户协议',\n    children: /*#__PURE__*/_jsxDEV(UserAgreementTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 17\n    }, this)\n  }, {\n    key: 'banner',\n    label: '轮播图',\n    children: /*#__PURE__*/_jsxDEV(BannerTab, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 17\n    }, this)\n  }];\n  if (dataLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 743,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '16px'\n        },\n        children: \"\\u52A0\\u8F7D\\u914D\\u7F6E\\u6570\\u636E\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Tabs, {\n      defaultActiveKey: \"customerService\",\n      items: tabItems,\n      size: \"large\",\n      style: {\n        background: '#ffffff',\n        borderRadius: '8px',\n        padding: '16px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 751,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 750,\n    columnNumber: 5\n  }, this);\n};\n_s(AppConfig, \"WCkcs8DXXmXk39mkdnUy8V3uM1A=\", false, function () {\n  return [Form.useForm, Form.useForm, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = AppConfig;\nexport default AppConfig;\nvar _c;\n$RefreshReg$(_c, \"AppConfig\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Tabs", "Form", "Input", "<PERSON><PERSON>", "Typography", "message", "Spin", "SaveOutlined", "axios", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "API_BASE_URL", "AppConfig", "_s", "customerServiceForm", "useForm", "announcementForm", "privacyForm", "userAgreementForm", "bannerForm", "loading", "setLoading", "dataLoading", "setDataLoading", "loadConfigs", "response", "get", "data", "success", "customer_service", "announcement", "privacy_policy", "user_agreement", "banner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "log", "bannerFormData", "banner1", "imageUrl", "linkUrl", "banner2", "banner3", "error", "handleCustomerServiceSave", "values", "put", "handleAnnouncementSave", "handlePrivacySave", "handleUserAgreementSave", "handleBannerSave", "_values$banner", "_values$banner2", "_values$banner3", "_values$banner4", "_values$banner5", "_values$banner6", "bannerData", "errors", "_error$response", "_error$response2", "_error$response2$data", "CustomerServiceTab", "children", "level", "style", "color", "fontWeight", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "form", "layout", "onFinish", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "label", "name", "rules", "required", "placeholder", "backgroundColor", "border", "borderRadius", "padding", "marginTop", "type", "htmlType", "icon", "size", "AnnouncementTab", "rows", "valuePropName", "ghost", "PrivacyPolicyTab", "UserAgreementTab", "display", "lineHeight", "BannerTab", "tabItems", "key", "textAlign", "defaultActiveKey", "items", "background", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/AppConfig.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Tabs,\n  Form,\n  Input,\n  Button,\n  Typography,\n  message,\n  Spin,\n} from 'antd';\nimport { SaveOutlined } from '@ant-design/icons';\nimport type { TabsProps } from 'antd';\nimport axios from 'axios';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\n// API基础URL\nconst API_BASE_URL = 'http://localhost:8000/api/v1';\n\nconst AppConfig: React.FC = () => {\n  const [customerServiceForm] = Form.useForm();\n  const [announcementForm] = Form.useForm();\n  const [privacyForm] = Form.useForm();\n  const [userAgreementForm] = Form.useForm();\n  const [bannerForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [dataLoading, setDataLoading] = useState(true);\n\n  // 加载配置数据\n  const loadConfigs = async () => {\n    setDataLoading(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/app-configs`);\n      if (response.data.success) {\n        const { customer_service, announcement, privacy_policy, user_agreement, banner } = response.data.data;\n\n        // 设置表单数据\n        customerServiceForm.setFieldsValue(customer_service || {});\n        announcementForm.setFieldsValue(announcement || {});\n        privacyForm.setFieldsValue(privacy_policy || {});\n        userAgreementForm.setFieldsValue(user_agreement || {});\n\n        // 特殊处理轮播图数据\n        console.log('轮播图原始数据:', banner);\n        if (banner) {\n          const bannerFormData = {\n            banner1: {\n              imageUrl: banner['banner1.imageUrl'] || '',\n              linkUrl: banner['banner1.linkUrl'] || '',\n            },\n            banner2: {\n              imageUrl: banner['banner2.imageUrl'] || '',\n              linkUrl: banner['banner2.linkUrl'] || '',\n            },\n            banner3: {\n              imageUrl: banner['banner3.imageUrl'] || '',\n              linkUrl: banner['banner3.linkUrl'] || '',\n            },\n          };\n          console.log('轮播图表单数据:', bannerFormData);\n          bannerForm.setFieldsValue(bannerFormData);\n        }\n      } else {\n        message.error('加载配置失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('加载配置错误:', error);\n      message.error('加载配置失败，请检查网络连接');\n    } finally {\n      setDataLoading(false);\n    }\n  };\n\n  // 组件挂载时加载数据\n  useEffect(() => {\n    loadConfigs();\n  }, []);\n\n  // 客服设置保存\n  const handleCustomerServiceSave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/customer_service`, values);\n      if (response.data.success) {\n        message.success('客服设置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存客服设置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 公告弹出保存\n  const handleAnnouncementSave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/announcement`, values);\n      if (response.data.success) {\n        message.success('公告设置保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存公告设置错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 隐私政策保存\n  const handlePrivacySave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/privacy_policy`, values);\n      if (response.data.success) {\n        message.success('隐私政策保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存隐私政策错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 用户协议保存\n  const handleUserAgreementSave = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await axios.put(`${API_BASE_URL}/app-configs/user_agreement`, values);\n      if (response.data.success) {\n        message.success('用户协议保存成功');\n      } else {\n        message.error('保存失败：' + response.data.message);\n      }\n    } catch (error: any) {\n      console.error('保存用户协议错误:', error);\n      message.error('保存失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 轮播图保存\n  const handleBannerSave = async (values: any) => {\n    setLoading(true);\n    try {\n      console.log('保存轮播图数据:', values);\n\n      // 转换数据格式以匹配后端期望的格式\n      const bannerData = {\n        'banner1.imageUrl': values.banner1?.imageUrl || '',\n        'banner1.linkUrl': values.banner1?.linkUrl || '',\n        'banner2.imageUrl': values.banner2?.imageUrl || '',\n        'banner2.linkUrl': values.banner2?.linkUrl || '',\n        'banner3.imageUrl': values.banner3?.imageUrl || '',\n        'banner3.linkUrl': values.banner3?.linkUrl || '',\n      };\n\n      console.log('转换后的轮播图数据:', bannerData);\n\n      const response = await axios.put(`${API_BASE_URL}/app-configs/banner`, bannerData);\n      console.log('保存响应:', response.data);\n\n      if (response.data.success) {\n        message.success('轮播图配置保存成功');\n        // 重新加载数据以确保表单状态正确\n        loadConfigs();\n      } else {\n        console.error('保存失败响应:', response.data);\n        message.error('保存失败：' + (response.data.message || '未知错误'));\n        if (response.data.errors) {\n          console.error('验证错误:', response.data.errors);\n        }\n      }\n    } catch (error: any) {\n      console.error('保存轮播图配置错误:', error);\n      console.error('错误详情:', error.response?.data);\n      message.error('保存失败：' + (error.response?.data?.message || error.message || '请重试'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 客服设置选项卡\n  const CustomerServiceTab = () => (\n    <Card>\n      <Title \n        level={3} \n        style={{ \n          color: '#666666', \n          fontWeight: 'bold', \n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        客服设置\n      </Title>\n      \n      <Form\n        form={customerServiceForm}\n        layout=\"vertical\"\n        onFinish={handleCustomerServiceSave}\n        style={{ maxWidth: 600 }}\n      >\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              微信客服\n            </Text>\n          }\n          name=\"wechatService\"\n          rules={[{ required: true, message: '请输入微信客服号' }]}\n        >\n          <Input\n            placeholder=\"请输入微信客服号\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              微信客服二维码\n            </Text>\n          }\n          name=\"wechatQrCode\"\n          rules={[{ required: true, message: '请输入微信客服二维码链接' }]}\n        >\n          <Input\n            placeholder=\"请输入微信客服二维码链接\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              客服电话\n            </Text>\n          }\n          name=\"servicePhone\"\n        >\n          <Input\n            placeholder=\"请输入客服电话\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              客服邮箱\n            </Text>\n          }\n          name=\"serviceEmail\"\n        >\n          <Input\n            placeholder=\"请输入客服邮箱\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              工作时间\n            </Text>\n          }\n          name=\"workingHours\"\n        >\n          <Input\n            placeholder=\"例如：周一至周五 9:00-18:00\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 公告弹出选项卡\n  const AnnouncementTab = () => (\n    <Card>\n      <Title \n        level={3} \n        style={{ \n          color: '#666666', \n          fontWeight: 'bold', \n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        公告弹出\n      </Title>\n      \n      <Form\n        form={announcementForm}\n        layout=\"vertical\"\n        onFinish={handleAnnouncementSave}\n        style={{ maxWidth: 600 }}\n      >\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              公告标题\n            </Text>\n          }\n          name=\"title\"\n          rules={[{ required: true, message: '请输入公告标题' }]}\n        >\n          <Input\n            placeholder=\"请输入公告标题\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              公告内容\n            </Text>\n          }\n          name=\"content\"\n          rules={[{ required: true, message: '请输入公告内容' }]}\n        >\n          <TextArea\n            rows={6}\n            placeholder=\"请输入公告内容\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              是否启用\n            </Text>\n          }\n          name=\"enabled\"\n          valuePropName=\"checked\"\n        >\n          <Button type=\"primary\" ghost>\n            启用公告弹出\n          </Button>\n        </Form.Item>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 隐私政策选项卡\n  const PrivacyPolicyTab = () => (\n    <Card>\n      <Title \n        level={3} \n        style={{ \n          color: '#666666', \n          fontWeight: 'bold', \n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        隐私政策\n      </Title>\n      \n      <Form\n        form={privacyForm}\n        layout=\"vertical\"\n        onFinish={handlePrivacySave}\n        style={{ maxWidth: 800 }}\n      >\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              隐私政策标题\n            </Text>\n          }\n          name=\"title\"\n          rules={[{ required: true, message: '请输入隐私政策标题' }]}\n        >\n          <Input\n            placeholder=\"请输入隐私政策标题\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              隐私政策内容\n            </Text>\n          }\n          name=\"content\"\n          rules={[{ required: true, message: '请输入隐私政策内容' }]}\n        >\n          <TextArea\n            rows={12}\n            placeholder=\"请输入隐私政策内容\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              生效日期\n            </Text>\n          }\n          name=\"effectiveDate\"\n        >\n          <Input\n            placeholder=\"例如：2024年1月1日\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 用户协议选项卡\n  const UserAgreementTab = () => (\n    <Card>\n      <Title\n        level={3}\n        style={{\n          color: '#666666',\n          fontWeight: 'bold',\n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        用户协议\n      </Title>\n      <Text style={{ color: '#999999', fontSize: '14px', display: 'block', marginBottom: 24 }}>\n        配置应用的用户服务协议内容，用户在注册或使用服务时需要同意的条款。\n      </Text>\n\n      <Form\n        form={userAgreementForm}\n        layout=\"vertical\"\n        onFinish={handleUserAgreementSave}\n        style={{ maxWidth: '800px' }}\n      >\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              协议标题\n            </Text>\n          }\n          name=\"title\"\n          rules={[{ required: true, message: '请输入协议标题' }]}\n        >\n          <Input\n            placeholder=\"例如：用户服务协议\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              协议内容\n            </Text>\n          }\n          name=\"content\"\n          rules={[{ required: true, message: '请输入协议内容' }]}\n        >\n          <TextArea\n            rows={12}\n            placeholder=\"请输入详细的用户协议内容...\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '12px',\n              fontSize: '14px',\n              lineHeight: '1.6',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item\n          label={\n            <Text style={{ color: '#666666', fontSize: '14px' }}>\n              生效日期\n            </Text>\n          }\n          name=\"effectiveDate\"\n        >\n          <Input\n            placeholder=\"例如：2024年1月1日\"\n            style={{\n              backgroundColor: '#ffffff',\n              border: '1px solid #d9d9d9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n            }}\n          />\n        </Form.Item>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 轮播图选项卡\n  const BannerTab = () => (\n    <Card>\n      <Title\n        level={3}\n        style={{\n          color: '#666666',\n          fontWeight: 'bold',\n          marginBottom: 24,\n          fontSize: '20px'\n        }}\n      >\n        轮播图配置\n      </Title>\n      <Text style={{ color: '#999999', fontSize: '14px', display: 'block', marginBottom: 24 }}>\n        配置应用首页的轮播图内容，支持3张轮播图片和相关信息。\n      </Text>\n\n      <Form\n        form={bannerForm}\n        layout=\"vertical\"\n        onFinish={handleBannerSave}\n        style={{ maxWidth: '800px' }}\n      >\n        {/* 轮播图1 */}\n        <div style={{ marginBottom: 32, padding: 16, border: '1px solid #f0f0f0', borderRadius: 8 }}>\n          <Title level={5} style={{ color: '#666666', marginBottom: 16 }}>轮播图 1</Title>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>图片URL</Text>}\n            name={['banner1', 'imageUrl']}\n          >\n            <Input placeholder=\"轮播图片链接（可选）\" />\n          </Form.Item>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>跳转链接</Text>}\n            name={['banner1', 'linkUrl']}\n          >\n            <Input placeholder=\"点击跳转的链接（可选）\" />\n          </Form.Item>\n        </div>\n\n        {/* 轮播图2 */}\n        <div style={{ marginBottom: 32, padding: 16, border: '1px solid #f0f0f0', borderRadius: 8 }}>\n          <Title level={5} style={{ color: '#666666', marginBottom: 16 }}>轮播图 2</Title>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>图片URL</Text>}\n            name={['banner2', 'imageUrl']}\n          >\n            <Input placeholder=\"轮播图片链接（可选）\" />\n          </Form.Item>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>跳转链接</Text>}\n            name={['banner2', 'linkUrl']}\n          >\n            <Input placeholder=\"点击跳转的链接（可选）\" />\n          </Form.Item>\n        </div>\n\n        {/* 轮播图3 */}\n        <div style={{ marginBottom: 32, padding: 16, border: '1px solid #f0f0f0', borderRadius: 8 }}>\n          <Title level={5} style={{ color: '#666666', marginBottom: 16 }}>轮播图 3</Title>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>图片URL</Text>}\n            name={['banner3', 'imageUrl']}\n          >\n            <Input placeholder=\"轮播图片链接（可选）\" />\n          </Form.Item>\n\n          <Form.Item\n            label={<Text style={{ color: '#666666', fontSize: '14px' }}>跳转链接</Text>}\n            name={['banner3', 'linkUrl']}\n          >\n            <Input placeholder=\"点击跳转的链接（可选）\" />\n          </Form.Item>\n        </div>\n\n        <Form.Item style={{ marginTop: 32 }}>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            loading={loading}\n            icon={<SaveOutlined />}\n            size=\"large\"\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n\n  // 选项卡配置\n  const tabItems: TabsProps['items'] = [\n    {\n      key: 'customerService',\n      label: '客服设置',\n      children: <CustomerServiceTab />,\n    },\n    {\n      key: 'announcement',\n      label: '公告弹出',\n      children: <AnnouncementTab />,\n    },\n    {\n      key: 'privacy',\n      label: '隐私政策',\n      children: <PrivacyPolicyTab />,\n    },\n    {\n      key: 'userAgreement',\n      label: '用户协议',\n      children: <UserAgreementTab />,\n    },\n    {\n      key: 'banner',\n      label: '轮播图',\n      children: <BannerTab />,\n    },\n  ];\n\n  if (dataLoading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: '16px' }}>加载配置数据中...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Tabs\n        defaultActiveKey=\"customerService\"\n        items={tabItems}\n        size=\"large\"\n        style={{\n          background: '#ffffff',\n          borderRadius: '8px',\n          padding: '16px',\n        }}\n      />\n    </div>\n  );\n};\n\nexport default AppConfig;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,IAAI,QACC,MAAM;AACb,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGR,UAAU;AAClC,MAAM;EAAES;AAAS,CAAC,GAAGX,KAAK;;AAE1B;AACA,MAAMY,YAAY,GAAG,8BAA8B;AAEnD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,mBAAmB,CAAC,GAAGhB,IAAI,CAACiB,OAAO,CAAC,CAAC;EAC5C,MAAM,CAACC,gBAAgB,CAAC,GAAGlB,IAAI,CAACiB,OAAO,CAAC,CAAC;EACzC,MAAM,CAACE,WAAW,CAAC,GAAGnB,IAAI,CAACiB,OAAO,CAAC,CAAC;EACpC,MAAM,CAACG,iBAAiB,CAAC,GAAGpB,IAAI,CAACiB,OAAO,CAAC,CAAC;EAC1C,MAAM,CAACI,UAAU,CAAC,GAAGrB,IAAI,CAACiB,OAAO,CAAC,CAAC;EACnC,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM8B,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BD,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMpB,KAAK,CAACqB,GAAG,CAAC,GAAGf,YAAY,cAAc,CAAC;MAC/D,IAAIc,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEC,gBAAgB;UAAEC,YAAY;UAAEC,cAAc;UAAEC,cAAc;UAAEC;QAAO,CAAC,GAAGR,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAErG;QACAb,mBAAmB,CAACoB,cAAc,CAACL,gBAAgB,IAAI,CAAC,CAAC,CAAC;QAC1Db,gBAAgB,CAACkB,cAAc,CAACJ,YAAY,IAAI,CAAC,CAAC,CAAC;QACnDb,WAAW,CAACiB,cAAc,CAACH,cAAc,IAAI,CAAC,CAAC,CAAC;QAChDb,iBAAiB,CAACgB,cAAc,CAACF,cAAc,IAAI,CAAC,CAAC,CAAC;;QAEtD;QACAG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,MAAM,CAAC;QAC/B,IAAIA,MAAM,EAAE;UACV,MAAMI,cAAc,GAAG;YACrBC,OAAO,EAAE;cACPC,QAAQ,EAAEN,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE;cAC1CO,OAAO,EAAEP,MAAM,CAAC,iBAAiB,CAAC,IAAI;YACxC,CAAC;YACDQ,OAAO,EAAE;cACPF,QAAQ,EAAEN,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE;cAC1CO,OAAO,EAAEP,MAAM,CAAC,iBAAiB,CAAC,IAAI;YACxC,CAAC;YACDS,OAAO,EAAE;cACPH,QAAQ,EAAEN,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE;cAC1CO,OAAO,EAAEP,MAAM,CAAC,iBAAiB,CAAC,IAAI;YACxC;UACF,CAAC;UACDE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,cAAc,CAAC;UACvClB,UAAU,CAACe,cAAc,CAACG,cAAc,CAAC;QAC3C;MACF,CAAC,MAAM;QACLnC,OAAO,CAACyC,KAAK,CAAC,SAAS,GAAGlB,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAClD;IACF,CAAC,CAAC,OAAOyC,KAAU,EAAE;MACnBR,OAAO,CAACQ,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BzC,OAAO,CAACyC,KAAK,CAAC,gBAAgB,CAAC;IACjC,CAAC,SAAS;MACRpB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACd6B,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoB,yBAAyB,GAAG,MAAOC,MAAW,IAAK;IACvDxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACyC,GAAG,CAAC,GAAGnC,YAAY,+BAA+B,EAAEkC,MAAM,CAAC;MACxF,IAAIpB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL1B,OAAO,CAACyC,KAAK,CAAC,OAAO,GAAGlB,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOyC,KAAU,EAAE;MACnBR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCzC,OAAO,CAACyC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,sBAAsB,GAAG,MAAOF,MAAW,IAAK;IACpDxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACyC,GAAG,CAAC,GAAGnC,YAAY,2BAA2B,EAAEkC,MAAM,CAAC;MACpF,IAAIpB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL1B,OAAO,CAACyC,KAAK,CAAC,OAAO,GAAGlB,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOyC,KAAU,EAAE;MACnBR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCzC,OAAO,CAACyC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2B,iBAAiB,GAAG,MAAOH,MAAW,IAAK;IAC/CxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACyC,GAAG,CAAC,GAAGnC,YAAY,6BAA6B,EAAEkC,MAAM,CAAC;MACtF,IAAIpB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL1B,OAAO,CAACyC,KAAK,CAAC,OAAO,GAAGlB,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOyC,KAAU,EAAE;MACnBR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCzC,OAAO,CAACyC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,uBAAuB,GAAG,MAAOJ,MAAW,IAAK;IACrDxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpB,KAAK,CAACyC,GAAG,CAAC,GAAGnC,YAAY,6BAA6B,EAAEkC,MAAM,CAAC;MACtF,IAAIpB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL1B,OAAO,CAACyC,KAAK,CAAC,OAAO,GAAGlB,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOyC,KAAU,EAAE;MACnBR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCzC,OAAO,CAACyC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6B,gBAAgB,GAAG,MAAOL,MAAW,IAAK;IAC9CxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAA8B,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA;MACFrB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAES,MAAM,CAAC;;MAE/B;MACA,MAAMY,UAAU,GAAG;QACjB,kBAAkB,EAAE,EAAAN,cAAA,GAAAN,MAAM,CAACP,OAAO,cAAAa,cAAA,uBAAdA,cAAA,CAAgBZ,QAAQ,KAAI,EAAE;QAClD,iBAAiB,EAAE,EAAAa,eAAA,GAAAP,MAAM,CAACP,OAAO,cAAAc,eAAA,uBAAdA,eAAA,CAAgBZ,OAAO,KAAI,EAAE;QAChD,kBAAkB,EAAE,EAAAa,eAAA,GAAAR,MAAM,CAACJ,OAAO,cAAAY,eAAA,uBAAdA,eAAA,CAAgBd,QAAQ,KAAI,EAAE;QAClD,iBAAiB,EAAE,EAAAe,eAAA,GAAAT,MAAM,CAACJ,OAAO,cAAAa,eAAA,uBAAdA,eAAA,CAAgBd,OAAO,KAAI,EAAE;QAChD,kBAAkB,EAAE,EAAAe,eAAA,GAAAV,MAAM,CAACH,OAAO,cAAAa,eAAA,uBAAdA,eAAA,CAAgBhB,QAAQ,KAAI,EAAE;QAClD,iBAAiB,EAAE,EAAAiB,eAAA,GAAAX,MAAM,CAACH,OAAO,cAAAc,eAAA,uBAAdA,eAAA,CAAgBhB,OAAO,KAAI;MAChD,CAAC;MAEDL,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqB,UAAU,CAAC;MAErC,MAAMhC,QAAQ,GAAG,MAAMpB,KAAK,CAACyC,GAAG,CAAC,GAAGnC,YAAY,qBAAqB,EAAE8C,UAAU,CAAC;MAClFtB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEX,QAAQ,CAACE,IAAI,CAAC;MAEnC,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,OAAO,CAAC0B,OAAO,CAAC,WAAW,CAAC;QAC5B;QACAJ,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLW,OAAO,CAACQ,KAAK,CAAC,SAAS,EAAElB,QAAQ,CAACE,IAAI,CAAC;QACvCzB,OAAO,CAACyC,KAAK,CAAC,OAAO,IAAIlB,QAAQ,CAACE,IAAI,CAACzB,OAAO,IAAI,MAAM,CAAC,CAAC;QAC1D,IAAIuB,QAAQ,CAACE,IAAI,CAAC+B,MAAM,EAAE;UACxBvB,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAElB,QAAQ,CAACE,IAAI,CAAC+B,MAAM,CAAC;QAC9C;MACF;IACF,CAAC,CAAC,OAAOf,KAAU,EAAE;MAAA,IAAAgB,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACnB1B,OAAO,CAACQ,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCR,OAAO,CAACQ,KAAK,CAAC,OAAO,GAAAgB,eAAA,GAAEhB,KAAK,CAAClB,QAAQ,cAAAkC,eAAA,uBAAdA,eAAA,CAAgBhC,IAAI,CAAC;MAC5CzB,OAAO,CAACyC,KAAK,CAAC,OAAO,IAAI,EAAAiB,gBAAA,GAAAjB,KAAK,CAAClB,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjC,IAAI,cAAAkC,qBAAA,uBAApBA,qBAAA,CAAsB3D,OAAO,KAAIyC,KAAK,CAACzC,OAAO,IAAI,KAAK,CAAC,CAAC;IACpF,CAAC,SAAS;MACRmB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,kBAAkB,GAAGA,CAAA,kBACzBvD,OAAA,CAACX,IAAI;IAAAmE,QAAA,gBACHxD,OAAA,CAACC,KAAK;MACJwD,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERlE,OAAA,CAACT,IAAI;MACH4E,IAAI,EAAE5D,mBAAoB;MAC1B6D,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAEhC,yBAA0B;MACpCqB,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAI,CAAE;MAAAd,QAAA,gBAEzBxD,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,eAAe;QACpBC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEhF,OAAO,EAAE;QAAW,CAAC,CAAE;QAAA6D,QAAA,eAEjDxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,kDAAU;UACtBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,cAAc;QACnBC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEhF,OAAO,EAAE;QAAe,CAAC,CAAE;QAAA6D,QAAA,eAErDxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,0EAAc;UAC1BlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,cAAc;QAAAjB,QAAA,eAEnBxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,4CAAS;UACrBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,cAAc;QAAAjB,QAAA,eAEnBxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,4CAAS;UACrBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,cAAc;QAAAjB,QAAA,eAEnBxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,6DAAqB;UACjClB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCxD,OAAA,CAACP,MAAM;UACLyF,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBtE,OAAO,EAAEA,OAAQ;UACjBuE,IAAI,eAAEpF,OAAA,CAACH,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAMoB,eAAe,GAAGA,CAAA,kBACtBtF,OAAA,CAACX,IAAI;IAAAmE,QAAA,gBACHxD,OAAA,CAACC,KAAK;MACJwD,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERlE,OAAA,CAACT,IAAI;MACH4E,IAAI,EAAE1D,gBAAiB;MACvB2D,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAE7B,sBAAuB;MACjCkB,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAI,CAAE;MAAAd,QAAA,gBAEzBxD,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEhF,OAAO,EAAE;QAAU,CAAC,CAAE;QAAA6D,QAAA,eAEhDxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,4CAAS;UACrBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,SAAS;QACdC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEhF,OAAO,EAAE;QAAU,CAAC,CAAE;QAAA6D,QAAA,eAEhDxD,OAAA,CAACG,QAAQ;UACPoF,IAAI,EAAE,CAAE;UACRX,WAAW,EAAC,4CAAS;UACrBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,SAAS;QACde,aAAa,EAAC,SAAS;QAAAhC,QAAA,eAEvBxD,OAAA,CAACP,MAAM;UAACyF,IAAI,EAAC,SAAS;UAACO,KAAK;UAAAjC,QAAA,EAAC;QAE7B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCxD,OAAA,CAACP,MAAM;UACLyF,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBtE,OAAO,EAAEA,OAAQ;UACjBuE,IAAI,eAAEpF,OAAA,CAACH,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAMwB,gBAAgB,GAAGA,CAAA,kBACvB1F,OAAA,CAACX,IAAI;IAAAmE,QAAA,gBACHxD,OAAA,CAACC,KAAK;MACJwD,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERlE,OAAA,CAACT,IAAI;MACH4E,IAAI,EAAEzD,WAAY;MAClB0D,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAE5B,iBAAkB;MAC5BiB,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAI,CAAE;MAAAd,QAAA,gBAEzBxD,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEhF,OAAO,EAAE;QAAY,CAAC,CAAE;QAAA6D,QAAA,eAElDxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,wDAAW;UACvBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,SAAS;QACdC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEhF,OAAO,EAAE;QAAY,CAAC,CAAE;QAAA6D,QAAA,eAElDxD,OAAA,CAACG,QAAQ;UACPoF,IAAI,EAAE,EAAG;UACTX,WAAW,EAAC,wDAAW;UACvBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,eAAe;QAAAjB,QAAA,eAEpBxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,4CAAc;UAC1BlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCxD,OAAA,CAACP,MAAM;UACLyF,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBtE,OAAO,EAAEA,OAAQ;UACjBuE,IAAI,eAAEpF,OAAA,CAACH,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAMyB,gBAAgB,GAAGA,CAAA,kBACvB3F,OAAA,CAACX,IAAI;IAAAmE,QAAA,gBACHxD,OAAA,CAACC,KAAK;MACJwD,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRlE,OAAA,CAACE,IAAI;MAACwD,KAAK,EAAE;QAAEC,KAAK,EAAE,SAAS;QAAEG,QAAQ,EAAE,MAAM;QAAE8B,OAAO,EAAE,OAAO;QAAE/B,YAAY,EAAE;MAAG,CAAE;MAAAL,QAAA,EAAC;IAEzF;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPlE,OAAA,CAACT,IAAI;MACH4E,IAAI,EAAExD,iBAAkB;MACxByD,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAE3B,uBAAwB;MAClCgB,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAQ,CAAE;MAAAd,QAAA,gBAE7BxD,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEhF,OAAO,EAAE;QAAU,CAAC,CAAE;QAAA6D,QAAA,eAEhDxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,wDAAW;UACvBlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,SAAS;QACdC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEhF,OAAO,EAAE;QAAU,CAAC,CAAE;QAAA6D,QAAA,eAEhDxD,OAAA,CAACG,QAAQ;UACPoF,IAAI,EAAE,EAAG;UACTX,WAAW,EAAC,6EAAiB;UAC7BlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,MAAM;YACflB,QAAQ,EAAE,MAAM;YAChB+B,UAAU,EAAE;UACd;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QACRC,KAAK,eACHxE,OAAA,CAACE,IAAI;UAACwD,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAErD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QACDO,IAAI,EAAC,eAAe;QAAAjB,QAAA,eAEpBxD,OAAA,CAACR,KAAK;UACJoF,WAAW,EAAC,4CAAc;UAC1BlB,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE;UACX;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCxD,OAAA,CAACP,MAAM;UACLyF,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBtE,OAAO,EAAEA,OAAQ;UACjBuE,IAAI,eAAEpF,OAAA,CAACH,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAM4B,SAAS,GAAGA,CAAA,kBAChB9F,OAAA,CAACX,IAAI;IAAAmE,QAAA,gBACHxD,OAAA,CAACC,KAAK;MACJwD,KAAK,EAAE,CAAE;MACTC,KAAK,EAAE;QACLC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAN,QAAA,EACH;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRlE,OAAA,CAACE,IAAI;MAACwD,KAAK,EAAE;QAAEC,KAAK,EAAE,SAAS;QAAEG,QAAQ,EAAE,MAAM;QAAE8B,OAAO,EAAE,OAAO;QAAE/B,YAAY,EAAE;MAAG,CAAE;MAAAL,QAAA,EAAC;IAEzF;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPlE,OAAA,CAACT,IAAI;MACH4E,IAAI,EAAEvD,UAAW;MACjBwD,MAAM,EAAC,UAAU;MACjBC,QAAQ,EAAE1B,gBAAiB;MAC3Be,KAAK,EAAE;QAAEY,QAAQ,EAAE;MAAQ,CAAE;MAAAd,QAAA,gBAG7BxD,OAAA;QAAK0D,KAAK,EAAE;UAAEG,YAAY,EAAE,EAAE;UAAEmB,OAAO,EAAE,EAAE;UAAEF,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC1FxD,OAAA,CAACC,KAAK;UAACwD,KAAK,EAAE,CAAE;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEE,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAE7ElE,OAAA,CAACT,IAAI,CAACgF,IAAI;UACRC,KAAK,eAAExE,OAAA,CAACE,IAAI;YAACwD,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACzEO,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAE;UAAAjB,QAAA,eAE9BxD,OAAA,CAACR,KAAK;YAACoF,WAAW,EAAC;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;UACRC,KAAK,eAAExE,OAAA,CAACE,IAAI;YAACwD,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACxEO,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAE;UAAAjB,QAAA,eAE7BxD,OAAA,CAACR,KAAK;YAACoF,WAAW,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGNlE,OAAA;QAAK0D,KAAK,EAAE;UAAEG,YAAY,EAAE,EAAE;UAAEmB,OAAO,EAAE,EAAE;UAAEF,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC1FxD,OAAA,CAACC,KAAK;UAACwD,KAAK,EAAE,CAAE;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEE,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAE7ElE,OAAA,CAACT,IAAI,CAACgF,IAAI;UACRC,KAAK,eAAExE,OAAA,CAACE,IAAI;YAACwD,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACzEO,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAE;UAAAjB,QAAA,eAE9BxD,OAAA,CAACR,KAAK;YAACoF,WAAW,EAAC;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;UACRC,KAAK,eAAExE,OAAA,CAACE,IAAI;YAACwD,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACxEO,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAE;UAAAjB,QAAA,eAE7BxD,OAAA,CAACR,KAAK;YAACoF,WAAW,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGNlE,OAAA;QAAK0D,KAAK,EAAE;UAAEG,YAAY,EAAE,EAAE;UAAEmB,OAAO,EAAE,EAAE;UAAEF,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC1FxD,OAAA,CAACC,KAAK;UAACwD,KAAK,EAAE,CAAE;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEE,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAE7ElE,OAAA,CAACT,IAAI,CAACgF,IAAI;UACRC,KAAK,eAAExE,OAAA,CAACE,IAAI;YAACwD,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACzEO,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAE;UAAAjB,QAAA,eAE9BxD,OAAA,CAACR,KAAK;YAACoF,WAAW,EAAC;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAEZlE,OAAA,CAACT,IAAI,CAACgF,IAAI;UACRC,KAAK,eAAExE,OAAA,CAACE,IAAI;YAACwD,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEG,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UACxEO,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,CAAE;UAAAjB,QAAA,eAE7BxD,OAAA,CAACR,KAAK;YAACoF,WAAW,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENlE,OAAA,CAACT,IAAI,CAACgF,IAAI;QAACb,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAClCxD,OAAA,CAACP,MAAM;UACLyF,IAAI,EAAC,SAAS;UACdC,QAAQ,EAAC,QAAQ;UACjBtE,OAAO,EAAEA,OAAQ;UACjBuE,IAAI,eAAEpF,OAAA,CAACH,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAA7B,QAAA,EACb;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;;EAED;EACA,MAAM6B,QAA4B,GAAG,CACnC;IACEC,GAAG,EAAE,iBAAiB;IACtBxB,KAAK,EAAE,MAAM;IACbhB,QAAQ,eAAExD,OAAA,CAACuD,kBAAkB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACjC,CAAC,EACD;IACE8B,GAAG,EAAE,cAAc;IACnBxB,KAAK,EAAE,MAAM;IACbhB,QAAQ,eAAExD,OAAA,CAACsF,eAAe;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9B,CAAC,EACD;IACE8B,GAAG,EAAE,SAAS;IACdxB,KAAK,EAAE,MAAM;IACbhB,QAAQ,eAAExD,OAAA,CAAC0F,gBAAgB;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC/B,CAAC,EACD;IACE8B,GAAG,EAAE,eAAe;IACpBxB,KAAK,EAAE,MAAM;IACbhB,QAAQ,eAAExD,OAAA,CAAC2F,gBAAgB;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC/B,CAAC,EACD;IACE8B,GAAG,EAAE,QAAQ;IACbxB,KAAK,EAAE,KAAK;IACZhB,QAAQ,eAAExD,OAAA,CAAC8F,SAAS;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACxB,CAAC,CACF;EAED,IAAInD,WAAW,EAAE;IACf,oBACEf,OAAA;MAAK0D,KAAK,EAAE;QAAEuC,SAAS,EAAE,QAAQ;QAAEjB,OAAO,EAAE;MAAO,CAAE;MAAAxB,QAAA,gBACnDxD,OAAA,CAACJ,IAAI;QAACyF,IAAI,EAAC;MAAO;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBlE,OAAA;QAAK0D,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAO,CAAE;QAAAzB,QAAA,EAAC;MAAU;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACElE,OAAA;IAAAwD,QAAA,eACExD,OAAA,CAACV,IAAI;MACH4G,gBAAgB,EAAC,iBAAiB;MAClCC,KAAK,EAAEJ,QAAS;MAChBV,IAAI,EAAC,OAAO;MACZ3B,KAAK,EAAE;QACL0C,UAAU,EAAE,SAAS;QACrBrB,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE;MACX;IAAE;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC5D,EAAA,CAruBID,SAAmB;EAAA,QACOd,IAAI,CAACiB,OAAO,EACfjB,IAAI,CAACiB,OAAO,EACjBjB,IAAI,CAACiB,OAAO,EACNjB,IAAI,CAACiB,OAAO,EACnBjB,IAAI,CAACiB,OAAO;AAAA;AAAA6F,EAAA,GAL7BhG,SAAmB;AAuuBzB,eAAeA,SAAS;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
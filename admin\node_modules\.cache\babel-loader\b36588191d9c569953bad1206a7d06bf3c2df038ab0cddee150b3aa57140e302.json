{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genStepsProgressStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    iconSize,\n    iconSizeSM,\n    processIconColor,\n    marginXXS,\n    lineWidthBold,\n    lineWidth,\n    paddingXXS\n  } = token;\n  const progressSize = token.calc(iconSize).add(token.calc(lineWidthBold).mul(4).equal()).equal();\n  const progressSizeSM = token.calc(iconSizeSM).add(token.calc(token.lineWidth).mul(4).equal()).equal();\n  return {\n    [`&${componentCls}-with-progress`]: {\n      [`${componentCls}-item`]: {\n        paddingTop: paddingXXS,\n        [`&-process ${componentCls}-item-container ${componentCls}-item-icon ${componentCls}-icon`]: {\n          color: processIconColor\n        }\n      },\n      [`&${componentCls}-vertical > ${componentCls}-item `]: {\n        paddingInlineStart: paddingXXS,\n        [`> ${componentCls}-item-container > ${componentCls}-item-tail`]: {\n          top: marginXXS,\n          insetInlineStart: token.calc(iconSize).div(2).sub(lineWidth).add(paddingXXS).equal()\n        }\n      },\n      [`&, &${componentCls}-small`]: {\n        [`&${componentCls}-horizontal ${componentCls}-item:first-child`]: {\n          paddingBottom: paddingXXS,\n          paddingInlineStart: paddingXXS\n        }\n      },\n      [`&${componentCls}-small${componentCls}-vertical > ${componentCls}-item > ${componentCls}-item-container > ${componentCls}-item-tail`]: {\n        insetInlineStart: token.calc(iconSizeSM).div(2).sub(lineWidth).add(paddingXXS).equal()\n      },\n      [`&${componentCls}-label-vertical ${componentCls}-item ${componentCls}-item-tail`]: {\n        top: token.calc(iconSize).div(2).add(paddingXXS).equal()\n      },\n      [`${componentCls}-item-icon`]: {\n        position: 'relative',\n        [`${antCls}-progress`]: {\n          position: 'absolute',\n          insetInlineStart: '50%',\n          top: '50%',\n          transform: 'translate(-50%, -50%)',\n          '&-inner': {\n            width: `${unit(progressSize)} !important`,\n            height: `${unit(progressSize)} !important`\n          }\n        }\n      },\n      // ============================== Small size ==============================\n      [`&${componentCls}-small`]: {\n        [`&${componentCls}-label-vertical ${componentCls}-item ${componentCls}-item-tail`]: {\n          top: token.calc(iconSizeSM).div(2).add(paddingXXS).equal()\n        },\n        [`${componentCls}-item-icon ${antCls}-progress-inner`]: {\n          width: `${unit(progressSizeSM)} !important`,\n          height: `${unit(progressSizeSM)} !important`\n        }\n      }\n    }\n  };\n};\nexport default genStepsProgressStyle;", "map": {"version": 3, "names": ["unit", "genStepsProgressStyle", "token", "antCls", "componentCls", "iconSize", "iconSizeSM", "processIconColor", "marginXXS", "lineWidthBold", "lineWidth", "paddingXXS", "progressSize", "calc", "add", "mul", "equal", "progressSizeSM", "paddingTop", "color", "paddingInlineStart", "top", "insetInlineStart", "div", "sub", "paddingBottom", "position", "transform", "width", "height"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/steps/style/progress.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genStepsProgressStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    iconSize,\n    iconSizeSM,\n    processIconColor,\n    marginXXS,\n    lineWidthBold,\n    lineWidth,\n    paddingXXS\n  } = token;\n  const progressSize = token.calc(iconSize).add(token.calc(lineWidthBold).mul(4).equal()).equal();\n  const progressSizeSM = token.calc(iconSizeSM).add(token.calc(token.lineWidth).mul(4).equal()).equal();\n  return {\n    [`&${componentCls}-with-progress`]: {\n      [`${componentCls}-item`]: {\n        paddingTop: paddingXXS,\n        [`&-process ${componentCls}-item-container ${componentCls}-item-icon ${componentCls}-icon`]: {\n          color: processIconColor\n        }\n      },\n      [`&${componentCls}-vertical > ${componentCls}-item `]: {\n        paddingInlineStart: paddingXXS,\n        [`> ${componentCls}-item-container > ${componentCls}-item-tail`]: {\n          top: marginXXS,\n          insetInlineStart: token.calc(iconSize).div(2).sub(lineWidth).add(paddingXXS).equal()\n        }\n      },\n      [`&, &${componentCls}-small`]: {\n        [`&${componentCls}-horizontal ${componentCls}-item:first-child`]: {\n          paddingBottom: paddingXXS,\n          paddingInlineStart: paddingXXS\n        }\n      },\n      [`&${componentCls}-small${componentCls}-vertical > ${componentCls}-item > ${componentCls}-item-container > ${componentCls}-item-tail`]: {\n        insetInlineStart: token.calc(iconSizeSM).div(2).sub(lineWidth).add(paddingXXS).equal()\n      },\n      [`&${componentCls}-label-vertical ${componentCls}-item ${componentCls}-item-tail`]: {\n        top: token.calc(iconSize).div(2).add(paddingXXS).equal()\n      },\n      [`${componentCls}-item-icon`]: {\n        position: 'relative',\n        [`${antCls}-progress`]: {\n          position: 'absolute',\n          insetInlineStart: '50%',\n          top: '50%',\n          transform: 'translate(-50%, -50%)',\n          '&-inner': {\n            width: `${unit(progressSize)} !important`,\n            height: `${unit(progressSize)} !important`\n          }\n        }\n      },\n      // ============================== Small size ==============================\n      [`&${componentCls}-small`]: {\n        [`&${componentCls}-label-vertical ${componentCls}-item ${componentCls}-item-tail`]: {\n          top: token.calc(iconSizeSM).div(2).add(paddingXXS).equal()\n        },\n        [`${componentCls}-item-icon ${antCls}-progress-inner`]: {\n          width: `${unit(progressSizeSM)} !important`,\n          height: `${unit(progressSizeSM)} !important`\n        }\n      }\n    }\n  };\n};\nexport default genStepsProgressStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,qBAAqB,GAAGC,KAAK,IAAI;EACrC,MAAM;IACJC,MAAM;IACNC,YAAY;IACZC,QAAQ;IACRC,UAAU;IACVC,gBAAgB;IAChBC,SAAS;IACTC,aAAa;IACbC,SAAS;IACTC;EACF,CAAC,GAAGT,KAAK;EACT,MAAMU,YAAY,GAAGV,KAAK,CAACW,IAAI,CAACR,QAAQ,CAAC,CAACS,GAAG,CAACZ,KAAK,CAACW,IAAI,CAACJ,aAAa,CAAC,CAACM,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,CAAC;EAC/F,MAAMC,cAAc,GAAGf,KAAK,CAACW,IAAI,CAACP,UAAU,CAAC,CAACQ,GAAG,CAACZ,KAAK,CAACW,IAAI,CAACX,KAAK,CAACQ,SAAS,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,CAAC;EACrG,OAAO;IACL,CAAC,IAAIZ,YAAY,gBAAgB,GAAG;MAClC,CAAC,GAAGA,YAAY,OAAO,GAAG;QACxBc,UAAU,EAAEP,UAAU;QACtB,CAAC,aAAaP,YAAY,mBAAmBA,YAAY,cAAcA,YAAY,OAAO,GAAG;UAC3Fe,KAAK,EAAEZ;QACT;MACF,CAAC;MACD,CAAC,IAAIH,YAAY,eAAeA,YAAY,QAAQ,GAAG;QACrDgB,kBAAkB,EAAET,UAAU;QAC9B,CAAC,KAAKP,YAAY,qBAAqBA,YAAY,YAAY,GAAG;UAChEiB,GAAG,EAAEb,SAAS;UACdc,gBAAgB,EAAEpB,KAAK,CAACW,IAAI,CAACR,QAAQ,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAACd,SAAS,CAAC,CAACI,GAAG,CAACH,UAAU,CAAC,CAACK,KAAK,CAAC;QACrF;MACF,CAAC;MACD,CAAC,OAAOZ,YAAY,QAAQ,GAAG;QAC7B,CAAC,IAAIA,YAAY,eAAeA,YAAY,mBAAmB,GAAG;UAChEqB,aAAa,EAAEd,UAAU;UACzBS,kBAAkB,EAAET;QACtB;MACF,CAAC;MACD,CAAC,IAAIP,YAAY,SAASA,YAAY,eAAeA,YAAY,WAAWA,YAAY,qBAAqBA,YAAY,YAAY,GAAG;QACtIkB,gBAAgB,EAAEpB,KAAK,CAACW,IAAI,CAACP,UAAU,CAAC,CAACiB,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAACd,SAAS,CAAC,CAACI,GAAG,CAACH,UAAU,CAAC,CAACK,KAAK,CAAC;MACvF,CAAC;MACD,CAAC,IAAIZ,YAAY,mBAAmBA,YAAY,SAASA,YAAY,YAAY,GAAG;QAClFiB,GAAG,EAAEnB,KAAK,CAACW,IAAI,CAACR,QAAQ,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC,CAACT,GAAG,CAACH,UAAU,CAAC,CAACK,KAAK,CAAC;MACzD,CAAC;MACD,CAAC,GAAGZ,YAAY,YAAY,GAAG;QAC7BsB,QAAQ,EAAE,UAAU;QACpB,CAAC,GAAGvB,MAAM,WAAW,GAAG;UACtBuB,QAAQ,EAAE,UAAU;UACpBJ,gBAAgB,EAAE,KAAK;UACvBD,GAAG,EAAE,KAAK;UACVM,SAAS,EAAE,uBAAuB;UAClC,SAAS,EAAE;YACTC,KAAK,EAAE,GAAG5B,IAAI,CAACY,YAAY,CAAC,aAAa;YACzCiB,MAAM,EAAE,GAAG7B,IAAI,CAACY,YAAY,CAAC;UAC/B;QACF;MACF,CAAC;MACD;MACA,CAAC,IAAIR,YAAY,QAAQ,GAAG;QAC1B,CAAC,IAAIA,YAAY,mBAAmBA,YAAY,SAASA,YAAY,YAAY,GAAG;UAClFiB,GAAG,EAAEnB,KAAK,CAACW,IAAI,CAACP,UAAU,CAAC,CAACiB,GAAG,CAAC,CAAC,CAAC,CAACT,GAAG,CAACH,UAAU,CAAC,CAACK,KAAK,CAAC;QAC3D,CAAC;QACD,CAAC,GAAGZ,YAAY,cAAcD,MAAM,iBAAiB,GAAG;UACtDyB,KAAK,EAAE,GAAG5B,IAAI,CAACiB,cAAc,CAAC,aAAa;UAC3CY,MAAM,EAAE,GAAG7B,IAAI,CAACiB,cAAc,CAAC;QACjC;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAehB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
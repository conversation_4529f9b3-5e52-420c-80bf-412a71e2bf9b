{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nvar uniquePrefix = Math.random().toFixed(5).toString().slice(2);\nvar internalId = 0;\nexport default function useUUID(id) {\n  var _useMergedState = useMergedState(id, {\n      value: id\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    uuid = _useMergedState2[0],\n    setUUID = _useMergedState2[1];\n  React.useEffect(function () {\n    internalId += 1;\n    var newId = process.env.NODE_ENV === 'test' ? 'test' : \"\".concat(uniquePrefix, \"-\").concat(internalId);\n    setUUID(\"rc-menu-uuid-\".concat(newId));\n  }, []);\n  return uuid;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, Form, Input, Select, DatePicker, Row, Col, Statistic, message, Modal, Descriptions } from 'antd';\nimport { SearchOutlined, EyeOutlined, EditOutlined, ExportOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { orderApi } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\n\n// 使用从 types/order.ts 导入的类型，这里定义一个本地接口用于显示\n\n// 订单数据将从API获取，不再使用模拟数据\n\nconst OrderList = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState([]);\n  const [filteredOrders, setFilteredOrders] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    processing: 0,\n    pending_upload: 0,\n    shipped: 0,\n    activated: 0,\n    failed: 0,\n    cancelled: 0,\n    today_orders: 0,\n    week_orders: 0,\n    month_orders: 0\n  });\n  const navigate = useNavigate();\n\n  // 组件加载时获取订单数据\n  useEffect(() => {\n    loadOrders();\n    loadStats();\n  }, []);\n\n  // 转换API数据格式为组件使用的格式\n  const transformOrderData = apiOrder => {\n    return {\n      id: apiOrder.id,\n      orderNo: apiOrder.order_no,\n      customerName: apiOrder.customer_name,\n      customerPhone: apiOrder.customer_phone,\n      customerIdCard: apiOrder.customer_id_card,\n      productName: apiOrder.product_name,\n      operator: apiOrder.operator,\n      deliveryAddress: apiOrder.delivery_address,\n      status: apiOrder.status,\n      priority: apiOrder.priority,\n      logisticsCompany: apiOrder.logistics_company,\n      trackingNumber: apiOrder.tracking_number,\n      shippedAt: apiOrder.shipped_at,\n      estimatedDelivery: apiOrder.estimated_delivery,\n      createdAt: apiOrder.created_at,\n      updatedAt: apiOrder.updated_at\n    };\n  };\n\n  // 加载订单数据\n  const loadOrders = async params => {\n    setLoading(true);\n    try {\n      const queryParams = {\n        page: pagination.current,\n        per_page: pagination.pageSize,\n        ...params\n      };\n      const response = await orderApi.getList(queryParams);\n      const transformedOrders = response.data.data.map(transformOrderData);\n      setOrders(transformedOrders);\n      setFilteredOrders(transformedOrders);\n      setPagination(prev => ({\n        ...prev,\n        total: response.data.total,\n        current: response.data.current_page\n      }));\n    } catch (error) {\n      console.error('获取订单数据失败:', error);\n      message.error('获取订单数据失败');\n      // 如果API失败，设置为空数组\n      setOrders([]);\n      setFilteredOrders([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载统计数据\n  const loadStats = async () => {\n    try {\n      const response = await orderApi.getStats();\n      setStats(response.data);\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      // 如果API失败，使用本地计算的统计数据\n    }\n  };\n\n  // 状态颜色映射\n  const getStatusColor = status => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      pending_upload: 'purple',\n      shipped: 'cyan',\n      activated: 'green',\n      failed: 'red',\n      cancelled: 'default'\n    };\n    return colors[status] || 'default';\n  };\n\n  // 状态文本映射\n  const getStatusText = status => {\n    const texts = {\n      pending: '待处理',\n      processing: '开卡中',\n      pending_upload: '待上传三证',\n      shipped: '已发货',\n      activated: '已激活',\n      failed: '开卡失败',\n      cancelled: '已取消'\n    };\n    return texts[status] || status;\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = priority => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red'\n    };\n    return colors[priority] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = priority => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急'\n    };\n    return texts[priority] || '普通';\n  };\n\n  // 如果API统计数据不可用，使用本地计算的统计数据作为备用\n  const localStats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    pending_upload: filteredOrders.filter(order => order.status === 'pending_upload').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    failed: filteredOrders.filter(order => order.status === 'failed').length,\n    cancelled: filteredOrders.filter(order => order.status === 'cancelled').length\n  };\n\n  // 使用API统计数据，如果不可用则使用本地计算的数据\n  const displayStats = stats.total > 0 ? stats : localStats;\n\n  // 表格列定义\n  const columns = [{\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '订单状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物流信息',\n    key: 'logistics',\n    width: 200,\n    render: (_, record) => {\n      if (record.status === 'shipped' || record.status === 'activated') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 600,\n              fontSize: '12px'\n            },\n            children: record.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#666'\n            },\n            children: record.trackingNumber || '暂无快递单号'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this), record.shippedAt && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#999'\n            },\n            children: [\"\\u53D1\\u8D27: \", record.shippedAt.split(' ')[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: \"\\u672A\\u53D1\\u8D27\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditOrder(record.id),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleSearch = values => {\n    const searchParams = {\n      order_no: values.orderNo,\n      customer_name: values.customerName,\n      operator: values.operator,\n      status: values.status\n    };\n\n    // 重置分页到第一页\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n    loadOrders(searchParams);\n  };\n  const handleRefresh = async () => {\n    await loadOrders();\n    await loadStats();\n    message.success('数据已刷新');\n  };\n  const handleViewOrder = order => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n  const handleEditOrder = id => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#1890ff'\n        },\n        children: \"\\u8BA2\\u5355\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u7BA1\\u7406\\u6240\\u6709\\u8BA2\\u5355\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA2\\u5355\\u6570\",\n            value: stats.total,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\",\n            value: stats.pending,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\",\n            value: stats.pending_upload,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F00\\u5361\\u4E2D\",\n            value: stats.processing,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u53D1\\u8D27\",\n            value: stats.shipped,\n            valueStyle: {\n              color: '#13c2c2'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u6FC0\\u6D3B\",\n            value: stats.activated,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"orderNo\",\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",\n            style: {\n              width: 150\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"customerName\",\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",\n            style: {\n              width: 120\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"operator\",\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u79FB\\u52A8\",\n              children: \"\\u4E2D\\u56FD\\u79FB\\u52A8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u7535\\u4FE1\",\n              children: \"\\u4E2D\\u56FD\\u7535\\u4FE1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u8054\\u901A\",\n              children: \"\\u4E2D\\u56FD\\u8054\\u901A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u5E7F\\u7535\",\n              children: \"\\u4E2D\\u56FD\\u5E7F\\u7535\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending\",\n              children: \"\\u5F85\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"processing\",\n              children: \"\\u5F00\\u5361\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending_upload\",\n              children: \"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"shipped\",\n              children: \"\\u5DF2\\u53D1\\u8D27\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"activated\",\n              children: \"\\u5DF2\\u6FC0\\u6D3B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"failed\",\n              children: \"\\u5F00\\u5361\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"cancelled\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 60\n            }, this),\n            children: \"\\u641C\\u7D22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", filteredOrders.length, \" \\u6761\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleExport,\n            icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 50\n            }, this),\n            children: \"\\u5BFC\\u51FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredOrders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BA2\\u5355\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          span: 2,\n          children: selectedOrder.orderNo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: selectedOrder.customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8054\\u7CFB\\u7535\\u8BDD\",\n          children: selectedOrder.customerPhone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n          span: 2,\n          children: selectedOrder.productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: selectedOrder.operator\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(selectedOrder.status),\n            children: getStatusText(selectedOrder.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 13\n        }, this), (selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7269\\u6D41\\u516C\\u53F8\",\n            children: selectedOrder.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5FEB\\u9012\\u5355\\u53F7\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              code: true,\n              children: selectedOrder.trackingNumber || '暂无'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u53D1\\u8D27\\u65F6\\u95F4\",\n            children: selectedOrder.shippedAt || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u9884\\u8BA1\\u9001\\u8FBE\",\n            children: selectedOrder.estimatedDelivery || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: selectedOrder.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: selectedOrder.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 375,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderList, \"C4aHSdeOMyHo5roTHgJQrRzODU0=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = OrderList;\nexport default OrderList;\nvar _c;\n$RefreshReg$(_c, \"OrderList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Form", "Input", "Select", "DatePicker", "Row", "Col", "Statistic", "message", "Modal", "Descriptions", "SearchOutlined", "EyeOutlined", "EditOutlined", "ExportOutlined", "ReloadOutlined", "useNavigate", "orderApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "RangePicker", "OrderList", "_s", "form", "useForm", "orders", "setOrders", "filteredOrders", "setFilteredOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "detailModalVisible", "setDetailModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "pagination", "setPagination", "current", "pageSize", "total", "stats", "setStats", "pending", "processing", "pending_upload", "shipped", "activated", "failed", "cancelled", "today_orders", "week_orders", "month_orders", "navigate", "loadOrders", "loadStats", "transformOrderData", "apiOrder", "id", "orderNo", "order_no", "customerName", "customer_name", "customerPhone", "customer_phone", "customerIdCard", "customer_id_card", "productName", "product_name", "operator", "deliveryAddress", "delivery_address", "status", "priority", "logisticsCompany", "logistics_company", "trackingNumber", "tracking_number", "shippedAt", "shipped_at", "estimatedDelivery", "estimated_delivery", "createdAt", "created_at", "updatedAt", "updated_at", "params", "queryParams", "page", "per_page", "response", "getList", "transformedOrders", "data", "map", "prev", "current_page", "error", "console", "getStats", "getStatusColor", "colors", "getStatusText", "texts", "getPriorityColor", "low", "normal", "high", "urgent", "getPriorityText", "localStats", "length", "filter", "order", "displayStats", "columns", "title", "dataIndex", "key", "width", "render", "text", "code", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "record", "fontWeight", "color", "ellipsis", "split", "type", "size", "icon", "onClick", "handleViewOrder", "handleEditOrder", "handleSearch", "values", "searchParams", "handleRefresh", "success", "info", "handleExport", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "placeholder", "Option", "htmlType", "display", "justifyContent", "alignItems", "strong", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "footer", "column", "bordered", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Modal,\n  Descriptions,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  FilterOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { useNavigate } from 'react-router-dom';\nimport { orderApi } from '../services/api';\nimport type { Order as OrderType, OrderListParams, OrderStats } from '../types/order';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\n// 使用从 types/order.ts 导入的类型，这里定义一个本地接口用于显示\ninterface Order {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  customerIdCard: string;\n  productName: string;\n  operator: string;\n  deliveryAddress: string;\n  status: 'pending' | 'processing' | 'pending_upload' | 'shipped' | 'activated' | 'cancelled' | 'failed';\n  priority?: 'low' | 'normal' | 'high' | 'urgent';\n  logisticsCompany?: string; // 物流公司\n  trackingNumber?: string; // 快递单号\n  shippedAt?: string; // 发货时间\n  estimatedDelivery?: string; // 预计送达时间\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 订单数据将从API获取，不再使用模拟数据\n\nconst OrderList: React.FC = () => {\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0,\n  });\n  const [stats, setStats] = useState<OrderStats>({\n    total: 0,\n    pending: 0,\n    processing: 0,\n    pending_upload: 0,\n    shipped: 0,\n    activated: 0,\n    failed: 0,\n    cancelled: 0,\n    today_orders: 0,\n    week_orders: 0,\n    month_orders: 0,\n  });\n  const navigate = useNavigate();\n\n  // 组件加载时获取订单数据\n  useEffect(() => {\n    loadOrders();\n    loadStats();\n  }, []);\n\n  // 转换API数据格式为组件使用的格式\n  const transformOrderData = (apiOrder: OrderType): Order => {\n    return {\n      id: apiOrder.id,\n      orderNo: apiOrder.order_no,\n      customerName: apiOrder.customer_name,\n      customerPhone: apiOrder.customer_phone,\n      customerIdCard: apiOrder.customer_id_card,\n      productName: apiOrder.product_name,\n      operator: apiOrder.operator,\n      deliveryAddress: apiOrder.delivery_address,\n      status: apiOrder.status,\n      priority: apiOrder.priority,\n      logisticsCompany: apiOrder.logistics_company,\n      trackingNumber: apiOrder.tracking_number,\n      shippedAt: apiOrder.shipped_at,\n      estimatedDelivery: apiOrder.estimated_delivery,\n      createdAt: apiOrder.created_at,\n      updatedAt: apiOrder.updated_at,\n    };\n  };\n\n  // 加载订单数据\n  const loadOrders = async (params?: OrderListParams) => {\n    setLoading(true);\n    try {\n      const queryParams = {\n        page: pagination.current,\n        per_page: pagination.pageSize,\n        ...params,\n      };\n\n      const response = await orderApi.getList(queryParams);\n      const transformedOrders = response.data.data.map(transformOrderData);\n\n      setOrders(transformedOrders);\n      setFilteredOrders(transformedOrders);\n      setPagination(prev => ({\n        ...prev,\n        total: response.data.total,\n        current: response.data.current_page,\n      }));\n    } catch (error) {\n      console.error('获取订单数据失败:', error);\n      message.error('获取订单数据失败');\n      // 如果API失败，设置为空数组\n      setOrders([]);\n      setFilteredOrders([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载统计数据\n  const loadStats = async () => {\n    try {\n      const response = await orderApi.getStats();\n      setStats(response.data);\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      // 如果API失败，使用本地计算的统计数据\n    }\n  };\n\n  // 状态颜色映射\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      pending_upload: 'purple',\n      shipped: 'cyan',\n      activated: 'green',\n      failed: 'red',\n      cancelled: 'default',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n\n\n  // 状态文本映射\n  const getStatusText = (status: string) => {\n    const texts = {\n      pending: '待处理',\n      processing: '开卡中',\n      pending_upload: '待上传三证',\n      shipped: '已发货',\n      activated: '已激活',\n      failed: '开卡失败',\n      cancelled: '已取消',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = (priority?: string) => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red',\n    };\n    return colors[priority as keyof typeof colors] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = (priority?: string) => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急',\n    };\n    return texts[priority as keyof typeof texts] || '普通';\n  };\n\n\n\n  // 如果API统计数据不可用，使用本地计算的统计数据作为备用\n  const localStats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    pending_upload: filteredOrders.filter(order => order.status === 'pending_upload').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    failed: filteredOrders.filter(order => order.status === 'failed').length,\n    cancelled: filteredOrders.filter(order => order.status === 'cancelled').length,\n  };\n\n  // 使用API统计数据，如果不可用则使用本地计算的数据\n  const displayStats = stats.total > 0 ? stats : localStats;\n\n  // 表格列定义\n  const columns: ColumnsType<Order> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n    {\n      title: '订单状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '物流信息',\n      key: 'logistics',\n      width: 200,\n      render: (_, record) => {\n        if (record.status === 'shipped' || record.status === 'activated') {\n          return (\n            <div>\n              <div style={{ fontWeight: 600, fontSize: '12px' }}>\n                {record.logisticsCompany || '暂无'}\n              </div>\n              <div style={{ fontSize: '11px', color: '#666' }}>\n                {record.trackingNumber || '暂无快递单号'}\n              </div>\n              {record.shippedAt && (\n                <div style={{ fontSize: '11px', color: '#999' }}>\n                  发货: {record.shippedAt.split(' ')[0]}\n                </div>\n              )}\n            </div>\n          );\n        }\n        return <Text type=\"secondary\" style={{ fontSize: '12px' }}>未发货</Text>;\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditOrder(record.id)}\n          >\n            编辑\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleSearch = (values: any) => {\n    const searchParams: OrderListParams = {\n      order_no: values.orderNo,\n      customer_name: values.customerName,\n      operator: values.operator,\n      status: values.status,\n    };\n\n    // 重置分页到第一页\n    setPagination(prev => ({ ...prev, current: 1 }));\n    loadOrders(searchParams);\n  };\n\n  const handleRefresh = async () => {\n    await loadOrders();\n    await loadStats();\n    message.success('数据已刷新');\n  };\n\n  const handleViewOrder = (order: Order) => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n\n  const handleEditOrder = (id: number) => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n          订单列表\n        </Title>\n        <Text type=\"secondary\">\n          管理所有订单信息\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={stats.total}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"待处理\"\n              value={stats.pending}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"待上传三证\"\n              value={stats.pending_upload}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"开卡中\"\n              value={stats.processing}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"已发货\"\n              value={stats.shipped}\n              valueStyle={{ color: '#13c2c2' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"已激活\"\n              value={stats.activated}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 搜索表单 */}\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ marginBottom: 16 }}\n        >\n          <Form.Item name=\"orderNo\" label=\"订单号\">\n            <Input placeholder=\"请输入订单号\" style={{ width: 150 }} />\n          </Form.Item>\n          <Form.Item name=\"customerName\" label=\"客户姓名\">\n            <Input placeholder=\"请输入客户姓名\" style={{ width: 120 }} />\n          </Form.Item>\n          <Form.Item name=\"operator\" label=\"运营商\">\n            <Select placeholder=\"请选择运营商\" style={{ width: 120 }}>\n              <Select.Option value=\"中国移动\">中国移动</Select.Option>\n              <Select.Option value=\"中国电信\">中国电信</Select.Option>\n              <Select.Option value=\"中国联通\">中国联通</Select.Option>\n              <Select.Option value=\"中国广电\">中国广电</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"status\" label=\"订单状态\">\n            <Select placeholder=\"请选择状态\" style={{ width: 120 }}>\n              <Select.Option value=\"pending\">待处理</Select.Option>\n              <Select.Option value=\"processing\">开卡中</Select.Option>\n              <Select.Option value=\"pending_upload\">待上传三证</Select.Option>\n              <Select.Option value=\"shipped\">已发货</Select.Option>\n              <Select.Option value=\"activated\">已激活</Select.Option>\n              <Select.Option value=\"failed\">开卡失败</Select.Option>\n              <Select.Option value=\"cancelled\">已取消</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item>\n            <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n              搜索\n            </Button>\n          </Form.Item>\n        </Form>\n\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {filteredOrders.length} 条订单\n            </Text>\n          </div>\n          <Space>\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n            <Button onClick={handleExport} icon={<ExportOutlined />}>\n              导出\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={filteredOrders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 订单详情弹窗 */}\n      <Modal\n        title=\"订单详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={600}\n      >\n        {selectedOrder && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"订单号\" span={2}>\n              {selectedOrder.orderNo}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"客户姓名\">\n              {selectedOrder.customerName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"联系电话\">\n              {selectedOrder.customerPhone}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"产品名称\" span={2}>\n              {selectedOrder.productName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"运营商\">\n              <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"订单状态\">\n              <Tag color={getStatusColor(selectedOrder.status)}>\n                {getStatusText(selectedOrder.status)}\n              </Tag>\n            </Descriptions.Item>\n            {(selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && (\n              <>\n                <Descriptions.Item label=\"物流公司\">\n                  {selectedOrder.logisticsCompany || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"快递单号\">\n                  <Text code>{selectedOrder.trackingNumber || '暂无'}</Text>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"发货时间\">\n                  {selectedOrder.shippedAt || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"预计送达\">\n                  {selectedOrder.estimatedDelivery || '暂无'}\n                </Descriptions.Item>\n              </>\n            )}\n            <Descriptions.Item label=\"创建时间\">\n              {selectedOrder.createdAt}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"更新时间\">\n              {selectedOrder.updatedAt}\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,QACP,MAAM;AACb,SACEC,cAAc,EACdC,WAAW,EACXC,YAAY,EAEZC,cAAc,EACdC,cAAc,QAET,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG3C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGxB,UAAU;AAClC,MAAM;EAAEyB;AAAY,CAAC,GAAGpB,UAAU;;AAElC;;AAoBA;;AAEA,MAAMqB,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAG1B,IAAI,CAAC2B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAU,EAAE,CAAC;EACjE,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC4C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC;IAC3CkD,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAa;IAC7CoD,KAAK,EAAE,CAAC;IACRG,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;;EAE9B;EACAtB,SAAS,CAAC,MAAM;IACdiE,UAAU,CAAC,CAAC;IACZC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,kBAAkB,GAAIC,QAAmB,IAAY;IACzD,OAAO;MACLC,EAAE,EAAED,QAAQ,CAACC,EAAE;MACfC,OAAO,EAAEF,QAAQ,CAACG,QAAQ;MAC1BC,YAAY,EAAEJ,QAAQ,CAACK,aAAa;MACpCC,aAAa,EAAEN,QAAQ,CAACO,cAAc;MACtCC,cAAc,EAAER,QAAQ,CAACS,gBAAgB;MACzCC,WAAW,EAAEV,QAAQ,CAACW,YAAY;MAClCC,QAAQ,EAAEZ,QAAQ,CAACY,QAAQ;MAC3BC,eAAe,EAAEb,QAAQ,CAACc,gBAAgB;MAC1CC,MAAM,EAAEf,QAAQ,CAACe,MAAM;MACvBC,QAAQ,EAAEhB,QAAQ,CAACgB,QAAQ;MAC3BC,gBAAgB,EAAEjB,QAAQ,CAACkB,iBAAiB;MAC5CC,cAAc,EAAEnB,QAAQ,CAACoB,eAAe;MACxCC,SAAS,EAAErB,QAAQ,CAACsB,UAAU;MAC9BC,iBAAiB,EAAEvB,QAAQ,CAACwB,kBAAkB;MAC9CC,SAAS,EAAEzB,QAAQ,CAAC0B,UAAU;MAC9BC,SAAS,EAAE3B,QAAQ,CAAC4B;IACtB,CAAC;EACH,CAAC;;EAED;EACA,MAAM/B,UAAU,GAAG,MAAOgC,MAAwB,IAAK;IACrDzD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM0D,WAAW,GAAG;QAClBC,IAAI,EAAEpD,UAAU,CAACE,OAAO;QACxBmD,QAAQ,EAAErD,UAAU,CAACG,QAAQ;QAC7B,GAAG+C;MACL,CAAC;MAED,MAAMI,QAAQ,GAAG,MAAM9E,QAAQ,CAAC+E,OAAO,CAACJ,WAAW,CAAC;MACpD,MAAMK,iBAAiB,GAAGF,QAAQ,CAACG,IAAI,CAACA,IAAI,CAACC,GAAG,CAACtC,kBAAkB,CAAC;MAEpE/B,SAAS,CAACmE,iBAAiB,CAAC;MAC5BjE,iBAAiB,CAACiE,iBAAiB,CAAC;MACpCvD,aAAa,CAAC0D,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPvD,KAAK,EAAEkD,QAAQ,CAACG,IAAI,CAACrD,KAAK;QAC1BF,OAAO,EAAEoD,QAAQ,CAACG,IAAI,CAACG;MACzB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9F,OAAO,CAAC8F,KAAK,CAAC,UAAU,CAAC;MACzB;MACAxE,SAAS,CAAC,EAAE,CAAC;MACbE,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMmC,QAAQ,GAAG,MAAM9E,QAAQ,CAACuF,QAAQ,CAAC,CAAC;MAC1CzD,QAAQ,CAACgD,QAAQ,CAACG,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,cAAc,GAAI5B,MAAc,IAAK;IACzC,MAAM6B,MAAM,GAAG;MACb1D,OAAO,EAAE,QAAQ;MACjBC,UAAU,EAAE,MAAM;MAClBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE;IACb,CAAC;IACD,OAAOoD,MAAM,CAAC7B,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;;EAID;EACA,MAAM8B,aAAa,GAAI9B,MAAc,IAAK;IACxC,MAAM+B,KAAK,GAAG;MACZ5D,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,OAAO;MACvBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE;IACb,CAAC;IACD,OAAOsD,KAAK,CAAC/B,MAAM,CAAuB,IAAIA,MAAM;EACtD,CAAC;;EAED;EACA,MAAMgC,gBAAgB,GAAI/B,QAAiB,IAAK;IAC9C,MAAM4B,MAAM,GAAG;MACbI,GAAG,EAAE,SAAS;MACdC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAOP,MAAM,CAAC5B,QAAQ,CAAwB,IAAI,SAAS;EAC7D,CAAC;;EAED;EACA,MAAMoC,eAAe,GAAIpC,QAAiB,IAAK;IAC7C,MAAM8B,KAAK,GAAG;MACZE,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE;IACV,CAAC;IACD,OAAOL,KAAK,CAAC9B,QAAQ,CAAuB,IAAI,IAAI;EACtD,CAAC;;EAID;EACA,MAAMqC,UAAU,GAAG;IACjBtE,KAAK,EAAEd,cAAc,CAACqF,MAAM;IAC5BpE,OAAO,EAAEjB,cAAc,CAACsF,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACzC,MAAM,KAAK,SAAS,CAAC,CAACuC,MAAM;IAC1EnE,UAAU,EAAElB,cAAc,CAACsF,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACzC,MAAM,KAAK,YAAY,CAAC,CAACuC,MAAM;IAChFlE,cAAc,EAAEnB,cAAc,CAACsF,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACzC,MAAM,KAAK,gBAAgB,CAAC,CAACuC,MAAM;IACxFjE,OAAO,EAAEpB,cAAc,CAACsF,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACzC,MAAM,KAAK,SAAS,CAAC,CAACuC,MAAM;IAC1EhE,SAAS,EAAErB,cAAc,CAACsF,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACzC,MAAM,KAAK,WAAW,CAAC,CAACuC,MAAM;IAC9E/D,MAAM,EAAEtB,cAAc,CAACsF,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACzC,MAAM,KAAK,QAAQ,CAAC,CAACuC,MAAM;IACxE9D,SAAS,EAAEvB,cAAc,CAACsF,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACzC,MAAM,KAAK,WAAW,CAAC,CAACuC;EAC1E,CAAC;;EAED;EACA,MAAMG,YAAY,GAAGzE,KAAK,CAACD,KAAK,GAAG,CAAC,GAAGC,KAAK,GAAGqE,UAAU;;EAEzD;EACA,MAAMK,OAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnB3G,OAAA,CAACI,IAAI;MAACwG,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EACpCJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChBrH,OAAA;MAAA+G,QAAA,gBACE/G,OAAA;QAAK6G,KAAK,EAAE;UAAES,UAAU,EAAE;QAAI,CAAE;QAAAP,QAAA,EAAEM,MAAM,CAACtE;MAAY;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5DnH,OAAA;QAAK6G,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAES,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,EAC7CM,MAAM,CAACpE;MAAa;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBgB,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnB3G,OAAA,CAACnB,GAAG;MAAC0I,KAAK,EAAC,MAAM;MAAAR,QAAA,EAAEJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGhD,MAAc,iBACrB1D,OAAA,CAACnB,GAAG;MAAC0I,KAAK,EAAEjC,cAAc,CAAC5B,MAAM,CAAE;MAAAqD,QAAA,EAChCvB,aAAa,CAAC9B,MAAM;IAAC;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,KAAK;MACrB,IAAIA,MAAM,CAAC3D,MAAM,KAAK,SAAS,IAAI2D,MAAM,CAAC3D,MAAM,KAAK,WAAW,EAAE;QAChE,oBACE1D,OAAA;UAAA+G,QAAA,gBACE/G,OAAA;YAAK6G,KAAK,EAAE;cAAES,UAAU,EAAE,GAAG;cAAER,QAAQ,EAAE;YAAO,CAAE;YAAAC,QAAA,EAC/CM,MAAM,CAACzD,gBAAgB,IAAI;UAAI;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNnH,OAAA;YAAK6G,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,EAC7CM,MAAM,CAACvD,cAAc,IAAI;UAAQ;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACLE,MAAM,CAACrD,SAAS,iBACfhE,OAAA;YAAK6G,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAC,gBAC3C,EAACM,MAAM,CAACrD,SAAS,CAACyD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEV;MACA,oBAAOnH,OAAA,CAACI,IAAI;QAACsH,IAAI,EAAC,WAAW;QAACb,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAC,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACvE;EACF,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnB3G,OAAA;MAAK6G,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EAC9BJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChBrH,OAAA,CAACrB,KAAK;MAACgJ,IAAI,EAAC,OAAO;MAAAZ,QAAA,gBACjB/G,OAAA,CAACtB,MAAM;QACLgJ,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE5H,OAAA,CAACP,WAAW;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBU,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACT,MAAM,CAAE;QAAAN,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnH,OAAA,CAACtB,MAAM;QACLgJ,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE5H,OAAA,CAACN,YAAY;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBU,OAAO,EAAEA,CAAA,KAAME,eAAe,CAACV,MAAM,CAACzE,EAAE,CAAE;QAAAmE,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMa,YAAY,GAAIC,MAAW,IAAK;IACpC,MAAMC,YAA6B,GAAG;MACpCpF,QAAQ,EAAEmF,MAAM,CAACpF,OAAO;MACxBG,aAAa,EAAEiF,MAAM,CAAClF,YAAY;MAClCQ,QAAQ,EAAE0E,MAAM,CAAC1E,QAAQ;MACzBG,MAAM,EAAEuE,MAAM,CAACvE;IACjB,CAAC;;IAED;IACAnC,aAAa,CAAC0D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzD,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;IAChDgB,UAAU,CAAC0F,YAAY,CAAC;EAC1B,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAM3F,UAAU,CAAC,CAAC;IAClB,MAAMC,SAAS,CAAC,CAAC;IACjBpD,OAAO,CAAC+I,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;EAED,MAAMN,eAAe,GAAI3B,KAAY,IAAK;IACxC9E,gBAAgB,CAAC8E,KAAK,CAAC;IACvBhF,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM4G,eAAe,GAAInF,EAAU,IAAK;IACtCvD,OAAO,CAACgJ,IAAI,CAAC,QAAQzF,EAAE,WAAW,CAAC;EACrC,CAAC;EAED,MAAM0F,YAAY,GAAGA,CAAA,KAAM;IACzBjJ,OAAO,CAACgJ,IAAI,CAAC,YAAY,CAAC;EAC5B,CAAC;EAED,oBACErI,OAAA;IAAA+G,QAAA,gBACE/G,OAAA;MAAK6G,KAAK,EAAE;QAAE0B,YAAY,EAAE;MAAO,CAAE;MAAAxB,QAAA,gBACnC/G,OAAA,CAACG,KAAK;QAACqI,KAAK,EAAE,CAAE;QAAC3B,KAAK,EAAE;UAAE4B,MAAM,EAAE,CAAC;UAAElB,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRnH,OAAA,CAACI,IAAI;QAACsH,IAAI,EAAC,WAAW;QAAAX,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNnH,OAAA,CAACd,GAAG;MAACwJ,MAAM,EAAE,EAAG;MAAC7B,KAAK,EAAE;QAAE0B,YAAY,EAAE;MAAO,CAAE;MAAAxB,QAAA,gBAC/C/G,OAAA,CAACb,GAAG;QAACwJ,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACX/G,OAAA,CAACxB,IAAI;UAAAuI,QAAA,eACH/G,OAAA,CAACZ,SAAS;YACRkH,KAAK,EAAC,0BAAM;YACZsC,KAAK,EAAEjH,KAAK,CAACD,KAAM;YACnBmH,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnH,OAAA,CAACb,GAAG;QAACwJ,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACX/G,OAAA,CAACxB,IAAI;UAAAuI,QAAA,eACH/G,OAAA,CAACZ,SAAS;YACRkH,KAAK,EAAC,oBAAK;YACXsC,KAAK,EAAEjH,KAAK,CAACE,OAAQ;YACrBgH,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnH,OAAA,CAACb,GAAG;QAACwJ,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACX/G,OAAA,CAACxB,IAAI;UAAAuI,QAAA,eACH/G,OAAA,CAACZ,SAAS;YACRkH,KAAK,EAAC,gCAAO;YACbsC,KAAK,EAAEjH,KAAK,CAACI,cAAe;YAC5B8G,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnH,OAAA,CAACb,GAAG;QAACwJ,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACX/G,OAAA,CAACxB,IAAI;UAAAuI,QAAA,eACH/G,OAAA,CAACZ,SAAS;YACRkH,KAAK,EAAC,oBAAK;YACXsC,KAAK,EAAEjH,KAAK,CAACG,UAAW;YACxB+G,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnH,OAAA,CAACb,GAAG;QAACwJ,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACX/G,OAAA,CAACxB,IAAI;UAAAuI,QAAA,eACH/G,OAAA,CAACZ,SAAS;YACRkH,KAAK,EAAC,oBAAK;YACXsC,KAAK,EAAEjH,KAAK,CAACK,OAAQ;YACrB6G,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnH,OAAA,CAACb,GAAG;QAACwJ,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACX/G,OAAA,CAACxB,IAAI;UAAAuI,QAAA,eACH/G,OAAA,CAACZ,SAAS;YACRkH,KAAK,EAAC,oBAAK;YACXsC,KAAK,EAAEjH,KAAK,CAACM,SAAU;YACvB4G,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnH,OAAA,CAACxB,IAAI;MAAAuI,QAAA,gBAEH/G,OAAA,CAAClB,IAAI;QACH0B,IAAI,EAAEA,IAAK;QACXsI,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEf,YAAa;QACvBnB,KAAK,EAAE;UAAE0B,YAAY,EAAE;QAAG,CAAE;QAAAxB,QAAA,gBAE5B/G,OAAA,CAAClB,IAAI,CAACkK,IAAI;UAACC,IAAI,EAAC,SAAS;UAACC,KAAK,EAAC,oBAAK;UAAAnC,QAAA,eACnC/G,OAAA,CAACjB,KAAK;YAACoK,WAAW,EAAC,sCAAQ;YAACtC,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACZnH,OAAA,CAAClB,IAAI,CAACkK,IAAI;UAACC,IAAI,EAAC,cAAc;UAACC,KAAK,EAAC,0BAAM;UAAAnC,QAAA,eACzC/G,OAAA,CAACjB,KAAK;YAACoK,WAAW,EAAC,4CAAS;YAACtC,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACZnH,OAAA,CAAClB,IAAI,CAACkK,IAAI;UAACC,IAAI,EAAC,UAAU;UAACC,KAAK,EAAC,oBAAK;UAAAnC,QAAA,eACpC/G,OAAA,CAAChB,MAAM;YAACmK,WAAW,EAAC,sCAAQ;YAACtC,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACjD/G,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZnH,OAAA,CAAClB,IAAI,CAACkK,IAAI;UAACC,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC,0BAAM;UAAAnC,QAAA,eACnC/G,OAAA,CAAChB,MAAM;YAACmK,WAAW,EAAC,gCAAO;YAACtC,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBAChD/G,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,SAAS;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,YAAY;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACrDnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,gBAAgB;cAAA7B,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC3DnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,SAAS;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,WAAW;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACpDnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,QAAQ;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDnH,OAAA,CAAChB,MAAM,CAACoK,MAAM;cAACR,KAAK,EAAC,WAAW;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZnH,OAAA,CAAClB,IAAI,CAACkK,IAAI;UAAAjC,QAAA,eACR/G,OAAA,CAACtB,MAAM;YAACgJ,IAAI,EAAC,SAAS;YAAC2B,QAAQ,EAAC,QAAQ;YAACzB,IAAI,eAAE5H,OAAA,CAACR,cAAc;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEnE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGPnH,OAAA;QAAK6G,KAAK,EAAE;UACVyC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBjB,YAAY,EAAE;QAChB,CAAE;QAAAxB,QAAA,gBACA/G,OAAA;UAAA+G,QAAA,eACE/G,OAAA,CAACI,IAAI;YAACqJ,MAAM;YAAA1C,QAAA,GAAC,SACT,EAACnG,cAAc,CAACqF,MAAM,EAAC,qBAC3B;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNnH,OAAA,CAACrB,KAAK;UAAAoI,QAAA,gBACJ/G,OAAA,CAACtB,MAAM;YAACmJ,OAAO,EAAEM,aAAc;YAACP,IAAI,eAAE5H,OAAA,CAACJ,cAAc;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnH,OAAA,CAACtB,MAAM;YAACmJ,OAAO,EAAES,YAAa;YAACV,IAAI,eAAE5H,OAAA,CAACL,cAAc;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNnH,OAAA,CAACvB,KAAK;QACJ4H,OAAO,EAAEA,OAAQ;QACjBqD,UAAU,EAAE9I,cAAe;QAC3B+I,MAAM,EAAC,IAAI;QACX7I,OAAO,EAAEA,OAAQ;QACjB8I,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBvI,UAAU,EAAE;UACVwI,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACtI,KAAK,EAAEuI,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAASvI,KAAK;QAC3C,CAAE;QACFwI,YAAY,EAAE;UACZlJ,eAAe;UACfmJ,QAAQ,EAAElJ;QACZ;MAAE;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnH,OAAA,CAACV,KAAK;MACJgH,KAAK,EAAC,0BAAM;MACZ8D,IAAI,EAAElJ,kBAAmB;MACzBmJ,QAAQ,EAAEA,CAAA,KAAMlJ,qBAAqB,CAAC,KAAK,CAAE;MAC7CmJ,MAAM,EAAE,cACNtK,OAAA,CAACtB,MAAM;QAAamJ,OAAO,EAAEA,CAAA,KAAM1G,qBAAqB,CAAC,KAAK,CAAE;QAAA4F,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFV,KAAK,EAAE,GAAI;MAAAM,QAAA,EAEV3F,aAAa,iBACZpB,OAAA,CAACT,YAAY;QAACgL,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAAzD,QAAA,gBAC/B/G,OAAA,CAACT,YAAY,CAACyJ,IAAI;UAACE,KAAK,EAAC,oBAAK;UAACP,IAAI,EAAE,CAAE;UAAA5B,QAAA,EACpC3F,aAAa,CAACyB;QAAO;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,EAC5B3F,aAAa,CAAC2B;QAAY;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,EAC5B3F,aAAa,CAAC6B;QAAa;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;UAACE,KAAK,EAAC,0BAAM;UAACP,IAAI,EAAE,CAAE;UAAA5B,QAAA,EACrC3F,aAAa,CAACiC;QAAW;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;UAACE,KAAK,EAAC,oBAAK;UAAAnC,QAAA,eAC5B/G,OAAA,CAACnB,GAAG;YAAC0I,KAAK,EAAC,MAAM;YAAAR,QAAA,EAAE3F,aAAa,CAACmC;UAAQ;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,eAC7B/G,OAAA,CAACnB,GAAG;YAAC0I,KAAK,EAAEjC,cAAc,CAAClE,aAAa,CAACsC,MAAM,CAAE;YAAAqD,QAAA,EAC9CvB,aAAa,CAACpE,aAAa,CAACsC,MAAM;UAAC;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,EACnB,CAAC/F,aAAa,CAACsC,MAAM,KAAK,SAAS,IAAItC,aAAa,CAACsC,MAAM,KAAK,WAAW,kBAC1E1D,OAAA,CAAAE,SAAA;UAAA6G,QAAA,gBACE/G,OAAA,CAACT,YAAY,CAACyJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAnC,QAAA,EAC5B3F,aAAa,CAACwC,gBAAgB,IAAI;UAAI;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAnC,QAAA,eAC7B/G,OAAA,CAACI,IAAI;cAACwG,IAAI;cAAAG,QAAA,EAAE3F,aAAa,CAAC0C,cAAc,IAAI;YAAI;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAnC,QAAA,EAC5B3F,aAAa,CAAC4C,SAAS,IAAI;UAAI;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAnC,QAAA,EAC5B3F,aAAa,CAAC8C,iBAAiB,IAAI;UAAI;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA,eACpB,CACH,eACDnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,EAC5B3F,aAAa,CAACgD;QAAS;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACpBnH,OAAA,CAACT,YAAY,CAACyJ,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,EAC5B3F,aAAa,CAACkD;QAAS;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5G,EAAA,CA/gBID,SAAmB;EAAA,QACRxB,IAAI,CAAC2B,OAAO,EAyBVZ,WAAW;AAAA;AAAA4K,EAAA,GA1BxBnK,SAAmB;AAihBzB,eAAeA,SAAS;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport dayjs from 'dayjs';\nimport 'dayjs/locale/zh-cn';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport ProductList from './pages/ProductList';\nimport ProductCreate from './pages/ProductCreate';\nimport ProductEdit from './pages/ProductEdit';\nimport ProductDetail from './pages/ProductDetail';\nimport CategoryManagement from './pages/CategoryManagement';\nimport OrderList from './pages/OrderList';\nimport OrderPending from './pages/OrderPending';\nimport OrderReviewing from './pages/OrderReviewing';\nimport OrderPendingUpload from './pages/OrderPendingUpload';\nimport OrderShipped from './pages/OrderShipped';\nimport OrderFailed from './pages/OrderFailed';\nimport OrderCompleted from './pages/OrderCompleted';\n\n// 设置 dayjs 中文\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndayjs.locale('zh-cn');\n\n// Ant Design 主题配置\nconst theme = {\n  token: {\n    colorPrimary: '#1890ff',\n    borderRadius: 6\n  },\n  components: {\n    Layout: {\n      siderBg: '#f5f5f5',\n      // 浅灰色背景\n      triggerBg: '#e8e8e8'\n    },\n    Menu: {\n      itemBg: 'transparent',\n      itemColor: '#666666',\n      // 深灰色文字\n      itemHoverBg: '#e6f7ff',\n      itemHoverColor: '#1890ff',\n      itemSelectedBg: '#e6f7ff',\n      // 浅蓝色背景高亮\n      itemSelectedColor: '#1890ff',\n      // 蓝色文字\n      subMenuItemBg: 'transparent'\n    }\n  }\n};\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    theme: theme,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 36\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 35\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"dashboard\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"products\",\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"list\",\n              element: /*#__PURE__*/_jsxDEV(ProductList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"category\",\n              element: /*#__PURE__*/_jsxDEV(CategoryManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"create\",\n              element: /*#__PURE__*/_jsxDEV(ProductCreate, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"edit/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProductEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"detail/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProductDetail, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"orders\",\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"list\",\n              element: /*#__PURE__*/_jsxDEV(OrderList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"pending\",\n              element: /*#__PURE__*/_jsxDEV(OrderPending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"reviewing\",\n              element: /*#__PURE__*/_jsxDEV(OrderReviewing, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"pending-upload\",\n              element: /*#__PURE__*/_jsxDEV(OrderPendingUpload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"shipped\",\n              element: /*#__PURE__*/_jsxDEV(OrderShipped, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"failed\",\n              element: /*#__PURE__*/_jsxDEV(OrderFailed, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"completed\",\n              element: /*#__PURE__*/_jsxDEV(OrderCompleted, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "dayjs", "Layout", "Dashboard", "ProductList", "ProductCreate", "ProductEdit", "ProductDetail", "CategoryManagement", "OrderList", "OrderPending", "OrderReviewing", "OrderPendingUpload", "OrderShipped", "OrderFailed", "OrderCompleted", "jsxDEV", "_jsxDEV", "locale", "theme", "token", "colorPrimary", "borderRadius", "components", "siderBg", "triggerBg", "<PERSON><PERSON>", "itemBg", "itemColor", "itemHoverBg", "itemHoverColor", "itemSelectedBg", "itemSelectedColor", "subMenuItemBg", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "replace", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport dayjs from 'dayjs';\nimport 'dayjs/locale/zh-cn';\n\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport ProductList from './pages/ProductList';\nimport ProductCreate from './pages/ProductCreate';\nimport ProductEdit from './pages/ProductEdit';\nimport ProductDetail from './pages/ProductDetail';\nimport CategoryManagement from './pages/CategoryManagement';\nimport OrderList from './pages/OrderList';\nimport OrderPending from './pages/OrderPending';\nimport OrderReviewing from './pages/OrderReviewing';\nimport OrderPendingUpload from './pages/OrderPendingUpload';\nimport OrderShipped from './pages/OrderShipped';\nimport OrderFailed from './pages/OrderFailed';\nimport OrderCompleted from './pages/OrderCompleted';\n\n// 设置 dayjs 中文\ndayjs.locale('zh-cn');\n\n// Ant Design 主题配置\nconst theme = {\n  token: {\n    colorPrimary: '#1890ff',\n    borderRadius: 6,\n  },\n  components: {\n    Layout: {\n      siderBg: '#f5f5f5', // 浅灰色背景\n      triggerBg: '#e8e8e8',\n    },\n    Menu: {\n      itemBg: 'transparent',\n      itemColor: '#666666', // 深灰色文字\n      itemHoverBg: '#e6f7ff',\n      itemHoverColor: '#1890ff',\n      itemSelectedBg: '#e6f7ff', // 浅蓝色背景高亮\n      itemSelectedColor: '#1890ff', // 蓝色文字\n      subMenuItemBg: 'transparent',\n    },\n  },\n};\n\nconst App: React.FC = () => {\n  return (\n    <ConfigProvider locale={zhCN} theme={theme}>\n      <Router>\n        <Routes>\n          <Route path=\"/\" element={<Layout />}>\n            <Route index element={<Navigate to=\"/dashboard\" replace />} />\n            <Route path=\"dashboard\" element={<Dashboard />} />\n            <Route path=\"products\">\n              <Route path=\"list\" element={<ProductList />} />\n              <Route path=\"category\" element={<CategoryManagement />} />\n              <Route path=\"create\" element={<ProductCreate />} />\n              <Route path=\"edit/:id\" element={<ProductEdit />} />\n              <Route path=\"detail/:id\" element={<ProductDetail />} />\n            </Route>\n            <Route path=\"orders\">\n              <Route path=\"list\" element={<OrderList />} />\n              <Route path=\"pending\" element={<OrderPending />} />\n              <Route path=\"reviewing\" element={<OrderReviewing />} />\n              <Route path=\"pending-upload\" element={<OrderPendingUpload />} />\n              <Route path=\"shipped\" element={<OrderShipped />} />\n              <Route path=\"failed\" element={<OrderFailed />} />\n              <Route path=\"completed\" element={<OrderCompleted />} />\n            </Route>\n          </Route>\n        </Routes>\n      </Router>\n    </ConfigProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAE3B,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,cAAc,MAAM,wBAAwB;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAhB,KAAK,CAACiB,MAAM,CAAC,OAAO,CAAC;;AAErB;AACA,MAAMC,KAAK,GAAG;EACZC,KAAK,EAAE;IACLC,YAAY,EAAE,SAAS;IACvBC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVrB,MAAM,EAAE;MACNsB,OAAO,EAAE,SAAS;MAAE;MACpBC,SAAS,EAAE;IACb,CAAC;IACDC,IAAI,EAAE;MACJC,MAAM,EAAE,aAAa;MACrBC,SAAS,EAAE,SAAS;MAAE;MACtBC,WAAW,EAAE,SAAS;MACtBC,cAAc,EAAE,SAAS;MACzBC,cAAc,EAAE,SAAS;MAAE;MAC3BC,iBAAiB,EAAE,SAAS;MAAE;MAC9BC,aAAa,EAAE;IACjB;EACF;AACF,CAAC;AAED,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEjB,OAAA,CAAClB,cAAc;IAACmB,MAAM,EAAElB,IAAK;IAACmB,KAAK,EAAEA,KAAM;IAAAgB,QAAA,eACzClB,OAAA,CAACtB,MAAM;MAAAwC,QAAA,eACLlB,OAAA,CAACrB,MAAM;QAAAuC,QAAA,eACLlB,OAAA,CAACpB,KAAK;UAACuC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEpB,OAAA,CAACf,MAAM;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,gBAClClB,OAAA,CAACpB,KAAK;YAAC6C,KAAK;YAACL,OAAO,eAAEpB,OAAA,CAACnB,QAAQ;cAAC6C,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DxB,OAAA,CAACpB,KAAK;YAACuC,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEpB,OAAA,CAACd,SAAS;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDxB,OAAA,CAACpB,KAAK;YAACuC,IAAI,EAAC,UAAU;YAAAD,QAAA,gBACpBlB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,MAAM;cAACC,OAAO,eAAEpB,OAAA,CAACb,WAAW;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEpB,OAAA,CAACT,kBAAkB;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEpB,OAAA,CAACZ,aAAa;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEpB,OAAA,CAACX,WAAW;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,YAAY;cAACC,OAAO,eAAEpB,OAAA,CAACV,aAAa;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACRxB,OAAA,CAACpB,KAAK;YAACuC,IAAI,EAAC,QAAQ;YAAAD,QAAA,gBAClBlB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,MAAM;cAACC,OAAO,eAAEpB,OAAA,CAACR,SAAS;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,SAAS;cAACC,OAAO,eAAEpB,OAAA,CAACP,YAAY;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEpB,OAAA,CAACN,cAAc;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,gBAAgB;cAACC,OAAO,eAAEpB,OAAA,CAACL,kBAAkB;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChExB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,SAAS;cAACC,OAAO,eAAEpB,OAAA,CAACJ,YAAY;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEpB,OAAA,CAACH,WAAW;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDxB,OAAA,CAACpB,KAAK;cAACuC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEpB,OAAA,CAACF,cAAc;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB,CAAC;AAACI,EAAA,GA7BIX,GAAa;AA+BnB,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    tableExpandColumnWidth,\n    calc\n  } = token;\n  const getSizeStyle = (size, paddingVertical, paddingHorizontal, fontSize) => ({\n    [\"\".concat(componentCls).concat(componentCls, \"-\").concat(size)]: {\n      fontSize,\n      [\"\\n        \".concat(componentCls, \"-title,\\n        \").concat(componentCls, \"-footer,\\n        \").concat(componentCls, \"-cell,\\n        \").concat(componentCls, \"-thead > tr > th,\\n        \").concat(componentCls, \"-tbody > tr > th,\\n        \").concat(componentCls, \"-tbody > tr > td,\\n        tfoot > tr > th,\\n        tfoot > tr > td\\n      \")]: {\n        padding: \"\".concat(unit(paddingVertical), \" \").concat(unit(paddingHorizontal))\n      },\n      [\"\".concat(componentCls, \"-filter-trigger\")]: {\n        marginInlineEnd: unit(calc(paddingHorizontal).div(2).mul(-1).equal())\n      },\n      [\"\".concat(componentCls, \"-expanded-row-fixed\")]: {\n        margin: \"\".concat(unit(calc(paddingVertical).mul(-1).equal()), \" \").concat(unit(calc(paddingHorizontal).mul(-1).equal()))\n      },\n      [\"\".concat(componentCls, \"-tbody\")]: {\n        // ========================= Nest Table ===========================\n        [\"\".concat(componentCls, \"-wrapper:only-child \").concat(componentCls)]: {\n          marginBlock: unit(calc(paddingVertical).mul(-1).equal()),\n          marginInline: \"\".concat(unit(calc(tableExpandColumnWidth).sub(paddingHorizontal).equal()), \" \").concat(unit(calc(paddingHorizontal).mul(-1).equal()))\n        }\n      },\n      // https://github.com/ant-design/ant-design/issues/35167\n      [\"\".concat(componentCls, \"-selection-extra\")]: {\n        paddingInlineStart: unit(calc(paddingHorizontal).div(4).equal())\n      }\n    }\n  });\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: Object.assign(Object.assign({}, getSizeStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle, token.tableFontSizeMiddle)), getSizeStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall, token.tableFontSizeSmall))\n  };\n};\nexport default genSizeStyle;", "map": {"version": 3, "names": ["unit", "genSizeStyle", "token", "componentCls", "tableExpandColumnWidth", "calc", "getSizeStyle", "size", "paddingVertical", "paddingHorizontal", "fontSize", "concat", "padding", "marginInlineEnd", "div", "mul", "equal", "margin", "marginBlock", "marginInline", "sub", "paddingInlineStart", "Object", "assign", "tablePaddingVerticalMiddle", "tablePaddingHorizontalMiddle", "tableFontSizeMiddle", "tablePaddingVerticalSmall", "tablePaddingHorizontalSmall", "tableFontSizeSmall"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/table/style/size.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    tableExpandColumnWidth,\n    calc\n  } = token;\n  const getSizeStyle = (size, paddingVertical, paddingHorizontal, fontSize) => ({\n    [`${componentCls}${componentCls}-${size}`]: {\n      fontSize,\n      [`\n        ${componentCls}-title,\n        ${componentCls}-footer,\n        ${componentCls}-cell,\n        ${componentCls}-thead > tr > th,\n        ${componentCls}-tbody > tr > th,\n        ${componentCls}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]: {\n        padding: `${unit(paddingVertical)} ${unit(paddingHorizontal)}`\n      },\n      [`${componentCls}-filter-trigger`]: {\n        marginInlineEnd: unit(calc(paddingHorizontal).div(2).mul(-1).equal())\n      },\n      [`${componentCls}-expanded-row-fixed`]: {\n        margin: `${unit(calc(paddingVertical).mul(-1).equal())} ${unit(calc(paddingHorizontal).mul(-1).equal())}`\n      },\n      [`${componentCls}-tbody`]: {\n        // ========================= Nest Table ===========================\n        [`${componentCls}-wrapper:only-child ${componentCls}`]: {\n          marginBlock: unit(calc(paddingVertical).mul(-1).equal()),\n          marginInline: `${unit(calc(tableExpandColumnWidth).sub(paddingHorizontal).equal())} ${unit(calc(paddingHorizontal).mul(-1).equal())}`\n        }\n      },\n      // https://github.com/ant-design/ant-design/issues/35167\n      [`${componentCls}-selection-extra`]: {\n        paddingInlineStart: unit(calc(paddingHorizontal).div(4).equal())\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, getSizeStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle, token.tableFontSizeMiddle)), getSizeStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall, token.tableFontSizeSmall))\n  };\n};\nexport default genSizeStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,sBAAsB;IACtBC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,YAAY,GAAGA,CAACC,IAAI,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,QAAQ,MAAM;IAC5E,IAAAC,MAAA,CAAIR,YAAY,EAAAQ,MAAA,CAAGR,YAAY,OAAAQ,MAAA,CAAIJ,IAAI,IAAK;MAC1CG,QAAQ;MACR,cAAAC,MAAA,CACIR,YAAY,uBAAAQ,MAAA,CACZR,YAAY,wBAAAQ,MAAA,CACZR,YAAY,sBAAAQ,MAAA,CACZR,YAAY,iCAAAQ,MAAA,CACZR,YAAY,iCAAAQ,MAAA,CACZR,YAAY,oFAGZ;QACFS,OAAO,KAAAD,MAAA,CAAKX,IAAI,CAACQ,eAAe,CAAC,OAAAG,MAAA,CAAIX,IAAI,CAACS,iBAAiB,CAAC;MAC9D,CAAC;MACD,IAAAE,MAAA,CAAIR,YAAY,uBAAoB;QAClCU,eAAe,EAAEb,IAAI,CAACK,IAAI,CAACI,iBAAiB,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACtE,CAAC;MACD,IAAAL,MAAA,CAAIR,YAAY,2BAAwB;QACtCc,MAAM,KAAAN,MAAA,CAAKX,IAAI,CAACK,IAAI,CAACG,eAAe,CAAC,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,OAAAL,MAAA,CAAIX,IAAI,CAACK,IAAI,CAACI,iBAAiB,CAAC,CAACM,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;MACzG,CAAC;MACD,IAAAL,MAAA,CAAIR,YAAY,cAAW;QACzB;QACA,IAAAQ,MAAA,CAAIR,YAAY,0BAAAQ,MAAA,CAAuBR,YAAY,IAAK;UACtDe,WAAW,EAAElB,IAAI,CAACK,IAAI,CAACG,eAAe,CAAC,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;UACxDG,YAAY,KAAAR,MAAA,CAAKX,IAAI,CAACK,IAAI,CAACD,sBAAsB,CAAC,CAACgB,GAAG,CAACX,iBAAiB,CAAC,CAACO,KAAK,CAAC,CAAC,CAAC,OAAAL,MAAA,CAAIX,IAAI,CAACK,IAAI,CAACI,iBAAiB,CAAC,CAACM,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;QACrI;MACF,CAAC;MACD;MACA,IAAAL,MAAA,CAAIR,YAAY,wBAAqB;QACnCkB,kBAAkB,EAAErB,IAAI,CAACK,IAAI,CAACI,iBAAiB,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC;MACjE;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACL,IAAAL,MAAA,CAAIR,YAAY,gBAAamB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,YAAY,CAAC,QAAQ,EAAEJ,KAAK,CAACsB,0BAA0B,EAAEtB,KAAK,CAACuB,4BAA4B,EAAEvB,KAAK,CAACwB,mBAAmB,CAAC,CAAC,EAAEpB,YAAY,CAAC,OAAO,EAAEJ,KAAK,CAACyB,yBAAyB,EAAEzB,KAAK,CAAC0B,2BAA2B,EAAE1B,KAAK,CAAC2B,kBAAkB,CAAC;EAC5S,CAAC;AACH,CAAC;AACD,eAAe5B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
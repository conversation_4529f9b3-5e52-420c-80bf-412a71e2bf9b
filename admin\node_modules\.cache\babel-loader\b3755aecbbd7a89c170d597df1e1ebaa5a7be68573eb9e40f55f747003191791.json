{"ast": null, "code": "import { generate } from '@ant-design/colors';\nimport defaultAlgorithm from '../default';\nimport { defaultPresetColors } from '../seed';\nimport genColorMapToken from '../shared/genColorMapToken';\nimport { generateColorPalettes, generateNeutralColorPalettes } from './colors';\nconst derivative = (token, mapToken) => {\n  const colorPalettes = Object.keys(defaultPresetColors).map(colorKey => {\n    const colors = generate(token[colorKey], {\n      theme: 'dark'\n    });\n    return Array.from({\n      length: 10\n    }, () => 1).reduce((prev, _, i) => {\n      prev[`${colorKey}-${i + 1}`] = colors[i];\n      prev[`${colorKey}${i + 1}`] = colors[i];\n      return prev;\n    }, {});\n  }).reduce((prev, cur) => {\n    // biome-ignore lint/style/noParameterAssign: it is a reduce\n    prev = Object.assign(Object.assign({}, prev), cur);\n    return prev;\n  }, {});\n  const mergedMapToken = mapToken !== null && mapToken !== void 0 ? mapToken : defaultAlgorithm(token);\n  const colorMapToken = genColorMapToken(token, {\n    generateColorPalettes,\n    generateNeutralColorPalettes\n  });\n  return Object.assign(Object.assign(Object.assign(Object.assign({}, mergedMapToken), colorPalettes), colorMapToken), {\n    // Customize selected item background color\n    // https://github.com/ant-design/ant-design/issues/30524#issuecomment-871961867\n    colorPrimaryBg: colorMapToken.colorPrimaryBorder,\n    colorPrimaryBgHover: colorMapToken.colorPrimaryBorderHover\n  });\n};\nexport default derivative;", "map": {"version": 3, "names": ["generate", "defaultAlgorithm", "defaultPresetColors", "genColorMapToken", "generateColorPalettes", "generateNeutralColorPalettes", "derivative", "token", "mapToken", "colorPalettes", "Object", "keys", "map", "colorKey", "colors", "theme", "Array", "from", "length", "reduce", "prev", "_", "i", "cur", "assign", "mergedMapToken", "colorMapToken", "colorPrimaryBg", "colorPrimaryBorder", "colorPrimaryBgHover", "colorPrimaryBorderHover"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/theme/themes/dark/index.js"], "sourcesContent": ["import { generate } from '@ant-design/colors';\nimport defaultAlgorithm from '../default';\nimport { defaultPresetColors } from '../seed';\nimport genColorMapToken from '../shared/genColorMapToken';\nimport { generateColorPalettes, generateNeutralColorPalettes } from './colors';\nconst derivative = (token, mapToken) => {\n  const colorPalettes = Object.keys(defaultPresetColors).map(colorKey => {\n    const colors = generate(token[colorKey], {\n      theme: 'dark'\n    });\n    return Array.from({\n      length: 10\n    }, () => 1).reduce((prev, _, i) => {\n      prev[`${colorKey}-${i + 1}`] = colors[i];\n      prev[`${colorKey}${i + 1}`] = colors[i];\n      return prev;\n    }, {});\n  }).reduce((prev, cur) => {\n    // biome-ignore lint/style/noParameterAssign: it is a reduce\n    prev = Object.assign(Object.assign({}, prev), cur);\n    return prev;\n  }, {});\n  const mergedMapToken = mapToken !== null && mapToken !== void 0 ? mapToken : defaultAlgorithm(token);\n  const colorMapToken = genColorMapToken(token, {\n    generateColorPalettes,\n    generateNeutralColorPalettes\n  });\n  return Object.assign(Object.assign(Object.assign(Object.assign({}, mergedMapToken), colorPalettes), colorMapToken), {\n    // Customize selected item background color\n    // https://github.com/ant-design/ant-design/issues/30524#issuecomment-871961867\n    colorPrimaryBg: colorMapToken.colorPrimaryBorder,\n    colorPrimaryBgHover: colorMapToken.colorPrimaryBorderHover\n  });\n};\nexport default derivative;"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,gBAAgB,MAAM,YAAY;AACzC,SAASC,mBAAmB,QAAQ,SAAS;AAC7C,OAAOC,gBAAgB,MAAM,4BAA4B;AACzD,SAASC,qBAAqB,EAAEC,4BAA4B,QAAQ,UAAU;AAC9E,MAAMC,UAAU,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;EACtC,MAAMC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACT,mBAAmB,CAAC,CAACU,GAAG,CAACC,QAAQ,IAAI;IACrE,MAAMC,MAAM,GAAGd,QAAQ,CAACO,KAAK,CAACM,QAAQ,CAAC,EAAE;MACvCE,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAOC,KAAK,CAACC,IAAI,CAAC;MAChBC,MAAM,EAAE;IACV,CAAC,EAAE,MAAM,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,CAAC,EAAEC,CAAC,KAAK;MACjCF,IAAI,CAAC,GAAGP,QAAQ,IAAIS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGR,MAAM,CAACQ,CAAC,CAAC;MACxCF,IAAI,CAAC,GAAGP,QAAQ,GAAGS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGR,MAAM,CAACQ,CAAC,CAAC;MACvC,OAAOF,IAAI;IACb,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAACD,MAAM,CAAC,CAACC,IAAI,EAAEG,GAAG,KAAK;IACvB;IACAH,IAAI,GAAGV,MAAM,CAACc,MAAM,CAACd,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,EAAEJ,IAAI,CAAC,EAAEG,GAAG,CAAC;IAClD,OAAOH,IAAI;EACb,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,MAAMK,cAAc,GAAGjB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGP,gBAAgB,CAACM,KAAK,CAAC;EACpG,MAAMmB,aAAa,GAAGvB,gBAAgB,CAACI,KAAK,EAAE;IAC5CH,qBAAqB;IACrBC;EACF,CAAC,CAAC;EACF,OAAOK,MAAM,CAACc,MAAM,CAACd,MAAM,CAACc,MAAM,CAACd,MAAM,CAACc,MAAM,CAACd,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,EAAEC,cAAc,CAAC,EAAEhB,aAAa,CAAC,EAAEiB,aAAa,CAAC,EAAE;IAClH;IACA;IACAC,cAAc,EAAED,aAAa,CAACE,kBAAkB;IAChDC,mBAAmB,EAAEH,aAAa,CAACI;EACrC,CAAC,CAAC;AACJ,CAAC;AACD,eAAexB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
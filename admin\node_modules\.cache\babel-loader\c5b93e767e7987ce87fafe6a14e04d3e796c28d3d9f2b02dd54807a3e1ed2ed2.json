{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport EditOutlined from \"@ant-design/icons/es/icons/EditOutlined\";\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { isStyleSupport } from '../../_util/styleChecker';\nimport { ConfigContext } from '../../config-provider';\nimport useLocale from '../../locale/useLocale';\nimport Tooltip from '../../tooltip';\nimport Editable from '../Editable';\nimport useCopyClick from '../hooks/useCopyClick';\nimport useMergedConfig from '../hooks/useMergedConfig';\nimport usePrevious from '../hooks/usePrevious';\nimport useTooltipProps from '../hooks/useTooltipProps';\nimport Typography from '../Typography';\nimport CopyBtn from './CopyBtn';\nimport Ellipsis from './Ellipsis';\nimport EllipsisTooltip from './EllipsisTooltip';\nimport { isEleEllipsis, isValidText } from './util';\nfunction wrapperDecorations(_ref, content) {\n  let {\n    mark,\n    code,\n    underline,\n    delete: del,\n    strong,\n    keyboard,\n    italic\n  } = _ref;\n  let currentContent = content;\n  function wrap(tag, needed) {\n    if (!needed) {\n      return;\n    }\n    currentContent = /*#__PURE__*/React.createElement(tag, {}, currentContent);\n  }\n  wrap('strong', strong);\n  wrap('u', underline);\n  wrap('del', del);\n  wrap('code', code);\n  wrap('mark', mark);\n  wrap('kbd', keyboard);\n  wrap('i', italic);\n  return currentContent;\n}\nconst ELLIPSIS_STR = '...';\nconst DECORATION_PROPS = ['delete', 'mark', 'code', 'underline', 'strong', 'keyboard', 'italic'];\nconst Base = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      style,\n      type,\n      disabled,\n      children,\n      ellipsis,\n      editable,\n      copyable,\n      component,\n      title\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"type\", \"disabled\", \"children\", \"ellipsis\", \"editable\", \"copyable\", \"component\", \"title\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const [textLocale] = useLocale('Text');\n  const typographyRef = React.useRef(null);\n  const editIconRef = React.useRef(null);\n  // ============================ MISC ============================\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  const textProps = omit(restProps, DECORATION_PROPS);\n  // ========================== Editable ==========================\n  const [enableEdit, editConfig] = useMergedConfig(editable);\n  const [editing, setEditing] = useMergedState(false, {\n    value: editConfig.editing\n  });\n  const {\n    triggerType = ['icon']\n  } = editConfig;\n  const triggerEdit = edit => {\n    var _a;\n    if (edit) {\n      (_a = editConfig.onStart) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    }\n    setEditing(edit);\n  };\n  // Focus edit icon when back\n  const prevEditing = usePrevious(editing);\n  useLayoutEffect(() => {\n    var _a;\n    if (!editing && prevEditing) {\n      (_a = editIconRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [editing]);\n  const onEditClick = e => {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    triggerEdit(true);\n  };\n  const onEditChange = value => {\n    var _a;\n    (_a = editConfig.onChange) === null || _a === void 0 ? void 0 : _a.call(editConfig, value);\n    triggerEdit(false);\n  };\n  const onEditCancel = () => {\n    var _a;\n    (_a = editConfig.onCancel) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    triggerEdit(false);\n  };\n  // ========================== Copyable ==========================\n  const [enableCopy, copyConfig] = useMergedConfig(copyable);\n  const {\n    copied,\n    copyLoading,\n    onClick: onCopyClick\n  } = useCopyClick({\n    copyConfig,\n    children\n  });\n  // ========================== Ellipsis ==========================\n  const [isLineClampSupport, setIsLineClampSupport] = React.useState(false);\n  const [isTextOverflowSupport, setIsTextOverflowSupport] = React.useState(false);\n  const [isJsEllipsis, setIsJsEllipsis] = React.useState(false);\n  const [isNativeEllipsis, setIsNativeEllipsis] = React.useState(false);\n  const [isNativeVisible, setIsNativeVisible] = React.useState(true);\n  const [enableEllipsis, ellipsisConfig] = useMergedConfig(ellipsis, {\n    expandable: false,\n    symbol: isExpanded => isExpanded ? textLocale === null || textLocale === void 0 ? void 0 : textLocale.collapse : textLocale === null || textLocale === void 0 ? void 0 : textLocale.expand\n  });\n  const [expanded, setExpanded] = useMergedState(ellipsisConfig.defaultExpanded || false, {\n    value: ellipsisConfig.expanded\n  });\n  const mergedEnableEllipsis = enableEllipsis && (!expanded || ellipsisConfig.expandable === 'collapsible');\n  // Shared prop to reduce bundle size\n  const {\n    rows = 1\n  } = ellipsisConfig;\n  const needMeasureEllipsis = React.useMemo(() =>\n  // Disable ellipsis\n  mergedEnableEllipsis && (\n  // Provide suffix\n  ellipsisConfig.suffix !== undefined || ellipsisConfig.onEllipsis ||\n  // Can't use css ellipsis since we need to provide the place for button\n  ellipsisConfig.expandable || enableEdit || enableCopy), [mergedEnableEllipsis, ellipsisConfig, enableEdit, enableCopy]);\n  useLayoutEffect(() => {\n    if (enableEllipsis && !needMeasureEllipsis) {\n      setIsLineClampSupport(isStyleSupport('webkitLineClamp'));\n      setIsTextOverflowSupport(isStyleSupport('textOverflow'));\n    }\n  }, [needMeasureEllipsis, enableEllipsis]);\n  const [cssEllipsis, setCssEllipsis] = React.useState(mergedEnableEllipsis);\n  const canUseCssEllipsis = React.useMemo(() => {\n    if (needMeasureEllipsis) {\n      return false;\n    }\n    if (rows === 1) {\n      return isTextOverflowSupport;\n    }\n    return isLineClampSupport;\n  }, [needMeasureEllipsis, isTextOverflowSupport, isLineClampSupport]);\n  // We use effect to change from css ellipsis to js ellipsis.\n  // To make SSR still can see the ellipsis.\n  useLayoutEffect(() => {\n    setCssEllipsis(canUseCssEllipsis && mergedEnableEllipsis);\n  }, [canUseCssEllipsis, mergedEnableEllipsis]);\n  const isMergedEllipsis = mergedEnableEllipsis && (cssEllipsis ? isNativeEllipsis : isJsEllipsis);\n  const cssTextOverflow = mergedEnableEllipsis && rows === 1 && cssEllipsis;\n  const cssLineClamp = mergedEnableEllipsis && rows > 1 && cssEllipsis;\n  // >>>>> Expand\n  const onExpandClick = (e, info) => {\n    var _a;\n    setExpanded(info.expanded);\n    (_a = ellipsisConfig.onExpand) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, e, info);\n  };\n  const [ellipsisWidth, setEllipsisWidth] = React.useState(0);\n  const onResize = _ref2 => {\n    let {\n      offsetWidth\n    } = _ref2;\n    setEllipsisWidth(offsetWidth);\n  };\n  // >>>>> JS Ellipsis\n  const onJsEllipsis = jsEllipsis => {\n    var _a;\n    setIsJsEllipsis(jsEllipsis);\n    // Trigger if changed\n    if (isJsEllipsis !== jsEllipsis) {\n      (_a = ellipsisConfig.onEllipsis) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, jsEllipsis);\n    }\n  };\n  // >>>>> Native ellipsis\n  React.useEffect(() => {\n    const textEle = typographyRef.current;\n    if (enableEllipsis && cssEllipsis && textEle) {\n      const currentEllipsis = isEleEllipsis(textEle);\n      if (isNativeEllipsis !== currentEllipsis) {\n        setIsNativeEllipsis(currentEllipsis);\n      }\n    }\n  }, [enableEllipsis, cssEllipsis, children, cssLineClamp, isNativeVisible, ellipsisWidth]);\n  // https://github.com/ant-design/ant-design/issues/36786\n  // Use IntersectionObserver to check if element is invisible\n  React.useEffect(() => {\n    const textEle = typographyRef.current;\n    if (typeof IntersectionObserver === 'undefined' || !textEle || !cssEllipsis || !mergedEnableEllipsis) {\n      return;\n    }\n    /* eslint-disable-next-line compat/compat */\n    const observer = new IntersectionObserver(() => {\n      setIsNativeVisible(!!textEle.offsetParent);\n    });\n    observer.observe(textEle);\n    return () => {\n      observer.disconnect();\n    };\n  }, [cssEllipsis, mergedEnableEllipsis]);\n  // ========================== Tooltip ===========================\n  const tooltipProps = useTooltipProps(ellipsisConfig.tooltip, editConfig.text, children);\n  const topAriaLabel = React.useMemo(() => {\n    if (!enableEllipsis || cssEllipsis) {\n      return undefined;\n    }\n    return [editConfig.text, children, title, tooltipProps.title].find(isValidText);\n  }, [enableEllipsis, cssEllipsis, title, tooltipProps.title, isMergedEllipsis]);\n  // =========================== Render ===========================\n  // >>>>>>>>>>> Editing input\n  if (editing) {\n    return /*#__PURE__*/React.createElement(Editable, {\n      value: (_a = editConfig.text) !== null && _a !== void 0 ? _a : typeof children === 'string' ? children : '',\n      onSave: onEditChange,\n      onCancel: onEditCancel,\n      onEnd: editConfig.onEnd,\n      prefixCls: prefixCls,\n      className: className,\n      style: style,\n      direction: direction,\n      component: component,\n      maxLength: editConfig.maxLength,\n      autoSize: editConfig.autoSize,\n      enterIcon: editConfig.enterIcon\n    });\n  }\n  // >>>>>>>>>>> Typography\n  // Expand\n  const renderExpand = () => {\n    const {\n      expandable,\n      symbol\n    } = ellipsisConfig;\n    return expandable ? (/*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      key: \"expand\",\n      className: \"\".concat(prefixCls, \"-\").concat(expanded ? 'collapse' : 'expand'),\n      onClick: e => onExpandClick(e, {\n        expanded: !expanded\n      }),\n      \"aria-label\": expanded ? textLocale.collapse : textLocale === null || textLocale === void 0 ? void 0 : textLocale.expand\n    }, typeof symbol === 'function' ? symbol(expanded) : symbol)) : null;\n  };\n  // Edit\n  const renderEdit = () => {\n    if (!enableEdit) {\n      return;\n    }\n    const {\n      icon,\n      tooltip,\n      tabIndex\n    } = editConfig;\n    const editTitle = toArray(tooltip)[0] || (textLocale === null || textLocale === void 0 ? void 0 : textLocale.edit);\n    const ariaLabel = typeof editTitle === 'string' ? editTitle : '';\n    return triggerType.includes('icon') ? (/*#__PURE__*/React.createElement(Tooltip, {\n      key: \"edit\",\n      title: tooltip === false ? '' : editTitle\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      ref: editIconRef,\n      className: \"\".concat(prefixCls, \"-edit\"),\n      onClick: onEditClick,\n      \"aria-label\": ariaLabel,\n      tabIndex: tabIndex\n    }, icon || /*#__PURE__*/React.createElement(EditOutlined, {\n      role: \"button\"\n    })))) : null;\n  };\n  // Copy\n  const renderCopy = () => {\n    if (!enableCopy) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(CopyBtn, Object.assign({\n      key: \"copy\"\n    }, copyConfig, {\n      prefixCls: prefixCls,\n      copied: copied,\n      locale: textLocale,\n      onCopy: onCopyClick,\n      loading: copyLoading,\n      iconOnly: children === null || children === undefined\n    }));\n  };\n  const renderOperations = canEllipsis => [canEllipsis && renderExpand(), renderEdit(), renderCopy()];\n  const renderEllipsis = canEllipsis => [canEllipsis && !expanded && (/*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    key: \"ellipsis\"\n  }, ELLIPSIS_STR)), ellipsisConfig.suffix, renderOperations(canEllipsis)];\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onResize,\n    disabled: !mergedEnableEllipsis\n  }, resizeRef => (/*#__PURE__*/React.createElement(EllipsisTooltip, {\n    tooltipProps: tooltipProps,\n    enableEllipsis: mergedEnableEllipsis,\n    isEllipsis: isMergedEllipsis\n  }, /*#__PURE__*/React.createElement(Typography, Object.assign({\n    className: classNames({\n      [\"\".concat(prefixCls, \"-\").concat(type)]: type,\n      [\"\".concat(prefixCls, \"-disabled\")]: disabled,\n      [\"\".concat(prefixCls, \"-ellipsis\")]: enableEllipsis,\n      [\"\".concat(prefixCls, \"-ellipsis-single-line\")]: cssTextOverflow,\n      [\"\".concat(prefixCls, \"-ellipsis-multiple-line\")]: cssLineClamp\n    }, className),\n    prefixCls: customizePrefixCls,\n    style: Object.assign(Object.assign({}, style), {\n      WebkitLineClamp: cssLineClamp ? rows : undefined\n    }),\n    component: component,\n    ref: composeRef(resizeRef, typographyRef, ref),\n    direction: direction,\n    onClick: triggerType.includes('text') ? onEditClick : undefined,\n    \"aria-label\": topAriaLabel === null || topAriaLabel === void 0 ? void 0 : topAriaLabel.toString(),\n    title: title\n  }, textProps), /*#__PURE__*/React.createElement(Ellipsis, {\n    enableMeasure: mergedEnableEllipsis && !cssEllipsis,\n    text: children,\n    rows: rows,\n    width: ellipsisWidth,\n    onEllipsis: onJsEllipsis,\n    expanded: expanded,\n    miscDeps: [copied, expanded, copyLoading, enableEdit, enableCopy, textLocale].concat(_toConsumableArray(DECORATION_PROPS.map(key => props[key])))\n  }, (node, canEllipsis) => wrapperDecorations(props, /*#__PURE__*/React.createElement(React.Fragment, null, node.length > 0 && canEllipsis && !expanded && topAriaLabel ? (/*#__PURE__*/React.createElement(\"span\", {\n    key: \"show-content\",\n    \"aria-hidden\": true\n  }, node)) : node, renderEllipsis(canEllipsis))))))));\n});\nexport default Base;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
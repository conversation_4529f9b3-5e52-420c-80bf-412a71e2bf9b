{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { clearFix, resetComponent, resetIcon } from '../../style';\nimport { genCollapseMotion, initSlideMotion, initZoomMotion } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport getHorizontalStyle from './horizontal';\nimport getRTLStyle from './rtl';\nimport getThemeStyle from './theme';\nimport getVerticalStyle from './vertical';\nconst genMenuItemStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    motionDurationMid,\n    motionEaseInOut,\n    motionEaseOut,\n    iconCls,\n    iconSize,\n    iconMarginInlineEnd\n  } = token;\n  return {\n    // >>>>> Item\n    [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu-title\")]: {\n      position: 'relative',\n      display: 'block',\n      margin: 0,\n      whiteSpace: 'nowrap',\n      cursor: 'pointer',\n      transition: [\"border-color \".concat(motionDurationSlow), \"background \".concat(motionDurationSlow), \"padding calc(\".concat(motionDurationSlow, \" + 0.1s) \").concat(motionEaseInOut)].join(','),\n      [\"\".concat(componentCls, \"-item-icon, \").concat(iconCls)]: {\n        minWidth: iconSize,\n        fontSize: iconSize,\n        transition: [\"font-size \".concat(motionDurationMid, \" \").concat(motionEaseOut), \"margin \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"color \".concat(motionDurationSlow)].join(','),\n        '+ span': {\n          marginInlineStart: iconMarginInlineEnd,\n          opacity: 1,\n          transition: [\"opacity \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"margin \".concat(motionDurationSlow), \"color \".concat(motionDurationSlow)].join(',')\n        }\n      },\n      [\"\".concat(componentCls, \"-item-icon\")]: Object.assign({}, resetIcon()),\n      [\"&\".concat(componentCls, \"-item-only-child\")]: {\n        [\"> \".concat(iconCls, \", > \").concat(componentCls, \"-item-icon\")]: {\n          marginInlineEnd: 0\n        }\n      }\n    },\n    // Disabled state sets text to gray and nukes hover/tab effects\n    [\"\".concat(componentCls, \"-item-disabled, \").concat(componentCls, \"-submenu-disabled\")]: {\n      background: 'none !important',\n      cursor: 'not-allowed',\n      '&::after': {\n        borderColor: 'transparent !important'\n      },\n      a: {\n        color: 'inherit !important',\n        cursor: 'not-allowed',\n        pointerEvents: 'none'\n      },\n      [\"> \".concat(componentCls, \"-submenu-title\")]: {\n        color: 'inherit !important',\n        cursor: 'not-allowed'\n      }\n    }\n  };\n};\nconst genSubMenuArrowStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    motionEaseInOut,\n    borderRadius,\n    menuArrowSize,\n    menuArrowOffset\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-submenu\")]: {\n      '&-expand-icon, &-arrow': {\n        position: 'absolute',\n        top: '50%',\n        insetInlineEnd: token.margin,\n        width: menuArrowSize,\n        color: 'currentcolor',\n        transform: 'translateY(-50%)',\n        transition: \"transform \".concat(motionDurationSlow, \" \").concat(motionEaseInOut, \", opacity \").concat(motionDurationSlow)\n      },\n      '&-arrow': {\n        // →\n        '&::before, &::after': {\n          position: 'absolute',\n          width: token.calc(menuArrowSize).mul(0.6).equal(),\n          height: token.calc(menuArrowSize).mul(0.15).equal(),\n          backgroundColor: 'currentcolor',\n          borderRadius,\n          transition: [\"background \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"transform \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"top \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"color \".concat(motionDurationSlow, \" \").concat(motionEaseInOut)].join(','),\n          content: '\"\"'\n        },\n        '&::before': {\n          transform: \"rotate(45deg) translateY(\".concat(unit(token.calc(menuArrowOffset).mul(-1).equal()), \")\")\n        },\n        '&::after': {\n          transform: \"rotate(-45deg) translateY(\".concat(unit(menuArrowOffset), \")\")\n        }\n      }\n    }\n  };\n};\n// =============================== Base ===============================\nconst getBaseStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    fontSize,\n    motionDurationSlow,\n    motionDurationMid,\n    motionEaseInOut,\n    paddingXS,\n    padding,\n    colorSplit,\n    lineWidth,\n    zIndexPopup,\n    borderRadiusLG,\n    subMenuItemBorderRadius,\n    menuArrowSize,\n    menuArrowOffset,\n    lineType,\n    groupTitleLineHeight,\n    groupTitleFontSize\n  } = token;\n  return [\n  // Misc\n  {\n    '': {\n      [componentCls]: Object.assign(Object.assign({}, clearFix()), {\n        // Hidden\n        '&-hidden': {\n          display: 'none'\n        }\n      })\n    },\n    [\"\".concat(componentCls, \"-submenu-hidden\")]: {\n      display: 'none'\n    }\n  }, {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), clearFix()), {\n      marginBottom: 0,\n      paddingInlineStart: 0,\n      // Override default ul/ol\n      fontSize,\n      lineHeight: 0,\n      listStyle: 'none',\n      outline: 'none',\n      // Magic cubic here but smooth transition\n      transition: \"width \".concat(motionDurationSlow, \" cubic-bezier(0.2, 0, 0, 1) 0s\"),\n      'ul, ol': {\n        margin: 0,\n        padding: 0,\n        listStyle: 'none'\n      },\n      // Overflow ellipsis\n      '&-overflow': {\n        display: 'flex',\n        [\"\".concat(componentCls, \"-item\")]: {\n          flex: 'none'\n        }\n      },\n      [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu, \").concat(componentCls, \"-submenu-title\")]: {\n        borderRadius: token.itemBorderRadius\n      },\n      [\"\".concat(componentCls, \"-item-group-title\")]: {\n        padding: \"\".concat(unit(paddingXS), \" \").concat(unit(padding)),\n        fontSize: groupTitleFontSize,\n        lineHeight: groupTitleLineHeight,\n        transition: \"all \".concat(motionDurationSlow)\n      },\n      [\"&-horizontal \".concat(componentCls, \"-submenu\")]: {\n        transition: [\"border-color \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"background \".concat(motionDurationSlow, \" \").concat(motionEaseInOut)].join(',')\n      },\n      [\"\".concat(componentCls, \"-submenu, \").concat(componentCls, \"-submenu-inline\")]: {\n        transition: [\"border-color \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"background \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"padding \".concat(motionDurationMid, \" \").concat(motionEaseInOut)].join(',')\n      },\n      [\"\".concat(componentCls, \"-submenu \").concat(componentCls, \"-sub\")]: {\n        cursor: 'initial',\n        transition: [\"background \".concat(motionDurationSlow, \" \").concat(motionEaseInOut), \"padding \".concat(motionDurationSlow, \" \").concat(motionEaseInOut)].join(',')\n      },\n      [\"\".concat(componentCls, \"-title-content\")]: {\n        transition: \"color \".concat(motionDurationSlow),\n        '&-with-extra': {\n          display: 'inline-flex',\n          alignItems: 'center',\n          width: '100%'\n        },\n        // https://github.com/ant-design/ant-design/issues/41143\n        [\"> \".concat(antCls, \"-typography-ellipsis-single-line\")]: {\n          display: 'inline',\n          verticalAlign: 'unset'\n        },\n        [\"\".concat(componentCls, \"-item-extra\")]: {\n          marginInlineStart: 'auto',\n          paddingInlineStart: token.padding\n        }\n      },\n      [\"\".concat(componentCls, \"-item a\")]: {\n        '&::before': {\n          position: 'absolute',\n          inset: 0,\n          backgroundColor: 'transparent',\n          content: '\"\"'\n        }\n      },\n      // Removed a Badge related style seems it's safe\n      // https://github.com/ant-design/ant-design/issues/19809\n      // >>>>> Divider\n      [\"\".concat(componentCls, \"-item-divider\")]: {\n        overflow: 'hidden',\n        lineHeight: 0,\n        borderColor: colorSplit,\n        borderStyle: lineType,\n        borderWidth: 0,\n        borderTopWidth: lineWidth,\n        marginBlock: lineWidth,\n        padding: 0,\n        '&-dashed': {\n          borderStyle: 'dashed'\n        }\n      }\n    }), genMenuItemStyle(token)), {\n      [\"\".concat(componentCls, \"-item-group\")]: {\n        [\"\".concat(componentCls, \"-item-group-list\")]: {\n          margin: 0,\n          padding: 0,\n          [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu-title\")]: {\n            paddingInline: \"\".concat(unit(token.calc(fontSize).mul(2).equal()), \" \").concat(unit(padding))\n          }\n        }\n      },\n      // ======================= Sub Menu =======================\n      '&-submenu': {\n        '&-popup': {\n          position: 'absolute',\n          zIndex: zIndexPopup,\n          borderRadius: borderRadiusLG,\n          boxShadow: 'none',\n          transformOrigin: '0 0',\n          [\"&\".concat(componentCls, \"-submenu\")]: {\n            background: 'transparent'\n          },\n          // https://github.com/ant-design/ant-design/issues/13955\n          '&::before': {\n            position: 'absolute',\n            inset: 0,\n            zIndex: -1,\n            width: '100%',\n            height: '100%',\n            opacity: 0,\n            content: '\"\"'\n          },\n          [\"> \".concat(componentCls)]: Object.assign(Object.assign(Object.assign({\n            borderRadius: borderRadiusLG\n          }, genMenuItemStyle(token)), genSubMenuArrowStyle(token)), {\n            [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu > \").concat(componentCls, \"-submenu-title\")]: {\n              borderRadius: subMenuItemBorderRadius\n            },\n            [\"\".concat(componentCls, \"-submenu-title::after\")]: {\n              transition: \"transform \".concat(motionDurationSlow, \" \").concat(motionEaseInOut)\n            }\n          })\n        },\n        [\"\\n          &-placement-leftTop,\\n          &-placement-bottomRight,\\n          \"]: {\n          transformOrigin: '100% 0'\n        },\n        [\"\\n          &-placement-leftBottom,\\n          &-placement-topRight,\\n          \"]: {\n          transformOrigin: '100% 100%'\n        },\n        [\"\\n          &-placement-rightBottom,\\n          &-placement-topLeft,\\n          \"]: {\n          transformOrigin: '0 100%'\n        },\n        [\"\\n          &-placement-bottomLeft,\\n          &-placement-rightTop,\\n          \"]: {\n          transformOrigin: '0 0'\n        },\n        [\"\\n          &-placement-leftTop,\\n          &-placement-leftBottom\\n          \"]: {\n          paddingInlineEnd: token.paddingXS\n        },\n        [\"\\n          &-placement-rightTop,\\n          &-placement-rightBottom\\n          \"]: {\n          paddingInlineStart: token.paddingXS\n        },\n        [\"\\n          &-placement-topRight,\\n          &-placement-topLeft\\n          \"]: {\n          paddingBottom: token.paddingXS\n        },\n        [\"\\n          &-placement-bottomRight,\\n          &-placement-bottomLeft\\n          \"]: {\n          paddingTop: token.paddingXS\n        }\n      }\n    }), genSubMenuArrowStyle(token)), {\n      [\"&-inline-collapsed \".concat(componentCls, \"-submenu-arrow,\\n        &-inline \").concat(componentCls, \"-submenu-arrow\")]: {\n        // ↓\n        '&::before': {\n          transform: \"rotate(-45deg) translateX(\".concat(unit(menuArrowOffset), \")\")\n        },\n        '&::after': {\n          transform: \"rotate(45deg) translateX(\".concat(unit(token.calc(menuArrowOffset).mul(-1).equal()), \")\")\n        }\n      },\n      [\"\".concat(componentCls, \"-submenu-open\").concat(componentCls, \"-submenu-inline > \").concat(componentCls, \"-submenu-title > \").concat(componentCls, \"-submenu-arrow\")]: {\n        // ↑\n        transform: \"translateY(\".concat(unit(token.calc(menuArrowSize).mul(0.2).mul(-1).equal()), \")\"),\n        '&::after': {\n          transform: \"rotate(-45deg) translateX(\".concat(unit(token.calc(menuArrowOffset).mul(-1).equal()), \")\")\n        },\n        '&::before': {\n          transform: \"rotate(45deg) translateX(\".concat(unit(menuArrowOffset), \")\")\n        }\n      }\n    })\n  },\n  // Integration with header element so menu items have the same height\n  {\n    [\"\".concat(antCls, \"-layout-header\")]: {\n      [componentCls]: {\n        lineHeight: 'inherit'\n      }\n    }\n  }];\n};\nexport const prepareComponentToken = token => {\n  var _a, _b, _c;\n  const {\n    colorPrimary,\n    colorError,\n    colorTextDisabled,\n    colorErrorBg,\n    colorText,\n    colorTextDescription,\n    colorBgContainer,\n    colorFillAlter,\n    colorFillContent,\n    lineWidth,\n    lineWidthBold,\n    controlItemBgActive,\n    colorBgTextHover,\n    controlHeightLG,\n    lineHeight,\n    colorBgElevated,\n    marginXXS,\n    padding,\n    fontSize,\n    controlHeightSM,\n    fontSizeLG,\n    colorTextLightSolid,\n    colorErrorHover\n  } = token;\n  const activeBarWidth = (_a = token.activeBarWidth) !== null && _a !== void 0 ? _a : 0;\n  const activeBarBorderWidth = (_b = token.activeBarBorderWidth) !== null && _b !== void 0 ? _b : lineWidth;\n  const itemMarginInline = (_c = token.itemMarginInline) !== null && _c !== void 0 ? _c : token.marginXXS;\n  const colorTextDark = new FastColor(colorTextLightSolid).setA(0.65).toRgbString();\n  return {\n    dropdownWidth: 160,\n    zIndexPopup: token.zIndexPopupBase + 50,\n    radiusItem: token.borderRadiusLG,\n    itemBorderRadius: token.borderRadiusLG,\n    radiusSubMenuItem: token.borderRadiusSM,\n    subMenuItemBorderRadius: token.borderRadiusSM,\n    colorItemText: colorText,\n    itemColor: colorText,\n    colorItemTextHover: colorText,\n    itemHoverColor: colorText,\n    colorItemTextHoverHorizontal: colorPrimary,\n    horizontalItemHoverColor: colorPrimary,\n    colorGroupTitle: colorTextDescription,\n    groupTitleColor: colorTextDescription,\n    colorItemTextSelected: colorPrimary,\n    itemSelectedColor: colorPrimary,\n    subMenuItemSelectedColor: colorPrimary,\n    colorItemTextSelectedHorizontal: colorPrimary,\n    horizontalItemSelectedColor: colorPrimary,\n    colorItemBg: colorBgContainer,\n    itemBg: colorBgContainer,\n    colorItemBgHover: colorBgTextHover,\n    itemHoverBg: colorBgTextHover,\n    colorItemBgActive: colorFillContent,\n    itemActiveBg: controlItemBgActive,\n    colorSubItemBg: colorFillAlter,\n    subMenuItemBg: colorFillAlter,\n    colorItemBgSelected: controlItemBgActive,\n    itemSelectedBg: controlItemBgActive,\n    colorItemBgSelectedHorizontal: 'transparent',\n    horizontalItemSelectedBg: 'transparent',\n    colorActiveBarWidth: 0,\n    activeBarWidth,\n    colorActiveBarHeight: lineWidthBold,\n    activeBarHeight: lineWidthBold,\n    colorActiveBarBorderSize: lineWidth,\n    activeBarBorderWidth,\n    // Disabled\n    colorItemTextDisabled: colorTextDisabled,\n    itemDisabledColor: colorTextDisabled,\n    // Danger\n    colorDangerItemText: colorError,\n    dangerItemColor: colorError,\n    colorDangerItemTextHover: colorError,\n    dangerItemHoverColor: colorError,\n    colorDangerItemTextSelected: colorError,\n    dangerItemSelectedColor: colorError,\n    colorDangerItemBgActive: colorErrorBg,\n    dangerItemActiveBg: colorErrorBg,\n    colorDangerItemBgSelected: colorErrorBg,\n    dangerItemSelectedBg: colorErrorBg,\n    itemMarginInline,\n    horizontalItemBorderRadius: 0,\n    horizontalItemHoverBg: 'transparent',\n    itemHeight: controlHeightLG,\n    groupTitleLineHeight: lineHeight,\n    collapsedWidth: controlHeightLG * 2,\n    popupBg: colorBgElevated,\n    itemMarginBlock: marginXXS,\n    itemPaddingInline: padding,\n    horizontalLineHeight: \"\".concat(controlHeightLG * 1.15, \"px\"),\n    iconSize: fontSize,\n    iconMarginInlineEnd: controlHeightSM - fontSize,\n    collapsedIconSize: fontSizeLG,\n    groupTitleFontSize: fontSize,\n    // Disabled\n    darkItemDisabledColor: new FastColor(colorTextLightSolid).setA(0.25).toRgbString(),\n    // Dark\n    darkItemColor: colorTextDark,\n    darkDangerItemColor: colorError,\n    darkItemBg: '#001529',\n    darkPopupBg: '#001529',\n    darkSubMenuItemBg: '#000c17',\n    darkItemSelectedColor: colorTextLightSolid,\n    darkItemSelectedBg: colorPrimary,\n    darkDangerItemSelectedBg: colorError,\n    darkItemHoverBg: 'transparent',\n    darkGroupTitleColor: colorTextDark,\n    darkItemHoverColor: colorTextLightSolid,\n    darkDangerItemHoverColor: colorErrorHover,\n    darkDangerItemSelectedColor: colorTextLightSolid,\n    darkDangerItemActiveBg: colorError,\n    // internal\n    itemWidth: activeBarWidth ? \"calc(100% + \".concat(activeBarBorderWidth, \"px)\") : \"calc(100% - \".concat(itemMarginInline * 2, \"px)\")\n  };\n};\n// ============================== Export ==============================\nexport default (function (prefixCls) {\n  let rootCls = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : prefixCls;\n  let injectStyle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  const useStyle = genStyleHooks('Menu', token => {\n    const {\n      colorBgElevated,\n      controlHeightLG,\n      fontSize,\n      darkItemColor,\n      darkDangerItemColor,\n      darkItemBg,\n      darkSubMenuItemBg,\n      darkItemSelectedColor,\n      darkItemSelectedBg,\n      darkDangerItemSelectedBg,\n      darkItemHoverBg,\n      darkGroupTitleColor,\n      darkItemHoverColor,\n      darkItemDisabledColor,\n      darkDangerItemHoverColor,\n      darkDangerItemSelectedColor,\n      darkDangerItemActiveBg,\n      popupBg,\n      darkPopupBg\n    } = token;\n    const menuArrowSize = token.calc(fontSize).div(7).mul(5).equal();\n    // Menu Token\n    const menuToken = mergeToken(token, {\n      menuArrowSize,\n      menuHorizontalHeight: token.calc(controlHeightLG).mul(1.15).equal(),\n      menuArrowOffset: token.calc(menuArrowSize).mul(0.25).equal(),\n      menuSubMenuBg: colorBgElevated,\n      calc: token.calc,\n      popupBg\n    });\n    const menuDarkToken = mergeToken(menuToken, {\n      itemColor: darkItemColor,\n      itemHoverColor: darkItemHoverColor,\n      groupTitleColor: darkGroupTitleColor,\n      itemSelectedColor: darkItemSelectedColor,\n      subMenuItemSelectedColor: darkItemSelectedColor,\n      itemBg: darkItemBg,\n      popupBg: darkPopupBg,\n      subMenuItemBg: darkSubMenuItemBg,\n      itemActiveBg: 'transparent',\n      itemSelectedBg: darkItemSelectedBg,\n      activeBarHeight: 0,\n      activeBarBorderWidth: 0,\n      itemHoverBg: darkItemHoverBg,\n      // Disabled\n      itemDisabledColor: darkItemDisabledColor,\n      // Danger\n      dangerItemColor: darkDangerItemColor,\n      dangerItemHoverColor: darkDangerItemHoverColor,\n      dangerItemSelectedColor: darkDangerItemSelectedColor,\n      dangerItemActiveBg: darkDangerItemActiveBg,\n      dangerItemSelectedBg: darkDangerItemSelectedBg,\n      menuSubMenuBg: darkSubMenuItemBg,\n      // Horizontal\n      horizontalItemSelectedColor: darkItemSelectedColor,\n      horizontalItemSelectedBg: darkItemSelectedBg\n    });\n    return [\n    // Basic\n    getBaseStyle(menuToken),\n    // Horizontal\n    getHorizontalStyle(menuToken),\n    // Hard code for some light style\n    // Vertical\n    getVerticalStyle(menuToken),\n    // Hard code for some light style\n    // Theme\n    getThemeStyle(menuToken, 'light'), getThemeStyle(menuDarkToken, 'dark'),\n    // RTL\n    getRTLStyle(menuToken),\n    // Motion\n    genCollapseMotion(menuToken), initSlideMotion(menuToken, 'slide-up'), initSlideMotion(menuToken, 'slide-down'), initZoomMotion(menuToken, 'zoom-big')];\n  }, prepareComponentToken, {\n    deprecatedTokens: [['colorGroupTitle', 'groupTitleColor'], ['radiusItem', 'itemBorderRadius'], ['radiusSubMenuItem', 'subMenuItemBorderRadius'], ['colorItemText', 'itemColor'], ['colorItemTextHover', 'itemHoverColor'], ['colorItemTextHoverHorizontal', 'horizontalItemHoverColor'], ['colorItemTextSelected', 'itemSelectedColor'], ['colorItemTextSelectedHorizontal', 'horizontalItemSelectedColor'], ['colorItemTextDisabled', 'itemDisabledColor'], ['colorDangerItemText', 'dangerItemColor'], ['colorDangerItemTextHover', 'dangerItemHoverColor'], ['colorDangerItemTextSelected', 'dangerItemSelectedColor'], ['colorDangerItemBgActive', 'dangerItemActiveBg'], ['colorDangerItemBgSelected', 'dangerItemSelectedBg'], ['colorItemBg', 'itemBg'], ['colorItemBgHover', 'itemHoverBg'], ['colorSubItemBg', 'subMenuItemBg'], ['colorItemBgActive', 'itemActiveBg'], ['colorItemBgSelectedHorizontal', 'horizontalItemSelectedBg'], ['colorActiveBarWidth', 'activeBarWidth'], ['colorActiveBarHeight', 'activeBarHeight'], ['colorActiveBarBorderSize', 'activeBarBorderWidth'], ['colorItemBgSelected', 'itemSelectedBg']],\n    // Dropdown will handle menu style self. We do not need to handle this.\n    injectStyle,\n    unitless: {\n      groupTitleLineHeight: true\n    }\n  });\n  return useStyle(prefixCls, rootCls);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderPending.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, message, Modal, Form, Select, Input, Row, Col, Statistic } from 'antd';\nimport { CheckOutlined, CloseOutlined, EyeOutlined, ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\n// 模拟待处理订单数据\nconst mockPendingOrders = [{\n  id: 1,\n  orderNo: 'ORD202401150003',\n  customerName: '王五',\n  customerPhone: '13800138003',\n  productName: '中国联通青春套餐',\n  operator: '中国联通',\n  amount: 79.00,\n  paymentStatus: 'unpaid',\n  priority: 'normal',\n  createdAt: '2024-01-15 12:00:00',\n  waitingTime: 2\n}, {\n  id: 2,\n  orderNo: 'ORD202401150005',\n  customerName: '钱七',\n  customerPhone: '13800138005',\n  productName: '中国移动5G尊享套餐',\n  operator: '中国移动',\n  amount: 199.00,\n  paymentStatus: 'paid',\n  priority: 'high',\n  createdAt: '2024-01-15 14:30:00',\n  waitingTime: 0.5\n}, {\n  id: 3,\n  orderNo: 'ORD202401150006',\n  customerName: '孙八',\n  customerPhone: '13800138006',\n  productName: '中国电信天翼套餐',\n  operator: '中国电信',\n  amount: 89.00,\n  paymentStatus: 'paid',\n  priority: 'urgent',\n  createdAt: '2024-01-15 15:00:00',\n  waitingTime: 0.2\n}];\nconst OrderPending = () => {\n  _s();\n  const [orders, setOrders] = useState(mockPendingOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [processModalVisible, setProcessModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [form] = Form.useForm();\n\n  // 优先级颜色映射\n  const getPriorityColor = priority => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red'\n    };\n    return colors[priority] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = priority => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急'\n    };\n    return texts[priority] || priority;\n  };\n\n  // 支付状态颜色映射\n  const getPaymentStatusColor = status => {\n    const colors = {\n      unpaid: 'red',\n      paid: 'green'\n    };\n    return colors[status] || 'default';\n  };\n\n  // 支付状态文本映射\n  const getPaymentStatusText = status => {\n    const texts = {\n      unpaid: '未支付',\n      paid: '已支付'\n    };\n    return texts[status] || status;\n  };\n\n  // 统计数据\n  const stats = {\n    total: orders.length,\n    urgent: orders.filter(order => order.priority === 'urgent').length,\n    high: orders.filter(order => order.priority === 'high').length,\n    unpaid: orders.filter(order => order.paymentStatus === 'unpaid').length\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '优先级',\n    dataIndex: 'priority',\n    key: 'priority',\n    width: 80,\n    render: priority => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getPriorityColor(priority),\n      children: getPriorityText(priority)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => {\n      const priorityOrder = {\n        urgent: 4,\n        high: 3,\n        normal: 2,\n        low: 1\n      };\n      return priorityOrder[a.priority] - priorityOrder[b.priority];\n    }\n  }, {\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '金额',\n    dataIndex: 'amount',\n    key: 'amount',\n    width: 100,\n    render: amount => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#f5222d'\n      },\n      children: [\"\\xA5\", amount.toFixed(2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '支付状态',\n    dataIndex: 'paymentStatus',\n    key: 'paymentStatus',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getPaymentStatusColor(status),\n      children: getPaymentStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '等待时间',\n    dataIndex: 'waitingTime',\n    key: 'waitingTime',\n    width: 100,\n    render: time => /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        color: time > 1 ? '#f5222d' : '#52c41a'\n      },\n      children: time < 1 ? `${Math.round(time * 60)}分钟` : `${time.toFixed(1)}小时`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.waitingTime - b.waitingTime\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(CheckOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleProcessOrder(record),\n        children: \"\\u5904\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        danger: true,\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleCancelOrder(record.id),\n        children: \"\\u53D6\\u6D88\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleProcessOrder = order => {\n    setSelectedOrder(order);\n    setProcessModalVisible(true);\n  };\n  const handleViewOrder = order => {\n    message.info(`查看订单 ${order.orderNo} 详情功能开发中...`);\n  };\n  const handleCancelOrder = id => {\n    Modal.confirm({\n      title: '确认取消订单',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 13\n      }, this),\n      content: '确定要取消这个订单吗？此操作不可恢复。',\n      onOk: () => {\n        setOrders(orders.filter(order => order.id !== id));\n        message.success('订单已取消');\n      }\n    });\n  };\n  const handleBatchProcess = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要处理的订单');\n      return;\n    }\n    Modal.confirm({\n      title: '批量处理订单',\n      content: `确定要批量处理选中的 ${selectedRowKeys.length} 个订单吗？`,\n      onOk: () => {\n        setOrders(orders.filter(order => !selectedRowKeys.includes(order.id)));\n        setSelectedRowKeys([]);\n        message.success('批量处理成功');\n      }\n    });\n  };\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockPendingOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n  const handleProcessSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 模拟处理订单\n      setOrders(orders.filter(order => order.id !== (selectedOrder === null || selectedOrder === void 0 ? void 0 : selectedOrder.id)));\n      setProcessModalVisible(false);\n      form.resetFields();\n      message.success('订单处理成功');\n    } catch (error) {\n      console.error('处理失败:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#faad14'\n        },\n        children: \"\\u5F85\\u5904\\u7406\\u8BA2\\u5355\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u5904\\u7406\\u9700\\u8981\\u4EBA\\u5DE5\\u5BA1\\u6838\\u7684\\u8BA2\\u5355\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\\u603B\\u6570\",\n            value: stats.total,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7D27\\u6025\\u8BA2\\u5355\",\n            value: stats.urgent,\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9AD8\\u4F18\\u5148\\u7EA7\",\n            value: stats.high,\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u672A\\u652F\\u4ED8\\u8BA2\\u5355\",\n            value: stats.unpaid,\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", orders.length, \" \\u4E2A\\u5F85\\u5904\\u7406\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              marginLeft: 16,\n              color: '#1890ff'\n            },\n            children: [\"\\u5DF2\\u9009\\u62E9 \", selectedRowKeys.length, \" \\u4E2A\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleBatchProcess,\n            children: \"\\u6279\\u91CF\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: orders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5904\\u7406\\u8BA2\\u5355\",\n      open: processModalVisible,\n      onOk: handleProcessSubmit,\n      onCancel: () => {\n        setProcessModalVisible(false);\n        form.resetFields();\n      },\n      okText: \"\\u786E\\u8BA4\\u5904\\u7406\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16,\n            padding: 16,\n            background: '#f5f5f5',\n            borderRadius: 6\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u8BA2\\u5355\\u4FE1\\u606F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u8BA2\\u5355\\u53F7\\uFF1A\", selectedOrder.orderNo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u5BA2\\u6237\\uFF1A\", selectedOrder.customerName, \" (\", selectedOrder.customerPhone, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u4EA7\\u54C1\\uFF1A\", selectedOrder.productName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u91D1\\u989D\\uFF1A\\xA5\", selectedOrder.amount.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"action\",\n            label: \"\\u5904\\u7406\\u52A8\\u4F5C\",\n            rules: [{\n              required: true,\n              message: '请选择处理动作'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u5904\\u7406\\u52A8\\u4F5C\",\n              children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                value: \"approve\",\n                children: \"\\u6279\\u51C6\\u8BA2\\u5355\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                value: \"reject\",\n                children: \"\\u62D2\\u7EDD\\u8BA2\\u5355\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                value: \"pending_info\",\n                children: \"\\u7B49\\u5F85\\u8865\\u5145\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"remark\",\n            label: \"\\u5904\\u7406\\u5907\\u6CE8\",\n            rules: [{\n              required: true,\n              message: '请输入处理备注'\n            }],\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              rows: 4,\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5904\\u7406\\u5907\\u6CE8\\u6216\\u62D2\\u7EDD\\u539F\\u56E0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 343,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderPending, \"RJx/FkvMdDCBhSyxg9M92ovo3/A=\", false, function () {\n  return [Form.useForm];\n});\n_c = OrderPending;\nexport default OrderPending;\nvar _c;\n$RefreshReg$(_c, \"OrderPending\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "message", "Modal", "Form", "Select", "Input", "Row", "Col", "Statistic", "CheckOutlined", "CloseOutlined", "EyeOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "mockPendingOrders", "id", "orderNo", "customerName", "customerPhone", "productName", "operator", "amount", "paymentStatus", "priority", "createdAt", "waitingTime", "OrderPending", "_s", "orders", "setOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "processModalVisible", "setProcessModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "form", "useForm", "getPriorityColor", "colors", "low", "normal", "high", "urgent", "getPriorityText", "texts", "getPaymentStatusColor", "status", "unpaid", "paid", "getPaymentStatusText", "stats", "total", "length", "filter", "order", "columns", "title", "dataIndex", "key", "width", "render", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sorter", "a", "b", "priorityOrder", "text", "code", "style", "fontSize", "_", "record", "fontWeight", "ellipsis", "strong", "toFixed", "time", "Math", "round", "size", "type", "icon", "onClick", "handleProcessOrder", "handleViewOrder", "danger", "handleCancelOrder", "info", "confirm", "content", "onOk", "success", "handleBatchProcess", "warning", "includes", "handleRefresh", "setTimeout", "handleProcessSubmit", "values", "validateFields", "resetFields", "error", "console", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "display", "justifyContent", "alignItems", "marginLeft", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "okText", "cancelText", "padding", "background", "borderRadius", "marginTop", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "Option", "rows", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderPending.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  message,\n  Modal,\n  Form,\n  Select,\n  Input,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport {\n  CheckOutlined,\n  CloseOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface PendingOrder {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  productName: string;\n  operator: string;\n  amount: number;\n  paymentStatus: 'unpaid' | 'paid';\n  priority: 'low' | 'normal' | 'high' | 'urgent';\n  createdAt: string;\n  waitingTime: number; // 等待时间（小时）\n}\n\n// 模拟待处理订单数据\nconst mockPendingOrders: PendingOrder[] = [\n  {\n    id: 1,\n    orderNo: 'ORD202401150003',\n    customerName: '王五',\n    customerPhone: '13800138003',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n    amount: 79.00,\n    paymentStatus: 'unpaid',\n    priority: 'normal',\n    createdAt: '2024-01-15 12:00:00',\n    waitingTime: 2,\n  },\n  {\n    id: 2,\n    orderNo: 'ORD202401150005',\n    customerName: '钱七',\n    customerPhone: '13800138005',\n    productName: '中国移动5G尊享套餐',\n    operator: '中国移动',\n    amount: 199.00,\n    paymentStatus: 'paid',\n    priority: 'high',\n    createdAt: '2024-01-15 14:30:00',\n    waitingTime: 0.5,\n  },\n  {\n    id: 3,\n    orderNo: 'ORD202401150006',\n    customerName: '孙八',\n    customerPhone: '13800138006',\n    productName: '中国电信天翼套餐',\n    operator: '中国电信',\n    amount: 89.00,\n    paymentStatus: 'paid',\n    priority: 'urgent',\n    createdAt: '2024-01-15 15:00:00',\n    waitingTime: 0.2,\n  },\n];\n\nconst OrderPending: React.FC = () => {\n  const [orders, setOrders] = useState<PendingOrder[]>(mockPendingOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [processModalVisible, setProcessModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<PendingOrder | null>(null);\n  const [form] = Form.useForm();\n\n  // 优先级颜色映射\n  const getPriorityColor = (priority: string) => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red',\n    };\n    return colors[priority as keyof typeof colors] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = (priority: string) => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急',\n    };\n    return texts[priority as keyof typeof texts] || priority;\n  };\n\n  // 支付状态颜色映射\n  const getPaymentStatusColor = (status: string) => {\n    const colors = {\n      unpaid: 'red',\n      paid: 'green',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n  // 支付状态文本映射\n  const getPaymentStatusText = (status: string) => {\n    const texts = {\n      unpaid: '未支付',\n      paid: '已支付',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  // 统计数据\n  const stats = {\n    total: orders.length,\n    urgent: orders.filter(order => order.priority === 'urgent').length,\n    high: orders.filter(order => order.priority === 'high').length,\n    unpaid: orders.filter(order => order.paymentStatus === 'unpaid').length,\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<PendingOrder> = [\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      width: 80,\n      render: (priority: string) => (\n        <Tag color={getPriorityColor(priority)}>\n          {getPriorityText(priority)}\n        </Tag>\n      ),\n      sorter: (a, b) => {\n        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };\n        return priorityOrder[a.priority as keyof typeof priorityOrder] - \n               priorityOrder[b.priority as keyof typeof priorityOrder];\n      },\n    },\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n    {\n      title: '金额',\n      dataIndex: 'amount',\n      key: 'amount',\n      width: 100,\n      render: (amount: number) => (\n        <Text strong style={{ color: '#f5222d' }}>\n          ¥{amount.toFixed(2)}\n        </Text>\n      ),\n    },\n    {\n      title: '支付状态',\n      dataIndex: 'paymentStatus',\n      key: 'paymentStatus',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getPaymentStatusColor(status)}>\n          {getPaymentStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '等待时间',\n      dataIndex: 'waitingTime',\n      key: 'waitingTime',\n      width: 100,\n      render: (time: number) => (\n        <Text style={{ color: time > 1 ? '#f5222d' : '#52c41a' }}>\n          {time < 1 ? `${Math.round(time * 60)}分钟` : `${time.toFixed(1)}小时`}\n        </Text>\n      ),\n      sorter: (a, b) => a.waitingTime - b.waitingTime,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<CheckOutlined />}\n            onClick={() => handleProcessOrder(record)}\n          >\n            处理\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            danger\n            size=\"small\"\n            icon={<CloseOutlined />}\n            onClick={() => handleCancelOrder(record.id)}\n          >\n            取消\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleProcessOrder = (order: PendingOrder) => {\n    setSelectedOrder(order);\n    setProcessModalVisible(true);\n  };\n\n  const handleViewOrder = (order: PendingOrder) => {\n    message.info(`查看订单 ${order.orderNo} 详情功能开发中...`);\n  };\n\n  const handleCancelOrder = (id: number) => {\n    Modal.confirm({\n      title: '确认取消订单',\n      icon: <ExclamationCircleOutlined />,\n      content: '确定要取消这个订单吗？此操作不可恢复。',\n      onOk: () => {\n        setOrders(orders.filter(order => order.id !== id));\n        message.success('订单已取消');\n      },\n    });\n  };\n\n  const handleBatchProcess = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要处理的订单');\n      return;\n    }\n    \n    Modal.confirm({\n      title: '批量处理订单',\n      content: `确定要批量处理选中的 ${selectedRowKeys.length} 个订单吗？`,\n      onOk: () => {\n        setOrders(orders.filter(order => !selectedRowKeys.includes(order.id)));\n        setSelectedRowKeys([]);\n        message.success('批量处理成功');\n      },\n    });\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockPendingOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n\n  const handleProcessSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      // 模拟处理订单\n      setOrders(orders.filter(order => order.id !== selectedOrder?.id));\n      setProcessModalVisible(false);\n      form.resetFields();\n      message.success('订单处理成功');\n    } catch (error) {\n      console.error('处理失败:', error);\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#faad14' }}>\n          待处理订单\n        </Title>\n        <Text type=\"secondary\">\n          处理需要人工审核的订单\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待处理总数\"\n              value={stats.total}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"紧急订单\"\n              value={stats.urgent}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"高优先级\"\n              value={stats.high}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"未支付订单\"\n              value={stats.unpaid}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {orders.length} 个待处理订单\n            </Text>\n            {selectedRowKeys.length > 0 && (\n              <Text style={{ marginLeft: 16, color: '#1890ff' }}>\n                已选择 {selectedRowKeys.length} 个订单\n              </Text>\n            )}\n          </div>\n          <Space>\n            {selectedRowKeys.length > 0 && (\n              <Button type=\"primary\" onClick={handleBatchProcess}>\n                批量处理\n              </Button>\n            )}\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={orders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 处理订单弹窗 */}\n      <Modal\n        title=\"处理订单\"\n        open={processModalVisible}\n        onOk={handleProcessSubmit}\n        onCancel={() => {\n          setProcessModalVisible(false);\n          form.resetFields();\n        }}\n        okText=\"确认处理\"\n        cancelText=\"取消\"\n      >\n        {selectedOrder && (\n          <div>\n            <div style={{ marginBottom: 16, padding: 16, background: '#f5f5f5', borderRadius: 6 }}>\n              <Text strong>订单信息：</Text>\n              <div style={{ marginTop: 8 }}>\n                <div>订单号：{selectedOrder.orderNo}</div>\n                <div>客户：{selectedOrder.customerName} ({selectedOrder.customerPhone})</div>\n                <div>产品：{selectedOrder.productName}</div>\n                <div>金额：¥{selectedOrder.amount.toFixed(2)}</div>\n              </div>\n            </div>\n            \n            <Form form={form} layout=\"vertical\">\n              <Form.Item\n                name=\"action\"\n                label=\"处理动作\"\n                rules={[{ required: true, message: '请选择处理动作' }]}\n              >\n                <Select placeholder=\"请选择处理动作\">\n                  <Select.Option value=\"approve\">批准订单</Select.Option>\n                  <Select.Option value=\"reject\">拒绝订单</Select.Option>\n                  <Select.Option value=\"pending_info\">等待补充信息</Select.Option>\n                </Select>\n              </Form.Item>\n              \n              <Form.Item\n                name=\"remark\"\n                label=\"处理备注\"\n                rules={[{ required: true, message: '请输入处理备注' }]}\n              >\n                <TextArea \n                  rows={4} \n                  placeholder=\"请输入处理备注或拒绝原因\"\n                />\n              </Form.Item>\n            </Form>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderPending;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SACEC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,cAAc,EACdC,yBAAyB,QACpB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGlB,UAAU;AAClC,MAAM;EAAEmB;AAAS,CAAC,GAAGb,KAAK;AAgB1B;AACA,MAAMc,iBAAiC,GAAG,CACxC;EACEC,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,KAAK;EACbC,aAAa,EAAE,QAAQ;EACvBC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,EACD;EACEV,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,MAAM;EACdC,aAAa,EAAE,MAAM;EACrBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,EACD;EACEV,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,KAAK;EACbC,aAAa,EAAE,MAAM;EACrBC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAiByB,iBAAiB,CAAC;EACvE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC6C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAsB,IAAI,CAAC;EAC7E,MAAM,CAACiD,IAAI,CAAC,GAAGxC,IAAI,CAACyC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,gBAAgB,GAAIjB,QAAgB,IAAK;IAC7C,MAAMkB,MAAM,GAAG;MACbC,GAAG,EAAE,SAAS;MACdC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAOJ,MAAM,CAAClB,QAAQ,CAAwB,IAAI,SAAS;EAC7D,CAAC;;EAED;EACA,MAAMuB,eAAe,GAAIvB,QAAgB,IAAK;IAC5C,MAAMwB,KAAK,GAAG;MACZL,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE;IACV,CAAC;IACD,OAAOE,KAAK,CAACxB,QAAQ,CAAuB,IAAIA,QAAQ;EAC1D,CAAC;;EAED;EACA,MAAMyB,qBAAqB,GAAIC,MAAc,IAAK;IAChD,MAAMR,MAAM,GAAG;MACbS,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE;IACR,CAAC;IACD,OAAOV,MAAM,CAACQ,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAIH,MAAc,IAAK;IAC/C,MAAMF,KAAK,GAAG;MACZG,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE;IACR,CAAC;IACD,OAAOJ,KAAK,CAACE,MAAM,CAAuB,IAAIA,MAAM;EACtD,CAAC;;EAED;EACA,MAAMI,KAAK,GAAG;IACZC,KAAK,EAAE1B,MAAM,CAAC2B,MAAM;IACpBV,MAAM,EAAEjB,MAAM,CAAC4B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAClC,QAAQ,KAAK,QAAQ,CAAC,CAACgC,MAAM;IAClEX,IAAI,EAAEhB,MAAM,CAAC4B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAClC,QAAQ,KAAK,MAAM,CAAC,CAACgC,MAAM;IAC9DL,MAAM,EAAEtB,MAAM,CAAC4B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACnC,aAAa,KAAK,QAAQ,CAAC,CAACiC;EACnE,CAAC;;EAED;EACA,MAAMG,OAAkC,GAAG,CACzC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGxC,QAAgB,iBACvBb,OAAA,CAACf,GAAG;MAACqE,KAAK,EAAExB,gBAAgB,CAACjB,QAAQ,CAAE;MAAA0C,QAAA,EACpCnB,eAAe,CAACvB,QAAQ;IAAC;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACN;IACDC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK;MAChB,MAAMC,aAAa,GAAG;QAAE5B,MAAM,EAAE,CAAC;QAAED,IAAI,EAAE,CAAC;QAAED,MAAM,EAAE,CAAC;QAAED,GAAG,EAAE;MAAE,CAAC;MAC/D,OAAO+B,aAAa,CAACF,CAAC,CAAChD,QAAQ,CAA+B,GACvDkD,aAAa,CAACD,CAAC,CAACjD,QAAQ,CAA+B;IAChE;EACF,CAAC,EACD;IACEoC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnBhE,OAAA,CAACE,IAAI;MAAC+D,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAZ,QAAA,EACpCS;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChBrE,OAAA;MAAAuD,QAAA,gBACEvD,OAAA;QAAKkE,KAAK,EAAE;UAAEI,UAAU,EAAE;QAAI,CAAE;QAAAf,QAAA,EAAEc,MAAM,CAAC9D;MAAY;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5D3D,OAAA;QAAKkE,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEb,KAAK,EAAE;QAAO,CAAE;QAAAC,QAAA,EAC7Cc,MAAM,CAAC7D;MAAa;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBoB,QAAQ,EAAE;EACZ,CAAC,EACD;IACEtB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnBhE,OAAA,CAACf,GAAG;MAACqE,KAAK,EAAC,MAAM;MAAAC,QAAA,EAAES;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG1C,MAAc,iBACrBX,OAAA,CAACE,IAAI;MAACsE,MAAM;MAACN,KAAK,EAAE;QAAEZ,KAAK,EAAE;MAAU,CAAE;MAAAC,QAAA,GAAC,MACvC,EAAC5C,MAAM,CAAC8D,OAAO,CAAC,CAAC,CAAC;IAAA;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAEV,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGd,MAAc,iBACrBvC,OAAA,CAACf,GAAG;MAACqE,KAAK,EAAEhB,qBAAqB,CAACC,MAAM,CAAE;MAAAgB,QAAA,EACvCb,oBAAoB,CAACH,MAAM;IAAC;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B;EAET,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGqB,IAAY,iBACnB1E,OAAA,CAACE,IAAI;MAACgE,KAAK,EAAE;QAAEZ,KAAK,EAAEoB,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAnB,QAAA,EACtDmB,IAAI,GAAG,CAAC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,GAAGA,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC;IAAI;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CACP;IACDC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC9C,WAAW,GAAG+C,CAAC,CAAC/C;EACtC,CAAC,EACD;IACEkC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnBhE,OAAA;MAAKkE,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAZ,QAAA,EAC9BS;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChBrE,OAAA,CAACjB,KAAK;MAAC8F,IAAI,EAAC,OAAO;MAAAtB,QAAA,gBACjBvD,OAAA,CAAClB,MAAM;QACLgG,IAAI,EAAC,SAAS;QACdD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE/E,OAAA,CAACN,aAAa;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBqB,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACZ,MAAM,CAAE;QAAAd,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3D,OAAA,CAAClB,MAAM;QACLgG,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE/E,OAAA,CAACJ,WAAW;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBqB,OAAO,EAAEA,CAAA,KAAME,eAAe,CAACb,MAAM,CAAE;QAAAd,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3D,OAAA,CAAClB,MAAM;QACLqG,MAAM;QACNN,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE/E,OAAA,CAACL,aAAa;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBqB,OAAO,EAAEA,CAAA,KAAMI,iBAAiB,CAACf,MAAM,CAAChE,EAAE,CAAE;QAAAkD,QAAA,EAC7C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMsB,kBAAkB,GAAIlC,KAAmB,IAAK;IAClDpB,gBAAgB,CAACoB,KAAK,CAAC;IACvBtB,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMyD,eAAe,GAAInC,KAAmB,IAAK;IAC/C7D,OAAO,CAACmG,IAAI,CAAC,QAAQtC,KAAK,CAACzC,OAAO,aAAa,CAAC;EAClD,CAAC;EAED,MAAM8E,iBAAiB,GAAI/E,EAAU,IAAK;IACxClB,KAAK,CAACmG,OAAO,CAAC;MACZrC,KAAK,EAAE,QAAQ;MACf8B,IAAI,eAAE/E,OAAA,CAACF,yBAAyB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnC4B,OAAO,EAAE,qBAAqB;MAC9BC,IAAI,EAAEA,CAAA,KAAM;QACVrE,SAAS,CAACD,MAAM,CAAC4B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC1C,EAAE,KAAKA,EAAE,CAAC,CAAC;QAClDnB,OAAO,CAACuG,OAAO,CAAC,OAAO,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIpE,eAAe,CAACuB,MAAM,KAAK,CAAC,EAAE;MAChC3D,OAAO,CAACyG,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;IAEAxG,KAAK,CAACmG,OAAO,CAAC;MACZrC,KAAK,EAAE,QAAQ;MACfsC,OAAO,EAAE,cAAcjE,eAAe,CAACuB,MAAM,QAAQ;MACrD2C,IAAI,EAAEA,CAAA,KAAM;QACVrE,SAAS,CAACD,MAAM,CAAC4B,MAAM,CAACC,KAAK,IAAI,CAACzB,eAAe,CAACsE,QAAQ,CAAC7C,KAAK,CAAC1C,EAAE,CAAC,CAAC,CAAC;QACtEkB,kBAAkB,CAAC,EAAE,CAAC;QACtBrC,OAAO,CAACuG,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1BxE,UAAU,CAAC,IAAI,CAAC;IAChByE,UAAU,CAAC,MAAM;MACf3E,SAAS,CAACf,iBAAiB,CAAC;MAC5BiB,UAAU,CAAC,KAAK,CAAC;MACjBnC,OAAO,CAACuG,OAAO,CAAC,OAAO,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMM,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMpE,IAAI,CAACqE,cAAc,CAAC,CAAC;;MAE1C;MACA9E,SAAS,CAACD,MAAM,CAAC4B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC1C,EAAE,MAAKqB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAErB,EAAE,EAAC,CAAC;MACjEoB,sBAAsB,CAAC,KAAK,CAAC;MAC7BG,IAAI,CAACsE,WAAW,CAAC,CAAC;MAClBhH,OAAO,CAACuG,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,oBACEnG,OAAA;IAAAuD,QAAA,gBACEvD,OAAA;MAAKkE,KAAK,EAAE;QAAEmC,YAAY,EAAE;MAAO,CAAE;MAAA9C,QAAA,gBACnCvD,OAAA,CAACC,KAAK;QAACqG,KAAK,EAAE,CAAE;QAACpC,KAAK,EAAE;UAAEqC,MAAM,EAAE,CAAC;UAAEjD,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR3D,OAAA,CAACE,IAAI;QAAC4E,IAAI,EAAC,WAAW;QAAAvB,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN3D,OAAA,CAACT,GAAG;MAACiH,MAAM,EAAE,EAAG;MAACtC,KAAK,EAAE;QAAEmC,YAAY,EAAE;MAAO,CAAE;MAAA9C,QAAA,gBAC/CvD,OAAA,CAACR,GAAG;QAACiH,IAAI,EAAE,CAAE;QAAAlD,QAAA,eACXvD,OAAA,CAACpB,IAAI;UAAA2E,QAAA,eACHvD,OAAA,CAACP,SAAS;YACRwD,KAAK,EAAC,gCAAO;YACbyD,KAAK,EAAE/D,KAAK,CAACC,KAAM;YACnB+D,UAAU,EAAE;cAAErD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3D,OAAA,CAACR,GAAG;QAACiH,IAAI,EAAE,CAAE;QAAAlD,QAAA,eACXvD,OAAA,CAACpB,IAAI;UAAA2E,QAAA,eACHvD,OAAA,CAACP,SAAS;YACRwD,KAAK,EAAC,0BAAM;YACZyD,KAAK,EAAE/D,KAAK,CAACR,MAAO;YACpBwE,UAAU,EAAE;cAAErD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3D,OAAA,CAACR,GAAG;QAACiH,IAAI,EAAE,CAAE;QAAAlD,QAAA,eACXvD,OAAA,CAACpB,IAAI;UAAA2E,QAAA,eACHvD,OAAA,CAACP,SAAS;YACRwD,KAAK,EAAC,0BAAM;YACZyD,KAAK,EAAE/D,KAAK,CAACT,IAAK;YAClByE,UAAU,EAAE;cAAErD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3D,OAAA,CAACR,GAAG;QAACiH,IAAI,EAAE,CAAE;QAAAlD,QAAA,eACXvD,OAAA,CAACpB,IAAI;UAAA2E,QAAA,eACHvD,OAAA,CAACP,SAAS;YACRwD,KAAK,EAAC,gCAAO;YACbyD,KAAK,EAAE/D,KAAK,CAACH,MAAO;YACpBmE,UAAU,EAAE;cAAErD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3D,OAAA,CAACpB,IAAI;MAAA2E,QAAA,gBAEHvD,OAAA;QAAKkE,KAAK,EAAE;UACV0C,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBT,YAAY,EAAE;QAChB,CAAE;QAAA9C,QAAA,gBACAvD,OAAA;UAAAuD,QAAA,gBACEvD,OAAA,CAACE,IAAI;YAACsE,MAAM;YAAAjB,QAAA,GAAC,SACT,EAACrC,MAAM,CAAC2B,MAAM,EAAC,uCACnB;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNrC,eAAe,CAACuB,MAAM,GAAG,CAAC,iBACzB7C,OAAA,CAACE,IAAI;YAACgE,KAAK,EAAE;cAAE6C,UAAU,EAAE,EAAE;cAAEzD,KAAK,EAAE;YAAU,CAAE;YAAAC,QAAA,GAAC,qBAC7C,EAACjC,eAAe,CAACuB,MAAM,EAAC,qBAC9B;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN3D,OAAA,CAACjB,KAAK;UAAAwE,QAAA,GACHjC,eAAe,CAACuB,MAAM,GAAG,CAAC,iBACzB7C,OAAA,CAAClB,MAAM;YAACgG,IAAI,EAAC,SAAS;YAACE,OAAO,EAAEU,kBAAmB;YAAAnC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACD3D,OAAA,CAAClB,MAAM;YAACkG,OAAO,EAAEa,aAAc;YAACd,IAAI,eAAE/E,OAAA,CAACH,cAAc;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN3D,OAAA,CAACnB,KAAK;QACJmE,OAAO,EAAEA,OAAQ;QACjBgE,UAAU,EAAE9F,MAAO;QACnB+F,MAAM,EAAC,IAAI;QACX7F,OAAO,EAAEA,OAAQ;QACjB8F,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC3E,KAAK,EAAE4E,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAAS5E,KAAK;QAC3C,CAAE;QACF6E,YAAY,EAAE;UACZnG,eAAe;UACfoG,QAAQ,EAAEnG;QACZ;MAAE;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP3D,OAAA,CAACb,KAAK;MACJ8D,KAAK,EAAC,0BAAM;MACZ0E,IAAI,EAAEnG,mBAAoB;MAC1BgE,IAAI,EAAEO,mBAAoB;MAC1B6B,QAAQ,EAAEA,CAAA,KAAM;QACdnG,sBAAsB,CAAC,KAAK,CAAC;QAC7BG,IAAI,CAACsE,WAAW,CAAC,CAAC;MACpB,CAAE;MACF2B,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MAAAvE,QAAA,EAEd7B,aAAa,iBACZ1B,OAAA;QAAAuD,QAAA,gBACEvD,OAAA;UAAKkE,KAAK,EAAE;YAAEmC,YAAY,EAAE,EAAE;YAAE0B,OAAO,EAAE,EAAE;YAAEC,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA1E,QAAA,gBACpFvD,OAAA,CAACE,IAAI;YAACsE,MAAM;YAAAjB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzB3D,OAAA;YAAKkE,KAAK,EAAE;cAAEgE,SAAS,EAAE;YAAE,CAAE;YAAA3E,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,GAAK,0BAAI,EAAC7B,aAAa,CAACpB,OAAO;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtC3D,OAAA;cAAAuD,QAAA,GAAK,oBAAG,EAAC7B,aAAa,CAACnB,YAAY,EAAC,IAAE,EAACmB,aAAa,CAAClB,aAAa,EAAC,GAAC;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1E3D,OAAA;cAAAuD,QAAA,GAAK,oBAAG,EAAC7B,aAAa,CAACjB,WAAW;YAAA;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC3D,OAAA;cAAAuD,QAAA,GAAK,wBAAI,EAAC7B,aAAa,CAACf,MAAM,CAAC8D,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3D,OAAA,CAACZ,IAAI;UAACwC,IAAI,EAAEA,IAAK;UAACuG,MAAM,EAAC,UAAU;UAAA5E,QAAA,gBACjCvD,OAAA,CAACZ,IAAI,CAACgJ,IAAI;YACRC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEtJ,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAqE,QAAA,eAEhDvD,OAAA,CAACX,MAAM;cAACoJ,WAAW,EAAC,4CAAS;cAAAlF,QAAA,gBAC3BvD,OAAA,CAACX,MAAM,CAACqJ,MAAM;gBAAChC,KAAK,EAAC,SAAS;gBAAAnD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eACnD3D,OAAA,CAACX,MAAM,CAACqJ,MAAM;gBAAChC,KAAK,EAAC,QAAQ;gBAAAnD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAClD3D,OAAA,CAACX,MAAM,CAACqJ,MAAM;gBAAChC,KAAK,EAAC,cAAc;gBAAAnD,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZ3D,OAAA,CAACZ,IAAI,CAACgJ,IAAI;YACRC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEtJ,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAqE,QAAA,eAEhDvD,OAAA,CAACG,QAAQ;cACPwI,IAAI,EAAE,CAAE;cACRF,WAAW,EAAC;YAAc;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAzZID,YAAsB;EAAA,QAMX5B,IAAI,CAACyC,OAAO;AAAA;AAAA+G,EAAA,GANvB5H,YAAsB;AA2Z5B,eAAeA,YAAY;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
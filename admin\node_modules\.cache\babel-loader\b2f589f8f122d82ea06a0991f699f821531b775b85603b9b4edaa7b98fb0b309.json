{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AudioTwoToneSvg from \"@ant-design/icons-svg/es/asn/AudioTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AudioTwoTone = function AudioTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AudioTwoToneSvg\n  }));\n};\n\n/**![audio](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA1NTJjNTQuMyAwIDk4LTQzLjIgOTgtOTZWMjMyYzAtNTIuOC00My43LTk2LTk4LTk2cy05OCA0My4yLTk4IDk2djIyNGMwIDUyLjggNDMuNyA5NiA5OCA5NnoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTg0MiA0NTRjMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4IDAgMTQwLjMtMTEzLjcgMjU0LTI1NCAyNTRTMjU4IDU5NC4zIDI1OCA0NTRjMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4IDAgMTY4LjcgMTI2LjYgMzA3LjkgMjkwIDMyNy42Vjg4NEgzMjYuN2MtMTMuNyAwLTI0LjcgMTQuMy0yNC43IDMydjM2YzAgNC40IDIuOCA4IDYuMiA4aDQwNy42YzMuNCAwIDYuMi0zLjYgNi4yLTh2LTM2YzAtMTcuNy0xMS0zMi0yNC43LTMySDU0OFY3ODIuMWMxNjUuMy0xOCAyOTQtMTU4IDI5NC0zMjguMXoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiA2MjRjOTMuOSAwIDE3MC03NS4yIDE3MC0xNjhWMjMyYzAtOTIuOC03Ni4xLTE2OC0xNzAtMTY4cy0xNzAgNzUuMi0xNzAgMTY4djIyNGMwIDkyLjggNzYuMSAxNjggMTcwIDE2OHptLTk4LTM5MmMwLTUyLjggNDMuNy05NiA5OC05NnM5OCA0My4yIDk4IDk2djIyNGMwIDUyLjgtNDMuNyA5Ni05OCA5NnMtOTgtNDMuMi05OC05NlYyMzJ6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AudioTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AudioTwoTone';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
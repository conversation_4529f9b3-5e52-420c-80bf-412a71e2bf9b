{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport get from \"./get\";\nfunction internalSet(entity, paths, value, removeIfUndefined) {\n  if (!paths.length) {\n    return value;\n  }\n  var _paths = _toArray(paths),\n    path = _paths[0],\n    restPath = _paths.slice(1);\n  var clone;\n  if (!entity && typeof path === 'number') {\n    clone = [];\n  } else if (Array.isArray(entity)) {\n    clone = _toConsumableArray(entity);\n  } else {\n    clone = _objectSpread({}, entity);\n  }\n\n  // Delete prop if `removeIfUndefined` and value is undefined\n  if (removeIfUndefined && value === undefined && restPath.length === 1) {\n    delete clone[path][restPath[0]];\n  } else {\n    clone[path] = internalSet(clone[path], restPath, value, removeIfUndefined);\n  }\n  return clone;\n}\nexport default function set(entity, paths, value) {\n  var removeIfUndefined = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  // Do nothing if `removeIfUndefined` and parent object not exist\n  if (paths.length && removeIfUndefined && value === undefined && !get(entity, paths.slice(0, -1))) {\n    return entity;\n  }\n  return internalSet(entity, paths, value, removeIfUndefined);\n}\nfunction isObject(obj) {\n  return _typeof(obj) === 'object' && obj !== null && Object.getPrototypeOf(obj) === Object.prototype;\n}\nfunction createEmpty(source) {\n  return Array.isArray(source) ? [] : {};\n}\nvar keys = typeof Reflect === 'undefined' ? Object.keys : Reflect.ownKeys;\n\n/**\n * Merge objects which will create\n */\nexport function merge() {\n  for (var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++) {\n    sources[_key] = arguments[_key];\n  }\n  var clone = createEmpty(sources[0]);\n  sources.forEach(function (src) {\n    function internalMerge(path, parentLoopSet) {\n      var loopSet = new Set(parentLoopSet);\n      var value = get(src, path);\n      var isArr = Array.isArray(value);\n      if (isArr || isObject(value)) {\n        // Only add not loop obj\n        if (!loopSet.has(value)) {\n          loopSet.add(value);\n          var originValue = get(clone, path);\n          if (isArr) {\n            // Array will always be override\n            clone = set(clone, path, []);\n          } else if (!originValue || _typeof(originValue) !== 'object') {\n            // Init container if not exist\n            clone = set(clone, path, createEmpty(value));\n          }\n          keys(value).forEach(function (key) {\n            internalMerge([].concat(_toConsumableArray(path), [key]), loopSet);\n          });\n        }\n      } else {\n        clone = set(clone, path, value);\n      }\n    }\n    internalMerge([]);\n  });\n  return clone;\n}", "map": {"version": 3, "names": ["_typeof", "_objectSpread", "_toConsumableArray", "_toArray", "get", "internalSet", "entity", "paths", "value", "removeIfUndefined", "length", "_paths", "path", "restPath", "slice", "clone", "Array", "isArray", "undefined", "set", "arguments", "isObject", "obj", "Object", "getPrototypeOf", "prototype", "createEmpty", "source", "keys", "Reflect", "ownKeys", "merge", "_len", "sources", "_key", "for<PERSON>ach", "src", "internalMerge", "parentLoopSet", "loopSet", "Set", "isArr", "has", "add", "originValue", "key", "concat"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-util/es/utils/set.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport get from \"./get\";\nfunction internalSet(entity, paths, value, removeIfUndefined) {\n  if (!paths.length) {\n    return value;\n  }\n  var _paths = _toArray(paths),\n    path = _paths[0],\n    restPath = _paths.slice(1);\n  var clone;\n  if (!entity && typeof path === 'number') {\n    clone = [];\n  } else if (Array.isArray(entity)) {\n    clone = _toConsumableArray(entity);\n  } else {\n    clone = _objectSpread({}, entity);\n  }\n\n  // Delete prop if `removeIfUndefined` and value is undefined\n  if (removeIfUndefined && value === undefined && restPath.length === 1) {\n    delete clone[path][restPath[0]];\n  } else {\n    clone[path] = internalSet(clone[path], restPath, value, removeIfUndefined);\n  }\n  return clone;\n}\nexport default function set(entity, paths, value) {\n  var removeIfUndefined = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  // Do nothing if `removeIfUndefined` and parent object not exist\n  if (paths.length && removeIfUndefined && value === undefined && !get(entity, paths.slice(0, -1))) {\n    return entity;\n  }\n  return internalSet(entity, paths, value, removeIfUndefined);\n}\nfunction isObject(obj) {\n  return _typeof(obj) === 'object' && obj !== null && Object.getPrototypeOf(obj) === Object.prototype;\n}\nfunction createEmpty(source) {\n  return Array.isArray(source) ? [] : {};\n}\nvar keys = typeof Reflect === 'undefined' ? Object.keys : Reflect.ownKeys;\n\n/**\n * Merge objects which will create\n */\nexport function merge() {\n  for (var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++) {\n    sources[_key] = arguments[_key];\n  }\n  var clone = createEmpty(sources[0]);\n  sources.forEach(function (src) {\n    function internalMerge(path, parentLoopSet) {\n      var loopSet = new Set(parentLoopSet);\n      var value = get(src, path);\n      var isArr = Array.isArray(value);\n      if (isArr || isObject(value)) {\n        // Only add not loop obj\n        if (!loopSet.has(value)) {\n          loopSet.add(value);\n          var originValue = get(clone, path);\n          if (isArr) {\n            // Array will always be override\n            clone = set(clone, path, []);\n          } else if (!originValue || _typeof(originValue) !== 'object') {\n            // Init container if not exist\n            clone = set(clone, path, createEmpty(value));\n          }\n          keys(value).forEach(function (key) {\n            internalMerge([].concat(_toConsumableArray(path), [key]), loopSet);\n          });\n        }\n      } else {\n        clone = set(clone, path, value);\n      }\n    }\n    internalMerge([]);\n  });\n  return clone;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,iBAAiB,EAAE;EAC5D,IAAI,CAACF,KAAK,CAACG,MAAM,EAAE;IACjB,OAAOF,KAAK;EACd;EACA,IAAIG,MAAM,GAAGR,QAAQ,CAACI,KAAK,CAAC;IAC1BK,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC;IAChBE,QAAQ,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIC,KAAK;EACT,IAAI,CAACT,MAAM,IAAI,OAAOM,IAAI,KAAK,QAAQ,EAAE;IACvCG,KAAK,GAAG,EAAE;EACZ,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACX,MAAM,CAAC,EAAE;IAChCS,KAAK,GAAGb,kBAAkB,CAACI,MAAM,CAAC;EACpC,CAAC,MAAM;IACLS,KAAK,GAAGd,aAAa,CAAC,CAAC,CAAC,EAAEK,MAAM,CAAC;EACnC;;EAEA;EACA,IAAIG,iBAAiB,IAAID,KAAK,KAAKU,SAAS,IAAIL,QAAQ,CAACH,MAAM,KAAK,CAAC,EAAE;IACrE,OAAOK,KAAK,CAACH,IAAI,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,MAAM;IACLE,KAAK,CAACH,IAAI,CAAC,GAAGP,WAAW,CAACU,KAAK,CAACH,IAAI,CAAC,EAAEC,QAAQ,EAAEL,KAAK,EAAEC,iBAAiB,CAAC;EAC5E;EACA,OAAOM,KAAK;AACd;AACA,eAAe,SAASI,GAAGA,CAACb,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAChD,IAAIC,iBAAiB,GAAGW,SAAS,CAACV,MAAM,GAAG,CAAC,IAAIU,SAAS,CAAC,CAAC,CAAC,KAAKF,SAAS,GAAGE,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACjG;EACA,IAAIb,KAAK,CAACG,MAAM,IAAID,iBAAiB,IAAID,KAAK,KAAKU,SAAS,IAAI,CAACd,GAAG,CAACE,MAAM,EAAEC,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IAChG,OAAOR,MAAM;EACf;EACA,OAAOD,WAAW,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,iBAAiB,CAAC;AAC7D;AACA,SAASY,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAOtB,OAAO,CAACsB,GAAG,CAAC,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIC,MAAM,CAACC,cAAc,CAACF,GAAG,CAAC,KAAKC,MAAM,CAACE,SAAS;AACrG;AACA,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC3B,OAAOX,KAAK,CAACC,OAAO,CAACU,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxC;AACA,IAAIC,IAAI,GAAG,OAAOC,OAAO,KAAK,WAAW,GAAGN,MAAM,CAACK,IAAI,GAAGC,OAAO,CAACC,OAAO;;AAEzE;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtB,KAAK,IAAIC,IAAI,GAAGZ,SAAS,CAACV,MAAM,EAAEuB,OAAO,GAAG,IAAIjB,KAAK,CAACgB,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;IAC1FD,OAAO,CAACC,IAAI,CAAC,GAAGd,SAAS,CAACc,IAAI,CAAC;EACjC;EACA,IAAInB,KAAK,GAAGW,WAAW,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC;EACnCA,OAAO,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC7B,SAASC,aAAaA,CAACzB,IAAI,EAAE0B,aAAa,EAAE;MAC1C,IAAIC,OAAO,GAAG,IAAIC,GAAG,CAACF,aAAa,CAAC;MACpC,IAAI9B,KAAK,GAAGJ,GAAG,CAACgC,GAAG,EAAExB,IAAI,CAAC;MAC1B,IAAI6B,KAAK,GAAGzB,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC;MAChC,IAAIiC,KAAK,IAAIpB,QAAQ,CAACb,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAAC+B,OAAO,CAACG,GAAG,CAAClC,KAAK,CAAC,EAAE;UACvB+B,OAAO,CAACI,GAAG,CAACnC,KAAK,CAAC;UAClB,IAAIoC,WAAW,GAAGxC,GAAG,CAACW,KAAK,EAAEH,IAAI,CAAC;UAClC,IAAI6B,KAAK,EAAE;YACT;YACA1B,KAAK,GAAGI,GAAG,CAACJ,KAAK,EAAEH,IAAI,EAAE,EAAE,CAAC;UAC9B,CAAC,MAAM,IAAI,CAACgC,WAAW,IAAI5C,OAAO,CAAC4C,WAAW,CAAC,KAAK,QAAQ,EAAE;YAC5D;YACA7B,KAAK,GAAGI,GAAG,CAACJ,KAAK,EAAEH,IAAI,EAAEc,WAAW,CAAClB,KAAK,CAAC,CAAC;UAC9C;UACAoB,IAAI,CAACpB,KAAK,CAAC,CAAC2B,OAAO,CAAC,UAAUU,GAAG,EAAE;YACjCR,aAAa,CAAC,EAAE,CAACS,MAAM,CAAC5C,kBAAkB,CAACU,IAAI,CAAC,EAAE,CAACiC,GAAG,CAAC,CAAC,EAAEN,OAAO,CAAC;UACpE,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLxB,KAAK,GAAGI,GAAG,CAACJ,KAAK,EAAEH,IAAI,EAAEJ,KAAK,CAAC;MACjC;IACF;IACA6B,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC,CAAC;EACF,OAAOtB,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
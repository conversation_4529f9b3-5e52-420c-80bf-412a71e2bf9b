{"ast": null, "code": "const getOffset = radius => {\n  if (radius === 0) {\n    return 0;\n  }\n  // 如果要考虑通用性，这里应该用三角函数 Math.sin(45)\n  // 但是这个场景比较特殊，始终是等腰直角三角形，所以直接用 Math.sqrt() 开方即可\n  return radius - Math.sqrt(Math.pow(radius, 2) / 2);\n};\nexport default getOffset;", "map": {"version": 3, "names": ["getOffset", "radius", "Math", "sqrt", "pow"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/float-button/util.js"], "sourcesContent": ["const getOffset = radius => {\n  if (radius === 0) {\n    return 0;\n  }\n  // 如果要考虑通用性，这里应该用三角函数 Math.sin(45)\n  // 但是这个场景比较特殊，始终是等腰直角三角形，所以直接用 Math.sqrt() 开方即可\n  return radius - Math.sqrt(Math.pow(radius, 2) / 2);\n};\nexport default getOffset;"], "mappings": "AAAA,MAAMA,SAAS,GAAGC,MAAM,IAAI;EAC1B,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB,OAAO,CAAC;EACV;EACA;EACA;EACA,OAAOA,MAAM,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACH,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACpD,CAAC;AACD,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport React, { forwardRef } from 'react';\nimport classNames from 'classnames';\nconst IconWrapper = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n    className,\n    style,\n    children,\n    prefixCls\n  } = props;\n  const iconWrapperCls = classNames(`${prefixCls}-icon`, className);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    ref: ref,\n    className: iconWrapperCls,\n    style: style\n  }, children);\n});\nexport default IconWrapper;", "map": {"version": 3, "names": ["React", "forwardRef", "classNames", "IconWrapper", "props", "ref", "className", "style", "children", "prefixCls", "iconWrapperCls", "createElement"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/button/IconWrapper.js"], "sourcesContent": ["\"use client\";\n\nimport React, { forwardRef } from 'react';\nimport classNames from 'classnames';\nconst IconWrapper = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n    className,\n    style,\n    children,\n    prefixCls\n  } = props;\n  const iconWrapperCls = classNames(`${prefixCls}-icon`, className);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    ref: ref,\n    className: iconWrapperCls,\n    style: style\n  }, children);\n});\nexport default IconWrapper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,aAAaF,UAAU,CAAC,CAACG,KAAK,EAAEC,GAAG,KAAK;EAC1D,MAAM;IACJC,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,cAAc,GAAGR,UAAU,CAAC,GAAGO,SAAS,OAAO,EAAEH,SAAS,CAAC;EACjE,OAAO,aAAaN,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAC9CN,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAEI,cAAc;IACzBH,KAAK,EAAEA;EACT,CAAC,EAAEC,QAAQ,CAAC;AACd,CAAC,CAAC;AACF,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
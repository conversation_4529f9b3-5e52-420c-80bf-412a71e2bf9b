{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { getSize } from './utils';\nconst Steps = props => {\n  const {\n    size,\n    steps,\n    rounding: customRounding = Math.round,\n    percent = 0,\n    strokeWidth = 8,\n    strokeColor,\n    trailColor = null,\n    prefixCls,\n    children\n  } = props;\n  const current = customRounding(steps * (percent / 100));\n  const stepWidth = size === 'small' ? 2 : 14;\n  const mergedSize = size !== null && size !== void 0 ? size : [stepWidth, strokeWidth];\n  const [width, height] = getSize(mergedSize, 'step', {\n    steps,\n    strokeWidth\n  });\n  const unitWidth = width / steps;\n  const styledSteps = Array.from({\n    length: steps\n  });\n  for (let i = 0; i < steps; i++) {\n    const color = Array.isArray(strokeColor) ? strokeColor[i] : strokeColor;\n    styledSteps[i] = /*#__PURE__*/React.createElement(\"div\", {\n      key: i,\n      className: classNames(`${prefixCls}-steps-item`, {\n        [`${prefixCls}-steps-item-active`]: i <= current - 1\n      }),\n      style: {\n        backgroundColor: i <= current - 1 ? color : trailColor,\n        width: unitWidth,\n        height\n      }\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-steps-outer`\n  }, styledSteps, children);\n};\nexport default Steps;", "map": {"version": 3, "names": ["React", "classNames", "getSize", "Steps", "props", "size", "steps", "rounding", "customRounding", "Math", "round", "percent", "strokeWidth", "strokeColor", "trailColor", "prefixCls", "children", "current", "<PERSON><PERSON><PERSON><PERSON>", "mergedSize", "width", "height", "unitWidth", "styledSteps", "Array", "from", "length", "i", "color", "isArray", "createElement", "key", "className", "style", "backgroundColor"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/progress/Steps.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { getSize } from './utils';\nconst Steps = props => {\n  const {\n    size,\n    steps,\n    rounding: customRounding = Math.round,\n    percent = 0,\n    strokeWidth = 8,\n    strokeColor,\n    trailColor = null,\n    prefixCls,\n    children\n  } = props;\n  const current = customRounding(steps * (percent / 100));\n  const stepWidth = size === 'small' ? 2 : 14;\n  const mergedSize = size !== null && size !== void 0 ? size : [stepWidth, strokeWidth];\n  const [width, height] = getSize(mergedSize, 'step', {\n    steps,\n    strokeWidth\n  });\n  const unitWidth = width / steps;\n  const styledSteps = Array.from({\n    length: steps\n  });\n  for (let i = 0; i < steps; i++) {\n    const color = Array.isArray(strokeColor) ? strokeColor[i] : strokeColor;\n    styledSteps[i] = /*#__PURE__*/React.createElement(\"div\", {\n      key: i,\n      className: classNames(`${prefixCls}-steps-item`, {\n        [`${prefixCls}-steps-item-active`]: i <= current - 1\n      }),\n      style: {\n        backgroundColor: i <= current - 1 ? color : trailColor,\n        width: unitWidth,\n        height\n      }\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-steps-outer`\n  }, styledSteps, children);\n};\nexport default Steps;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,SAAS;AACjC,MAAMC,KAAK,GAAGC,KAAK,IAAI;EACrB,MAAM;IACJC,IAAI;IACJC,KAAK;IACLC,QAAQ,EAAEC,cAAc,GAAGC,IAAI,CAACC,KAAK;IACrCC,OAAO,GAAG,CAAC;IACXC,WAAW,GAAG,CAAC;IACfC,WAAW;IACXC,UAAU,GAAG,IAAI;IACjBC,SAAS;IACTC;EACF,CAAC,GAAGZ,KAAK;EACT,MAAMa,OAAO,GAAGT,cAAc,CAACF,KAAK,IAAIK,OAAO,GAAG,GAAG,CAAC,CAAC;EACvD,MAAMO,SAAS,GAAGb,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE;EAC3C,MAAMc,UAAU,GAAGd,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAACa,SAAS,EAAEN,WAAW,CAAC;EACrF,MAAM,CAACQ,KAAK,EAAEC,MAAM,CAAC,GAAGnB,OAAO,CAACiB,UAAU,EAAE,MAAM,EAAE;IAClDb,KAAK;IACLM;EACF,CAAC,CAAC;EACF,MAAMU,SAAS,GAAGF,KAAK,GAAGd,KAAK;EAC/B,MAAMiB,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;IAC7BC,MAAM,EAAEpB;EACV,CAAC,CAAC;EACF,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,KAAK,EAAEqB,CAAC,EAAE,EAAE;IAC9B,MAAMC,KAAK,GAAGJ,KAAK,CAACK,OAAO,CAAChB,WAAW,CAAC,GAAGA,WAAW,CAACc,CAAC,CAAC,GAAGd,WAAW;IACvEU,WAAW,CAACI,CAAC,CAAC,GAAG,aAAa3B,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;MACvDC,GAAG,EAAEJ,CAAC;MACNK,SAAS,EAAE/B,UAAU,CAAC,GAAGc,SAAS,aAAa,EAAE;QAC/C,CAAC,GAAGA,SAAS,oBAAoB,GAAGY,CAAC,IAAIV,OAAO,GAAG;MACrD,CAAC,CAAC;MACFgB,KAAK,EAAE;QACLC,eAAe,EAAEP,CAAC,IAAIV,OAAO,GAAG,CAAC,GAAGW,KAAK,GAAGd,UAAU;QACtDM,KAAK,EAAEE,SAAS;QAChBD;MACF;IACF,CAAC,CAAC;EACJ;EACA,OAAO,aAAarB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC7CE,SAAS,EAAE,GAAGjB,SAAS;EACzB,CAAC,EAAEQ,WAAW,EAAEP,QAAQ,CAAC;AAC3B,CAAC;AACD,eAAeb,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
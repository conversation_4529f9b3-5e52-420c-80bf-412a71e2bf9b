{"ast": null, "code": "\"use client\";\n\nimport React, { useContext } from 'react';\nimport ActionButton from '../../_util/ActionButton';\nimport { ModalContext } from '../context';\nconst ConfirmOkBtn = () => {\n  const {\n    autoFocusButton,\n    close,\n    isSilent,\n    okButtonProps,\n    rootPrefixCls,\n    okTextLocale,\n    okType,\n    onConfirm,\n    onOk\n  } = useContext(ModalContext);\n  return /*#__PURE__*/React.createElement(ActionButton, {\n    isSilent: isSilent,\n    type: okType || 'primary',\n    actionFn: onOk,\n    close: function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      close === null || close === void 0 ? void 0 : close.apply(void 0, args);\n      onConfirm === null || onConfirm === void 0 ? void 0 : onConfirm(true);\n    },\n    autoFocus: autoFocusButton === 'ok',\n    buttonProps: okButtonProps,\n    prefixCls: \"\".concat(rootPrefixCls, \"-btn\")\n  }, okTextLocale);\n};\nexport default ConfirmOkBtn;", "map": {"version": 3, "names": ["React", "useContext", "ActionButton", "ModalContext", "ConfirmOkBtn", "autoFocusButton", "close", "isSilent", "okButtonProps", "rootPrefixCls", "okTextLocale", "okType", "onConfirm", "onOk", "createElement", "type", "actionFn", "_len", "arguments", "length", "args", "Array", "_key", "apply", "autoFocus", "buttonProps", "prefixCls", "concat"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/modal/components/ConfirmOkBtn.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport ActionButton from '../../_util/ActionButton';\nimport { ModalContext } from '../context';\nconst ConfirmOkBtn = () => {\n  const {\n    autoFocusButton,\n    close,\n    isSilent,\n    okButtonProps,\n    rootPrefixCls,\n    okTextLocale,\n    okType,\n    onConfirm,\n    onOk\n  } = useContext(ModalContext);\n  return /*#__PURE__*/React.createElement(ActionButton, {\n    isSilent: isSilent,\n    type: okType || 'primary',\n    actionFn: onOk,\n    close: (...args) => {\n      close === null || close === void 0 ? void 0 : close.apply(void 0, args);\n      onConfirm === null || onConfirm === void 0 ? void 0 : onConfirm(true);\n    },\n    autoFocus: autoFocusButton === 'ok',\n    buttonProps: okButtonProps,\n    prefixCls: `${rootPrefixCls}-btn`\n  }, okTextLocale);\n};\nexport default ConfirmOkBtn;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,YAAY,MAAM,0BAA0B;AACnD,SAASC,YAAY,QAAQ,YAAY;AACzC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAM;IACJC,eAAe;IACfC,KAAK;IACLC,QAAQ;IACRC,aAAa;IACbC,aAAa;IACbC,YAAY;IACZC,MAAM;IACNC,SAAS;IACTC;EACF,CAAC,GAAGZ,UAAU,CAACE,YAAY,CAAC;EAC5B,OAAO,aAAaH,KAAK,CAACc,aAAa,CAACZ,YAAY,EAAE;IACpDK,QAAQ,EAAEA,QAAQ;IAClBQ,IAAI,EAAEJ,MAAM,IAAI,SAAS;IACzBK,QAAQ,EAAEH,IAAI;IACdP,KAAK,EAAE,SAAAA,CAAA,EAAa;MAAA,SAAAW,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MACbhB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACiB,KAAK,CAAC,KAAK,CAAC,EAAEH,IAAI,CAAC;MACvER,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC,IAAI,CAAC;IACvE,CAAC;IACDY,SAAS,EAAEnB,eAAe,KAAK,IAAI;IACnCoB,WAAW,EAAEjB,aAAa;IAC1BkB,SAAS,KAAAC,MAAA,CAAKlB,aAAa;EAC7B,CAAC,EAAEC,YAAY,CAAC;AAClB,CAAC;AACD,eAAeN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport RcSegmented from 'rc-segmented';\nimport useId from \"rc-util/es/hooks/useId\";\nimport { useComponentConfig } from '../config-provider/context';\nimport useSize from '../config-provider/hooks/useSize';\nimport useStyle from './style';\nfunction isSegmentedLabeledOptionWithIcon(option) {\n  return typeof option === 'object' && !!(option === null || option === void 0 ? void 0 : option.icon);\n}\nconst InternalSegmented = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const defaultName = useId();\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      block,\n      options = [],\n      size: customSize = 'middle',\n      style,\n      vertical,\n      shape = 'default',\n      name = defaultName\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"rootClassName\", \"block\", \"options\", \"size\", \"style\", \"vertical\", \"shape\", \"name\"]);\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('segmented');\n  const prefixCls = getPrefixCls('segmented', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // ===================== Size =====================\n  const mergedSize = useSize(customSize);\n  // syntactic sugar to support `icon` for Segmented Item\n  const extendedOptions = React.useMemo(() => options.map(option => {\n    if (isSegmentedLabeledOptionWithIcon(option)) {\n      const {\n          icon,\n          label\n        } = option,\n        restOption = __rest(option, [\"icon\", \"label\"]);\n      return Object.assign(Object.assign({}, restOption), {\n        label: (/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n          className: \"\".concat(prefixCls, \"-item-icon\")\n        }, icon), label && /*#__PURE__*/React.createElement(\"span\", null, label)))\n      });\n    }\n    return option;\n  }), [options, prefixCls]);\n  const cls = classNames(className, rootClassName, contextClassName, {\n    [\"\".concat(prefixCls, \"-block\")]: block,\n    [\"\".concat(prefixCls, \"-sm\")]: mergedSize === 'small',\n    [\"\".concat(prefixCls, \"-lg\")]: mergedSize === 'large',\n    [\"\".concat(prefixCls, \"-vertical\")]: vertical,\n    [\"\".concat(prefixCls, \"-shape-\").concat(shape)]: shape === 'round'\n  }, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(RcSegmented, Object.assign({}, restProps, {\n    name: name,\n    className: cls,\n    style: mergedStyle,\n    options: extendedOptions,\n    ref: ref,\n    prefixCls: prefixCls,\n    direction: direction,\n    vertical: vertical\n  })));\n});\nconst Segmented = InternalSegmented;\nif (process.env.NODE_ENV !== 'production') {\n  Segmented.displayName = 'Segmented';\n}\nexport default Segmented;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
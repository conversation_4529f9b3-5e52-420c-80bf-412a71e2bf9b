{"ast": null, "code": "\"use client\";\n\nimport dayjsGenerateConfig from \"rc-picker/es/generate/dayjs\";\nimport genPurePanel from '../_util/PurePanel';\nimport generatePicker from './generatePicker';\nconst DatePicker = generatePicker(dayjsGenerateConfig);\n// We don't care debug panel\n/* istanbul ignore next */\nconst PurePanel = genPurePanel(DatePicker, 'popupAlign', undefined, 'picker');\nDatePicker._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nconst PureRangePanel = genPurePanel(DatePicker.RangePicker, 'popupAlign', undefined, 'picker');\nDatePicker._InternalRangePanelDoNotUseOrYouWillBeFired = PureRangePanel;\nDatePicker.generatePicker = generatePicker;\nexport default DatePicker;", "map": {"version": 3, "names": ["dayjsGenerateConfig", "genPurePanel", "generatePicker", "DatePicker", "PurePanel", "undefined", "_InternalPanelDoNotUseOrYouWillBeFired", "PureRangePanel", "RangePicker", "_InternalRangePanelDoNotUseOrYouWillBeFired"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/date-picker/index.js"], "sourcesContent": ["\"use client\";\n\nimport dayjsGenerateConfig from \"rc-picker/es/generate/dayjs\";\nimport genPurePanel from '../_util/PurePanel';\nimport generatePicker from './generatePicker';\nconst DatePicker = generatePicker(dayjsGenerateConfig);\n// We don't care debug panel\n/* istanbul ignore next */\nconst PurePanel = genPurePanel(DatePicker, 'popupAlign', undefined, 'picker');\nDatePicker._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nconst PureRangePanel = genPurePanel(DatePicker.RangePicker, 'popupAlign', undefined, 'picker');\nDatePicker._InternalRangePanelDoNotUseOrYouWillBeFired = PureRangePanel;\nDatePicker.generatePicker = generatePicker;\nexport default DatePicker;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,MAAMC,UAAU,GAAGD,cAAc,CAACF,mBAAmB,CAAC;AACtD;AACA;AACA,MAAMI,SAAS,GAAGH,YAAY,CAACE,UAAU,EAAE,YAAY,EAAEE,SAAS,EAAE,QAAQ,CAAC;AAC7EF,UAAU,CAACG,sCAAsC,GAAGF,SAAS;AAC7D,MAAMG,cAAc,GAAGN,YAAY,CAACE,UAAU,CAACK,WAAW,EAAE,YAAY,EAAEH,SAAS,EAAE,QAAQ,CAAC;AAC9FF,UAAU,CAACM,2CAA2C,GAAGF,cAAc;AACvEJ,UAAU,CAACD,cAAc,GAAGA,cAAc;AAC1C,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
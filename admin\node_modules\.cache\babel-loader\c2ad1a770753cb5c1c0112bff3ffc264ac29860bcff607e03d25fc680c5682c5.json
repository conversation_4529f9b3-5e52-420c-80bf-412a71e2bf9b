{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genRoundedArrow } from './roundedArrow';\nexport const MAX_VERTICAL_CONTENT_RADIUS = 8;\nexport function getArrowOffsetToken(options) {\n  const {\n    contentRadius,\n    limitVerticalRadius\n  } = options;\n  const arrowOffset = contentRadius > 12 ? contentRadius + 2 : 12;\n  const arrowOffsetVertical = limitVerticalRadius ? MAX_VERTICAL_CONTENT_RADIUS : arrowOffset;\n  return {\n    arrowOffsetHorizontal: arrowOffset,\n    arrowOffsetVertical\n  };\n}\nfunction isInject(valid, code) {\n  if (!valid) {\n    return {};\n  }\n  return code;\n}\nexport default function getArrowStyle(token, colorBg, options) {\n  const {\n    componentCls,\n    boxShadowPopoverArrow,\n    arrowOffsetVertical,\n    arrowOffsetHorizontal\n  } = token;\n  const {\n    arrowDistance = 0,\n    arrowPlacement = {\n      left: true,\n      right: true,\n      top: true,\n      bottom: true\n    }\n  } = options || {};\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({\n      // ============================ Basic ============================\n      [`${componentCls}-arrow`]: [Object.assign(Object.assign({\n        position: 'absolute',\n        zIndex: 1,\n        display: 'block'\n      }, genRoundedArrow(token, colorBg, boxShadowPopoverArrow)), {\n        '&:before': {\n          background: colorBg\n        }\n      })]\n    }, isInject(!!arrowPlacement.top, {\n      [[`&-placement-top > ${componentCls}-arrow`, `&-placement-topLeft > ${componentCls}-arrow`, `&-placement-topRight > ${componentCls}-arrow`].join(',')]: {\n        bottom: arrowDistance,\n        transform: 'translateY(100%) rotate(180deg)'\n      },\n      [`&-placement-top > ${componentCls}-arrow`]: {\n        left: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateX(-50%) translateY(100%) rotate(180deg)'\n      },\n      '&-placement-topLeft': {\n        '--arrow-offset-horizontal': arrowOffsetHorizontal,\n        [`> ${componentCls}-arrow`]: {\n          left: {\n            _skip_check_: true,\n            value: arrowOffsetHorizontal\n          }\n        }\n      },\n      '&-placement-topRight': {\n        '--arrow-offset-horizontal': `calc(100% - ${unit(arrowOffsetHorizontal)})`,\n        [`> ${componentCls}-arrow`]: {\n          right: {\n            _skip_check_: true,\n            value: arrowOffsetHorizontal\n          }\n        }\n      }\n    })), isInject(!!arrowPlacement.bottom, {\n      [[`&-placement-bottom > ${componentCls}-arrow`, `&-placement-bottomLeft > ${componentCls}-arrow`, `&-placement-bottomRight > ${componentCls}-arrow`].join(',')]: {\n        top: arrowDistance,\n        transform: `translateY(-100%)`\n      },\n      [`&-placement-bottom > ${componentCls}-arrow`]: {\n        left: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: `translateX(-50%) translateY(-100%)`\n      },\n      '&-placement-bottomLeft': {\n        '--arrow-offset-horizontal': arrowOffsetHorizontal,\n        [`> ${componentCls}-arrow`]: {\n          left: {\n            _skip_check_: true,\n            value: arrowOffsetHorizontal\n          }\n        }\n      },\n      '&-placement-bottomRight': {\n        '--arrow-offset-horizontal': `calc(100% - ${unit(arrowOffsetHorizontal)})`,\n        [`> ${componentCls}-arrow`]: {\n          right: {\n            _skip_check_: true,\n            value: arrowOffsetHorizontal\n          }\n        }\n      }\n    })), isInject(!!arrowPlacement.left, {\n      [[`&-placement-left > ${componentCls}-arrow`, `&-placement-leftTop > ${componentCls}-arrow`, `&-placement-leftBottom > ${componentCls}-arrow`].join(',')]: {\n        right: {\n          _skip_check_: true,\n          value: arrowDistance\n        },\n        transform: 'translateX(100%) rotate(90deg)'\n      },\n      [`&-placement-left > ${componentCls}-arrow`]: {\n        top: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateY(-50%) translateX(100%) rotate(90deg)'\n      },\n      [`&-placement-leftTop > ${componentCls}-arrow`]: {\n        top: arrowOffsetVertical\n      },\n      [`&-placement-leftBottom > ${componentCls}-arrow`]: {\n        bottom: arrowOffsetVertical\n      }\n    })), isInject(!!arrowPlacement.right, {\n      [[`&-placement-right > ${componentCls}-arrow`, `&-placement-rightTop > ${componentCls}-arrow`, `&-placement-rightBottom > ${componentCls}-arrow`].join(',')]: {\n        left: {\n          _skip_check_: true,\n          value: arrowDistance\n        },\n        transform: 'translateX(-100%) rotate(-90deg)'\n      },\n      [`&-placement-right > ${componentCls}-arrow`]: {\n        top: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateY(-50%) translateX(-100%) rotate(-90deg)'\n      },\n      [`&-placement-rightTop > ${componentCls}-arrow`]: {\n        top: arrowOffsetVertical\n      },\n      [`&-placement-rightBottom > ${componentCls}-arrow`]: {\n        bottom: arrowOffsetVertical\n      }\n    }))\n  };\n}", "map": {"version": 3, "names": ["unit", "genRoundedArrow", "MAX_VERTICAL_CONTENT_RADIUS", "getArrowOffsetToken", "options", "contentRadius", "limitVerticalRadius", "arrowOffset", "arrowOffsetVertical", "arrowOffsetHorizontal", "isInject", "valid", "code", "getArrowStyle", "token", "colorBg", "componentCls", "boxShadowPopoverArrow", "arrowDistance", "arrowPlacement", "left", "right", "top", "bottom", "Object", "assign", "position", "zIndex", "display", "background", "join", "transform", "_skip_check_", "value"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/style/placementArrow.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { genRoundedArrow } from './roundedArrow';\nexport const MAX_VERTICAL_CONTENT_RADIUS = 8;\nexport function getArrowOffsetToken(options) {\n  const {\n    contentRadius,\n    limitVerticalRadius\n  } = options;\n  const arrowOffset = contentRadius > 12 ? contentRadius + 2 : 12;\n  const arrowOffsetVertical = limitVerticalRadius ? MAX_VERTICAL_CONTENT_RADIUS : arrowOffset;\n  return {\n    arrowOffsetHorizontal: arrowOffset,\n    arrowOffsetVertical\n  };\n}\nfunction isInject(valid, code) {\n  if (!valid) {\n    return {};\n  }\n  return code;\n}\nexport default function getArrowStyle(token, colorBg, options) {\n  const {\n    componentCls,\n    boxShadowPopoverArrow,\n    arrowOffsetVertical,\n    arrowOffsetHorizontal\n  } = token;\n  const {\n    arrowDistance = 0,\n    arrowPlacement = {\n      left: true,\n      right: true,\n      top: true,\n      bottom: true\n    }\n  } = options || {};\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({\n      // ============================ Basic ============================\n      [`${componentCls}-arrow`]: [Object.assign(Object.assign({\n        position: 'absolute',\n        zIndex: 1,\n        display: 'block'\n      }, genRoundedArrow(token, colorBg, boxShadowPopoverArrow)), {\n        '&:before': {\n          background: colorBg\n        }\n      })]\n    }, isInject(!!arrowPlacement.top, {\n      [[`&-placement-top > ${componentCls}-arrow`, `&-placement-topLeft > ${componentCls}-arrow`, `&-placement-topRight > ${componentCls}-arrow`].join(',')]: {\n        bottom: arrowDistance,\n        transform: 'translateY(100%) rotate(180deg)'\n      },\n      [`&-placement-top > ${componentCls}-arrow`]: {\n        left: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateX(-50%) translateY(100%) rotate(180deg)'\n      },\n      '&-placement-topLeft': {\n        '--arrow-offset-horizontal': arrowOffsetHorizontal,\n        [`> ${componentCls}-arrow`]: {\n          left: {\n            _skip_check_: true,\n            value: arrowOffsetHorizontal\n          }\n        }\n      },\n      '&-placement-topRight': {\n        '--arrow-offset-horizontal': `calc(100% - ${unit(arrowOffsetHorizontal)})`,\n        [`> ${componentCls}-arrow`]: {\n          right: {\n            _skip_check_: true,\n            value: arrowOffsetHorizontal\n          }\n        }\n      }\n    })), isInject(!!arrowPlacement.bottom, {\n      [[`&-placement-bottom > ${componentCls}-arrow`, `&-placement-bottomLeft > ${componentCls}-arrow`, `&-placement-bottomRight > ${componentCls}-arrow`].join(',')]: {\n        top: arrowDistance,\n        transform: `translateY(-100%)`\n      },\n      [`&-placement-bottom > ${componentCls}-arrow`]: {\n        left: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: `translateX(-50%) translateY(-100%)`\n      },\n      '&-placement-bottomLeft': {\n        '--arrow-offset-horizontal': arrowOffsetHorizontal,\n        [`> ${componentCls}-arrow`]: {\n          left: {\n            _skip_check_: true,\n            value: arrowOffsetHorizontal\n          }\n        }\n      },\n      '&-placement-bottomRight': {\n        '--arrow-offset-horizontal': `calc(100% - ${unit(arrowOffsetHorizontal)})`,\n        [`> ${componentCls}-arrow`]: {\n          right: {\n            _skip_check_: true,\n            value: arrowOffsetHorizontal\n          }\n        }\n      }\n    })), isInject(!!arrowPlacement.left, {\n      [[`&-placement-left > ${componentCls}-arrow`, `&-placement-leftTop > ${componentCls}-arrow`, `&-placement-leftBottom > ${componentCls}-arrow`].join(',')]: {\n        right: {\n          _skip_check_: true,\n          value: arrowDistance\n        },\n        transform: 'translateX(100%) rotate(90deg)'\n      },\n      [`&-placement-left > ${componentCls}-arrow`]: {\n        top: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateY(-50%) translateX(100%) rotate(90deg)'\n      },\n      [`&-placement-leftTop > ${componentCls}-arrow`]: {\n        top: arrowOffsetVertical\n      },\n      [`&-placement-leftBottom > ${componentCls}-arrow`]: {\n        bottom: arrowOffsetVertical\n      }\n    })), isInject(!!arrowPlacement.right, {\n      [[`&-placement-right > ${componentCls}-arrow`, `&-placement-rightTop > ${componentCls}-arrow`, `&-placement-rightBottom > ${componentCls}-arrow`].join(',')]: {\n        left: {\n          _skip_check_: true,\n          value: arrowDistance\n        },\n        transform: 'translateX(-100%) rotate(-90deg)'\n      },\n      [`&-placement-right > ${componentCls}-arrow`]: {\n        top: {\n          _skip_check_: true,\n          value: '50%'\n        },\n        transform: 'translateY(-50%) translateX(-100%) rotate(-90deg)'\n      },\n      [`&-placement-rightTop > ${componentCls}-arrow`]: {\n        top: arrowOffsetVertical\n      },\n      [`&-placement-rightBottom > ${componentCls}-arrow`]: {\n        bottom: arrowOffsetVertical\n      }\n    }))\n  };\n}"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAO,MAAMC,2BAA2B,GAAG,CAAC;AAC5C,OAAO,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAC3C,MAAM;IACJC,aAAa;IACbC;EACF,CAAC,GAAGF,OAAO;EACX,MAAMG,WAAW,GAAGF,aAAa,GAAG,EAAE,GAAGA,aAAa,GAAG,CAAC,GAAG,EAAE;EAC/D,MAAMG,mBAAmB,GAAGF,mBAAmB,GAAGJ,2BAA2B,GAAGK,WAAW;EAC3F,OAAO;IACLE,qBAAqB,EAAEF,WAAW;IAClCC;EACF,CAAC;AACH;AACA,SAASE,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC7B,IAAI,CAACD,KAAK,EAAE;IACV,OAAO,CAAC,CAAC;EACX;EACA,OAAOC,IAAI;AACb;AACA,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAEX,OAAO,EAAE;EAC7D,MAAM;IACJY,YAAY;IACZC,qBAAqB;IACrBT,mBAAmB;IACnBC;EACF,CAAC,GAAGK,KAAK;EACT,MAAM;IACJI,aAAa,GAAG,CAAC;IACjBC,cAAc,GAAG;MACfC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE;IACV;EACF,CAAC,GAAGnB,OAAO,IAAI,CAAC,CAAC;EACjB,OAAO;IACL,CAACY,YAAY,GAAGQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MACtE;MACA,CAAC,GAAGT,YAAY,QAAQ,GAAG,CAACQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QACtDC,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE;MACX,CAAC,EAAE3B,eAAe,CAACa,KAAK,EAAEC,OAAO,EAAEE,qBAAqB,CAAC,CAAC,EAAE;QAC1D,UAAU,EAAE;UACVY,UAAU,EAAEd;QACd;MACF,CAAC,CAAC;IACJ,CAAC,EAAEL,QAAQ,CAAC,CAAC,CAACS,cAAc,CAACG,GAAG,EAAE;MAChC,CAAC,CAAC,qBAAqBN,YAAY,QAAQ,EAAE,yBAAyBA,YAAY,QAAQ,EAAE,0BAA0BA,YAAY,QAAQ,CAAC,CAACc,IAAI,CAAC,GAAG,CAAC,GAAG;QACtJP,MAAM,EAAEL,aAAa;QACrBa,SAAS,EAAE;MACb,CAAC;MACD,CAAC,qBAAqBf,YAAY,QAAQ,GAAG;QAC3CI,IAAI,EAAE;UACJY,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDF,SAAS,EAAE;MACb,CAAC;MACD,qBAAqB,EAAE;QACrB,2BAA2B,EAAEtB,qBAAqB;QAClD,CAAC,KAAKO,YAAY,QAAQ,GAAG;UAC3BI,IAAI,EAAE;YACJY,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAExB;UACT;QACF;MACF,CAAC;MACD,sBAAsB,EAAE;QACtB,2BAA2B,EAAE,eAAeT,IAAI,CAACS,qBAAqB,CAAC,GAAG;QAC1E,CAAC,KAAKO,YAAY,QAAQ,GAAG;UAC3BK,KAAK,EAAE;YACLW,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAExB;UACT;QACF;MACF;IACF,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACS,cAAc,CAACI,MAAM,EAAE;MACrC,CAAC,CAAC,wBAAwBP,YAAY,QAAQ,EAAE,4BAA4BA,YAAY,QAAQ,EAAE,6BAA6BA,YAAY,QAAQ,CAAC,CAACc,IAAI,CAAC,GAAG,CAAC,GAAG;QAC/JR,GAAG,EAAEJ,aAAa;QAClBa,SAAS,EAAE;MACb,CAAC;MACD,CAAC,wBAAwBf,YAAY,QAAQ,GAAG;QAC9CI,IAAI,EAAE;UACJY,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDF,SAAS,EAAE;MACb,CAAC;MACD,wBAAwB,EAAE;QACxB,2BAA2B,EAAEtB,qBAAqB;QAClD,CAAC,KAAKO,YAAY,QAAQ,GAAG;UAC3BI,IAAI,EAAE;YACJY,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAExB;UACT;QACF;MACF,CAAC;MACD,yBAAyB,EAAE;QACzB,2BAA2B,EAAE,eAAeT,IAAI,CAACS,qBAAqB,CAAC,GAAG;QAC1E,CAAC,KAAKO,YAAY,QAAQ,GAAG;UAC3BK,KAAK,EAAE;YACLW,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAExB;UACT;QACF;MACF;IACF,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACS,cAAc,CAACC,IAAI,EAAE;MACnC,CAAC,CAAC,sBAAsBJ,YAAY,QAAQ,EAAE,yBAAyBA,YAAY,QAAQ,EAAE,4BAA4BA,YAAY,QAAQ,CAAC,CAACc,IAAI,CAAC,GAAG,CAAC,GAAG;QACzJT,KAAK,EAAE;UACLW,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEf;QACT,CAAC;QACDa,SAAS,EAAE;MACb,CAAC;MACD,CAAC,sBAAsBf,YAAY,QAAQ,GAAG;QAC5CM,GAAG,EAAE;UACHU,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDF,SAAS,EAAE;MACb,CAAC;MACD,CAAC,yBAAyBf,YAAY,QAAQ,GAAG;QAC/CM,GAAG,EAAEd;MACP,CAAC;MACD,CAAC,4BAA4BQ,YAAY,QAAQ,GAAG;QAClDO,MAAM,EAAEf;MACV;IACF,CAAC,CAAC,CAAC,EAAEE,QAAQ,CAAC,CAAC,CAACS,cAAc,CAACE,KAAK,EAAE;MACpC,CAAC,CAAC,uBAAuBL,YAAY,QAAQ,EAAE,0BAA0BA,YAAY,QAAQ,EAAE,6BAA6BA,YAAY,QAAQ,CAAC,CAACc,IAAI,CAAC,GAAG,CAAC,GAAG;QAC5JV,IAAI,EAAE;UACJY,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEf;QACT,CAAC;QACDa,SAAS,EAAE;MACb,CAAC;MACD,CAAC,uBAAuBf,YAAY,QAAQ,GAAG;QAC7CM,GAAG,EAAE;UACHU,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDF,SAAS,EAAE;MACb,CAAC;MACD,CAAC,0BAA0Bf,YAAY,QAAQ,GAAG;QAChDM,GAAG,EAAEd;MACP,CAAC;MACD,CAAC,6BAA6BQ,YAAY,QAAQ,GAAG;QACnDO,MAAM,EAAEf;MACV;IACF,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
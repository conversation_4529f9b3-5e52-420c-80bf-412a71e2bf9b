{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderPending.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, message, Modal, Form, Select, Input, Row, Col, Statistic } from 'antd';\nimport { CheckOutlined, CloseOutlined, EyeOutlined, ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\n// 模拟待处理订单数据\nconst mockPendingOrders = [{\n  id: 1,\n  orderNo: 'ORD202401150003',\n  customerName: '王五',\n  customerPhone: '13800138003',\n  customerIdCard: '110101199001011234',\n  productName: '中国联通青春套餐',\n  productDescription: '5G网络，月租59元，包含20GB通用流量+30GB定向流量，300分钟通话',\n  monthlyFee: 59,\n  generalTraffic: 20,\n  operator: '中国联通',\n  priority: 'normal',\n  deliveryAddress: '北京市朝阳区建国门外大街1号国贸大厦A座1001室',\n  createdAt: '2024-01-15 12:00:00',\n  waitingTime: 2\n}, {\n  id: 2,\n  orderNo: 'ORD202401150005',\n  customerName: '钱七',\n  customerPhone: '13800138005',\n  customerIdCard: '310101198505155678',\n  productName: '中国移动5G尊享套餐',\n  productDescription: '5G网络，月租128元，包含50GB通用流量+100GB定向流量，1000分钟通话',\n  monthlyFee: 128,\n  generalTraffic: 50,\n  operator: '中国移动',\n  priority: 'high',\n  deliveryAddress: '上海市浦东新区陆家嘴环路1000号恒生银行大厦50楼',\n  createdAt: '2024-01-15 14:30:00',\n  waitingTime: 0.5\n}, {\n  id: 3,\n  orderNo: 'ORD202401150006',\n  customerName: '孙八',\n  customerPhone: '13800138006',\n  customerIdCard: '******************',\n  productName: '中国电信天翼套餐',\n  productDescription: '5G网络，月租89元，包含30GB通用流量+50GB定向流量，500分钟通话',\n  monthlyFee: 89,\n  generalTraffic: 30,\n  operator: '中国电信',\n  priority: 'urgent',\n  deliveryAddress: '广州市天河区珠江新城花城大道85号高德置地广场B2栋2001',\n  createdAt: '2024-01-15 15:00:00',\n  waitingTime: 0.2\n}, {\n  id: 4,\n  orderNo: 'ORD202401150007',\n  customerName: '李九',\n  customerPhone: '13800138007',\n  customerIdCard: '******************',\n  productName: '中国广电智慧套餐',\n  productDescription: '5G网络，月租79元，包含25GB通用流量+40GB定向流量，400分钟通话',\n  monthlyFee: 79,\n  generalTraffic: 25,\n  operator: '中国广电',\n  priority: 'normal',\n  deliveryAddress: '深圳市南山区深南大道9988号华润置地大厦A座3501室',\n  createdAt: '2024-01-15 16:00:00',\n  waitingTime: 1.5\n}];\nconst OrderPending = () => {\n  _s();\n  const [orders, setOrders] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [processModalVisible, setProcessModalVisible] = useState(false);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [form] = Form.useForm();\n\n  // 页面加载时获取数据\n  useEffect(() => {\n    handleRefresh();\n  }, []);\n\n  // 转换API数据格式为前端格式\n  const transformApiData = apiData => {\n    return {\n      id: apiData.id,\n      orderNo: apiData.order_no,\n      customerName: apiData.customer_name,\n      customerPhone: apiData.customer_phone,\n      customerIdCard: apiData.customer_id_card,\n      productName: apiData.product_name,\n      productDescription: `月租费用：¥${apiData.monthly_fee}，通用流量：${apiData.general_traffic}GB，定向流量：${apiData.directional_traffic}GB，免费通话：${apiData.free_minutes}分钟，套餐时长：${apiData.package_duration}`,\n      monthlyFee: parseFloat(apiData.monthly_fee),\n      generalTraffic: parseFloat(apiData.general_traffic),\n      operator: apiData.operator,\n      priority: apiData.priority || 'normal',\n      deliveryAddress: apiData.delivery_address,\n      createdAt: apiData.created_at ? new Date(apiData.created_at).toLocaleString('zh-CN') : '',\n      waitingTime: apiData.created_at ? Math.max(0.1, (new Date().getTime() - new Date(apiData.created_at).getTime()) / (1000 * 60 * 60)) : 0.1 // 计算等待时间（小时）\n    };\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = priority => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red'\n    };\n    return colors[priority] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = priority => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急'\n    };\n    return texts[priority] || priority;\n  };\n\n  // 统计数据\n  const stats = {\n    total: orders.length,\n    urgent: orders.filter(order => order.priority === 'urgent').length,\n    high: orders.filter(order => order.priority === 'high').length,\n    processing: orders.filter(order => order.waitingTime > 1).length // 等待超过1小时的订单\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '优先级',\n    dataIndex: 'priority',\n    key: 'priority',\n    width: 80,\n    render: priority => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getPriorityColor(priority),\n      children: getPriorityText(priority)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => {\n      const priorityOrder = {\n        urgent: 4,\n        high: 3,\n        normal: 2,\n        low: 1\n      };\n      return priorityOrder[a.priority] - priorityOrder[b.priority];\n    }\n  }, {\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '收货地址',\n    dataIndex: 'deliveryAddress',\n    key: 'deliveryAddress',\n    width: 280,\n    render: address => {\n      // 智能分割地址为两行\n      const splitAddress = addr => {\n        if (addr.length <= 25) {\n          return [addr, ''];\n        }\n\n        // 寻找合适的分割点（优先在区、县、市、路、街、号等后面分割）\n        const splitPoints = ['区', '县', '市', '路', '街', '号', '栋', '座', '楼'];\n        let bestSplit = Math.floor(addr.length / 2);\n        for (let i = 15; i < Math.min(30, addr.length - 5); i++) {\n          if (splitPoints.some(point => addr.charAt(i) === point)) {\n            bestSplit = i + 1;\n            break;\n          }\n        }\n        return [addr.substring(0, bestSplit), addr.substring(bestSplit)];\n      };\n      const [line1, line2] = splitAddress(address);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxWidth: 260,\n          lineHeight: '1.3'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#333',\n            marginBottom: line2 ? '2px' : '0'\n          },\n          children: line1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), line2 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: line2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '等待时间',\n    dataIndex: 'waitingTime',\n    key: 'waitingTime',\n    width: 100,\n    render: time => /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        color: time > 1 ? '#f5222d' : '#52c41a'\n      },\n      children: time < 1 ? `${Math.round(time * 60)}分钟` : `${time.toFixed(1)}小时`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.waitingTime - b.waitingTime\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(CheckOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleProcessOrder(record),\n        children: \"\\u5904\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        danger: true,\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleCancelOrder(record.id),\n        children: \"\\u53D6\\u6D88\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleProcessOrder = order => {\n    setSelectedOrder(order);\n    setProcessModalVisible(true);\n  };\n  const handleViewOrder = order => {\n    setSelectedOrder(order);\n    setViewModalVisible(true);\n  };\n  const handleCancelOrder = id => {\n    Modal.confirm({\n      title: '确认取消订单',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 13\n      }, this),\n      content: '确定要取消这个订单吗？此操作不可恢复。',\n      onOk: () => {\n        setOrders(orders.filter(order => order.id !== id));\n        message.success('订单已取消');\n      }\n    });\n  };\n  const handleBatchProcess = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要处理的订单');\n      return;\n    }\n    Modal.confirm({\n      title: '批量处理订单',\n      content: `确定要批量处理选中的 ${selectedRowKeys.length} 个订单吗？`,\n      onOk: () => {\n        setOrders(orders.filter(order => !selectedRowKeys.includes(order.id)));\n        setSelectedRowKeys([]);\n        message.success('批量处理成功');\n      }\n    });\n  };\n  const handleRefresh = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/orders/pending', {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json'\n        }\n      });\n      const result = await response.json();\n      if (result.code === 200) {\n        setOrders(result.data.list);\n        message.success('数据已刷新');\n      } else {\n        message.error(result.message || '刷新失败');\n      }\n    } catch (error) {\n      console.error('刷新失败:', error);\n      message.error('网络错误，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleProcessSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      if (!selectedOrder) {\n        message.error('未选择订单');\n        return;\n      }\n\n      // 调用API更新订单状态\n      const response = await fetch(`http://localhost:8000/api/v1/orders/${selectedOrder.id}/status`, {\n        method: 'PATCH',\n        headers: {\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          status: values.action,\n          process_notes: values.remark,\n          processed_by: 'admin'\n        })\n      });\n      const result = await response.json();\n      if (result.code === 200) {\n        let successMessage = '';\n\n        // 根据处理动作确定成功消息\n        if (values.action === 'processing') {\n          successMessage = '订单已转入开卡中，请在【审核中订单】中查看';\n        } else if (values.action === 'failed') {\n          successMessage = '订单已标记为失败，请在【失败订单】中查看';\n        } else if (values.action === 'pending_upload') {\n          successMessage = '订单已转入待上传三证状态，app用户端将收到上传链接';\n        }\n\n        // 从待处理订单列表中移除\n        setOrders(orders.filter(order => order.id !== selectedOrder.id));\n        setProcessModalVisible(false);\n        setSelectedOrder(null);\n        form.resetFields();\n        message.success(successMessage);\n      } else {\n        message.error(result.message || '处理失败');\n      }\n    } catch (error) {\n      console.error('处理失败:', error);\n      message.error('网络错误，请稍后重试');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#faad14'\n        },\n        children: \"\\u5F85\\u5904\\u7406\\u8BA2\\u5355\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u5904\\u7406\\u9700\\u8981\\u4EBA\\u5DE5\\u5BA1\\u6838\\u7684\\u8BA2\\u5355\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\\u603B\\u6570\",\n            value: stats.total,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7D27\\u6025\\u8BA2\\u5355\",\n            value: stats.urgent,\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9AD8\\u4F18\\u5148\\u7EA7\",\n            value: stats.high,\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8D85\\u65F6\\u8BA2\\u5355\",\n            value: stats.processing,\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", orders.length, \" \\u4E2A\\u5F85\\u5904\\u7406\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this), selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              marginLeft: 16,\n              color: '#1890ff'\n            },\n            children: [\"\\u5DF2\\u9009\\u62E9 \", selectedRowKeys.length, \" \\u4E2A\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleBatchProcess,\n            children: \"\\u6279\\u91CF\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: orders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1480\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 519,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5904\\u7406\\u8BA2\\u5355\",\n      open: processModalVisible,\n      onOk: handleProcessSubmit,\n      onCancel: () => {\n        setProcessModalVisible(false);\n        form.resetFields();\n      },\n      okText: \"\\u786E\\u8BA4\\u5904\\u7406\",\n      cancelText: \"\\u53D6\\u6D88\",\n      width: 600,\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 24,\n            padding: 16,\n            background: '#f8f9fa',\n            borderRadius: 8,\n            border: '1px solid #e9ecef'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 12\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                fontSize: '14px'\n              },\n              children: \"\\u8BA2\\u5355\\u53F7\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              code: true,\n              style: {\n                fontSize: '14px'\n              },\n              children: selectedOrder.orderNo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 12\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                fontSize: '14px'\n              },\n              children: \"\\u4EA7\\u54C1\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '14px'\n              },\n              children: selectedOrder.productName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 12\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                fontSize: '14px'\n              },\n              children: \"\\u8FD0\\u8425\\u5546\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '14px'\n              },\n              children: selectedOrder.operator\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 12\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                fontSize: '14px'\n              },\n              children: \"\\u59D3\\u540D\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '14px'\n              },\n              children: selectedOrder.customerName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 12\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                fontSize: '14px'\n              },\n              children: \"\\u624B\\u673A\\u53F7\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '14px'\n              },\n              children: selectedOrder.customerPhone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 12\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                fontSize: '14px'\n              },\n              children: \"\\u6536\\u8D27\\u5730\\u5740\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                marginTop: 4,\n                lineHeight: '1.5'\n              },\n              children: selectedOrder.deliveryAddress\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                fontSize: '14px'\n              },\n              children: \"\\u8EAB\\u4EFD\\u8BC1\\u53F7\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '14px'\n              },\n              children: selectedOrder.customerIdCard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            style: {\n              fontSize: '16px',\n              marginBottom: '12px',\n              display: 'block'\n            },\n            children: \"\\u5904\\u7406\\u52A8\\u4F5C\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"action\",\n              rules: [{\n                required: true,\n                message: '请选择处理动作'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5904\\u7406\\u52A8\\u4F5C\",\n                style: {\n                  width: '100%'\n                },\n                size: \"large\",\n                children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"processing\",\n                  children: \"\\u5F00\\u5361\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"failed\",\n                  children: \"\\u5F00\\u5361\\u5931\\u8D25\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"pending_upload\",\n                  children: \"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"remark\",\n              label: /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                style: {\n                  fontSize: '14px'\n                },\n                children: \"\\u5907\\u6CE8\\u8BF4\\u660E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 26\n              }, this),\n              rules: [{\n                required: true,\n                message: '请填写备注说明'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                rows: 4,\n                placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u8BF4\\u660E\\u5904\\u7406\\u60C5\\u51B5\\u3001\\u6CE8\\u610F\\u4E8B\\u9879\\u6216\\u5931\\u8D25\\u539F\\u56E0...\",\n                maxLength: 500,\n                showCount: true,\n                style: {\n                  fontSize: '14px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BA2\\u5355\\u8BE6\\u60C5\",\n      open: viewModalVisible,\n      onCancel: () => setViewModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setViewModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5BA2\\u6237\\u4FE1\\u606F\",\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u59D3\\u540D\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: selectedOrder.customerName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u624B\\u673A\\u53F7\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  code: true,\n                  children: selectedOrder.customerPhone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8EAB\\u4EFD\\u8BC1\\u53F7\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  code: true,\n                  children: selectedOrder.customerIdCard\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u4EA7\\u54C1\\u4FE1\\u606F\",\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u4EA7\\u54C1\\u540D\\u79F0\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: selectedOrder.productName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 6,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8FD0\\u8425\\u5546\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: selectedOrder.operator\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 6,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u6708\\u79DF\\u8D39\\u7528\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: '#f5222d',\n                    fontWeight: 600\n                  },\n                  children: [\"\\xA5\", selectedOrder.monthlyFee]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u901A\\u7528\\u6D41\\u91CF\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: [selectedOrder.generalTraffic, \"GB\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 16,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u4EA7\\u54C1\\u63CF\\u8FF0\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 4,\n                    color: '#666',\n                    lineHeight: 1.5\n                  },\n                  children: selectedOrder.productDescription\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6536\\u8D27\\u5730\\u5740\",\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '8px 0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedOrder.deliveryAddress\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8BA2\\u5355\\u4FE1\\u606F\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8BA2\\u5355\\u53F7\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  code: true,\n                  children: selectedOrder.orderNo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u4F18\\u5148\\u7EA7\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: getPriorityColor(selectedOrder.priority),\n                  children: getPriorityText(selectedOrder.priority)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u7B49\\u5F85\\u65F6\\u95F4\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: selectedOrder.waitingTime > 1 ? '#f5222d' : '#52c41a'\n                  },\n                  children: selectedOrder.waitingTime < 1 ? `${Math.round(selectedOrder.waitingTime * 60)}分钟` : `${selectedOrder.waitingTime.toFixed(1)}小时`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u521B\\u5EFA\\u65F6\\u95F4\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: selectedOrder.createdAt\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 469,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderPending, \"CUnZ5qxv4ZcKKrb5md3GHskMPZw=\", false, function () {\n  return [Form.useForm];\n});\n_c = OrderPending;\nexport default OrderPending;\nvar _c;\n$RefreshReg$(_c, \"OrderPending\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "message", "Modal", "Form", "Select", "Input", "Row", "Col", "Statistic", "CheckOutlined", "CloseOutlined", "EyeOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "mockPendingOrders", "id", "orderNo", "customerName", "customerPhone", "customerIdCard", "productName", "productDescription", "monthlyFee", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "operator", "priority", "deliveryAddress", "createdAt", "waitingTime", "OrderPending", "_s", "orders", "setOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "processModalVisible", "setProcessModalVisible", "viewModalVisible", "setViewModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "form", "useForm", "handleRefresh", "transformApiData", "apiData", "order_no", "customer_name", "customer_phone", "customer_id_card", "product_name", "monthly_fee", "general_traffic", "directional_traffic", "free_minutes", "package_duration", "parseFloat", "delivery_address", "created_at", "Date", "toLocaleString", "Math", "max", "getTime", "getPriorityColor", "colors", "low", "normal", "high", "urgent", "getPriorityText", "texts", "stats", "total", "length", "filter", "order", "processing", "columns", "title", "dataIndex", "key", "width", "render", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sorter", "a", "b", "priorityOrder", "text", "code", "style", "fontSize", "_", "record", "fontWeight", "ellipsis", "address", "splitAddress", "addr", "splitPoints", "bestSplit", "floor", "i", "min", "some", "point", "char<PERSON>t", "substring", "line1", "line2", "max<PERSON><PERSON><PERSON>", "lineHeight", "marginBottom", "time", "round", "toFixed", "size", "type", "icon", "onClick", "handleProcessOrder", "handleViewOrder", "danger", "handleCancelOrder", "confirm", "content", "onOk", "success", "handleBatchProcess", "warning", "includes", "response", "fetch", "method", "headers", "result", "json", "data", "list", "error", "console", "handleProcessSubmit", "values", "validateFields", "body", "JSON", "stringify", "status", "action", "process_notes", "remark", "processed_by", "successMessage", "resetFields", "level", "margin", "gutter", "span", "value", "valueStyle", "display", "justifyContent", "alignItems", "strong", "marginLeft", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "okText", "cancelText", "padding", "background", "borderRadius", "border", "marginTop", "layout", "<PERSON><PERSON>", "name", "rules", "required", "placeholder", "Option", "label", "rows", "max<PERSON><PERSON><PERSON>", "showCount", "footer", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderPending.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  message,\n  Modal,\n  Form,\n  Select,\n  Input,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport {\n  CheckOutlined,\n  CloseOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface PendingOrder {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  customerIdCard: string; // 身份证号\n  productName: string;\n  productDescription?: string; // 产品描述\n  monthlyFee?: number; // 月租费用\n  generalTraffic?: number; // 通用流量\n  operator: string;\n  priority: 'low' | 'normal' | 'high' | 'urgent';\n  deliveryAddress: string; // 收货地址\n  createdAt: string;\n  waitingTime: number; // 等待时间（小时）\n}\n\n// 模拟待处理订单数据\nconst mockPendingOrders: PendingOrder[] = [\n  {\n    id: 1,\n    orderNo: 'ORD202401150003',\n    customerName: '王五',\n    customerPhone: '13800138003',\n    customerIdCard: '110101199001011234',\n    productName: '中国联通青春套餐',\n    productDescription: '5G网络，月租59元，包含20GB通用流量+30GB定向流量，300分钟通话',\n    monthlyFee: 59,\n    generalTraffic: 20,\n    operator: '中国联通',\n    priority: 'normal',\n    deliveryAddress: '北京市朝阳区建国门外大街1号国贸大厦A座1001室',\n    createdAt: '2024-01-15 12:00:00',\n    waitingTime: 2,\n  },\n  {\n    id: 2,\n    orderNo: 'ORD202401150005',\n    customerName: '钱七',\n    customerPhone: '13800138005',\n    customerIdCard: '310101198505155678',\n    productName: '中国移动5G尊享套餐',\n    productDescription: '5G网络，月租128元，包含50GB通用流量+100GB定向流量，1000分钟通话',\n    monthlyFee: 128,\n    generalTraffic: 50,\n    operator: '中国移动',\n    priority: 'high',\n    deliveryAddress: '上海市浦东新区陆家嘴环路1000号恒生银行大厦50楼',\n    createdAt: '2024-01-15 14:30:00',\n    waitingTime: 0.5,\n  },\n  {\n    id: 3,\n    orderNo: 'ORD202401150006',\n    customerName: '孙八',\n    customerPhone: '13800138006',\n    customerIdCard: '******************',\n    productName: '中国电信天翼套餐',\n    productDescription: '5G网络，月租89元，包含30GB通用流量+50GB定向流量，500分钟通话',\n    monthlyFee: 89,\n    generalTraffic: 30,\n    operator: '中国电信',\n    priority: 'urgent',\n    deliveryAddress: '广州市天河区珠江新城花城大道85号高德置地广场B2栋2001',\n    createdAt: '2024-01-15 15:00:00',\n    waitingTime: 0.2,\n  },\n  {\n    id: 4,\n    orderNo: 'ORD202401150007',\n    customerName: '李九',\n    customerPhone: '13800138007',\n    customerIdCard: '******************',\n    productName: '中国广电智慧套餐',\n    productDescription: '5G网络，月租79元，包含25GB通用流量+40GB定向流量，400分钟通话',\n    monthlyFee: 79,\n    generalTraffic: 25,\n    operator: '中国广电',\n    priority: 'normal',\n    deliveryAddress: '深圳市南山区深南大道9988号华润置地大厦A座3501室',\n    createdAt: '2024-01-15 16:00:00',\n    waitingTime: 1.5,\n  },\n];\n\nconst OrderPending: React.FC = () => {\n  const [orders, setOrders] = useState<PendingOrder[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [processModalVisible, setProcessModalVisible] = useState(false);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<PendingOrder | null>(null);\n  const [form] = Form.useForm();\n\n  // 页面加载时获取数据\n  useEffect(() => {\n    handleRefresh();\n  }, []);\n\n  // 转换API数据格式为前端格式\n  const transformApiData = (apiData: any): PendingOrder => {\n    return {\n      id: apiData.id,\n      orderNo: apiData.order_no,\n      customerName: apiData.customer_name,\n      customerPhone: apiData.customer_phone,\n      customerIdCard: apiData.customer_id_card,\n      productName: apiData.product_name,\n      productDescription: `月租费用：¥${apiData.monthly_fee}，通用流量：${apiData.general_traffic}GB，定向流量：${apiData.directional_traffic}GB，免费通话：${apiData.free_minutes}分钟，套餐时长：${apiData.package_duration}`,\n      monthlyFee: parseFloat(apiData.monthly_fee),\n      generalTraffic: parseFloat(apiData.general_traffic),\n      operator: apiData.operator,\n      priority: apiData.priority || 'normal',\n      deliveryAddress: apiData.delivery_address,\n      createdAt: apiData.created_at ? new Date(apiData.created_at).toLocaleString('zh-CN') : '',\n      waitingTime: apiData.created_at ?\n        Math.max(0.1, (new Date().getTime() - new Date(apiData.created_at).getTime()) / (1000 * 60 * 60)) : 0.1, // 计算等待时间（小时）\n    };\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = (priority: string) => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red',\n    };\n    return colors[priority as keyof typeof colors] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = (priority: string) => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急',\n    };\n    return texts[priority as keyof typeof texts] || priority;\n  };\n\n\n\n  // 统计数据\n  const stats = {\n    total: orders.length,\n    urgent: orders.filter(order => order.priority === 'urgent').length,\n    high: orders.filter(order => order.priority === 'high').length,\n    processing: orders.filter(order => order.waitingTime > 1).length, // 等待超过1小时的订单\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<PendingOrder> = [\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      width: 80,\n      render: (priority: string) => (\n        <Tag color={getPriorityColor(priority)}>\n          {getPriorityText(priority)}\n        </Tag>\n      ),\n      sorter: (a, b) => {\n        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };\n        return priorityOrder[a.priority as keyof typeof priorityOrder] - \n               priorityOrder[b.priority as keyof typeof priorityOrder];\n      },\n    },\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n    {\n      title: '收货地址',\n      dataIndex: 'deliveryAddress',\n      key: 'deliveryAddress',\n      width: 280,\n      render: (address: string) => {\n        // 智能分割地址为两行\n        const splitAddress = (addr: string) => {\n          if (addr.length <= 25) {\n            return [addr, ''];\n          }\n\n          // 寻找合适的分割点（优先在区、县、市、路、街、号等后面分割）\n          const splitPoints = ['区', '县', '市', '路', '街', '号', '栋', '座', '楼'];\n          let bestSplit = Math.floor(addr.length / 2);\n\n          for (let i = 15; i < Math.min(30, addr.length - 5); i++) {\n            if (splitPoints.some(point => addr.charAt(i) === point)) {\n              bestSplit = i + 1;\n              break;\n            }\n          }\n\n          return [\n            addr.substring(0, bestSplit),\n            addr.substring(bestSplit)\n          ];\n        };\n\n        const [line1, line2] = splitAddress(address);\n\n        return (\n          <div style={{ maxWidth: 260, lineHeight: '1.3' }}>\n            <div style={{\n              fontSize: '12px',\n              color: '#333',\n              marginBottom: line2 ? '2px' : '0'\n            }}>\n              {line1}\n            </div>\n            {line2 && (\n              <div style={{\n                fontSize: '12px',\n                color: '#666'\n              }}>\n                {line2}\n              </div>\n            )}\n          </div>\n        );\n      },\n    },\n    {\n      title: '等待时间',\n      dataIndex: 'waitingTime',\n      key: 'waitingTime',\n      width: 100,\n      render: (time: number) => (\n        <Text style={{ color: time > 1 ? '#f5222d' : '#52c41a' }}>\n          {time < 1 ? `${Math.round(time * 60)}分钟` : `${time.toFixed(1)}小时`}\n        </Text>\n      ),\n      sorter: (a, b) => a.waitingTime - b.waitingTime,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<CheckOutlined />}\n            onClick={() => handleProcessOrder(record)}\n          >\n            处理\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            danger\n            size=\"small\"\n            icon={<CloseOutlined />}\n            onClick={() => handleCancelOrder(record.id)}\n          >\n            取消\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleProcessOrder = (order: PendingOrder) => {\n    setSelectedOrder(order);\n    setProcessModalVisible(true);\n  };\n\n  const handleViewOrder = (order: PendingOrder) => {\n    setSelectedOrder(order);\n    setViewModalVisible(true);\n  };\n\n  const handleCancelOrder = (id: number) => {\n    Modal.confirm({\n      title: '确认取消订单',\n      icon: <ExclamationCircleOutlined />,\n      content: '确定要取消这个订单吗？此操作不可恢复。',\n      onOk: () => {\n        setOrders(orders.filter(order => order.id !== id));\n        message.success('订单已取消');\n      },\n    });\n  };\n\n  const handleBatchProcess = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要处理的订单');\n      return;\n    }\n    \n    Modal.confirm({\n      title: '批量处理订单',\n      content: `确定要批量处理选中的 ${selectedRowKeys.length} 个订单吗？`,\n      onOk: () => {\n        setOrders(orders.filter(order => !selectedRowKeys.includes(order.id)));\n        setSelectedRowKeys([]);\n        message.success('批量处理成功');\n      },\n    });\n  };\n\n  const handleRefresh = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/orders/pending', {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      const result = await response.json();\n\n      if (result.code === 200) {\n        setOrders(result.data.list);\n        message.success('数据已刷新');\n      } else {\n        message.error(result.message || '刷新失败');\n      }\n    } catch (error) {\n      console.error('刷新失败:', error);\n      message.error('网络错误，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleProcessSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (!selectedOrder) {\n        message.error('未选择订单');\n        return;\n      }\n\n      // 调用API更新订单状态\n      const response = await fetch(`http://localhost:8000/api/v1/orders/${selectedOrder.id}/status`, {\n        method: 'PATCH',\n        headers: {\n          'Accept': 'application/json',\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          status: values.action,\n          process_notes: values.remark,\n          processed_by: 'admin'\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.code === 200) {\n        let successMessage = '';\n\n        // 根据处理动作确定成功消息\n        if (values.action === 'processing') {\n          successMessage = '订单已转入开卡中，请在【审核中订单】中查看';\n        } else if (values.action === 'failed') {\n          successMessage = '订单已标记为失败，请在【失败订单】中查看';\n        } else if (values.action === 'pending_upload') {\n          successMessage = '订单已转入待上传三证状态，app用户端将收到上传链接';\n        }\n\n        // 从待处理订单列表中移除\n        setOrders(orders.filter(order => order.id !== selectedOrder.id));\n        setProcessModalVisible(false);\n        setSelectedOrder(null);\n        form.resetFields();\n\n        message.success(successMessage);\n      } else {\n        message.error(result.message || '处理失败');\n      }\n    } catch (error) {\n      console.error('处理失败:', error);\n      message.error('网络错误，请稍后重试');\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#faad14' }}>\n          待处理订单\n        </Title>\n        <Text type=\"secondary\">\n          处理需要人工审核的订单\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待处理总数\"\n              value={stats.total}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"紧急订单\"\n              value={stats.urgent}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"高优先级\"\n              value={stats.high}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"超时订单\"\n              value={stats.processing}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {orders.length} 个待处理订单\n            </Text>\n            {selectedRowKeys.length > 0 && (\n              <Text style={{ marginLeft: 16, color: '#1890ff' }}>\n                已选择 {selectedRowKeys.length} 个订单\n              </Text>\n            )}\n          </div>\n          <Space>\n            {selectedRowKeys.length > 0 && (\n              <Button type=\"primary\" onClick={handleBatchProcess}>\n                批量处理\n              </Button>\n            )}\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={orders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1480 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 处理订单弹窗 */}\n      <Modal\n        title=\"处理订单\"\n        open={processModalVisible}\n        onOk={handleProcessSubmit}\n        onCancel={() => {\n          setProcessModalVisible(false);\n          form.resetFields();\n        }}\n        okText=\"确认处理\"\n        cancelText=\"取消\"\n        width={600}\n      >\n        {selectedOrder && (\n          <div>\n            {/* 订单信息展示 */}\n            <div style={{ marginBottom: 24, padding: 16, background: '#f8f9fa', borderRadius: 8, border: '1px solid #e9ecef' }}>\n              <div style={{ marginBottom: 12 }}>\n                <Text strong style={{ fontSize: '14px' }}>订单号：</Text>\n                <Text code style={{ fontSize: '14px' }}>{selectedOrder.orderNo}</Text>\n              </div>\n\n              <div style={{ marginBottom: 12 }}>\n                <Text strong style={{ fontSize: '14px' }}>产品：</Text>\n                <Text style={{ fontSize: '14px' }}>{selectedOrder.productName}</Text>\n              </div>\n\n              <div style={{ marginBottom: 12 }}>\n                <Text strong style={{ fontSize: '14px' }}>运营商：</Text>\n                <Text style={{ fontSize: '14px' }}>{selectedOrder.operator}</Text>\n              </div>\n\n              <div style={{ marginBottom: 12 }}>\n                <Text strong style={{ fontSize: '14px' }}>姓名：</Text>\n                <Text style={{ fontSize: '14px' }}>{selectedOrder.customerName}</Text>\n              </div>\n\n              <div style={{ marginBottom: 12 }}>\n                <Text strong style={{ fontSize: '14px' }}>手机号：</Text>\n                <Text style={{ fontSize: '14px' }}>{selectedOrder.customerPhone}</Text>\n              </div>\n\n              <div style={{ marginBottom: 12 }}>\n                <Text strong style={{ fontSize: '14px' }}>收货地址：</Text>\n                <div style={{ fontSize: '14px', marginTop: 4, lineHeight: '1.5' }}>\n                  {selectedOrder.deliveryAddress}\n                </div>\n              </div>\n\n              <div>\n                <Text strong style={{ fontSize: '14px' }}>身份证号：</Text>\n                <Text style={{ fontSize: '14px' }}>{selectedOrder.customerIdCard}</Text>\n              </div>\n            </div>\n            \n            {/* 处理动作选择 */}\n            <div>\n              <Text strong style={{ fontSize: '16px', marginBottom: '12px', display: 'block' }}>处理动作：</Text>\n              <Form form={form} layout=\"vertical\">\n                <Form.Item\n                  name=\"action\"\n                  rules={[{ required: true, message: '请选择处理动作' }]}\n                >\n                  <Select\n                    placeholder=\"请选择处理动作\"\n                    style={{ width: '100%' }}\n                    size=\"large\"\n                  >\n                    <Select.Option value=\"processing\">开卡中</Select.Option>\n                    <Select.Option value=\"failed\">开卡失败</Select.Option>\n                    <Select.Option value=\"pending_upload\">待上传三证</Select.Option>\n                  </Select>\n                </Form.Item>\n\n                <Form.Item\n                  name=\"remark\"\n                  label={<Text strong style={{ fontSize: '14px' }}>备注说明</Text>}\n                  rules={[{ required: true, message: '请填写备注说明' }]}\n                >\n                  <Input.TextArea\n                    rows={4}\n                    placeholder=\"请详细说明处理情况、注意事项或失败原因...\"\n                    maxLength={500}\n                    showCount\n                    style={{ fontSize: '14px' }}\n                  />\n                </Form.Item>\n              </Form>\n            </div>\n          </div>\n        )}\n      </Modal>\n\n      {/* 查看订单详情弹窗 */}\n      <Modal\n        title=\"订单详情\"\n        open={viewModalVisible}\n        onCancel={() => setViewModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setViewModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={800}\n      >\n        {selectedOrder && (\n          <div>\n            {/* 客户信息 */}\n            <Card title=\"客户信息\" size=\"small\" style={{ marginBottom: 16 }}>\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>姓名：</Text>\n                    <Text>{selectedOrder.customerName}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>手机号：</Text>\n                    <Text code>{selectedOrder.customerPhone}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>身份证号：</Text>\n                    <Text code>{selectedOrder.customerIdCard}</Text>\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n\n            {/* 产品信息 */}\n            <Card title=\"产品信息\" size=\"small\" style={{ marginBottom: 16 }}>\n              <Row gutter={16}>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>产品名称：</Text>\n                    <Text>{selectedOrder.productName}</Text>\n                  </div>\n                </Col>\n                <Col span={6}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>运营商：</Text>\n                    <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n                  </div>\n                </Col>\n                <Col span={6}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>月租费用：</Text>\n                    <Text style={{ color: '#f5222d', fontWeight: 600 }}>\n                      ¥{selectedOrder.monthlyFee}\n                    </Text>\n                  </div>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>通用流量：</Text>\n                    <Text>{selectedOrder.generalTraffic}GB</Text>\n                  </div>\n                </Col>\n                <Col span={16}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>产品描述：</Text>\n                    <div style={{ marginTop: 4, color: '#666', lineHeight: 1.5 }}>\n                      {selectedOrder.productDescription}\n                    </div>\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n\n            {/* 收货地址 */}\n            <Card title=\"收货地址\" size=\"small\" style={{ marginBottom: 16 }}>\n              <div style={{ padding: '8px 0' }}>\n                <Text>{selectedOrder.deliveryAddress}</Text>\n              </div>\n            </Card>\n\n            {/* 订单信息 */}\n            <Card title=\"订单信息\" size=\"small\">\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>订单号：</Text>\n                    <Text code>{selectedOrder.orderNo}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>优先级：</Text>\n                    <Tag color={getPriorityColor(selectedOrder.priority)}>\n                      {getPriorityText(selectedOrder.priority)}\n                    </Tag>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>等待时间：</Text>\n                    <Text style={{ color: selectedOrder.waitingTime > 1 ? '#f5222d' : '#52c41a' }}>\n                      {selectedOrder.waitingTime < 1\n                        ? `${Math.round(selectedOrder.waitingTime * 60)}分钟`\n                        : `${selectedOrder.waitingTime.toFixed(1)}小时`}\n                    </Text>\n                  </div>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>创建时间：</Text>\n                    <Text>{selectedOrder.createdAt}</Text>\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderPending;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SACEC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,cAAc,EACdC,yBAAyB,QACpB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGlB,UAAU;AAClC,MAAM;EAAEmB;AAAS,CAAC,GAAGb,KAAK;AAmB1B;AACA,MAAMc,iBAAiC,GAAG,CACxC;EACEC,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,UAAU;EACvBC,kBAAkB,EAAE,wCAAwC;EAC5DC,UAAU,EAAE,EAAE;EACdC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,eAAe,EAAE,2BAA2B;EAC5CC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,YAAY;EACzBC,kBAAkB,EAAE,2CAA2C;EAC/DC,UAAU,EAAE,GAAG;EACfC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,MAAM;EAChBC,eAAe,EAAE,4BAA4B;EAC7CC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,UAAU;EACvBC,kBAAkB,EAAE,wCAAwC;EAC5DC,UAAU,EAAE,EAAE;EACdC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,eAAe,EAAE,gCAAgC;EACjDC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,cAAc,EAAE,oBAAoB;EACpCC,WAAW,EAAE,UAAU;EACvBC,kBAAkB,EAAE,wCAAwC;EAC5DC,UAAU,EAAE,EAAE;EACdC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,eAAe,EAAE,8BAA8B;EAC/CC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAiB,EAAE,CAAC;EACxD,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAACiD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAsB,IAAI,CAAC;EAC7E,MAAM,CAACuD,IAAI,CAAC,GAAG7C,IAAI,CAAC8C,OAAO,CAAC,CAAC;;EAE7B;EACAvD,SAAS,CAAC,MAAM;IACdwD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAIC,OAAY,IAAmB;IACvD,OAAO;MACLhC,EAAE,EAAEgC,OAAO,CAAChC,EAAE;MACdC,OAAO,EAAE+B,OAAO,CAACC,QAAQ;MACzB/B,YAAY,EAAE8B,OAAO,CAACE,aAAa;MACnC/B,aAAa,EAAE6B,OAAO,CAACG,cAAc;MACrC/B,cAAc,EAAE4B,OAAO,CAACI,gBAAgB;MACxC/B,WAAW,EAAE2B,OAAO,CAACK,YAAY;MACjC/B,kBAAkB,EAAE,SAAS0B,OAAO,CAACM,WAAW,SAASN,OAAO,CAACO,eAAe,WAAWP,OAAO,CAACQ,mBAAmB,WAAWR,OAAO,CAACS,YAAY,WAAWT,OAAO,CAACU,gBAAgB,EAAE;MAC1LnC,UAAU,EAAEoC,UAAU,CAACX,OAAO,CAACM,WAAW,CAAC;MAC3C9B,cAAc,EAAEmC,UAAU,CAACX,OAAO,CAACO,eAAe,CAAC;MACnD9B,QAAQ,EAAEuB,OAAO,CAACvB,QAAQ;MAC1BC,QAAQ,EAAEsB,OAAO,CAACtB,QAAQ,IAAI,QAAQ;MACtCC,eAAe,EAAEqB,OAAO,CAACY,gBAAgB;MACzChC,SAAS,EAAEoB,OAAO,CAACa,UAAU,GAAG,IAAIC,IAAI,CAACd,OAAO,CAACa,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE;MACzFlC,WAAW,EAAEmB,OAAO,CAACa,UAAU,GAC7BG,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAIH,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAG,IAAIJ,IAAI,CAACd,OAAO,CAACa,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAE;IAC7G,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIzC,QAAgB,IAAK;IAC7C,MAAM0C,MAAM,GAAG;MACbC,GAAG,EAAE,SAAS;MACdC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAOJ,MAAM,CAAC1C,QAAQ,CAAwB,IAAI,SAAS;EAC7D,CAAC;;EAED;EACA,MAAM+C,eAAe,GAAI/C,QAAgB,IAAK;IAC5C,MAAMgD,KAAK,GAAG;MACZL,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE;IACV,CAAC;IACD,OAAOE,KAAK,CAAChD,QAAQ,CAAuB,IAAIA,QAAQ;EAC1D,CAAC;;EAID;EACA,MAAMiD,KAAK,GAAG;IACZC,KAAK,EAAE5C,MAAM,CAAC6C,MAAM;IACpBL,MAAM,EAAExC,MAAM,CAAC8C,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrD,QAAQ,KAAK,QAAQ,CAAC,CAACmD,MAAM;IAClEN,IAAI,EAAEvC,MAAM,CAAC8C,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrD,QAAQ,KAAK,MAAM,CAAC,CAACmD,MAAM;IAC9DG,UAAU,EAAEhD,MAAM,CAAC8C,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAClD,WAAW,GAAG,CAAC,CAAC,CAACgD,MAAM,CAAE;EACpE,CAAC;;EAED;EACA,MAAMI,OAAkC,GAAG,CACzC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAG5D,QAAgB,iBACvBf,OAAA,CAACf,GAAG;MAAC2F,KAAK,EAAEpB,gBAAgB,CAACzC,QAAQ,CAAE;MAAA8D,QAAA,EACpCf,eAAe,CAAC/C,QAAQ;IAAC;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACN;IACDC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK;MAChB,MAAMC,aAAa,GAAG;QAAExB,MAAM,EAAE,CAAC;QAAED,IAAI,EAAE,CAAC;QAAED,MAAM,EAAE,CAAC;QAAED,GAAG,EAAE;MAAE,CAAC;MAC/D,OAAO2B,aAAa,CAACF,CAAC,CAACpE,QAAQ,CAA+B,GACvDsE,aAAa,CAACD,CAAC,CAACrE,QAAQ,CAA+B;IAChE;EACF,CAAC,EACD;IACEwD,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnBtF,OAAA,CAACE,IAAI;MAACqF,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAZ,QAAA,EACpCS;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChB3F,OAAA;MAAA6E,QAAA,gBACE7E,OAAA;QAAKwF,KAAK,EAAE;UAAEI,UAAU,EAAE;QAAI,CAAE;QAAAf,QAAA,EAAEc,MAAM,CAACpF;MAAY;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5DjF,OAAA;QAAKwF,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEb,KAAK,EAAE;QAAO,CAAE;QAAAC,QAAA,EAC7Cc,MAAM,CAACnF;MAAa;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBoB,QAAQ,EAAE;EACZ,CAAC,EACD;IACEtB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnBtF,OAAA,CAACf,GAAG;MAAC2F,KAAK,EAAC,MAAM;MAAAC,QAAA,EAAES;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGmB,OAAe,IAAK;MAC3B;MACA,MAAMC,YAAY,GAAIC,IAAY,IAAK;QACrC,IAAIA,IAAI,CAAC9B,MAAM,IAAI,EAAE,EAAE;UACrB,OAAO,CAAC8B,IAAI,EAAE,EAAE,CAAC;QACnB;;QAEA;QACA,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QACjE,IAAIC,SAAS,GAAG7C,IAAI,CAAC8C,KAAK,CAACH,IAAI,CAAC9B,MAAM,GAAG,CAAC,CAAC;QAE3C,KAAK,IAAIkC,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG/C,IAAI,CAACgD,GAAG,CAAC,EAAE,EAAEL,IAAI,CAAC9B,MAAM,GAAG,CAAC,CAAC,EAAEkC,CAAC,EAAE,EAAE;UACvD,IAAIH,WAAW,CAACK,IAAI,CAACC,KAAK,IAAIP,IAAI,CAACQ,MAAM,CAACJ,CAAC,CAAC,KAAKG,KAAK,CAAC,EAAE;YACvDL,SAAS,GAAGE,CAAC,GAAG,CAAC;YACjB;UACF;QACF;QAEA,OAAO,CACLJ,IAAI,CAACS,SAAS,CAAC,CAAC,EAAEP,SAAS,CAAC,EAC5BF,IAAI,CAACS,SAAS,CAACP,SAAS,CAAC,CAC1B;MACH,CAAC;MAED,MAAM,CAACQ,KAAK,EAAEC,KAAK,CAAC,GAAGZ,YAAY,CAACD,OAAO,CAAC;MAE5C,oBACE9F,OAAA;QAAKwF,KAAK,EAAE;UAAEoB,QAAQ,EAAE,GAAG;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAhC,QAAA,gBAC/C7E,OAAA;UAAKwF,KAAK,EAAE;YACVC,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE,MAAM;YACbkC,YAAY,EAAEH,KAAK,GAAG,KAAK,GAAG;UAChC,CAAE;UAAA9B,QAAA,EACC6B;QAAK;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACL0B,KAAK,iBACJ3G,OAAA;UAAKwF,KAAK,EAAE;YACVC,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EACC8B;QAAK;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEV;EACF,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGoC,IAAY,iBACnB/G,OAAA,CAACE,IAAI;MAACsF,KAAK,EAAE;QAAEZ,KAAK,EAAEmC,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAlC,QAAA,EACtDkC,IAAI,GAAG,CAAC,GAAG,GAAG1D,IAAI,CAAC2D,KAAK,CAACD,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,GAAGA,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC;IAAI;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CACP;IACDC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjE,WAAW,GAAGkE,CAAC,CAAClE;EACtC,CAAC,EACD;IACEqD,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnBtF,OAAA;MAAKwF,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAZ,QAAA,EAC9BS;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChB3F,OAAA,CAACjB,KAAK;MAACmI,IAAI,EAAC,OAAO;MAAArC,QAAA,gBACjB7E,OAAA,CAAClB,MAAM;QACLqI,IAAI,EAAC,SAAS;QACdD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAEpH,OAAA,CAACN,aAAa;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBoC,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAAC3B,MAAM,CAAE;QAAAd,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjF,OAAA,CAAClB,MAAM;QACLqI,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAEpH,OAAA,CAACJ,WAAW;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBoC,OAAO,EAAEA,CAAA,KAAME,eAAe,CAAC5B,MAAM,CAAE;QAAAd,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjF,OAAA,CAAClB,MAAM;QACL0I,MAAM;QACNN,IAAI,EAAC,OAAO;QACZE,IAAI,eAAEpH,OAAA,CAACL,aAAa;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBoC,OAAO,EAAEA,CAAA,KAAMI,iBAAiB,CAAC9B,MAAM,CAACtF,EAAE,CAAE;QAAAwE,QAAA,EAC7C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMqC,kBAAkB,GAAIlD,KAAmB,IAAK;IAClDpC,gBAAgB,CAACoC,KAAK,CAAC;IACvBxC,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM2F,eAAe,GAAInD,KAAmB,IAAK;IAC/CpC,gBAAgB,CAACoC,KAAK,CAAC;IACvBtC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM2F,iBAAiB,GAAIpH,EAAU,IAAK;IACxClB,KAAK,CAACuI,OAAO,CAAC;MACZnD,KAAK,EAAE,QAAQ;MACf6C,IAAI,eAAEpH,OAAA,CAACF,yBAAyB;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnC0C,OAAO,EAAE,qBAAqB;MAC9BC,IAAI,EAAEA,CAAA,KAAM;QACVtG,SAAS,CAACD,MAAM,CAAC8C,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC/D,EAAE,KAAKA,EAAE,CAAC,CAAC;QAClDnB,OAAO,CAAC2I,OAAO,CAAC,OAAO,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIrG,eAAe,CAACyC,MAAM,KAAK,CAAC,EAAE;MAChChF,OAAO,CAAC6I,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;IAEA5I,KAAK,CAACuI,OAAO,CAAC;MACZnD,KAAK,EAAE,QAAQ;MACfoD,OAAO,EAAE,cAAclG,eAAe,CAACyC,MAAM,QAAQ;MACrD0D,IAAI,EAAEA,CAAA,KAAM;QACVtG,SAAS,CAACD,MAAM,CAAC8C,MAAM,CAACC,KAAK,IAAI,CAAC3C,eAAe,CAACuG,QAAQ,CAAC5D,KAAK,CAAC/D,EAAE,CAAC,CAAC,CAAC;QACtEqB,kBAAkB,CAAC,EAAE,CAAC;QACtBxC,OAAO,CAAC2I,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM1F,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMyG,QAAQ,GAAG,MAAMC,KAAK,CAAC,6CAA6C,EAAE;QAC1EC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAEpC,IAAID,MAAM,CAAC9C,IAAI,KAAK,GAAG,EAAE;QACvBjE,SAAS,CAAC+G,MAAM,CAACE,IAAI,CAACC,IAAI,CAAC;QAC3BtJ,OAAO,CAAC2I,OAAO,CAAC,OAAO,CAAC;MAC1B,CAAC,MAAM;QACL3I,OAAO,CAACuJ,KAAK,CAACJ,MAAM,CAACnJ,OAAO,IAAI,MAAM,CAAC;MACzC;IACF,CAAC,CAAC,OAAOuJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BvJ,OAAO,CAACuJ,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRjH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmH,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM3G,IAAI,CAAC4G,cAAc,CAAC,CAAC;MAE1C,IAAI,CAAC9G,aAAa,EAAE;QAClB7C,OAAO,CAACuJ,KAAK,CAAC,OAAO,CAAC;QACtB;MACF;;MAEA;MACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuCnG,aAAa,CAAC1B,EAAE,SAAS,EAAE;QAC7F8H,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE;UACP,QAAQ,EAAE,kBAAkB;UAC5B,cAAc,EAAE;QAClB,CAAC;QACDU,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,MAAM,EAAEL,MAAM,CAACM,MAAM;UACrBC,aAAa,EAAEP,MAAM,CAACQ,MAAM;UAC5BC,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMhB,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAEpC,IAAID,MAAM,CAAC9C,IAAI,KAAK,GAAG,EAAE;QACvB,IAAI+D,cAAc,GAAG,EAAE;;QAEvB;QACA,IAAIV,MAAM,CAACM,MAAM,KAAK,YAAY,EAAE;UAClCI,cAAc,GAAG,uBAAuB;QAC1C,CAAC,MAAM,IAAIV,MAAM,CAACM,MAAM,KAAK,QAAQ,EAAE;UACrCI,cAAc,GAAG,sBAAsB;QACzC,CAAC,MAAM,IAAIV,MAAM,CAACM,MAAM,KAAK,gBAAgB,EAAE;UAC7CI,cAAc,GAAG,4BAA4B;QAC/C;;QAEA;QACAhI,SAAS,CAACD,MAAM,CAAC8C,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC/D,EAAE,KAAK0B,aAAa,CAAC1B,EAAE,CAAC,CAAC;QAChEuB,sBAAsB,CAAC,KAAK,CAAC;QAC7BI,gBAAgB,CAAC,IAAI,CAAC;QACtBC,IAAI,CAACsH,WAAW,CAAC,CAAC;QAElBrK,OAAO,CAAC2I,OAAO,CAACyB,cAAc,CAAC;MACjC,CAAC,MAAM;QACLpK,OAAO,CAACuJ,KAAK,CAACJ,MAAM,CAACnJ,OAAO,IAAI,MAAM,CAAC;MACzC;IACF,CAAC,CAAC,OAAOuJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BvJ,OAAO,CAACuJ,KAAK,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC;EAED,oBACEzI,OAAA;IAAA6E,QAAA,gBACE7E,OAAA;MAAKwF,KAAK,EAAE;QAAEsB,YAAY,EAAE;MAAO,CAAE;MAAAjC,QAAA,gBACnC7E,OAAA,CAACC,KAAK;QAACuJ,KAAK,EAAE,CAAE;QAAChE,KAAK,EAAE;UAAEiE,MAAM,EAAE,CAAC;UAAE7E,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjF,OAAA,CAACE,IAAI;QAACiH,IAAI,EAAC,WAAW;QAAAtC,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNjF,OAAA,CAACT,GAAG;MAACmK,MAAM,EAAE,EAAG;MAAClE,KAAK,EAAE;QAAEsB,YAAY,EAAE;MAAO,CAAE;MAAAjC,QAAA,gBAC/C7E,OAAA,CAACR,GAAG;QAACmK,IAAI,EAAE,CAAE;QAAA9E,QAAA,eACX7E,OAAA,CAACpB,IAAI;UAAAiG,QAAA,eACH7E,OAAA,CAACP,SAAS;YACR8E,KAAK,EAAC,gCAAO;YACbqF,KAAK,EAAE5F,KAAK,CAACC,KAAM;YACnB4F,UAAU,EAAE;cAAEjF,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjF,OAAA,CAACR,GAAG;QAACmK,IAAI,EAAE,CAAE;QAAA9E,QAAA,eACX7E,OAAA,CAACpB,IAAI;UAAAiG,QAAA,eACH7E,OAAA,CAACP,SAAS;YACR8E,KAAK,EAAC,0BAAM;YACZqF,KAAK,EAAE5F,KAAK,CAACH,MAAO;YACpBgG,UAAU,EAAE;cAAEjF,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjF,OAAA,CAACR,GAAG;QAACmK,IAAI,EAAE,CAAE;QAAA9E,QAAA,eACX7E,OAAA,CAACpB,IAAI;UAAAiG,QAAA,eACH7E,OAAA,CAACP,SAAS;YACR8E,KAAK,EAAC,0BAAM;YACZqF,KAAK,EAAE5F,KAAK,CAACJ,IAAK;YAClBiG,UAAU,EAAE;cAAEjF,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjF,OAAA,CAACR,GAAG;QAACmK,IAAI,EAAE,CAAE;QAAA9E,QAAA,eACX7E,OAAA,CAACpB,IAAI;UAAAiG,QAAA,eACH7E,OAAA,CAACP,SAAS;YACR8E,KAAK,EAAC,0BAAM;YACZqF,KAAK,EAAE5F,KAAK,CAACK,UAAW;YACxBwF,UAAU,EAAE;cAAEjF,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjF,OAAA,CAACpB,IAAI;MAAAiG,QAAA,gBAEH7E,OAAA;QAAKwF,KAAK,EAAE;UACVsE,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBlD,YAAY,EAAE;QAChB,CAAE;QAAAjC,QAAA,gBACA7E,OAAA;UAAA6E,QAAA,gBACE7E,OAAA,CAACE,IAAI;YAAC+J,MAAM;YAAApF,QAAA,GAAC,SACT,EAACxD,MAAM,CAAC6C,MAAM,EAAC,uCACnB;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNxD,eAAe,CAACyC,MAAM,GAAG,CAAC,iBACzBlE,OAAA,CAACE,IAAI;YAACsF,KAAK,EAAE;cAAE0E,UAAU,EAAE,EAAE;cAAEtF,KAAK,EAAE;YAAU,CAAE;YAAAC,QAAA,GAAC,qBAC7C,EAACpD,eAAe,CAACyC,MAAM,EAAC,qBAC9B;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNjF,OAAA,CAACjB,KAAK;UAAA8F,QAAA,GACHpD,eAAe,CAACyC,MAAM,GAAG,CAAC,iBACzBlE,OAAA,CAAClB,MAAM;YAACqI,IAAI,EAAC,SAAS;YAACE,OAAO,EAAES,kBAAmB;YAAAjD,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDjF,OAAA,CAAClB,MAAM;YAACuI,OAAO,EAAElF,aAAc;YAACiF,IAAI,eAAEpH,OAAA,CAACH,cAAc;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNjF,OAAA,CAACnB,KAAK;QACJyF,OAAO,EAAEA,OAAQ;QACjB6F,UAAU,EAAE9I,MAAO;QACnB+I,MAAM,EAAC,IAAI;QACX7I,OAAO,EAAEA,OAAQ;QACjB8I,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACzG,KAAK,EAAE0G,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAAS1G,KAAK;QAC3C,CAAE;QACF2G,YAAY,EAAE;UACZnJ,eAAe;UACfoJ,QAAQ,EAAEnJ;QACZ;MAAE;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPjF,OAAA,CAACb,KAAK;MACJoF,KAAK,EAAC,0BAAM;MACZuG,IAAI,EAAEnJ,mBAAoB;MAC1BiG,IAAI,EAAEe,mBAAoB;MAC1BoC,QAAQ,EAAEA,CAAA,KAAM;QACdnJ,sBAAsB,CAAC,KAAK,CAAC;QAC7BK,IAAI,CAACsH,WAAW,CAAC,CAAC;MACpB,CAAE;MACFyB,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfvG,KAAK,EAAE,GAAI;MAAAG,QAAA,EAEV9C,aAAa,iBACZ/B,OAAA;QAAA6E,QAAA,gBAEE7E,OAAA;UAAKwF,KAAK,EAAE;YAAEsB,YAAY,EAAE,EAAE;YAAEoE,OAAO,EAAE,EAAE;YAAEC,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAoB,CAAE;UAAAxG,QAAA,gBACjH7E,OAAA;YAAKwF,KAAK,EAAE;cAAEsB,YAAY,EAAE;YAAG,CAAE;YAAAjC,QAAA,gBAC/B7E,OAAA,CAACE,IAAI;cAAC+J,MAAM;cAACzE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDjF,OAAA,CAACE,IAAI;cAACqF,IAAI;cAACC,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAE9C,aAAa,CAACzB;YAAO;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAENjF,OAAA;YAAKwF,KAAK,EAAE;cAAEsB,YAAY,EAAE;YAAG,CAAE;YAAAjC,QAAA,gBAC/B7E,OAAA,CAACE,IAAI;cAAC+J,MAAM;cAACzE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDjF,OAAA,CAACE,IAAI;cAACsF,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAE9C,aAAa,CAACrB;YAAW;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAENjF,OAAA;YAAKwF,KAAK,EAAE;cAAEsB,YAAY,EAAE;YAAG,CAAE;YAAAjC,QAAA,gBAC/B7E,OAAA,CAACE,IAAI;cAAC+J,MAAM;cAACzE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDjF,OAAA,CAACE,IAAI;cAACsF,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAE9C,aAAa,CAACjB;YAAQ;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eAENjF,OAAA;YAAKwF,KAAK,EAAE;cAAEsB,YAAY,EAAE;YAAG,CAAE;YAAAjC,QAAA,gBAC/B7E,OAAA,CAACE,IAAI;cAAC+J,MAAM;cAACzE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDjF,OAAA,CAACE,IAAI;cAACsF,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAE9C,aAAa,CAACxB;YAAY;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAENjF,OAAA;YAAKwF,KAAK,EAAE;cAAEsB,YAAY,EAAE;YAAG,CAAE;YAAAjC,QAAA,gBAC/B7E,OAAA,CAACE,IAAI;cAAC+J,MAAM;cAACzE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDjF,OAAA,CAACE,IAAI;cAACsF,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAE9C,aAAa,CAACvB;YAAa;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAENjF,OAAA;YAAKwF,KAAK,EAAE;cAAEsB,YAAY,EAAE;YAAG,CAAE;YAAAjC,QAAA,gBAC/B7E,OAAA,CAACE,IAAI;cAAC+J,MAAM;cAACzE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDjF,OAAA;cAAKwF,KAAK,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAE6F,SAAS,EAAE,CAAC;gBAAEzE,UAAU,EAAE;cAAM,CAAE;cAAAhC,QAAA,EAC/D9C,aAAa,CAACf;YAAe;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjF,OAAA;YAAA6E,QAAA,gBACE7E,OAAA,CAACE,IAAI;cAAC+J,MAAM;cAACzE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDjF,OAAA,CAACE,IAAI;cAACsF,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAE9C,aAAa,CAACtB;YAAc;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjF,OAAA;UAAA6E,QAAA,gBACE7E,OAAA,CAACE,IAAI;YAAC+J,MAAM;YAACzE,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEqB,YAAY,EAAE,MAAM;cAAEgD,OAAO,EAAE;YAAQ,CAAE;YAAAjF,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9FjF,OAAA,CAACZ,IAAI;YAAC6C,IAAI,EAAEA,IAAK;YAACsJ,MAAM,EAAC,UAAU;YAAA1G,QAAA,gBACjC7E,OAAA,CAACZ,IAAI,CAACoM,IAAI;cACRC,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzM,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2F,QAAA,eAEhD7E,OAAA,CAACX,MAAM;gBACLuM,WAAW,EAAC,4CAAS;gBACrBpG,KAAK,EAAE;kBAAEd,KAAK,EAAE;gBAAO,CAAE;gBACzBwC,IAAI,EAAC,OAAO;gBAAArC,QAAA,gBAEZ7E,OAAA,CAACX,MAAM,CAACwM,MAAM;kBAACjC,KAAK,EAAC,YAAY;kBAAA/E,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eACrDjF,OAAA,CAACX,MAAM,CAACwM,MAAM;kBAACjC,KAAK,EAAC,QAAQ;kBAAA/E,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAClDjF,OAAA,CAACX,MAAM,CAACwM,MAAM;kBAACjC,KAAK,EAAC,gBAAgB;kBAAA/E,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZjF,OAAA,CAACZ,IAAI,CAACoM,IAAI;cACRC,IAAI,EAAC,QAAQ;cACbK,KAAK,eAAE9L,OAAA,CAACE,IAAI;gBAAC+J,MAAM;gBAACzE,KAAK,EAAE;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAC7DyG,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzM,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2F,QAAA,eAEhD7E,OAAA,CAACV,KAAK,CAACa,QAAQ;gBACb4L,IAAI,EAAE,CAAE;gBACRH,WAAW,EAAC,uHAAwB;gBACpCI,SAAS,EAAE,GAAI;gBACfC,SAAS;gBACTzG,KAAK,EAAE;kBAAEC,QAAQ,EAAE;gBAAO;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRjF,OAAA,CAACb,KAAK;MACJoF,KAAK,EAAC,0BAAM;MACZuG,IAAI,EAAEjJ,gBAAiB;MACvBkJ,QAAQ,EAAEA,CAAA,KAAMjJ,mBAAmB,CAAC,KAAK,CAAE;MAC3CoK,MAAM,EAAE,cACNlM,OAAA,CAAClB,MAAM;QAAauI,OAAO,EAAEA,CAAA,KAAMvF,mBAAmB,CAAC,KAAK,CAAE;QAAA+C,QAAA,EAAC;MAE/D,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFP,KAAK,EAAE,GAAI;MAAAG,QAAA,EAEV9C,aAAa,iBACZ/B,OAAA;QAAA6E,QAAA,gBAEE7E,OAAA,CAACpB,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAAC2C,IAAI,EAAC,OAAO;UAAC1B,KAAK,EAAE;YAAEsB,YAAY,EAAE;UAAG,CAAE;UAAAjC,QAAA,eAC1D7E,OAAA,CAACT,GAAG;YAACmK,MAAM,EAAE,EAAG;YAAA7E,QAAA,gBACd7E,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvBjF,OAAA,CAACE,IAAI;kBAAA2E,QAAA,EAAE9C,aAAa,CAACxB;gBAAY;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjF,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxBjF,OAAA,CAACE,IAAI;kBAACqF,IAAI;kBAAAV,QAAA,EAAE9C,aAAa,CAACvB;gBAAa;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjF,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBjF,OAAA,CAACE,IAAI;kBAACqF,IAAI;kBAAAV,QAAA,EAAE9C,aAAa,CAACtB;gBAAc;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPjF,OAAA,CAACpB,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAAC2C,IAAI,EAAC,OAAO;UAAC1B,KAAK,EAAE;YAAEsB,YAAY,EAAE;UAAG,CAAE;UAAAjC,QAAA,gBAC1D7E,OAAA,CAACT,GAAG;YAACmK,MAAM,EAAE,EAAG;YAAA7E,QAAA,gBACd7E,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,EAAG;cAAA9E,QAAA,eACZ7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBjF,OAAA,CAACE,IAAI;kBAAA2E,QAAA,EAAE9C,aAAa,CAACrB;gBAAW;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjF,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxBjF,OAAA,CAACf,GAAG;kBAAC2F,KAAK,EAAC,MAAM;kBAAAC,QAAA,EAAE9C,aAAa,CAACjB;gBAAQ;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjF,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBjF,OAAA,CAACE,IAAI;kBAACsF,KAAK,EAAE;oBAAEZ,KAAK,EAAE,SAAS;oBAAEgB,UAAU,EAAE;kBAAI,CAAE;kBAAAf,QAAA,GAAC,MACjD,EAAC9C,aAAa,CAACnB,UAAU;gBAAA;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjF,OAAA,CAACT,GAAG;YAACmK,MAAM,EAAE,EAAG;YAAA7E,QAAA,gBACd7E,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBjF,OAAA,CAACE,IAAI;kBAAA2E,QAAA,GAAE9C,aAAa,CAAClB,cAAc,EAAC,IAAE;gBAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjF,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,EAAG;cAAA9E,QAAA,eACZ7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBjF,OAAA;kBAAKwF,KAAK,EAAE;oBAAE8F,SAAS,EAAE,CAAC;oBAAE1G,KAAK,EAAE,MAAM;oBAAEiC,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,EAC1D9C,aAAa,CAACpB;gBAAkB;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPjF,OAAA,CAACpB,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAAC2C,IAAI,EAAC,OAAO;UAAC1B,KAAK,EAAE;YAAEsB,YAAY,EAAE;UAAG,CAAE;UAAAjC,QAAA,eAC1D7E,OAAA;YAAKwF,KAAK,EAAE;cAAE0F,OAAO,EAAE;YAAQ,CAAE;YAAArG,QAAA,eAC/B7E,OAAA,CAACE,IAAI;cAAA2E,QAAA,EAAE9C,aAAa,CAACf;YAAe;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPjF,OAAA,CAACpB,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAAC2C,IAAI,EAAC,OAAO;UAAArC,QAAA,gBAC7B7E,OAAA,CAACT,GAAG;YAACmK,MAAM,EAAE,EAAG;YAAA7E,QAAA,gBACd7E,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxBjF,OAAA,CAACE,IAAI;kBAACqF,IAAI;kBAAAV,QAAA,EAAE9C,aAAa,CAACzB;gBAAO;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjF,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxBjF,OAAA,CAACf,GAAG;kBAAC2F,KAAK,EAAEpB,gBAAgB,CAACzB,aAAa,CAAChB,QAAQ,CAAE;kBAAA8D,QAAA,EAClDf,eAAe,CAAC/B,aAAa,CAAChB,QAAQ;gBAAC;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjF,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,CAAE;cAAA9E,QAAA,eACX7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBjF,OAAA,CAACE,IAAI;kBAACsF,KAAK,EAAE;oBAAEZ,KAAK,EAAE7C,aAAa,CAACb,WAAW,GAAG,CAAC,GAAG,SAAS,GAAG;kBAAU,CAAE;kBAAA2D,QAAA,EAC3E9C,aAAa,CAACb,WAAW,GAAG,CAAC,GAC1B,GAAGmC,IAAI,CAAC2D,KAAK,CAACjF,aAAa,CAACb,WAAW,GAAG,EAAE,CAAC,IAAI,GACjD,GAAGa,aAAa,CAACb,WAAW,CAAC+F,OAAO,CAAC,CAAC,CAAC;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjF,OAAA,CAACT,GAAG;YAACmK,MAAM,EAAE,EAAG;YAAA7E,QAAA,eACd7E,OAAA,CAACR,GAAG;cAACmK,IAAI,EAAE,EAAG;cAAA9E,QAAA,eACZ7E,OAAA;gBAAKwF,KAAK,EAAE;kBAAEsB,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBAC9B7E,OAAA,CAACE,IAAI;kBAAC+J,MAAM;kBAAApF,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBjF,OAAA,CAACE,IAAI;kBAAA2E,QAAA,EAAE9C,aAAa,CAACd;gBAAS;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC7D,EAAA,CApqBID,YAAsB;EAAA,QAOX/B,IAAI,CAAC8C,OAAO;AAAA;AAAAiK,EAAA,GAPvBhL,YAAsB;AAsqB5B,eAAeA,YAAY;AAAC,IAAAgL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
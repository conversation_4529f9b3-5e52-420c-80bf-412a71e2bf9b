{"ast": null, "code": "import classNames from 'classnames';\nexport const flexWrapValues = ['wrap', 'nowrap', 'wrap-reverse'];\nexport const justifyContentValues = ['flex-start', 'flex-end', 'start', 'end', 'center', 'space-between', 'space-around', 'space-evenly', 'stretch', 'normal', 'left', 'right'];\nexport const alignItemsValues = ['center', 'start', 'end', 'flex-start', 'flex-end', 'self-start', 'self-end', 'baseline', 'normal', 'stretch'];\nconst genClsWrap = (prefixCls, props) => {\n  const wrap = props.wrap === true ? 'wrap' : props.wrap;\n  return {\n    [`${prefixCls}-wrap-${wrap}`]: wrap && flexWrapValues.includes(wrap)\n  };\n};\nconst genClsAlign = (prefixCls, props) => {\n  const alignCls = {};\n  alignItemsValues.forEach(cssKey => {\n    alignCls[`${prefixCls}-align-${cssKey}`] = props.align === cssKey;\n  });\n  alignCls[`${prefixCls}-align-stretch`] = !props.align && !!props.vertical;\n  return alignCls;\n};\nconst genClsJustify = (prefixCls, props) => {\n  const justifyCls = {};\n  justifyContentValues.forEach(cssKey => {\n    justifyCls[`${prefixCls}-justify-${cssKey}`] = props.justify === cssKey;\n  });\n  return justifyCls;\n};\nfunction createFlexClassNames(prefixCls, props) {\n  return classNames(Object.assign(Object.assign(Object.assign({}, genClsWrap(prefixCls, props)), genClsAlign(prefixCls, props)), genClsJustify(prefixCls, props)));\n}\nexport default createFlexClassNames;", "map": {"version": 3, "names": ["classNames", "flexWrapValues", "justify<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alignItemsValues", "genClsWrap", "prefixCls", "props", "wrap", "includes", "genClsAlign", "alignCls", "for<PERSON>ach", "cssKey", "align", "vertical", "genClsJustify", "justifyCls", "justify", "createFlexClassNames", "Object", "assign"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/flex/utils.js"], "sourcesContent": ["import classNames from 'classnames';\nexport const flexWrapValues = ['wrap', 'nowrap', 'wrap-reverse'];\nexport const justifyContentValues = ['flex-start', 'flex-end', 'start', 'end', 'center', 'space-between', 'space-around', 'space-evenly', 'stretch', 'normal', 'left', 'right'];\nexport const alignItemsValues = ['center', 'start', 'end', 'flex-start', 'flex-end', 'self-start', 'self-end', 'baseline', 'normal', 'stretch'];\nconst genClsWrap = (prefixCls, props) => {\n  const wrap = props.wrap === true ? 'wrap' : props.wrap;\n  return {\n    [`${prefixCls}-wrap-${wrap}`]: wrap && flexWrapValues.includes(wrap)\n  };\n};\nconst genClsAlign = (prefixCls, props) => {\n  const alignCls = {};\n  alignItemsValues.forEach(cssKey => {\n    alignCls[`${prefixCls}-align-${cssKey}`] = props.align === cssKey;\n  });\n  alignCls[`${prefixCls}-align-stretch`] = !props.align && !!props.vertical;\n  return alignCls;\n};\nconst genClsJustify = (prefixCls, props) => {\n  const justifyCls = {};\n  justifyContentValues.forEach(cssKey => {\n    justifyCls[`${prefixCls}-justify-${cssKey}`] = props.justify === cssKey;\n  });\n  return justifyCls;\n};\nfunction createFlexClassNames(prefixCls, props) {\n  return classNames(Object.assign(Object.assign(Object.assign({}, genClsWrap(prefixCls, props)), genClsAlign(prefixCls, props)), genClsJustify(prefixCls, props)));\n}\nexport default createFlexClassNames;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,MAAMC,cAAc,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,CAAC;AAChE,OAAO,MAAMC,oBAAoB,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AAC/K,OAAO,MAAMC,gBAAgB,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,CAAC;AAC/I,MAAMC,UAAU,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK;EACvC,MAAMC,IAAI,GAAGD,KAAK,CAACC,IAAI,KAAK,IAAI,GAAG,MAAM,GAAGD,KAAK,CAACC,IAAI;EACtD,OAAO;IACL,CAAC,GAAGF,SAAS,SAASE,IAAI,EAAE,GAAGA,IAAI,IAAIN,cAAc,CAACO,QAAQ,CAACD,IAAI;EACrE,CAAC;AACH,CAAC;AACD,MAAME,WAAW,GAAGA,CAACJ,SAAS,EAAEC,KAAK,KAAK;EACxC,MAAMI,QAAQ,GAAG,CAAC,CAAC;EACnBP,gBAAgB,CAACQ,OAAO,CAACC,MAAM,IAAI;IACjCF,QAAQ,CAAC,GAAGL,SAAS,UAAUO,MAAM,EAAE,CAAC,GAAGN,KAAK,CAACO,KAAK,KAAKD,MAAM;EACnE,CAAC,CAAC;EACFF,QAAQ,CAAC,GAAGL,SAAS,gBAAgB,CAAC,GAAG,CAACC,KAAK,CAACO,KAAK,IAAI,CAAC,CAACP,KAAK,CAACQ,QAAQ;EACzE,OAAOJ,QAAQ;AACjB,CAAC;AACD,MAAMK,aAAa,GAAGA,CAACV,SAAS,EAAEC,KAAK,KAAK;EAC1C,MAAMU,UAAU,GAAG,CAAC,CAAC;EACrBd,oBAAoB,CAACS,OAAO,CAACC,MAAM,IAAI;IACrCI,UAAU,CAAC,GAAGX,SAAS,YAAYO,MAAM,EAAE,CAAC,GAAGN,KAAK,CAACW,OAAO,KAAKL,MAAM;EACzE,CAAC,CAAC;EACF,OAAOI,UAAU;AACnB,CAAC;AACD,SAASE,oBAAoBA,CAACb,SAAS,EAAEC,KAAK,EAAE;EAC9C,OAAON,UAAU,CAACmB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhB,UAAU,CAACC,SAAS,EAAEC,KAAK,CAAC,CAAC,EAAEG,WAAW,CAACJ,SAAS,EAAEC,KAAK,CAAC,CAAC,EAAES,aAAa,CAACV,SAAS,EAAEC,KAAK,CAAC,CAAC,CAAC;AAClK;AACA,eAAeY,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
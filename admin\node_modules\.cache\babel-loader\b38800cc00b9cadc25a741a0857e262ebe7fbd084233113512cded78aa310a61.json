{"ast": null, "code": "\"use client\";\n\nimport React, { useContext } from 'react';\nimport { PanelPresetsContext } from '../context';\nimport ColorPresets from './ColorPresets';\nconst PanelPresets = () => {\n  const {\n    prefixCls,\n    value,\n    presets,\n    onChange\n  } = useContext(PanelPresetsContext);\n  return Array.isArray(presets) ? (/*#__PURE__*/React.createElement(ColorPresets, {\n    value: value,\n    presets: presets,\n    prefixCls: prefixCls,\n    onChange: onChange\n  })) : null;\n};\nexport default PanelPresets;", "map": {"version": 3, "names": ["React", "useContext", "PanelPresetsContext", "ColorPresets", "PanelPresets", "prefixCls", "value", "presets", "onChange", "Array", "isArray", "createElement"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/color-picker/components/PanelPresets.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport { PanelPresetsContext } from '../context';\nimport ColorPresets from './ColorPresets';\nconst PanelPresets = () => {\n  const {\n    prefixCls,\n    value,\n    presets,\n    onChange\n  } = useContext(PanelPresetsContext);\n  return Array.isArray(presets) ? (/*#__PURE__*/React.createElement(ColorPresets, {\n    value: value,\n    presets: presets,\n    prefixCls: prefixCls,\n    onChange: onChange\n  })) : null;\n};\nexport default PanelPresets;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,mBAAmB,QAAQ,YAAY;AAChD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAM;IACJC,SAAS;IACTC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAGP,UAAU,CAACC,mBAAmB,CAAC;EACnC,OAAOO,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,IAAI,aAAaP,KAAK,CAACW,aAAa,CAACR,YAAY,EAAE;IAC9EG,KAAK,EAAEA,KAAK;IACZC,OAAO,EAAEA,OAAO;IAChBF,SAAS,EAAEA,SAAS;IACpBG,QAAQ,EAAEA;EACZ,CAAC,CAAC,IAAI,IAAI;AACZ,CAAC;AACD,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
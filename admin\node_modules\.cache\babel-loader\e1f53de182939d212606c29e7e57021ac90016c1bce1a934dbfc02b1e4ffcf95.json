{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"children\", \"action\", \"showAction\", \"hideAction\", \"popupVisible\", \"defaultPopupVisible\", \"onPopupVisibleChange\", \"afterPopupVisibleChange\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"focusDelay\", \"blurDelay\", \"mask\", \"maskClosable\", \"getPopupContainer\", \"forceRender\", \"autoDestroy\", \"destroyPopupOnHide\", \"popup\", \"popupClassName\", \"popupStyle\", \"popupPlacement\", \"builtinPlacements\", \"popupAlign\", \"zIndex\", \"stretch\", \"getPopupClassNameFromAlign\", \"fresh\", \"alignPoint\", \"onPopupClick\", \"onPopupAlign\", \"arrow\", \"popupMotion\", \"maskMotion\", \"popupTransitionName\", \"popupAnimation\", \"maskTransitionName\", \"maskAnimation\", \"className\", \"getTriggerDOMNode\"];\nimport Portal from '@rc-component/portal';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useId from \"rc-util/es/hooks/useId\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport * as React from 'react';\nimport Popup from \"./Popup\";\nimport TriggerWrapper from \"./TriggerWrapper\";\nimport TriggerContext from \"./context\";\nimport useAction from \"./hooks/useAction\";\nimport useAlign from \"./hooks/useAlign\";\nimport useWatch from \"./hooks/useWatch\";\nimport useWinClick from \"./hooks/useWinClick\";\nimport { getAlignPopupClassName, getMotion } from \"./util\";\n\n// Removed Props List\n// Seems this can be auto\n// getDocument?: (element?: HTMLElement) => Document;\n\n// New version will not wrap popup with `rc-trigger-popup-content` when multiple children\n\nexport function generateTrigger() {\n  var PortalComponent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Portal;\n  var Trigger = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-trigger-popup' : _props$prefixCls,\n      children = props.children,\n      _props$action = props.action,\n      action = _props$action === void 0 ? 'hover' : _props$action,\n      showAction = props.showAction,\n      hideAction = props.hideAction,\n      popupVisible = props.popupVisible,\n      defaultPopupVisible = props.defaultPopupVisible,\n      onPopupVisibleChange = props.onPopupVisibleChange,\n      afterPopupVisibleChange = props.afterPopupVisibleChange,\n      mouseEnterDelay = props.mouseEnterDelay,\n      _props$mouseLeaveDela = props.mouseLeaveDelay,\n      mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n      focusDelay = props.focusDelay,\n      blurDelay = props.blurDelay,\n      mask = props.mask,\n      _props$maskClosable = props.maskClosable,\n      maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n      getPopupContainer = props.getPopupContainer,\n      forceRender = props.forceRender,\n      autoDestroy = props.autoDestroy,\n      destroyPopupOnHide = props.destroyPopupOnHide,\n      popup = props.popup,\n      popupClassName = props.popupClassName,\n      popupStyle = props.popupStyle,\n      popupPlacement = props.popupPlacement,\n      _props$builtinPlaceme = props.builtinPlacements,\n      builtinPlacements = _props$builtinPlaceme === void 0 ? {} : _props$builtinPlaceme,\n      popupAlign = props.popupAlign,\n      zIndex = props.zIndex,\n      stretch = props.stretch,\n      getPopupClassNameFromAlign = props.getPopupClassNameFromAlign,\n      fresh = props.fresh,\n      alignPoint = props.alignPoint,\n      onPopupClick = props.onPopupClick,\n      onPopupAlign = props.onPopupAlign,\n      arrow = props.arrow,\n      popupMotion = props.popupMotion,\n      maskMotion = props.maskMotion,\n      popupTransitionName = props.popupTransitionName,\n      popupAnimation = props.popupAnimation,\n      maskTransitionName = props.maskTransitionName,\n      maskAnimation = props.maskAnimation,\n      className = props.className,\n      getTriggerDOMNode = props.getTriggerDOMNode,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var mergedAutoDestroy = autoDestroy || destroyPopupOnHide || false;\n\n    // =========================== Mobile ===========================\n    var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      mobile = _React$useState2[0],\n      setMobile = _React$useState2[1];\n    useLayoutEffect(function () {\n      setMobile(isMobile());\n    }, []);\n\n    // ========================== Context ===========================\n    var subPopupElements = React.useRef({});\n    var parentContext = React.useContext(TriggerContext);\n    var context = React.useMemo(function () {\n      return {\n        registerSubPopup: function registerSubPopup(id, subPopupEle) {\n          subPopupElements.current[id] = subPopupEle;\n          parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, subPopupEle);\n        }\n      };\n    }, [parentContext]);\n\n    // =========================== Popup ============================\n    var id = useId();\n    var _React$useState3 = React.useState(null),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      popupEle = _React$useState4[0],\n      setPopupEle = _React$useState4[1];\n\n    // Used for forwardRef popup. Not use internal\n    var externalPopupRef = React.useRef(null);\n    var setPopupRef = useEvent(function (node) {\n      externalPopupRef.current = node;\n      if (isDOM(node) && popupEle !== node) {\n        setPopupEle(node);\n      }\n      parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, node);\n    });\n\n    // =========================== Target ===========================\n    // Use state to control here since `useRef` update not trigger render\n    var _React$useState5 = React.useState(null),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      targetEle = _React$useState6[0],\n      setTargetEle = _React$useState6[1];\n\n    // Used for forwardRef target. Not use internal\n    var externalForwardRef = React.useRef(null);\n    var setTargetRef = useEvent(function (node) {\n      if (isDOM(node) && targetEle !== node) {\n        setTargetEle(node);\n        externalForwardRef.current = node;\n      }\n    });\n\n    // ========================== Children ==========================\n    var child = React.Children.only(children);\n    var originChildProps = (child === null || child === void 0 ? void 0 : child.props) || {};\n    var cloneProps = {};\n    var inPopupOrChild = useEvent(function (ele) {\n      var _getShadowRoot, _getShadowRoot2;\n      var childDOM = targetEle;\n      return (childDOM === null || childDOM === void 0 ? void 0 : childDOM.contains(ele)) || ((_getShadowRoot = getShadowRoot(childDOM)) === null || _getShadowRoot === void 0 ? void 0 : _getShadowRoot.host) === ele || ele === childDOM || (popupEle === null || popupEle === void 0 ? void 0 : popupEle.contains(ele)) || ((_getShadowRoot2 = getShadowRoot(popupEle)) === null || _getShadowRoot2 === void 0 ? void 0 : _getShadowRoot2.host) === ele || ele === popupEle || Object.values(subPopupElements.current).some(function (subPopupEle) {\n        return (subPopupEle === null || subPopupEle === void 0 ? void 0 : subPopupEle.contains(ele)) || ele === subPopupEle;\n      });\n    });\n\n    // =========================== Motion ===========================\n    var mergePopupMotion = getMotion(prefixCls, popupMotion, popupAnimation, popupTransitionName);\n    var mergeMaskMotion = getMotion(prefixCls, maskMotion, maskAnimation, maskTransitionName);\n\n    // ============================ Open ============================\n    var _React$useState7 = React.useState(defaultPopupVisible || false),\n      _React$useState8 = _slicedToArray(_React$useState7, 2),\n      internalOpen = _React$useState8[0],\n      setInternalOpen = _React$useState8[1];\n\n    // Render still use props as first priority\n    var mergedOpen = popupVisible !== null && popupVisible !== void 0 ? popupVisible : internalOpen;\n\n    // We use effect sync here in case `popupVisible` back to `undefined`\n    var setMergedOpen = useEvent(function (nextOpen) {\n      if (popupVisible === undefined) {\n        setInternalOpen(nextOpen);\n      }\n    });\n    useLayoutEffect(function () {\n      setInternalOpen(popupVisible || false);\n    }, [popupVisible]);\n    var openRef = React.useRef(mergedOpen);\n    openRef.current = mergedOpen;\n    var lastTriggerRef = React.useRef([]);\n    lastTriggerRef.current = [];\n    var internalTriggerOpen = useEvent(function (nextOpen) {\n      var _lastTriggerRef$curre;\n      setMergedOpen(nextOpen);\n\n      // Enter or Pointer will both trigger open state change\n      // We only need take one to avoid duplicated change event trigger\n      // Use `lastTriggerRef` to record last open type\n      if (((_lastTriggerRef$curre = lastTriggerRef.current[lastTriggerRef.current.length - 1]) !== null && _lastTriggerRef$curre !== void 0 ? _lastTriggerRef$curre : mergedOpen) !== nextOpen) {\n        lastTriggerRef.current.push(nextOpen);\n        onPopupVisibleChange === null || onPopupVisibleChange === void 0 || onPopupVisibleChange(nextOpen);\n      }\n    });\n\n    // Trigger for delay\n    var delayRef = React.useRef();\n    var clearDelay = function clearDelay() {\n      clearTimeout(delayRef.current);\n    };\n    var triggerOpen = function triggerOpen(nextOpen) {\n      var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      clearDelay();\n      if (delay === 0) {\n        internalTriggerOpen(nextOpen);\n      } else {\n        delayRef.current = setTimeout(function () {\n          internalTriggerOpen(nextOpen);\n        }, delay * 1000);\n      }\n    };\n    React.useEffect(function () {\n      return clearDelay;\n    }, []);\n\n    // ========================== Motion ============================\n    var _React$useState9 = React.useState(false),\n      _React$useState10 = _slicedToArray(_React$useState9, 2),\n      inMotion = _React$useState10[0],\n      setInMotion = _React$useState10[1];\n    useLayoutEffect(function (firstMount) {\n      if (!firstMount || mergedOpen) {\n        setInMotion(true);\n      }\n    }, [mergedOpen]);\n    var _React$useState11 = React.useState(null),\n      _React$useState12 = _slicedToArray(_React$useState11, 2),\n      motionPrepareResolve = _React$useState12[0],\n      setMotionPrepareResolve = _React$useState12[1];\n\n    // =========================== Align ============================\n    var _React$useState13 = React.useState(null),\n      _React$useState14 = _slicedToArray(_React$useState13, 2),\n      mousePos = _React$useState14[0],\n      setMousePos = _React$useState14[1];\n    var setMousePosByEvent = function setMousePosByEvent(event) {\n      setMousePos([event.clientX, event.clientY]);\n    };\n    var _useAlign = useAlign(mergedOpen, popupEle, alignPoint && mousePos !== null ? mousePos : targetEle, popupPlacement, builtinPlacements, popupAlign, onPopupAlign),\n      _useAlign2 = _slicedToArray(_useAlign, 11),\n      ready = _useAlign2[0],\n      offsetX = _useAlign2[1],\n      offsetY = _useAlign2[2],\n      offsetR = _useAlign2[3],\n      offsetB = _useAlign2[4],\n      arrowX = _useAlign2[5],\n      arrowY = _useAlign2[6],\n      scaleX = _useAlign2[7],\n      scaleY = _useAlign2[8],\n      alignInfo = _useAlign2[9],\n      onAlign = _useAlign2[10];\n    var _useAction = useAction(mobile, action, showAction, hideAction),\n      _useAction2 = _slicedToArray(_useAction, 2),\n      showActions = _useAction2[0],\n      hideActions = _useAction2[1];\n    var clickToShow = showActions.has('click');\n    var clickToHide = hideActions.has('click') || hideActions.has('contextMenu');\n    var triggerAlign = useEvent(function () {\n      if (!inMotion) {\n        onAlign();\n      }\n    });\n    var onScroll = function onScroll() {\n      if (openRef.current && alignPoint && clickToHide) {\n        triggerOpen(false);\n      }\n    };\n    useWatch(mergedOpen, targetEle, popupEle, triggerAlign, onScroll);\n    useLayoutEffect(function () {\n      triggerAlign();\n    }, [mousePos, popupPlacement]);\n\n    // When no builtinPlacements and popupAlign changed\n    useLayoutEffect(function () {\n      if (mergedOpen && !(builtinPlacements !== null && builtinPlacements !== void 0 && builtinPlacements[popupPlacement])) {\n        triggerAlign();\n      }\n    }, [JSON.stringify(popupAlign)]);\n    var alignedClassName = React.useMemo(function () {\n      var baseClassName = getAlignPopupClassName(builtinPlacements, prefixCls, alignInfo, alignPoint);\n      return classNames(baseClassName, getPopupClassNameFromAlign === null || getPopupClassNameFromAlign === void 0 ? void 0 : getPopupClassNameFromAlign(alignInfo));\n    }, [alignInfo, getPopupClassNameFromAlign, builtinPlacements, prefixCls, alignPoint]);\n\n    // ============================ Refs ============================\n    React.useImperativeHandle(ref, function () {\n      return {\n        nativeElement: externalForwardRef.current,\n        popupElement: externalPopupRef.current,\n        forceAlign: triggerAlign\n      };\n    });\n\n    // ========================== Stretch ===========================\n    var _React$useState15 = React.useState(0),\n      _React$useState16 = _slicedToArray(_React$useState15, 2),\n      targetWidth = _React$useState16[0],\n      setTargetWidth = _React$useState16[1];\n    var _React$useState17 = React.useState(0),\n      _React$useState18 = _slicedToArray(_React$useState17, 2),\n      targetHeight = _React$useState18[0],\n      setTargetHeight = _React$useState18[1];\n    var syncTargetSize = function syncTargetSize() {\n      if (stretch && targetEle) {\n        var rect = targetEle.getBoundingClientRect();\n        setTargetWidth(rect.width);\n        setTargetHeight(rect.height);\n      }\n    };\n    var onTargetResize = function onTargetResize() {\n      syncTargetSize();\n      triggerAlign();\n    };\n\n    // ========================== Motion ============================\n    var onVisibleChanged = function onVisibleChanged(visible) {\n      setInMotion(false);\n      onAlign();\n      afterPopupVisibleChange === null || afterPopupVisibleChange === void 0 || afterPopupVisibleChange(visible);\n    };\n\n    // We will trigger align when motion is in prepare\n    var onPrepare = function onPrepare() {\n      return new Promise(function (resolve) {\n        syncTargetSize();\n        setMotionPrepareResolve(function () {\n          return resolve;\n        });\n      });\n    };\n    useLayoutEffect(function () {\n      if (motionPrepareResolve) {\n        onAlign();\n        motionPrepareResolve();\n        setMotionPrepareResolve(null);\n      }\n    }, [motionPrepareResolve]);\n\n    // =========================== Action ===========================\n    /**\n     * Util wrapper for trigger action\n     */\n    function wrapperAction(eventName, nextOpen, delay, preEvent) {\n      cloneProps[eventName] = function (event) {\n        var _originChildProps$eve;\n        preEvent === null || preEvent === void 0 || preEvent(event);\n        triggerOpen(nextOpen, delay);\n\n        // Pass to origin\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        (_originChildProps$eve = originChildProps[eventName]) === null || _originChildProps$eve === void 0 || _originChildProps$eve.call.apply(_originChildProps$eve, [originChildProps, event].concat(args));\n      };\n    }\n\n    // ======================= Action: Click ========================\n    if (clickToShow || clickToHide) {\n      cloneProps.onClick = function (event) {\n        var _originChildProps$onC;\n        if (openRef.current && clickToHide) {\n          triggerOpen(false);\n        } else if (!openRef.current && clickToShow) {\n          setMousePosByEvent(event);\n          triggerOpen(true);\n        }\n\n        // Pass to origin\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        (_originChildProps$onC = originChildProps.onClick) === null || _originChildProps$onC === void 0 || _originChildProps$onC.call.apply(_originChildProps$onC, [originChildProps, event].concat(args));\n      };\n    }\n\n    // Click to hide is special action since click popup element should not hide\n    var onPopupPointerDown = useWinClick(mergedOpen, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen);\n\n    // ======================= Action: Hover ========================\n    var hoverToShow = showActions.has('hover');\n    var hoverToHide = hideActions.has('hover');\n    var onPopupMouseEnter;\n    var onPopupMouseLeave;\n    if (hoverToShow) {\n      // Compatible with old browser which not support pointer event\n      wrapperAction('onMouseEnter', true, mouseEnterDelay, function (event) {\n        setMousePosByEvent(event);\n      });\n      wrapperAction('onPointerEnter', true, mouseEnterDelay, function (event) {\n        setMousePosByEvent(event);\n      });\n      onPopupMouseEnter = function onPopupMouseEnter(event) {\n        // Only trigger re-open when popup is visible\n        if ((mergedOpen || inMotion) && popupEle !== null && popupEle !== void 0 && popupEle.contains(event.target)) {\n          triggerOpen(true, mouseEnterDelay);\n        }\n      };\n\n      // Align Point\n      if (alignPoint) {\n        cloneProps.onMouseMove = function (event) {\n          var _originChildProps$onM;\n          // setMousePosByEvent(event);\n          (_originChildProps$onM = originChildProps.onMouseMove) === null || _originChildProps$onM === void 0 || _originChildProps$onM.call(originChildProps, event);\n        };\n      }\n    }\n    if (hoverToHide) {\n      wrapperAction('onMouseLeave', false, mouseLeaveDelay);\n      wrapperAction('onPointerLeave', false, mouseLeaveDelay);\n      onPopupMouseLeave = function onPopupMouseLeave() {\n        triggerOpen(false, mouseLeaveDelay);\n      };\n    }\n\n    // ======================= Action: Focus ========================\n    if (showActions.has('focus')) {\n      wrapperAction('onFocus', true, focusDelay);\n    }\n    if (hideActions.has('focus')) {\n      wrapperAction('onBlur', false, blurDelay);\n    }\n\n    // ==================== Action: ContextMenu =====================\n    if (showActions.has('contextMenu')) {\n      cloneProps.onContextMenu = function (event) {\n        var _originChildProps$onC2;\n        if (openRef.current && hideActions.has('contextMenu')) {\n          triggerOpen(false);\n        } else {\n          setMousePosByEvent(event);\n          triggerOpen(true);\n        }\n        event.preventDefault();\n\n        // Pass to origin\n        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n          args[_key3 - 1] = arguments[_key3];\n        }\n        (_originChildProps$onC2 = originChildProps.onContextMenu) === null || _originChildProps$onC2 === void 0 || _originChildProps$onC2.call.apply(_originChildProps$onC2, [originChildProps, event].concat(args));\n      };\n    }\n\n    // ========================= ClassName ==========================\n    if (className) {\n      cloneProps.className = classNames(originChildProps.className, className);\n    }\n\n    // =========================== Render ===========================\n    var mergedChildrenProps = _objectSpread(_objectSpread({}, originChildProps), cloneProps);\n\n    // Pass props into cloneProps for nest usage\n    var passedProps = {};\n    var passedEventList = ['onContextMenu', 'onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur'];\n    passedEventList.forEach(function (eventName) {\n      if (restProps[eventName]) {\n        passedProps[eventName] = function () {\n          var _mergedChildrenProps$;\n          for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n            args[_key4] = arguments[_key4];\n          }\n          (_mergedChildrenProps$ = mergedChildrenProps[eventName]) === null || _mergedChildrenProps$ === void 0 || _mergedChildrenProps$.call.apply(_mergedChildrenProps$, [mergedChildrenProps].concat(args));\n          restProps[eventName].apply(restProps, args);\n        };\n      }\n    });\n\n    // Child Node\n    var triggerNode = /*#__PURE__*/React.cloneElement(child, _objectSpread(_objectSpread({}, mergedChildrenProps), passedProps));\n    var arrowPos = {\n      x: arrowX,\n      y: arrowY\n    };\n    var innerArrow = arrow ? _objectSpread({}, arrow !== true ? arrow : {}) : null;\n\n    // Render\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ResizeObserver, {\n      disabled: !mergedOpen,\n      ref: setTargetRef,\n      onResize: onTargetResize\n    }, /*#__PURE__*/React.createElement(TriggerWrapper, {\n      getTriggerDOMNode: getTriggerDOMNode\n    }, triggerNode)), /*#__PURE__*/React.createElement(TriggerContext.Provider, {\n      value: context\n    }, /*#__PURE__*/React.createElement(Popup, {\n      portal: PortalComponent,\n      ref: setPopupRef,\n      prefixCls: prefixCls,\n      popup: popup,\n      className: classNames(popupClassName, alignedClassName),\n      style: popupStyle,\n      target: targetEle,\n      onMouseEnter: onPopupMouseEnter,\n      onMouseLeave: onPopupMouseLeave\n      // https://github.com/ant-design/ant-design/issues/43924\n      ,\n\n      onPointerEnter: onPopupMouseEnter,\n      zIndex: zIndex\n      // Open\n      ,\n\n      open: mergedOpen,\n      keepDom: inMotion,\n      fresh: fresh\n      // Click\n      ,\n\n      onClick: onPopupClick,\n      onPointerDownCapture: onPopupPointerDown\n      // Mask\n      ,\n\n      mask: mask\n      // Motion\n      ,\n\n      motion: mergePopupMotion,\n      maskMotion: mergeMaskMotion,\n      onVisibleChanged: onVisibleChanged,\n      onPrepare: onPrepare\n      // Portal\n      ,\n\n      forceRender: forceRender,\n      autoDestroy: mergedAutoDestroy,\n      getPopupContainer: getPopupContainer\n      // Arrow\n      ,\n\n      align: alignInfo,\n      arrow: innerArrow,\n      arrowPos: arrowPos\n      // Align\n      ,\n\n      ready: ready,\n      offsetX: offsetX,\n      offsetY: offsetY,\n      offsetR: offsetR,\n      offsetB: offsetB,\n      onAlign: triggerAlign\n      // Stretch\n      ,\n\n      stretch: stretch,\n      targetWidth: targetWidth / scaleX,\n      targetHeight: targetHeight / scaleY\n    })));\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    Trigger.displayName = 'Trigger';\n  }\n  return Trigger;\n}\nexport default generateTrigger(Portal);", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "Portal", "classNames", "ResizeObserver", "isDOM", "getShadowRoot", "useEvent", "useId", "useLayoutEffect", "isMobile", "React", "Popup", "TriggerWrapper", "TriggerContext", "useAction", "useAlign", "useWatch", "useWinClick", "getAlignPopupClassName", "getMotion", "generateTrigger", "PortalComponent", "arguments", "length", "undefined", "<PERSON><PERSON>", "forwardRef", "props", "ref", "_props$prefixCls", "prefixCls", "children", "_props$action", "action", "showAction", "hideAction", "popupVisible", "defaultPopupVisible", "onPopupVisibleChange", "afterPopupVisibleChange", "mouseEnterDelay", "_props$mouseLeaveDela", "mouseLeaveDelay", "focusDelay", "blurDelay", "mask", "_props$maskClosable", "maskClosable", "getPopupContainer", "forceRender", "autoDestroy", "destroyPopupOnHide", "popup", "popupClassName", "popupStyle", "popupPlacement", "_props$builtinPlaceme", "builtinPlacements", "popupAlign", "zIndex", "stretch", "getPopupClassNameFromAlign", "fresh", "alignPoint", "onPopupClick", "onPopupAlign", "arrow", "popupMotion", "maskMotion", "popupTransitionName", "popupAnimation", "maskTransitionName", "maskAnimation", "className", "getTriggerDOMNode", "restProps", "mergedAutoDestroy", "_React$useState", "useState", "_React$useState2", "mobile", "setMobile", "subPopupElements", "useRef", "parentContext", "useContext", "context", "useMemo", "registerSubPopup", "id", "subPopupEle", "current", "_React$useState3", "_React$useState4", "popup<PERSON>le", "setPopup<PERSON>le", "externalPopupRef", "setPopupRef", "node", "_React$useState5", "_React$useState6", "targetEle", "setTarget<PERSON>le", "externalForwardRef", "setTargetRef", "child", "Children", "only", "originChildProps", "cloneProps", "inPopupOrChild", "ele", "_getShadowRoot", "_getShadowRoot2", "childDOM", "contains", "host", "Object", "values", "some", "mergePopupMotion", "mergeMaskMotion", "_React$useState7", "_React$useState8", "internalOpen", "setInternalOpen", "mergedOpen", "setMergedOpen", "nextOpen", "openRef", "lastTriggerRef", "internalTriggerOpen", "_lastTriggerRef$curre", "push", "delayRef", "clear<PERSON>elay", "clearTimeout", "triggerOpen", "delay", "setTimeout", "useEffect", "_React$useState9", "_React$useState10", "inMotion", "setInMotion", "firstMount", "_React$useState11", "_React$useState12", "motionPrepareResolve", "setMotionPrepareResolve", "_React$useState13", "_React$useState14", "mousePos", "setMousePos", "setMousePosByEvent", "event", "clientX", "clientY", "_useAlign", "_useAlign2", "ready", "offsetX", "offsetY", "offsetR", "offsetB", "arrowX", "arrowY", "scaleX", "scaleY", "alignInfo", "onAlign", "_useAction", "_useAction2", "showActions", "hideActions", "clickToShow", "has", "clickToHide", "triggerAlign", "onScroll", "JSON", "stringify", "alignedClassName", "baseClassName", "useImperativeHandle", "nativeElement", "popupElement", "forceAlign", "_React$useState15", "_React$useState16", "targetWidth", "set<PERSON><PERSON><PERSON><PERSON>id<PERSON>", "_React$useState17", "_React$useState18", "targetHeight", "setTargetHeight", "syncTargetSize", "rect", "getBoundingClientRect", "width", "height", "onTargetResize", "onVisibleChanged", "visible", "onPrepare", "Promise", "resolve", "wrapperAction", "eventName", "preEvent", "_originChildProps$eve", "_len", "args", "Array", "_key", "call", "apply", "concat", "onClick", "_originChildProps$onC", "_len2", "_key2", "onPopupPointerDown", "hoverToShow", "hoverToHide", "onPopupMouseEnter", "onPopupMouseLeave", "target", "onMouseMove", "_originChildProps$onM", "onContextMenu", "_originChildProps$onC2", "preventDefault", "_len3", "_key3", "mergedChildrenProps", "passedProps", "passedEventList", "for<PERSON>ach", "_mergedChildrenProps$", "_len4", "_key4", "triggerNode", "cloneElement", "arrowPos", "x", "y", "innerArrow", "createElement", "Fragment", "disabled", "onResize", "Provider", "value", "portal", "style", "onMouseEnter", "onMouseLeave", "onPointerEnter", "open", "keepDom", "onPointerDownCapture", "motion", "align", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@rc-component/trigger/es/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"children\", \"action\", \"showAction\", \"hideAction\", \"popupVisible\", \"defaultPopupVisible\", \"onPopupVisibleChange\", \"afterPopupVisibleChange\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"focusDelay\", \"blurDelay\", \"mask\", \"maskClosable\", \"getPopupContainer\", \"forceRender\", \"autoDestroy\", \"destroyPopupOnHide\", \"popup\", \"popupClassName\", \"popupStyle\", \"popupPlacement\", \"builtinPlacements\", \"popupAlign\", \"zIndex\", \"stretch\", \"getPopupClassNameFromAlign\", \"fresh\", \"alignPoint\", \"onPopupClick\", \"onPopupAlign\", \"arrow\", \"popupMotion\", \"maskMotion\", \"popupTransitionName\", \"popupAnimation\", \"maskTransitionName\", \"maskAnimation\", \"className\", \"getTriggerDOMNode\"];\nimport Portal from '@rc-component/portal';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useId from \"rc-util/es/hooks/useId\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport * as React from 'react';\nimport Popup from \"./Popup\";\nimport TriggerWrapper from \"./TriggerWrapper\";\nimport TriggerContext from \"./context\";\nimport useAction from \"./hooks/useAction\";\nimport useAlign from \"./hooks/useAlign\";\nimport useWatch from \"./hooks/useWatch\";\nimport useWinClick from \"./hooks/useWinClick\";\nimport { getAlignPopupClassName, getMotion } from \"./util\";\n\n// Removed Props List\n// Seems this can be auto\n// getDocument?: (element?: HTMLElement) => Document;\n\n// New version will not wrap popup with `rc-trigger-popup-content` when multiple children\n\nexport function generateTrigger() {\n  var PortalComponent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Portal;\n  var Trigger = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-trigger-popup' : _props$prefixCls,\n      children = props.children,\n      _props$action = props.action,\n      action = _props$action === void 0 ? 'hover' : _props$action,\n      showAction = props.showAction,\n      hideAction = props.hideAction,\n      popupVisible = props.popupVisible,\n      defaultPopupVisible = props.defaultPopupVisible,\n      onPopupVisibleChange = props.onPopupVisibleChange,\n      afterPopupVisibleChange = props.afterPopupVisibleChange,\n      mouseEnterDelay = props.mouseEnterDelay,\n      _props$mouseLeaveDela = props.mouseLeaveDelay,\n      mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n      focusDelay = props.focusDelay,\n      blurDelay = props.blurDelay,\n      mask = props.mask,\n      _props$maskClosable = props.maskClosable,\n      maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n      getPopupContainer = props.getPopupContainer,\n      forceRender = props.forceRender,\n      autoDestroy = props.autoDestroy,\n      destroyPopupOnHide = props.destroyPopupOnHide,\n      popup = props.popup,\n      popupClassName = props.popupClassName,\n      popupStyle = props.popupStyle,\n      popupPlacement = props.popupPlacement,\n      _props$builtinPlaceme = props.builtinPlacements,\n      builtinPlacements = _props$builtinPlaceme === void 0 ? {} : _props$builtinPlaceme,\n      popupAlign = props.popupAlign,\n      zIndex = props.zIndex,\n      stretch = props.stretch,\n      getPopupClassNameFromAlign = props.getPopupClassNameFromAlign,\n      fresh = props.fresh,\n      alignPoint = props.alignPoint,\n      onPopupClick = props.onPopupClick,\n      onPopupAlign = props.onPopupAlign,\n      arrow = props.arrow,\n      popupMotion = props.popupMotion,\n      maskMotion = props.maskMotion,\n      popupTransitionName = props.popupTransitionName,\n      popupAnimation = props.popupAnimation,\n      maskTransitionName = props.maskTransitionName,\n      maskAnimation = props.maskAnimation,\n      className = props.className,\n      getTriggerDOMNode = props.getTriggerDOMNode,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var mergedAutoDestroy = autoDestroy || destroyPopupOnHide || false;\n\n    // =========================== Mobile ===========================\n    var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      mobile = _React$useState2[0],\n      setMobile = _React$useState2[1];\n    useLayoutEffect(function () {\n      setMobile(isMobile());\n    }, []);\n\n    // ========================== Context ===========================\n    var subPopupElements = React.useRef({});\n    var parentContext = React.useContext(TriggerContext);\n    var context = React.useMemo(function () {\n      return {\n        registerSubPopup: function registerSubPopup(id, subPopupEle) {\n          subPopupElements.current[id] = subPopupEle;\n          parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, subPopupEle);\n        }\n      };\n    }, [parentContext]);\n\n    // =========================== Popup ============================\n    var id = useId();\n    var _React$useState3 = React.useState(null),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      popupEle = _React$useState4[0],\n      setPopupEle = _React$useState4[1];\n\n    // Used for forwardRef popup. Not use internal\n    var externalPopupRef = React.useRef(null);\n    var setPopupRef = useEvent(function (node) {\n      externalPopupRef.current = node;\n      if (isDOM(node) && popupEle !== node) {\n        setPopupEle(node);\n      }\n      parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, node);\n    });\n\n    // =========================== Target ===========================\n    // Use state to control here since `useRef` update not trigger render\n    var _React$useState5 = React.useState(null),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      targetEle = _React$useState6[0],\n      setTargetEle = _React$useState6[1];\n\n    // Used for forwardRef target. Not use internal\n    var externalForwardRef = React.useRef(null);\n    var setTargetRef = useEvent(function (node) {\n      if (isDOM(node) && targetEle !== node) {\n        setTargetEle(node);\n        externalForwardRef.current = node;\n      }\n    });\n\n    // ========================== Children ==========================\n    var child = React.Children.only(children);\n    var originChildProps = (child === null || child === void 0 ? void 0 : child.props) || {};\n    var cloneProps = {};\n    var inPopupOrChild = useEvent(function (ele) {\n      var _getShadowRoot, _getShadowRoot2;\n      var childDOM = targetEle;\n      return (childDOM === null || childDOM === void 0 ? void 0 : childDOM.contains(ele)) || ((_getShadowRoot = getShadowRoot(childDOM)) === null || _getShadowRoot === void 0 ? void 0 : _getShadowRoot.host) === ele || ele === childDOM || (popupEle === null || popupEle === void 0 ? void 0 : popupEle.contains(ele)) || ((_getShadowRoot2 = getShadowRoot(popupEle)) === null || _getShadowRoot2 === void 0 ? void 0 : _getShadowRoot2.host) === ele || ele === popupEle || Object.values(subPopupElements.current).some(function (subPopupEle) {\n        return (subPopupEle === null || subPopupEle === void 0 ? void 0 : subPopupEle.contains(ele)) || ele === subPopupEle;\n      });\n    });\n\n    // =========================== Motion ===========================\n    var mergePopupMotion = getMotion(prefixCls, popupMotion, popupAnimation, popupTransitionName);\n    var mergeMaskMotion = getMotion(prefixCls, maskMotion, maskAnimation, maskTransitionName);\n\n    // ============================ Open ============================\n    var _React$useState7 = React.useState(defaultPopupVisible || false),\n      _React$useState8 = _slicedToArray(_React$useState7, 2),\n      internalOpen = _React$useState8[0],\n      setInternalOpen = _React$useState8[1];\n\n    // Render still use props as first priority\n    var mergedOpen = popupVisible !== null && popupVisible !== void 0 ? popupVisible : internalOpen;\n\n    // We use effect sync here in case `popupVisible` back to `undefined`\n    var setMergedOpen = useEvent(function (nextOpen) {\n      if (popupVisible === undefined) {\n        setInternalOpen(nextOpen);\n      }\n    });\n    useLayoutEffect(function () {\n      setInternalOpen(popupVisible || false);\n    }, [popupVisible]);\n    var openRef = React.useRef(mergedOpen);\n    openRef.current = mergedOpen;\n    var lastTriggerRef = React.useRef([]);\n    lastTriggerRef.current = [];\n    var internalTriggerOpen = useEvent(function (nextOpen) {\n      var _lastTriggerRef$curre;\n      setMergedOpen(nextOpen);\n\n      // Enter or Pointer will both trigger open state change\n      // We only need take one to avoid duplicated change event trigger\n      // Use `lastTriggerRef` to record last open type\n      if (((_lastTriggerRef$curre = lastTriggerRef.current[lastTriggerRef.current.length - 1]) !== null && _lastTriggerRef$curre !== void 0 ? _lastTriggerRef$curre : mergedOpen) !== nextOpen) {\n        lastTriggerRef.current.push(nextOpen);\n        onPopupVisibleChange === null || onPopupVisibleChange === void 0 || onPopupVisibleChange(nextOpen);\n      }\n    });\n\n    // Trigger for delay\n    var delayRef = React.useRef();\n    var clearDelay = function clearDelay() {\n      clearTimeout(delayRef.current);\n    };\n    var triggerOpen = function triggerOpen(nextOpen) {\n      var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      clearDelay();\n      if (delay === 0) {\n        internalTriggerOpen(nextOpen);\n      } else {\n        delayRef.current = setTimeout(function () {\n          internalTriggerOpen(nextOpen);\n        }, delay * 1000);\n      }\n    };\n    React.useEffect(function () {\n      return clearDelay;\n    }, []);\n\n    // ========================== Motion ============================\n    var _React$useState9 = React.useState(false),\n      _React$useState10 = _slicedToArray(_React$useState9, 2),\n      inMotion = _React$useState10[0],\n      setInMotion = _React$useState10[1];\n    useLayoutEffect(function (firstMount) {\n      if (!firstMount || mergedOpen) {\n        setInMotion(true);\n      }\n    }, [mergedOpen]);\n    var _React$useState11 = React.useState(null),\n      _React$useState12 = _slicedToArray(_React$useState11, 2),\n      motionPrepareResolve = _React$useState12[0],\n      setMotionPrepareResolve = _React$useState12[1];\n\n    // =========================== Align ============================\n    var _React$useState13 = React.useState(null),\n      _React$useState14 = _slicedToArray(_React$useState13, 2),\n      mousePos = _React$useState14[0],\n      setMousePos = _React$useState14[1];\n    var setMousePosByEvent = function setMousePosByEvent(event) {\n      setMousePos([event.clientX, event.clientY]);\n    };\n    var _useAlign = useAlign(mergedOpen, popupEle, alignPoint && mousePos !== null ? mousePos : targetEle, popupPlacement, builtinPlacements, popupAlign, onPopupAlign),\n      _useAlign2 = _slicedToArray(_useAlign, 11),\n      ready = _useAlign2[0],\n      offsetX = _useAlign2[1],\n      offsetY = _useAlign2[2],\n      offsetR = _useAlign2[3],\n      offsetB = _useAlign2[4],\n      arrowX = _useAlign2[5],\n      arrowY = _useAlign2[6],\n      scaleX = _useAlign2[7],\n      scaleY = _useAlign2[8],\n      alignInfo = _useAlign2[9],\n      onAlign = _useAlign2[10];\n    var _useAction = useAction(mobile, action, showAction, hideAction),\n      _useAction2 = _slicedToArray(_useAction, 2),\n      showActions = _useAction2[0],\n      hideActions = _useAction2[1];\n    var clickToShow = showActions.has('click');\n    var clickToHide = hideActions.has('click') || hideActions.has('contextMenu');\n    var triggerAlign = useEvent(function () {\n      if (!inMotion) {\n        onAlign();\n      }\n    });\n    var onScroll = function onScroll() {\n      if (openRef.current && alignPoint && clickToHide) {\n        triggerOpen(false);\n      }\n    };\n    useWatch(mergedOpen, targetEle, popupEle, triggerAlign, onScroll);\n    useLayoutEffect(function () {\n      triggerAlign();\n    }, [mousePos, popupPlacement]);\n\n    // When no builtinPlacements and popupAlign changed\n    useLayoutEffect(function () {\n      if (mergedOpen && !(builtinPlacements !== null && builtinPlacements !== void 0 && builtinPlacements[popupPlacement])) {\n        triggerAlign();\n      }\n    }, [JSON.stringify(popupAlign)]);\n    var alignedClassName = React.useMemo(function () {\n      var baseClassName = getAlignPopupClassName(builtinPlacements, prefixCls, alignInfo, alignPoint);\n      return classNames(baseClassName, getPopupClassNameFromAlign === null || getPopupClassNameFromAlign === void 0 ? void 0 : getPopupClassNameFromAlign(alignInfo));\n    }, [alignInfo, getPopupClassNameFromAlign, builtinPlacements, prefixCls, alignPoint]);\n\n    // ============================ Refs ============================\n    React.useImperativeHandle(ref, function () {\n      return {\n        nativeElement: externalForwardRef.current,\n        popupElement: externalPopupRef.current,\n        forceAlign: triggerAlign\n      };\n    });\n\n    // ========================== Stretch ===========================\n    var _React$useState15 = React.useState(0),\n      _React$useState16 = _slicedToArray(_React$useState15, 2),\n      targetWidth = _React$useState16[0],\n      setTargetWidth = _React$useState16[1];\n    var _React$useState17 = React.useState(0),\n      _React$useState18 = _slicedToArray(_React$useState17, 2),\n      targetHeight = _React$useState18[0],\n      setTargetHeight = _React$useState18[1];\n    var syncTargetSize = function syncTargetSize() {\n      if (stretch && targetEle) {\n        var rect = targetEle.getBoundingClientRect();\n        setTargetWidth(rect.width);\n        setTargetHeight(rect.height);\n      }\n    };\n    var onTargetResize = function onTargetResize() {\n      syncTargetSize();\n      triggerAlign();\n    };\n\n    // ========================== Motion ============================\n    var onVisibleChanged = function onVisibleChanged(visible) {\n      setInMotion(false);\n      onAlign();\n      afterPopupVisibleChange === null || afterPopupVisibleChange === void 0 || afterPopupVisibleChange(visible);\n    };\n\n    // We will trigger align when motion is in prepare\n    var onPrepare = function onPrepare() {\n      return new Promise(function (resolve) {\n        syncTargetSize();\n        setMotionPrepareResolve(function () {\n          return resolve;\n        });\n      });\n    };\n    useLayoutEffect(function () {\n      if (motionPrepareResolve) {\n        onAlign();\n        motionPrepareResolve();\n        setMotionPrepareResolve(null);\n      }\n    }, [motionPrepareResolve]);\n\n    // =========================== Action ===========================\n    /**\n     * Util wrapper for trigger action\n     */\n    function wrapperAction(eventName, nextOpen, delay, preEvent) {\n      cloneProps[eventName] = function (event) {\n        var _originChildProps$eve;\n        preEvent === null || preEvent === void 0 || preEvent(event);\n        triggerOpen(nextOpen, delay);\n\n        // Pass to origin\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        (_originChildProps$eve = originChildProps[eventName]) === null || _originChildProps$eve === void 0 || _originChildProps$eve.call.apply(_originChildProps$eve, [originChildProps, event].concat(args));\n      };\n    }\n\n    // ======================= Action: Click ========================\n    if (clickToShow || clickToHide) {\n      cloneProps.onClick = function (event) {\n        var _originChildProps$onC;\n        if (openRef.current && clickToHide) {\n          triggerOpen(false);\n        } else if (!openRef.current && clickToShow) {\n          setMousePosByEvent(event);\n          triggerOpen(true);\n        }\n\n        // Pass to origin\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        (_originChildProps$onC = originChildProps.onClick) === null || _originChildProps$onC === void 0 || _originChildProps$onC.call.apply(_originChildProps$onC, [originChildProps, event].concat(args));\n      };\n    }\n\n    // Click to hide is special action since click popup element should not hide\n    var onPopupPointerDown = useWinClick(mergedOpen, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen);\n\n    // ======================= Action: Hover ========================\n    var hoverToShow = showActions.has('hover');\n    var hoverToHide = hideActions.has('hover');\n    var onPopupMouseEnter;\n    var onPopupMouseLeave;\n    if (hoverToShow) {\n      // Compatible with old browser which not support pointer event\n      wrapperAction('onMouseEnter', true, mouseEnterDelay, function (event) {\n        setMousePosByEvent(event);\n      });\n      wrapperAction('onPointerEnter', true, mouseEnterDelay, function (event) {\n        setMousePosByEvent(event);\n      });\n      onPopupMouseEnter = function onPopupMouseEnter(event) {\n        // Only trigger re-open when popup is visible\n        if ((mergedOpen || inMotion) && popupEle !== null && popupEle !== void 0 && popupEle.contains(event.target)) {\n          triggerOpen(true, mouseEnterDelay);\n        }\n      };\n\n      // Align Point\n      if (alignPoint) {\n        cloneProps.onMouseMove = function (event) {\n          var _originChildProps$onM;\n          // setMousePosByEvent(event);\n          (_originChildProps$onM = originChildProps.onMouseMove) === null || _originChildProps$onM === void 0 || _originChildProps$onM.call(originChildProps, event);\n        };\n      }\n    }\n    if (hoverToHide) {\n      wrapperAction('onMouseLeave', false, mouseLeaveDelay);\n      wrapperAction('onPointerLeave', false, mouseLeaveDelay);\n      onPopupMouseLeave = function onPopupMouseLeave() {\n        triggerOpen(false, mouseLeaveDelay);\n      };\n    }\n\n    // ======================= Action: Focus ========================\n    if (showActions.has('focus')) {\n      wrapperAction('onFocus', true, focusDelay);\n    }\n    if (hideActions.has('focus')) {\n      wrapperAction('onBlur', false, blurDelay);\n    }\n\n    // ==================== Action: ContextMenu =====================\n    if (showActions.has('contextMenu')) {\n      cloneProps.onContextMenu = function (event) {\n        var _originChildProps$onC2;\n        if (openRef.current && hideActions.has('contextMenu')) {\n          triggerOpen(false);\n        } else {\n          setMousePosByEvent(event);\n          triggerOpen(true);\n        }\n        event.preventDefault();\n\n        // Pass to origin\n        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n          args[_key3 - 1] = arguments[_key3];\n        }\n        (_originChildProps$onC2 = originChildProps.onContextMenu) === null || _originChildProps$onC2 === void 0 || _originChildProps$onC2.call.apply(_originChildProps$onC2, [originChildProps, event].concat(args));\n      };\n    }\n\n    // ========================= ClassName ==========================\n    if (className) {\n      cloneProps.className = classNames(originChildProps.className, className);\n    }\n\n    // =========================== Render ===========================\n    var mergedChildrenProps = _objectSpread(_objectSpread({}, originChildProps), cloneProps);\n\n    // Pass props into cloneProps for nest usage\n    var passedProps = {};\n    var passedEventList = ['onContextMenu', 'onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur'];\n    passedEventList.forEach(function (eventName) {\n      if (restProps[eventName]) {\n        passedProps[eventName] = function () {\n          var _mergedChildrenProps$;\n          for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n            args[_key4] = arguments[_key4];\n          }\n          (_mergedChildrenProps$ = mergedChildrenProps[eventName]) === null || _mergedChildrenProps$ === void 0 || _mergedChildrenProps$.call.apply(_mergedChildrenProps$, [mergedChildrenProps].concat(args));\n          restProps[eventName].apply(restProps, args);\n        };\n      }\n    });\n\n    // Child Node\n    var triggerNode = /*#__PURE__*/React.cloneElement(child, _objectSpread(_objectSpread({}, mergedChildrenProps), passedProps));\n    var arrowPos = {\n      x: arrowX,\n      y: arrowY\n    };\n    var innerArrow = arrow ? _objectSpread({}, arrow !== true ? arrow : {}) : null;\n\n    // Render\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ResizeObserver, {\n      disabled: !mergedOpen,\n      ref: setTargetRef,\n      onResize: onTargetResize\n    }, /*#__PURE__*/React.createElement(TriggerWrapper, {\n      getTriggerDOMNode: getTriggerDOMNode\n    }, triggerNode)), /*#__PURE__*/React.createElement(TriggerContext.Provider, {\n      value: context\n    }, /*#__PURE__*/React.createElement(Popup, {\n      portal: PortalComponent,\n      ref: setPopupRef,\n      prefixCls: prefixCls,\n      popup: popup,\n      className: classNames(popupClassName, alignedClassName),\n      style: popupStyle,\n      target: targetEle,\n      onMouseEnter: onPopupMouseEnter,\n      onMouseLeave: onPopupMouseLeave\n      // https://github.com/ant-design/ant-design/issues/43924\n      ,\n      onPointerEnter: onPopupMouseEnter,\n      zIndex: zIndex\n      // Open\n      ,\n      open: mergedOpen,\n      keepDom: inMotion,\n      fresh: fresh\n      // Click\n      ,\n      onClick: onPopupClick,\n      onPointerDownCapture: onPopupPointerDown\n      // Mask\n      ,\n      mask: mask\n      // Motion\n      ,\n      motion: mergePopupMotion,\n      maskMotion: mergeMaskMotion,\n      onVisibleChanged: onVisibleChanged,\n      onPrepare: onPrepare\n      // Portal\n      ,\n      forceRender: forceRender,\n      autoDestroy: mergedAutoDestroy,\n      getPopupContainer: getPopupContainer\n      // Arrow\n      ,\n      align: alignInfo,\n      arrow: innerArrow,\n      arrowPos: arrowPos\n      // Align\n      ,\n      ready: ready,\n      offsetX: offsetX,\n      offsetY: offsetY,\n      offsetR: offsetR,\n      offsetB: offsetB,\n      onAlign: triggerAlign\n      // Stretch\n      ,\n      stretch: stretch,\n      targetWidth: targetWidth / scaleX,\n      targetHeight: targetHeight / scaleY\n    })));\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    Trigger.displayName = 'Trigger';\n  }\n  return Trigger;\n}\nexport default generateTrigger(Portal);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,aAAa,EAAE,aAAa,EAAE,oBAAoB,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,4BAA4B,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,eAAe,EAAE,WAAW,EAAE,mBAAmB,CAAC;AAC3qB,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,sBAAsB,EAAEC,SAAS,QAAQ,QAAQ;;AAE1D;AACA;AACA;;AAEA;;AAEA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,IAAIC,eAAe,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGrB,MAAM;EAChG,IAAIwB,OAAO,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAChE,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;MACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,kBAAkB,GAAGA,gBAAgB;MAC/EE,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;MACzBC,aAAa,GAAGL,KAAK,CAACM,MAAM;MAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,aAAa;MAC3DE,UAAU,GAAGP,KAAK,CAACO,UAAU;MAC7BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;MAC7BC,YAAY,GAAGT,KAAK,CAACS,YAAY;MACjCC,mBAAmB,GAAGV,KAAK,CAACU,mBAAmB;MAC/CC,oBAAoB,GAAGX,KAAK,CAACW,oBAAoB;MACjDC,uBAAuB,GAAGZ,KAAK,CAACY,uBAAuB;MACvDC,eAAe,GAAGb,KAAK,CAACa,eAAe;MACvCC,qBAAqB,GAAGd,KAAK,CAACe,eAAe;MAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;MAChFE,UAAU,GAAGhB,KAAK,CAACgB,UAAU;MAC7BC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;MAC3BC,IAAI,GAAGlB,KAAK,CAACkB,IAAI;MACjBC,mBAAmB,GAAGnB,KAAK,CAACoB,YAAY;MACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;MAC1EE,iBAAiB,GAAGrB,KAAK,CAACqB,iBAAiB;MAC3CC,WAAW,GAAGtB,KAAK,CAACsB,WAAW;MAC/BC,WAAW,GAAGvB,KAAK,CAACuB,WAAW;MAC/BC,kBAAkB,GAAGxB,KAAK,CAACwB,kBAAkB;MAC7CC,KAAK,GAAGzB,KAAK,CAACyB,KAAK;MACnBC,cAAc,GAAG1B,KAAK,CAAC0B,cAAc;MACrCC,UAAU,GAAG3B,KAAK,CAAC2B,UAAU;MAC7BC,cAAc,GAAG5B,KAAK,CAAC4B,cAAc;MACrCC,qBAAqB,GAAG7B,KAAK,CAAC8B,iBAAiB;MAC/CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;MACjFE,UAAU,GAAG/B,KAAK,CAAC+B,UAAU;MAC7BC,MAAM,GAAGhC,KAAK,CAACgC,MAAM;MACrBC,OAAO,GAAGjC,KAAK,CAACiC,OAAO;MACvBC,0BAA0B,GAAGlC,KAAK,CAACkC,0BAA0B;MAC7DC,KAAK,GAAGnC,KAAK,CAACmC,KAAK;MACnBC,UAAU,GAAGpC,KAAK,CAACoC,UAAU;MAC7BC,YAAY,GAAGrC,KAAK,CAACqC,YAAY;MACjCC,YAAY,GAAGtC,KAAK,CAACsC,YAAY;MACjCC,KAAK,GAAGvC,KAAK,CAACuC,KAAK;MACnBC,WAAW,GAAGxC,KAAK,CAACwC,WAAW;MAC/BC,UAAU,GAAGzC,KAAK,CAACyC,UAAU;MAC7BC,mBAAmB,GAAG1C,KAAK,CAAC0C,mBAAmB;MAC/CC,cAAc,GAAG3C,KAAK,CAAC2C,cAAc;MACrCC,kBAAkB,GAAG5C,KAAK,CAAC4C,kBAAkB;MAC7CC,aAAa,GAAG7C,KAAK,CAAC6C,aAAa;MACnCC,SAAS,GAAG9C,KAAK,CAAC8C,SAAS;MAC3BC,iBAAiB,GAAG/C,KAAK,CAAC+C,iBAAiB;MAC3CC,SAAS,GAAG5E,wBAAwB,CAAC4B,KAAK,EAAE3B,SAAS,CAAC;IACxD,IAAI4E,iBAAiB,GAAG1B,WAAW,IAAIC,kBAAkB,IAAI,KAAK;;IAElE;IACA,IAAI0B,eAAe,GAAGnE,KAAK,CAACoE,QAAQ,CAAC,KAAK,CAAC;MACzCC,gBAAgB,GAAGjF,cAAc,CAAC+E,eAAe,EAAE,CAAC,CAAC;MACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACjCvE,eAAe,CAAC,YAAY;MAC1ByE,SAAS,CAACxE,QAAQ,CAAC,CAAC,CAAC;IACvB,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,IAAIyE,gBAAgB,GAAGxE,KAAK,CAACyE,MAAM,CAAC,CAAC,CAAC,CAAC;IACvC,IAAIC,aAAa,GAAG1E,KAAK,CAAC2E,UAAU,CAACxE,cAAc,CAAC;IACpD,IAAIyE,OAAO,GAAG5E,KAAK,CAAC6E,OAAO,CAAC,YAAY;MACtC,OAAO;QACLC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,EAAE,EAAEC,WAAW,EAAE;UAC3DR,gBAAgB,CAACS,OAAO,CAACF,EAAE,CAAC,GAAGC,WAAW;UAC1CN,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAACI,gBAAgB,CAACC,EAAE,EAAEC,WAAW,CAAC;QACvG;MACF,CAAC;IACH,CAAC,EAAE,CAACN,aAAa,CAAC,CAAC;;IAEnB;IACA,IAAIK,EAAE,GAAGlF,KAAK,CAAC,CAAC;IAChB,IAAIqF,gBAAgB,GAAGlF,KAAK,CAACoE,QAAQ,CAAC,IAAI,CAAC;MACzCe,gBAAgB,GAAG/F,cAAc,CAAC8F,gBAAgB,EAAE,CAAC,CAAC;MACtDE,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;IAEnC;IACA,IAAIG,gBAAgB,GAAGtF,KAAK,CAACyE,MAAM,CAAC,IAAI,CAAC;IACzC,IAAIc,WAAW,GAAG3F,QAAQ,CAAC,UAAU4F,IAAI,EAAE;MACzCF,gBAAgB,CAACL,OAAO,GAAGO,IAAI;MAC/B,IAAI9F,KAAK,CAAC8F,IAAI,CAAC,IAAIJ,QAAQ,KAAKI,IAAI,EAAE;QACpCH,WAAW,CAACG,IAAI,CAAC;MACnB;MACAd,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAACI,gBAAgB,CAACC,EAAE,EAAES,IAAI,CAAC;IAChG,CAAC,CAAC;;IAEF;IACA;IACA,IAAIC,gBAAgB,GAAGzF,KAAK,CAACoE,QAAQ,CAAC,IAAI,CAAC;MACzCsB,gBAAgB,GAAGtG,cAAc,CAACqG,gBAAgB,EAAE,CAAC,CAAC;MACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;IAEpC;IACA,IAAIG,kBAAkB,GAAG7F,KAAK,CAACyE,MAAM,CAAC,IAAI,CAAC;IAC3C,IAAIqB,YAAY,GAAGlG,QAAQ,CAAC,UAAU4F,IAAI,EAAE;MAC1C,IAAI9F,KAAK,CAAC8F,IAAI,CAAC,IAAIG,SAAS,KAAKH,IAAI,EAAE;QACrCI,YAAY,CAACJ,IAAI,CAAC;QAClBK,kBAAkB,CAACZ,OAAO,GAAGO,IAAI;MACnC;IACF,CAAC,CAAC;;IAEF;IACA,IAAIO,KAAK,GAAG/F,KAAK,CAACgG,QAAQ,CAACC,IAAI,CAAC5E,QAAQ,CAAC;IACzC,IAAI6E,gBAAgB,GAAG,CAACH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC9E,KAAK,KAAK,CAAC,CAAC;IACxF,IAAIkF,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIC,cAAc,GAAGxG,QAAQ,CAAC,UAAUyG,GAAG,EAAE;MAC3C,IAAIC,cAAc,EAAEC,eAAe;MACnC,IAAIC,QAAQ,GAAGb,SAAS;MACxB,OAAO,CAACa,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,QAAQ,CAACJ,GAAG,CAAC,KAAK,CAAC,CAACC,cAAc,GAAG3G,aAAa,CAAC6G,QAAQ,CAAC,MAAM,IAAI,IAAIF,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,IAAI,MAAML,GAAG,IAAIA,GAAG,KAAKG,QAAQ,KAAKpB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACqB,QAAQ,CAACJ,GAAG,CAAC,CAAC,IAAI,CAAC,CAACE,eAAe,GAAG5G,aAAa,CAACyF,QAAQ,CAAC,MAAM,IAAI,IAAImB,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACG,IAAI,MAAML,GAAG,IAAIA,GAAG,KAAKjB,QAAQ,IAAIuB,MAAM,CAACC,MAAM,CAACpC,gBAAgB,CAACS,OAAO,CAAC,CAAC4B,IAAI,CAAC,UAAU7B,WAAW,EAAE;QAC9gB,OAAO,CAACA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACyB,QAAQ,CAACJ,GAAG,CAAC,KAAKA,GAAG,KAAKrB,WAAW;MACrH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,IAAI8B,gBAAgB,GAAGrG,SAAS,CAACW,SAAS,EAAEqC,WAAW,EAAEG,cAAc,EAAED,mBAAmB,CAAC;IAC7F,IAAIoD,eAAe,GAAGtG,SAAS,CAACW,SAAS,EAAEsC,UAAU,EAAEI,aAAa,EAAED,kBAAkB,CAAC;;IAEzF;IACA,IAAImD,gBAAgB,GAAGhH,KAAK,CAACoE,QAAQ,CAACzC,mBAAmB,IAAI,KAAK,CAAC;MACjEsF,gBAAgB,GAAG7H,cAAc,CAAC4H,gBAAgB,EAAE,CAAC,CAAC;MACtDE,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;IAEvC;IACA,IAAIG,UAAU,GAAG1F,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGwF,YAAY;;IAE/F;IACA,IAAIG,aAAa,GAAGzH,QAAQ,CAAC,UAAU0H,QAAQ,EAAE;MAC/C,IAAI5F,YAAY,KAAKZ,SAAS,EAAE;QAC9BqG,eAAe,CAACG,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC;IACFxH,eAAe,CAAC,YAAY;MAC1BqH,eAAe,CAACzF,YAAY,IAAI,KAAK,CAAC;IACxC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;IAClB,IAAI6F,OAAO,GAAGvH,KAAK,CAACyE,MAAM,CAAC2C,UAAU,CAAC;IACtCG,OAAO,CAACtC,OAAO,GAAGmC,UAAU;IAC5B,IAAII,cAAc,GAAGxH,KAAK,CAACyE,MAAM,CAAC,EAAE,CAAC;IACrC+C,cAAc,CAACvC,OAAO,GAAG,EAAE;IAC3B,IAAIwC,mBAAmB,GAAG7H,QAAQ,CAAC,UAAU0H,QAAQ,EAAE;MACrD,IAAII,qBAAqB;MACzBL,aAAa,CAACC,QAAQ,CAAC;;MAEvB;MACA;MACA;MACA,IAAI,CAAC,CAACI,qBAAqB,GAAGF,cAAc,CAACvC,OAAO,CAACuC,cAAc,CAACvC,OAAO,CAACpE,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAI6G,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGN,UAAU,MAAME,QAAQ,EAAE;QACxLE,cAAc,CAACvC,OAAO,CAAC0C,IAAI,CAACL,QAAQ,CAAC;QACrC1F,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,IAAIA,oBAAoB,CAAC0F,QAAQ,CAAC;MACpG;IACF,CAAC,CAAC;;IAEF;IACA,IAAIM,QAAQ,GAAG5H,KAAK,CAACyE,MAAM,CAAC,CAAC;IAC7B,IAAIoD,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;MACrCC,YAAY,CAACF,QAAQ,CAAC3C,OAAO,CAAC;IAChC,CAAC;IACD,IAAI8C,WAAW,GAAG,SAASA,WAAWA,CAACT,QAAQ,EAAE;MAC/C,IAAIU,KAAK,GAAGpH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MACjFiH,UAAU,CAAC,CAAC;MACZ,IAAIG,KAAK,KAAK,CAAC,EAAE;QACfP,mBAAmB,CAACH,QAAQ,CAAC;MAC/B,CAAC,MAAM;QACLM,QAAQ,CAAC3C,OAAO,GAAGgD,UAAU,CAAC,YAAY;UACxCR,mBAAmB,CAACH,QAAQ,CAAC;QAC/B,CAAC,EAAEU,KAAK,GAAG,IAAI,CAAC;MAClB;IACF,CAAC;IACDhI,KAAK,CAACkI,SAAS,CAAC,YAAY;MAC1B,OAAOL,UAAU;IACnB,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,IAAIM,gBAAgB,GAAGnI,KAAK,CAACoE,QAAQ,CAAC,KAAK,CAAC;MAC1CgE,iBAAiB,GAAGhJ,cAAc,CAAC+I,gBAAgB,EAAE,CAAC,CAAC;MACvDE,QAAQ,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAC/BE,WAAW,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACpCtI,eAAe,CAAC,UAAUyI,UAAU,EAAE;MACpC,IAAI,CAACA,UAAU,IAAInB,UAAU,EAAE;QAC7BkB,WAAW,CAAC,IAAI,CAAC;MACnB;IACF,CAAC,EAAE,CAAClB,UAAU,CAAC,CAAC;IAChB,IAAIoB,iBAAiB,GAAGxI,KAAK,CAACoE,QAAQ,CAAC,IAAI,CAAC;MAC1CqE,iBAAiB,GAAGrJ,cAAc,CAACoJ,iBAAiB,EAAE,CAAC,CAAC;MACxDE,oBAAoB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAC3CE,uBAAuB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;;IAEhD;IACA,IAAIG,iBAAiB,GAAG5I,KAAK,CAACoE,QAAQ,CAAC,IAAI,CAAC;MAC1CyE,iBAAiB,GAAGzJ,cAAc,CAACwJ,iBAAiB,EAAE,CAAC,CAAC;MACxDE,QAAQ,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAC/BE,WAAW,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACpC,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAE;MAC1DF,WAAW,CAAC,CAACE,KAAK,CAACC,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC;IAC7C,CAAC;IACD,IAAIC,SAAS,GAAG/I,QAAQ,CAAC+G,UAAU,EAAEhC,QAAQ,EAAE/B,UAAU,IAAIyF,QAAQ,KAAK,IAAI,GAAGA,QAAQ,GAAGnD,SAAS,EAAE9C,cAAc,EAAEE,iBAAiB,EAAEC,UAAU,EAAEO,YAAY,CAAC;MACjK8F,UAAU,GAAGjK,cAAc,CAACgK,SAAS,EAAE,EAAE,CAAC;MAC1CE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;MACrBE,OAAO,GAAGF,UAAU,CAAC,CAAC,CAAC;MACvBG,OAAO,GAAGH,UAAU,CAAC,CAAC,CAAC;MACvBI,OAAO,GAAGJ,UAAU,CAAC,CAAC,CAAC;MACvBK,OAAO,GAAGL,UAAU,CAAC,CAAC,CAAC;MACvBM,MAAM,GAAGN,UAAU,CAAC,CAAC,CAAC;MACtBO,MAAM,GAAGP,UAAU,CAAC,CAAC,CAAC;MACtBQ,MAAM,GAAGR,UAAU,CAAC,CAAC,CAAC;MACtBS,MAAM,GAAGT,UAAU,CAAC,CAAC,CAAC;MACtBU,SAAS,GAAGV,UAAU,CAAC,CAAC,CAAC;MACzBW,OAAO,GAAGX,UAAU,CAAC,EAAE,CAAC;IAC1B,IAAIY,UAAU,GAAG7J,SAAS,CAACkE,MAAM,EAAE/C,MAAM,EAAEC,UAAU,EAAEC,UAAU,CAAC;MAChEyI,WAAW,GAAG9K,cAAc,CAAC6K,UAAU,EAAE,CAAC,CAAC;MAC3CE,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;MAC5BE,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC9B,IAAIG,WAAW,GAAGF,WAAW,CAACG,GAAG,CAAC,OAAO,CAAC;IAC1C,IAAIC,WAAW,GAAGH,WAAW,CAACE,GAAG,CAAC,OAAO,CAAC,IAAIF,WAAW,CAACE,GAAG,CAAC,aAAa,CAAC;IAC5E,IAAIE,YAAY,GAAG5K,QAAQ,CAAC,YAAY;MACtC,IAAI,CAACyI,QAAQ,EAAE;QACb2B,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;IACF,IAAIS,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;MACjC,IAAIlD,OAAO,CAACtC,OAAO,IAAI5B,UAAU,IAAIkH,WAAW,EAAE;QAChDxC,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC;IACDzH,QAAQ,CAAC8G,UAAU,EAAEzB,SAAS,EAAEP,QAAQ,EAAEoF,YAAY,EAAEC,QAAQ,CAAC;IACjE3K,eAAe,CAAC,YAAY;MAC1B0K,YAAY,CAAC,CAAC;IAChB,CAAC,EAAE,CAAC1B,QAAQ,EAAEjG,cAAc,CAAC,CAAC;;IAE9B;IACA/C,eAAe,CAAC,YAAY;MAC1B,IAAIsH,UAAU,IAAI,EAAErE,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACF,cAAc,CAAC,CAAC,EAAE;QACpH2H,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,EAAE,CAACE,IAAI,CAACC,SAAS,CAAC3H,UAAU,CAAC,CAAC,CAAC;IAChC,IAAI4H,gBAAgB,GAAG5K,KAAK,CAAC6E,OAAO,CAAC,YAAY;MAC/C,IAAIgG,aAAa,GAAGrK,sBAAsB,CAACuC,iBAAiB,EAAE3B,SAAS,EAAE2I,SAAS,EAAE1G,UAAU,CAAC;MAC/F,OAAO7D,UAAU,CAACqL,aAAa,EAAE1H,0BAA0B,KAAK,IAAI,IAAIA,0BAA0B,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,0BAA0B,CAAC4G,SAAS,CAAC,CAAC;IACjK,CAAC,EAAE,CAACA,SAAS,EAAE5G,0BAA0B,EAAEJ,iBAAiB,EAAE3B,SAAS,EAAEiC,UAAU,CAAC,CAAC;;IAErF;IACArD,KAAK,CAAC8K,mBAAmB,CAAC5J,GAAG,EAAE,YAAY;MACzC,OAAO;QACL6J,aAAa,EAAElF,kBAAkB,CAACZ,OAAO;QACzC+F,YAAY,EAAE1F,gBAAgB,CAACL,OAAO;QACtCgG,UAAU,EAAET;MACd,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,IAAIU,iBAAiB,GAAGlL,KAAK,CAACoE,QAAQ,CAAC,CAAC,CAAC;MACvC+G,iBAAiB,GAAG/L,cAAc,CAAC8L,iBAAiB,EAAE,CAAC,CAAC;MACxDE,WAAW,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAClCE,cAAc,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACvC,IAAIG,iBAAiB,GAAGtL,KAAK,CAACoE,QAAQ,CAAC,CAAC,CAAC;MACvCmH,iBAAiB,GAAGnM,cAAc,CAACkM,iBAAiB,EAAE,CAAC,CAAC;MACxDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MACnCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACxC,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;MAC7C,IAAIxI,OAAO,IAAIyC,SAAS,EAAE;QACxB,IAAIgG,IAAI,GAAGhG,SAAS,CAACiG,qBAAqB,CAAC,CAAC;QAC5CP,cAAc,CAACM,IAAI,CAACE,KAAK,CAAC;QAC1BJ,eAAe,CAACE,IAAI,CAACG,MAAM,CAAC;MAC9B;IACF,CAAC;IACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;MAC7CL,cAAc,CAAC,CAAC;MAChBlB,YAAY,CAAC,CAAC;IAChB,CAAC;;IAED;IACA,IAAIwB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,OAAO,EAAE;MACxD3D,WAAW,CAAC,KAAK,CAAC;MAClB0B,OAAO,CAAC,CAAC;MACTnI,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,IAAIA,uBAAuB,CAACoK,OAAO,CAAC;IAC5G,CAAC;;IAED;IACA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;MACnC,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;QACpCV,cAAc,CAAC,CAAC;QAChB/C,uBAAuB,CAAC,YAAY;UAClC,OAAOyD,OAAO;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDtM,eAAe,CAAC,YAAY;MAC1B,IAAI4I,oBAAoB,EAAE;QACxBsB,OAAO,CAAC,CAAC;QACTtB,oBAAoB,CAAC,CAAC;QACtBC,uBAAuB,CAAC,IAAI,CAAC;MAC/B;IACF,CAAC,EAAE,CAACD,oBAAoB,CAAC,CAAC;;IAE1B;IACA;AACJ;AACA;IACI,SAAS2D,aAAaA,CAACC,SAAS,EAAEhF,QAAQ,EAAEU,KAAK,EAAEuE,QAAQ,EAAE;MAC3DpG,UAAU,CAACmG,SAAS,CAAC,GAAG,UAAUrD,KAAK,EAAE;QACvC,IAAIuD,qBAAqB;QACzBD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACtD,KAAK,CAAC;QAC3DlB,WAAW,CAACT,QAAQ,EAAEU,KAAK,CAAC;;QAE5B;QACA,KAAK,IAAIyE,IAAI,GAAG7L,SAAS,CAACC,MAAM,EAAE6L,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;UAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGhM,SAAS,CAACgM,IAAI,CAAC;QAClC;QACA,CAACJ,qBAAqB,GAAGtG,gBAAgB,CAACoG,SAAS,CAAC,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACK,IAAI,CAACC,KAAK,CAACN,qBAAqB,EAAE,CAACtG,gBAAgB,EAAE+C,KAAK,CAAC,CAAC8D,MAAM,CAACL,IAAI,CAAC,CAAC;MACvM,CAAC;IACH;;IAEA;IACA,IAAIrC,WAAW,IAAIE,WAAW,EAAE;MAC9BpE,UAAU,CAAC6G,OAAO,GAAG,UAAU/D,KAAK,EAAE;QACpC,IAAIgE,qBAAqB;QACzB,IAAI1F,OAAO,CAACtC,OAAO,IAAIsF,WAAW,EAAE;UAClCxC,WAAW,CAAC,KAAK,CAAC;QACpB,CAAC,MAAM,IAAI,CAACR,OAAO,CAACtC,OAAO,IAAIoF,WAAW,EAAE;UAC1CrB,kBAAkB,CAACC,KAAK,CAAC;UACzBlB,WAAW,CAAC,IAAI,CAAC;QACnB;;QAEA;QACA,KAAK,IAAImF,KAAK,GAAGtM,SAAS,CAACC,MAAM,EAAE6L,IAAI,GAAG,IAAIC,KAAK,CAACO,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UACjHT,IAAI,CAACS,KAAK,GAAG,CAAC,CAAC,GAAGvM,SAAS,CAACuM,KAAK,CAAC;QACpC;QACA,CAACF,qBAAqB,GAAG/G,gBAAgB,CAAC8G,OAAO,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACJ,IAAI,CAACC,KAAK,CAACG,qBAAqB,EAAE,CAAC/G,gBAAgB,EAAE+C,KAAK,CAAC,CAAC8D,MAAM,CAACL,IAAI,CAAC,CAAC;MACpM,CAAC;IACH;;IAEA;IACA,IAAIU,kBAAkB,GAAG7M,WAAW,CAAC6G,UAAU,EAAEmD,WAAW,EAAE5E,SAAS,EAAEP,QAAQ,EAAEjD,IAAI,EAAEE,YAAY,EAAE+D,cAAc,EAAE2B,WAAW,CAAC;;IAEnI;IACA,IAAIsF,WAAW,GAAGlD,WAAW,CAACG,GAAG,CAAC,OAAO,CAAC;IAC1C,IAAIgD,WAAW,GAAGlD,WAAW,CAACE,GAAG,CAAC,OAAO,CAAC;IAC1C,IAAIiD,iBAAiB;IACrB,IAAIC,iBAAiB;IACrB,IAAIH,WAAW,EAAE;MACf;MACAhB,aAAa,CAAC,cAAc,EAAE,IAAI,EAAEvK,eAAe,EAAE,UAAUmH,KAAK,EAAE;QACpED,kBAAkB,CAACC,KAAK,CAAC;MAC3B,CAAC,CAAC;MACFoD,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAEvK,eAAe,EAAE,UAAUmH,KAAK,EAAE;QACtED,kBAAkB,CAACC,KAAK,CAAC;MAC3B,CAAC,CAAC;MACFsE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACtE,KAAK,EAAE;QACpD;QACA,IAAI,CAAC7B,UAAU,IAAIiB,QAAQ,KAAKjD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACqB,QAAQ,CAACwC,KAAK,CAACwE,MAAM,CAAC,EAAE;UAC3G1F,WAAW,CAAC,IAAI,EAAEjG,eAAe,CAAC;QACpC;MACF,CAAC;;MAED;MACA,IAAIuB,UAAU,EAAE;QACd8C,UAAU,CAACuH,WAAW,GAAG,UAAUzE,KAAK,EAAE;UACxC,IAAI0E,qBAAqB;UACzB;UACA,CAACA,qBAAqB,GAAGzH,gBAAgB,CAACwH,WAAW,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACd,IAAI,CAAC3G,gBAAgB,EAAE+C,KAAK,CAAC;QAC5J,CAAC;MACH;IACF;IACA,IAAIqE,WAAW,EAAE;MACfjB,aAAa,CAAC,cAAc,EAAE,KAAK,EAAErK,eAAe,CAAC;MACrDqK,aAAa,CAAC,gBAAgB,EAAE,KAAK,EAAErK,eAAe,CAAC;MACvDwL,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;QAC/CzF,WAAW,CAAC,KAAK,EAAE/F,eAAe,CAAC;MACrC,CAAC;IACH;;IAEA;IACA,IAAImI,WAAW,CAACG,GAAG,CAAC,OAAO,CAAC,EAAE;MAC5B+B,aAAa,CAAC,SAAS,EAAE,IAAI,EAAEpK,UAAU,CAAC;IAC5C;IACA,IAAImI,WAAW,CAACE,GAAG,CAAC,OAAO,CAAC,EAAE;MAC5B+B,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAEnK,SAAS,CAAC;IAC3C;;IAEA;IACA,IAAIiI,WAAW,CAACG,GAAG,CAAC,aAAa,CAAC,EAAE;MAClCnE,UAAU,CAACyH,aAAa,GAAG,UAAU3E,KAAK,EAAE;QAC1C,IAAI4E,sBAAsB;QAC1B,IAAItG,OAAO,CAACtC,OAAO,IAAImF,WAAW,CAACE,GAAG,CAAC,aAAa,CAAC,EAAE;UACrDvC,WAAW,CAAC,KAAK,CAAC;QACpB,CAAC,MAAM;UACLiB,kBAAkB,CAACC,KAAK,CAAC;UACzBlB,WAAW,CAAC,IAAI,CAAC;QACnB;QACAkB,KAAK,CAAC6E,cAAc,CAAC,CAAC;;QAEtB;QACA,KAAK,IAAIC,KAAK,GAAGnN,SAAS,CAACC,MAAM,EAAE6L,IAAI,GAAG,IAAIC,KAAK,CAACoB,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UACjHtB,IAAI,CAACsB,KAAK,GAAG,CAAC,CAAC,GAAGpN,SAAS,CAACoN,KAAK,CAAC;QACpC;QACA,CAACH,sBAAsB,GAAG3H,gBAAgB,CAAC0H,aAAa,MAAM,IAAI,IAAIC,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAAChB,IAAI,CAACC,KAAK,CAACe,sBAAsB,EAAE,CAAC3H,gBAAgB,EAAE+C,KAAK,CAAC,CAAC8D,MAAM,CAACL,IAAI,CAAC,CAAC;MAC9M,CAAC;IACH;;IAEA;IACA,IAAI3I,SAAS,EAAE;MACboC,UAAU,CAACpC,SAAS,GAAGvE,UAAU,CAAC0G,gBAAgB,CAACnC,SAAS,EAAEA,SAAS,CAAC;IAC1E;;IAEA;IACA,IAAIkK,mBAAmB,GAAG9O,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+G,gBAAgB,CAAC,EAAEC,UAAU,CAAC;;IAExF;IACA,IAAI+H,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIC,eAAe,GAAG,CAAC,eAAe,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,CAAC;IACtIA,eAAe,CAACC,OAAO,CAAC,UAAU9B,SAAS,EAAE;MAC3C,IAAIrI,SAAS,CAACqI,SAAS,CAAC,EAAE;QACxB4B,WAAW,CAAC5B,SAAS,CAAC,GAAG,YAAY;UACnC,IAAI+B,qBAAqB;UACzB,KAAK,IAAIC,KAAK,GAAG1N,SAAS,CAACC,MAAM,EAAE6L,IAAI,GAAG,IAAIC,KAAK,CAAC2B,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;YAC7F7B,IAAI,CAAC6B,KAAK,CAAC,GAAG3N,SAAS,CAAC2N,KAAK,CAAC;UAChC;UACA,CAACF,qBAAqB,GAAGJ,mBAAmB,CAAC3B,SAAS,CAAC,MAAM,IAAI,IAAI+B,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACxB,IAAI,CAACC,KAAK,CAACuB,qBAAqB,EAAE,CAACJ,mBAAmB,CAAC,CAAClB,MAAM,CAACL,IAAI,CAAC,CAAC;UACpMzI,SAAS,CAACqI,SAAS,CAAC,CAACQ,KAAK,CAAC7I,SAAS,EAAEyI,IAAI,CAAC;QAC7C,CAAC;MACH;IACF,CAAC,CAAC;;IAEF;IACA,IAAI8B,WAAW,GAAG,aAAaxO,KAAK,CAACyO,YAAY,CAAC1I,KAAK,EAAE5G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8O,mBAAmB,CAAC,EAAEC,WAAW,CAAC,CAAC;IAC5H,IAAIQ,QAAQ,GAAG;MACbC,CAAC,EAAEhF,MAAM;MACTiF,CAAC,EAAEhF;IACL,CAAC;IACD,IAAIiF,UAAU,GAAGrL,KAAK,GAAGrE,aAAa,CAAC,CAAC,CAAC,EAAEqE,KAAK,KAAK,IAAI,GAAGA,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;;IAE9E;IACA,OAAO,aAAaxD,KAAK,CAAC8O,aAAa,CAAC9O,KAAK,CAAC+O,QAAQ,EAAE,IAAI,EAAE,aAAa/O,KAAK,CAAC8O,aAAa,CAACrP,cAAc,EAAE;MAC7GuP,QAAQ,EAAE,CAAC5H,UAAU;MACrBlG,GAAG,EAAE4E,YAAY;MACjBmJ,QAAQ,EAAElD;IACZ,CAAC,EAAE,aAAa/L,KAAK,CAAC8O,aAAa,CAAC5O,cAAc,EAAE;MAClD8D,iBAAiB,EAAEA;IACrB,CAAC,EAAEwK,WAAW,CAAC,CAAC,EAAE,aAAaxO,KAAK,CAAC8O,aAAa,CAAC3O,cAAc,CAAC+O,QAAQ,EAAE;MAC1EC,KAAK,EAAEvK;IACT,CAAC,EAAE,aAAa5E,KAAK,CAAC8O,aAAa,CAAC7O,KAAK,EAAE;MACzCmP,MAAM,EAAEzO,eAAe;MACvBO,GAAG,EAAEqE,WAAW;MAChBnE,SAAS,EAAEA,SAAS;MACpBsB,KAAK,EAAEA,KAAK;MACZqB,SAAS,EAAEvE,UAAU,CAACmD,cAAc,EAAEiI,gBAAgB,CAAC;MACvDyE,KAAK,EAAEzM,UAAU;MACjB6K,MAAM,EAAE9H,SAAS;MACjB2J,YAAY,EAAE/B,iBAAiB;MAC/BgC,YAAY,EAAE/B;MACd;MAAA;;MAEAgC,cAAc,EAAEjC,iBAAiB;MACjCtK,MAAM,EAAEA;MACR;MAAA;;MAEAwM,IAAI,EAAErI,UAAU;MAChBsI,OAAO,EAAErH,QAAQ;MACjBjF,KAAK,EAAEA;MACP;MAAA;;MAEA4J,OAAO,EAAE1J,YAAY;MACrBqM,oBAAoB,EAAEvC;MACtB;MAAA;;MAEAjL,IAAI,EAAEA;MACN;MAAA;;MAEAyN,MAAM,EAAE9I,gBAAgB;MACxBpD,UAAU,EAAEqD,eAAe;MAC3BiF,gBAAgB,EAAEA,gBAAgB;MAClCE,SAAS,EAAEA;MACX;MAAA;;MAEA3J,WAAW,EAAEA,WAAW;MACxBC,WAAW,EAAE0B,iBAAiB;MAC9B5B,iBAAiB,EAAEA;MACnB;MAAA;;MAEAuN,KAAK,EAAE9F,SAAS;MAChBvG,KAAK,EAAEqL,UAAU;MACjBH,QAAQ,EAAEA;MACV;MAAA;;MAEApF,KAAK,EAAEA,KAAK;MACZC,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBM,OAAO,EAAEQ;MACT;MAAA;;MAEAtH,OAAO,EAAEA,OAAO;MAChBkI,WAAW,EAAEA,WAAW,GAAGvB,MAAM;MACjC2B,YAAY,EAAEA,YAAY,GAAG1B;IAC/B,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC;EACF,IAAIgG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCjP,OAAO,CAACkP,WAAW,GAAG,SAAS;EACjC;EACA,OAAOlP,OAAO;AAChB;AACA,eAAeL,eAAe,CAACnB,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
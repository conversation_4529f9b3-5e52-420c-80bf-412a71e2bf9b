{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport StatisticTimer from './Timer';\nconst Countdown = props => {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Countdown');\n    warning.deprecated(false, '<Statistic.Countdown />', '<Statistic.Timer type=\"countdown\" />');\n  }\n  return /*#__PURE__*/React.createElement(StatisticTimer, Object.assign({}, props, {\n    type: \"countdown\"\n  }));\n};\nexport default /*#__PURE__*/React.memo(Countdown);", "map": {"version": 3, "names": ["React", "devUseW<PERSON>ning", "StatisticTimer", "Countdown", "props", "process", "env", "NODE_ENV", "warning", "deprecated", "createElement", "Object", "assign", "type", "memo"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/statistic/Countdown.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport StatisticTimer from './Timer';\nconst Countdown = props => {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Countdown');\n    warning.deprecated(false, '<Statistic.Countdown />', '<Statistic.Timer type=\"countdown\" />');\n  }\n  return /*#__PURE__*/React.createElement(StatisticTimer, Object.assign({}, props, {\n    type: \"countdown\"\n  }));\n};\nexport default /*#__PURE__*/React.memo(Countdown);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,cAAc,MAAM,SAAS;AACpC,MAAMC,SAAS,GAAGC,KAAK,IAAI;EACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGP,aAAa,CAAC,WAAW,CAAC;IAC1CO,OAAO,CAACC,UAAU,CAAC,KAAK,EAAE,yBAAyB,EAAE,sCAAsC,CAAC;EAC9F;EACA,OAAO,aAAaT,KAAK,CAACU,aAAa,CAACR,cAAc,EAAES,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,KAAK,EAAE;IAC/ES,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAe,aAAab,KAAK,CAACc,IAAI,CAACX,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
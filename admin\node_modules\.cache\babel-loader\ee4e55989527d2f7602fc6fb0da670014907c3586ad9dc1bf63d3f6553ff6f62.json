{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { presetPrimaryColors } from '@ant-design/colors';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { LineStrokeColorVar, Percent } from './style';\nimport { getSize, getSuccessPercent, validProgress } from './utils';\n/**\n * @example\n *   {\n *     \"0%\": \"#afc163\",\n *     \"75%\": \"#009900\",\n *     \"50%\": \"green\", // ====> '#afc163 0%, #66FF00 25%, #00CC00 50%, #009900 75%, #ffffff 100%'\n *     \"25%\": \"#66FF00\",\n *     \"100%\": \"#ffffff\"\n *   }\n */\nexport const sortGradient = gradients => {\n  let tempArr = [];\n  Object.keys(gradients).forEach(key => {\n    const formattedKey = parseFloat(key.replace(/%/g, ''));\n    if (!Number.isNaN(formattedKey)) {\n      tempArr.push({\n        key: formattedKey,\n        value: gradients[key]\n      });\n    }\n  });\n  tempArr = tempArr.sort((a, b) => a.key - b.key);\n  return tempArr.map(_ref => {\n    let {\n      key,\n      value\n    } = _ref;\n    return \"\".concat(value, \" \").concat(key, \"%\");\n  }).join(', ');\n};\n/**\n * Then this man came to realize the truth: Besides six pence, there is the moon. Besides bread and\n * butter, there is the bug. And... Besides women, there is the code.\n *\n * @example\n *   {\n *     \"0%\": \"#afc163\",\n *     \"25%\": \"#66FF00\",\n *     \"50%\": \"#00CC00\", // ====>  linear-gradient(to right, #afc163 0%, #66FF00 25%,\n *     \"75%\": \"#009900\", //        #00CC00 50%, #009900 75%, #ffffff 100%)\n *     \"100%\": \"#ffffff\"\n *   }\n */\nexport const handleGradient = (strokeColor, directionConfig) => {\n  const {\n      from = presetPrimaryColors.blue,\n      to = presetPrimaryColors.blue,\n      direction = directionConfig === 'rtl' ? 'to left' : 'to right'\n    } = strokeColor,\n    rest = __rest(strokeColor, [\"from\", \"to\", \"direction\"]);\n  if (Object.keys(rest).length !== 0) {\n    const sortedGradients = sortGradient(rest);\n    const background = \"linear-gradient(\".concat(direction, \", \").concat(sortedGradients, \")\");\n    return {\n      background,\n      [LineStrokeColorVar]: background\n    };\n  }\n  const background = \"linear-gradient(\".concat(direction, \", \").concat(from, \", \").concat(to, \")\");\n  return {\n    background,\n    [LineStrokeColorVar]: background\n  };\n};\nconst Line = props => {\n  const {\n    prefixCls,\n    direction: directionConfig,\n    percent,\n    size,\n    strokeWidth,\n    strokeColor,\n    strokeLinecap = 'round',\n    children,\n    trailColor = null,\n    percentPosition,\n    success\n  } = props;\n  const {\n    align: infoAlign,\n    type: infoPosition\n  } = percentPosition;\n  const backgroundProps = strokeColor && typeof strokeColor !== 'string' ? handleGradient(strokeColor, directionConfig) : {\n    [LineStrokeColorVar]: strokeColor,\n    background: strokeColor\n  };\n  const borderRadius = strokeLinecap === 'square' || strokeLinecap === 'butt' ? 0 : undefined;\n  const mergedSize = size !== null && size !== void 0 ? size : [-1, strokeWidth || (size === 'small' ? 6 : 8)];\n  const [width, height] = getSize(mergedSize, 'line', {\n    strokeWidth\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Progress');\n    warning.deprecated(!('strokeWidth' in props), 'strokeWidth', 'size');\n  }\n  const trailStyle = {\n    backgroundColor: trailColor || undefined,\n    borderRadius\n  };\n  const percentStyle = Object.assign(Object.assign({\n    width: \"\".concat(validProgress(percent), \"%\"),\n    height,\n    borderRadius\n  }, backgroundProps), {\n    [Percent]: validProgress(percent) / 100\n  });\n  const successPercent = getSuccessPercent(props);\n  const successPercentStyle = {\n    width: \"\".concat(validProgress(successPercent), \"%\"),\n    height,\n    borderRadius,\n    backgroundColor: success === null || success === void 0 ? void 0 : success.strokeColor\n  };\n  const outerStyle = {\n    width: width < 0 ? '100%' : width\n  };\n  const lineInner = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\"),\n    style: trailStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-bg\"), \"\".concat(prefixCls, \"-bg-\").concat(infoPosition)),\n    style: percentStyle\n  }, infoPosition === 'inner' && children), successPercent !== undefined && (/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-success-bg\"),\n    style: successPercentStyle\n  })));\n  const isOuterStart = infoPosition === 'outer' && infoAlign === 'start';\n  const isOuterEnd = infoPosition === 'outer' && infoAlign === 'end';\n  return infoPosition === 'outer' && infoAlign === 'center' ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-layout-bottom\")\n  }, lineInner, children)) : (/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-outer\"),\n    style: outerStyle\n  }, isOuterStart && children, lineInner, isOuterEnd && children));\n};\nexport default Line;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nconst CollapsePanel = /*#__PURE__*/React.forwardRef((props, ref) => {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Collapse.Panel');\n    warning.deprecated(!('disabled' in props), 'disabled', 'collapsible=\"disabled\"');\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    showArrow = true\n  } = props;\n  const prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  const collapsePanelClassName = classNames({\n    [\"\".concat(prefixCls, \"-no-arrow\")]: !showArrow\n  }, className);\n  return /*#__PURE__*/React.createElement(RcCollapse.Panel, Object.assign({\n    ref: ref\n  }, props, {\n    prefixCls: prefixCls,\n    className: collapsePanelClassName\n  }));\n});\nexport default CollapsePanel;", "map": {"version": 3, "names": ["React", "classNames", "RcCollapse", "devUseW<PERSON>ning", "ConfigContext", "CollapsePanel", "forwardRef", "props", "ref", "process", "env", "NODE_ENV", "warning", "deprecated", "getPrefixCls", "useContext", "prefixCls", "customizePrefixCls", "className", "showArrow", "collapsePanelClassName", "concat", "createElement", "Panel", "Object", "assign"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/collapse/CollapsePanel.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nconst CollapsePanel = /*#__PURE__*/React.forwardRef((props, ref) => {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Collapse.Panel');\n    warning.deprecated(!('disabled' in props), 'disabled', 'collapsible=\"disabled\"');\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    showArrow = true\n  } = props;\n  const prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  const collapsePanelClassName = classNames({\n    [`${prefixCls}-no-arrow`]: !showArrow\n  }, className);\n  return /*#__PURE__*/React.createElement(RcCollapse.Panel, Object.assign({\n    ref: ref\n  }, props, {\n    prefixCls: prefixCls,\n    className: collapsePanelClassName\n  }));\n});\nexport default CollapsePanel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,aAAa;AACpC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,MAAMC,aAAa,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAClE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGT,aAAa,CAAC,gBAAgB,CAAC;IAC/CS,OAAO,CAACC,UAAU,CAAC,EAAE,UAAU,IAAIN,KAAK,CAAC,EAAE,UAAU,EAAE,wBAAwB,CAAC;EAClF;EACA,MAAM;IACJO;EACF,CAAC,GAAGd,KAAK,CAACe,UAAU,CAACX,aAAa,CAAC;EACnC,MAAM;IACJY,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,SAAS,GAAG;EACd,CAAC,GAAGZ,KAAK;EACT,MAAMS,SAAS,GAAGF,YAAY,CAAC,UAAU,EAAEG,kBAAkB,CAAC;EAC9D,MAAMG,sBAAsB,GAAGnB,UAAU,CAAC;IACxC,IAAAoB,MAAA,CAAIL,SAAS,iBAAc,CAACG;EAC9B,CAAC,EAAED,SAAS,CAAC;EACb,OAAO,aAAalB,KAAK,CAACsB,aAAa,CAACpB,UAAU,CAACqB,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IACtEjB,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,EAAE;IACRS,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEE;EACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAef,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_weekYear = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t) {\n    t.prototype.weekYear = function () {\n      var e = this.month(),\n        t = this.week(),\n        n = this.year();\n      return 1 === t && 11 === e ? n + 1 : 0 === e && t >= 52 ? n - 1 : n;\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_weekYear", "prototype", "weekYear", "month", "week", "n", "year"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/dayjs/plugin/weekYear.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_weekYear=t()}(this,(function(){\"use strict\";return function(e,t){t.prototype.weekYear=function(){var e=this.month(),t=this.week(),n=this.year();return 1===t&&11===e?n+1:0===e&&t>=52?n-1:n}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,qBAAqB,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,OAAO,UAASD,CAAC,EAACC,CAAC,EAAC;IAACA,CAAC,CAACQ,SAAS,CAACC,QAAQ,GAAC,YAAU;MAAC,IAAIV,CAAC,GAAC,IAAI,CAACW,KAAK,CAAC,CAAC;QAACV,CAAC,GAAC,IAAI,CAACW,IAAI,CAAC,CAAC;QAACC,CAAC,GAAC,IAAI,CAACC,IAAI,CAAC,CAAC;MAAC,OAAO,CAAC,KAAGb,CAAC,IAAE,EAAE,KAAGD,CAAC,GAACa,CAAC,GAAC,CAAC,GAAC,CAAC,KAAGb,CAAC,IAAEC,CAAC,IAAE,EAAE,GAACY,CAAC,GAAC,CAAC,GAACA,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
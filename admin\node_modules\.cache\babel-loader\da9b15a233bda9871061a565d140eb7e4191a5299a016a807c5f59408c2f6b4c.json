{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { forwardRef, useMemo } from 'react';\nimport { ColorBlock } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useLocale } from '../../locale';\nimport { getColorAlpha } from '../util';\nimport ColorClear from './ColorClear';\nconst ColorTrigger = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n      color,\n      prefixCls,\n      open,\n      disabled,\n      format,\n      className,\n      showText,\n      activeIndex\n    } = props,\n    rest = __rest(props, [\"color\", \"prefixCls\", \"open\", \"disabled\", \"format\", \"className\", \"showText\", \"activeIndex\"]);\n  const colorTriggerPrefixCls = \"\".concat(prefixCls, \"-trigger\");\n  const colorTextPrefixCls = \"\".concat(colorTriggerPrefixCls, \"-text\");\n  const colorTextCellPrefixCls = \"\".concat(colorTextPrefixCls, \"-cell\");\n  const [locale] = useLocale('ColorPicker');\n  // ============================== Text ==============================\n  const desc = React.useMemo(() => {\n    if (!showText) {\n      return '';\n    }\n    if (typeof showText === 'function') {\n      return showText(color);\n    }\n    if (color.cleared) {\n      return locale.transparent;\n    }\n    if (color.isGradient()) {\n      return color.getColors().map((c, index) => {\n        const inactive = activeIndex !== -1 && activeIndex !== index;\n        return /*#__PURE__*/React.createElement(\"span\", {\n          key: index,\n          className: classNames(colorTextCellPrefixCls, inactive && \"\".concat(colorTextCellPrefixCls, \"-inactive\"))\n        }, c.color.toRgbString(), \" \", c.percent, \"%\");\n      });\n    }\n    const hexString = color.toHexString().toUpperCase();\n    const alpha = getColorAlpha(color);\n    switch (format) {\n      case 'rgb':\n        return color.toRgbString();\n      case 'hsb':\n        return color.toHsbString();\n      // case 'hex':\n      default:\n        return alpha < 100 ? \"\".concat(hexString.slice(0, 7), \",\").concat(alpha, \"%\") : hexString;\n    }\n  }, [color, format, showText, activeIndex]);\n  // ============================= Render =============================\n  const containerNode = useMemo(() => color.cleared ? (/*#__PURE__*/React.createElement(ColorClear, {\n    prefixCls: prefixCls\n  })) : (/*#__PURE__*/React.createElement(ColorBlock, {\n    prefixCls: prefixCls,\n    color: color.toCssString()\n  })), [color, prefixCls]);\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    ref: ref,\n    className: classNames(colorTriggerPrefixCls, className, {\n      [\"\".concat(colorTriggerPrefixCls, \"-active\")]: open,\n      [\"\".concat(colorTriggerPrefixCls, \"-disabled\")]: disabled\n    })\n  }, pickAttrs(rest)), containerNode, showText && /*#__PURE__*/React.createElement(\"div\", {\n    className: colorTextPrefixCls\n  }, desc));\n});\nexport default ColorTrigger;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "forwardRef", "useMemo", "ColorBlock", "classNames", "pickAttrs", "useLocale", "getColorAlpha", "ColorClear", "ColorTrigger", "props", "ref", "color", "prefixCls", "open", "disabled", "format", "className", "showText", "activeIndex", "rest", "colorTriggerPrefixCls", "concat", "colorTextPrefixCls", "colorTextCellPrefixCls", "locale", "desc", "cleared", "transparent", "isGradient", "getColors", "map", "c", "index", "inactive", "createElement", "key", "toRgbString", "percent", "hexString", "toHexString", "toUpperCase", "alpha", "toHsbString", "slice", "containerNode", "toCssString", "assign"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/color-picker/components/ColorTrigger.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { forwardRef, useMemo } from 'react';\nimport { ColorBlock } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useLocale } from '../../locale';\nimport { getColorAlpha } from '../util';\nimport ColorClear from './ColorClear';\nconst ColorTrigger = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n      color,\n      prefixCls,\n      open,\n      disabled,\n      format,\n      className,\n      showText,\n      activeIndex\n    } = props,\n    rest = __rest(props, [\"color\", \"prefixCls\", \"open\", \"disabled\", \"format\", \"className\", \"showText\", \"activeIndex\"]);\n  const colorTriggerPrefixCls = `${prefixCls}-trigger`;\n  const colorTextPrefixCls = `${colorTriggerPrefixCls}-text`;\n  const colorTextCellPrefixCls = `${colorTextPrefixCls}-cell`;\n  const [locale] = useLocale('ColorPicker');\n  // ============================== Text ==============================\n  const desc = React.useMemo(() => {\n    if (!showText) {\n      return '';\n    }\n    if (typeof showText === 'function') {\n      return showText(color);\n    }\n    if (color.cleared) {\n      return locale.transparent;\n    }\n    if (color.isGradient()) {\n      return color.getColors().map((c, index) => {\n        const inactive = activeIndex !== -1 && activeIndex !== index;\n        return /*#__PURE__*/React.createElement(\"span\", {\n          key: index,\n          className: classNames(colorTextCellPrefixCls, inactive && `${colorTextCellPrefixCls}-inactive`)\n        }, c.color.toRgbString(), \" \", c.percent, \"%\");\n      });\n    }\n    const hexString = color.toHexString().toUpperCase();\n    const alpha = getColorAlpha(color);\n    switch (format) {\n      case 'rgb':\n        return color.toRgbString();\n      case 'hsb':\n        return color.toHsbString();\n      // case 'hex':\n      default:\n        return alpha < 100 ? `${hexString.slice(0, 7)},${alpha}%` : hexString;\n    }\n  }, [color, format, showText, activeIndex]);\n  // ============================= Render =============================\n  const containerNode = useMemo(() => color.cleared ? (/*#__PURE__*/React.createElement(ColorClear, {\n    prefixCls: prefixCls\n  })) : (/*#__PURE__*/React.createElement(ColorBlock, {\n    prefixCls: prefixCls,\n    color: color.toCssString()\n  })), [color, prefixCls]);\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    ref: ref,\n    className: classNames(colorTriggerPrefixCls, className, {\n      [`${colorTriggerPrefixCls}-active`]: open,\n      [`${colorTriggerPrefixCls}-disabled`]: disabled\n    })\n  }, pickAttrs(rest)), containerNode, showText && /*#__PURE__*/React.createElement(\"div\", {\n    className: colorTextPrefixCls\n  }, desc));\n});\nexport default ColorTrigger;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,IAAIC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,4BAA4B;AACvD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,MAAMC,YAAY,GAAG,aAAaR,UAAU,CAAC,CAACS,KAAK,EAAEC,GAAG,KAAK;EAC3D,MAAM;MACFC,KAAK;MACLC,SAAS;MACTC,IAAI;MACJC,QAAQ;MACRC,MAAM;MACNC,SAAS;MACTC,QAAQ;MACRC;IACF,CAAC,GAAGT,KAAK;IACTU,IAAI,GAAGlC,MAAM,CAACwB,KAAK,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;EACpH,MAAMW,qBAAqB,MAAAC,MAAA,CAAMT,SAAS,aAAU;EACpD,MAAMU,kBAAkB,MAAAD,MAAA,CAAMD,qBAAqB,UAAO;EAC1D,MAAMG,sBAAsB,MAAAF,MAAA,CAAMC,kBAAkB,UAAO;EAC3D,MAAM,CAACE,MAAM,CAAC,GAAGnB,SAAS,CAAC,aAAa,CAAC;EACzC;EACA,MAAMoB,IAAI,GAAG1B,KAAK,CAACE,OAAO,CAAC,MAAM;IAC/B,IAAI,CAACgB,QAAQ,EAAE;MACb,OAAO,EAAE;IACX;IACA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAClC,OAAOA,QAAQ,CAACN,KAAK,CAAC;IACxB;IACA,IAAIA,KAAK,CAACe,OAAO,EAAE;MACjB,OAAOF,MAAM,CAACG,WAAW;IAC3B;IACA,IAAIhB,KAAK,CAACiB,UAAU,CAAC,CAAC,EAAE;MACtB,OAAOjB,KAAK,CAACkB,SAAS,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;QACzC,MAAMC,QAAQ,GAAGf,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,KAAKc,KAAK;QAC5D,OAAO,aAAajC,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAE;UAC9CC,GAAG,EAAEH,KAAK;UACVhB,SAAS,EAAEb,UAAU,CAACoB,sBAAsB,EAAEU,QAAQ,OAAAZ,MAAA,CAAOE,sBAAsB,cAAW;QAChG,CAAC,EAAEQ,CAAC,CAACpB,KAAK,CAACyB,WAAW,CAAC,CAAC,EAAE,GAAG,EAAEL,CAAC,CAACM,OAAO,EAAE,GAAG,CAAC;MAChD,CAAC,CAAC;IACJ;IACA,MAAMC,SAAS,GAAG3B,KAAK,CAAC4B,WAAW,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnD,MAAMC,KAAK,GAAGnC,aAAa,CAACK,KAAK,CAAC;IAClC,QAAQI,MAAM;MACZ,KAAK,KAAK;QACR,OAAOJ,KAAK,CAACyB,WAAW,CAAC,CAAC;MAC5B,KAAK,KAAK;QACR,OAAOzB,KAAK,CAAC+B,WAAW,CAAC,CAAC;MAC5B;MACA;QACE,OAAOD,KAAK,GAAG,GAAG,MAAApB,MAAA,CAAMiB,SAAS,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,OAAAtB,MAAA,CAAIoB,KAAK,SAAMH,SAAS;IACzE;EACF,CAAC,EAAE,CAAC3B,KAAK,EAAEI,MAAM,EAAEE,QAAQ,EAAEC,WAAW,CAAC,CAAC;EAC1C;EACA,MAAM0B,aAAa,GAAG3C,OAAO,CAAC,MAAMU,KAAK,CAACe,OAAO,IAAI,aAAa3B,KAAK,CAACmC,aAAa,CAAC3B,UAAU,EAAE;IAChGK,SAAS,EAAEA;EACb,CAAC,CAAC,KAAK,aAAab,KAAK,CAACmC,aAAa,CAAChC,UAAU,EAAE;IAClDU,SAAS,EAAEA,SAAS;IACpBD,KAAK,EAAEA,KAAK,CAACkC,WAAW,CAAC;EAC3B,CAAC,CAAC,CAAC,EAAE,CAAClC,KAAK,EAAEC,SAAS,CAAC,CAAC;EACxB,OAAO,aAAab,KAAK,CAACmC,aAAa,CAAC,KAAK,EAAE5C,MAAM,CAACwD,MAAM,CAAC;IAC3DpC,GAAG,EAAEA,GAAG;IACRM,SAAS,EAAEb,UAAU,CAACiB,qBAAqB,EAAEJ,SAAS,EAAE;MACtD,IAAAK,MAAA,CAAID,qBAAqB,eAAYP,IAAI;MACzC,IAAAQ,MAAA,CAAID,qBAAqB,iBAAcN;IACzC,CAAC;EACH,CAAC,EAAEV,SAAS,CAACe,IAAI,CAAC,CAAC,EAAEyB,aAAa,EAAE3B,QAAQ,IAAI,aAAalB,KAAK,CAACmC,aAAa,CAAC,KAAK,EAAE;IACtFlB,SAAS,EAAEM;EACb,CAAC,EAAEG,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AACF,eAAejB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genStepsSmallStyle = token => {\n  const {\n    componentCls,\n    iconSizeSM,\n    // stepsSmallIconMargin,\n    fontSizeSM,\n    fontSize,\n    colorTextDescription\n  } = token;\n  return {\n    [\"&\".concat(componentCls, \"-small\")]: {\n      [\"&\".concat(componentCls, \"-horizontal:not(\").concat(componentCls, \"-label-vertical) \").concat(componentCls, \"-item\")]: {\n        paddingInlineStart: token.paddingSM,\n        '&:first-child': {\n          paddingInlineStart: 0\n        }\n      },\n      [\"\".concat(componentCls, \"-item-icon\")]: {\n        width: iconSizeSM,\n        height: iconSizeSM,\n        // margin: stepsSmallIconMargin,\n        marginTop: 0,\n        marginBottom: 0,\n        marginInline: \"0 \".concat(unit(token.marginXS)),\n        fontSize: fontSizeSM,\n        lineHeight: unit(iconSizeSM),\n        textAlign: 'center',\n        borderRadius: iconSizeSM\n      },\n      [\"\".concat(componentCls, \"-item-title\")]: {\n        paddingInlineEnd: token.paddingSM,\n        fontSize,\n        lineHeight: unit(iconSizeSM),\n        '&::after': {\n          top: token.calc(iconSizeSM).div(2).equal()\n        }\n      },\n      [\"\".concat(componentCls, \"-item-description\")]: {\n        color: colorTextDescription,\n        fontSize\n      },\n      [\"\".concat(componentCls, \"-item-tail\")]: {\n        top: token.calc(iconSizeSM).div(2).sub(token.paddingXXS).equal()\n      },\n      [\"\".concat(componentCls, \"-item-custom \").concat(componentCls, \"-item-icon\")]: {\n        width: 'inherit',\n        height: 'inherit',\n        lineHeight: 'inherit',\n        background: 'none',\n        border: 0,\n        borderRadius: 0,\n        [\"> \".concat(componentCls, \"-icon\")]: {\n          fontSize: iconSizeSM,\n          lineHeight: unit(iconSizeSM),\n          transform: 'none'\n        }\n      }\n    }\n  };\n};\nexport default genStepsSmallStyle;", "map": {"version": 3, "names": ["unit", "genStepsSmallStyle", "token", "componentCls", "iconSizeSM", "fontSizeSM", "fontSize", "colorTextDescription", "concat", "paddingInlineStart", "paddingSM", "width", "height", "marginTop", "marginBottom", "marginInline", "marginXS", "lineHeight", "textAlign", "borderRadius", "paddingInlineEnd", "top", "calc", "div", "equal", "color", "sub", "paddingXXS", "background", "border", "transform"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/steps/style/small.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genStepsSmallStyle = token => {\n  const {\n    componentCls,\n    iconSizeSM,\n    // stepsSmallIconMargin,\n    fontSizeSM,\n    fontSize,\n    colorTextDescription\n  } = token;\n  return {\n    [`&${componentCls}-small`]: {\n      [`&${componentCls}-horizontal:not(${componentCls}-label-vertical) ${componentCls}-item`]: {\n        paddingInlineStart: token.paddingSM,\n        '&:first-child': {\n          paddingInlineStart: 0\n        }\n      },\n      [`${componentCls}-item-icon`]: {\n        width: iconSizeSM,\n        height: iconSizeSM,\n        // margin: stepsSmallIconMargin,\n        marginTop: 0,\n        marginBottom: 0,\n        marginInline: `0 ${unit(token.marginXS)}`,\n        fontSize: fontSizeSM,\n        lineHeight: unit(iconSizeSM),\n        textAlign: 'center',\n        borderRadius: iconSizeSM\n      },\n      [`${componentCls}-item-title`]: {\n        paddingInlineEnd: token.paddingSM,\n        fontSize,\n        lineHeight: unit(iconSizeSM),\n        '&::after': {\n          top: token.calc(iconSizeSM).div(2).equal()\n        }\n      },\n      [`${componentCls}-item-description`]: {\n        color: colorTextDescription,\n        fontSize\n      },\n      [`${componentCls}-item-tail`]: {\n        top: token.calc(iconSizeSM).div(2).sub(token.paddingXXS).equal()\n      },\n      [`${componentCls}-item-custom ${componentCls}-item-icon`]: {\n        width: 'inherit',\n        height: 'inherit',\n        lineHeight: 'inherit',\n        background: 'none',\n        border: 0,\n        borderRadius: 0,\n        [`> ${componentCls}-icon`]: {\n          fontSize: iconSizeSM,\n          lineHeight: unit(iconSizeSM),\n          transform: 'none'\n        }\n      }\n    }\n  };\n};\nexport default genStepsSmallStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,kBAAkB,GAAGC,KAAK,IAAI;EAClC,MAAM;IACJC,YAAY;IACZC,UAAU;IACV;IACAC,UAAU;IACVC,QAAQ;IACRC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,KAAAM,MAAA,CAAKL,YAAY,cAAW;MAC1B,KAAAK,MAAA,CAAKL,YAAY,sBAAAK,MAAA,CAAmBL,YAAY,uBAAAK,MAAA,CAAoBL,YAAY,aAAU;QACxFM,kBAAkB,EAAEP,KAAK,CAACQ,SAAS;QACnC,eAAe,EAAE;UACfD,kBAAkB,EAAE;QACtB;MACF,CAAC;MACD,IAAAD,MAAA,CAAIL,YAAY,kBAAe;QAC7BQ,KAAK,EAAEP,UAAU;QACjBQ,MAAM,EAAER,UAAU;QAClB;QACAS,SAAS,EAAE,CAAC;QACZC,YAAY,EAAE,CAAC;QACfC,YAAY,OAAAP,MAAA,CAAOR,IAAI,CAACE,KAAK,CAACc,QAAQ,CAAC,CAAE;QACzCV,QAAQ,EAAED,UAAU;QACpBY,UAAU,EAAEjB,IAAI,CAACI,UAAU,CAAC;QAC5Bc,SAAS,EAAE,QAAQ;QACnBC,YAAY,EAAEf;MAChB,CAAC;MACD,IAAAI,MAAA,CAAIL,YAAY,mBAAgB;QAC9BiB,gBAAgB,EAAElB,KAAK,CAACQ,SAAS;QACjCJ,QAAQ;QACRW,UAAU,EAAEjB,IAAI,CAACI,UAAU,CAAC;QAC5B,UAAU,EAAE;UACViB,GAAG,EAAEnB,KAAK,CAACoB,IAAI,CAAClB,UAAU,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;QAC3C;MACF,CAAC;MACD,IAAAhB,MAAA,CAAIL,YAAY,yBAAsB;QACpCsB,KAAK,EAAElB,oBAAoB;QAC3BD;MACF,CAAC;MACD,IAAAE,MAAA,CAAIL,YAAY,kBAAe;QAC7BkB,GAAG,EAAEnB,KAAK,CAACoB,IAAI,CAAClB,UAAU,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAC,CAACG,GAAG,CAACxB,KAAK,CAACyB,UAAU,CAAC,CAACH,KAAK,CAAC;MACjE,CAAC;MACD,IAAAhB,MAAA,CAAIL,YAAY,mBAAAK,MAAA,CAAgBL,YAAY,kBAAe;QACzDQ,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,SAAS;QACjBK,UAAU,EAAE,SAAS;QACrBW,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAE,CAAC;QACTV,YAAY,EAAE,CAAC;QACf,MAAAX,MAAA,CAAML,YAAY,aAAU;UAC1BG,QAAQ,EAAEF,UAAU;UACpBa,UAAU,EAAEjB,IAAI,CAACI,UAAU,CAAC;UAC5B0B,SAAS,EAAE;QACb;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAe7B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
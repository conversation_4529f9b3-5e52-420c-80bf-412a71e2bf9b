#!/bin/bash
# Admin页面空白问题诊断脚本

echo "========================================="
echo "🔍 Admin页面空白问题诊断"
echo "========================================="
echo

echo "📁 检查文件结构..."
echo "Admin目录内容:"
ls -la /www/wwwroot/h5.haokajiyun.com/admin/
echo

echo "Static目录内容:"
if [ -d "/www/wwwroot/h5.haokajiyun.com/admin/static" ]; then
    ls -la /www/wwwroot/h5.haokajiyun.com/admin/static/
    echo
    
    echo "CSS目录:"
    if [ -d "/www/wwwroot/h5.haokajiyun.com/admin/static/css" ]; then
        ls -la /www/wwwroot/h5.haokajiyun.com/admin/static/css/
    else
        echo "❌ CSS目录不存在"
    fi
    echo
    
    echo "JS目录:"
    if [ -d "/www/wwwroot/h5.haokajiyun.com/admin/static/js" ]; then
        ls -la /www/wwwroot/h5.haokajiyun.com/admin/static/js/
    else
        echo "❌ JS目录不存在"
    fi
else
    echo "❌ static目录不存在"
fi
echo

echo "📄 检查index.html内容..."
if [ -f "/www/wwwroot/h5.haokajiyun.com/admin/index.html" ]; then
    echo "index.html文件大小:"
    wc -c /www/wwwroot/h5.haokajiyun.com/admin/index.html
    echo
    
    echo "index.html内容:"
    cat /www/wwwroot/h5.haokajiyun.com/admin/index.html
    echo
    
    echo "检查资源引用:"
    grep -E "(href=|src=)" /www/wwwroot/h5.haokajiyun.com/admin/index.html
else
    echo "❌ index.html文件不存在"
fi
echo

echo "🔧 检查文件权限..."
echo "Admin目录权限:"
ls -ld /www/wwwroot/h5.haokajiyun.com/admin/
echo "index.html权限:"
ls -l /www/wwwroot/h5.haokajiyun.com/admin/index.html
echo

echo "🌐 测试文件访问..."
echo "测试index.html访问:"
curl -I https://h5.haokajiyun.com/admin/index.html
echo

echo "测试CSS文件访问:"
if [ -f "/www/wwwroot/h5.haokajiyun.com/admin/static/css/main.1c68141c.css" ]; then
    curl -I https://h5.haokajiyun.com/admin/static/css/main.1c68141c.css
else
    echo "CSS文件不存在，查找其他CSS文件:"
    find /www/wwwroot/h5.haokajiyun.com/admin/static -name "*.css" 2>/dev/null
fi
echo

echo "测试JS文件访问:"
if [ -f "/www/wwwroot/h5.haokajiyun.com/admin/static/js/main.990c5350.js" ]; then
    curl -I https://h5.haokajiyun.com/admin/static/js/main.990c5350.js
else
    echo "JS文件不存在，查找其他JS文件:"
    find /www/wwwroot/h5.haokajiyun.com/admin/static -name "*.js" 2>/dev/null
fi
echo

echo "📋 检查Nginx配置..."
echo "测试Nginx配置:"
nginx -t
echo

echo "查看Nginx错误日志 (最后10行):"
tail -10 /www/wwwlogs/h5.haokajiyun.com.error.log
echo

echo "========================================="
echo "🎯 诊断完成"
echo "========================================="
echo "请检查以上输出，找出问题所在："
echo "1. 文件是否存在"
echo "2. 权限是否正确"
echo "3. 资源路径是否正确"
echo "4. Nginx配置是否有误"
echo "5. 是否有错误日志"

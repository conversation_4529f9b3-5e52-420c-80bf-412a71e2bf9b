{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\CategoryManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, Modal, Form, Input, message, Popconfirm } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, FolderOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\n// 模拟数据\nconst mockCategories = [{\n  id: 1,\n  name: '中国电信',\n  description: '中国电信运营商套餐产品',\n  productCount: 28,\n  status: 'active',\n  createdAt: '2024-01-01'\n}, {\n  id: 2,\n  name: '中国联通',\n  description: '中国联通运营商套餐产品',\n  productCount: 22,\n  status: 'active',\n  createdAt: '2024-01-02'\n}, {\n  id: 3,\n  name: '中国移动',\n  description: '中国移动运营商套餐产品',\n  productCount: 35,\n  status: 'active',\n  createdAt: '2024-01-03'\n}, {\n  id: 4,\n  name: '中国广电',\n  description: '中国广电运营商套餐产品',\n  productCount: 8,\n  status: 'active',\n  createdAt: '2024-01-04'\n}];\nconst CategoryManagement = () => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [form] = Form.useForm();\n\n  // 从localStorage加载数据\n  const loadCategories = () => {\n    try {\n      const savedCategories = localStorage.getItem('categories');\n      if (savedCategories) {\n        const parsedCategories = JSON.parse(savedCategories);\n        setCategories(parsedCategories);\n      } else {\n        // 如果没有保存的数据，使用默认数据并保存\n        setCategories(mockCategories);\n        localStorage.setItem('categories', JSON.stringify(mockCategories));\n      }\n    } catch (error) {\n      console.error('加载分类数据失败:', error);\n      setCategories(mockCategories);\n    }\n  };\n\n  // 保存数据到localStorage\n  const saveCategories = newCategories => {\n    try {\n      localStorage.setItem('categories', JSON.stringify(newCategories));\n      setCategories(newCategories);\n    } catch (error) {\n      console.error('保存分类数据失败:', error);\n      message.error('保存失败，请重试');\n    }\n  };\n\n  // 组件挂载时加载数据\n  React.useEffect(() => {\n    loadCategories();\n  }, []);\n\n  // 重置数据到默认状态\n  const handleResetData = () => {\n    Modal.confirm({\n      title: '确认重置',\n      content: '确定要重置所有分类数据到默认状态吗？此操作不可恢复。',\n      onOk: () => {\n        saveCategories(mockCategories);\n        // 触发自定义事件通知其他组件分类数据已更新\n        window.dispatchEvent(new CustomEvent('categoriesUpdated'));\n        message.success('数据已重置');\n      }\n    });\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '分类ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      style: {\n        fontFamily: 'monospace',\n        fontWeight: 'bold'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '分类名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: text => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(FolderOutlined, {\n        style: {\n          color: '#1890ff'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontWeight: 600\n        },\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    ellipsis: true\n  }, {\n    title: '产品数量',\n    dataIndex: 'productCount',\n    key: 'productCount',\n    width: 100,\n    render: count => /*#__PURE__*/_jsxDEV(Tag, {\n      color: count > 0 ? 'green' : 'default',\n      children: [count, \" \\u4E2A\\u4EA7\\u54C1\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 'active' ? 'success' : 'default',\n      children: status === 'active' ? '启用' : '禁用'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 120\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u5206\\u7C7B\\u5417\\uFF1F\",\n        description: \"\\u5220\\u9664\\u540E\\u65E0\\u6CD5\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\u3002\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 新增分类\n  const handleAdd = () => {\n    setEditingCategory(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 编辑分类\n  const handleEdit = category => {\n    setEditingCategory(category);\n    form.setFieldsValue(category);\n    setModalVisible(true);\n  };\n\n  // 删除分类\n  const handleDelete = id => {\n    const newCategories = categories.filter(cat => cat.id !== id);\n    saveCategories(newCategories);\n    // 触发自定义事件通知其他组件分类数据已更新\n    window.dispatchEvent(new CustomEvent('categoriesUpdated'));\n    message.success('删除成功');\n  };\n\n  // 保存分类\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      let newCategories;\n      if (editingCategory) {\n        // 编辑\n        newCategories = categories.map(cat => cat.id === editingCategory.id ? {\n          ...cat,\n          ...values\n        } : cat);\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newCategory = {\n          id: categories.length > 0 ? Math.max(...categories.map(c => c.id)) + 1 : 1,\n          ...values,\n          productCount: 0,\n          status: 'active',\n          createdAt: new Date().toISOString().split('T')[0]\n        };\n        newCategories = [...categories, newCategory];\n        message.success('新增成功');\n      }\n      saveCategories(newCategories);\n      // 触发自定义事件通知其他组件分类数据已更新\n      window.dispatchEvent(new CustomEvent('categoriesUpdated'));\n      setModalVisible(false);\n    } catch (error) {\n      console.error('保存失败:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#1890ff'\n        },\n        children: \"\\u5206\\u7C7B\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n        type: \"secondary\",\n        children: \"\\u7BA1\\u7406\\u4EA7\\u54C1\\u5206\\u7C7B\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Typography.Text, {\n            strong: true,\n            children: [\"\\u5171 \", categories.length, \" \\u4E2A\\u5206\\u7C7B\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleResetData,\n            style: {\n              color: '#666'\n            },\n            children: \"\\u91CD\\u7F6E\\u6570\\u636E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u65B0\\u589E\\u5206\\u7C7B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: categories,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingCategory ? '编辑分类' : '新增分类',\n      open: modalVisible,\n      onOk: handleSave,\n      onCancel: () => setModalVisible(false),\n      okText: \"\\u4FDD\\u5B58\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u5206\\u7C7B\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入分类名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5206\\u7C7B\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u5206\\u7C7B\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入分类描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5206\\u7C7B\\u63CF\\u8FF0\",\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"kk47ycGbmE+OOsFWA9Womqtkf0Y=\", false, function () {\n  return [Form.useForm];\n});\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Modal", "Form", "Input", "message", "Popconfirm", "PlusOutlined", "EditOutlined", "DeleteOutlined", "FolderOutlined", "jsxDEV", "_jsxDEV", "Title", "mockCategories", "id", "name", "description", "productCount", "status", "createdAt", "CategoryManagement", "_s", "categories", "setCategories", "loading", "setLoading", "modalVisible", "setModalVisible", "editingCategory", "setEditingCategory", "form", "useForm", "loadCategories", "savedCategories", "localStorage", "getItem", "parsedCategories", "JSON", "parse", "setItem", "stringify", "error", "console", "saveCategories", "newCategories", "useEffect", "handleResetData", "confirm", "title", "content", "onOk", "window", "dispatchEvent", "CustomEvent", "success", "columns", "dataIndex", "key", "width", "render", "text", "color", "style", "fontFamily", "fontWeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ellipsis", "count", "_", "record", "size", "type", "icon", "onClick", "handleEdit", "onConfirm", "handleDelete", "okText", "cancelText", "danger", "handleAdd", "resetFields", "category", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cat", "handleSave", "values", "validateFields", "map", "newCategory", "length", "Math", "max", "c", "Date", "toISOString", "split", "marginBottom", "level", "margin", "Text", "display", "justifyContent", "alignItems", "strong", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onCancel", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "TextArea", "rows", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/CategoryManagement.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  message,\n  Popconfirm,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  FolderOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title } = Typography;\n\ninterface Category {\n  id: number;\n  name: string;\n  description: string;\n  productCount: number;\n  status: 'active' | 'inactive';\n  createdAt: string;\n}\n\n// 模拟数据\nconst mockCategories: Category[] = [\n  {\n    id: 1,\n    name: '中国电信',\n    description: '中国电信运营商套餐产品',\n    productCount: 28,\n    status: 'active',\n    createdAt: '2024-01-01',\n  },\n  {\n    id: 2,\n    name: '中国联通',\n    description: '中国联通运营商套餐产品',\n    productCount: 22,\n    status: 'active',\n    createdAt: '2024-01-02',\n  },\n  {\n    id: 3,\n    name: '中国移动',\n    description: '中国移动运营商套餐产品',\n    productCount: 35,\n    status: 'active',\n    createdAt: '2024-01-03',\n  },\n  {\n    id: 4,\n    name: '中国广电',\n    description: '中国广电运营商套餐产品',\n    productCount: 8,\n    status: 'active',\n    createdAt: '2024-01-04',\n  },\n];\n\nconst CategoryManagement: React.FC = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [form] = Form.useForm();\n\n  // 从localStorage加载数据\n  const loadCategories = () => {\n    try {\n      const savedCategories = localStorage.getItem('categories');\n      if (savedCategories) {\n        const parsedCategories = JSON.parse(savedCategories);\n        setCategories(parsedCategories);\n      } else {\n        // 如果没有保存的数据，使用默认数据并保存\n        setCategories(mockCategories);\n        localStorage.setItem('categories', JSON.stringify(mockCategories));\n      }\n    } catch (error) {\n      console.error('加载分类数据失败:', error);\n      setCategories(mockCategories);\n    }\n  };\n\n  // 保存数据到localStorage\n  const saveCategories = (newCategories: Category[]) => {\n    try {\n      localStorage.setItem('categories', JSON.stringify(newCategories));\n      setCategories(newCategories);\n    } catch (error) {\n      console.error('保存分类数据失败:', error);\n      message.error('保存失败，请重试');\n    }\n  };\n\n  // 组件挂载时加载数据\n  React.useEffect(() => {\n    loadCategories();\n  }, []);\n\n  // 重置数据到默认状态\n  const handleResetData = () => {\n    Modal.confirm({\n      title: '确认重置',\n      content: '确定要重置所有分类数据到默认状态吗？此操作不可恢复。',\n      onOk: () => {\n        saveCategories(mockCategories);\n        // 触发自定义事件通知其他组件分类数据已更新\n        window.dispatchEvent(new CustomEvent('categoriesUpdated'));\n        message.success('数据已重置');\n      },\n    });\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Category> = [\n    {\n      title: '分类ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n      render: (text: number) => (\n        <Tag color=\"blue\" style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>\n          {text}\n        </Tag>\n      ),\n    },\n    {\n      title: '分类名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text: string) => (\n        <Space>\n          <FolderOutlined style={{ color: '#1890ff' }} />\n          <span style={{ fontWeight: 600 }}>{text}</span>\n        </Space>\n      ),\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true,\n    },\n    {\n      title: '产品数量',\n      dataIndex: 'productCount',\n      key: 'productCount',\n      width: 100,\n      render: (count: number) => (\n        <Tag color={count > 0 ? 'green' : 'default'}>\n          {count} 个产品\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={status === 'active' ? 'success' : 'default'}>\n          {status === 'active' ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个分类吗？\"\n            description=\"删除后无法恢复，请谨慎操作。\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  // 新增分类\n  const handleAdd = () => {\n    setEditingCategory(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 编辑分类\n  const handleEdit = (category: Category) => {\n    setEditingCategory(category);\n    form.setFieldsValue(category);\n    setModalVisible(true);\n  };\n\n  // 删除分类\n  const handleDelete = (id: number) => {\n    const newCategories = categories.filter(cat => cat.id !== id);\n    saveCategories(newCategories);\n    // 触发自定义事件通知其他组件分类数据已更新\n    window.dispatchEvent(new CustomEvent('categoriesUpdated'));\n    message.success('删除成功');\n  };\n\n  // 保存分类\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      let newCategories: Category[];\n\n      if (editingCategory) {\n        // 编辑\n        newCategories = categories.map(cat =>\n          cat.id === editingCategory.id\n            ? { ...cat, ...values }\n            : cat\n        );\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newCategory: Category = {\n          id: categories.length > 0 ? Math.max(...categories.map(c => c.id)) + 1 : 1,\n          ...values,\n          productCount: 0,\n          status: 'active' as const,\n          createdAt: new Date().toISOString().split('T')[0],\n        };\n        newCategories = [...categories, newCategory];\n        message.success('新增成功');\n      }\n\n      saveCategories(newCategories);\n      // 触发自定义事件通知其他组件分类数据已更新\n      window.dispatchEvent(new CustomEvent('categoriesUpdated'));\n      setModalVisible(false);\n    } catch (error) {\n      console.error('保存失败:', error);\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n          分类管理\n        </Title>\n        <Typography.Text type=\"secondary\">\n          管理产品分类信息\n        </Typography.Text>\n      </div>\n\n      <Card>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        }}>\n          <div>\n            <Typography.Text strong>\n              共 {categories.length} 个分类\n            </Typography.Text>\n          </div>\n          <Space>\n            <Button\n              onClick={handleResetData}\n              style={{ color: '#666' }}\n            >\n              重置数据\n            </Button>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              新增分类\n            </Button>\n          </Space>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={categories}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 新增/编辑弹窗 */}\n      <Modal\n        title={editingCategory ? '编辑分类' : '新增分类'}\n        open={modalVisible}\n        onOk={handleSave}\n        onCancel={() => setModalVisible(false)}\n        okText=\"保存\"\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"分类名称\"\n            rules={[{ required: true, message: '请输入分类名称' }]}\n          >\n            <Input placeholder=\"请输入分类名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"description\"\n            label=\"分类描述\"\n            rules={[{ required: true, message: '请输入分类描述' }]}\n          >\n            <Input.TextArea \n              placeholder=\"请输入分类描述\" \n              rows={3}\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CategoryManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC;AAAM,CAAC,GAAGb,UAAU;AAW5B;AACA,MAAMc,cAA0B,GAAG,CACjC;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,aAAa;EAC1BC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAC,EACD;EACEL,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,aAAa;EAC1BC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAC,EACD;EACEL,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,aAAa;EAC1BC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAC,EACD;EACEL,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,aAAa;EAC1BC,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAC,CACF;AAED,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAACoC,IAAI,CAAC,GAAG5B,IAAI,CAAC6B,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI;MACF,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAC1D,IAAIF,eAAe,EAAE;QACnB,MAAMG,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACL,eAAe,CAAC;QACpDV,aAAa,CAACa,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACAb,aAAa,CAACV,cAAc,CAAC;QAC7BqB,YAAY,CAACK,OAAO,CAAC,YAAY,EAAEF,IAAI,CAACG,SAAS,CAAC3B,cAAc,CAAC,CAAC;MACpE;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjClB,aAAa,CAACV,cAAc,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM8B,cAAc,GAAIC,aAAyB,IAAK;IACpD,IAAI;MACFV,YAAY,CAACK,OAAO,CAAC,YAAY,EAAEF,IAAI,CAACG,SAAS,CAACI,aAAa,CAAC,CAAC;MACjErB,aAAa,CAACqB,aAAa,CAAC;IAC9B,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCrC,OAAO,CAACqC,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;;EAED;EACAhD,KAAK,CAACoD,SAAS,CAAC,MAAM;IACpBb,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,eAAe,GAAGA,CAAA,KAAM;IAC5B7C,KAAK,CAAC8C,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAEA,CAAA,KAAM;QACVP,cAAc,CAAC9B,cAAc,CAAC;QAC9B;QACAsC,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,CAAC,CAAC;QAC1DjD,OAAO,CAACkD,OAAO,CAAC,OAAO,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,OAA8B,GAAG,CACrC;IACEP,KAAK,EAAE,MAAM;IACbQ,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGC,IAAY,iBACnBjD,OAAA,CAACX,GAAG;MAAC6D,KAAK,EAAC,MAAM;MAACC,KAAK,EAAE;QAAEC,UAAU,EAAE,WAAW;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAC,QAAA,EACtEL;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACErB,KAAK,EAAE,MAAM;IACbQ,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXE,MAAM,EAAGC,IAAY,iBACnBjD,OAAA,CAACb,KAAK;MAAAmE,QAAA,gBACJtD,OAAA,CAACF,cAAc;QAACqD,KAAK,EAAE;UAAED,KAAK,EAAE;QAAU;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/C1D,OAAA;QAAMmD,KAAK,EAAE;UAAEE,UAAU,EAAE;QAAI,CAAE;QAAAC,QAAA,EAAEL;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C;EAEX,CAAC,EACD;IACErB,KAAK,EAAE,IAAI;IACXQ,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBa,QAAQ,EAAE;EACZ,CAAC,EACD;IACEtB,KAAK,EAAE,MAAM;IACbQ,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGY,KAAa,iBACpB5D,OAAA,CAACX,GAAG;MAAC6D,KAAK,EAAEU,KAAK,GAAG,CAAC,GAAG,OAAO,GAAG,SAAU;MAAAN,QAAA,GACzCM,KAAK,EAAC,qBACT;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAET,CAAC,EACD;IACErB,KAAK,EAAE,IAAI;IACXQ,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGzC,MAAc,iBACrBP,OAAA,CAACX,GAAG;MAAC6D,KAAK,EAAE3C,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;MAAA+C,QAAA,EACrD/C,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAI;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAET,CAAC,EACD;IACErB,KAAK,EAAE,MAAM;IACbQ,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACa,CAAC,EAAEC,MAAM,kBAChB9D,OAAA,CAACb,KAAK;MAAC4E,IAAI,EAAC,OAAO;MAAAT,QAAA,gBACjBtD,OAAA,CAACd,MAAM;QACL8E,IAAI,EAAC,SAAS;QACdD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAEjE,OAAA,CAACJ,YAAY;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAACL,MAAM,CAAE;QAAAR,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1D,OAAA,CAACN,UAAU;QACT2C,KAAK,EAAC,oEAAa;QACnBhC,WAAW,EAAC,sFAAgB;QAC5B+D,SAAS,EAAEA,CAAA,KAAMC,YAAY,CAACP,MAAM,CAAC3D,EAAE,CAAE;QACzCmE,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAjB,QAAA,eAEftD,OAAA,CAACd,MAAM;UACLsF,MAAM;UACNT,IAAI,EAAC,OAAO;UACZE,IAAI,eAAEjE,OAAA,CAACH,cAAc;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMe,SAAS,GAAGA,CAAA,KAAM;IACtBvD,kBAAkB,CAAC,IAAI,CAAC;IACxBC,IAAI,CAACuD,WAAW,CAAC,CAAC;IAClB1D,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMmD,UAAU,GAAIQ,QAAkB,IAAK;IACzCzD,kBAAkB,CAACyD,QAAQ,CAAC;IAC5BxD,IAAI,CAACyD,cAAc,CAACD,QAAQ,CAAC;IAC7B3D,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMqD,YAAY,GAAIlE,EAAU,IAAK;IACnC,MAAM8B,aAAa,GAAGtB,UAAU,CAACkE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC3E,EAAE,KAAKA,EAAE,CAAC;IAC7D6B,cAAc,CAACC,aAAa,CAAC;IAC7B;IACAO,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAC1DjD,OAAO,CAACkD,OAAO,CAAC,MAAM,CAAC;EACzB,CAAC;;EAED;EACA,MAAMoC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM7D,IAAI,CAAC8D,cAAc,CAAC,CAAC;MAC1C,IAAIhD,aAAyB;MAE7B,IAAIhB,eAAe,EAAE;QACnB;QACAgB,aAAa,GAAGtB,UAAU,CAACuE,GAAG,CAACJ,GAAG,IAChCA,GAAG,CAAC3E,EAAE,KAAKc,eAAe,CAACd,EAAE,GACzB;UAAE,GAAG2E,GAAG;UAAE,GAAGE;QAAO,CAAC,GACrBF,GACN,CAAC;QACDrF,OAAO,CAACkD,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAMwC,WAAqB,GAAG;UAC5BhF,EAAE,EAAEQ,UAAU,CAACyE,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG3E,UAAU,CAACuE,GAAG,CAACK,CAAC,IAAIA,CAAC,CAACpF,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC1E,GAAG6E,MAAM;UACT1E,YAAY,EAAE,CAAC;UACfC,MAAM,EAAE,QAAiB;UACzBC,SAAS,EAAE,IAAIgF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACDzD,aAAa,GAAG,CAAC,GAAGtB,UAAU,EAAEwE,WAAW,CAAC;QAC5C1F,OAAO,CAACkD,OAAO,CAAC,MAAM,CAAC;MACzB;MAEAX,cAAc,CAACC,aAAa,CAAC;MAC7B;MACAO,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,CAAC,CAAC;MAC1D1B,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,oBACE9B,OAAA;IAAAsD,QAAA,gBACEtD,OAAA;MAAKmD,KAAK,EAAE;QAAEwC,YAAY,EAAE;MAAO,CAAE;MAAArC,QAAA,gBACnCtD,OAAA,CAACC,KAAK;QAAC2F,KAAK,EAAE,CAAE;QAACzC,KAAK,EAAE;UAAE0C,MAAM,EAAE,CAAC;UAAE3C,KAAK,EAAE;QAAU,CAAE;QAAAI,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR1D,OAAA,CAACZ,UAAU,CAAC0G,IAAI;QAAC9B,IAAI,EAAC,WAAW;QAAAV,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEN1D,OAAA,CAAChB,IAAI;MAAAsE,QAAA,gBACHtD,OAAA;QAAKmD,KAAK,EAAE;UACV4C,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBN,YAAY,EAAE;QAChB,CAAE;QAAArC,QAAA,gBACAtD,OAAA;UAAAsD,QAAA,eACEtD,OAAA,CAACZ,UAAU,CAAC0G,IAAI;YAACI,MAAM;YAAA5C,QAAA,GAAC,SACpB,EAAC3C,UAAU,CAACyE,MAAM,EAAC,qBACvB;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACN1D,OAAA,CAACb,KAAK;UAAAmE,QAAA,gBACJtD,OAAA,CAACd,MAAM;YACLgF,OAAO,EAAE/B,eAAgB;YACzBgB,KAAK,EAAE;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAI,QAAA,EAC1B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1D,OAAA,CAACd,MAAM;YACL8E,IAAI,EAAC,SAAS;YACdC,IAAI,eAAEjE,OAAA,CAACL,YAAY;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBQ,OAAO,EAAEO,SAAU;YAAAnB,QAAA,EACpB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN1D,OAAA,CAACf,KAAK;QACJ2D,OAAO,EAAEA,OAAQ;QACjBuD,UAAU,EAAExF,UAAW;QACvByF,MAAM,EAAC,IAAI;QACXvF,OAAO,EAAEA,OAAQ;QACjBwF,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAASD,KAAK;QAC3C;MAAE;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP1D,OAAA,CAACV,KAAK;MACJ+C,KAAK,EAAEpB,eAAe,GAAG,MAAM,GAAG,MAAO;MACzC0F,IAAI,EAAE5F,YAAa;MACnBwB,IAAI,EAAEwC,UAAW;MACjB6B,QAAQ,EAAEA,CAAA,KAAM5F,eAAe,CAAC,KAAK,CAAE;MACvCsD,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAAjB,QAAA,eAEftD,OAAA,CAACT,IAAI;QACH4B,IAAI,EAAEA,IAAK;QACX0F,MAAM,EAAC,UAAU;QAAAvD,QAAA,gBAEjBtD,OAAA,CAACT,IAAI,CAACuH,IAAI;UACR1G,IAAI,EAAC,MAAM;UACX2G,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExH,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6D,QAAA,eAEhDtD,OAAA,CAACR,KAAK;YAAC0H,WAAW,EAAC;UAAS;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZ1D,OAAA,CAACT,IAAI,CAACuH,IAAI;UACR1G,IAAI,EAAC,aAAa;UAClB2G,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExH,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6D,QAAA,eAEhDtD,OAAA,CAACR,KAAK,CAAC2H,QAAQ;YACbD,WAAW,EAAC,4CAAS;YACrBE,IAAI,EAAE;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChD,EAAA,CAtSID,kBAA4B;EAAA,QAKjBlB,IAAI,CAAC6B,OAAO;AAAA;AAAAiG,EAAA,GALvB5G,kBAA4B;AAwSlC,eAAeA,kBAAkB;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
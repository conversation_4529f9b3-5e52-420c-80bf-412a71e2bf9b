{"ast": null, "code": "// JSX Structure Syntactic Sugar. Never reach the render code.\n/* istanbul ignore next */\nconst DescriptionsItem = ({\n  children\n}) => children;\nexport default DescriptionsItem;", "map": {"version": 3, "names": ["DescriptionsItem", "children"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/descriptions/Item.js"], "sourcesContent": ["// JSX Structure Syntactic Sugar. Never reach the render code.\n/* istanbul ignore next */\nconst DescriptionsItem = ({\n  children\n}) => children;\nexport default DescriptionsItem;"], "mappings": "AAAA;AACA;AACA,MAAMA,gBAAgB,GAAGA,CAAC;EACxBC;AACF,CAAC,KAAKA,QAAQ;AACd,eAAeD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
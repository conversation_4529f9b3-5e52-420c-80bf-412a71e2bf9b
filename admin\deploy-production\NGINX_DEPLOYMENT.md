# 🚀 Nginx部署教程 - 后台管理系统

## 📋 **部署概览**

**服务器环境**: 
- 后台管理端: `/www/wwwroot/h5.haokajiyun.com/admin`
- API运行目录: `/www/wwwroot/h5.haokajiyun.com/public`
- 域名: `h5.haokajiyun.com`
- Web服务器: Nginx

## 🎯 **部署目标**

- ✅ 后台管理系统: `https://h5.haokajiyun.com/admin`
- ✅ API接口: `https://h5.haokajiyun.com/api`
- ✅ 自动HTTPS重定向
- ✅ 静态资源缓存优化
- ✅ CORS跨域支持

## 📦 **第一步: 上传文件**

### **1. 上传后台管理文件**
将当前目录下的所有文件上传到服务器：

```bash
# 目标路径
/www/wwwroot/h5.haokajiyun.com/admin/

# 需要上传的文件
├── index.html
├── static/
│   ├── css/
│   └── js/
├── asset-manifest.json
└── 其他文件...
```

### **2. 设置文件权限**
```bash
# 设置目录权限
chmod 755 /www/wwwroot/h5.haokajiyun.com/admin

# 设置文件权限
find /www/wwwroot/h5.haokajiyun.com/admin -type f -exec chmod 644 {} \;
find /www/wwwroot/h5.haokajiyun.com/admin -type d -exec chmod 755 {} \;

# 设置所有者 (如果需要)
chown -R www:www /www/wwwroot/h5.haokajiyun.com/admin
```

## ⚙️ **第二步: 配置Nginx**

### **方法A: 宝塔面板配置**

1. **登录宝塔面板**
2. **进入网站管理**
3. **找到 `h5.haokajiyun.com` 网站**
4. **点击"设置"**
5. **选择"配置文件"**
6. **替换为以下配置**:

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name h5.haokajiyun.com;
    
    root /www/wwwroot/h5.haokajiyun.com/public;
    index index.php index.html;
    
    # SSL配置 (宝塔自动生成)
    ssl_certificate /www/server/panel/vhost/cert/h5.haokajiyun.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/h5.haokajiyun.com/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 根目录重定向到admin
    location = / {
        return 301 /admin/;
    }
    
    # API路由
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept";
            add_header Access-Control-Max-Age 86400;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
    
    # PHP处理
    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass unix:/tmp/php-cgi-83.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # Admin后台
    location /admin/ {
        alias /www/wwwroot/h5.haokajiyun.com/admin/;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
        
        # 静态资源缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # HTML不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # 禁止访问敏感文件
    location ~ /\.(ht|env) {
        deny all;
    }
    
    # Gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript;
    
    client_max_body_size 20M;
    
    access_log /www/wwwlogs/h5.haokajiyun.com.log;
    error_log /www/wwwlogs/h5.haokajiyun.com.error.log;
}

# HTTP重定向
server {
    listen 80;
    server_name h5.haokajiyun.com;
    return 301 https://$server_name$request_uri;
}
```

### **方法B: 命令行配置**

1. **编辑Nginx配置文件**:
```bash
nano /etc/nginx/sites-available/h5.haokajiyun.com
```

2. **复制上述配置内容**

3. **启用站点**:
```bash
ln -s /etc/nginx/sites-available/h5.haokajiyun.com /etc/nginx/sites-enabled/
```

## 🔄 **第三步: 重启服务**

### **宝塔面板**
1. 在网站设置页面点击"保存"
2. 或在"软件商店" → "Nginx" → "重载配置"

### **命令行**
```bash
# 测试配置
nginx -t

# 重载配置
nginx -s reload

# 或重启Nginx
systemctl restart nginx
```

## ✅ **第四步: 验证部署**

### **1. 访问测试**
- **后台管理**: https://h5.haokajiyun.com/admin
- **API接口**: https://h5.haokajiyun.com/api
- **根目录**: https://h5.haokajiyun.com (应自动跳转到/admin)

### **2. 功能检查**
- ✅ 页面正常加载，无空白
- ✅ 左侧导航菜单正常
- ✅ 产品管理功能正常
- ✅ 订单管理功能正常
- ✅ API接口调用成功

### **3. 浏览器检查**
按F12打开开发者工具：
- **Console**: 无错误信息
- **Network**: API请求正常返回
- **Sources**: 静态资源加载正常

## 🛠️ **故障排除**

### **问题1: 页面404错误**
```bash
# 检查文件是否存在
ls -la /www/wwwroot/h5.haokajiyun.com/admin/

# 检查权限
ls -la /www/wwwroot/h5.haokajiyun.com/admin/index.html
```

### **问题2: API请求失败**
```bash
# 检查API服务
curl https://h5.haokajiyun.com/api/v1/products

# 检查PHP-FPM
systemctl status php8.3-fpm

# 检查错误日志
tail -f /www/wwwlogs/h5.haokajiyun.com.error.log
```

### **问题3: 静态资源404**
```bash
# 检查static目录
ls -la /www/wwwroot/h5.haokajiyun.com/admin/static/

# 检查Nginx配置
nginx -t
```

### **问题4: CORS错误**
确认Nginx配置中包含CORS头设置，并重载配置：
```bash
nginx -s reload
```

## 📊 **性能优化**

### **1. 启用Gzip压缩**
已在配置中包含，压缩文本文件可减少70%传输大小

### **2. 静态资源缓存**
- CSS/JS文件: 1年缓存
- HTML文件: 不缓存
- 图片文件: 1年缓存

### **3. HTTP/2支持**
已启用，提升并发请求性能

## 🔒 **安全配置**

### **1. SSL/TLS**
- 强制HTTPS访问
- 现代加密算法
- 安全头设置

### **2. 文件访问控制**
- 禁止访问.env等敏感文件
- 限制上传文件大小

## 📞 **技术支持**

### **重要路径**
- **网站根目录**: `/www/wwwroot/h5.haokajiyun.com/public`
- **Admin目录**: `/www/wwwroot/h5.haokajiyun.com/admin`
- **Nginx配置**: `/etc/nginx/sites-available/h5.haokajiyun.com`
- **错误日志**: `/www/wwwlogs/h5.haokajiyun.com.error.log`

### **常用命令**
```bash
# 检查Nginx状态
systemctl status nginx

# 重载Nginx配置
nginx -s reload

# 查看错误日志
tail -f /www/wwwlogs/h5.haokajiyun.com.error.log

# 测试API
curl -X GET https://h5.haokajiyun.com/api/v1/products
```

---

## 🎉 **部署完成**

按照以上步骤完成后，您的后台管理系统将在以下地址可用：

**🔗 访问地址**: https://h5.haokajiyun.com/admin

**🎯 下一步**: 测试所有功能并开始使用！

{"ast": null, "code": "import _objectSpread from\"G:/phpstudy_pro/WWW/admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Card,Table,Button,Space,Typography,Tag,Modal,Form,Input,message,Popconfirm}from'antd';import{PlusOutlined,EditOutlined,DeleteOutlined,FolderOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;// 模拟数据\nconst mockCategories=[{id:1,name:'中国电信',description:'中国电信运营商套餐产品',productCount:28,status:'active',createdAt:'2024-01-01'},{id:2,name:'中国联通',description:'中国联通运营商套餐产品',productCount:22,status:'active',createdAt:'2024-01-02'},{id:3,name:'中国移动',description:'中国移动运营商套餐产品',productCount:35,status:'active',createdAt:'2024-01-03'},{id:4,name:'中国广电',description:'中国广电运营商套餐产品',productCount:8,status:'active',createdAt:'2024-01-04'}];const CategoryManagement=()=>{const[categories,setCategories]=useState([]);const[loading,setLoading]=useState(false);const[modalVisible,setModalVisible]=useState(false);const[editingCategory,setEditingCategory]=useState(null);const[form]=Form.useForm();// 从localStorage加载数据\nconst loadCategories=()=>{try{const savedCategories=localStorage.getItem('categories');if(savedCategories){const parsedCategories=JSON.parse(savedCategories);setCategories(parsedCategories);}else{// 如果没有保存的数据，使用默认数据并保存\nsetCategories(mockCategories);localStorage.setItem('categories',JSON.stringify(mockCategories));}}catch(error){console.error('加载分类数据失败:',error);setCategories(mockCategories);}};// 保存数据到localStorage\nconst saveCategories=newCategories=>{try{localStorage.setItem('categories',JSON.stringify(newCategories));setCategories(newCategories);}catch(error){console.error('保存分类数据失败:',error);message.error('保存失败，请重试');}};// 组件挂载时加载数据\nReact.useEffect(()=>{loadCategories();},[]);// 重置数据到默认状态\nconst handleResetData=()=>{Modal.confirm({title:'确认重置',content:'确定要重置所有分类数据到默认状态吗？此操作不可恢复。',onOk:()=>{saveCategories(mockCategories);// 触发自定义事件通知其他组件分类数据已更新\nwindow.dispatchEvent(new CustomEvent('categoriesUpdated'));message.success('数据已重置');}});};// 表格列定义\nconst columns=[{title:'分类ID',dataIndex:'id',key:'id',width:80,render:text=>/*#__PURE__*/_jsx(Tag,{color:\"blue\",style:{fontFamily:'monospace',fontWeight:'bold'},children:text})},{title:'分类名称',dataIndex:'name',key:'name',render:text=>/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(FolderOutlined,{style:{color:'#1890ff'}}),/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:600},children:text})]})},{title:'描述',dataIndex:'description',key:'description',ellipsis:true},{title:'产品数量',dataIndex:'productCount',key:'productCount',width:100,render:count=>/*#__PURE__*/_jsxs(Tag,{color:count>0?'green':'default',children:[count,\" \\u4E2A\\u4EA7\\u54C1\"]})},{title:'状态',dataIndex:'status',key:'status',width:100,render:status=>/*#__PURE__*/_jsx(Tag,{color:status==='active'?'success':'default',children:status==='active'?'启用':'禁用'})},{title:'创建时间',dataIndex:'createdAt',key:'createdAt',width:120},{title:'操作',key:'action',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record),children:\"\\u7F16\\u8F91\"}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u5206\\u7C7B\\u5417\\uFF1F\",description:\"\\u5220\\u9664\\u540E\\u65E0\\u6CD5\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\u3002\",onConfirm:()=>handleDelete(record.id),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Button,{danger:true,size:\"small\",icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),children:\"\\u5220\\u9664\"})})]})}];// 新增分类\nconst handleAdd=()=>{setEditingCategory(null);form.resetFields();setModalVisible(true);};// 编辑分类\nconst handleEdit=category=>{setEditingCategory(category);form.setFieldsValue(category);setModalVisible(true);};// 删除分类\nconst handleDelete=id=>{const newCategories=categories.filter(cat=>cat.id!==id);saveCategories(newCategories);// 触发自定义事件通知其他组件分类数据已更新\nwindow.dispatchEvent(new CustomEvent('categoriesUpdated'));message.success('删除成功');};// 保存分类\nconst handleSave=async()=>{try{const values=await form.validateFields();let newCategories;if(editingCategory){// 编辑\nnewCategories=categories.map(cat=>cat.id===editingCategory.id?_objectSpread(_objectSpread({},cat),values):cat);message.success('更新成功');}else{// 新增\nconst newCategory=_objectSpread(_objectSpread({id:categories.length>0?Math.max(...categories.map(c=>c.id))+1:1},values),{},{productCount:0,status:'active',createdAt:new Date().toISOString().split('T')[0]});newCategories=[...categories,newCategory];message.success('新增成功');}saveCategories(newCategories);// 触发自定义事件通知其他组件分类数据已更新\nwindow.dispatchEvent(new CustomEvent('categoriesUpdated'));setModalVisible(false);}catch(error){console.error('保存失败:',error);}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Title,{level:2,style:{margin:0,color:'#1890ff'},children:\"\\u5206\\u7C7B\\u7BA1\\u7406\"}),/*#__PURE__*/_jsx(Typography.Text,{type:\"secondary\",children:\"\\u7BA1\\u7406\\u4EA7\\u54C1\\u5206\\u7C7B\\u4FE1\\u606F\"})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:16},children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Typography.Text,{strong:true,children:[\"\\u5171 \",categories.length,\" \\u4E2A\\u5206\\u7C7B\"]})}),/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleResetData,style:{color:'#666'},children:\"\\u91CD\\u7F6E\\u6570\\u636E\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAdd,children:\"\\u65B0\\u589E\\u5206\\u7C7B\"})]})]}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:categories,rowKey:\"id\",loading:loading,pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u603B\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsx(Modal,{title:editingCategory?'编辑分类':'新增分类',open:modalVisible,onOk:handleSave,onCancel:()=>setModalVisible(false),okText:\"\\u4FDD\\u5B58\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsxs(Form,{form:form,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"\\u5206\\u7C7B\\u540D\\u79F0\",rules:[{required:true,message:'请输入分类名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5206\\u7C7B\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u5206\\u7C7B\\u63CF\\u8FF0\",rules:[{required:true,message:'请输入分类描述'}],children:/*#__PURE__*/_jsx(Input.TextArea,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5206\\u7C7B\\u63CF\\u8FF0\",rows:3})})]})})]});};export default CategoryManagement;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport React, { useContext } from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useMessage from '../message/useMessage';\nimport useModal from '../modal/useModal';\nimport useNotification from '../notification/useNotification';\nimport AppContext, { AppConfigContext } from './context';\nimport useStyle from './style';\nconst App = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    children,\n    className,\n    rootClassName,\n    message,\n    notification,\n    style,\n    component = 'div'\n  } = props;\n  const {\n    direction,\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('app', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const customClassName = classNames(hashId, prefixCls, className, rootClassName, cssVarCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  });\n  const appConfig = useContext(AppConfigContext);\n  const mergedAppConfig = React.useMemo(() => ({\n    message: Object.assign(Object.assign({}, appConfig.message), message),\n    notification: Object.assign(Object.assign({}, appConfig.notification), notification)\n  }), [message, notification, appConfig.message, appConfig.notification]);\n  const [messageApi, messageContextHolder] = useMessage(mergedAppConfig.message);\n  const [notificationApi, notificationContextHolder] = useNotification(mergedAppConfig.notification);\n  const [ModalApi, ModalContextHolder] = useModal();\n  const memoizedContextValue = React.useMemo(() => ({\n    message: messageApi,\n    notification: notificationApi,\n    modal: ModalApi\n  }), [messageApi, notificationApi, ModalApi]);\n  // https://github.com/ant-design/ant-design/issues/48802#issuecomment-**********\n  devUseWarning('App')(!(cssVarCls && component === false), 'usage', 'When using cssVar, ensure `component` is assigned a valid React component string.');\n  // ============================ Render ============================\n  const Component = component === false ? React.Fragment : component;\n  const rootProps = {\n    className: customClassName,\n    style\n  };\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AppContext.Provider, {\n    value: memoizedContextValue\n  }, /*#__PURE__*/React.createElement(AppConfigContext.Provider, {\n    value: mergedAppConfig\n  }, /*#__PURE__*/React.createElement(Component, Object.assign({}, component === false ? undefined : rootProps), ModalContextHolder, messageContextHolder, notificationContextHolder, children))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  App.displayName = 'App';\n}\nexport default App;", "map": {"version": 3, "names": ["React", "useContext", "classNames", "devUseW<PERSON>ning", "ConfigContext", "useMessage", "useModal", "useNotification", "AppContext", "AppConfigContext", "useStyle", "App", "props", "prefixCls", "customizePrefixCls", "children", "className", "rootClassName", "message", "notification", "style", "component", "direction", "getPrefixCls", "wrapCSSVar", "hashId", "cssVarCls", "customClassName", "appConfig", "mergedAppConfig", "useMemo", "Object", "assign", "messageApi", "messageContextHolder", "notificationApi", "notificationContextHolder", "<PERSON><PERSON><PERSON><PERSON>", "ModalContextHolder", "memoizedContextValue", "modal", "Component", "Fragment", "rootProps", "createElement", "Provider", "value", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/app/App.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useMessage from '../message/useMessage';\nimport useModal from '../modal/useModal';\nimport useNotification from '../notification/useNotification';\nimport AppContext, { AppConfigContext } from './context';\nimport useStyle from './style';\nconst App = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    children,\n    className,\n    rootClassName,\n    message,\n    notification,\n    style,\n    component = 'div'\n  } = props;\n  const {\n    direction,\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('app', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const customClassName = classNames(hashId, prefixCls, className, rootClassName, cssVarCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  });\n  const appConfig = useContext(AppConfigContext);\n  const mergedAppConfig = React.useMemo(() => ({\n    message: Object.assign(Object.assign({}, appConfig.message), message),\n    notification: Object.assign(Object.assign({}, appConfig.notification), notification)\n  }), [message, notification, appConfig.message, appConfig.notification]);\n  const [messageApi, messageContextHolder] = useMessage(mergedAppConfig.message);\n  const [notificationApi, notificationContextHolder] = useNotification(mergedAppConfig.notification);\n  const [ModalApi, ModalContextHolder] = useModal();\n  const memoizedContextValue = React.useMemo(() => ({\n    message: messageApi,\n    notification: notificationApi,\n    modal: ModalApi\n  }), [messageApi, notificationApi, ModalApi]);\n  // https://github.com/ant-design/ant-design/issues/48802#issuecomment-**********\n  devUseWarning('App')(!(cssVarCls && component === false), 'usage', 'When using cssVar, ensure `component` is assigned a valid React component string.');\n  // ============================ Render ============================\n  const Component = component === false ? React.Fragment : component;\n  const rootProps = {\n    className: customClassName,\n    style\n  };\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AppContext.Provider, {\n    value: memoizedContextValue\n  }, /*#__PURE__*/React.createElement(AppConfigContext.Provider, {\n    value: mergedAppConfig\n  }, /*#__PURE__*/React.createElement(Component, Object.assign({}, component === false ? undefined : rootProps), ModalContextHolder, messageContextHolder, notificationContextHolder, children))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  App.displayName = 'App';\n}\nexport default App;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,UAAU,IAAIC,gBAAgB,QAAQ,WAAW;AACxD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,GAAG,GAAGC,KAAK,IAAI;EACnB,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,QAAQ;IACRC,SAAS;IACTC,aAAa;IACbC,OAAO;IACPC,YAAY;IACZC,KAAK;IACLC,SAAS,GAAG;EACd,CAAC,GAAGT,KAAK;EACT,MAAM;IACJU,SAAS;IACTC;EACF,CAAC,GAAGtB,UAAU,CAACG,aAAa,CAAC;EAC7B,MAAMS,SAAS,GAAGU,YAAY,CAAC,KAAK,EAAET,kBAAkB,CAAC;EACzD,MAAM,CAACU,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAACG,SAAS,CAAC;EAC3D,MAAMc,eAAe,GAAGzB,UAAU,CAACuB,MAAM,EAAEZ,SAAS,EAAEG,SAAS,EAAEC,aAAa,EAAES,SAAS,EAAE;IACzF,CAAC,GAAGb,SAAS,MAAM,GAAGS,SAAS,KAAK;EACtC,CAAC,CAAC;EACF,MAAMM,SAAS,GAAG3B,UAAU,CAACQ,gBAAgB,CAAC;EAC9C,MAAMoB,eAAe,GAAG7B,KAAK,CAAC8B,OAAO,CAAC,OAAO;IAC3CZ,OAAO,EAAEa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,SAAS,CAACV,OAAO,CAAC,EAAEA,OAAO,CAAC;IACrEC,YAAY,EAAEY,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,SAAS,CAACT,YAAY,CAAC,EAAEA,YAAY;EACrF,CAAC,CAAC,EAAE,CAACD,OAAO,EAAEC,YAAY,EAAES,SAAS,CAACV,OAAO,EAAEU,SAAS,CAACT,YAAY,CAAC,CAAC;EACvE,MAAM,CAACc,UAAU,EAAEC,oBAAoB,CAAC,GAAG7B,UAAU,CAACwB,eAAe,CAACX,OAAO,CAAC;EAC9E,MAAM,CAACiB,eAAe,EAAEC,yBAAyB,CAAC,GAAG7B,eAAe,CAACsB,eAAe,CAACV,YAAY,CAAC;EAClG,MAAM,CAACkB,QAAQ,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,CAAC;EACjD,MAAMiC,oBAAoB,GAAGvC,KAAK,CAAC8B,OAAO,CAAC,OAAO;IAChDZ,OAAO,EAAEe,UAAU;IACnBd,YAAY,EAAEgB,eAAe;IAC7BK,KAAK,EAAEH;EACT,CAAC,CAAC,EAAE,CAACJ,UAAU,EAAEE,eAAe,EAAEE,QAAQ,CAAC,CAAC;EAC5C;EACAlC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAEuB,SAAS,IAAIL,SAAS,KAAK,KAAK,CAAC,EAAE,OAAO,EAAE,mFAAmF,CAAC;EACvJ;EACA,MAAMoB,SAAS,GAAGpB,SAAS,KAAK,KAAK,GAAGrB,KAAK,CAAC0C,QAAQ,GAAGrB,SAAS;EAClE,MAAMsB,SAAS,GAAG;IAChB3B,SAAS,EAAEW,eAAe;IAC1BP;EACF,CAAC;EACD,OAAOI,UAAU,CAAC,aAAaxB,KAAK,CAAC4C,aAAa,CAACpC,UAAU,CAACqC,QAAQ,EAAE;IACtEC,KAAK,EAAEP;EACT,CAAC,EAAE,aAAavC,KAAK,CAAC4C,aAAa,CAACnC,gBAAgB,CAACoC,QAAQ,EAAE;IAC7DC,KAAK,EAAEjB;EACT,CAAC,EAAE,aAAa7B,KAAK,CAAC4C,aAAa,CAACH,SAAS,EAAEV,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEX,SAAS,KAAK,KAAK,GAAG0B,SAAS,GAAGJ,SAAS,CAAC,EAAEL,kBAAkB,EAAEJ,oBAAoB,EAAEE,yBAAyB,EAAErB,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClM,CAAC;AACD,IAAIiC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCvC,GAAG,CAACwC,WAAW,GAAG,KAAK;AACzB;AACA,eAAexC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { Provider as MotionProvider } from 'rc-motion';\nimport { useToken } from '../theme/internal';\nconst MotionCacheContext = /*#__PURE__*/React.createContext(true);\nif (process.env.NODE_ENV !== 'production') {\n  MotionCacheContext.displayName = 'MotionCacheContext';\n}\nexport default function MotionWrapper(props) {\n  const parentMotion = React.useContext(MotionCacheContext);\n  const {\n    children\n  } = props;\n  const [, token] = useToken();\n  const {\n    motion\n  } = token;\n  const needWrapMotionProviderRef = React.useRef(false);\n  needWrapMotionProviderRef.current || (needWrapMotionProviderRef.current = parentMotion !== motion);\n  if (needWrapMotionProviderRef.current) {\n    return /*#__PURE__*/React.createElement(MotionCacheContext.Provider, {\n      value: motion\n    }, /*#__PURE__*/React.createElement(MotionProvider, {\n      motion: motion\n    }, children));\n  }\n  return children;\n}", "map": {"version": 3, "names": ["React", "Provider", "MotionProvider", "useToken", "MotionCacheContext", "createContext", "process", "env", "NODE_ENV", "displayName", "MotionWrapper", "props", "parentMotion", "useContext", "children", "token", "motion", "needWrapMotionProviderRef", "useRef", "current", "createElement", "value"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/config-provider/MotionWrapper.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { Provider as MotionProvider } from 'rc-motion';\nimport { useToken } from '../theme/internal';\nconst MotionCacheContext = /*#__PURE__*/React.createContext(true);\nif (process.env.NODE_ENV !== 'production') {\n  MotionCacheContext.displayName = 'MotionCacheContext';\n}\nexport default function MotionWrapper(props) {\n  const parentMotion = React.useContext(MotionCacheContext);\n  const {\n    children\n  } = props;\n  const [, token] = useToken();\n  const {\n    motion\n  } = token;\n  const needWrapMotionProviderRef = React.useRef(false);\n  needWrapMotionProviderRef.current || (needWrapMotionProviderRef.current = parentMotion !== motion);\n  if (needWrapMotionProviderRef.current) {\n    return /*#__PURE__*/React.createElement(MotionCacheContext.Provider, {\n      value: motion\n    }, /*#__PURE__*/React.createElement(MotionProvider, {\n      motion: motion\n    }, children));\n  }\n  return children;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,IAAIC,cAAc,QAAQ,WAAW;AACtD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,MAAMC,kBAAkB,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAAC,IAAI,CAAC;AACjE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,kBAAkB,CAACK,WAAW,GAAG,oBAAoB;AACvD;AACA,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,MAAMC,YAAY,GAAGZ,KAAK,CAACa,UAAU,CAACT,kBAAkB,CAAC;EACzD,MAAM;IACJU;EACF,CAAC,GAAGH,KAAK;EACT,MAAM,GAAGI,KAAK,CAAC,GAAGZ,QAAQ,CAAC,CAAC;EAC5B,MAAM;IACJa;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,yBAAyB,GAAGjB,KAAK,CAACkB,MAAM,CAAC,KAAK,CAAC;EACrDD,yBAAyB,CAACE,OAAO,KAAKF,yBAAyB,CAACE,OAAO,GAAGP,YAAY,KAAKI,MAAM,CAAC;EAClG,IAAIC,yBAAyB,CAACE,OAAO,EAAE;IACrC,OAAO,aAAanB,KAAK,CAACoB,aAAa,CAAChB,kBAAkB,CAACH,QAAQ,EAAE;MACnEoB,KAAK,EAAEL;IACT,CAAC,EAAE,aAAahB,KAAK,CAACoB,aAAa,CAAClB,cAAc,EAAE;MAClDc,MAAM,EAAEA;IACV,CAAC,EAAEF,QAAQ,CAAC,CAAC;EACf;EACA,OAAOA,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
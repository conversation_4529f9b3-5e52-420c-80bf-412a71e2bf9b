{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\components\\\\ProductForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Form, Input, InputNumber, Select, Switch, Button, Row, Col, Card, Space, message } from 'antd';\n\n// 图标导入（预留用于未来功能）\n// import {\n//   UploadOutlined,\n//   InfoCircleOutlined,\n//   CopyOutlined,\n//   SaveOutlined,\n// } from '@ant-design/icons';\n\n// import { productApi } from '../services/api'; // 预留用于API调用\n\nimport { OPERATOR_OPTIONS, LOCATION_OPTIONS, PRODUCT_STATUS_OPTIONS } from '../utils/constants';\nimport { arrayToString, copyToClipboard } from '../utils/helpers';\n\n// 分类接口定义\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 临时常量定义\nconst LOGISTICS_OPTIONS = [{\n  label: '快递',\n  value: 'express'\n}, {\n  label: '自提',\n  value: 'pickup'\n}];\nconst PRODUCT_WEIGHT_OPTIONS = [{\n  label: '1',\n  value: 1\n}, {\n  label: '2',\n  value: 2\n}, {\n  label: '3',\n  value: 3\n}, {\n  label: '4',\n  value: 4\n}, {\n  label: '5',\n  value: 5\n}, {\n  label: '6',\n  value: 6\n}, {\n  label: '7',\n  value: 7\n}, {\n  label: '8',\n  value: 8\n}, {\n  label: '9',\n  value: 9\n}, {\n  label: '10',\n  value: 10\n}];\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst ProductForm = ({\n  form,\n  initialValues,\n  onSubmit,\n  loading = false\n}) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState('');\n  const [uploading, setUploading] = useState(false);\n  const [categories, setCategories] = useState([]);\n\n  // 获取分类数据\n  const loadCategories = () => {\n    try {\n      const savedCategories = localStorage.getItem('categories');\n      if (savedCategories) {\n        const categoriesData = JSON.parse(savedCategories);\n        // 只显示激活状态的分类\n        const activeCategories = categoriesData.filter(cat => cat.status === 'active');\n        setCategories(activeCategories);\n      }\n    } catch (error) {\n      console.error('加载分类数据失败:', error);\n    }\n  };\n\n  // 组件挂载时加载分类数据\n  useEffect(() => {\n    loadCategories();\n\n    // 监听localStorage变化，实时更新分类数据\n    const handleStorageChange = e => {\n      if (e.key === 'categories') {\n        loadCategories();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n\n    // 也监听自定义事件，用于同一页面内的更新\n    const handleCategoriesUpdate = () => {\n      loadCategories();\n    };\n    window.addEventListener('categoriesUpdated', handleCategoriesUpdate);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('categoriesUpdated', handleCategoriesUpdate);\n    };\n  }, []);\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (initialValues) {\n      const formData = {\n        ...initialValues,\n        monthly_fee: parseFloat(initialValues.monthly_fee),\n        general_traffic: parseFloat(initialValues.general_traffic),\n        directional_traffic: parseFloat(initialValues.directional_traffic),\n        tag1: initialValues.tags.tag1,\n        tag2: initialValues.tags.tag2,\n        tag3: initialValues.tags.tag3,\n        forbidden_areas: initialValues.forbidden_areas_array\n      };\n      form.setFieldsValue(formData);\n      setImageUrl(initialValues.main_image || '');\n    }\n  }, [initialValues, form]);\n\n  // 表单提交处理\n  const handleSubmit = async values => {\n    try {\n      const submitData = {\n        ...values,\n        main_image: values.main_image,\n        // 直接使用表单中的值\n        forbidden_areas: Array.isArray(values.forbidden_areas) ? arrayToString(values.forbidden_areas) : values.forbidden_areas,\n        no_delivery_areas: Array.isArray(values.no_delivery_areas) ? arrayToString(values.no_delivery_areas) : values.no_delivery_areas\n      };\n      await onSubmit(submitData);\n    } catch (error) {\n      console.error('表单提交失败:', error);\n    }\n  };\n\n  // 复制产品信息\n  const handleCopyProduct = async () => {\n    const values = form.getFieldsValue();\n    const productInfo = JSON.stringify(values, null, 2);\n    const success = await copyToClipboard(productInfo);\n    if (success) {\n      message.success('产品信息已复制到剪贴板');\n    } else {\n      message.error('复制失败');\n    }\n  };\n\n  // 保存草稿\n  const handleSaveDraft = () => {\n    const values = form.getFieldsValue();\n    localStorage.setItem('product_draft', JSON.stringify(values));\n    message.success('草稿已保存');\n  };\n\n  // 加载草稿\n  const handleLoadDraft = () => {\n    const draft = localStorage.getItem('product_draft');\n    if (draft) {\n      try {\n        const draftData = JSON.parse(draft);\n        form.setFieldsValue(draftData);\n        message.success('草稿已加载');\n      } catch (error) {\n        message.error('草稿格式错误');\n      }\n    } else {\n      message.info('没有找到草稿');\n    }\n  };\n\n  // 图片上传处理\n  const handleImageUpload = file => {\n    // 这里应该实现真实的图片上传逻辑\n    // 目前只是模拟\n    setUploading(true);\n    setTimeout(() => {\n      const mockUrl = URL.createObjectURL(file);\n      setImageUrl(mockUrl);\n      form.setFieldsValue({\n        main_image: mockUrl\n      });\n      setUploading(false);\n      message.success('图片上传成功');\n    }, 1000);\n    return false; // 阻止默认上传行为\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    layout: \"vertical\",\n    onFinish: handleSubmit,\n    initialValues: {\n      status: 'online',\n      need_id_photo: true,\n      sms_notification: false\n    },\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"name\",\n                label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n                rules: [{\n                  required: true,\n                  message: '请输入产品名称'\n                }, {\n                  max: 255,\n                  message: '产品名称不能超过255个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u540D\\u79F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"status\",\n                label: \"\\u4EA7\\u54C1\\u72B6\\u6001\",\n                rules: [{\n                  required: true,\n                  message: '请选择产品状态'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u72B6\\u6001\",\n                  children: PRODUCT_STATUS_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"description\",\n                label: \"\\u4EA7\\u54C1\\u4ECB\\u7ECD\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 3,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u4ECB\\u7ECD\",\n                  maxLength: 500,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"discount_details\",\n                label: \"\\u4F18\\u60E0\\u8BE6\\u60C5\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 2,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F18\\u60E0\\u8BE6\\u60C5\",\n                  maxLength: 300,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"delivery_method\",\n                label: \"\\u5FEB\\u9012\\u65B9\\u5F0F\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u5FEB\\u9012\\u65B9\\u5F0F\",\n                  children: [/*#__PURE__*/_jsxDEV(Option, {\n                    value: \"express\",\n                    children: \"\\u5FEB\\u9012\\u914D\\u9001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"ems\",\n                    children: \"EMS\\u90AE\\u653F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"sf\",\n                    children: \"\\u987A\\u4E30\\u5FEB\\u9012\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"yt\",\n                    children: \"\\u5706\\u901A\\u5FEB\\u9012\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"sto\",\n                    children: \"\\u7533\\u901A\\u5FEB\\u9012\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"zto\",\n                    children: \"\\u4E2D\\u901A\\u5FEB\\u9012\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"yunda\",\n                    children: \"\\u97F5\\u8FBE\\u5FEB\\u9012\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"pickup\",\n                    children: \"\\u81EA\\u63D0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"activation_method\",\n                label: \"\\u6FC0\\u6D3B\\u65B9\\u5F0F\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u6FC0\\u6D3B\\u65B9\\u5F0F\",\n                  children: [/*#__PURE__*/_jsxDEV(Option, {\n                    value: \"online\",\n                    children: \"\\u5728\\u7EBF\\u6FC0\\u6D3B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"app\",\n                    children: \"APP\\u6FC0\\u6D3B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"sms\",\n                    children: \"\\u77ED\\u4FE1\\u6FC0\\u6D3B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"call\",\n                    children: \"\\u7535\\u8BDD\\u6FC0\\u6D3B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Option, {\n                    value: \"offline\",\n                    children: \"\\u7EBF\\u4E0B\\u6FC0\\u6D3B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"recharge_link\",\n                label: \"\\u5145\\u503C\\u94FE\\u63A5\",\n                rules: [{\n                  type: 'url',\n                  message: '请输入有效的充值链接URL'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5145\\u503C\\u94FE\\u63A5\\uFF0C\\u5982\\uFF1Ahttps://example.com/recharge\",\n                  maxLength: 200,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"card_age_requirement\",\n                label: \"\\u529E\\u5361\\u5E74\\u9F84\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u529E\\u5361\\u5E74\\u9F84\\u8981\\u6C42\\uFF0C\\u5982\\uFF1A18-65\\u5C81\",\n                  maxLength: 50\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"no_delivery_areas\",\n                label: \"\\u4E0D\\u53D1\\u8D27\\u5730\\u533A\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  mode: \"tags\",\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4E0D\\u53D1\\u8D27\\u5730\\u533A\\uFF0C\\u652F\\u6301\\u591A\\u4E2A\",\n                  style: {\n                    width: '100%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"directional_scope\",\n                label: \"\\u5B9A\\u5411\\u8303\\u56F4\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 2,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B9A\\u5411\\u6D41\\u91CF\\u9002\\u7528\\u8303\\u56F4\\uFF0C\\u5982\\uFF1A\\u6296\\u97F3\\u3001\\u5FEB\\u624B\\u3001\\u5FAE\\u4FE1\\u7B49\",\n                  maxLength: 200,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"reactivation_cancellation\",\n                label: \"\\u590D\\u673A\\u53CA\\u6CE8\\u9500\\u65B9\\u5F0F\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 2,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u590D\\u673A\\u53CA\\u6CE8\\u9500\\u65B9\\u5F0F\\u8BF4\\u660E\",\n                  maxLength: 200,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5957\\u9910\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"monthly_fee\",\n                label: \"\\u5957\\u9910\\u6708\\u79DF\\uFF08\\u5143\\uFF09\",\n                rules: [{\n                  required: true,\n                  message: '请输入套餐月租'\n                }, {\n                  type: 'number',\n                  min: 0,\n                  message: '月租不能小于0'\n                }],\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6708\\u79DF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"general_traffic\",\n                label: \"\\u901A\\u7528\\u6D41\\u91CF\\uFF08G\\uFF09\",\n                rules: [{\n                  required: true,\n                  message: '请输入通用流量'\n                }, {\n                  type: 'number',\n                  min: 0,\n                  message: '流量不能小于0'\n                }],\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u901A\\u7528\\u6D41\\u91CF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"directional_traffic\",\n                label: \"\\u5B9A\\u5411\\u6D41\\u91CF\\uFF08G\\uFF09\",\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B9A\\u5411\\u6D41\\u91CF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"free_minutes\",\n                label: \"\\u514D\\u8D39\\u901A\\u8BDD\\uFF08\\u5206\\u949F\\uFF09\",\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u514D\\u8D39\\u901A\\u8BDD\\u65F6\\u957F\",\n                  min: 0,\n                  max: 99999\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"package_duration\",\n                label: \"\\u5957\\u9910\\u65F6\\u957F\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5957\\u9910\\u65F6\\u957F\\uFF0C\\u5982\\uFF1A1\\u4E2A\\u6708\\u30013\\u4E2A\\u6708\\u300112\\u4E2A\\u6708\\u7B49\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"product_weight\",\n                label: \"\\u4EA7\\u54C1\\u6743\\u91CD\",\n                tooltip: \"\\u6743\\u91CD\\u8D8A\\u9AD8\\uFF0C\\u4EA7\\u54C1\\u5728\\u5217\\u8868\\u4E2D\\u7684\\u6392\\u5E8F\\u8D8A\\u9760\\u524D\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u6743\\u91CD\",\n                  children: PRODUCT_WEIGHT_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8FD0\\u8425\\u5546\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"category_id\",\n                label: \"\\u4EA7\\u54C1\\u5206\\u7C7B\",\n                rules: [{\n                  required: true,\n                  message: '请选择产品分类'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u5206\\u7C7B\",\n                  showSearch: true,\n                  filterOption: (input, option) => {\n                    var _option$children;\n                    return option === null || option === void 0 ? void 0 : (_option$children = option.children) === null || _option$children === void 0 ? void 0 : _option$children.toLowerCase().includes(input.toLowerCase());\n                  },\n                  children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n                    value: category.id,\n                    children: category.name\n                  }, category.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"operator\",\n                label: \"\\u8FD0\\u8425\\u5546\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n                  children: OPERATOR_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"location\",\n                label: \"\\u5F52\\u5C5E\\u5730\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F52\\u5C5E\\u5730\",\n                  children: LOCATION_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5546\\u54C1\\u6807\\u7B7E\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag1\",\n                label: \"\\u6807\\u7B7E1\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E1\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag2\",\n                label: \"\\u6807\\u7B7E2\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E2\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag3\",\n                label: \"\\u6807\\u7B7E3\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E3\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7269\\u6D41\\u914D\\u7F6E\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"logistics_method\",\n                label: \"\\u7269\\u6D41\\u65B9\\u5F0F\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u7269\\u6D41\\u65B9\\u5F0F\",\n                  children: LOGISTICS_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"forbidden_areas\",\n                label: \"\\u7981\\u53D1\\u5730\\u533A\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  mode: \"tags\",\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u7981\\u53D1\\u5730\\u533A\\uFF0C\\u652F\\u6301\\u591A\\u4E2A\",\n                  style: {\n                    width: '100%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5176\\u4ED6\\u8BBE\\u7F6E\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"need_id_photo\",\n                label: \"\\u9700\\u8981\\u4E0A\\u4F20\\u8EAB\\u4EFD\\u8BC1\\u7167\\u7247\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"sms_notification\",\n                label: \"\\u5F00\\u542F\\u7528\\u6237\\u4E0B\\u5355\\u6210\\u529F\\u77ED\\u4FE1\\u63D0\\u9192\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"main_image\",\n                label: \"\\u4EA7\\u54C1\\u4E3B\\u56FE\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u4E3B\\u56FEURL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"detail_description\",\n                label: \"\\u5546\\u54C1\\u8BE6\\u60C5\\u56FE\",\n                tooltip: \"\\u8BF7\\u8F93\\u5165\\u56FE\\u7247\\u94FE\\u63A5URL\\uFF0C\\u652F\\u6301JPG\\u3001PNG\\u3001GIF\\u7B49\\u683C\\u5F0F\",\n                rules: [{\n                  type: 'url',\n                  message: '请输入有效的图片链接URL'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u8BE6\\u60C5\\u56FE\\u7247\\u94FE\\u63A5\\uFF0C\\u5982\\uFF1Ahttps://example.com/image.jpg\",\n                  maxLength: 500,\n                  showCount: true,\n                  onChange: e => {\n                    const url = e.target.value;\n                    if (url && (url.startsWith('http://') || url.startsWith('https://'))) {\n                      // 可以在这里添加图片预览逻辑\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                shouldUpdate: true,\n                children: ({\n                  getFieldValue\n                }) => {\n                  const imageUrl = getFieldValue('detail_description');\n                  if (imageUrl && (imageUrl.startsWith('http://') || imageUrl.startsWith('https://'))) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: 8,\n                          color: '#666',\n                          fontSize: '14px'\n                        },\n                        children: \"\\u56FE\\u7247\\u9884\\u89C8\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 682,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: imageUrl,\n                        alt: \"\\u5546\\u54C1\\u8BE6\\u60C5\\u56FE\\u9884\\u89C8\",\n                        style: {\n                          maxWidth: '100%',\n                          maxHeight: '300px',\n                          border: '1px solid #d9d9d9',\n                          borderRadius: '6px',\n                          objectFit: 'contain'\n                        },\n                        onError: e => {\n                          e.currentTarget.style.display = 'none';\n                        },\n                        onLoad: e => {\n                          e.currentTarget.style.display = 'block';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 683,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 25\n                    }, this);\n                  }\n                  return null;\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              size: \"large\",\n              children: initialValues ? '更新产品' : '创建产品'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"large\",\n              onClick: () => form.resetFields(),\n              children: \"\\u91CD\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductForm, \"tQ0p/TMYRMYuI2d9fAqcD5HS9Bw=\");\n_c = ProductForm;\nexport default ProductForm;\nvar _c;\n$RefreshReg$(_c, \"ProductForm\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Form", "Input", "InputNumber", "Select", "Switch", "<PERSON><PERSON>", "Row", "Col", "Card", "Space", "message", "OPERATOR_OPTIONS", "LOCATION_OPTIONS", "PRODUCT_STATUS_OPTIONS", "arrayToString", "copyToClipboard", "jsxDEV", "_jsxDEV", "LOGISTICS_OPTIONS", "label", "value", "PRODUCT_WEIGHT_OPTIONS", "Option", "TextArea", "ProductForm", "form", "initialValues", "onSubmit", "loading", "_s", "imageUrl", "setImageUrl", "uploading", "setUploading", "categories", "setCategories", "loadCategories", "savedCategories", "localStorage", "getItem", "categoriesData", "JSON", "parse", "activeCategories", "filter", "cat", "status", "error", "console", "handleStorageChange", "e", "key", "window", "addEventListener", "handleCategoriesUpdate", "removeEventListener", "formData", "monthly_fee", "parseFloat", "general_traffic", "directional_traffic", "tag1", "tags", "tag2", "tag3", "forbidden_areas", "forbidden_areas_array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "main_image", "handleSubmit", "values", "submitData", "Array", "isArray", "no_delivery_areas", "handleCopyProduct", "getFieldsValue", "productInfo", "stringify", "success", "handleSaveDraft", "setItem", "handleLoadDraft", "draft", "draftData", "info", "handleImageUpload", "file", "setTimeout", "mockUrl", "URL", "createObjectURL", "layout", "onFinish", "need_id_photo", "sms_notification", "children", "gutter", "span", "title", "style", "marginBottom", "<PERSON><PERSON>", "name", "rules", "required", "max", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "option", "rows", "max<PERSON><PERSON><PERSON>", "showCount", "type", "mode", "width", "min", "precision", "tooltip", "showSearch", "filterOption", "input", "_option$children", "toLowerCase", "includes", "category", "id", "valuePropName", "onChange", "url", "target", "startsWith", "shouldUpdate", "getFieldValue", "marginTop", "color", "fontSize", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "border", "borderRadius", "objectFit", "onError", "currentTarget", "display", "onLoad", "htmlType", "size", "onClick", "resetFields", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/components/ProductForm.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport {\n  Form,\n  Input,\n  InputNumber,\n  Select,\n  Switch,\n  Button,\n  Row,\n  Col,\n  Card,\n  Space,\n  message,\n} from 'antd';\nimport RichTextEditor from './RichTextEditor';\n// 图标导入（预留用于未来功能）\n// import {\n//   UploadOutlined,\n//   InfoCircleOutlined,\n//   CopyOutlined,\n//   SaveOutlined,\n// } from '@ant-design/icons';\nimport type { FormInstance } from 'antd/es/form';\nimport type { UploadFile } from 'antd/es/upload/interface';\n// import { productApi } from '../services/api'; // 预留用于API调用\nimport type { Product, ProductRequest } from '../types/product';\nimport {\n  OPERATOR_OPTIONS,\n  LOCATION_OPTIONS,\n  PRODUCT_STATUS_OPTIONS,\n} from '../utils/constants';\nimport { arrayToString, copyToClipboard } from '../utils/helpers';\n\n// 分类接口定义\ninterface Category {\n  id: number;\n  name: string;\n  description: string;\n  productCount: number;\n  status: 'active' | 'inactive';\n  createdAt: string;\n}\n\n// 临时常量定义\nconst LOGISTICS_OPTIONS = [\n  { label: '快递', value: 'express' },\n  { label: '自提', value: 'pickup' },\n];\n\n\n\nconst PRODUCT_WEIGHT_OPTIONS = [\n  { label: '1', value: 1 },\n  { label: '2', value: 2 },\n  { label: '3', value: 3 },\n  { label: '4', value: 4 },\n  { label: '5', value: 5 },\n  { label: '6', value: 6 },\n  { label: '7', value: 7 },\n  { label: '8', value: 8 },\n  { label: '9', value: 9 },\n  { label: '10', value: 10 },\n];\n\nconst { Option } = Select;\nconst { TextArea } = Input;\n\ninterface ProductFormProps {\n  form: FormInstance;\n  initialValues?: Product;\n  onSubmit: (values: ProductRequest) => Promise<void>;\n  loading?: boolean;\n}\n\nconst ProductForm: React.FC<ProductFormProps> = ({\n  form,\n  initialValues,\n  onSubmit,\n  loading = false,\n}) => {\n  const [imageUrl, setImageUrl] = useState<string>('');\n  const [uploading, setUploading] = useState(false);\n  const [categories, setCategories] = useState<Category[]>([]);\n\n  // 获取分类数据\n  const loadCategories = () => {\n    try {\n      const savedCategories = localStorage.getItem('categories');\n      if (savedCategories) {\n        const categoriesData = JSON.parse(savedCategories);\n        // 只显示激活状态的分类\n        const activeCategories = categoriesData.filter((cat: Category) => cat.status === 'active');\n        setCategories(activeCategories);\n      }\n    } catch (error) {\n      console.error('加载分类数据失败:', error);\n    }\n  };\n\n  // 组件挂载时加载分类数据\n  useEffect(() => {\n    loadCategories();\n\n    // 监听localStorage变化，实时更新分类数据\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'categories') {\n        loadCategories();\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n\n    // 也监听自定义事件，用于同一页面内的更新\n    const handleCategoriesUpdate = () => {\n      loadCategories();\n    };\n\n    window.addEventListener('categoriesUpdated', handleCategoriesUpdate);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('categoriesUpdated', handleCategoriesUpdate);\n    };\n  }, []);\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (initialValues) {\n      const formData = {\n        ...initialValues,\n        monthly_fee: parseFloat(initialValues.monthly_fee),\n        general_traffic: parseFloat(initialValues.general_traffic),\n        directional_traffic: parseFloat(initialValues.directional_traffic),\n        tag1: initialValues.tags.tag1,\n        tag2: initialValues.tags.tag2,\n        tag3: initialValues.tags.tag3,\n        forbidden_areas: initialValues.forbidden_areas_array,\n      };\n      form.setFieldsValue(formData);\n      setImageUrl(initialValues.main_image || '');\n    }\n  }, [initialValues, form]);\n\n  // 表单提交处理\n  const handleSubmit = async (values: any) => {\n    try {\n      const submitData: ProductRequest = {\n        ...values,\n        main_image: values.main_image, // 直接使用表单中的值\n        forbidden_areas: Array.isArray(values.forbidden_areas)\n          ? arrayToString(values.forbidden_areas)\n          : values.forbidden_areas,\n        no_delivery_areas: Array.isArray(values.no_delivery_areas)\n          ? arrayToString(values.no_delivery_areas)\n          : values.no_delivery_areas,\n      };\n\n      await onSubmit(submitData);\n    } catch (error) {\n      console.error('表单提交失败:', error);\n    }\n  };\n\n  // 复制产品信息\n  const handleCopyProduct = async () => {\n    const values = form.getFieldsValue();\n    const productInfo = JSON.stringify(values, null, 2);\n    const success = await copyToClipboard(productInfo);\n    if (success) {\n      message.success('产品信息已复制到剪贴板');\n    } else {\n      message.error('复制失败');\n    }\n  };\n\n  // 保存草稿\n  const handleSaveDraft = () => {\n    const values = form.getFieldsValue();\n    localStorage.setItem('product_draft', JSON.stringify(values));\n    message.success('草稿已保存');\n  };\n\n  // 加载草稿\n  const handleLoadDraft = () => {\n    const draft = localStorage.getItem('product_draft');\n    if (draft) {\n      try {\n        const draftData = JSON.parse(draft);\n        form.setFieldsValue(draftData);\n        message.success('草稿已加载');\n      } catch (error) {\n        message.error('草稿格式错误');\n      }\n    } else {\n      message.info('没有找到草稿');\n    }\n  };\n\n  // 图片上传处理\n  const handleImageUpload = (file: UploadFile) => {\n    // 这里应该实现真实的图片上传逻辑\n    // 目前只是模拟\n    setUploading(true);\n    setTimeout(() => {\n      const mockUrl = URL.createObjectURL(file as any);\n      setImageUrl(mockUrl);\n      form.setFieldsValue({ main_image: mockUrl });\n      setUploading(false);\n      message.success('图片上传成功');\n    }, 1000);\n    return false; // 阻止默认上传行为\n  };\n\n  return (\n    <Form\n      form={form}\n      layout=\"vertical\"\n      onFinish={handleSubmit}\n      initialValues={{\n        status: 'online',\n        need_id_photo: true,\n        sms_notification: false,\n      }}\n    >\n      <Row gutter={24}>\n        {/* 基本信息 */}\n        <Col span={24}>\n          <Card title=\"基本信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"name\"\n                  label=\"产品名称\"\n                  rules={[\n                    { required: true, message: '请输入产品名称' },\n                    { max: 255, message: '产品名称不能超过255个字符' },\n                  ]}\n                >\n                  <Input placeholder=\"请输入产品名称\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"status\"\n                  label=\"产品状态\"\n                  rules={[{ required: true, message: '请选择产品状态' }]}\n                >\n                  <Select placeholder=\"请选择产品状态\">\n                    {PRODUCT_STATUS_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"description\"\n                  label=\"产品介绍\"\n                >\n                  <TextArea\n                    rows={3}\n                    placeholder=\"请输入产品介绍\"\n                    maxLength={500}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"discount_details\"\n                  label=\"优惠详情\"\n                >\n                  <TextArea\n                    rows={2}\n                    placeholder=\"请输入优惠详情\"\n                    maxLength={300}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"delivery_method\"\n                  label=\"快递方式\"\n                >\n                  <Select placeholder=\"请选择快递方式\">\n                    <Option value=\"express\">快递配送</Option>\n                    <Option value=\"ems\">EMS邮政</Option>\n                    <Option value=\"sf\">顺丰快递</Option>\n                    <Option value=\"yt\">圆通快递</Option>\n                    <Option value=\"sto\">申通快递</Option>\n                    <Option value=\"zto\">中通快递</Option>\n                    <Option value=\"yunda\">韵达快递</Option>\n                    <Option value=\"pickup\">自提</Option>\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"activation_method\"\n                  label=\"激活方式\"\n                >\n                  <Select placeholder=\"请选择激活方式\">\n                    <Option value=\"online\">在线激活</Option>\n                    <Option value=\"app\">APP激活</Option>\n                    <Option value=\"sms\">短信激活</Option>\n                    <Option value=\"call\">电话激活</Option>\n                    <Option value=\"offline\">线下激活</Option>\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"recharge_link\"\n                  label=\"充值链接\"\n                  rules={[\n                    {\n                      type: 'url',\n                      message: '请输入有效的充值链接URL',\n                    },\n                  ]}\n                >\n                  <Input\n                    placeholder=\"请输入充值链接，如：https://example.com/recharge\"\n                    maxLength={200}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"card_age_requirement\"\n                  label=\"办卡年龄\"\n                >\n                  <Input\n                    placeholder=\"请输入办卡年龄要求，如：18-65岁\"\n                    maxLength={50}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"no_delivery_areas\"\n                  label=\"不发货地区\"\n                >\n                  <Select\n                    mode=\"tags\"\n                    placeholder=\"请输入不发货地区，支持多个\"\n                    style={{ width: '100%' }}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"directional_scope\"\n                  label=\"定向范围\"\n                >\n                  <TextArea\n                    rows={2}\n                    placeholder=\"请输入定向流量适用范围，如：抖音、快手、微信等\"\n                    maxLength={200}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"reactivation_cancellation\"\n                  label=\"复机及注销方式\"\n                >\n                  <TextArea\n                    rows={2}\n                    placeholder=\"请输入复机及注销方式说明\"\n                    maxLength={200}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 套餐信息 */}\n        <Col span={24}>\n          <Card title=\"套餐信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"monthly_fee\"\n                  label=\"套餐月租（元）\"\n                  rules={[\n                    { required: true, message: '请输入套餐月租' },\n                    { type: 'number', min: 0, message: '月租不能小于0' },\n                  ]}\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入月租\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"general_traffic\"\n                  label=\"通用流量（G）\"\n                  rules={[\n                    { required: true, message: '请输入通用流量' },\n                    { type: 'number', min: 0, message: '流量不能小于0' },\n                  ]}\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入通用流量\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"directional_traffic\"\n                  label=\"定向流量（G）\"\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入定向流量\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"free_minutes\"\n                  label=\"免费通话（分钟）\"\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入免费通话时长\"\n                    min={0}\n                    max={99999}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"package_duration\"\n                  label=\"套餐时长\"\n                >\n                  <Input placeholder=\"请输入套餐时长，如：1个月、3个月、12个月等\" />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"product_weight\"\n                  label=\"产品权重\"\n                  tooltip=\"权重越高，产品在列表中的排序越靠前\"\n                >\n                  <Select placeholder=\"请选择产品权重\">\n                    {PRODUCT_WEIGHT_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 运营商信息 */}\n        <Col span={24}>\n          <Card title=\"运营商信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"category_id\"\n                  label=\"产品分类\"\n                  rules={[{ required: true, message: '请选择产品分类' }]}\n                >\n                  <Select\n                    placeholder=\"请选择产品分类\"\n                    showSearch\n                    filterOption={(input, option) =>\n                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                    }\n                  >\n                    {categories.map(category => (\n                      <Option key={category.id} value={category.id}>\n                        {category.name}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"operator\"\n                  label=\"运营商\"\n                >\n                  <Select placeholder=\"请选择运营商\">\n                    {OPERATOR_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"location\"\n                  label=\"归属地\"\n                >\n                  <Select placeholder=\"请选择归属地\">\n                    {LOCATION_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 商品标签 */}\n        <Col span={24}>\n          <Card title=\"商品标签\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag1\"\n                  label=\"标签1\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签1\" maxLength={6} />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag2\"\n                  label=\"标签2\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签2\" maxLength={6} />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag3\"\n                  label=\"标签3\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签3\" maxLength={6} />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 物流配置 */}\n        <Col span={24}>\n          <Card title=\"物流配置\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"logistics_method\"\n                  label=\"物流方式\"\n                >\n                  <Select placeholder=\"请选择物流方式\">\n                    {LOGISTICS_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"forbidden_areas\"\n                  label=\"禁发地区\"\n                >\n                  <Select\n                    mode=\"tags\"\n                    placeholder=\"请输入禁发地区，支持多个\"\n                    style={{ width: '100%' }}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 其他设置 */}\n        <Col span={24}>\n          <Card title=\"其他设置\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"need_id_photo\"\n                  label=\"需要上传身份证照片\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"sms_notification\"\n                  label=\"开启用户下单成功短信提醒\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"main_image\"\n                  label=\"产品主图\"\n                >\n                  <Input placeholder=\"请输入产品主图URL\" />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"detail_description\"\n                  label=\"商品详情图\"\n                  tooltip=\"请输入图片链接URL，支持JPG、PNG、GIF等格式\"\n                  rules={[\n                    {\n                      type: 'url',\n                      message: '请输入有效的图片链接URL',\n                    },\n                  ]}\n                >\n                  <Input\n                    placeholder=\"请输入商品详情图片链接，如：https://example.com/image.jpg\"\n                    maxLength={500}\n                    showCount\n                    onChange={(e) => {\n                      const url = e.target.value;\n                      if (url && (url.startsWith('http://') || url.startsWith('https://'))) {\n                        // 可以在这里添加图片预览逻辑\n                      }\n                    }}\n                  />\n                </Form.Item>\n                {/* 图片预览 */}\n                <Form.Item shouldUpdate>\n                  {({ getFieldValue }) => {\n                    const imageUrl = getFieldValue('detail_description');\n                    if (imageUrl && (imageUrl.startsWith('http://') || imageUrl.startsWith('https://'))) {\n                      return (\n                        <div style={{ marginTop: 8 }}>\n                          <div style={{ marginBottom: 8, color: '#666', fontSize: '14px' }}>图片预览：</div>\n                          <img\n                            src={imageUrl}\n                            alt=\"商品详情图预览\"\n                            style={{\n                              maxWidth: '100%',\n                              maxHeight: '300px',\n                              border: '1px solid #d9d9d9',\n                              borderRadius: '6px',\n                              objectFit: 'contain',\n                            }}\n                            onError={(e) => {\n                              e.currentTarget.style.display = 'none';\n                            }}\n                            onLoad={(e) => {\n                              e.currentTarget.style.display = 'block';\n                            }}\n                          />\n                        </div>\n                      );\n                    }\n                    return null;\n                  }}\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 提交按钮 */}\n        <Col span={24}>\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                size=\"large\"\n              >\n                {initialValues ? '更新产品' : '创建产品'}\n              </Button>\n              <Button\n                size=\"large\"\n                onClick={() => form.resetFields()}\n              >\n                重置\n              </Button>\n            </Space>\n          </Form.Item>\n        </Col>\n      </Row>\n    </Form>\n  );\n};\n\nexport default ProductForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,MAAM;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAEA,SACEC,gBAAgB,EAChBC,gBAAgB,EAChBC,sBAAsB,QACjB,oBAAoB;AAC3B,SAASC,aAAa,EAAEC,eAAe,QAAQ,kBAAkB;;AAEjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAUA;AACA,MAAMC,iBAAiB,GAAG,CACxB;EAAEC,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAS,CAAC,CACjC;AAID,MAAMC,sBAAsB,GAAG,CAC7B;EAAEF,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAG,CAAC,CAC3B;AAED,MAAM;EAAEE;AAAO,CAAC,GAAGnB,MAAM;AACzB,MAAM;EAAEoB;AAAS,CAAC,GAAGtB,KAAK;AAS1B,MAAMuB,WAAuC,GAAGA,CAAC;EAC/CC,IAAI;EACJC,aAAa;EACbC,QAAQ;EACRC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAa,EAAE,CAAC;;EAE5D;EACA,MAAMqC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI;MACF,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAC1D,IAAIF,eAAe,EAAE;QACnB,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,eAAe,CAAC;QAClD;QACA,MAAMM,gBAAgB,GAAGH,cAAc,CAACI,MAAM,CAAEC,GAAa,IAAKA,GAAG,CAACC,MAAM,KAAK,QAAQ,CAAC;QAC1FX,aAAa,CAACQ,gBAAgB,CAAC;MACjC;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACAjD,SAAS,CAAC,MAAM;IACdsC,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAMa,mBAAmB,GAAIC,CAAe,IAAK;MAC/C,IAAIA,CAAC,CAACC,GAAG,KAAK,YAAY,EAAE;QAC1Bf,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IAEDgB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;;IAEvD;IACA,MAAMK,sBAAsB,GAAGA,CAAA,KAAM;MACnClB,cAAc,CAAC,CAAC;IAClB,CAAC;IAEDgB,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAEC,sBAAsB,CAAC;IAEpE,OAAO,MAAM;MACXF,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAEN,mBAAmB,CAAC;MAC1DG,MAAM,CAACG,mBAAmB,CAAC,mBAAmB,EAAED,sBAAsB,CAAC;IACzE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI4B,aAAa,EAAE;MACjB,MAAM8B,QAAQ,GAAG;QACf,GAAG9B,aAAa;QAChB+B,WAAW,EAAEC,UAAU,CAAChC,aAAa,CAAC+B,WAAW,CAAC;QAClDE,eAAe,EAAED,UAAU,CAAChC,aAAa,CAACiC,eAAe,CAAC;QAC1DC,mBAAmB,EAAEF,UAAU,CAAChC,aAAa,CAACkC,mBAAmB,CAAC;QAClEC,IAAI,EAAEnC,aAAa,CAACoC,IAAI,CAACD,IAAI;QAC7BE,IAAI,EAAErC,aAAa,CAACoC,IAAI,CAACC,IAAI;QAC7BC,IAAI,EAAEtC,aAAa,CAACoC,IAAI,CAACE,IAAI;QAC7BC,eAAe,EAAEvC,aAAa,CAACwC;MACjC,CAAC;MACDzC,IAAI,CAAC0C,cAAc,CAACX,QAAQ,CAAC;MAC7BzB,WAAW,CAACL,aAAa,CAAC0C,UAAU,IAAI,EAAE,CAAC;IAC7C;EACF,CAAC,EAAE,CAAC1C,aAAa,EAAED,IAAI,CAAC,CAAC;;EAEzB;EACA,MAAM4C,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,MAAMC,UAA0B,GAAG;QACjC,GAAGD,MAAM;QACTF,UAAU,EAAEE,MAAM,CAACF,UAAU;QAAE;QAC/BH,eAAe,EAAEO,KAAK,CAACC,OAAO,CAACH,MAAM,CAACL,eAAe,CAAC,GAClDnD,aAAa,CAACwD,MAAM,CAACL,eAAe,CAAC,GACrCK,MAAM,CAACL,eAAe;QAC1BS,iBAAiB,EAAEF,KAAK,CAACC,OAAO,CAACH,MAAM,CAACI,iBAAiB,CAAC,GACtD5D,aAAa,CAACwD,MAAM,CAACI,iBAAiB,CAAC,GACvCJ,MAAM,CAACI;MACb,CAAC;MAED,MAAM/C,QAAQ,CAAC4C,UAAU,CAAC;IAC5B,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAML,MAAM,GAAG7C,IAAI,CAACmD,cAAc,CAAC,CAAC;IACpC,MAAMC,WAAW,GAAGpC,IAAI,CAACqC,SAAS,CAACR,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMS,OAAO,GAAG,MAAMhE,eAAe,CAAC8D,WAAW,CAAC;IAClD,IAAIE,OAAO,EAAE;MACXrE,OAAO,CAACqE,OAAO,CAAC,aAAa,CAAC;IAChC,CAAC,MAAM;MACLrE,OAAO,CAACqC,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMiC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMV,MAAM,GAAG7C,IAAI,CAACmD,cAAc,CAAC,CAAC;IACpCtC,YAAY,CAAC2C,OAAO,CAAC,eAAe,EAAExC,IAAI,CAACqC,SAAS,CAACR,MAAM,CAAC,CAAC;IAC7D5D,OAAO,CAACqE,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,KAAK,GAAG7C,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACnD,IAAI4C,KAAK,EAAE;MACT,IAAI;QACF,MAAMC,SAAS,GAAG3C,IAAI,CAACC,KAAK,CAACyC,KAAK,CAAC;QACnC1D,IAAI,CAAC0C,cAAc,CAACiB,SAAS,CAAC;QAC9B1E,OAAO,CAACqE,OAAO,CAAC,OAAO,CAAC;MAC1B,CAAC,CAAC,OAAOhC,KAAK,EAAE;QACdrC,OAAO,CAACqC,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,MAAM;MACLrC,OAAO,CAAC2E,IAAI,CAAC,QAAQ,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,IAAgB,IAAK;IAC9C;IACA;IACAtD,YAAY,CAAC,IAAI,CAAC;IAClBuD,UAAU,CAAC,MAAM;MACf,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAW,CAAC;MAChDxD,WAAW,CAAC0D,OAAO,CAAC;MACpBhE,IAAI,CAAC0C,cAAc,CAAC;QAAEC,UAAU,EAAEqB;MAAQ,CAAC,CAAC;MAC5CxD,YAAY,CAAC,KAAK,CAAC;MACnBvB,OAAO,CAACqE,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,oBACE9D,OAAA,CAACjB,IAAI;IACHyB,IAAI,EAAEA,IAAK;IACXmE,MAAM,EAAC,UAAU;IACjBC,QAAQ,EAAExB,YAAa;IACvB3C,aAAa,EAAE;MACboB,MAAM,EAAE,QAAQ;MAChBgD,aAAa,EAAE,IAAI;MACnBC,gBAAgB,EAAE;IACpB,CAAE;IAAAC,QAAA,eAEF/E,OAAA,CAACX,GAAG;MAAC2F,MAAM,EAAE,EAAG;MAAAD,QAAA,gBAEd/E,OAAA,CAACV,GAAG;QAAC2F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/E,OAAA,CAACT,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C/E,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXpF,KAAK,EAAC,0BAAM;gBACZqF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE/F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAEgG,GAAG,EAAE,GAAG;kBAAEhG,OAAO,EAAE;gBAAiB,CAAC,CACvC;gBAAAsF,QAAA,eAEF/E,OAAA,CAAChB,KAAK;kBAAC0G,WAAW,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,QAAQ;gBACbpF,KAAK,EAAC,0BAAM;gBACZqF,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE/F,OAAO,EAAE;gBAAU,CAAC,CAAE;gBAAAsF,QAAA,eAEhD/E,OAAA,CAACd,MAAM;kBAACwG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,EAC1BnF,sBAAsB,CAACmG,GAAG,CAACC,MAAM,iBAChChG,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE6F,MAAM,CAAC7F,KAAM;oBAAA4E,QAAA,EAC5CiB,MAAM,CAAC9F;kBAAK,GADF8F,MAAM,CAAC7F,KAAK;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9F,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAACM,QAAQ;kBACP2F,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,4CAAS;kBACrBQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9F,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAACM,QAAQ;kBACP2F,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,4CAAS;kBACrBQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,iBAAiB;gBACtBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAACd,MAAM;kBAACwG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,gBAC3B/E,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,SAAS;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,KAAK;oBAAA4E,QAAA,EAAC;kBAAK;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,IAAI;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,IAAI;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,KAAK;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,KAAK;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,OAAO;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,QAAQ;oBAAA4E,QAAA,EAAC;kBAAE;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9F,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,mBAAmB;gBACxBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAACd,MAAM;kBAACwG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,gBAC3B/E,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,QAAQ;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,KAAK;oBAAA4E,QAAA,EAAC;kBAAK;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,KAAK;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,MAAM;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC9F,OAAA,CAACK,MAAM;oBAACF,KAAK,EAAC,SAAS;oBAAA4E,QAAA,EAAC;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,eAAe;gBACpBpF,KAAK,EAAC,0BAAM;gBACZqF,KAAK,EAAE,CACL;kBACEa,IAAI,EAAE,KAAK;kBACX3G,OAAO,EAAE;gBACX,CAAC,CACD;gBAAAsF,QAAA,eAEF/E,OAAA,CAAChB,KAAK;kBACJ0G,WAAW,EAAC,0FAAwC;kBACpDQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9F,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,sBAAsB;gBAC3BpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAAChB,KAAK;kBACJ0G,WAAW,EAAC,qFAAoB;kBAChCQ,SAAS,EAAE;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,mBAAmB;gBACxBpF,KAAK,EAAC,gCAAO;gBAAA6E,QAAA,eAEb/E,OAAA,CAACd,MAAM;kBACLmH,IAAI,EAAC,MAAM;kBACXX,WAAW,EAAC,gFAAe;kBAC3BP,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9F,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,mBAAmB;gBACxBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAACM,QAAQ;kBACP2F,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,4IAAyB;kBACrCQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,2BAA2B;gBAChCpF,KAAK,EAAC,4CAAS;gBAAA6E,QAAA,eAEf/E,OAAA,CAACM,QAAQ;kBACP2F,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,0EAAc;kBAC1BQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9F,OAAA,CAACV,GAAG;QAAC2F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/E,OAAA,CAACT,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C/E,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBpF,KAAK,EAAC,4CAAS;gBACfqF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE/F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAE2G,IAAI,EAAE,QAAQ;kBAAEG,GAAG,EAAE,CAAC;kBAAE9G,OAAO,EAAE;gBAAU,CAAC,CAC9C;gBAAAsF,QAAA,eAEF/E,OAAA,CAACf,WAAW;kBACVkG,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO,CAAE;kBACzBZ,WAAW,EAAC,gCAAO;kBACnBc,SAAS,EAAE,CAAE;kBACbD,GAAG,EAAE,CAAE;kBACPd,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,iBAAiB;gBACtBpF,KAAK,EAAC,uCAAS;gBACfqF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE/F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAE2G,IAAI,EAAE,QAAQ;kBAAEG,GAAG,EAAE,CAAC;kBAAE9G,OAAO,EAAE;gBAAU,CAAC,CAC9C;gBAAAsF,QAAA,eAEF/E,OAAA,CAACf,WAAW;kBACVkG,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO,CAAE;kBACzBZ,WAAW,EAAC,4CAAS;kBACrBc,SAAS,EAAE,CAAE;kBACbD,GAAG,EAAE,CAAE;kBACPd,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,qBAAqB;gBAC1BpF,KAAK,EAAC,uCAAS;gBAAA6E,QAAA,eAEf/E,OAAA,CAACf,WAAW;kBACVkG,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO,CAAE;kBACzBZ,WAAW,EAAC,4CAAS;kBACrBc,SAAS,EAAE,CAAE;kBACbD,GAAG,EAAE,CAAE;kBACPd,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9F,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,cAAc;gBACnBpF,KAAK,EAAC,kDAAU;gBAAA6E,QAAA,eAEhB/E,OAAA,CAACf,WAAW;kBACVkG,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO,CAAE;kBACzBZ,WAAW,EAAC,wDAAW;kBACvBa,GAAG,EAAE,CAAE;kBACPd,GAAG,EAAE;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAAChB,KAAK;kBAAC0G,WAAW,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,gBAAgB;gBACrBpF,KAAK,EAAC,0BAAM;gBACZuG,OAAO,EAAC,wGAAmB;gBAAA1B,QAAA,eAE3B/E,OAAA,CAACd,MAAM;kBAACwG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,EAC1B3E,sBAAsB,CAAC2F,GAAG,CAACC,MAAM,iBAChChG,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE6F,MAAM,CAAC7F,KAAM;oBAAA4E,QAAA,EAC5CiB,MAAM,CAAC9F;kBAAK,GADF8F,MAAM,CAAC7F,KAAK;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9F,OAAA,CAACV,GAAG;QAAC2F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/E,OAAA,CAACT,IAAI;UAAC2F,KAAK,EAAC,gCAAO;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,eAC9C/E,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBpF,KAAK,EAAC,0BAAM;gBACZqF,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE/F,OAAO,EAAE;gBAAU,CAAC,CAAE;gBAAAsF,QAAA,eAEhD/E,OAAA,CAACd,MAAM;kBACLwG,WAAW,EAAC,4CAAS;kBACrBgB,UAAU;kBACVC,YAAY,EAAEA,CAACC,KAAK,EAAEZ,MAAM;oBAAA,IAAAa,gBAAA;oBAAA,OACzBb,MAAM,aAANA,MAAM,wBAAAa,gBAAA,GAANb,MAAM,CAAEjB,QAAQ,cAAA8B,gBAAA,uBAAjBA,gBAAA,CAAyCC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACH,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;kBAAA,CACrF;kBAAA/B,QAAA,EAEA9D,UAAU,CAAC8E,GAAG,CAACiB,QAAQ,iBACtBhH,OAAA,CAACK,MAAM;oBAAmBF,KAAK,EAAE6G,QAAQ,CAACC,EAAG;oBAAAlC,QAAA,EAC1CiC,QAAQ,CAAC1B;kBAAI,GADH0B,QAAQ,CAACC,EAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,UAAU;gBACfpF,KAAK,EAAC,oBAAK;gBAAA6E,QAAA,eAEX/E,OAAA,CAACd,MAAM;kBAACwG,WAAW,EAAC,sCAAQ;kBAAAX,QAAA,EACzBrF,gBAAgB,CAACqG,GAAG,CAACC,MAAM,iBAC1BhG,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE6F,MAAM,CAAC7F,KAAM;oBAAA4E,QAAA,EAC5CiB,MAAM,CAAC9F;kBAAK,GADF8F,MAAM,CAAC7F,KAAK;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,UAAU;gBACfpF,KAAK,EAAC,oBAAK;gBAAA6E,QAAA,eAEX/E,OAAA,CAACd,MAAM;kBAACwG,WAAW,EAAC,sCAAQ;kBAAAX,QAAA,EACzBpF,gBAAgB,CAACoG,GAAG,CAACC,MAAM,iBAC1BhG,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE6F,MAAM,CAAC7F,KAAM;oBAAA4E,QAAA,EAC5CiB,MAAM,CAAC9F;kBAAK,GADF8F,MAAM,CAAC7F,KAAK;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9F,OAAA,CAACV,GAAG;QAAC2F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/E,OAAA,CAACT,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,eAC7C/E,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXpF,KAAK,EAAC,eAAK;gBACXqF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAEhG,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAsF,QAAA,eAE3C/E,OAAA,CAAChB,KAAK;kBAAC0G,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXpF,KAAK,EAAC,eAAK;gBACXqF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAEhG,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAsF,QAAA,eAE3C/E,OAAA,CAAChB,KAAK;kBAAC0G,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXpF,KAAK,EAAC,eAAK;gBACXqF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAEhG,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAsF,QAAA,eAE3C/E,OAAA,CAAChB,KAAK;kBAAC0G,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9F,OAAA,CAACV,GAAG;QAAC2F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/E,OAAA,CAACT,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,eAC7C/E,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAACd,MAAM;kBAACwG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,EAC1B9E,iBAAiB,CAAC8F,GAAG,CAACC,MAAM,iBAC3BhG,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE6F,MAAM,CAAC7F,KAAM;oBAAA4E,QAAA,EAC5CiB,MAAM,CAAC9F;kBAAK,GADF8F,MAAM,CAAC7F,KAAK;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,iBAAiB;gBACtBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAACd,MAAM;kBACLmH,IAAI,EAAC,MAAM;kBACXX,WAAW,EAAC,0EAAc;kBAC1BP,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9F,OAAA,CAACV,GAAG;QAAC2F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/E,OAAA,CAACT,IAAI;UAAC2F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C/E,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,eAAe;gBACpBpF,KAAK,EAAC,wDAAW;gBACjBgH,aAAa,EAAC,SAAS;gBAAAnC,QAAA,eAEvB/E,OAAA,CAACb,MAAM;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9F,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBpF,KAAK,EAAC,0EAAc;gBACpBgH,aAAa,EAAC,SAAS;gBAAAnC,QAAA,eAEvB/E,OAAA,CAACb,MAAM;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9F,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,YAAY;gBACjBpF,KAAK,EAAC,0BAAM;gBAAA6E,QAAA,eAEZ/E,OAAA,CAAChB,KAAK;kBAAC0G,WAAW,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9F,OAAA,CAACX,GAAG;YAAC2F,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd/E,OAAA,CAACV,GAAG;cAAC2F,IAAI,EAAE,EAAG;cAAAF,QAAA,gBACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,oBAAoB;gBACzBpF,KAAK,EAAC,gCAAO;gBACbuG,OAAO,EAAC,wGAA6B;gBACrClB,KAAK,EAAE,CACL;kBACEa,IAAI,EAAE,KAAK;kBACX3G,OAAO,EAAE;gBACX,CAAC,CACD;gBAAAsF,QAAA,eAEF/E,OAAA,CAAChB,KAAK;kBACJ0G,WAAW,EAAC,mHAA6C;kBACzDQ,SAAS,EAAE,GAAI;kBACfC,SAAS;kBACTgB,QAAQ,EAAGlF,CAAC,IAAK;oBACf,MAAMmF,GAAG,GAAGnF,CAAC,CAACoF,MAAM,CAAClH,KAAK;oBAC1B,IAAIiH,GAAG,KAAKA,GAAG,CAACE,UAAU,CAAC,SAAS,CAAC,IAAIF,GAAG,CAACE,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE;sBACpE;oBAAA;kBAEJ;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZ9F,OAAA,CAACjB,IAAI,CAACsG,IAAI;gBAACkC,YAAY;gBAAAxC,QAAA,EACpBA,CAAC;kBAAEyC;gBAAc,CAAC,KAAK;kBACtB,MAAM3G,QAAQ,GAAG2G,aAAa,CAAC,oBAAoB,CAAC;kBACpD,IAAI3G,QAAQ,KAAKA,QAAQ,CAACyG,UAAU,CAAC,SAAS,CAAC,IAAIzG,QAAQ,CAACyG,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE;oBACnF,oBACEtH,OAAA;sBAAKmF,KAAK,EAAE;wBAAEsC,SAAS,EAAE;sBAAE,CAAE;sBAAA1C,QAAA,gBAC3B/E,OAAA;wBAAKmF,KAAK,EAAE;0BAAEC,YAAY,EAAE,CAAC;0BAAEsC,KAAK,EAAE,MAAM;0BAAEC,QAAQ,EAAE;wBAAO,CAAE;wBAAA5C,QAAA,EAAC;sBAAK;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7E9F,OAAA;wBACE4H,GAAG,EAAE/G,QAAS;wBACdgH,GAAG,EAAC,4CAAS;wBACb1C,KAAK,EAAE;0BACL2C,QAAQ,EAAE,MAAM;0BAChBC,SAAS,EAAE,OAAO;0BAClBC,MAAM,EAAE,mBAAmB;0BAC3BC,YAAY,EAAE,KAAK;0BACnBC,SAAS,EAAE;wBACb,CAAE;wBACFC,OAAO,EAAGlG,CAAC,IAAK;0BACdA,CAAC,CAACmG,aAAa,CAACjD,KAAK,CAACkD,OAAO,GAAG,MAAM;wBACxC,CAAE;wBACFC,MAAM,EAAGrG,CAAC,IAAK;0BACbA,CAAC,CAACmG,aAAa,CAACjD,KAAK,CAACkD,OAAO,GAAG,OAAO;wBACzC;sBAAE;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAEV;kBACA,OAAO,IAAI;gBACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9F,OAAA,CAACV,GAAG;QAAC2F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/E,OAAA,CAACjB,IAAI,CAACsG,IAAI;UAAAN,QAAA,eACR/E,OAAA,CAACR,KAAK;YAAAuF,QAAA,gBACJ/E,OAAA,CAACZ,MAAM;cACLgH,IAAI,EAAC,SAAS;cACdmC,QAAQ,EAAC,QAAQ;cACjB5H,OAAO,EAAEA,OAAQ;cACjB6H,IAAI,EAAC,OAAO;cAAAzD,QAAA,EAEXtE,aAAa,GAAG,MAAM,GAAG;YAAM;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACT9F,OAAA,CAACZ,MAAM;cACLoJ,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAMjI,IAAI,CAACkI,WAAW,CAAC,CAAE;cAAA3D,QAAA,EACnC;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAClF,EAAA,CAppBIL,WAAuC;AAAAoI,EAAA,GAAvCpI,WAAuC;AAspB7C,eAAeA,WAAW;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
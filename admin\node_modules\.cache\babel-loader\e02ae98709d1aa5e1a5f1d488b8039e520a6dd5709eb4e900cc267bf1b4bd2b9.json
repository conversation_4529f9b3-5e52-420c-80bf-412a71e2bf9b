{"ast": null, "code": "import { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { alignItemsValues, flexWrapValues, justifyContentValues } from '../utils';\nconst genFlexStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'flex',\n      margin: 0,\n      padding: 0,\n      '&-vertical': {\n        flexDirection: 'column'\n      },\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&:empty': {\n        display: 'none'\n      }\n    }\n  };\n};\nconst genFlexGapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-gap-small': {\n        gap: token.flexGapSM\n      },\n      '&-gap-middle': {\n        gap: token.flexGap\n      },\n      '&-gap-large': {\n        gap: token.flexGapLG\n      }\n    }\n  };\n};\nconst genFlexWrapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const wrapStyle = {};\n  flexWrapValues.forEach(value => {\n    wrapStyle[\"\".concat(componentCls, \"-wrap-\").concat(value)] = {\n      flexWrap: value\n    };\n  });\n  return wrapStyle;\n};\nconst genAlignItemsStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const alignStyle = {};\n  alignItemsValues.forEach(value => {\n    alignStyle[\"\".concat(componentCls, \"-align-\").concat(value)] = {\n      alignItems: value\n    };\n  });\n  return alignStyle;\n};\nconst genJustifyContentStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const justifyStyle = {};\n  justifyContentValues.forEach(value => {\n    justifyStyle[\"\".concat(componentCls, \"-justify-\").concat(value)] = {\n      justifyContent: value\n    };\n  });\n  return justifyStyle;\n};\nexport const prepareComponentToken = () => ({});\nexport default genStyleHooks('Flex', token => {\n  const {\n    paddingXS,\n    padding,\n    paddingLG\n  } = token;\n  const flexToken = mergeToken(token, {\n    flexGapSM: paddingXS,\n    flexGap: padding,\n    flexGapLG: paddingLG\n  });\n  return [genFlexStyle(flexToken), genFlexGapStyle(flexToken), genFlexWrapStyle(flexToken), genAlignItemsStyle(flexToken), genJustifyContentStyle(flexToken)];\n}, prepareComponentToken, {\n  // Flex component don't apply extra font style\n  // https://github.com/ant-design/ant-design/issues/46403\n  resetStyle: false\n});", "map": {"version": 3, "names": ["genStyleHooks", "mergeToken", "alignItemsValues", "flexWrapValues", "justify<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genFlexStyle", "token", "componentCls", "display", "margin", "padding", "flexDirection", "direction", "genFlexGapStyle", "gap", "flexGapSM", "flexGap", "flexGapLG", "genFlexWrapStyle", "wrapStyle", "for<PERSON>ach", "value", "concat", "flexWrap", "genAlignItemsStyle", "alignStyle", "alignItems", "genJustifyContentStyle", "justifyStyle", "justifyContent", "prepareComponentToken", "paddingXS", "paddingLG", "flexToken", "resetStyle"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/flex/style/index.js"], "sourcesContent": ["import { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { alignItemsValues, flexWrapValues, justifyContentValues } from '../utils';\nconst genFlexStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'flex',\n      margin: 0,\n      padding: 0,\n      '&-vertical': {\n        flexDirection: 'column'\n      },\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&:empty': {\n        display: 'none'\n      }\n    }\n  };\n};\nconst genFlexGapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-gap-small': {\n        gap: token.flexGapSM\n      },\n      '&-gap-middle': {\n        gap: token.flexGap\n      },\n      '&-gap-large': {\n        gap: token.flexGapLG\n      }\n    }\n  };\n};\nconst genFlexWrapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const wrapStyle = {};\n  flexWrapValues.forEach(value => {\n    wrapStyle[`${componentCls}-wrap-${value}`] = {\n      flexWrap: value\n    };\n  });\n  return wrapStyle;\n};\nconst genAlignItemsStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const alignStyle = {};\n  alignItemsValues.forEach(value => {\n    alignStyle[`${componentCls}-align-${value}`] = {\n      alignItems: value\n    };\n  });\n  return alignStyle;\n};\nconst genJustifyContentStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const justifyStyle = {};\n  justifyContentValues.forEach(value => {\n    justifyStyle[`${componentCls}-justify-${value}`] = {\n      justifyContent: value\n    };\n  });\n  return justifyStyle;\n};\nexport const prepareComponentToken = () => ({});\nexport default genStyleHooks('Flex', token => {\n  const {\n    paddingXS,\n    padding,\n    paddingLG\n  } = token;\n  const flexToken = mergeToken(token, {\n    flexGapSM: paddingXS,\n    flexGap: padding,\n    flexGapLG: paddingLG\n  });\n  return [genFlexStyle(flexToken), genFlexGapStyle(flexToken), genFlexWrapStyle(flexToken), genAlignItemsStyle(flexToken), genJustifyContentStyle(flexToken)];\n}, prepareComponentToken, {\n  // Flex component don't apply extra font style\n  // https://github.com/ant-design/ant-design/issues/46403\n  resetStyle: false\n});"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,SAASC,gBAAgB,EAAEC,cAAc,EAAEC,oBAAoB,QAAQ,UAAU;AACjF,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACdC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACV,YAAY,EAAE;QACZC,aAAa,EAAE;MACjB,CAAC;MACD,OAAO,EAAE;QACPC,SAAS,EAAE;MACb,CAAC;MACD,SAAS,EAAE;QACTJ,OAAO,EAAE;MACX;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMK,eAAe,GAAGP,KAAK,IAAI;EAC/B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACd,aAAa,EAAE;QACbO,GAAG,EAAER,KAAK,CAACS;MACb,CAAC;MACD,cAAc,EAAE;QACdD,GAAG,EAAER,KAAK,CAACU;MACb,CAAC;MACD,aAAa,EAAE;QACbF,GAAG,EAAER,KAAK,CAACW;MACb;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMC,gBAAgB,GAAGZ,KAAK,IAAI;EAChC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAMa,SAAS,GAAG,CAAC,CAAC;EACpBhB,cAAc,CAACiB,OAAO,CAACC,KAAK,IAAI;IAC9BF,SAAS,IAAAG,MAAA,CAAIf,YAAY,YAAAe,MAAA,CAASD,KAAK,EAAG,GAAG;MAC3CE,QAAQ,EAAEF;IACZ,CAAC;EACH,CAAC,CAAC;EACF,OAAOF,SAAS;AAClB,CAAC;AACD,MAAMK,kBAAkB,GAAGlB,KAAK,IAAI;EAClC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAMmB,UAAU,GAAG,CAAC,CAAC;EACrBvB,gBAAgB,CAACkB,OAAO,CAACC,KAAK,IAAI;IAChCI,UAAU,IAAAH,MAAA,CAAIf,YAAY,aAAAe,MAAA,CAAUD,KAAK,EAAG,GAAG;MAC7CK,UAAU,EAAEL;IACd,CAAC;EACH,CAAC,CAAC;EACF,OAAOI,UAAU;AACnB,CAAC;AACD,MAAME,sBAAsB,GAAGrB,KAAK,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAMsB,YAAY,GAAG,CAAC,CAAC;EACvBxB,oBAAoB,CAACgB,OAAO,CAACC,KAAK,IAAI;IACpCO,YAAY,IAAAN,MAAA,CAAIf,YAAY,eAAAe,MAAA,CAAYD,KAAK,EAAG,GAAG;MACjDQ,cAAc,EAAER;IAClB,CAAC;EACH,CAAC,CAAC;EACF,OAAOO,YAAY;AACrB,CAAC;AACD,OAAO,MAAME,qBAAqB,GAAGA,CAAA,MAAO,CAAC,CAAC,CAAC;AAC/C,eAAe9B,aAAa,CAAC,MAAM,EAAEM,KAAK,IAAI;EAC5C,MAAM;IACJyB,SAAS;IACTrB,OAAO;IACPsB;EACF,CAAC,GAAG1B,KAAK;EACT,MAAM2B,SAAS,GAAGhC,UAAU,CAACK,KAAK,EAAE;IAClCS,SAAS,EAAEgB,SAAS;IACpBf,OAAO,EAAEN,OAAO;IAChBO,SAAS,EAAEe;EACb,CAAC,CAAC;EACF,OAAO,CAAC3B,YAAY,CAAC4B,SAAS,CAAC,EAAEpB,eAAe,CAACoB,SAAS,CAAC,EAAEf,gBAAgB,CAACe,SAAS,CAAC,EAAET,kBAAkB,CAACS,SAAS,CAAC,EAAEN,sBAAsB,CAACM,SAAS,CAAC,CAAC;AAC7J,CAAC,EAAEH,qBAAqB,EAAE;EACxB;EACA;EACAI,UAAU,EAAE;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
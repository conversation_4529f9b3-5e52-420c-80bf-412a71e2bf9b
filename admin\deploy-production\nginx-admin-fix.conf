# Nginx配置 - 修复Admin页面空白问题

server {
    listen 80;
    listen 443 ssl;
    http2 on;
    server_name h5.haokajiyun.com;
    
    # SSL证书配置
    ssl_certificate /www/server/panel/vhost/cert/h5.haokajiyun.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/h5.haokajiyun.com/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 根目录重定向到admin
    location = / {
        return 301 /admin/;
    }
    
    # Admin后台管理系统 - 修正版
    location /admin {
        # 处理不带斜杠的访问
        return 301 /admin/;
    }
    
    location /admin/ {
        alias /www/wwwroot/h5.haokajiyun.com/admin/;
        index index.html;
        
        # 尝试直接文件，然后目录，最后fallback到index.html
        try_files $uri $uri/ /admin/index.html;
        
        # 确保MIME类型正确
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
        
        # CSS文件处理
        location ~* \.css$ {
            add_header Content-Type "text/css";
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # JavaScript文件处理
        location ~* \.js$ {
            add_header Content-Type "application/javascript";
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # 其他静态资源
        location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # JSON文件
        location ~* \.json$ {
            add_header Content-Type "application/json";
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # API路由配置
    location /api/ {
        root /www/wwwroot/h5.haokajiyun.com/api/public;
        rewrite ^/api/(.*)$ /$1 break;
        try_files $uri $uri/ /index.php?$query_string;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept";
            add_header Access-Control-Max-Age 86400;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
    
    # PHP处理
    location ~ \.php$ {
        root /www/wwwroot/h5.haokajiyun.com/api/public;
        try_files $uri /index.php =404;
        fastcgi_pass unix:/tmp/php-cgi-83.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
    }
    
    # 禁止访问敏感文件
    location ~ /\.(ht|env) {
        deny all;
    }
    
    # Gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript;
    
    client_max_body_size 20M;
    
    # 日志
    access_log /www/wwwlogs/h5.haokajiyun.com.log;
    error_log /www/wwwlogs/h5.haokajiyun.com.error.log;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name h5.haokajiyun.com;
    return 301 https://$server_name$request_uri;
}

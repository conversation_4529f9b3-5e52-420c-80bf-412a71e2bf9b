{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable default-case */\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport CascaderContext from \"../context\";\nimport { getFullPathKeys, isLeaf, scrollIntoParentView, toPathKey, toPathKeys, toPathValueStr } from \"../utils/commonUtil\";\nimport { toPathOptions } from \"../utils/treeUtil\";\nimport CacheContent from \"./CacheContent\";\nimport Column, { FIX_LABEL } from \"./Column\";\nimport useActive from \"./useActive\";\nimport useKeyboard from \"./useKeyboard\";\nvar RawOptionList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _optionColumns$, _ref3, _classNames;\n  var prefixCls = props.prefixCls,\n    multiple = props.multiple,\n    searchValue = props.searchValue,\n    toggleOpen = props.toggleOpen,\n    notFoundContent = props.notFoundContent,\n    direction = props.direction,\n    open = props.open,\n    disabled = props.disabled;\n  var containerRef = React.useRef(null);\n  var rtl = direction === 'rtl';\n  var _React$useContext = React.useContext(CascaderContext),\n    options = _React$useContext.options,\n    values = _React$useContext.values,\n    halfValues = _React$useContext.halfValues,\n    fieldNames = _React$useContext.fieldNames,\n    changeOnSelect = _React$useContext.changeOnSelect,\n    onSelect = _React$useContext.onSelect,\n    searchOptions = _React$useContext.searchOptions,\n    dropdownPrefixCls = _React$useContext.dropdownPrefixCls,\n    loadData = _React$useContext.loadData,\n    expandTrigger = _React$useContext.expandTrigger;\n  var mergedPrefixCls = dropdownPrefixCls || prefixCls;\n\n  // ========================= loadData =========================\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    loadingKeys = _React$useState2[0],\n    setLoadingKeys = _React$useState2[1];\n  var internalLoadData = function internalLoadData(valueCells) {\n    // Do not load when search\n    if (!loadData || searchValue) {\n      return;\n    }\n    var optionList = toPathOptions(valueCells, options, fieldNames);\n    var rawOptions = optionList.map(function (_ref) {\n      var option = _ref.option;\n      return option;\n    });\n    var lastOption = rawOptions[rawOptions.length - 1];\n    if (lastOption && !isLeaf(lastOption, fieldNames)) {\n      var pathKey = toPathKey(valueCells);\n      setLoadingKeys(function (keys) {\n        return [].concat(_toConsumableArray(keys), [pathKey]);\n      });\n      loadData(rawOptions);\n    }\n  };\n\n  // zombieJ: This is bad. We should make this same as `rc-tree` to use Promise instead.\n  React.useEffect(function () {\n    if (loadingKeys.length) {\n      loadingKeys.forEach(function (loadingKey) {\n        var valueStrCells = toPathValueStr(loadingKey);\n        var optionList = toPathOptions(valueStrCells, options, fieldNames, true).map(function (_ref2) {\n          var option = _ref2.option;\n          return option;\n        });\n        var lastOption = optionList[optionList.length - 1];\n        if (!lastOption || lastOption[fieldNames.children] || isLeaf(lastOption, fieldNames)) {\n          setLoadingKeys(function (keys) {\n            return keys.filter(function (key) {\n              return key !== loadingKey;\n            });\n          });\n        }\n      });\n    }\n  }, [options, loadingKeys, fieldNames]);\n\n  // ========================== Values ==========================\n  var checkedSet = React.useMemo(function () {\n    return new Set(toPathKeys(values));\n  }, [values]);\n  var halfCheckedSet = React.useMemo(function () {\n    return new Set(toPathKeys(halfValues));\n  }, [halfValues]);\n\n  // ====================== Accessibility =======================\n  var _useActive = useActive(multiple, open),\n    _useActive2 = _slicedToArray(_useActive, 2),\n    activeValueCells = _useActive2[0],\n    setActiveValueCells = _useActive2[1];\n\n  // =========================== Path ===========================\n  var onPathOpen = function onPathOpen(nextValueCells) {\n    setActiveValueCells(nextValueCells);\n\n    // Trigger loadData\n    internalLoadData(nextValueCells);\n  };\n  var isSelectable = function isSelectable(option) {\n    if (disabled) {\n      return false;\n    }\n    var optionDisabled = option.disabled;\n    var isMergedLeaf = isLeaf(option, fieldNames);\n    return !optionDisabled && (isMergedLeaf || changeOnSelect || multiple);\n  };\n  var onPathSelect = function onPathSelect(valuePath, leaf) {\n    var fromKeyboard = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    onSelect(valuePath);\n    if (!multiple && (leaf || changeOnSelect && (expandTrigger === 'hover' || fromKeyboard))) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================== Option ==========================\n  var mergedOptions = React.useMemo(function () {\n    if (searchValue) {\n      return searchOptions;\n    }\n    return options;\n  }, [searchValue, searchOptions, options]);\n\n  // ========================== Column ==========================\n  var optionColumns = React.useMemo(function () {\n    var optionList = [{\n      options: mergedOptions\n    }];\n    var currentList = mergedOptions;\n    var fullPathKeys = getFullPathKeys(currentList, fieldNames);\n    var _loop = function _loop() {\n      var activeValueCell = activeValueCells[i];\n      var currentOption = currentList.find(function (option, index) {\n        return (fullPathKeys[index] ? toPathKey(fullPathKeys[index]) : option[fieldNames.value]) === activeValueCell;\n      });\n      var subOptions = currentOption === null || currentOption === void 0 ? void 0 : currentOption[fieldNames.children];\n      if (!(subOptions !== null && subOptions !== void 0 && subOptions.length)) {\n        return 1; // break\n      }\n      currentList = subOptions;\n      optionList.push({\n        options: subOptions\n      });\n    };\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      if (_loop()) break;\n    }\n    return optionList;\n  }, [mergedOptions, activeValueCells, fieldNames]);\n\n  // ========================= Keyboard =========================\n  var onKeyboardSelect = function onKeyboardSelect(selectValueCells, option) {\n    if (isSelectable(option)) {\n      onPathSelect(selectValueCells, isLeaf(option, fieldNames), true);\n    }\n  };\n  useKeyboard(ref, mergedOptions, fieldNames, activeValueCells, onPathOpen, onKeyboardSelect, {\n    direction: direction,\n    searchValue: searchValue,\n    toggleOpen: toggleOpen,\n    open: open\n  });\n\n  // >>>>> Active Scroll\n  React.useEffect(function () {\n    if (searchValue) {\n      return;\n    }\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      var _containerRef$current;\n      var cellPath = activeValueCells.slice(0, i + 1);\n      var cellKeyPath = toPathKey(cellPath);\n      var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelector(\"li[data-path-key=\\\"\".concat(cellKeyPath.replace(/\\\\{0,2}\"/g, '\\\\\"'), \"\\\"]\") // matches unescaped double quotes\n      );\n      if (ele) {\n        scrollIntoParentView(ele);\n      }\n    }\n  }, [activeValueCells, searchValue]);\n\n  // ========================== Render ==========================\n  // >>>>> Empty\n  var isEmpty = !((_optionColumns$ = optionColumns[0]) !== null && _optionColumns$ !== void 0 && (_optionColumns$ = _optionColumns$.options) !== null && _optionColumns$ !== void 0 && _optionColumns$.length);\n  var emptyList = [(_ref3 = {}, _defineProperty(_ref3, fieldNames.value, '__EMPTY__'), _defineProperty(_ref3, FIX_LABEL, notFoundContent), _defineProperty(_ref3, \"disabled\", true), _ref3)];\n  var columnProps = _objectSpread(_objectSpread({}, props), {}, {\n    multiple: !isEmpty && multiple,\n    onSelect: onPathSelect,\n    onActive: onPathOpen,\n    onToggleOpen: toggleOpen,\n    checkedSet: checkedSet,\n    halfCheckedSet: halfCheckedSet,\n    loadingKeys: loadingKeys,\n    isSelectable: isSelectable\n  });\n\n  // >>>>> Columns\n  var mergedOptionColumns = isEmpty ? [{\n    options: emptyList\n  }] : optionColumns;\n  var columnNodes = mergedOptionColumns.map(function (col, index) {\n    var prevValuePath = activeValueCells.slice(0, index);\n    var activeValue = activeValueCells[index];\n    return /*#__PURE__*/React.createElement(Column, _extends({\n      key: index\n    }, columnProps, {\n      prefixCls: mergedPrefixCls,\n      options: col.options,\n      prevValuePath: prevValuePath,\n      activeValue: activeValue\n    }));\n  });\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(CacheContent, {\n    open: open\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(mergedPrefixCls, \"-menus\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-menu-empty\"), isEmpty), _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-rtl\"), rtl), _classNames)),\n    ref: containerRef\n  }, columnNodes));\n});\nif (process.env.NODE_ENV !== 'production') {\n  RawOptionList.displayName = 'RawOptionList';\n}\nexport default RawOptionList;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_defineProperty", "_toConsumableArray", "_slicedToArray", "classNames", "React", "CascaderContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "scrollIntoParentView", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toPathValueStr", "toPathOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Column", "FIX_LABEL", "useActive", "useKeyboard", "RawOptionList", "forwardRef", "props", "ref", "_optionColumns$", "_ref3", "_classNames", "prefixCls", "multiple", "searchValue", "toggle<PERSON><PERSON>", "notFoundContent", "direction", "open", "disabled", "containerRef", "useRef", "rtl", "_React$useContext", "useContext", "options", "values", "halfV<PERSON>ues", "fieldNames", "changeOnSelect", "onSelect", "searchOptions", "dropdownPrefixCls", "loadData", "expandTrigger", "mergedPrefixCls", "_React$useState", "useState", "_React$useState2", "loadingKeys", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "internalLoadData", "valueCells", "optionList", "rawOptions", "map", "_ref", "option", "lastOption", "length", "path<PERSON><PERSON>", "keys", "concat", "useEffect", "for<PERSON>ach", "loadingKey", "valueStrCells", "_ref2", "children", "filter", "key", "checkedSet", "useMemo", "Set", "halfCheckedSet", "_useActive", "_useActive2", "activeValueCells", "setActiveValueCells", "onPathOpen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isSelectable", "optionDisabled", "isMergedLeaf", "onPathSelect", "valuePath", "leaf", "fromKeyboard", "arguments", "undefined", "mergedOptions", "optionColumns", "currentList", "fullPath<PERSON><PERSON><PERSON>", "_loop", "activeValueCell", "i", "currentOption", "find", "index", "value", "subOptions", "push", "onKeyboardSelect", "selectV<PERSON>ueCells", "_containerRef$current", "cellPath", "slice", "cellKeyPath", "ele", "current", "querySelector", "replace", "isEmpty", "emptyList", "columnProps", "onActive", "onToggleOpen", "mergedOptionColumns", "columnNodes", "col", "prev<PERSON><PERSON><PERSON><PERSON><PERSON>", "activeValue", "createElement", "className", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-cascader/es/OptionList/List.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable default-case */\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport CascaderContext from \"../context\";\nimport { getFullPathKeys, isLeaf, scrollIntoParentView, toPathKey, toPathKeys, toPathValueStr } from \"../utils/commonUtil\";\nimport { toPathOptions } from \"../utils/treeUtil\";\nimport CacheContent from \"./CacheContent\";\nimport Column, { FIX_LABEL } from \"./Column\";\nimport useActive from \"./useActive\";\nimport useKeyboard from \"./useKeyboard\";\nvar RawOptionList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _optionColumns$, _ref3, _classNames;\n  var prefixCls = props.prefixCls,\n    multiple = props.multiple,\n    searchValue = props.searchValue,\n    toggleOpen = props.toggleOpen,\n    notFoundContent = props.notFoundContent,\n    direction = props.direction,\n    open = props.open,\n    disabled = props.disabled;\n  var containerRef = React.useRef(null);\n  var rtl = direction === 'rtl';\n  var _React$useContext = React.useContext(CascaderContext),\n    options = _React$useContext.options,\n    values = _React$useContext.values,\n    halfValues = _React$useContext.halfValues,\n    fieldNames = _React$useContext.fieldNames,\n    changeOnSelect = _React$useContext.changeOnSelect,\n    onSelect = _React$useContext.onSelect,\n    searchOptions = _React$useContext.searchOptions,\n    dropdownPrefixCls = _React$useContext.dropdownPrefixCls,\n    loadData = _React$useContext.loadData,\n    expandTrigger = _React$useContext.expandTrigger;\n  var mergedPrefixCls = dropdownPrefixCls || prefixCls;\n\n  // ========================= loadData =========================\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    loadingKeys = _React$useState2[0],\n    setLoadingKeys = _React$useState2[1];\n  var internalLoadData = function internalLoadData(valueCells) {\n    // Do not load when search\n    if (!loadData || searchValue) {\n      return;\n    }\n    var optionList = toPathOptions(valueCells, options, fieldNames);\n    var rawOptions = optionList.map(function (_ref) {\n      var option = _ref.option;\n      return option;\n    });\n    var lastOption = rawOptions[rawOptions.length - 1];\n    if (lastOption && !isLeaf(lastOption, fieldNames)) {\n      var pathKey = toPathKey(valueCells);\n      setLoadingKeys(function (keys) {\n        return [].concat(_toConsumableArray(keys), [pathKey]);\n      });\n      loadData(rawOptions);\n    }\n  };\n\n  // zombieJ: This is bad. We should make this same as `rc-tree` to use Promise instead.\n  React.useEffect(function () {\n    if (loadingKeys.length) {\n      loadingKeys.forEach(function (loadingKey) {\n        var valueStrCells = toPathValueStr(loadingKey);\n        var optionList = toPathOptions(valueStrCells, options, fieldNames, true).map(function (_ref2) {\n          var option = _ref2.option;\n          return option;\n        });\n        var lastOption = optionList[optionList.length - 1];\n        if (!lastOption || lastOption[fieldNames.children] || isLeaf(lastOption, fieldNames)) {\n          setLoadingKeys(function (keys) {\n            return keys.filter(function (key) {\n              return key !== loadingKey;\n            });\n          });\n        }\n      });\n    }\n  }, [options, loadingKeys, fieldNames]);\n\n  // ========================== Values ==========================\n  var checkedSet = React.useMemo(function () {\n    return new Set(toPathKeys(values));\n  }, [values]);\n  var halfCheckedSet = React.useMemo(function () {\n    return new Set(toPathKeys(halfValues));\n  }, [halfValues]);\n\n  // ====================== Accessibility =======================\n  var _useActive = useActive(multiple, open),\n    _useActive2 = _slicedToArray(_useActive, 2),\n    activeValueCells = _useActive2[0],\n    setActiveValueCells = _useActive2[1];\n\n  // =========================== Path ===========================\n  var onPathOpen = function onPathOpen(nextValueCells) {\n    setActiveValueCells(nextValueCells);\n\n    // Trigger loadData\n    internalLoadData(nextValueCells);\n  };\n  var isSelectable = function isSelectable(option) {\n    if (disabled) {\n      return false;\n    }\n    var optionDisabled = option.disabled;\n    var isMergedLeaf = isLeaf(option, fieldNames);\n    return !optionDisabled && (isMergedLeaf || changeOnSelect || multiple);\n  };\n  var onPathSelect = function onPathSelect(valuePath, leaf) {\n    var fromKeyboard = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    onSelect(valuePath);\n    if (!multiple && (leaf || changeOnSelect && (expandTrigger === 'hover' || fromKeyboard))) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================== Option ==========================\n  var mergedOptions = React.useMemo(function () {\n    if (searchValue) {\n      return searchOptions;\n    }\n    return options;\n  }, [searchValue, searchOptions, options]);\n\n  // ========================== Column ==========================\n  var optionColumns = React.useMemo(function () {\n    var optionList = [{\n      options: mergedOptions\n    }];\n    var currentList = mergedOptions;\n    var fullPathKeys = getFullPathKeys(currentList, fieldNames);\n    var _loop = function _loop() {\n      var activeValueCell = activeValueCells[i];\n      var currentOption = currentList.find(function (option, index) {\n        return (fullPathKeys[index] ? toPathKey(fullPathKeys[index]) : option[fieldNames.value]) === activeValueCell;\n      });\n      var subOptions = currentOption === null || currentOption === void 0 ? void 0 : currentOption[fieldNames.children];\n      if (!(subOptions !== null && subOptions !== void 0 && subOptions.length)) {\n        return 1; // break\n      }\n      currentList = subOptions;\n      optionList.push({\n        options: subOptions\n      });\n    };\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      if (_loop()) break;\n    }\n    return optionList;\n  }, [mergedOptions, activeValueCells, fieldNames]);\n\n  // ========================= Keyboard =========================\n  var onKeyboardSelect = function onKeyboardSelect(selectValueCells, option) {\n    if (isSelectable(option)) {\n      onPathSelect(selectValueCells, isLeaf(option, fieldNames), true);\n    }\n  };\n  useKeyboard(ref, mergedOptions, fieldNames, activeValueCells, onPathOpen, onKeyboardSelect, {\n    direction: direction,\n    searchValue: searchValue,\n    toggleOpen: toggleOpen,\n    open: open\n  });\n\n  // >>>>> Active Scroll\n  React.useEffect(function () {\n    if (searchValue) {\n      return;\n    }\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      var _containerRef$current;\n      var cellPath = activeValueCells.slice(0, i + 1);\n      var cellKeyPath = toPathKey(cellPath);\n      var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelector(\"li[data-path-key=\\\"\".concat(cellKeyPath.replace(/\\\\{0,2}\"/g, '\\\\\"'), \"\\\"]\") // matches unescaped double quotes\n      );\n      if (ele) {\n        scrollIntoParentView(ele);\n      }\n    }\n  }, [activeValueCells, searchValue]);\n\n  // ========================== Render ==========================\n  // >>>>> Empty\n  var isEmpty = !((_optionColumns$ = optionColumns[0]) !== null && _optionColumns$ !== void 0 && (_optionColumns$ = _optionColumns$.options) !== null && _optionColumns$ !== void 0 && _optionColumns$.length);\n  var emptyList = [(_ref3 = {}, _defineProperty(_ref3, fieldNames.value, '__EMPTY__'), _defineProperty(_ref3, FIX_LABEL, notFoundContent), _defineProperty(_ref3, \"disabled\", true), _ref3)];\n  var columnProps = _objectSpread(_objectSpread({}, props), {}, {\n    multiple: !isEmpty && multiple,\n    onSelect: onPathSelect,\n    onActive: onPathOpen,\n    onToggleOpen: toggleOpen,\n    checkedSet: checkedSet,\n    halfCheckedSet: halfCheckedSet,\n    loadingKeys: loadingKeys,\n    isSelectable: isSelectable\n  });\n\n  // >>>>> Columns\n  var mergedOptionColumns = isEmpty ? [{\n    options: emptyList\n  }] : optionColumns;\n  var columnNodes = mergedOptionColumns.map(function (col, index) {\n    var prevValuePath = activeValueCells.slice(0, index);\n    var activeValue = activeValueCells[index];\n    return /*#__PURE__*/React.createElement(Column, _extends({\n      key: index\n    }, columnProps, {\n      prefixCls: mergedPrefixCls,\n      options: col.options,\n      prevValuePath: prevValuePath,\n      activeValue: activeValue\n    }));\n  });\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(CacheContent, {\n    open: open\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(mergedPrefixCls, \"-menus\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-menu-empty\"), isEmpty), _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-rtl\"), rtl), _classNames)),\n    ref: containerRef\n  }, columnNodes));\n});\nif (process.env.NODE_ENV !== 'production') {\n  RawOptionList.displayName = 'RawOptionList';\n}\nexport default RawOptionList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,YAAY;AACxC,SAASC,eAAe,EAAEC,MAAM,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,cAAc,QAAQ,qBAAqB;AAC1H,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,IAAIC,SAAS,QAAQ,UAAU;AAC5C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,IAAIC,aAAa,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,eAAe,EAAEC,KAAK,EAAEC,WAAW;EACvC,IAAIC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC7BC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,eAAe,GAAGT,KAAK,CAACS,eAAe;IACvCC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,IAAI,GAAGX,KAAK,CAACW,IAAI;IACjBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;EAC3B,IAAIC,YAAY,GAAG7B,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIC,GAAG,GAAGL,SAAS,KAAK,KAAK;EAC7B,IAAIM,iBAAiB,GAAGhC,KAAK,CAACiC,UAAU,CAAChC,eAAe,CAAC;IACvDiC,OAAO,GAAGF,iBAAiB,CAACE,OAAO;IACnCC,MAAM,GAAGH,iBAAiB,CAACG,MAAM;IACjCC,UAAU,GAAGJ,iBAAiB,CAACI,UAAU;IACzCC,UAAU,GAAGL,iBAAiB,CAACK,UAAU;IACzCC,cAAc,GAAGN,iBAAiB,CAACM,cAAc;IACjDC,QAAQ,GAAGP,iBAAiB,CAACO,QAAQ;IACrCC,aAAa,GAAGR,iBAAiB,CAACQ,aAAa;IAC/CC,iBAAiB,GAAGT,iBAAiB,CAACS,iBAAiB;IACvDC,QAAQ,GAAGV,iBAAiB,CAACU,QAAQ;IACrCC,aAAa,GAAGX,iBAAiB,CAACW,aAAa;EACjD,IAAIC,eAAe,GAAGH,iBAAiB,IAAIpB,SAAS;;EAEpD;EACA,IAAIwB,eAAe,GAAG7C,KAAK,CAAC8C,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGjD,cAAc,CAAC+C,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAE;IAC3D;IACA,IAAI,CAACT,QAAQ,IAAInB,WAAW,EAAE;MAC5B;IACF;IACA,IAAI6B,UAAU,GAAG5C,aAAa,CAAC2C,UAAU,EAAEjB,OAAO,EAAEG,UAAU,CAAC;IAC/D,IAAIgB,UAAU,GAAGD,UAAU,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC9C,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxB,OAAOA,MAAM;IACf,CAAC,CAAC;IACF,IAAIC,UAAU,GAAGJ,UAAU,CAACA,UAAU,CAACK,MAAM,GAAG,CAAC,CAAC;IAClD,IAAID,UAAU,IAAI,CAACtD,MAAM,CAACsD,UAAU,EAAEpB,UAAU,CAAC,EAAE;MACjD,IAAIsB,OAAO,GAAGtD,SAAS,CAAC8C,UAAU,CAAC;MACnCF,cAAc,CAAC,UAAUW,IAAI,EAAE;QAC7B,OAAO,EAAE,CAACC,MAAM,CAAChE,kBAAkB,CAAC+D,IAAI,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;MACvD,CAAC,CAAC;MACFjB,QAAQ,CAACW,UAAU,CAAC;IACtB;EACF,CAAC;;EAED;EACArD,KAAK,CAAC8D,SAAS,CAAC,YAAY;IAC1B,IAAId,WAAW,CAACU,MAAM,EAAE;MACtBV,WAAW,CAACe,OAAO,CAAC,UAAUC,UAAU,EAAE;QACxC,IAAIC,aAAa,GAAG1D,cAAc,CAACyD,UAAU,CAAC;QAC9C,IAAIZ,UAAU,GAAG5C,aAAa,CAACyD,aAAa,EAAE/B,OAAO,EAAEG,UAAU,EAAE,IAAI,CAAC,CAACiB,GAAG,CAAC,UAAUY,KAAK,EAAE;UAC5F,IAAIV,MAAM,GAAGU,KAAK,CAACV,MAAM;UACzB,OAAOA,MAAM;QACf,CAAC,CAAC;QACF,IAAIC,UAAU,GAAGL,UAAU,CAACA,UAAU,CAACM,MAAM,GAAG,CAAC,CAAC;QAClD,IAAI,CAACD,UAAU,IAAIA,UAAU,CAACpB,UAAU,CAAC8B,QAAQ,CAAC,IAAIhE,MAAM,CAACsD,UAAU,EAAEpB,UAAU,CAAC,EAAE;UACpFY,cAAc,CAAC,UAAUW,IAAI,EAAE;YAC7B,OAAOA,IAAI,CAACQ,MAAM,CAAC,UAAUC,GAAG,EAAE;cAChC,OAAOA,GAAG,KAAKL,UAAU;YAC3B,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC9B,OAAO,EAAEc,WAAW,EAAEX,UAAU,CAAC,CAAC;;EAEtC;EACA,IAAIiC,UAAU,GAAGtE,KAAK,CAACuE,OAAO,CAAC,YAAY;IACzC,OAAO,IAAIC,GAAG,CAAClE,UAAU,CAAC6B,MAAM,CAAC,CAAC;EACpC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,IAAIsC,cAAc,GAAGzE,KAAK,CAACuE,OAAO,CAAC,YAAY;IAC7C,OAAO,IAAIC,GAAG,CAAClE,UAAU,CAAC8B,UAAU,CAAC,CAAC;EACxC,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,IAAIsC,UAAU,GAAG9D,SAAS,CAACU,QAAQ,EAAEK,IAAI,CAAC;IACxCgD,WAAW,GAAG7E,cAAc,CAAC4E,UAAU,EAAE,CAAC,CAAC;IAC3CE,gBAAgB,GAAGD,WAAW,CAAC,CAAC,CAAC;IACjCE,mBAAmB,GAAGF,WAAW,CAAC,CAAC,CAAC;;EAEtC;EACA,IAAIG,UAAU,GAAG,SAASA,UAAUA,CAACC,cAAc,EAAE;IACnDF,mBAAmB,CAACE,cAAc,CAAC;;IAEnC;IACA7B,gBAAgB,CAAC6B,cAAc,CAAC;EAClC,CAAC;EACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACxB,MAAM,EAAE;IAC/C,IAAI5B,QAAQ,EAAE;MACZ,OAAO,KAAK;IACd;IACA,IAAIqD,cAAc,GAAGzB,MAAM,CAAC5B,QAAQ;IACpC,IAAIsD,YAAY,GAAG/E,MAAM,CAACqD,MAAM,EAAEnB,UAAU,CAAC;IAC7C,OAAO,CAAC4C,cAAc,KAAKC,YAAY,IAAI5C,cAAc,IAAIhB,QAAQ,CAAC;EACxE,CAAC;EACD,IAAI6D,YAAY,GAAG,SAASA,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACxD,IAAIC,YAAY,GAAGC,SAAS,CAAC7B,MAAM,GAAG,CAAC,IAAI6B,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC5FhD,QAAQ,CAAC6C,SAAS,CAAC;IACnB,IAAI,CAAC9D,QAAQ,KAAK+D,IAAI,IAAI/C,cAAc,KAAKK,aAAa,KAAK,OAAO,IAAI2C,YAAY,CAAC,CAAC,EAAE;MACxF9D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,IAAIiE,aAAa,GAAGzF,KAAK,CAACuE,OAAO,CAAC,YAAY;IAC5C,IAAIhD,WAAW,EAAE;MACf,OAAOiB,aAAa;IACtB;IACA,OAAON,OAAO;EAChB,CAAC,EAAE,CAACX,WAAW,EAAEiB,aAAa,EAAEN,OAAO,CAAC,CAAC;;EAEzC;EACA,IAAIwD,aAAa,GAAG1F,KAAK,CAACuE,OAAO,CAAC,YAAY;IAC5C,IAAInB,UAAU,GAAG,CAAC;MAChBlB,OAAO,EAAEuD;IACX,CAAC,CAAC;IACF,IAAIE,WAAW,GAAGF,aAAa;IAC/B,IAAIG,YAAY,GAAG1F,eAAe,CAACyF,WAAW,EAAEtD,UAAU,CAAC;IAC3D,IAAIwD,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;MAC3B,IAAIC,eAAe,GAAGlB,gBAAgB,CAACmB,CAAC,CAAC;MACzC,IAAIC,aAAa,GAAGL,WAAW,CAACM,IAAI,CAAC,UAAUzC,MAAM,EAAE0C,KAAK,EAAE;QAC5D,OAAO,CAACN,YAAY,CAACM,KAAK,CAAC,GAAG7F,SAAS,CAACuF,YAAY,CAACM,KAAK,CAAC,CAAC,GAAG1C,MAAM,CAACnB,UAAU,CAAC8D,KAAK,CAAC,MAAML,eAAe;MAC9G,CAAC,CAAC;MACF,IAAIM,UAAU,GAAGJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC3D,UAAU,CAAC8B,QAAQ,CAAC;MACjH,IAAI,EAAEiC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAAC1C,MAAM,CAAC,EAAE;QACxE,OAAO,CAAC,CAAC,CAAC;MACZ;MACAiC,WAAW,GAAGS,UAAU;MACxBhD,UAAU,CAACiD,IAAI,CAAC;QACdnE,OAAO,EAAEkE;MACX,CAAC,CAAC;IACJ,CAAC;IACD,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,gBAAgB,CAAClB,MAAM,EAAEqC,CAAC,IAAI,CAAC,EAAE;MACnD,IAAIF,KAAK,CAAC,CAAC,EAAE;IACf;IACA,OAAOzC,UAAU;EACnB,CAAC,EAAE,CAACqC,aAAa,EAAEb,gBAAgB,EAAEvC,UAAU,CAAC,CAAC;;EAEjD;EACA,IAAIiE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,gBAAgB,EAAE/C,MAAM,EAAE;IACzE,IAAIwB,YAAY,CAACxB,MAAM,CAAC,EAAE;MACxB2B,YAAY,CAACoB,gBAAgB,EAAEpG,MAAM,CAACqD,MAAM,EAAEnB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClE;EACF,CAAC;EACDxB,WAAW,CAACI,GAAG,EAAEwE,aAAa,EAAEpD,UAAU,EAAEuC,gBAAgB,EAAEE,UAAU,EAAEwB,gBAAgB,EAAE;IAC1F5E,SAAS,EAAEA,SAAS;IACpBH,WAAW,EAAEA,WAAW;IACxBC,UAAU,EAAEA,UAAU;IACtBG,IAAI,EAAEA;EACR,CAAC,CAAC;;EAEF;EACA3B,KAAK,CAAC8D,SAAS,CAAC,YAAY;IAC1B,IAAIvC,WAAW,EAAE;MACf;IACF;IACA,KAAK,IAAIwE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,gBAAgB,CAAClB,MAAM,EAAEqC,CAAC,IAAI,CAAC,EAAE;MACnD,IAAIS,qBAAqB;MACzB,IAAIC,QAAQ,GAAG7B,gBAAgB,CAAC8B,KAAK,CAAC,CAAC,EAAEX,CAAC,GAAG,CAAC,CAAC;MAC/C,IAAIY,WAAW,GAAGtG,SAAS,CAACoG,QAAQ,CAAC;MACrC,IAAIG,GAAG,GAAG,CAACJ,qBAAqB,GAAG3E,YAAY,CAACgF,OAAO,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACM,aAAa,CAAC,qBAAqB,CAACjD,MAAM,CAAC8C,WAAW,CAACI,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;MAClO,CAAC;MACD,IAAIH,GAAG,EAAE;QACPxG,oBAAoB,CAACwG,GAAG,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAAChC,gBAAgB,EAAErD,WAAW,CAAC,CAAC;;EAEnC;EACA;EACA,IAAIyF,OAAO,GAAG,EAAE,CAAC9F,eAAe,GAAGwE,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIxE,eAAe,KAAK,KAAK,CAAC,IAAI,CAACA,eAAe,GAAGA,eAAe,CAACgB,OAAO,MAAM,IAAI,IAAIhB,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAACwC,MAAM,CAAC;EAC5M,IAAIuD,SAAS,GAAG,EAAE9F,KAAK,GAAG,CAAC,CAAC,EAAEvB,eAAe,CAACuB,KAAK,EAAEkB,UAAU,CAAC8D,KAAK,EAAE,WAAW,CAAC,EAAEvG,eAAe,CAACuB,KAAK,EAAER,SAAS,EAAEc,eAAe,CAAC,EAAE7B,eAAe,CAACuB,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,EAAEA,KAAK,EAAE;EAC1L,IAAI+F,WAAW,GAAGvH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5DM,QAAQ,EAAE,CAAC0F,OAAO,IAAI1F,QAAQ;IAC9BiB,QAAQ,EAAE4C,YAAY;IACtBgC,QAAQ,EAAErC,UAAU;IACpBsC,YAAY,EAAE5F,UAAU;IACxB8C,UAAU,EAAEA,UAAU;IACtBG,cAAc,EAAEA,cAAc;IAC9BzB,WAAW,EAAEA,WAAW;IACxBgC,YAAY,EAAEA;EAChB,CAAC,CAAC;;EAEF;EACA,IAAIqC,mBAAmB,GAAGL,OAAO,GAAG,CAAC;IACnC9E,OAAO,EAAE+E;EACX,CAAC,CAAC,GAAGvB,aAAa;EAClB,IAAI4B,WAAW,GAAGD,mBAAmB,CAAC/D,GAAG,CAAC,UAAUiE,GAAG,EAAErB,KAAK,EAAE;IAC9D,IAAIsB,aAAa,GAAG5C,gBAAgB,CAAC8B,KAAK,CAAC,CAAC,EAAER,KAAK,CAAC;IACpD,IAAIuB,WAAW,GAAG7C,gBAAgB,CAACsB,KAAK,CAAC;IACzC,OAAO,aAAalG,KAAK,CAAC0H,aAAa,CAAChH,MAAM,EAAEhB,QAAQ,CAAC;MACvD2E,GAAG,EAAE6B;IACP,CAAC,EAAEgB,WAAW,EAAE;MACd7F,SAAS,EAAEuB,eAAe;MAC1BV,OAAO,EAAEqF,GAAG,CAACrF,OAAO;MACpBsF,aAAa,EAAEA,aAAa;MAC5BC,WAAW,EAAEA;IACf,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;;EAEF;EACA,OAAO,aAAazH,KAAK,CAAC0H,aAAa,CAACjH,YAAY,EAAE;IACpDkB,IAAI,EAAEA;EACR,CAAC,EAAE,aAAa3B,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAE5H,UAAU,CAAC,EAAE,CAAC8D,MAAM,CAACjB,eAAe,EAAE,QAAQ,CAAC,GAAGxB,WAAW,GAAG,CAAC,CAAC,EAAExB,eAAe,CAACwB,WAAW,EAAE,EAAE,CAACyC,MAAM,CAACjB,eAAe,EAAE,aAAa,CAAC,EAAEoE,OAAO,CAAC,EAAEpH,eAAe,CAACwB,WAAW,EAAE,EAAE,CAACyC,MAAM,CAACjB,eAAe,EAAE,MAAM,CAAC,EAAEb,GAAG,CAAC,EAAEX,WAAW,CAAC,CAAC;IACrPH,GAAG,EAAEY;EACP,CAAC,EAAEyF,WAAW,CAAC,CAAC;AAClB,CAAC,CAAC;AACF,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChH,aAAa,CAACiH,WAAW,GAAG,eAAe;AAC7C;AACA,eAAejH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
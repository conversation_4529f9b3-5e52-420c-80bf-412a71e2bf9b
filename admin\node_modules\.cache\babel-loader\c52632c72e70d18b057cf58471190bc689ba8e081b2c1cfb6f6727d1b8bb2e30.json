{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n/**\n * respect https://github.com/cuth/postcss-pxtorem\n */\n// @ts-ignore\nimport unitless from '@emotion/unitless';\nvar pxRegex = /url\\([^)]+\\)|var\\([^)]+\\)|(\\d*\\.?\\d+)px/g;\nfunction toFixed(number, precision) {\n  var multiplier = Math.pow(10, precision + 1),\n    wholeNumber = Math.floor(number * multiplier);\n  return Math.round(wholeNumber / 10) * 10 / multiplier;\n}\nvar transform = function transform() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _options$rootValue = options.rootValue,\n    rootValue = _options$rootValue === void 0 ? 16 : _options$rootValue,\n    _options$precision = options.precision,\n    precision = _options$precision === void 0 ? 5 : _options$precision,\n    _options$mediaQuery = options.mediaQuery,\n    mediaQuery = _options$mediaQuery === void 0 ? false : _options$mediaQuery;\n  var pxReplace = function pxReplace(m, $1) {\n    if (!$1) return m;\n    var pixels = parseFloat($1);\n    // covenant: pixels <= 1, not transform to rem @zombieJ\n    if (pixels <= 1) return m;\n    var fixedVal = toFixed(pixels / rootValue, precision);\n    return \"\".concat(fixedVal, \"rem\");\n  };\n  var visit = function visit(cssObj) {\n    var clone = _objectSpread({}, cssObj);\n    Object.entries(cssObj).forEach(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 2),\n        key = _ref2[0],\n        value = _ref2[1];\n      if (typeof value === 'string' && value.includes('px')) {\n        var newValue = value.replace(pxRegex, pxReplace);\n        clone[key] = newValue;\n      }\n\n      // no unit\n      if (!unitless[key] && typeof value === 'number' && value !== 0) {\n        clone[key] = \"\".concat(value, \"px\").replace(pxRegex, pxReplace);\n      }\n\n      // Media queries\n      var mergedKey = key.trim();\n      if (mergedKey.startsWith('@') && mergedKey.includes('px') && mediaQuery) {\n        var newKey = key.replace(pxRegex, pxReplace);\n        clone[newKey] = clone[key];\n        delete clone[key];\n      }\n    });\n    return clone;\n  };\n  return {\n    visit: visit\n  };\n};\nexport default transform;", "map": {"version": 3, "names": ["_slicedToArray", "_objectSpread", "unitless", "pxRegex", "toFixed", "number", "precision", "multiplier", "Math", "pow", "wholeNumber", "floor", "round", "transform", "options", "arguments", "length", "undefined", "_options$rootValue", "rootValue", "_options$precision", "_options$mediaQuery", "mediaQuery", "px<PERSON><PERSON><PERSON>", "m", "$1", "pixels", "parseFloat", "fixedVal", "concat", "visit", "cssObj", "clone", "Object", "entries", "for<PERSON>ach", "_ref", "_ref2", "key", "value", "includes", "newValue", "replace", "mergedKey", "trim", "startsWith", "new<PERSON>ey"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/cssinjs/es/transformers/px2rem.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n/**\n * respect https://github.com/cuth/postcss-pxtorem\n */\n// @ts-ignore\nimport unitless from '@emotion/unitless';\nvar pxRegex = /url\\([^)]+\\)|var\\([^)]+\\)|(\\d*\\.?\\d+)px/g;\nfunction toFixed(number, precision) {\n  var multiplier = Math.pow(10, precision + 1),\n    wholeNumber = Math.floor(number * multiplier);\n  return Math.round(wholeNumber / 10) * 10 / multiplier;\n}\nvar transform = function transform() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _options$rootValue = options.rootValue,\n    rootValue = _options$rootValue === void 0 ? 16 : _options$rootValue,\n    _options$precision = options.precision,\n    precision = _options$precision === void 0 ? 5 : _options$precision,\n    _options$mediaQuery = options.mediaQuery,\n    mediaQuery = _options$mediaQuery === void 0 ? false : _options$mediaQuery;\n  var pxReplace = function pxReplace(m, $1) {\n    if (!$1) return m;\n    var pixels = parseFloat($1);\n    // covenant: pixels <= 1, not transform to rem @zombieJ\n    if (pixels <= 1) return m;\n    var fixedVal = toFixed(pixels / rootValue, precision);\n    return \"\".concat(fixedVal, \"rem\");\n  };\n  var visit = function visit(cssObj) {\n    var clone = _objectSpread({}, cssObj);\n    Object.entries(cssObj).forEach(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 2),\n        key = _ref2[0],\n        value = _ref2[1];\n      if (typeof value === 'string' && value.includes('px')) {\n        var newValue = value.replace(pxRegex, pxReplace);\n        clone[key] = newValue;\n      }\n\n      // no unit\n      if (!unitless[key] && typeof value === 'number' && value !== 0) {\n        clone[key] = \"\".concat(value, \"px\").replace(pxRegex, pxReplace);\n      }\n\n      // Media queries\n      var mergedKey = key.trim();\n      if (mergedKey.startsWith('@') && mergedKey.includes('px') && mediaQuery) {\n        var newKey = key.replace(pxRegex, pxReplace);\n        clone[newKey] = clone[key];\n        delete clone[key];\n      }\n    });\n    return clone;\n  };\n  return {\n    visit: visit\n  };\n};\nexport default transform;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA;AACA;AACA,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,IAAIC,OAAO,GAAG,0CAA0C;AACxD,SAASC,OAAOA,CAACC,MAAM,EAAEC,SAAS,EAAE;EAClC,IAAIC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEH,SAAS,GAAG,CAAC,CAAC;IAC1CI,WAAW,GAAGF,IAAI,CAACG,KAAK,CAACN,MAAM,GAAGE,UAAU,CAAC;EAC/C,OAAOC,IAAI,CAACI,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC,GAAG,EAAE,GAAGH,UAAU;AACvD;AACA,IAAIM,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;EACnC,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIG,kBAAkB,GAAGJ,OAAO,CAACK,SAAS;IACxCA,SAAS,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;IACnEE,kBAAkB,GAAGN,OAAO,CAACR,SAAS;IACtCA,SAAS,GAAGc,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,kBAAkB;IAClEC,mBAAmB,GAAGP,OAAO,CAACQ,UAAU;IACxCA,UAAU,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,mBAAmB;EAC3E,IAAIE,SAAS,GAAG,SAASA,SAASA,CAACC,CAAC,EAAEC,EAAE,EAAE;IACxC,IAAI,CAACA,EAAE,EAAE,OAAOD,CAAC;IACjB,IAAIE,MAAM,GAAGC,UAAU,CAACF,EAAE,CAAC;IAC3B;IACA,IAAIC,MAAM,IAAI,CAAC,EAAE,OAAOF,CAAC;IACzB,IAAII,QAAQ,GAAGxB,OAAO,CAACsB,MAAM,GAAGP,SAAS,EAAEb,SAAS,CAAC;IACrD,OAAO,EAAE,CAACuB,MAAM,CAACD,QAAQ,EAAE,KAAK,CAAC;EACnC,CAAC;EACD,IAAIE,KAAK,GAAG,SAASA,KAAKA,CAACC,MAAM,EAAE;IACjC,IAAIC,KAAK,GAAG/B,aAAa,CAAC,CAAC,CAAC,EAAE8B,MAAM,CAAC;IACrCE,MAAM,CAACC,OAAO,CAACH,MAAM,CAAC,CAACI,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC7C,IAAIC,KAAK,GAAGrC,cAAc,CAACoC,IAAI,EAAE,CAAC,CAAC;QACjCE,GAAG,GAAGD,KAAK,CAAC,CAAC,CAAC;QACdE,KAAK,GAAGF,KAAK,CAAC,CAAC,CAAC;MAClB,IAAI,OAAOE,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACrD,IAAIC,QAAQ,GAAGF,KAAK,CAACG,OAAO,CAACvC,OAAO,EAAEoB,SAAS,CAAC;QAChDS,KAAK,CAACM,GAAG,CAAC,GAAGG,QAAQ;MACvB;;MAEA;MACA,IAAI,CAACvC,QAAQ,CAACoC,GAAG,CAAC,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,CAAC,EAAE;QAC9DP,KAAK,CAACM,GAAG,CAAC,GAAG,EAAE,CAACT,MAAM,CAACU,KAAK,EAAE,IAAI,CAAC,CAACG,OAAO,CAACvC,OAAO,EAAEoB,SAAS,CAAC;MACjE;;MAEA;MACA,IAAIoB,SAAS,GAAGL,GAAG,CAACM,IAAI,CAAC,CAAC;MAC1B,IAAID,SAAS,CAACE,UAAU,CAAC,GAAG,CAAC,IAAIF,SAAS,CAACH,QAAQ,CAAC,IAAI,CAAC,IAAIlB,UAAU,EAAE;QACvE,IAAIwB,MAAM,GAAGR,GAAG,CAACI,OAAO,CAACvC,OAAO,EAAEoB,SAAS,CAAC;QAC5CS,KAAK,CAACc,MAAM,CAAC,GAAGd,KAAK,CAACM,GAAG,CAAC;QAC1B,OAAON,KAAK,CAACM,GAAG,CAAC;MACnB;IACF,CAAC,CAAC;IACF,OAAON,KAAK;EACd,CAAC;EACD,OAAO;IACLF,KAAK,EAAEA;EACT,CAAC;AACH,CAAC;AACD,eAAejB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
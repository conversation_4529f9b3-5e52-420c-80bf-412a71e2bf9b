{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport hash from '@emotion/hash';\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { useContext } from 'react';\nimport StyleContext, { ATTR_MARK, ATTR_TOKEN, CSS_IN_JS_INSTANCE } from \"../StyleContext\";\nimport { flattenToken, memoResult, token2key, toStyleStr } from \"../util\";\nimport { transformToken } from \"../util/css-variables\";\nimport useGlobalCache from \"./useGlobalCache\";\nvar EMPTY_OVERRIDE = {};\n\n// Generate different prefix to make user selector break in production env.\n// This helps developer not to do style override directly on the hash id.\nvar hashPrefix = process.env.NODE_ENV !== 'production' ? 'css-dev-only-do-not-override' : 'css';\nvar tokenKeys = new Map();\nfunction recordCleanToken(tokenKey) {\n  tokenKeys.set(tokenKey, (tokenKeys.get(tokenKey) || 0) + 1);\n}\nfunction removeStyleTags(key, instanceId) {\n  if (typeof document !== 'undefined') {\n    var styles = document.querySelectorAll(\"style[\".concat(ATTR_TOKEN, \"=\\\"\").concat(key, \"\\\"]\"));\n    styles.forEach(function (style) {\n      if (style[CSS_IN_JS_INSTANCE] === instanceId) {\n        var _style$parentNode;\n        (_style$parentNode = style.parentNode) === null || _style$parentNode === void 0 || _style$parentNode.removeChild(style);\n      }\n    });\n  }\n}\nvar TOKEN_THRESHOLD = 0;\n\n// Remove will check current keys first\nfunction cleanTokenStyle(tokenKey, instanceId) {\n  tokenKeys.set(tokenKey, (tokenKeys.get(tokenKey) || 0) - 1);\n  var tokenKeyList = Array.from(tokenKeys.keys());\n  var cleanableKeyList = tokenKeyList.filter(function (key) {\n    var count = tokenKeys.get(key) || 0;\n    return count <= 0;\n  });\n\n  // Should keep tokens under threshold for not to insert style too often\n  if (tokenKeyList.length - cleanableKeyList.length > TOKEN_THRESHOLD) {\n    cleanableKeyList.forEach(function (key) {\n      removeStyleTags(key, instanceId);\n      tokenKeys.delete(key);\n    });\n  }\n}\nexport var getComputedToken = function getComputedToken(originToken, overrideToken, theme, format) {\n  var derivativeToken = theme.getDerivativeToken(originToken);\n\n  // Merge with override\n  var mergedDerivativeToken = _objectSpread(_objectSpread({}, derivativeToken), overrideToken);\n\n  // Format if needed\n  if (format) {\n    mergedDerivativeToken = format(mergedDerivativeToken);\n  }\n  return mergedDerivativeToken;\n};\nexport var TOKEN_PREFIX = 'token';\n/**\n * Cache theme derivative token as global shared one\n * @param theme Theme entity\n * @param tokens List of tokens, used for cache. Please do not dynamic generate object directly\n * @param option Additional config\n * @returns Call Theme.getDerivativeToken(tokenObject) to get token\n */\nexport default function useCacheToken(theme, tokens) {\n  var option = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var _useContext = useContext(StyleContext),\n    instanceId = _useContext.cache.instanceId,\n    container = _useContext.container;\n  var _option$salt = option.salt,\n    salt = _option$salt === void 0 ? '' : _option$salt,\n    _option$override = option.override,\n    override = _option$override === void 0 ? EMPTY_OVERRIDE : _option$override,\n    formatToken = option.formatToken,\n    compute = option.getComputedToken,\n    cssVar = option.cssVar;\n\n  // Basic - We do basic cache here\n  var mergedToken = memoResult(function () {\n    return Object.assign.apply(Object, [{}].concat(_toConsumableArray(tokens)));\n  }, tokens);\n  var tokenStr = flattenToken(mergedToken);\n  var overrideTokenStr = flattenToken(override);\n  var cssVarStr = cssVar ? flattenToken(cssVar) : '';\n  var cachedToken = useGlobalCache(TOKEN_PREFIX, [salt, theme.id, tokenStr, overrideTokenStr, cssVarStr], function () {\n    var _cssVar$key;\n    var mergedDerivativeToken = compute ? compute(mergedToken, override, theme) : getComputedToken(mergedToken, override, theme, formatToken);\n\n    // Replace token value with css variables\n    var actualToken = _objectSpread({}, mergedDerivativeToken);\n    var cssVarsStr = '';\n    if (!!cssVar) {\n      var _transformToken = transformToken(mergedDerivativeToken, cssVar.key, {\n        prefix: cssVar.prefix,\n        ignore: cssVar.ignore,\n        unitless: cssVar.unitless,\n        preserve: cssVar.preserve\n      });\n      var _transformToken2 = _slicedToArray(_transformToken, 2);\n      mergedDerivativeToken = _transformToken2[0];\n      cssVarsStr = _transformToken2[1];\n    }\n\n    // Optimize for `useStyleRegister` performance\n    var tokenKey = token2key(mergedDerivativeToken, salt);\n    mergedDerivativeToken._tokenKey = tokenKey;\n    actualToken._tokenKey = token2key(actualToken, salt);\n    var themeKey = (_cssVar$key = cssVar === null || cssVar === void 0 ? void 0 : cssVar.key) !== null && _cssVar$key !== void 0 ? _cssVar$key : tokenKey;\n    mergedDerivativeToken._themeKey = themeKey;\n    recordCleanToken(themeKey);\n    var hashId = \"\".concat(hashPrefix, \"-\").concat(hash(tokenKey));\n    mergedDerivativeToken._hashId = hashId; // Not used\n\n    return [mergedDerivativeToken, hashId, actualToken, cssVarsStr, (cssVar === null || cssVar === void 0 ? void 0 : cssVar.key) || ''];\n  }, function (cache) {\n    // Remove token will remove all related style\n    cleanTokenStyle(cache[0]._themeKey, instanceId);\n  }, function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 4),\n      token = _ref2[0],\n      cssVarsStr = _ref2[3];\n    if (cssVar && cssVarsStr) {\n      var style = updateCSS(cssVarsStr, hash(\"css-variables-\".concat(token._themeKey)), {\n        mark: ATTR_MARK,\n        prepend: 'queue',\n        attachTo: container,\n        priority: -999\n      });\n      style[CSS_IN_JS_INSTANCE] = instanceId;\n\n      // Used for `useCacheToken` to remove on batch when token removed\n      style.setAttribute(ATTR_TOKEN, token._themeKey);\n    }\n  });\n  return cachedToken;\n}\nexport var extract = function extract(cache, effectStyles, options) {\n  var _cache = _slicedToArray(cache, 5),\n    realToken = _cache[2],\n    styleStr = _cache[3],\n    cssVarKey = _cache[4];\n  var _ref3 = options || {},\n    plain = _ref3.plain;\n  if (!styleStr) {\n    return null;\n  }\n  var styleId = realToken._tokenKey;\n  var order = -999;\n\n  // ====================== Style ======================\n  // Used for rc-util\n  var sharedAttrs = {\n    'data-rc-order': 'prependQueue',\n    'data-rc-priority': \"\".concat(order)\n  };\n  var styleText = toStyleStr(styleStr, cssVarKey, styleId, sharedAttrs, plain);\n  return [order, styleId, styleText];\n};", "map": {"version": 3, "names": ["_slicedToArray", "_toConsumableArray", "_objectSpread", "hash", "updateCSS", "useContext", "StyleContext", "ATTR_MARK", "ATTR_TOKEN", "CSS_IN_JS_INSTANCE", "flattenToken", "memoResult", "token2key", "toStyleStr", "transformToken", "useGlobalCache", "EMPTY_OVERRIDE", "hashPrefix", "process", "env", "NODE_ENV", "tokenKeys", "Map", "recordCleanToken", "<PERSON><PERSON><PERSON>", "set", "get", "removeStyleTags", "key", "instanceId", "document", "styles", "querySelectorAll", "concat", "for<PERSON>ach", "style", "_style$parentNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "TOKEN_THRESHOLD", "cleanTokenStyle", "tokenKeyList", "Array", "from", "keys", "cleanableKeyList", "filter", "count", "length", "delete", "getComputedToken", "originToken", "overrideToken", "theme", "format", "derivativeToken", "getDerivativeToken", "mergedDerivativeToken", "TOKEN_PREFIX", "useCacheToken", "tokens", "option", "arguments", "undefined", "_useContext", "cache", "container", "_option$salt", "salt", "_option$override", "override", "formatToken", "compute", "cssVar", "mergedToken", "Object", "assign", "apply", "tokenStr", "overrideTokenStr", "cssVarStr", "cachedToken", "id", "_cssVar$key", "actualToken", "cssVarsStr", "_transformToken", "prefix", "ignore", "unitless", "preserve", "_transformToken2", "_token<PERSON>ey", "<PERSON><PERSON><PERSON>", "_theme<PERSON>ey", "hashId", "_hashId", "_ref", "_ref2", "token", "mark", "prepend", "attachTo", "priority", "setAttribute", "extract", "effectStyles", "options", "_cache", "realToken", "styleStr", "cssVarKey", "_ref3", "plain", "styleId", "order", "sharedAttrs", "styleText"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/cssinjs/es/hooks/useCacheToken.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport hash from '@emotion/hash';\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { useContext } from 'react';\nimport StyleContext, { ATTR_MARK, ATTR_TOKEN, CSS_IN_JS_INSTANCE } from \"../StyleContext\";\nimport { flattenToken, memoResult, token2key, toStyleStr } from \"../util\";\nimport { transformToken } from \"../util/css-variables\";\nimport useGlobalCache from \"./useGlobalCache\";\nvar EMPTY_OVERRIDE = {};\n\n// Generate different prefix to make user selector break in production env.\n// This helps developer not to do style override directly on the hash id.\nvar hashPrefix = process.env.NODE_ENV !== 'production' ? 'css-dev-only-do-not-override' : 'css';\nvar tokenKeys = new Map();\nfunction recordCleanToken(tokenKey) {\n  tokenKeys.set(tokenKey, (tokenKeys.get(tokenKey) || 0) + 1);\n}\nfunction removeStyleTags(key, instanceId) {\n  if (typeof document !== 'undefined') {\n    var styles = document.querySelectorAll(\"style[\".concat(ATTR_TOKEN, \"=\\\"\").concat(key, \"\\\"]\"));\n    styles.forEach(function (style) {\n      if (style[CSS_IN_JS_INSTANCE] === instanceId) {\n        var _style$parentNode;\n        (_style$parentNode = style.parentNode) === null || _style$parentNode === void 0 || _style$parentNode.removeChild(style);\n      }\n    });\n  }\n}\nvar TOKEN_THRESHOLD = 0;\n\n// Remove will check current keys first\nfunction cleanTokenStyle(tokenKey, instanceId) {\n  tokenKeys.set(tokenKey, (tokenKeys.get(tokenKey) || 0) - 1);\n  var tokenKeyList = Array.from(tokenKeys.keys());\n  var cleanableKeyList = tokenKeyList.filter(function (key) {\n    var count = tokenKeys.get(key) || 0;\n    return count <= 0;\n  });\n\n  // Should keep tokens under threshold for not to insert style too often\n  if (tokenKeyList.length - cleanableKeyList.length > TOKEN_THRESHOLD) {\n    cleanableKeyList.forEach(function (key) {\n      removeStyleTags(key, instanceId);\n      tokenKeys.delete(key);\n    });\n  }\n}\nexport var getComputedToken = function getComputedToken(originToken, overrideToken, theme, format) {\n  var derivativeToken = theme.getDerivativeToken(originToken);\n\n  // Merge with override\n  var mergedDerivativeToken = _objectSpread(_objectSpread({}, derivativeToken), overrideToken);\n\n  // Format if needed\n  if (format) {\n    mergedDerivativeToken = format(mergedDerivativeToken);\n  }\n  return mergedDerivativeToken;\n};\nexport var TOKEN_PREFIX = 'token';\n/**\n * Cache theme derivative token as global shared one\n * @param theme Theme entity\n * @param tokens List of tokens, used for cache. Please do not dynamic generate object directly\n * @param option Additional config\n * @returns Call Theme.getDerivativeToken(tokenObject) to get token\n */\nexport default function useCacheToken(theme, tokens) {\n  var option = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var _useContext = useContext(StyleContext),\n    instanceId = _useContext.cache.instanceId,\n    container = _useContext.container;\n  var _option$salt = option.salt,\n    salt = _option$salt === void 0 ? '' : _option$salt,\n    _option$override = option.override,\n    override = _option$override === void 0 ? EMPTY_OVERRIDE : _option$override,\n    formatToken = option.formatToken,\n    compute = option.getComputedToken,\n    cssVar = option.cssVar;\n\n  // Basic - We do basic cache here\n  var mergedToken = memoResult(function () {\n    return Object.assign.apply(Object, [{}].concat(_toConsumableArray(tokens)));\n  }, tokens);\n  var tokenStr = flattenToken(mergedToken);\n  var overrideTokenStr = flattenToken(override);\n  var cssVarStr = cssVar ? flattenToken(cssVar) : '';\n  var cachedToken = useGlobalCache(TOKEN_PREFIX, [salt, theme.id, tokenStr, overrideTokenStr, cssVarStr], function () {\n    var _cssVar$key;\n    var mergedDerivativeToken = compute ? compute(mergedToken, override, theme) : getComputedToken(mergedToken, override, theme, formatToken);\n\n    // Replace token value with css variables\n    var actualToken = _objectSpread({}, mergedDerivativeToken);\n    var cssVarsStr = '';\n    if (!!cssVar) {\n      var _transformToken = transformToken(mergedDerivativeToken, cssVar.key, {\n        prefix: cssVar.prefix,\n        ignore: cssVar.ignore,\n        unitless: cssVar.unitless,\n        preserve: cssVar.preserve\n      });\n      var _transformToken2 = _slicedToArray(_transformToken, 2);\n      mergedDerivativeToken = _transformToken2[0];\n      cssVarsStr = _transformToken2[1];\n    }\n\n    // Optimize for `useStyleRegister` performance\n    var tokenKey = token2key(mergedDerivativeToken, salt);\n    mergedDerivativeToken._tokenKey = tokenKey;\n    actualToken._tokenKey = token2key(actualToken, salt);\n    var themeKey = (_cssVar$key = cssVar === null || cssVar === void 0 ? void 0 : cssVar.key) !== null && _cssVar$key !== void 0 ? _cssVar$key : tokenKey;\n    mergedDerivativeToken._themeKey = themeKey;\n    recordCleanToken(themeKey);\n    var hashId = \"\".concat(hashPrefix, \"-\").concat(hash(tokenKey));\n    mergedDerivativeToken._hashId = hashId; // Not used\n\n    return [mergedDerivativeToken, hashId, actualToken, cssVarsStr, (cssVar === null || cssVar === void 0 ? void 0 : cssVar.key) || ''];\n  }, function (cache) {\n    // Remove token will remove all related style\n    cleanTokenStyle(cache[0]._themeKey, instanceId);\n  }, function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 4),\n      token = _ref2[0],\n      cssVarsStr = _ref2[3];\n    if (cssVar && cssVarsStr) {\n      var style = updateCSS(cssVarsStr, hash(\"css-variables-\".concat(token._themeKey)), {\n        mark: ATTR_MARK,\n        prepend: 'queue',\n        attachTo: container,\n        priority: -999\n      });\n      style[CSS_IN_JS_INSTANCE] = instanceId;\n\n      // Used for `useCacheToken` to remove on batch when token removed\n      style.setAttribute(ATTR_TOKEN, token._themeKey);\n    }\n  });\n  return cachedToken;\n}\nexport var extract = function extract(cache, effectStyles, options) {\n  var _cache = _slicedToArray(cache, 5),\n    realToken = _cache[2],\n    styleStr = _cache[3],\n    cssVarKey = _cache[4];\n  var _ref3 = options || {},\n    plain = _ref3.plain;\n  if (!styleStr) {\n    return null;\n  }\n  var styleId = realToken._tokenKey;\n  var order = -999;\n\n  // ====================== Style ======================\n  // Used for rc-util\n  var sharedAttrs = {\n    'data-rc-order': 'prependQueue',\n    'data-rc-priority': \"\".concat(order)\n  };\n  var styleText = toStyleStr(styleStr, cssVarKey, styleId, sharedAttrs, plain);\n  return [order, styleId, styleText];\n};"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,IAAI,MAAM,eAAe;AAChC,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,YAAY,IAAIC,SAAS,EAAEC,UAAU,EAAEC,kBAAkB,QAAQ,iBAAiB;AACzF,SAASC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,UAAU,QAAQ,SAAS;AACzE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,IAAIC,cAAc,GAAG,CAAC,CAAC;;AAEvB;AACA;AACA,IAAIC,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,8BAA8B,GAAG,KAAK;AAC/F,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AACzB,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAClCH,SAAS,CAACI,GAAG,CAACD,QAAQ,EAAE,CAACH,SAAS,CAACK,GAAG,CAACF,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7D;AACA,SAASG,eAAeA,CAACC,GAAG,EAAEC,UAAU,EAAE;EACxC,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACnC,IAAIC,MAAM,GAAGD,QAAQ,CAACE,gBAAgB,CAAC,QAAQ,CAACC,MAAM,CAACzB,UAAU,EAAE,KAAK,CAAC,CAACyB,MAAM,CAACL,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7FG,MAAM,CAACG,OAAO,CAAC,UAAUC,KAAK,EAAE;MAC9B,IAAIA,KAAK,CAAC1B,kBAAkB,CAAC,KAAKoB,UAAU,EAAE;QAC5C,IAAIO,iBAAiB;QACrB,CAACA,iBAAiB,GAAGD,KAAK,CAACE,UAAU,MAAM,IAAI,IAAID,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACE,WAAW,CAACH,KAAK,CAAC;MACzH;IACF,CAAC,CAAC;EACJ;AACF;AACA,IAAII,eAAe,GAAG,CAAC;;AAEvB;AACA,SAASC,eAAeA,CAAChB,QAAQ,EAAEK,UAAU,EAAE;EAC7CR,SAAS,CAACI,GAAG,CAACD,QAAQ,EAAE,CAACH,SAAS,CAACK,GAAG,CAACF,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3D,IAAIiB,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACtB,SAAS,CAACuB,IAAI,CAAC,CAAC,CAAC;EAC/C,IAAIC,gBAAgB,GAAGJ,YAAY,CAACK,MAAM,CAAC,UAAUlB,GAAG,EAAE;IACxD,IAAImB,KAAK,GAAG1B,SAAS,CAACK,GAAG,CAACE,GAAG,CAAC,IAAI,CAAC;IACnC,OAAOmB,KAAK,IAAI,CAAC;EACnB,CAAC,CAAC;;EAEF;EACA,IAAIN,YAAY,CAACO,MAAM,GAAGH,gBAAgB,CAACG,MAAM,GAAGT,eAAe,EAAE;IACnEM,gBAAgB,CAACX,OAAO,CAAC,UAAUN,GAAG,EAAE;MACtCD,eAAe,CAACC,GAAG,EAAEC,UAAU,CAAC;MAChCR,SAAS,CAAC4B,MAAM,CAACrB,GAAG,CAAC;IACvB,CAAC,CAAC;EACJ;AACF;AACA,OAAO,IAAIsB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,WAAW,EAAEC,aAAa,EAAEC,KAAK,EAAEC,MAAM,EAAE;EACjG,IAAIC,eAAe,GAAGF,KAAK,CAACG,kBAAkB,CAACL,WAAW,CAAC;;EAE3D;EACA,IAAIM,qBAAqB,GAAGvD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,eAAe,CAAC,EAAEH,aAAa,CAAC;;EAE5F;EACA,IAAIE,MAAM,EAAE;IACVG,qBAAqB,GAAGH,MAAM,CAACG,qBAAqB,CAAC;EACvD;EACA,OAAOA,qBAAqB;AAC9B,CAAC;AACD,OAAO,IAAIC,YAAY,GAAG,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACN,KAAK,EAAEO,MAAM,EAAE;EACnD,IAAIC,MAAM,GAAGC,SAAS,CAACd,MAAM,GAAG,CAAC,IAAIc,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIE,WAAW,GAAG3D,UAAU,CAACC,YAAY,CAAC;IACxCuB,UAAU,GAAGmC,WAAW,CAACC,KAAK,CAACpC,UAAU;IACzCqC,SAAS,GAAGF,WAAW,CAACE,SAAS;EACnC,IAAIC,YAAY,GAAGN,MAAM,CAACO,IAAI;IAC5BA,IAAI,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,YAAY;IAClDE,gBAAgB,GAAGR,MAAM,CAACS,QAAQ;IAClCA,QAAQ,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAGrD,cAAc,GAAGqD,gBAAgB;IAC1EE,WAAW,GAAGV,MAAM,CAACU,WAAW;IAChCC,OAAO,GAAGX,MAAM,CAACX,gBAAgB;IACjCuB,MAAM,GAAGZ,MAAM,CAACY,MAAM;;EAExB;EACA,IAAIC,WAAW,GAAG/D,UAAU,CAAC,YAAY;IACvC,OAAOgE,MAAM,CAACC,MAAM,CAACC,KAAK,CAACF,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC1C,MAAM,CAAChC,kBAAkB,CAAC2D,MAAM,CAAC,CAAC,CAAC;EAC7E,CAAC,EAAEA,MAAM,CAAC;EACV,IAAIkB,QAAQ,GAAGpE,YAAY,CAACgE,WAAW,CAAC;EACxC,IAAIK,gBAAgB,GAAGrE,YAAY,CAAC4D,QAAQ,CAAC;EAC7C,IAAIU,SAAS,GAAGP,MAAM,GAAG/D,YAAY,CAAC+D,MAAM,CAAC,GAAG,EAAE;EAClD,IAAIQ,WAAW,GAAGlE,cAAc,CAAC2C,YAAY,EAAE,CAACU,IAAI,EAAEf,KAAK,CAAC6B,EAAE,EAAEJ,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,CAAC,EAAE,YAAY;IAClH,IAAIG,WAAW;IACf,IAAI1B,qBAAqB,GAAGe,OAAO,GAAGA,OAAO,CAACE,WAAW,EAAEJ,QAAQ,EAAEjB,KAAK,CAAC,GAAGH,gBAAgB,CAACwB,WAAW,EAAEJ,QAAQ,EAAEjB,KAAK,EAAEkB,WAAW,CAAC;;IAEzI;IACA,IAAIa,WAAW,GAAGlF,aAAa,CAAC,CAAC,CAAC,EAAEuD,qBAAqB,CAAC;IAC1D,IAAI4B,UAAU,GAAG,EAAE;IACnB,IAAI,CAAC,CAACZ,MAAM,EAAE;MACZ,IAAIa,eAAe,GAAGxE,cAAc,CAAC2C,qBAAqB,EAAEgB,MAAM,CAAC7C,GAAG,EAAE;QACtE2D,MAAM,EAAEd,MAAM,CAACc,MAAM;QACrBC,MAAM,EAAEf,MAAM,CAACe,MAAM;QACrBC,QAAQ,EAAEhB,MAAM,CAACgB,QAAQ;QACzBC,QAAQ,EAAEjB,MAAM,CAACiB;MACnB,CAAC,CAAC;MACF,IAAIC,gBAAgB,GAAG3F,cAAc,CAACsF,eAAe,EAAE,CAAC,CAAC;MACzD7B,qBAAqB,GAAGkC,gBAAgB,CAAC,CAAC,CAAC;MAC3CN,UAAU,GAAGM,gBAAgB,CAAC,CAAC,CAAC;IAClC;;IAEA;IACA,IAAInE,QAAQ,GAAGZ,SAAS,CAAC6C,qBAAqB,EAAEW,IAAI,CAAC;IACrDX,qBAAqB,CAACmC,SAAS,GAAGpE,QAAQ;IAC1C4D,WAAW,CAACQ,SAAS,GAAGhF,SAAS,CAACwE,WAAW,EAAEhB,IAAI,CAAC;IACpD,IAAIyB,QAAQ,GAAG,CAACV,WAAW,GAAGV,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC7C,GAAG,MAAM,IAAI,IAAIuD,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG3D,QAAQ;IACrJiC,qBAAqB,CAACqC,SAAS,GAAGD,QAAQ;IAC1CtE,gBAAgB,CAACsE,QAAQ,CAAC;IAC1B,IAAIE,MAAM,GAAG,EAAE,CAAC9D,MAAM,CAAChB,UAAU,EAAE,GAAG,CAAC,CAACgB,MAAM,CAAC9B,IAAI,CAACqB,QAAQ,CAAC,CAAC;IAC9DiC,qBAAqB,CAACuC,OAAO,GAAGD,MAAM,CAAC,CAAC;;IAExC,OAAO,CAACtC,qBAAqB,EAAEsC,MAAM,EAAEX,WAAW,EAAEC,UAAU,EAAE,CAACZ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC7C,GAAG,KAAK,EAAE,CAAC;EACrI,CAAC,EAAE,UAAUqC,KAAK,EAAE;IAClB;IACAzB,eAAe,CAACyB,KAAK,CAAC,CAAC,CAAC,CAAC6B,SAAS,EAAEjE,UAAU,CAAC;EACjD,CAAC,EAAE,UAAUoE,IAAI,EAAE;IACjB,IAAIC,KAAK,GAAGlG,cAAc,CAACiG,IAAI,EAAE,CAAC,CAAC;MACjCE,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC;MAChBb,UAAU,GAAGa,KAAK,CAAC,CAAC,CAAC;IACvB,IAAIzB,MAAM,IAAIY,UAAU,EAAE;MACxB,IAAIlD,KAAK,GAAG/B,SAAS,CAACiF,UAAU,EAAElF,IAAI,CAAC,gBAAgB,CAAC8B,MAAM,CAACkE,KAAK,CAACL,SAAS,CAAC,CAAC,EAAE;QAChFM,IAAI,EAAE7F,SAAS;QACf8F,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAEpC,SAAS;QACnBqC,QAAQ,EAAE,CAAC;MACb,CAAC,CAAC;MACFpE,KAAK,CAAC1B,kBAAkB,CAAC,GAAGoB,UAAU;;MAEtC;MACAM,KAAK,CAACqE,YAAY,CAAChG,UAAU,EAAE2F,KAAK,CAACL,SAAS,CAAC;IACjD;EACF,CAAC,CAAC;EACF,OAAOb,WAAW;AACpB;AACA,OAAO,IAAIwB,OAAO,GAAG,SAASA,OAAOA,CAACxC,KAAK,EAAEyC,YAAY,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM,GAAG5G,cAAc,CAACiE,KAAK,EAAE,CAAC,CAAC;IACnC4C,SAAS,GAAGD,MAAM,CAAC,CAAC,CAAC;IACrBE,QAAQ,GAAGF,MAAM,CAAC,CAAC,CAAC;IACpBG,SAAS,GAAGH,MAAM,CAAC,CAAC,CAAC;EACvB,IAAII,KAAK,GAAGL,OAAO,IAAI,CAAC,CAAC;IACvBM,KAAK,GAAGD,KAAK,CAACC,KAAK;EACrB,IAAI,CAACH,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,IAAII,OAAO,GAAGL,SAAS,CAACjB,SAAS;EACjC,IAAIuB,KAAK,GAAG,CAAC,GAAG;;EAEhB;EACA;EACA,IAAIC,WAAW,GAAG;IAChB,eAAe,EAAE,cAAc;IAC/B,kBAAkB,EAAE,EAAE,CAACnF,MAAM,CAACkF,KAAK;EACrC,CAAC;EACD,IAAIE,SAAS,GAAGxG,UAAU,CAACiG,QAAQ,EAAEC,SAAS,EAAEG,OAAO,EAAEE,WAAW,EAAEH,KAAK,CAAC;EAC5E,OAAO,CAACE,KAAK,EAAED,OAAO,EAAEG,SAAS,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
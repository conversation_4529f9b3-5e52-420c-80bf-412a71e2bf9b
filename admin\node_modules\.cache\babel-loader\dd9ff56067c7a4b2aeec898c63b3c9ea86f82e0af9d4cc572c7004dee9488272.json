{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderPending.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, message, Modal, Form, Select, Input, Row, Col, Statistic } from 'antd';\nimport { CheckOutlined, CloseOutlined, EyeOutlined, ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\n// 模拟待处理订单数据\nconst mockPendingOrders = [{\n  id: 1,\n  orderNo: 'ORD202401150003',\n  customerName: '王五',\n  customerPhone: '13800138003',\n  productName: '中国联通青春套餐',\n  operator: '中国联通',\n  priority: 'normal',\n  createdAt: '2024-01-15 12:00:00',\n  waitingTime: 2\n}, {\n  id: 2,\n  orderNo: 'ORD202401150005',\n  customerName: '钱七',\n  customerPhone: '13800138005',\n  productName: '中国移动5G尊享套餐',\n  operator: '中国移动',\n  priority: 'high',\n  createdAt: '2024-01-15 14:30:00',\n  waitingTime: 0.5\n}, {\n  id: 3,\n  orderNo: 'ORD202401150006',\n  customerName: '孙八',\n  customerPhone: '13800138006',\n  productName: '中国电信天翼套餐',\n  operator: '中国电信',\n  priority: 'urgent',\n  createdAt: '2024-01-15 15:00:00',\n  waitingTime: 0.2\n}, {\n  id: 4,\n  orderNo: 'ORD202401150007',\n  customerName: '李九',\n  customerPhone: '13800138007',\n  productName: '中国广电智慧套餐',\n  operator: '中国广电',\n  priority: 'normal',\n  createdAt: '2024-01-15 16:00:00',\n  waitingTime: 1.5\n}];\nconst OrderPending = () => {\n  _s();\n  const [orders, setOrders] = useState(mockPendingOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [processModalVisible, setProcessModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [form] = Form.useForm();\n\n  // 优先级颜色映射\n  const getPriorityColor = priority => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red'\n    };\n    return colors[priority] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = priority => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急'\n    };\n    return texts[priority] || priority;\n  };\n\n  // 统计数据\n  const stats = {\n    total: orders.length,\n    urgent: orders.filter(order => order.priority === 'urgent').length,\n    high: orders.filter(order => order.priority === 'high').length,\n    processing: orders.filter(order => order.waitingTime > 1).length // 等待超过1小时的订单\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '优先级',\n    dataIndex: 'priority',\n    key: 'priority',\n    width: 80,\n    render: priority => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getPriorityColor(priority),\n      children: getPriorityText(priority)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => {\n      const priorityOrder = {\n        urgent: 4,\n        high: 3,\n        normal: 2,\n        low: 1\n      };\n      return priorityOrder[a.priority] - priorityOrder[b.priority];\n    }\n  }, {\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '等待时间',\n    dataIndex: 'waitingTime',\n    key: 'waitingTime',\n    width: 100,\n    render: time => /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        color: time > 1 ? '#f5222d' : '#52c41a'\n      },\n      children: time < 1 ? `${Math.round(time * 60)}分钟` : `${time.toFixed(1)}小时`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.waitingTime - b.waitingTime\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(CheckOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleProcessOrder(record),\n        children: \"\\u5904\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        danger: true,\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleCancelOrder(record.id),\n        children: \"\\u53D6\\u6D88\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleProcessOrder = order => {\n    setSelectedOrder(order);\n    setProcessModalVisible(true);\n  };\n  const handleViewOrder = order => {\n    message.info(`查看订单 ${order.orderNo} 详情功能开发中...`);\n  };\n  const handleCancelOrder = id => {\n    Modal.confirm({\n      title: '确认取消订单',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 13\n      }, this),\n      content: '确定要取消这个订单吗？此操作不可恢复。',\n      onOk: () => {\n        setOrders(orders.filter(order => order.id !== id));\n        message.success('订单已取消');\n      }\n    });\n  };\n  const handleBatchProcess = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要处理的订单');\n      return;\n    }\n    Modal.confirm({\n      title: '批量处理订单',\n      content: `确定要批量处理选中的 ${selectedRowKeys.length} 个订单吗？`,\n      onOk: () => {\n        setOrders(orders.filter(order => !selectedRowKeys.includes(order.id)));\n        setSelectedRowKeys([]);\n        message.success('批量处理成功');\n      }\n    });\n  };\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockPendingOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n  const handleProcessSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 模拟处理订单\n      setOrders(orders.filter(order => order.id !== (selectedOrder === null || selectedOrder === void 0 ? void 0 : selectedOrder.id)));\n      setProcessModalVisible(false);\n      form.resetFields();\n      message.success('订单处理成功');\n    } catch (error) {\n      console.error('处理失败:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#faad14'\n        },\n        children: \"\\u5F85\\u5904\\u7406\\u8BA2\\u5355\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u5904\\u7406\\u9700\\u8981\\u4EBA\\u5DE5\\u5BA1\\u6838\\u7684\\u8BA2\\u5355\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\\u603B\\u6570\",\n            value: stats.total,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7D27\\u6025\\u8BA2\\u5355\",\n            value: stats.urgent,\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9AD8\\u4F18\\u5148\\u7EA7\",\n            value: stats.high,\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8D85\\u65F6\\u8BA2\\u5355\",\n            value: stats.processing,\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", orders.length, \" \\u4E2A\\u5F85\\u5904\\u7406\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              marginLeft: 16,\n              color: '#1890ff'\n            },\n            children: [\"\\u5DF2\\u9009\\u62E9 \", selectedRowKeys.length, \" \\u4E2A\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleBatchProcess,\n            children: \"\\u6279\\u91CF\\u5904\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: orders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5904\\u7406\\u8BA2\\u5355\",\n      open: processModalVisible,\n      onOk: handleProcessSubmit,\n      onCancel: () => {\n        setProcessModalVisible(false);\n        form.resetFields();\n      },\n      okText: \"\\u786E\\u8BA4\\u5904\\u7406\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16,\n            padding: 16,\n            background: '#f5f5f5',\n            borderRadius: 6\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u8BA2\\u5355\\u4FE1\\u606F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u8BA2\\u5355\\u53F7\\uFF1A\", selectedOrder.orderNo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u5BA2\\u6237\\uFF1A\", selectedOrder.customerName, \" (\", selectedOrder.customerPhone, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u4EA7\\u54C1\\uFF1A\", selectedOrder.productName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u91D1\\u989D\\uFF1A\\xA5\", selectedOrder.amount.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"action\",\n            label: \"\\u5904\\u7406\\u52A8\\u4F5C\",\n            rules: [{\n              required: true,\n              message: '请选择处理动作'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u5904\\u7406\\u52A8\\u4F5C\",\n              children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                value: \"approve\",\n                children: \"\\u6279\\u51C6\\u8BA2\\u5355\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                value: \"reject\",\n                children: \"\\u62D2\\u7EDD\\u8BA2\\u5355\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                value: \"pending_info\",\n                children: \"\\u7B49\\u5F85\\u8865\\u5145\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"remark\",\n            label: \"\\u5904\\u7406\\u5907\\u6CE8\",\n            rules: [{\n              required: true,\n              message: '请输入处理备注'\n            }],\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              rows: 4,\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5904\\u7406\\u5907\\u6CE8\\u6216\\u62D2\\u7EDD\\u539F\\u56E0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 309,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderPending, \"RJx/FkvMdDCBhSyxg9M92ovo3/A=\", false, function () {\n  return [Form.useForm];\n});\n_c = OrderPending;\nexport default OrderPending;\nvar _c;\n$RefreshReg$(_c, \"OrderPending\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "message", "Modal", "Form", "Select", "Input", "Row", "Col", "Statistic", "CheckOutlined", "CloseOutlined", "EyeOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "mockPendingOrders", "id", "orderNo", "customerName", "customerPhone", "productName", "operator", "priority", "createdAt", "waitingTime", "OrderPending", "_s", "orders", "setOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "processModalVisible", "setProcessModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "form", "useForm", "getPriorityColor", "colors", "low", "normal", "high", "urgent", "getPriorityText", "texts", "stats", "total", "length", "filter", "order", "processing", "columns", "title", "dataIndex", "key", "width", "render", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sorter", "a", "b", "priorityOrder", "text", "code", "style", "fontSize", "_", "record", "fontWeight", "ellipsis", "time", "Math", "round", "toFixed", "size", "type", "icon", "onClick", "handleProcessOrder", "handleViewOrder", "danger", "handleCancelOrder", "info", "confirm", "content", "onOk", "success", "handleBatchProcess", "warning", "includes", "handleRefresh", "setTimeout", "handleProcessSubmit", "values", "validateFields", "resetFields", "error", "console", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "display", "justifyContent", "alignItems", "strong", "marginLeft", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "okText", "cancelText", "padding", "background", "borderRadius", "marginTop", "amount", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "Option", "rows", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderPending.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  message,\n  Modal,\n  Form,\n  Select,\n  Input,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport {\n  CheckOutlined,\n  CloseOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface PendingOrder {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  productName: string;\n  operator: string;\n  priority: 'low' | 'normal' | 'high' | 'urgent';\n  createdAt: string;\n  waitingTime: number; // 等待时间（小时）\n}\n\n// 模拟待处理订单数据\nconst mockPendingOrders: PendingOrder[] = [\n  {\n    id: 1,\n    orderNo: 'ORD202401150003',\n    customerName: '王五',\n    customerPhone: '13800138003',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n    priority: 'normal',\n    createdAt: '2024-01-15 12:00:00',\n    waitingTime: 2,\n  },\n  {\n    id: 2,\n    orderNo: 'ORD202401150005',\n    customerName: '钱七',\n    customerPhone: '13800138005',\n    productName: '中国移动5G尊享套餐',\n    operator: '中国移动',\n    priority: 'high',\n    createdAt: '2024-01-15 14:30:00',\n    waitingTime: 0.5,\n  },\n  {\n    id: 3,\n    orderNo: 'ORD202401150006',\n    customerName: '孙八',\n    customerPhone: '13800138006',\n    productName: '中国电信天翼套餐',\n    operator: '中国电信',\n    priority: 'urgent',\n    createdAt: '2024-01-15 15:00:00',\n    waitingTime: 0.2,\n  },\n  {\n    id: 4,\n    orderNo: 'ORD202401150007',\n    customerName: '李九',\n    customerPhone: '13800138007',\n    productName: '中国广电智慧套餐',\n    operator: '中国广电',\n    priority: 'normal',\n    createdAt: '2024-01-15 16:00:00',\n    waitingTime: 1.5,\n  },\n];\n\nconst OrderPending: React.FC = () => {\n  const [orders, setOrders] = useState<PendingOrder[]>(mockPendingOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [processModalVisible, setProcessModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<PendingOrder | null>(null);\n  const [form] = Form.useForm();\n\n  // 优先级颜色映射\n  const getPriorityColor = (priority: string) => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red',\n    };\n    return colors[priority as keyof typeof colors] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = (priority: string) => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急',\n    };\n    return texts[priority as keyof typeof texts] || priority;\n  };\n\n\n\n  // 统计数据\n  const stats = {\n    total: orders.length,\n    urgent: orders.filter(order => order.priority === 'urgent').length,\n    high: orders.filter(order => order.priority === 'high').length,\n    processing: orders.filter(order => order.waitingTime > 1).length, // 等待超过1小时的订单\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<PendingOrder> = [\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      width: 80,\n      render: (priority: string) => (\n        <Tag color={getPriorityColor(priority)}>\n          {getPriorityText(priority)}\n        </Tag>\n      ),\n      sorter: (a, b) => {\n        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };\n        return priorityOrder[a.priority as keyof typeof priorityOrder] - \n               priorityOrder[b.priority as keyof typeof priorityOrder];\n      },\n    },\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n\n    {\n      title: '等待时间',\n      dataIndex: 'waitingTime',\n      key: 'waitingTime',\n      width: 100,\n      render: (time: number) => (\n        <Text style={{ color: time > 1 ? '#f5222d' : '#52c41a' }}>\n          {time < 1 ? `${Math.round(time * 60)}分钟` : `${time.toFixed(1)}小时`}\n        </Text>\n      ),\n      sorter: (a, b) => a.waitingTime - b.waitingTime,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<CheckOutlined />}\n            onClick={() => handleProcessOrder(record)}\n          >\n            处理\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            danger\n            size=\"small\"\n            icon={<CloseOutlined />}\n            onClick={() => handleCancelOrder(record.id)}\n          >\n            取消\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleProcessOrder = (order: PendingOrder) => {\n    setSelectedOrder(order);\n    setProcessModalVisible(true);\n  };\n\n  const handleViewOrder = (order: PendingOrder) => {\n    message.info(`查看订单 ${order.orderNo} 详情功能开发中...`);\n  };\n\n  const handleCancelOrder = (id: number) => {\n    Modal.confirm({\n      title: '确认取消订单',\n      icon: <ExclamationCircleOutlined />,\n      content: '确定要取消这个订单吗？此操作不可恢复。',\n      onOk: () => {\n        setOrders(orders.filter(order => order.id !== id));\n        message.success('订单已取消');\n      },\n    });\n  };\n\n  const handleBatchProcess = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要处理的订单');\n      return;\n    }\n    \n    Modal.confirm({\n      title: '批量处理订单',\n      content: `确定要批量处理选中的 ${selectedRowKeys.length} 个订单吗？`,\n      onOk: () => {\n        setOrders(orders.filter(order => !selectedRowKeys.includes(order.id)));\n        setSelectedRowKeys([]);\n        message.success('批量处理成功');\n      },\n    });\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockPendingOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n\n  const handleProcessSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      // 模拟处理订单\n      setOrders(orders.filter(order => order.id !== selectedOrder?.id));\n      setProcessModalVisible(false);\n      form.resetFields();\n      message.success('订单处理成功');\n    } catch (error) {\n      console.error('处理失败:', error);\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#faad14' }}>\n          待处理订单\n        </Title>\n        <Text type=\"secondary\">\n          处理需要人工审核的订单\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待处理总数\"\n              value={stats.total}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"紧急订单\"\n              value={stats.urgent}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"高优先级\"\n              value={stats.high}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"超时订单\"\n              value={stats.processing}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {orders.length} 个待处理订单\n            </Text>\n            {selectedRowKeys.length > 0 && (\n              <Text style={{ marginLeft: 16, color: '#1890ff' }}>\n                已选择 {selectedRowKeys.length} 个订单\n              </Text>\n            )}\n          </div>\n          <Space>\n            {selectedRowKeys.length > 0 && (\n              <Button type=\"primary\" onClick={handleBatchProcess}>\n                批量处理\n              </Button>\n            )}\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={orders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 处理订单弹窗 */}\n      <Modal\n        title=\"处理订单\"\n        open={processModalVisible}\n        onOk={handleProcessSubmit}\n        onCancel={() => {\n          setProcessModalVisible(false);\n          form.resetFields();\n        }}\n        okText=\"确认处理\"\n        cancelText=\"取消\"\n      >\n        {selectedOrder && (\n          <div>\n            <div style={{ marginBottom: 16, padding: 16, background: '#f5f5f5', borderRadius: 6 }}>\n              <Text strong>订单信息：</Text>\n              <div style={{ marginTop: 8 }}>\n                <div>订单号：{selectedOrder.orderNo}</div>\n                <div>客户：{selectedOrder.customerName} ({selectedOrder.customerPhone})</div>\n                <div>产品：{selectedOrder.productName}</div>\n                <div>金额：¥{selectedOrder.amount.toFixed(2)}</div>\n              </div>\n            </div>\n            \n            <Form form={form} layout=\"vertical\">\n              <Form.Item\n                name=\"action\"\n                label=\"处理动作\"\n                rules={[{ required: true, message: '请选择处理动作' }]}\n              >\n                <Select placeholder=\"请选择处理动作\">\n                  <Select.Option value=\"approve\">批准订单</Select.Option>\n                  <Select.Option value=\"reject\">拒绝订单</Select.Option>\n                  <Select.Option value=\"pending_info\">等待补充信息</Select.Option>\n                </Select>\n              </Form.Item>\n              \n              <Form.Item\n                name=\"remark\"\n                label=\"处理备注\"\n                rules={[{ required: true, message: '请输入处理备注' }]}\n              >\n                <TextArea \n                  rows={4} \n                  placeholder=\"请输入处理备注或拒绝原因\"\n                />\n              </Form.Item>\n            </Form>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderPending;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SACEC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,cAAc,EACdC,yBAAyB,QACpB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGlB,UAAU;AAClC,MAAM;EAAEmB;AAAS,CAAC,GAAGb,KAAK;AAc1B;AACA,MAAMc,iBAAiC,GAAG,CACxC;EACEC,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,qBAAqB;EAChCC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAiByB,iBAAiB,CAAC;EACvE,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC2C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAsB,IAAI,CAAC;EAC7E,MAAM,CAAC+C,IAAI,CAAC,GAAGtC,IAAI,CAACuC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,gBAAgB,GAAIjB,QAAgB,IAAK;IAC7C,MAAMkB,MAAM,GAAG;MACbC,GAAG,EAAE,SAAS;MACdC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAOJ,MAAM,CAAClB,QAAQ,CAAwB,IAAI,SAAS;EAC7D,CAAC;;EAED;EACA,MAAMuB,eAAe,GAAIvB,QAAgB,IAAK;IAC5C,MAAMwB,KAAK,GAAG;MACZL,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE;IACV,CAAC;IACD,OAAOE,KAAK,CAACxB,QAAQ,CAAuB,IAAIA,QAAQ;EAC1D,CAAC;;EAID;EACA,MAAMyB,KAAK,GAAG;IACZC,KAAK,EAAErB,MAAM,CAACsB,MAAM;IACpBL,MAAM,EAAEjB,MAAM,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC7B,QAAQ,KAAK,QAAQ,CAAC,CAAC2B,MAAM;IAClEN,IAAI,EAAEhB,MAAM,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC7B,QAAQ,KAAK,MAAM,CAAC,CAAC2B,MAAM;IAC9DG,UAAU,EAAEzB,MAAM,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC3B,WAAW,GAAG,CAAC,CAAC,CAACyB,MAAM,CAAE;EACpE,CAAC;;EAED;EACA,MAAMI,OAAkC,GAAG,CACzC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGpC,QAAgB,iBACvBX,OAAA,CAACf,GAAG;MAAC+D,KAAK,EAAEpB,gBAAgB,CAACjB,QAAQ,CAAE;MAAAsC,QAAA,EACpCf,eAAe,CAACvB,QAAQ;IAAC;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACN;IACDC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK;MAChB,MAAMC,aAAa,GAAG;QAAExB,MAAM,EAAE,CAAC;QAAED,IAAI,EAAE,CAAC;QAAED,MAAM,EAAE,CAAC;QAAED,GAAG,EAAE;MAAE,CAAC;MAC/D,OAAO2B,aAAa,CAACF,CAAC,CAAC5C,QAAQ,CAA+B,GACvD8C,aAAa,CAACD,CAAC,CAAC7C,QAAQ,CAA+B;IAChE;EACF,CAAC,EACD;IACEgC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnB1D,OAAA,CAACE,IAAI;MAACyD,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAZ,QAAA,EACpCS;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChB/D,OAAA;MAAAiD,QAAA,gBACEjD,OAAA;QAAK4D,KAAK,EAAE;UAAEI,UAAU,EAAE;QAAI,CAAE;QAAAf,QAAA,EAAEc,MAAM,CAACxD;MAAY;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5DrD,OAAA;QAAK4D,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEb,KAAK,EAAE;QAAO,CAAE;QAAAC,QAAA,EAC7Cc,MAAM,CAACvD;MAAa;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBoB,QAAQ,EAAE;EACZ,CAAC,EACD;IACEtB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnB1D,OAAA,CAACf,GAAG;MAAC+D,KAAK,EAAC,MAAM;MAAAC,QAAA,EAAES;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EAED;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGmB,IAAY,iBACnBlE,OAAA,CAACE,IAAI;MAAC0D,KAAK,EAAE;QAAEZ,KAAK,EAAEkB,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAjB,QAAA,EACtDiB,IAAI,GAAG,CAAC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,GAAGA,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC;IAAI;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CACP;IACDC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC1C,WAAW,GAAG2C,CAAC,CAAC3C;EACtC,CAAC,EACD;IACE8B,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGW,IAAY,iBACnB1D,OAAA;MAAK4D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAZ,QAAA,EAC9BS;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACe,CAAC,EAAEC,MAAM,kBAChB/D,OAAA,CAACjB,KAAK;MAACuF,IAAI,EAAC,OAAO;MAAArB,QAAA,gBACjBjD,OAAA,CAAClB,MAAM;QACLyF,IAAI,EAAC,SAAS;QACdD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAExE,OAAA,CAACN,aAAa;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBoB,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACX,MAAM,CAAE;QAAAd,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrD,OAAA,CAAClB,MAAM;QACLyF,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAExE,OAAA,CAACJ,WAAW;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBoB,OAAO,EAAEA,CAAA,KAAME,eAAe,CAACZ,MAAM,CAAE;QAAAd,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrD,OAAA,CAAClB,MAAM;QACL8F,MAAM;QACNN,IAAI,EAAC,OAAO;QACZE,IAAI,eAAExE,OAAA,CAACL,aAAa;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBoB,OAAO,EAAEA,CAAA,KAAMI,iBAAiB,CAACd,MAAM,CAAC1D,EAAE,CAAE;QAAA4C,QAAA,EAC7C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMqB,kBAAkB,GAAIlC,KAAmB,IAAK;IAClDf,gBAAgB,CAACe,KAAK,CAAC;IACvBjB,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMoD,eAAe,GAAInC,KAAmB,IAAK;IAC/CtD,OAAO,CAAC4F,IAAI,CAAC,QAAQtC,KAAK,CAAClC,OAAO,aAAa,CAAC;EAClD,CAAC;EAED,MAAMuE,iBAAiB,GAAIxE,EAAU,IAAK;IACxClB,KAAK,CAAC4F,OAAO,CAAC;MACZpC,KAAK,EAAE,QAAQ;MACf6B,IAAI,eAAExE,OAAA,CAACF,yBAAyB;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnC2B,OAAO,EAAE,qBAAqB;MAC9BC,IAAI,EAAEA,CAAA,KAAM;QACVhE,SAAS,CAACD,MAAM,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACnC,EAAE,KAAKA,EAAE,CAAC,CAAC;QAClDnB,OAAO,CAACgG,OAAO,CAAC,OAAO,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI/D,eAAe,CAACkB,MAAM,KAAK,CAAC,EAAE;MAChCpD,OAAO,CAACkG,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;IAEAjG,KAAK,CAAC4F,OAAO,CAAC;MACZpC,KAAK,EAAE,QAAQ;MACfqC,OAAO,EAAE,cAAc5D,eAAe,CAACkB,MAAM,QAAQ;MACrD2C,IAAI,EAAEA,CAAA,KAAM;QACVhE,SAAS,CAACD,MAAM,CAACuB,MAAM,CAACC,KAAK,IAAI,CAACpB,eAAe,CAACiE,QAAQ,CAAC7C,KAAK,CAACnC,EAAE,CAAC,CAAC,CAAC;QACtEgB,kBAAkB,CAAC,EAAE,CAAC;QACtBnC,OAAO,CAACgG,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1BnE,UAAU,CAAC,IAAI,CAAC;IAChBoE,UAAU,CAAC,MAAM;MACftE,SAAS,CAACb,iBAAiB,CAAC;MAC5Be,UAAU,CAAC,KAAK,CAAC;MACjBjC,OAAO,CAACgG,OAAO,CAAC,OAAO,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMM,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM/D,IAAI,CAACgE,cAAc,CAAC,CAAC;;MAE1C;MACAzE,SAAS,CAACD,MAAM,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACnC,EAAE,MAAKmB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEnB,EAAE,EAAC,CAAC;MACjEkB,sBAAsB,CAAC,KAAK,CAAC;MAC7BG,IAAI,CAACiE,WAAW,CAAC,CAAC;MAClBzG,OAAO,CAACgG,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,oBACE5F,OAAA;IAAAiD,QAAA,gBACEjD,OAAA;MAAK4D,KAAK,EAAE;QAAEkC,YAAY,EAAE;MAAO,CAAE;MAAA7C,QAAA,gBACnCjD,OAAA,CAACC,KAAK;QAAC8F,KAAK,EAAE,CAAE;QAACnC,KAAK,EAAE;UAAEoC,MAAM,EAAE,CAAC;UAAEhD,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRrD,OAAA,CAACE,IAAI;QAACqE,IAAI,EAAC,WAAW;QAAAtB,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNrD,OAAA,CAACT,GAAG;MAAC0G,MAAM,EAAE,EAAG;MAACrC,KAAK,EAAE;QAAEkC,YAAY,EAAE;MAAO,CAAE;MAAA7C,QAAA,gBAC/CjD,OAAA,CAACR,GAAG;QAAC0G,IAAI,EAAE,CAAE;QAAAjD,QAAA,eACXjD,OAAA,CAACpB,IAAI;UAAAqE,QAAA,eACHjD,OAAA,CAACP,SAAS;YACRkD,KAAK,EAAC,gCAAO;YACbwD,KAAK,EAAE/D,KAAK,CAACC,KAAM;YACnB+D,UAAU,EAAE;cAAEpD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrD,OAAA,CAACR,GAAG;QAAC0G,IAAI,EAAE,CAAE;QAAAjD,QAAA,eACXjD,OAAA,CAACpB,IAAI;UAAAqE,QAAA,eACHjD,OAAA,CAACP,SAAS;YACRkD,KAAK,EAAC,0BAAM;YACZwD,KAAK,EAAE/D,KAAK,CAACH,MAAO;YACpBmE,UAAU,EAAE;cAAEpD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrD,OAAA,CAACR,GAAG;QAAC0G,IAAI,EAAE,CAAE;QAAAjD,QAAA,eACXjD,OAAA,CAACpB,IAAI;UAAAqE,QAAA,eACHjD,OAAA,CAACP,SAAS;YACRkD,KAAK,EAAC,0BAAM;YACZwD,KAAK,EAAE/D,KAAK,CAACJ,IAAK;YAClBoE,UAAU,EAAE;cAAEpD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrD,OAAA,CAACR,GAAG;QAAC0G,IAAI,EAAE,CAAE;QAAAjD,QAAA,eACXjD,OAAA,CAACpB,IAAI;UAAAqE,QAAA,eACHjD,OAAA,CAACP,SAAS;YACRkD,KAAK,EAAC,0BAAM;YACZwD,KAAK,EAAE/D,KAAK,CAACK,UAAW;YACxB2D,UAAU,EAAE;cAAEpD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrD,OAAA,CAACpB,IAAI;MAAAqE,QAAA,gBAEHjD,OAAA;QAAK4D,KAAK,EAAE;UACVyC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBT,YAAY,EAAE;QAChB,CAAE;QAAA7C,QAAA,gBACAjD,OAAA;UAAAiD,QAAA,gBACEjD,OAAA,CAACE,IAAI;YAACsG,MAAM;YAAAvD,QAAA,GAAC,SACT,EAACjC,MAAM,CAACsB,MAAM,EAAC,uCACnB;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNjC,eAAe,CAACkB,MAAM,GAAG,CAAC,iBACzBtC,OAAA,CAACE,IAAI;YAAC0D,KAAK,EAAE;cAAE6C,UAAU,EAAE,EAAE;cAAEzD,KAAK,EAAE;YAAU,CAAE;YAAAC,QAAA,GAAC,qBAC7C,EAAC7B,eAAe,CAACkB,MAAM,EAAC,qBAC9B;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNrD,OAAA,CAACjB,KAAK;UAAAkE,QAAA,GACH7B,eAAe,CAACkB,MAAM,GAAG,CAAC,iBACzBtC,OAAA,CAAClB,MAAM;YAACyF,IAAI,EAAC,SAAS;YAACE,OAAO,EAAEU,kBAAmB;YAAAlC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDrD,OAAA,CAAClB,MAAM;YAAC2F,OAAO,EAAEa,aAAc;YAACd,IAAI,eAAExE,OAAA,CAACH,cAAc;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrD,OAAA,CAACnB,KAAK;QACJ6D,OAAO,EAAEA,OAAQ;QACjBgE,UAAU,EAAE1F,MAAO;QACnB2F,MAAM,EAAC,IAAI;QACXzF,OAAO,EAAEA,OAAQ;QACjB0F,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC5E,KAAK,EAAE6E,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAAS7E,KAAK;QAC3C,CAAE;QACF8E,YAAY,EAAE;UACZ/F,eAAe;UACfgG,QAAQ,EAAE/F;QACZ;MAAE;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPrD,OAAA,CAACb,KAAK;MACJwD,KAAK,EAAC,0BAAM;MACZ0E,IAAI,EAAE/F,mBAAoB;MAC1B2D,IAAI,EAAEO,mBAAoB;MAC1B8B,QAAQ,EAAEA,CAAA,KAAM;QACd/F,sBAAsB,CAAC,KAAK,CAAC;QAC7BG,IAAI,CAACiE,WAAW,CAAC,CAAC;MACpB,CAAE;MACF4B,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MAAAvE,QAAA,EAEdzB,aAAa,iBACZxB,OAAA;QAAAiD,QAAA,gBACEjD,OAAA;UAAK4D,KAAK,EAAE;YAAEkC,YAAY,EAAE,EAAE;YAAE2B,OAAO,EAAE,EAAE;YAAEC,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA1E,QAAA,gBACpFjD,OAAA,CAACE,IAAI;YAACsG,MAAM;YAAAvD,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBrD,OAAA;YAAK4D,KAAK,EAAE;cAAEgE,SAAS,EAAE;YAAE,CAAE;YAAA3E,QAAA,gBAC3BjD,OAAA;cAAAiD,QAAA,GAAK,0BAAI,EAACzB,aAAa,CAAClB,OAAO;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtCrD,OAAA;cAAAiD,QAAA,GAAK,oBAAG,EAACzB,aAAa,CAACjB,YAAY,EAAC,IAAE,EAACiB,aAAa,CAAChB,aAAa,EAAC,GAAC;YAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1ErD,OAAA;cAAAiD,QAAA,GAAK,oBAAG,EAACzB,aAAa,CAACf,WAAW;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCrD,OAAA;cAAAiD,QAAA,GAAK,wBAAI,EAACzB,aAAa,CAACqG,MAAM,CAACxD,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrD,OAAA,CAACZ,IAAI;UAACsC,IAAI,EAAEA,IAAK;UAACoG,MAAM,EAAC,UAAU;UAAA7E,QAAA,gBACjCjD,OAAA,CAACZ,IAAI,CAAC2I,IAAI;YACRC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEjJ,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA+D,QAAA,eAEhDjD,OAAA,CAACX,MAAM;cAAC+I,WAAW,EAAC,4CAAS;cAAAnF,QAAA,gBAC3BjD,OAAA,CAACX,MAAM,CAACgJ,MAAM;gBAAClC,KAAK,EAAC,SAAS;gBAAAlD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eACnDrD,OAAA,CAACX,MAAM,CAACgJ,MAAM;gBAAClC,KAAK,EAAC,QAAQ;gBAAAlD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAClDrD,OAAA,CAACX,MAAM,CAACgJ,MAAM;gBAAClC,KAAK,EAAC,cAAc;gBAAAlD,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZrD,OAAA,CAACZ,IAAI,CAAC2I,IAAI;YACRC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEjJ,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA+D,QAAA,eAEhDjD,OAAA,CAACG,QAAQ;cACPmI,IAAI,EAAE,CAAE;cACRF,WAAW,EAAC;YAAc;cAAAlF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtC,EAAA,CApXID,YAAsB;EAAA,QAMX1B,IAAI,CAACuC,OAAO;AAAA;AAAA4G,EAAA,GANvBzH,YAAsB;AAsX5B,eAAeA,YAAY;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
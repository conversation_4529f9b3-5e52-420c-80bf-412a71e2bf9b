{"ast": null, "code": "import { genStyleHooks } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedAffixStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'fixed',\n      zIndex: token.zIndexPopup\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexBase + 10\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Affix', genSharedAffixStyle, prepareComponentToken);", "map": {"version": 3, "names": ["genStyleHooks", "genSharedAffixStyle", "token", "componentCls", "position", "zIndex", "zIndexPopup", "prepareComponentToken", "zIndexBase"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/affix/style/index.js"], "sourcesContent": ["import { genStyleHooks } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedAffixStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'fixed',\n      zIndex: token.zIndexPopup\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexBase + 10\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Affix', genSharedAffixStyle, prepareComponentToken);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,sBAAsB;AACpD;AACA,MAAMC,mBAAmB,GAAGC,KAAK,IAAI;EACnC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACdC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAEH,KAAK,CAACI;IAChB;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGL,KAAK,KAAK;EAC7CI,WAAW,EAAEJ,KAAK,CAACM,UAAU,GAAG;AAClC,CAAC,CAAC;AACF;AACA,eAAeR,aAAa,CAAC,OAAO,EAAEC,mBAAmB,EAAEM,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import Collapse from \"./Collapse\";\nexport default Collapse;\n\n/**\n * @deprecated use `items` instead, will be removed in `v4.0.0`\n */\nvar Panel = Collapse.Panel;\nexport { Panel };", "map": {"version": 3, "names": ["Collapse", "Panel"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-collapse/es/index.js"], "sourcesContent": ["import Collapse from \"./Collapse\";\nexport default Collapse;\n\n/**\n * @deprecated use `items` instead, will be removed in `v4.0.0`\n */\nvar Panel = Collapse.Panel;\nexport { Panel };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,YAAY;AACjC,eAAeA,QAAQ;;AAEvB;AACA;AACA;AACA,IAAIC,KAAK,GAAGD,QAAQ,CAACC,KAAK;AAC1B,SAASA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
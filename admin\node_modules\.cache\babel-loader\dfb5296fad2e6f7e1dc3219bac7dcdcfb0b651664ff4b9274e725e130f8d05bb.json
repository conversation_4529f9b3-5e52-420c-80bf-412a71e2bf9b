{"ast": null, "code": "import * as React from 'react';\nexport default function useRefs() {\n  var nodeRef = React.useRef({});\n  function getRef(index) {\n    return nodeRef.current[index];\n  }\n  function setRef(index) {\n    return function (node) {\n      nodeRef.current[index] = node;\n    };\n  }\n  return [getRef, setRef];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
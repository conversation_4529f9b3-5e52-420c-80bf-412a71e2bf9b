{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PlaySquareOutlinedSvg from \"@ant-design/icons-svg/es/asn/PlaySquareOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PlaySquareOutlined = function PlaySquareOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PlaySquareOutlinedSvg\n  }));\n};\n\n/**![play-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ0Mi4zIDY3Ny42bDE5OS40LTE1Ni43YTExLjMgMTEuMyAwIDAwMC0xNy43TDQ0Mi4zIDM0Ni40Yy03LjQtNS44LTE4LjMtLjYtMTguMyA4Ljh2MzEzLjVjMCA5LjQgMTAuOSAxNC43IDE4LjMgOC45eiIgLz48cGF0aCBkPSJNODgwIDExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgNzI4SDE4NFYxODRoNjU2djY1NnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PlaySquareOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PlaySquareOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
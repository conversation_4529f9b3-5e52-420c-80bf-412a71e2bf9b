{"ast": null, "code": "import { resetComponent } from '../../style';\nimport { genStyleHooks } from '../../theme/internal';\nconst genRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [\"&-rtl\".concat(componentCls, \"-horizontal\")]: {\n      [\"> \".concat(componentCls, \"-bar\")]: {\n        [\"\".concat(componentCls, \"-bar-collapse-previous\")]: {\n          insetInlineEnd: 0,\n          insetInlineStart: 'unset'\n        },\n        [\"\".concat(componentCls, \"-bar-collapse-next\")]: {\n          insetInlineEnd: 'unset',\n          insetInlineStart: 0\n        }\n      }\n    },\n    [\"&-rtl\".concat(componentCls, \"-vertical\")]: {\n      [\"> \".concat(componentCls, \"-bar\")]: {\n        [\"\".concat(componentCls, \"-bar-collapse-previous\")]: {\n          insetInlineEnd: '50%',\n          insetInlineStart: 'unset'\n        },\n        [\"\".concat(componentCls, \"-bar-collapse-next\")]: {\n          insetInlineEnd: '50%',\n          insetInlineStart: 'unset'\n        }\n      }\n    }\n  };\n};\nconst centerStyle = {\n  position: 'absolute',\n  top: '50%',\n  left: {\n    _skip_check_: true,\n    value: '50%'\n  },\n  transform: 'translate(-50%, -50%)'\n};\nconst genSplitterStyle = token => {\n  const {\n    componentCls,\n    colorFill,\n    splitBarDraggableSize,\n    splitBarSize,\n    splitTriggerSize,\n    controlItemBgHover,\n    controlItemBgActive,\n    controlItemBgActiveHover,\n    prefixCls\n  } = token;\n  const splitBarCls = \"\".concat(componentCls, \"-bar\");\n  const splitMaskCls = \"\".concat(componentCls, \"-mask\");\n  const splitPanelCls = \"\".concat(componentCls, \"-panel\");\n  const halfTriggerSize = token.calc(splitTriggerSize).div(2).equal();\n  const splitterBarPreviewOffsetVar = \"\".concat(prefixCls, \"-bar-preview-offset\");\n  const splitterBarPreviewStyle = {\n    position: 'absolute',\n    background: token.colorPrimary,\n    opacity: 0.2,\n    pointerEvents: 'none',\n    transition: 'none',\n    zIndex: 1,\n    display: 'none'\n  };\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      width: '100%',\n      height: '100%',\n      alignItems: 'stretch',\n      // ======================== SplitBar ========================\n      // Use `>` to avoid conflict with mix layout\n      [\"> \".concat(splitBarCls)]: {\n        flex: 'none',\n        position: 'relative',\n        userSelect: 'none',\n        // ======================= Dragger =======================\n        [\"\".concat(splitBarCls, \"-dragger\")]: Object.assign(Object.assign({}, centerStyle), {\n          zIndex: 1,\n          // Hover background\n          '&::before': Object.assign({\n            content: '\"\"',\n            background: controlItemBgHover\n          }, centerStyle),\n          // Spinner\n          '&::after': Object.assign({\n            content: '\"\"',\n            background: colorFill\n          }, centerStyle),\n          // Hover\n          [\"&:hover:not(\".concat(splitBarCls, \"-dragger-active)\")]: {\n            '&::before': {\n              background: controlItemBgActive\n            }\n          },\n          // Active\n          '&-active': {\n            zIndex: 2,\n            '&::before': {\n              background: controlItemBgActiveHover\n            }\n          },\n          // Disabled, not use `pointer-events: none` since still need trigger collapse\n          [\"&-disabled\".concat(splitBarCls, \"-dragger\")]: {\n            zIndex: 0,\n            '&, &:hover, &-active': {\n              cursor: 'default',\n              '&::before': {\n                background: controlItemBgHover\n              }\n            },\n            '&::after': {\n              display: 'none'\n            }\n          }\n        }),\n        // ======================= Collapse =======================\n        [\"\".concat(splitBarCls, \"-collapse-bar\")]: Object.assign(Object.assign({}, centerStyle), {\n          zIndex: token.zIndexPopupBase,\n          background: controlItemBgHover,\n          fontSize: token.fontSizeSM,\n          borderRadius: token.borderRadiusXS,\n          color: token.colorText,\n          cursor: 'pointer',\n          opacity: 0,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          '@media(hover:none)': {\n            opacity: 1\n          },\n          // Hover\n          '&:hover': {\n            background: controlItemBgActive\n          },\n          // Active\n          '&:active': {\n            background: controlItemBgActiveHover\n          }\n        }),\n        // ======================== Status ========================\n        // Hover\n        '&:hover, &:active': {\n          [\"\".concat(splitBarCls, \"-collapse-bar\")]: {\n            opacity: 1\n          }\n        }\n      },\n      // =========================== Mask =========================\n      // Util dom for handle cursor\n      [splitMaskCls]: {\n        position: 'fixed',\n        zIndex: token.zIndexPopupBase,\n        inset: 0,\n        '&-horizontal': {\n          cursor: 'col-resize'\n        },\n        '&-vertical': {\n          cursor: 'row-resize'\n        }\n      },\n      // ==========================================================\n      // ==                        Layout                        ==\n      // ==========================================================\n      '&-horizontal': {\n        flexDirection: 'row',\n        [\"> \".concat(splitBarCls)]: {\n          width: 0,\n          // ======================= Preview =======================\n          [\"\".concat(splitBarCls, \"-preview\")]: Object.assign(Object.assign({\n            height: '100%',\n            width: splitBarSize\n          }, splitterBarPreviewStyle), {\n            [\"&\".concat(splitBarCls, \"-preview-active\")]: {\n              display: 'block',\n              transform: \"translateX(var(--\".concat(splitterBarPreviewOffsetVar, \"))\")\n            }\n          }),\n          // ======================= Dragger =======================\n          [\"\".concat(splitBarCls, \"-dragger\")]: {\n            cursor: 'col-resize',\n            height: '100%',\n            width: splitTriggerSize,\n            '&::before': {\n              height: '100%',\n              width: splitBarSize\n            },\n            '&::after': {\n              height: splitBarDraggableSize,\n              width: splitBarSize\n            }\n          },\n          // ======================= Collapse =======================\n          [\"\".concat(splitBarCls, \"-collapse-bar\")]: {\n            width: token.fontSizeSM,\n            height: token.controlHeightSM,\n            '&-start': {\n              left: {\n                _skip_check_: true,\n                value: 'auto'\n              },\n              right: {\n                _skip_check_: true,\n                value: halfTriggerSize\n              },\n              transform: 'translateY(-50%)'\n            },\n            '&-end': {\n              left: {\n                _skip_check_: true,\n                value: halfTriggerSize\n              },\n              right: {\n                _skip_check_: true,\n                value: 'auto'\n              },\n              transform: 'translateY(-50%)'\n            }\n          }\n        }\n      },\n      '&-vertical': {\n        flexDirection: 'column',\n        [\"> \".concat(splitBarCls)]: {\n          height: 0,\n          // ======================= Preview =======================\n          [\"\".concat(splitBarCls, \"-preview\")]: Object.assign(Object.assign({\n            height: splitBarSize,\n            width: '100%'\n          }, splitterBarPreviewStyle), {\n            [\"&\".concat(splitBarCls, \"-preview-active\")]: {\n              display: 'block',\n              transform: \"translateY(var(--\".concat(splitterBarPreviewOffsetVar, \"))\")\n            }\n          }),\n          // ======================= Dragger =======================\n          [\"\".concat(splitBarCls, \"-dragger\")]: {\n            cursor: 'row-resize',\n            width: '100%',\n            height: splitTriggerSize,\n            '&::before': {\n              width: '100%',\n              height: splitBarSize\n            },\n            '&::after': {\n              width: splitBarDraggableSize,\n              height: splitBarSize\n            }\n          },\n          // ======================= Collapse =======================\n          [\"\".concat(splitBarCls, \"-collapse-bar\")]: {\n            height: token.fontSizeSM,\n            width: token.controlHeightSM,\n            '&-start': {\n              top: 'auto',\n              bottom: halfTriggerSize,\n              transform: 'translateX(-50%)'\n            },\n            '&-end': {\n              top: halfTriggerSize,\n              bottom: 'auto',\n              transform: 'translateX(-50%)'\n            }\n          }\n        }\n      },\n      // ========================= Panels =========================\n      [splitPanelCls]: {\n        overflow: 'auto',\n        padding: '0 1px',\n        scrollbarWidth: 'thin',\n        boxSizing: 'border-box',\n        '&-hidden': {\n          padding: 0,\n          overflow: 'hidden'\n        },\n        [\"&:has(\".concat(componentCls, \":only-child)\")]: {\n          overflow: 'hidden'\n        }\n      }\n    }), genRtlStyle(token))\n  };\n};\nexport const prepareComponentToken = token => {\n  var _a;\n  const splitBarSize = token.splitBarSize || 2;\n  const splitTriggerSize = token.splitTriggerSize || 6;\n  // https://github.com/ant-design/ant-design/pull/51223\n  const resizeSpinnerSize = token.resizeSpinnerSize || 20;\n  const splitBarDraggableSize = (_a = token.splitBarDraggableSize) !== null && _a !== void 0 ? _a : resizeSpinnerSize;\n  return {\n    splitBarSize,\n    splitTriggerSize,\n    splitBarDraggableSize,\n    resizeSpinnerSize\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Splitter', token => [genSplitterStyle(token)], prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar FORMAT_KEYS = ['YYYY', 'MM', 'DD', 'HH', 'mm', 'ss', 'SSS'];\n// Use Chinese character to avoid conflict with the mask format\nvar REPLACE_KEY = '顧';\nvar MaskFormat = /*#__PURE__*/function () {\n  function MaskFormat(format) {\n    _classCallCheck(this, MaskFormat);\n    _defineProperty(this, \"format\", void 0);\n    _defineProperty(this, \"maskFormat\", void 0);\n    _defineProperty(this, \"cells\", void 0);\n    _defineProperty(this, \"maskCells\", void 0);\n    this.format = format;\n\n    // Generate mask format\n    var replaceKeys = FORMAT_KEYS.map(function (key) {\n      return \"(\".concat(key, \")\");\n    }).join('|');\n    var replaceReg = new RegExp(replaceKeys, 'g');\n    this.maskFormat = format.replace(replaceReg,\n    // Use Chinese character to avoid user use it in format\n    function (key) {\n      return REPLACE_KEY.repeat(key.length);\n    });\n\n    // Generate cells\n    var cellReg = new RegExp(\"(\".concat(FORMAT_KEYS.join('|'), \")\"));\n    var strCells = (format.split(cellReg) || []).filter(function (str) {\n      return str;\n    });\n    var offset = 0;\n    this.cells = strCells.map(function (text) {\n      var mask = FORMAT_KEYS.includes(text);\n      var start = offset;\n      var end = offset + text.length;\n      offset = end;\n      return {\n        text: text,\n        mask: mask,\n        start: start,\n        end: end\n      };\n    });\n\n    // Mask cells\n    this.maskCells = this.cells.filter(function (cell) {\n      return cell.mask;\n    });\n  }\n  _createClass(MaskFormat, [{\n    key: \"getSelection\",\n    value: function getSelection(maskCellIndex) {\n      var _ref = this.maskCells[maskCellIndex] || {},\n        start = _ref.start,\n        end = _ref.end;\n      return [start || 0, end || 0];\n    }\n\n    /** Check given text match format */\n  }, {\n    key: \"match\",\n    value: function match(text) {\n      for (var i = 0; i < this.maskFormat.length; i += 1) {\n        var maskChar = this.maskFormat[i];\n        var textChar = text[i];\n        if (!textChar || maskChar !== REPLACE_KEY && maskChar !== textChar) {\n          return false;\n        }\n      }\n      return true;\n    }\n\n    /** Get mask cell count */\n  }, {\n    key: \"size\",\n    value: function size() {\n      return this.maskCells.length;\n    }\n  }, {\n    key: \"getMaskCellIndex\",\n    value: function getMaskCellIndex(anchorIndex) {\n      var closetDist = Number.MAX_SAFE_INTEGER;\n      var closetIndex = 0;\n      for (var i = 0; i < this.maskCells.length; i += 1) {\n        var _this$maskCells$i = this.maskCells[i],\n          start = _this$maskCells$i.start,\n          end = _this$maskCells$i.end;\n        if (anchorIndex >= start && anchorIndex <= end) {\n          return i;\n        }\n        var dist = Math.min(Math.abs(anchorIndex - start), Math.abs(anchorIndex - end));\n        if (dist < closetDist) {\n          closetDist = dist;\n          closetIndex = i;\n        }\n      }\n      return closetIndex;\n    }\n  }]);\n  return MaskFormat;\n}();\nexport { MaskFormat as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
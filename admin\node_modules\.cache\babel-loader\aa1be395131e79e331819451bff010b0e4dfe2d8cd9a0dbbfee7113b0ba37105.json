{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { formatValue, isInRange, isSameYear } from \"../../utils/dateUtil\";\nimport { PanelContext, useInfo } from \"../context\";\nimport PanelBody from \"../PanelBody\";\nimport PanelHeader from \"../PanelHeader\";\nexport default function YearPanel(props) {\n  var prefixCls = props.prefixCls,\n    locale = props.locale,\n    generateConfig = props.generateConfig,\n    pickerValue = props.pickerValue,\n    disabledDate = props.disabledDate,\n    onPickerValueChange = props.onPickerValueChange,\n    onModeChange = props.onModeChange;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-year-panel\");\n\n  // ========================== Base ==========================\n  var _useInfo = useInfo(props, 'year'),\n    _useInfo2 = _slicedToArray(_useInfo, 1),\n    info = _useInfo2[0];\n  var getStartYear = function getStartYear(date) {\n    var startYear = Math.floor(generateConfig.getYear(date) / 10) * 10;\n    return generateConfig.setYear(date, startYear);\n  };\n  var getEndYear = function getEndYear(date) {\n    var startYear = getStartYear(date);\n    return generateConfig.addYear(startYear, 9);\n  };\n  var startYearDate = getStartYear(pickerValue);\n  var endYearDate = getEndYear(pickerValue);\n  var baseDate = generateConfig.addYear(startYearDate, -1);\n\n  // ========================= Cells ==========================\n  var getCellDate = function getCellDate(date, offset) {\n    return generateConfig.addYear(date, offset);\n  };\n  var getCellText = function getCellText(date) {\n    return formatValue(date, {\n      locale: locale,\n      format: locale.cellYearFormat,\n      generateConfig: generateConfig\n    });\n  };\n  var getCellClassName = function getCellClassName(date) {\n    return _defineProperty({}, \"\".concat(prefixCls, \"-cell-in-view\"), isSameYear(generateConfig, date, startYearDate) || isSameYear(generateConfig, date, endYearDate) || isInRange(generateConfig, startYearDate, endYearDate, date));\n  };\n\n  // ======================== Disabled ========================\n  var mergedDisabledDate = disabledDate ? function (currentDate, disabledInfo) {\n    // Start\n    var startMonth = generateConfig.setMonth(currentDate, 0);\n    var startDate = generateConfig.setDate(startMonth, 1);\n\n    // End\n    var endMonth = generateConfig.addYear(startDate, 1);\n    var endDate = generateConfig.addDate(endMonth, -1);\n    return disabledDate(startDate, disabledInfo) && disabledDate(endDate, disabledInfo);\n  } : null;\n\n  // ========================= Header =========================\n  var yearNode = /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    key: \"decade\",\n    \"aria-label\": locale.decadeSelect,\n    onClick: function onClick() {\n      onModeChange('decade');\n    },\n    tabIndex: -1,\n    className: \"\".concat(prefixCls, \"-decade-btn\")\n  }, formatValue(startYearDate, {\n    locale: locale,\n    format: locale.yearFormat,\n    generateConfig: generateConfig\n  }), \"-\", formatValue(endYearDate, {\n    locale: locale,\n    format: locale.yearFormat,\n    generateConfig: generateConfig\n  }));\n\n  // ========================= Render =========================\n  return /*#__PURE__*/React.createElement(PanelContext.Provider, {\n    value: info\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(PanelHeader, {\n    superOffset: function superOffset(distance) {\n      return generateConfig.addYear(pickerValue, distance * 10);\n    },\n    onChange: onPickerValueChange\n    // Limitation\n    ,\n\n    getStart: getStartYear,\n    getEnd: getEndYear\n  }, yearNode), /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    disabledDate: mergedDisabledDate,\n    titleFormat: locale.fieldYearFormat,\n    colNum: 3,\n    rowNum: 4,\n    baseDate: baseDate\n    // Body\n    ,\n\n    getCellDate: getCellDate,\n    getCellText: getCellText,\n    getCellClassName: getCellClassName\n  }))));\n}", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "React", "formatValue", "isInRange", "isSameYear", "PanelContext", "useInfo", "PanelBody", "PanelHeader", "YearPanel", "props", "prefixCls", "locale", "generateConfig", "picker<PERSON><PERSON><PERSON>", "disabledDate", "onPickerValueChange", "onModeChange", "panelPrefixCls", "concat", "_useInfo", "_useInfo2", "info", "getStartYear", "date", "startYear", "Math", "floor", "getYear", "setYear", "getEndYear", "addYear", "startYearDate", "endYearDate", "baseDate", "getCellDate", "offset", "getCellText", "format", "cellYearFormat", "getCellClassName", "mergedDisabledDate", "currentDate", "disabledInfo", "startMonth", "setMonth", "startDate", "setDate", "endMonth", "endDate", "addDate", "yearNode", "createElement", "type", "key", "decadeSelect", "onClick", "tabIndex", "className", "yearFormat", "Provider", "value", "superOffset", "distance", "onChange", "getStart", "getEnd", "titleFormat", "fieldYearFormat", "colNum", "row<PERSON>um"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-picker/es/PickerPanel/YearPanel/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { formatValue, isInRange, isSameYear } from \"../../utils/dateUtil\";\nimport { PanelContext, useInfo } from \"../context\";\nimport PanelBody from \"../PanelBody\";\nimport PanelHeader from \"../PanelHeader\";\nexport default function YearPanel(props) {\n  var prefixCls = props.prefixCls,\n    locale = props.locale,\n    generateConfig = props.generateConfig,\n    pickerValue = props.pickerValue,\n    disabledDate = props.disabledDate,\n    onPickerValueChange = props.onPickerValueChange,\n    onModeChange = props.onModeChange;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-year-panel\");\n\n  // ========================== Base ==========================\n  var _useInfo = useInfo(props, 'year'),\n    _useInfo2 = _slicedToArray(_useInfo, 1),\n    info = _useInfo2[0];\n  var getStartYear = function getStartYear(date) {\n    var startYear = Math.floor(generateConfig.getYear(date) / 10) * 10;\n    return generateConfig.setYear(date, startYear);\n  };\n  var getEndYear = function getEndYear(date) {\n    var startYear = getStartYear(date);\n    return generateConfig.addYear(startYear, 9);\n  };\n  var startYearDate = getStartYear(pickerValue);\n  var endYearDate = getEndYear(pickerValue);\n  var baseDate = generateConfig.addYear(startYearDate, -1);\n\n  // ========================= Cells ==========================\n  var getCellDate = function getCellDate(date, offset) {\n    return generateConfig.addYear(date, offset);\n  };\n  var getCellText = function getCellText(date) {\n    return formatValue(date, {\n      locale: locale,\n      format: locale.cellYearFormat,\n      generateConfig: generateConfig\n    });\n  };\n  var getCellClassName = function getCellClassName(date) {\n    return _defineProperty({}, \"\".concat(prefixCls, \"-cell-in-view\"), isSameYear(generateConfig, date, startYearDate) || isSameYear(generateConfig, date, endYearDate) || isInRange(generateConfig, startYearDate, endYearDate, date));\n  };\n\n  // ======================== Disabled ========================\n  var mergedDisabledDate = disabledDate ? function (currentDate, disabledInfo) {\n    // Start\n    var startMonth = generateConfig.setMonth(currentDate, 0);\n    var startDate = generateConfig.setDate(startMonth, 1);\n\n    // End\n    var endMonth = generateConfig.addYear(startDate, 1);\n    var endDate = generateConfig.addDate(endMonth, -1);\n    return disabledDate(startDate, disabledInfo) && disabledDate(endDate, disabledInfo);\n  } : null;\n\n  // ========================= Header =========================\n  var yearNode = /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    key: \"decade\",\n    \"aria-label\": locale.decadeSelect,\n    onClick: function onClick() {\n      onModeChange('decade');\n    },\n    tabIndex: -1,\n    className: \"\".concat(prefixCls, \"-decade-btn\")\n  }, formatValue(startYearDate, {\n    locale: locale,\n    format: locale.yearFormat,\n    generateConfig: generateConfig\n  }), \"-\", formatValue(endYearDate, {\n    locale: locale,\n    format: locale.yearFormat,\n    generateConfig: generateConfig\n  }));\n\n  // ========================= Render =========================\n  return /*#__PURE__*/React.createElement(PanelContext.Provider, {\n    value: info\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(PanelHeader, {\n    superOffset: function superOffset(distance) {\n      return generateConfig.addYear(pickerValue, distance * 10);\n    },\n    onChange: onPickerValueChange\n    // Limitation\n    ,\n    getStart: getStartYear,\n    getEnd: getEndYear\n  }, yearNode), /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    disabledDate: mergedDisabledDate,\n    titleFormat: locale.fieldYearFormat,\n    colNum: 3,\n    rowNum: 4,\n    baseDate: baseDate\n    // Body\n    ,\n    getCellDate: getCellDate,\n    getCellText: getCellText,\n    getCellClassName: getCellClassName\n  }))));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,sBAAsB;AACzE,SAASC,YAAY,EAAEC,OAAO,QAAQ,YAAY;AAClD,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,eAAe,SAASC,SAASA,CAACC,KAAK,EAAE;EACvC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,cAAc,GAAGH,KAAK,CAACG,cAAc;IACrCC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,mBAAmB,GAAGN,KAAK,CAACM,mBAAmB;IAC/CC,YAAY,GAAGP,KAAK,CAACO,YAAY;EACnC,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,aAAa,CAAC;;EAExD;EACA,IAAIS,QAAQ,GAAGd,OAAO,CAACI,KAAK,EAAE,MAAM,CAAC;IACnCW,SAAS,GAAGrB,cAAc,CAACoB,QAAQ,EAAE,CAAC,CAAC;IACvCE,IAAI,GAAGD,SAAS,CAAC,CAAC,CAAC;EACrB,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;IAC7C,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACd,cAAc,CAACe,OAAO,CAACJ,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;IAClE,OAAOX,cAAc,CAACgB,OAAO,CAACL,IAAI,EAAEC,SAAS,CAAC;EAChD,CAAC;EACD,IAAIK,UAAU,GAAG,SAASA,UAAUA,CAACN,IAAI,EAAE;IACzC,IAAIC,SAAS,GAAGF,YAAY,CAACC,IAAI,CAAC;IAClC,OAAOX,cAAc,CAACkB,OAAO,CAACN,SAAS,EAAE,CAAC,CAAC;EAC7C,CAAC;EACD,IAAIO,aAAa,GAAGT,YAAY,CAACT,WAAW,CAAC;EAC7C,IAAImB,WAAW,GAAGH,UAAU,CAAChB,WAAW,CAAC;EACzC,IAAIoB,QAAQ,GAAGrB,cAAc,CAACkB,OAAO,CAACC,aAAa,EAAE,CAAC,CAAC,CAAC;;EAExD;EACA,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAACX,IAAI,EAAEY,MAAM,EAAE;IACnD,OAAOvB,cAAc,CAACkB,OAAO,CAACP,IAAI,EAAEY,MAAM,CAAC;EAC7C,CAAC;EACD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACb,IAAI,EAAE;IAC3C,OAAOtB,WAAW,CAACsB,IAAI,EAAE;MACvBZ,MAAM,EAAEA,MAAM;MACd0B,MAAM,EAAE1B,MAAM,CAAC2B,cAAc;MAC7B1B,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC;EACD,IAAI2B,gBAAgB,GAAG,SAASA,gBAAgBA,CAAChB,IAAI,EAAE;IACrD,OAAOzB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoB,MAAM,CAACR,SAAS,EAAE,eAAe,CAAC,EAAEP,UAAU,CAACS,cAAc,EAAEW,IAAI,EAAEQ,aAAa,CAAC,IAAI5B,UAAU,CAACS,cAAc,EAAEW,IAAI,EAAES,WAAW,CAAC,IAAI9B,SAAS,CAACU,cAAc,EAAEmB,aAAa,EAAEC,WAAW,EAAET,IAAI,CAAC,CAAC;EACpO,CAAC;;EAED;EACA,IAAIiB,kBAAkB,GAAG1B,YAAY,GAAG,UAAU2B,WAAW,EAAEC,YAAY,EAAE;IAC3E;IACA,IAAIC,UAAU,GAAG/B,cAAc,CAACgC,QAAQ,CAACH,WAAW,EAAE,CAAC,CAAC;IACxD,IAAII,SAAS,GAAGjC,cAAc,CAACkC,OAAO,CAACH,UAAU,EAAE,CAAC,CAAC;;IAErD;IACA,IAAII,QAAQ,GAAGnC,cAAc,CAACkB,OAAO,CAACe,SAAS,EAAE,CAAC,CAAC;IACnD,IAAIG,OAAO,GAAGpC,cAAc,CAACqC,OAAO,CAACF,QAAQ,EAAE,CAAC,CAAC,CAAC;IAClD,OAAOjC,YAAY,CAAC+B,SAAS,EAAEH,YAAY,CAAC,IAAI5B,YAAY,CAACkC,OAAO,EAAEN,YAAY,CAAC;EACrF,CAAC,GAAG,IAAI;;EAER;EACA,IAAIQ,QAAQ,GAAG,aAAalD,KAAK,CAACmD,aAAa,CAAC,QAAQ,EAAE;IACxDC,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,QAAQ;IACb,YAAY,EAAE1C,MAAM,CAAC2C,YAAY;IACjCC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1BvC,YAAY,CAAC,QAAQ,CAAC;IACxB,CAAC;IACDwC,QAAQ,EAAE,CAAC,CAAC;IACZC,SAAS,EAAE,EAAE,CAACvC,MAAM,CAACR,SAAS,EAAE,aAAa;EAC/C,CAAC,EAAET,WAAW,CAAC8B,aAAa,EAAE;IAC5BpB,MAAM,EAAEA,MAAM;IACd0B,MAAM,EAAE1B,MAAM,CAAC+C,UAAU;IACzB9C,cAAc,EAAEA;EAClB,CAAC,CAAC,EAAE,GAAG,EAAEX,WAAW,CAAC+B,WAAW,EAAE;IAChCrB,MAAM,EAAEA,MAAM;IACd0B,MAAM,EAAE1B,MAAM,CAAC+C,UAAU;IACzB9C,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC;;EAEH;EACA,OAAO,aAAaZ,KAAK,CAACmD,aAAa,CAAC/C,YAAY,CAACuD,QAAQ,EAAE;IAC7DC,KAAK,EAAEvC;EACT,CAAC,EAAE,aAAarB,KAAK,CAACmD,aAAa,CAAC,KAAK,EAAE;IACzCM,SAAS,EAAExC;EACb,CAAC,EAAE,aAAajB,KAAK,CAACmD,aAAa,CAAC5C,WAAW,EAAE;IAC/CsD,WAAW,EAAE,SAASA,WAAWA,CAACC,QAAQ,EAAE;MAC1C,OAAOlD,cAAc,CAACkB,OAAO,CAACjB,WAAW,EAAEiD,QAAQ,GAAG,EAAE,CAAC;IAC3D,CAAC;IACDC,QAAQ,EAAEhD;IACV;IAAA;;IAEAiD,QAAQ,EAAE1C,YAAY;IACtB2C,MAAM,EAAEpC;EACV,CAAC,EAAEqB,QAAQ,CAAC,EAAE,aAAalD,KAAK,CAACmD,aAAa,CAAC7C,SAAS,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEY,KAAK,EAAE;IAC5EK,YAAY,EAAE0B,kBAAkB;IAChC0B,WAAW,EAAEvD,MAAM,CAACwD,eAAe;IACnCC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTpC,QAAQ,EAAEA;IACV;IAAA;;IAEAC,WAAW,EAAEA,WAAW;IACxBE,WAAW,EAAEA,WAAW;IACxBG,gBAAgB,EAAEA;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC;AACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
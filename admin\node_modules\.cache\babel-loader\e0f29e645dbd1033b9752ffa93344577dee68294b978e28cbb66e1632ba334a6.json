{"ast": null, "code": "import _objectSpread from\"G:/phpstudy_pro/WWW/admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Typography,Tag,Form,Input,Select,DatePicker,Row,Col,Statistic,message,Modal,Descriptions}from'antd';import{SearchOutlined,EyeOutlined,EditOutlined,ExportOutlined,ReloadOutlined}from'@ant-design/icons';import{useNavigate}from'react-router-dom';import{orderApi}from'../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{RangePicker}=DatePicker;// 使用从 types/order.ts 导入的类型，这里定义一个本地接口用于显示\n// 订单数据将从API获取，不再使用模拟数据\nconst OrderList=()=>{const[form]=Form.useForm();const[orders,setOrders]=useState([]);const[filteredOrders,setFilteredOrders]=useState([]);const[loading,setLoading]=useState(false);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[detailModalVisible,setDetailModalVisible]=useState(false);const[selectedOrder,setSelectedOrder]=useState(null);const[pagination,setPagination]=useState({current:1,pageSize:10,total:0});const[stats,setStats]=useState({total:0,pending:0,processing:0,pending_upload:0,shipped:0,activated:0,failed:0,cancelled:0,today_orders:0,week_orders:0,month_orders:0});const navigate=useNavigate();// 组件加载时获取订单数据\nuseEffect(()=>{loadOrders();loadStats();},[]);// 转换API数据格式为组件使用的格式\nconst transformOrderData=apiOrder=>{return{id:apiOrder.id,orderNo:apiOrder.order_no,customerName:apiOrder.customer_name,customerPhone:apiOrder.customer_phone,customerIdCard:apiOrder.customer_id_card,productName:apiOrder.product_name,operator:apiOrder.operator,deliveryAddress:apiOrder.delivery_address,status:apiOrder.status,priority:apiOrder.priority,logisticsCompany:apiOrder.logistics_company,trackingNumber:apiOrder.tracking_number,shippedAt:apiOrder.shipped_at,estimatedDelivery:apiOrder.estimated_delivery,createdAt:apiOrder.created_at,updatedAt:apiOrder.updated_at};};// 加载订单数据\nconst loadOrders=async params=>{setLoading(true);try{var _response$data,_response$data2,_response$data3;const queryParams=_objectSpread({page:pagination.current,per_page:pagination.pageSize},params);const response=await orderApi.getList(queryParams);console.log('API响应数据:',response);// 调试信息\n// 检查响应数据结构\nconst orderList=((_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.list)||((_response$data2=response.data)===null||_response$data2===void 0?void 0:_response$data2.data)||[];const pagination=((_response$data3=response.data)===null||_response$data3===void 0?void 0:_response$data3.pagination)||response.data;const transformedOrders=orderList.map(transformOrderData);setOrders(transformedOrders);setFilteredOrders(transformedOrders);setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{total:(pagination===null||pagination===void 0?void 0:pagination.total)||0,current:(pagination===null||pagination===void 0?void 0:pagination.current_page)||1}));}catch(error){console.error('获取订单数据失败:',error);message.error(\"\\u83B7\\u53D6\\u8BA2\\u5355\\u6570\\u636E\\u5931\\u8D25: \".concat(error instanceof Error?error.message:'未知错误'));// 如果API失败，设置为空数组\nsetOrders([]);setFilteredOrders([]);}finally{setLoading(false);}};// 加载统计数据\nconst loadStats=async()=>{try{const response=await orderApi.getStats();setStats(response.data);}catch(error){console.error('获取统计数据失败:',error);// 如果API失败，使用本地计算的统计数据\n}};// 状态颜色映射\nconst getStatusColor=status=>{const colors={pending:'orange',processing:'blue',pending_upload:'purple',shipped:'cyan',activated:'green',failed:'red',cancelled:'default'};return colors[status]||'default';};// 状态文本映射\nconst getStatusText=status=>{const texts={pending:'待处理',processing:'开卡中',pending_upload:'待上传三证',shipped:'已发货',activated:'已激活',failed:'开卡失败',cancelled:'已取消'};return texts[status]||status;};// 优先级颜色映射\nconst getPriorityColor=priority=>{const colors={low:'default',normal:'blue',high:'orange',urgent:'red'};return colors[priority]||'default';};// 优先级文本映射\nconst getPriorityText=priority=>{const texts={low:'低',normal:'普通',high:'高',urgent:'紧急'};return texts[priority]||'普通';};// 如果API统计数据不可用，使用本地计算的统计数据作为备用\nconst localStats={total:filteredOrders.length,pending:filteredOrders.filter(order=>order.status==='pending').length,processing:filteredOrders.filter(order=>order.status==='processing').length,pending_upload:filteredOrders.filter(order=>order.status==='pending_upload').length,shipped:filteredOrders.filter(order=>order.status==='shipped').length,activated:filteredOrders.filter(order=>order.status==='activated').length,failed:filteredOrders.filter(order=>order.status==='failed').length,cancelled:filteredOrders.filter(order=>order.status==='cancelled').length};// 使用API统计数据，如果不可用则使用本地计算的数据\nconst displayStats=stats.total>0?stats:localStats;// 表格列定义\nconst columns=[{title:'订单号',dataIndex:'orderNo',key:'orderNo',width:160,render:text=>/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:'12px'},children:text})},{title:'客户信息',key:'customer',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600},children:record.customerName}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#666'},children:record.customerPhone})]})},{title:'产品名称',dataIndex:'productName',key:'productName',ellipsis:true},{title:'运营商',dataIndex:'operator',key:'operator',width:100,render:text=>/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:text})},{title:'订单状态',dataIndex:'status',key:'status',width:100,render:status=>/*#__PURE__*/_jsx(Tag,{color:getStatusColor(status),children:getStatusText(status)})},{title:'物流信息',key:'logistics',width:200,render:(_,record)=>{if(record.status==='shipped'||record.status==='activated'){return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600,fontSize:'12px'},children:record.logisticsCompany||'暂无'}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'11px',color:'#666'},children:record.trackingNumber||'暂无快递单号'}),record.shippedAt&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'11px',color:'#999'},children:[\"\\u53D1\\u8D27: \",record.shippedAt.split(' ')[0]]})]});}return/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'12px'},children:\"\\u672A\\u53D1\\u8D27\"});}},{title:'创建时间',dataIndex:'createdAt',key:'createdAt',width:150,render:text=>/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px'},children:text})},{title:'操作',key:'action',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewOrder(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEditOrder(record.id),children:\"\\u7F16\\u8F91\"})]})}];// 事件处理函数\nconst handleSearch=values=>{const searchParams={order_no:values.orderNo,customer_name:values.customerName,operator:values.operator,status:values.status};// 重置分页到第一页\nsetPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:1}));loadOrders(searchParams);};const handleRefresh=async()=>{await loadOrders();await loadStats();message.success('数据已刷新');};const handleViewOrder=order=>{setSelectedOrder(order);setDetailModalVisible(true);};const handleEditOrder=id=>{message.info(\"\\u7F16\\u8F91\\u8BA2\\u5355 \".concat(id,\" \\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\"));};const handleExport=()=>{message.info('导出功能开发中...');};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Title,{level:2,style:{margin:0,color:'#1890ff'},children:\"\\u5168\\u90E8\\u8BA2\\u5355\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u7BA1\\u7406\\u6240\\u6709\\u8BA2\\u5355\\u4FE1\\u606F\"})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Col,{span:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u8BA2\\u5355\\u6570\",value:displayStats.total,valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{span:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F85\\u5904\\u7406\",value:displayStats.pending,valueStyle:{color:'#faad14'}})})}),/*#__PURE__*/_jsx(Col,{span:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\",value:displayStats.pending_upload,valueStyle:{color:'#722ed1'}})})}),/*#__PURE__*/_jsx(Col,{span:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F00\\u5361\\u4E2D\",value:displayStats.processing,valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{span:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u53D1\\u8D27\",value:displayStats.shipped,valueStyle:{color:'#13c2c2'}})})}),/*#__PURE__*/_jsx(Col,{span:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u6FC0\\u6D3B\",value:displayStats.activated,valueStyle:{color:'#52c41a'}})})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Form,{form:form,layout:\"inline\",onFinish:handleSearch,style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Form.Item,{name:\"orderNo\",label:\"\\u8BA2\\u5355\\u53F7\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",style:{width:150}})}),/*#__PURE__*/_jsx(Form.Item,{name:\"customerName\",label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",style:{width:120}})}),/*#__PURE__*/_jsx(Form.Item,{name:\"operator\",label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",style:{width:120},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u79FB\\u52A8\",children:\"\\u4E2D\\u56FD\\u79FB\\u52A8\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u7535\\u4FE1\",children:\"\\u4E2D\\u56FD\\u7535\\u4FE1\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u8054\\u901A\",children:\"\\u4E2D\\u56FD\\u8054\\u901A\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u5E7F\\u7535\",children:\"\\u4E2D\\u56FD\\u5E7F\\u7535\"})]})}),/*#__PURE__*/_jsx(Form.Item,{name:\"status\",label:\"\\u8BA2\\u5355\\u72B6\\u6001\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",style:{width:120},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"pending\",children:\"\\u5F85\\u5904\\u7406\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"processing\",children:\"\\u5F00\\u5361\\u4E2D\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"pending_upload\",children:\"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"shipped\",children:\"\\u5DF2\\u53D1\\u8D27\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"activated\",children:\"\\u5DF2\\u6FC0\\u6D3B\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"failed\",children:\"\\u5F00\\u5361\\u5931\\u8D25\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"cancelled\",children:\"\\u5DF2\\u53D6\\u6D88\"})]})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",icon:/*#__PURE__*/_jsx(SearchOutlined,{}),children:\"\\u641C\\u7D22\"})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:16},children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Text,{strong:true,children:[\"\\u5171 \",filteredOrders.length,\" \\u6761\\u8BA2\\u5355\"]})}),/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleRefresh,icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),children:\"\\u5237\\u65B0\"}),/*#__PURE__*/_jsx(Button,{onClick:handleExport,icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\"})]})]}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:filteredOrders,rowKey:\"id\",loading:loading,scroll:{x:1400},pagination:{current:pagination.current,pageSize:pagination.pageSize,total:pagination.total,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u603B\\u5171 \").concat(total,\" \\u6761\"),onChange:(page,pageSize)=>{setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:page,pageSize:pageSize||10}));loadOrders();}},rowSelection:{selectedRowKeys,onChange:setSelectedRowKeys}})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u8BA2\\u5355\\u8BE6\\u60C5\",open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:600,children:selectedOrder&&/*#__PURE__*/_jsxs(Descriptions,{column:2,bordered:true,children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u53F7\",span:2,children:selectedOrder.orderNo}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:selectedOrder.customerName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8054\\u7CFB\\u7535\\u8BDD\",children:selectedOrder.customerPhone}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u540D\\u79F0\",span:2,children:selectedOrder.productName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Tag,{color:getStatusColor(selectedOrder.status),children:getStatusText(selectedOrder.status)})}),(selectedOrder.status==='shipped'||selectedOrder.status==='activated')&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7269\\u6D41\\u516C\\u53F8\",children:selectedOrder.logisticsCompany||'暂无'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5FEB\\u9012\\u5355\\u53F7\",children:/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.trackingNumber||'暂无'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u53D1\\u8D27\\u65F6\\u95F4\",children:selectedOrder.shippedAt||'暂无'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u9884\\u8BA1\\u9001\\u8FBE\",children:selectedOrder.estimatedDelivery||'暂无'})]}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u521B\\u5EFA\\u65F6\\u95F4\",children:selectedOrder.createdAt}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u66F4\\u65B0\\u65F6\\u95F4\",children:selectedOrder.updatedAt})]})})]});};export default OrderList;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nconst useColumnIcons = (prefixCls, rtl, expandIcon) => {\n  let mergedExpandIcon = expandIcon;\n  if (!expandIcon) {\n    mergedExpandIcon = rtl ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null);\n  }\n  const loadingIcon = /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-menu-item-loading-icon\")\n  }, /*#__PURE__*/React.createElement(LoadingOutlined, {\n    spin: true\n  }));\n  return React.useMemo(() => [mergedExpandIcon, loadingIcon], [mergedExpandIcon]);\n};\nexport default useColumnIcons;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nexport const canUseDocElement = () => canUseDom() && window.document.documentElement;\nexport { isStyleSupport };", "map": {"version": 3, "names": ["canUseDom", "isStyleSupport", "canUseDocElement", "window", "document", "documentElement"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/_util/styleChecker.js"], "sourcesContent": ["import canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nexport const canUseDocElement = () => canUseDom() && window.document.documentElement;\nexport { isStyleSupport };"], "mappings": "AAAA,OAAOA,SAAS,MAAM,0BAA0B;AAChD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAMF,SAAS,CAAC,CAAC,IAAIG,MAAM,CAACC,QAAQ,CAACC,eAAe;AACpF,SAASJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { UnstableContext } from \"../context\";\n/** Drag to delete offset. It's a user experience number for dragging out */\nvar REMOVE_DIST = 130;\nfunction getPosition(e) {\n  var obj = 'targetTouches' in e ? e.targetTouches[0] : e;\n  return {\n    pageX: obj.pageX,\n    pageY: obj.pageY\n  };\n}\nfunction useDrag(containerRef, direction, rawValues, min, max, formatValue, triggerChange, finishChange, offsetValues, editable, minCount) {\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    draggingValue = _React$useState2[0],\n    setDraggingValue = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    draggingIndex = _React$useState4[0],\n    setDraggingIndex = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    draggingDelete = _React$useState6[0],\n    setDraggingDelete = _React$useState6[1];\n  var _React$useState7 = React.useState(rawValues),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    cacheValues = _React$useState8[0],\n    setCacheValues = _React$useState8[1];\n  var _React$useState9 = React.useState(rawValues),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    originValues = _React$useState10[0],\n    setOriginValues = _React$useState10[1];\n  var mouseMoveEventRef = React.useRef(null);\n  var mouseUpEventRef = React.useRef(null);\n  var touchEventTargetRef = React.useRef(null);\n  var _React$useContext = React.useContext(UnstableContext),\n    onDragStart = _React$useContext.onDragStart,\n    onDragChange = _React$useContext.onDragChange;\n  useLayoutEffect(function () {\n    if (draggingIndex === -1) {\n      setCacheValues(rawValues);\n    }\n  }, [rawValues, draggingIndex]);\n\n  // Clean up event\n  React.useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveEventRef.current);\n      document.removeEventListener('mouseup', mouseUpEventRef.current);\n      if (touchEventTargetRef.current) {\n        touchEventTargetRef.current.removeEventListener('touchmove', mouseMoveEventRef.current);\n        touchEventTargetRef.current.removeEventListener('touchend', mouseUpEventRef.current);\n      }\n    };\n  }, []);\n  var flushValues = function flushValues(nextValues, nextValue, deleteMark) {\n    // Perf: Only update state when value changed\n    if (nextValue !== undefined) {\n      setDraggingValue(nextValue);\n    }\n    setCacheValues(nextValues);\n    var changeValues = nextValues;\n    if (deleteMark) {\n      changeValues = nextValues.filter(function (_, i) {\n        return i !== draggingIndex;\n      });\n    }\n    triggerChange(changeValues);\n    if (onDragChange) {\n      onDragChange({\n        rawValues: nextValues,\n        deleteIndex: deleteMark ? draggingIndex : -1,\n        draggingIndex: draggingIndex,\n        draggingValue: nextValue\n      });\n    }\n  };\n  var updateCacheValue = useEvent(function (valueIndex, offsetPercent, deleteMark) {\n    if (valueIndex === -1) {\n      // >>>> Dragging on the track\n      var startValue = originValues[0];\n      var endValue = originValues[originValues.length - 1];\n      var maxStartOffset = min - startValue;\n      var maxEndOffset = max - endValue;\n\n      // Get valid offset\n      var offset = offsetPercent * (max - min);\n      offset = Math.max(offset, maxStartOffset);\n      offset = Math.min(offset, maxEndOffset);\n\n      // Use first value to revert back of valid offset (like steps marks)\n      var formatStartValue = formatValue(startValue + offset);\n      offset = formatStartValue - startValue;\n      var cloneCacheValues = originValues.map(function (val) {\n        return val + offset;\n      });\n      flushValues(cloneCacheValues);\n    } else {\n      // >>>> Dragging on the handle\n      var offsetDist = (max - min) * offsetPercent;\n\n      // Always start with the valueIndex origin value\n      var cloneValues = _toConsumableArray(cacheValues);\n      cloneValues[valueIndex] = originValues[valueIndex];\n      var next = offsetValues(cloneValues, offsetDist, valueIndex, 'dist');\n      flushValues(next.values, next.value, deleteMark);\n    }\n  });\n  var onStartMove = function onStartMove(e, valueIndex, startValues) {\n    e.stopPropagation();\n\n    // 如果是点击 track 触发的，需要传入变化后的初始值，而不能直接用 rawValues\n    var initialValues = startValues || rawValues;\n    var originValue = initialValues[valueIndex];\n    setDraggingIndex(valueIndex);\n    setDraggingValue(originValue);\n    setOriginValues(initialValues);\n    setCacheValues(initialValues);\n    setDraggingDelete(false);\n    var _getPosition = getPosition(e),\n      startX = _getPosition.pageX,\n      startY = _getPosition.pageY;\n\n    // We declare it here since closure can't get outer latest value\n    var deleteMark = false;\n\n    // Internal trigger event\n    if (onDragStart) {\n      onDragStart({\n        rawValues: initialValues,\n        draggingIndex: valueIndex,\n        draggingValue: originValue\n      });\n    }\n\n    // Moving\n    var onMouseMove = function onMouseMove(event) {\n      event.preventDefault();\n      var _getPosition2 = getPosition(event),\n        moveX = _getPosition2.pageX,\n        moveY = _getPosition2.pageY;\n      var offsetX = moveX - startX;\n      var offsetY = moveY - startY;\n      var _containerRef$current = containerRef.current.getBoundingClientRect(),\n        width = _containerRef$current.width,\n        height = _containerRef$current.height;\n      var offSetPercent;\n      var removeDist;\n      switch (direction) {\n        case 'btt':\n          offSetPercent = -offsetY / height;\n          removeDist = offsetX;\n          break;\n        case 'ttb':\n          offSetPercent = offsetY / height;\n          removeDist = offsetX;\n          break;\n        case 'rtl':\n          offSetPercent = -offsetX / width;\n          removeDist = offsetY;\n          break;\n        default:\n          offSetPercent = offsetX / width;\n          removeDist = offsetY;\n      }\n\n      // Check if need mark remove\n      deleteMark = editable ? Math.abs(removeDist) > REMOVE_DIST && minCount < cacheValues.length : false;\n      setDraggingDelete(deleteMark);\n      updateCacheValue(valueIndex, offSetPercent, deleteMark);\n    };\n\n    // End\n    var onMouseUp = function onMouseUp(event) {\n      event.preventDefault();\n      document.removeEventListener('mouseup', onMouseUp);\n      document.removeEventListener('mousemove', onMouseMove);\n      if (touchEventTargetRef.current) {\n        touchEventTargetRef.current.removeEventListener('touchmove', mouseMoveEventRef.current);\n        touchEventTargetRef.current.removeEventListener('touchend', mouseUpEventRef.current);\n      }\n      mouseMoveEventRef.current = null;\n      mouseUpEventRef.current = null;\n      touchEventTargetRef.current = null;\n      finishChange(deleteMark);\n      setDraggingIndex(-1);\n      setDraggingDelete(false);\n    };\n    document.addEventListener('mouseup', onMouseUp);\n    document.addEventListener('mousemove', onMouseMove);\n    e.currentTarget.addEventListener('touchend', onMouseUp);\n    e.currentTarget.addEventListener('touchmove', onMouseMove);\n    mouseMoveEventRef.current = onMouseMove;\n    mouseUpEventRef.current = onMouseUp;\n    touchEventTargetRef.current = e.currentTarget;\n  };\n\n  // Only return cache value when it mapping with rawValues\n  var returnValues = React.useMemo(function () {\n    var sourceValues = _toConsumableArray(rawValues).sort(function (a, b) {\n      return a - b;\n    });\n    var targetValues = _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n    var counts = {};\n    targetValues.forEach(function (val) {\n      counts[val] = (counts[val] || 0) + 1;\n    });\n    sourceValues.forEach(function (val) {\n      counts[val] = (counts[val] || 0) - 1;\n    });\n    var maxDiffCount = editable ? 1 : 0;\n    var diffCount = Object.values(counts).reduce(function (prev, next) {\n      return prev + Math.abs(next);\n    }, 0);\n    return diffCount <= maxDiffCount ? cacheValues : rawValues;\n  }, [rawValues, cacheValues, editable]);\n  return [draggingIndex, draggingValue, draggingDelete, returnValues, onStartMove];\n}\nexport default useDrag;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
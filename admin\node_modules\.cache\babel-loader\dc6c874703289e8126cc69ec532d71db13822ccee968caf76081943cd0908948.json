{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { AggregationColor } from '../../color-picker/color';\nimport { isBright } from '../../color-picker/components/ColorPresets';\nimport { getLineHeight, mergeToken } from '../../theme/internal';\nimport { PresetColors } from '../../theme/interface';\nimport getAlphaColor from '../../theme/util/getAlphaColor';\nexport const prepareToken = token => {\n  const {\n    paddingInline,\n    onlyIconSize\n  } = token;\n  const buttonToken = mergeToken(token, {\n    buttonPaddingHorizontal: paddingInline,\n    buttonPaddingVertical: 0,\n    buttonIconOnlyFontSize: onlyIconSize\n  });\n  return buttonToken;\n};\nexport const prepareComponentToken = token => {\n  var _a, _b, _c, _d, _e, _f;\n  const contentFontSize = (_a = token.contentFontSize) !== null && _a !== void 0 ? _a : token.fontSize;\n  const contentFontSizeSM = (_b = token.contentFontSizeSM) !== null && _b !== void 0 ? _b : token.fontSize;\n  const contentFontSizeLG = (_c = token.contentFontSizeLG) !== null && _c !== void 0 ? _c : token.fontSizeLG;\n  const contentLineHeight = (_d = token.contentLineHeight) !== null && _d !== void 0 ? _d : getLineHeight(contentFontSize);\n  const contentLineHeightSM = (_e = token.contentLineHeightSM) !== null && _e !== void 0 ? _e : getLineHeight(contentFontSizeSM);\n  const contentLineHeightLG = (_f = token.contentLineHeightLG) !== null && _f !== void 0 ? _f : getLineHeight(contentFontSizeLG);\n  const solidTextColor = isBright(new AggregationColor(token.colorBgSolid), '#fff') ? '#000' : '#fff';\n  const shadowColorTokens = PresetColors.reduce((prev, colorKey) => Object.assign(Object.assign({}, prev), {\n    [`${colorKey}ShadowColor`]: `0 ${unit(token.controlOutlineWidth)} 0 ${getAlphaColor(token[`${colorKey}1`], token.colorBgContainer)}`\n  }), {});\n  return Object.assign(Object.assign({}, shadowColorTokens), {\n    fontWeight: 400,\n    defaultShadow: `0 ${token.controlOutlineWidth}px 0 ${token.controlTmpOutline}`,\n    primaryShadow: `0 ${token.controlOutlineWidth}px 0 ${token.controlOutline}`,\n    dangerShadow: `0 ${token.controlOutlineWidth}px 0 ${token.colorErrorOutline}`,\n    primaryColor: token.colorTextLightSolid,\n    dangerColor: token.colorTextLightSolid,\n    borderColorDisabled: token.colorBorder,\n    defaultGhostColor: token.colorBgContainer,\n    ghostBg: 'transparent',\n    defaultGhostBorderColor: token.colorBgContainer,\n    paddingInline: token.paddingContentHorizontal - token.lineWidth,\n    paddingInlineLG: token.paddingContentHorizontal - token.lineWidth,\n    paddingInlineSM: 8 - token.lineWidth,\n    onlyIconSize: 'inherit',\n    onlyIconSizeSM: 'inherit',\n    onlyIconSizeLG: 'inherit',\n    groupBorderColor: token.colorPrimaryHover,\n    linkHoverBg: 'transparent',\n    textTextColor: token.colorText,\n    textTextHoverColor: token.colorText,\n    textTextActiveColor: token.colorText,\n    textHoverBg: token.colorFillTertiary,\n    defaultColor: token.colorText,\n    defaultBg: token.colorBgContainer,\n    defaultBorderColor: token.colorBorder,\n    defaultBorderColorDisabled: token.colorBorder,\n    defaultHoverBg: token.colorBgContainer,\n    defaultHoverColor: token.colorPrimaryHover,\n    defaultHoverBorderColor: token.colorPrimaryHover,\n    defaultActiveBg: token.colorBgContainer,\n    defaultActiveColor: token.colorPrimaryActive,\n    defaultActiveBorderColor: token.colorPrimaryActive,\n    solidTextColor,\n    contentFontSize,\n    contentFontSizeSM,\n    contentFontSizeLG,\n    contentLineHeight,\n    contentLineHeightSM,\n    contentLineHeightLG,\n    paddingBlock: Math.max((token.controlHeight - contentFontSize * contentLineHeight) / 2 - token.lineWidth, 0),\n    paddingBlockSM: Math.max((token.controlHeightSM - contentFontSizeSM * contentLineHeightSM) / 2 - token.lineWidth, 0),\n    paddingBlockLG: Math.max((token.controlHeightLG - contentFontSizeLG * contentLineHeightLG) / 2 - token.lineWidth, 0)\n  });\n};", "map": {"version": 3, "names": ["unit", "AggregationColor", "isBright", "getLineHeight", "mergeToken", "PresetColors", "getAlphaColor", "prepareToken", "token", "paddingInline", "onlyIconSize", "buttonToken", "buttonPaddingHorizontal", "buttonPaddingVertical", "buttonIconOnlyFontSize", "prepareComponentToken", "_a", "_b", "_c", "_d", "_e", "_f", "contentFontSize", "fontSize", "contentFontSizeSM", "contentFontSizeLG", "fontSizeLG", "contentLineHeight", "contentLineHeightSM", "contentLineHeightLG", "solidTextColor", "colorBgSolid", "shadowColorTokens", "reduce", "prev", "colorKey", "Object", "assign", "controlOutlineWidth", "colorBgContainer", "fontWeight", "defaultShadow", "controlTmpOutline", "primaryShadow", "controlOutline", "dangerShadow", "colorErrorOutline", "primaryColor", "colorTextLightSolid", "dangerColor", "borderColorDisabled", "colorBorder", "defaultGhostColor", "ghostBg", "defaultGhostBorderColor", "paddingContentHorizontal", "lineWidth", "paddingInlineLG", "paddingInlineSM", "onlyIconSizeSM", "onlyIconSizeLG", "groupBorderColor", "colorPrimaryHover", "linkHoverBg", "textTextColor", "colorText", "textTextHoverColor", "textTextActiveColor", "textHoverBg", "colorFillTertiary", "defaultColor", "defaultBg", "defaultBorderColor", "defaultBorderColorDisabled", "defaultHoverBg", "defaultHoverColor", "defaultHoverBorderColor", "defaultActiveBg", "defaultActiveColor", "colorPrimaryActive", "defaultActiveBorderColor", "paddingBlock", "Math", "max", "controlHeight", "paddingBlockSM", "controlHeightSM", "paddingBlockLG", "controlHeightLG"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/button/style/token.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { AggregationColor } from '../../color-picker/color';\nimport { isBright } from '../../color-picker/components/ColorPresets';\nimport { getLineHeight, mergeToken } from '../../theme/internal';\nimport { PresetColors } from '../../theme/interface';\nimport getAlphaColor from '../../theme/util/getAlphaColor';\nexport const prepareToken = token => {\n  const {\n    paddingInline,\n    onlyIconSize\n  } = token;\n  const buttonToken = mergeToken(token, {\n    buttonPaddingHorizontal: paddingInline,\n    buttonPaddingVertical: 0,\n    buttonIconOnlyFontSize: onlyIconSize\n  });\n  return buttonToken;\n};\nexport const prepareComponentToken = token => {\n  var _a, _b, _c, _d, _e, _f;\n  const contentFontSize = (_a = token.contentFontSize) !== null && _a !== void 0 ? _a : token.fontSize;\n  const contentFontSizeSM = (_b = token.contentFontSizeSM) !== null && _b !== void 0 ? _b : token.fontSize;\n  const contentFontSizeLG = (_c = token.contentFontSizeLG) !== null && _c !== void 0 ? _c : token.fontSizeLG;\n  const contentLineHeight = (_d = token.contentLineHeight) !== null && _d !== void 0 ? _d : getLineHeight(contentFontSize);\n  const contentLineHeightSM = (_e = token.contentLineHeightSM) !== null && _e !== void 0 ? _e : getLineHeight(contentFontSizeSM);\n  const contentLineHeightLG = (_f = token.contentLineHeightLG) !== null && _f !== void 0 ? _f : getLineHeight(contentFontSizeLG);\n  const solidTextColor = isBright(new AggregationColor(token.colorBgSolid), '#fff') ? '#000' : '#fff';\n  const shadowColorTokens = PresetColors.reduce((prev, colorKey) => Object.assign(Object.assign({}, prev), {\n    [`${colorKey}ShadowColor`]: `0 ${unit(token.controlOutlineWidth)} 0 ${getAlphaColor(token[`${colorKey}1`], token.colorBgContainer)}`\n  }), {});\n  return Object.assign(Object.assign({}, shadowColorTokens), {\n    fontWeight: 400,\n    defaultShadow: `0 ${token.controlOutlineWidth}px 0 ${token.controlTmpOutline}`,\n    primaryShadow: `0 ${token.controlOutlineWidth}px 0 ${token.controlOutline}`,\n    dangerShadow: `0 ${token.controlOutlineWidth}px 0 ${token.colorErrorOutline}`,\n    primaryColor: token.colorTextLightSolid,\n    dangerColor: token.colorTextLightSolid,\n    borderColorDisabled: token.colorBorder,\n    defaultGhostColor: token.colorBgContainer,\n    ghostBg: 'transparent',\n    defaultGhostBorderColor: token.colorBgContainer,\n    paddingInline: token.paddingContentHorizontal - token.lineWidth,\n    paddingInlineLG: token.paddingContentHorizontal - token.lineWidth,\n    paddingInlineSM: 8 - token.lineWidth,\n    onlyIconSize: 'inherit',\n    onlyIconSizeSM: 'inherit',\n    onlyIconSizeLG: 'inherit',\n    groupBorderColor: token.colorPrimaryHover,\n    linkHoverBg: 'transparent',\n    textTextColor: token.colorText,\n    textTextHoverColor: token.colorText,\n    textTextActiveColor: token.colorText,\n    textHoverBg: token.colorFillTertiary,\n    defaultColor: token.colorText,\n    defaultBg: token.colorBgContainer,\n    defaultBorderColor: token.colorBorder,\n    defaultBorderColorDisabled: token.colorBorder,\n    defaultHoverBg: token.colorBgContainer,\n    defaultHoverColor: token.colorPrimaryHover,\n    defaultHoverBorderColor: token.colorPrimaryHover,\n    defaultActiveBg: token.colorBgContainer,\n    defaultActiveColor: token.colorPrimaryActive,\n    defaultActiveBorderColor: token.colorPrimaryActive,\n    solidTextColor,\n    contentFontSize,\n    contentFontSizeSM,\n    contentFontSizeLG,\n    contentLineHeight,\n    contentLineHeightSM,\n    contentLineHeightLG,\n    paddingBlock: Math.max((token.controlHeight - contentFontSize * contentLineHeight) / 2 - token.lineWidth, 0),\n    paddingBlockSM: Math.max((token.controlHeightSM - contentFontSizeSM * contentLineHeightSM) / 2 - token.lineWidth, 0),\n    paddingBlockLG: Math.max((token.controlHeightLG - contentFontSizeLG * contentLineHeightLG) / 2 - token.lineWidth, 0)\n  });\n};"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,QAAQ,QAAQ,4CAA4C;AACrE,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAO,MAAMC,YAAY,GAAGC,KAAK,IAAI;EACnC,MAAM;IACJC,aAAa;IACbC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,WAAW,GAAGP,UAAU,CAACI,KAAK,EAAE;IACpCI,uBAAuB,EAAEH,aAAa;IACtCI,qBAAqB,EAAE,CAAC;IACxBC,sBAAsB,EAAEJ;EAC1B,CAAC,CAAC;EACF,OAAOC,WAAW;AACpB,CAAC;AACD,OAAO,MAAMI,qBAAqB,GAAGP,KAAK,IAAI;EAC5C,IAAIQ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAC1B,MAAMC,eAAe,GAAG,CAACN,EAAE,GAAGR,KAAK,CAACc,eAAe,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGR,KAAK,CAACe,QAAQ;EACpG,MAAMC,iBAAiB,GAAG,CAACP,EAAE,GAAGT,KAAK,CAACgB,iBAAiB,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGT,KAAK,CAACe,QAAQ;EACxG,MAAME,iBAAiB,GAAG,CAACP,EAAE,GAAGV,KAAK,CAACiB,iBAAiB,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGV,KAAK,CAACkB,UAAU;EAC1G,MAAMC,iBAAiB,GAAG,CAACR,EAAE,GAAGX,KAAK,CAACmB,iBAAiB,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGhB,aAAa,CAACmB,eAAe,CAAC;EACxH,MAAMM,mBAAmB,GAAG,CAACR,EAAE,GAAGZ,KAAK,CAACoB,mBAAmB,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGjB,aAAa,CAACqB,iBAAiB,CAAC;EAC9H,MAAMK,mBAAmB,GAAG,CAACR,EAAE,GAAGb,KAAK,CAACqB,mBAAmB,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGlB,aAAa,CAACsB,iBAAiB,CAAC;EAC9H,MAAMK,cAAc,GAAG5B,QAAQ,CAAC,IAAID,gBAAgB,CAACO,KAAK,CAACuB,YAAY,CAAC,EAAE,MAAM,CAAC,GAAG,MAAM,GAAG,MAAM;EACnG,MAAMC,iBAAiB,GAAG3B,YAAY,CAAC4B,MAAM,CAAC,CAACC,IAAI,EAAEC,QAAQ,KAAKC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC,EAAE;IACvG,CAAC,GAAGC,QAAQ,aAAa,GAAG,KAAKnC,IAAI,CAACQ,KAAK,CAAC8B,mBAAmB,CAAC,MAAMhC,aAAa,CAACE,KAAK,CAAC,GAAG2B,QAAQ,GAAG,CAAC,EAAE3B,KAAK,CAAC+B,gBAAgB,CAAC;EACpI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACP,OAAOH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,iBAAiB,CAAC,EAAE;IACzDQ,UAAU,EAAE,GAAG;IACfC,aAAa,EAAE,KAAKjC,KAAK,CAAC8B,mBAAmB,QAAQ9B,KAAK,CAACkC,iBAAiB,EAAE;IAC9EC,aAAa,EAAE,KAAKnC,KAAK,CAAC8B,mBAAmB,QAAQ9B,KAAK,CAACoC,cAAc,EAAE;IAC3EC,YAAY,EAAE,KAAKrC,KAAK,CAAC8B,mBAAmB,QAAQ9B,KAAK,CAACsC,iBAAiB,EAAE;IAC7EC,YAAY,EAAEvC,KAAK,CAACwC,mBAAmB;IACvCC,WAAW,EAAEzC,KAAK,CAACwC,mBAAmB;IACtCE,mBAAmB,EAAE1C,KAAK,CAAC2C,WAAW;IACtCC,iBAAiB,EAAE5C,KAAK,CAAC+B,gBAAgB;IACzCc,OAAO,EAAE,aAAa;IACtBC,uBAAuB,EAAE9C,KAAK,CAAC+B,gBAAgB;IAC/C9B,aAAa,EAAED,KAAK,CAAC+C,wBAAwB,GAAG/C,KAAK,CAACgD,SAAS;IAC/DC,eAAe,EAAEjD,KAAK,CAAC+C,wBAAwB,GAAG/C,KAAK,CAACgD,SAAS;IACjEE,eAAe,EAAE,CAAC,GAAGlD,KAAK,CAACgD,SAAS;IACpC9C,YAAY,EAAE,SAAS;IACvBiD,cAAc,EAAE,SAAS;IACzBC,cAAc,EAAE,SAAS;IACzBC,gBAAgB,EAAErD,KAAK,CAACsD,iBAAiB;IACzCC,WAAW,EAAE,aAAa;IAC1BC,aAAa,EAAExD,KAAK,CAACyD,SAAS;IAC9BC,kBAAkB,EAAE1D,KAAK,CAACyD,SAAS;IACnCE,mBAAmB,EAAE3D,KAAK,CAACyD,SAAS;IACpCG,WAAW,EAAE5D,KAAK,CAAC6D,iBAAiB;IACpCC,YAAY,EAAE9D,KAAK,CAACyD,SAAS;IAC7BM,SAAS,EAAE/D,KAAK,CAAC+B,gBAAgB;IACjCiC,kBAAkB,EAAEhE,KAAK,CAAC2C,WAAW;IACrCsB,0BAA0B,EAAEjE,KAAK,CAAC2C,WAAW;IAC7CuB,cAAc,EAAElE,KAAK,CAAC+B,gBAAgB;IACtCoC,iBAAiB,EAAEnE,KAAK,CAACsD,iBAAiB;IAC1Cc,uBAAuB,EAAEpE,KAAK,CAACsD,iBAAiB;IAChDe,eAAe,EAAErE,KAAK,CAAC+B,gBAAgB;IACvCuC,kBAAkB,EAAEtE,KAAK,CAACuE,kBAAkB;IAC5CC,wBAAwB,EAAExE,KAAK,CAACuE,kBAAkB;IAClDjD,cAAc;IACdR,eAAe;IACfE,iBAAiB;IACjBC,iBAAiB;IACjBE,iBAAiB;IACjBC,mBAAmB;IACnBC,mBAAmB;IACnBoD,YAAY,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC3E,KAAK,CAAC4E,aAAa,GAAG9D,eAAe,GAAGK,iBAAiB,IAAI,CAAC,GAAGnB,KAAK,CAACgD,SAAS,EAAE,CAAC,CAAC;IAC5G6B,cAAc,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC3E,KAAK,CAAC8E,eAAe,GAAG9D,iBAAiB,GAAGI,mBAAmB,IAAI,CAAC,GAAGpB,KAAK,CAACgD,SAAS,EAAE,CAAC,CAAC;IACpH+B,cAAc,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC3E,KAAK,CAACgF,eAAe,GAAG/D,iBAAiB,GAAGI,mBAAmB,IAAI,CAAC,GAAGrB,KAAK,CAACgD,SAAS,EAAE,CAAC;EACrH,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
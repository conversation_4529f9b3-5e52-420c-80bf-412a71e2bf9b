{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"],\n  _excluded2 = [\"fixed\"];\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { EXPAND_COLUMN } from \"../../constant\";\nimport { INTERNAL_COL_DEFINE } from \"../../utils/legacyUtil\";\nimport useWidthColumns from \"./useWidthColumns\";\nexport function convertChildrenToColumns(children) {\n  return toArray(children).filter(function (node) {\n    return /*#__PURE__*/React.isValidElement(node);\n  }).map(function (_ref) {\n    var key = _ref.key,\n      props = _ref.props;\n    var nodeChildren = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var column = _objectSpread({\n      key: key\n    }, restProps);\n    if (nodeChildren) {\n      column.children = convertChildrenToColumns(nodeChildren);\n    }\n    return column;\n  });\n}\nfunction filterHiddenColumns(columns) {\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object' && !column.hidden;\n  }).map(function (column) {\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return _objectSpread(_objectSpread({}, column), {}, {\n        children: filterHiddenColumns(subColumns)\n      });\n    }\n    return column;\n  });\n}\nfunction flatColumns(columns) {\n  var parentKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key';\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object';\n  }).reduce(function (list, column, index) {\n    var fixed = column.fixed;\n    // Convert `fixed='true'` to `fixed='left'` instead\n    var parsedFixed = fixed === true ? 'left' : fixed;\n    var mergedKey = \"\".concat(parentKey, \"-\").concat(index);\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return [].concat(_toConsumableArray(list), _toConsumableArray(flatColumns(subColumns, mergedKey).map(function (subColum) {\n        return _objectSpread({\n          fixed: parsedFixed\n        }, subColum);\n      })));\n    }\n    return [].concat(_toConsumableArray(list), [_objectSpread(_objectSpread({\n      key: mergedKey\n    }, column), {}, {\n      fixed: parsedFixed\n    })]);\n  }, []);\n}\nfunction revertForRtl(columns) {\n  return columns.map(function (column) {\n    var fixed = column.fixed,\n      restProps = _objectWithoutProperties(column, _excluded2);\n\n    // Convert `fixed='left'` to `fixed='right'` instead\n    var parsedFixed = fixed;\n    if (fixed === 'left') {\n      parsedFixed = 'right';\n    } else if (fixed === 'right') {\n      parsedFixed = 'left';\n    }\n    return _objectSpread({\n      fixed: parsedFixed\n    }, restProps);\n  });\n}\n\n/**\n * Parse `columns` & `children` into `columns`.\n */\nfunction useColumns(_ref2, transformColumns) {\n  var prefixCls = _ref2.prefixCls,\n    columns = _ref2.columns,\n    children = _ref2.children,\n    expandable = _ref2.expandable,\n    expandedKeys = _ref2.expandedKeys,\n    columnTitle = _ref2.columnTitle,\n    getRowKey = _ref2.getRowKey,\n    onTriggerExpand = _ref2.onTriggerExpand,\n    expandIcon = _ref2.expandIcon,\n    rowExpandable = _ref2.rowExpandable,\n    expandIconColumnIndex = _ref2.expandIconColumnIndex,\n    _ref2$expandedRowOffs = _ref2.expandedRowOffset,\n    expandedRowOffset = _ref2$expandedRowOffs === void 0 ? 0 : _ref2$expandedRowOffs,\n    direction = _ref2.direction,\n    expandRowByClick = _ref2.expandRowByClick,\n    columnWidth = _ref2.columnWidth,\n    fixed = _ref2.fixed,\n    scrollWidth = _ref2.scrollWidth,\n    clientWidth = _ref2.clientWidth;\n  var baseColumns = React.useMemo(function () {\n    var newColumns = columns || convertChildrenToColumns(children) || [];\n    return filterHiddenColumns(newColumns.slice());\n  }, [columns, children]);\n\n  // ========================== Expand ==========================\n  var withExpandColumns = React.useMemo(function () {\n    if (expandable) {\n      var cloneColumns = baseColumns.slice();\n\n      // >>> Warning if use `expandIconColumnIndex`\n      if (process.env.NODE_ENV !== 'production' && expandIconColumnIndex >= 0) {\n        warning(false, '`expandIconColumnIndex` is deprecated. Please use `Table.EXPAND_COLUMN` in `columns` instead.');\n      }\n\n      // >>> Insert expand column if not exist\n      if (!cloneColumns.includes(EXPAND_COLUMN)) {\n        var expandColIndex = expandIconColumnIndex || 0;\n        if (expandColIndex >= 0 && (expandColIndex || fixed === 'left' || !fixed)) {\n          cloneColumns.splice(expandColIndex, 0, EXPAND_COLUMN);\n        }\n        if (fixed === 'right') {\n          cloneColumns.splice(baseColumns.length, 0, EXPAND_COLUMN);\n        }\n      }\n\n      // >>> Deduplicate additional expand column\n      if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (c) {\n        return c === EXPAND_COLUMN;\n      }).length > 1) {\n        warning(false, 'There exist more than one `EXPAND_COLUMN` in `columns`.');\n      }\n      var expandColumnIndex = cloneColumns.indexOf(EXPAND_COLUMN);\n      cloneColumns = cloneColumns.filter(function (column, index) {\n        return column !== EXPAND_COLUMN || index === expandColumnIndex;\n      });\n\n      // >>> Check if expand column need to fixed\n      var prevColumn = baseColumns[expandColumnIndex];\n      var fixedColumn;\n      if (fixed) {\n        fixedColumn = fixed;\n      } else {\n        fixedColumn = prevColumn ? prevColumn.fixed : null;\n      }\n\n      // >>> Create expandable column\n      var expandColumn = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, INTERNAL_COL_DEFINE, {\n        className: \"\".concat(prefixCls, \"-expand-icon-col\"),\n        columnType: 'EXPAND_COLUMN'\n      }), \"title\", columnTitle), \"fixed\", fixedColumn), \"className\", \"\".concat(prefixCls, \"-row-expand-icon-cell\")), \"width\", columnWidth), \"render\", function render(_, record, index) {\n        var rowKey = getRowKey(record, index);\n        var expanded = expandedKeys.has(rowKey);\n        var recordExpandable = rowExpandable ? rowExpandable(record) : true;\n        var icon = expandIcon({\n          prefixCls: prefixCls,\n          expanded: expanded,\n          expandable: recordExpandable,\n          record: record,\n          onExpand: onTriggerExpand\n        });\n        if (expandRowByClick) {\n          return /*#__PURE__*/React.createElement(\"span\", {\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            }\n          }, icon);\n        }\n        return icon;\n      });\n      return cloneColumns.map(function (col, index) {\n        var column = col === EXPAND_COLUMN ? expandColumn : col;\n        if (index < expandedRowOffset) {\n          return _objectSpread(_objectSpread({}, column), {}, {\n            fixed: column.fixed || 'left'\n          });\n        }\n        return column;\n      });\n    }\n    if (process.env.NODE_ENV !== 'production' && baseColumns.includes(EXPAND_COLUMN)) {\n      warning(false, '`expandable` is not config but there exist `EXPAND_COLUMN` in `columns`.');\n    }\n    return baseColumns.filter(function (col) {\n      return col !== EXPAND_COLUMN;\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [expandable, baseColumns, getRowKey, expandedKeys, expandIcon, direction, expandedRowOffset]);\n\n  // ========================= Transform ========================\n  var mergedColumns = React.useMemo(function () {\n    var finalColumns = withExpandColumns;\n    if (transformColumns) {\n      finalColumns = transformColumns(finalColumns);\n    }\n\n    // Always provides at least one column for table display\n    if (!finalColumns.length) {\n      finalColumns = [{\n        render: function render() {\n          return null;\n        }\n      }];\n    }\n    return finalColumns;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [transformColumns, withExpandColumns, direction]);\n\n  // ========================== Flatten =========================\n  var flattenColumns = React.useMemo(function () {\n    if (direction === 'rtl') {\n      return revertForRtl(flatColumns(mergedColumns));\n    }\n    return flatColumns(mergedColumns);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [mergedColumns, direction, scrollWidth]);\n\n  // ========================= Gap Fixed ========================\n  var hasGapFixed = React.useMemo(function () {\n    // Fixed: left, since old browser not support `findLastIndex`, we should use reverse loop\n    var lastLeftIndex = -1;\n    for (var i = flattenColumns.length - 1; i >= 0; i -= 1) {\n      var colFixed = flattenColumns[i].fixed;\n      if (colFixed === 'left' || colFixed === true) {\n        lastLeftIndex = i;\n        break;\n      }\n    }\n    if (lastLeftIndex >= 0) {\n      for (var _i = 0; _i <= lastLeftIndex; _i += 1) {\n        var _colFixed = flattenColumns[_i].fixed;\n        if (_colFixed !== 'left' && _colFixed !== true) {\n          return true;\n        }\n      }\n    }\n\n    // Fixed: right\n    var firstRightIndex = flattenColumns.findIndex(function (_ref3) {\n      var colFixed = _ref3.fixed;\n      return colFixed === 'right';\n    });\n    if (firstRightIndex >= 0) {\n      for (var _i2 = firstRightIndex; _i2 < flattenColumns.length; _i2 += 1) {\n        var _colFixed2 = flattenColumns[_i2].fixed;\n        if (_colFixed2 !== 'right') {\n          return true;\n        }\n      }\n    }\n    return false;\n  }, [flattenColumns]);\n\n  // ========================= FillWidth ========================\n  var _useWidthColumns = useWidthColumns(flattenColumns, scrollWidth, clientWidth),\n    _useWidthColumns2 = _slicedToArray(_useWidthColumns, 2),\n    filledColumns = _useWidthColumns2[0],\n    realScrollWidth = _useWidthColumns2[1];\n  return [mergedColumns, filledColumns, realScrollWidth, hasGapFixed];\n}\nexport default useColumns;", "map": {"version": 3, "names": ["_slicedToArray", "_defineProperty", "_toConsumableArray", "_typeof", "_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "toArray", "warning", "React", "EXPAND_COLUMN", "INTERNAL_COL_DEFINE", "useWidthColumns", "convertChildrenToColumns", "children", "filter", "node", "isValidElement", "map", "_ref", "key", "props", "nodeChildren", "restProps", "column", "filterHiddenColumns", "columns", "hidden", "subColumns", "length", "flatColumns", "parent<PERSON><PERSON>", "arguments", "undefined", "reduce", "list", "index", "fixed", "parsedFixed", "mergedKey", "concat", "subColum", "revertForRtl", "useColumns", "_ref2", "transformColumns", "prefixCls", "expandable", "expandedKeys", "columnTitle", "getRowKey", "onTriggerExpand", "expandIcon", "rowExpandable", "expandIconColumnIndex", "_ref2$expandedRowOffs", "expandedRowOffset", "direction", "expandRowByClick", "columnWidth", "scrollWidth", "clientWidth", "baseColumns", "useMemo", "newColumns", "slice", "withExpandColumns", "cloneColumns", "process", "env", "NODE_ENV", "includes", "expandColIndex", "splice", "c", "expandColumnIndex", "indexOf", "prevColumn", "fixedColumn", "expandColumn", "className", "columnType", "render", "_", "record", "<PERSON><PERSON><PERSON>", "expanded", "has", "recordExpandable", "icon", "onExpand", "createElement", "onClick", "e", "stopPropagation", "col", "mergedColumns", "finalColumns", "flattenColumns", "hasGapFixed", "lastLeftIndex", "i", "colFixed", "_i", "_colFixed", "firstRightIndex", "findIndex", "_ref3", "_i2", "_colFixed2", "_useWidthColumns", "_useWidthColumns2", "filledColumns", "realScrollWidth"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-table/es/hooks/useColumns/index.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"],\n  _excluded2 = [\"fixed\"];\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { EXPAND_COLUMN } from \"../../constant\";\nimport { INTERNAL_COL_DEFINE } from \"../../utils/legacyUtil\";\nimport useWidthColumns from \"./useWidthColumns\";\nexport function convertChildrenToColumns(children) {\n  return toArray(children).filter(function (node) {\n    return /*#__PURE__*/React.isValidElement(node);\n  }).map(function (_ref) {\n    var key = _ref.key,\n      props = _ref.props;\n    var nodeChildren = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var column = _objectSpread({\n      key: key\n    }, restProps);\n    if (nodeChildren) {\n      column.children = convertChildrenToColumns(nodeChildren);\n    }\n    return column;\n  });\n}\nfunction filterHiddenColumns(columns) {\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object' && !column.hidden;\n  }).map(function (column) {\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return _objectSpread(_objectSpread({}, column), {}, {\n        children: filterHiddenColumns(subColumns)\n      });\n    }\n    return column;\n  });\n}\nfunction flatColumns(columns) {\n  var parentKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key';\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object';\n  }).reduce(function (list, column, index) {\n    var fixed = column.fixed;\n    // Convert `fixed='true'` to `fixed='left'` instead\n    var parsedFixed = fixed === true ? 'left' : fixed;\n    var mergedKey = \"\".concat(parentKey, \"-\").concat(index);\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return [].concat(_toConsumableArray(list), _toConsumableArray(flatColumns(subColumns, mergedKey).map(function (subColum) {\n        return _objectSpread({\n          fixed: parsedFixed\n        }, subColum);\n      })));\n    }\n    return [].concat(_toConsumableArray(list), [_objectSpread(_objectSpread({\n      key: mergedKey\n    }, column), {}, {\n      fixed: parsedFixed\n    })]);\n  }, []);\n}\nfunction revertForRtl(columns) {\n  return columns.map(function (column) {\n    var fixed = column.fixed,\n      restProps = _objectWithoutProperties(column, _excluded2);\n\n    // Convert `fixed='left'` to `fixed='right'` instead\n    var parsedFixed = fixed;\n    if (fixed === 'left') {\n      parsedFixed = 'right';\n    } else if (fixed === 'right') {\n      parsedFixed = 'left';\n    }\n    return _objectSpread({\n      fixed: parsedFixed\n    }, restProps);\n  });\n}\n\n/**\n * Parse `columns` & `children` into `columns`.\n */\nfunction useColumns(_ref2, transformColumns) {\n  var prefixCls = _ref2.prefixCls,\n    columns = _ref2.columns,\n    children = _ref2.children,\n    expandable = _ref2.expandable,\n    expandedKeys = _ref2.expandedKeys,\n    columnTitle = _ref2.columnTitle,\n    getRowKey = _ref2.getRowKey,\n    onTriggerExpand = _ref2.onTriggerExpand,\n    expandIcon = _ref2.expandIcon,\n    rowExpandable = _ref2.rowExpandable,\n    expandIconColumnIndex = _ref2.expandIconColumnIndex,\n    _ref2$expandedRowOffs = _ref2.expandedRowOffset,\n    expandedRowOffset = _ref2$expandedRowOffs === void 0 ? 0 : _ref2$expandedRowOffs,\n    direction = _ref2.direction,\n    expandRowByClick = _ref2.expandRowByClick,\n    columnWidth = _ref2.columnWidth,\n    fixed = _ref2.fixed,\n    scrollWidth = _ref2.scrollWidth,\n    clientWidth = _ref2.clientWidth;\n  var baseColumns = React.useMemo(function () {\n    var newColumns = columns || convertChildrenToColumns(children) || [];\n    return filterHiddenColumns(newColumns.slice());\n  }, [columns, children]);\n\n  // ========================== Expand ==========================\n  var withExpandColumns = React.useMemo(function () {\n    if (expandable) {\n      var cloneColumns = baseColumns.slice();\n\n      // >>> Warning if use `expandIconColumnIndex`\n      if (process.env.NODE_ENV !== 'production' && expandIconColumnIndex >= 0) {\n        warning(false, '`expandIconColumnIndex` is deprecated. Please use `Table.EXPAND_COLUMN` in `columns` instead.');\n      }\n\n      // >>> Insert expand column if not exist\n      if (!cloneColumns.includes(EXPAND_COLUMN)) {\n        var expandColIndex = expandIconColumnIndex || 0;\n        if (expandColIndex >= 0 && (expandColIndex || fixed === 'left' || !fixed)) {\n          cloneColumns.splice(expandColIndex, 0, EXPAND_COLUMN);\n        }\n        if (fixed === 'right') {\n          cloneColumns.splice(baseColumns.length, 0, EXPAND_COLUMN);\n        }\n      }\n\n      // >>> Deduplicate additional expand column\n      if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (c) {\n        return c === EXPAND_COLUMN;\n      }).length > 1) {\n        warning(false, 'There exist more than one `EXPAND_COLUMN` in `columns`.');\n      }\n      var expandColumnIndex = cloneColumns.indexOf(EXPAND_COLUMN);\n      cloneColumns = cloneColumns.filter(function (column, index) {\n        return column !== EXPAND_COLUMN || index === expandColumnIndex;\n      });\n\n      // >>> Check if expand column need to fixed\n      var prevColumn = baseColumns[expandColumnIndex];\n      var fixedColumn;\n      if (fixed) {\n        fixedColumn = fixed;\n      } else {\n        fixedColumn = prevColumn ? prevColumn.fixed : null;\n      }\n\n      // >>> Create expandable column\n      var expandColumn = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, INTERNAL_COL_DEFINE, {\n        className: \"\".concat(prefixCls, \"-expand-icon-col\"),\n        columnType: 'EXPAND_COLUMN'\n      }), \"title\", columnTitle), \"fixed\", fixedColumn), \"className\", \"\".concat(prefixCls, \"-row-expand-icon-cell\")), \"width\", columnWidth), \"render\", function render(_, record, index) {\n        var rowKey = getRowKey(record, index);\n        var expanded = expandedKeys.has(rowKey);\n        var recordExpandable = rowExpandable ? rowExpandable(record) : true;\n        var icon = expandIcon({\n          prefixCls: prefixCls,\n          expanded: expanded,\n          expandable: recordExpandable,\n          record: record,\n          onExpand: onTriggerExpand\n        });\n        if (expandRowByClick) {\n          return /*#__PURE__*/React.createElement(\"span\", {\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            }\n          }, icon);\n        }\n        return icon;\n      });\n      return cloneColumns.map(function (col, index) {\n        var column = col === EXPAND_COLUMN ? expandColumn : col;\n        if (index < expandedRowOffset) {\n          return _objectSpread(_objectSpread({}, column), {}, {\n            fixed: column.fixed || 'left'\n          });\n        }\n        return column;\n      });\n    }\n    if (process.env.NODE_ENV !== 'production' && baseColumns.includes(EXPAND_COLUMN)) {\n      warning(false, '`expandable` is not config but there exist `EXPAND_COLUMN` in `columns`.');\n    }\n    return baseColumns.filter(function (col) {\n      return col !== EXPAND_COLUMN;\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [expandable, baseColumns, getRowKey, expandedKeys, expandIcon, direction, expandedRowOffset]);\n\n  // ========================= Transform ========================\n  var mergedColumns = React.useMemo(function () {\n    var finalColumns = withExpandColumns;\n    if (transformColumns) {\n      finalColumns = transformColumns(finalColumns);\n    }\n\n    // Always provides at least one column for table display\n    if (!finalColumns.length) {\n      finalColumns = [{\n        render: function render() {\n          return null;\n        }\n      }];\n    }\n    return finalColumns;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [transformColumns, withExpandColumns, direction]);\n\n  // ========================== Flatten =========================\n  var flattenColumns = React.useMemo(function () {\n    if (direction === 'rtl') {\n      return revertForRtl(flatColumns(mergedColumns));\n    }\n    return flatColumns(mergedColumns);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [mergedColumns, direction, scrollWidth]);\n\n  // ========================= Gap Fixed ========================\n  var hasGapFixed = React.useMemo(function () {\n    // Fixed: left, since old browser not support `findLastIndex`, we should use reverse loop\n    var lastLeftIndex = -1;\n    for (var i = flattenColumns.length - 1; i >= 0; i -= 1) {\n      var colFixed = flattenColumns[i].fixed;\n      if (colFixed === 'left' || colFixed === true) {\n        lastLeftIndex = i;\n        break;\n      }\n    }\n    if (lastLeftIndex >= 0) {\n      for (var _i = 0; _i <= lastLeftIndex; _i += 1) {\n        var _colFixed = flattenColumns[_i].fixed;\n        if (_colFixed !== 'left' && _colFixed !== true) {\n          return true;\n        }\n      }\n    }\n\n    // Fixed: right\n    var firstRightIndex = flattenColumns.findIndex(function (_ref3) {\n      var colFixed = _ref3.fixed;\n      return colFixed === 'right';\n    });\n    if (firstRightIndex >= 0) {\n      for (var _i2 = firstRightIndex; _i2 < flattenColumns.length; _i2 += 1) {\n        var _colFixed2 = flattenColumns[_i2].fixed;\n        if (_colFixed2 !== 'right') {\n          return true;\n        }\n      }\n    }\n    return false;\n  }, [flattenColumns]);\n\n  // ========================= FillWidth ========================\n  var _useWidthColumns = useWidthColumns(flattenColumns, scrollWidth, clientWidth),\n    _useWidthColumns2 = _slicedToArray(_useWidthColumns, 2),\n    filledColumns = _useWidthColumns2[0],\n    realScrollWidth = _useWidthColumns2[1];\n  return [mergedColumns, filledColumns, realScrollWidth, hasGapFixed];\n}\nexport default useColumns;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;EAC1BC,UAAU,GAAG,CAAC,OAAO,CAAC;AACxB,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAO,SAASC,wBAAwBA,CAACC,QAAQ,EAAE;EACjD,OAAOP,OAAO,CAACO,QAAQ,CAAC,CAACC,MAAM,CAAC,UAAUC,IAAI,EAAE;IAC9C,OAAO,aAAaP,KAAK,CAACQ,cAAc,CAACD,IAAI,CAAC;EAChD,CAAC,CAAC,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;IACrB,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;MAChBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACpB,IAAIC,YAAY,GAAGD,KAAK,CAACP,QAAQ;MAC/BS,SAAS,GAAGnB,wBAAwB,CAACiB,KAAK,EAAEhB,SAAS,CAAC;IACxD,IAAImB,MAAM,GAAGrB,aAAa,CAAC;MACzBiB,GAAG,EAAEA;IACP,CAAC,EAAEG,SAAS,CAAC;IACb,IAAID,YAAY,EAAE;MAChBE,MAAM,CAACV,QAAQ,GAAGD,wBAAwB,CAACS,YAAY,CAAC;IAC1D;IACA,OAAOE,MAAM;EACf,CAAC,CAAC;AACJ;AACA,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EACpC,OAAOA,OAAO,CAACX,MAAM,CAAC,UAAUS,MAAM,EAAE;IACtC,OAAOA,MAAM,IAAItB,OAAO,CAACsB,MAAM,CAAC,KAAK,QAAQ,IAAI,CAACA,MAAM,CAACG,MAAM;EACjE,CAAC,CAAC,CAACT,GAAG,CAAC,UAAUM,MAAM,EAAE;IACvB,IAAII,UAAU,GAAGJ,MAAM,CAACV,QAAQ;IAChC,IAAIc,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,OAAO1B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqB,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAClDV,QAAQ,EAAEW,mBAAmB,CAACG,UAAU;MAC1C,CAAC,CAAC;IACJ;IACA,OAAOJ,MAAM;EACf,CAAC,CAAC;AACJ;AACA,SAASM,WAAWA,CAACJ,OAAO,EAAE;EAC5B,IAAIK,SAAS,GAAGC,SAAS,CAACH,MAAM,GAAG,CAAC,IAAIG,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,OAAON,OAAO,CAACX,MAAM,CAAC,UAAUS,MAAM,EAAE;IACtC,OAAOA,MAAM,IAAItB,OAAO,CAACsB,MAAM,CAAC,KAAK,QAAQ;EAC/C,CAAC,CAAC,CAACU,MAAM,CAAC,UAAUC,IAAI,EAAEX,MAAM,EAAEY,KAAK,EAAE;IACvC,IAAIC,KAAK,GAAGb,MAAM,CAACa,KAAK;IACxB;IACA,IAAIC,WAAW,GAAGD,KAAK,KAAK,IAAI,GAAG,MAAM,GAAGA,KAAK;IACjD,IAAIE,SAAS,GAAG,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,GAAG,CAAC,CAACS,MAAM,CAACJ,KAAK,CAAC;IACvD,IAAIR,UAAU,GAAGJ,MAAM,CAACV,QAAQ;IAChC,IAAIc,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,OAAO,EAAE,CAACW,MAAM,CAACvC,kBAAkB,CAACkC,IAAI,CAAC,EAAElC,kBAAkB,CAAC6B,WAAW,CAACF,UAAU,EAAEW,SAAS,CAAC,CAACrB,GAAG,CAAC,UAAUuB,QAAQ,EAAE;QACvH,OAAOtC,aAAa,CAAC;UACnBkC,KAAK,EAAEC;QACT,CAAC,EAAEG,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,CAAC;IACN;IACA,OAAO,EAAE,CAACD,MAAM,CAACvC,kBAAkB,CAACkC,IAAI,CAAC,EAAE,CAAChC,aAAa,CAACA,aAAa,CAAC;MACtEiB,GAAG,EAAEmB;IACP,CAAC,EAAEf,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACda,KAAK,EAAEC;IACT,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;AACR;AACA,SAASI,YAAYA,CAAChB,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAACR,GAAG,CAAC,UAAUM,MAAM,EAAE;IACnC,IAAIa,KAAK,GAAGb,MAAM,CAACa,KAAK;MACtBd,SAAS,GAAGnB,wBAAwB,CAACoB,MAAM,EAAElB,UAAU,CAAC;;IAE1D;IACA,IAAIgC,WAAW,GAAGD,KAAK;IACvB,IAAIA,KAAK,KAAK,MAAM,EAAE;MACpBC,WAAW,GAAG,OAAO;IACvB,CAAC,MAAM,IAAID,KAAK,KAAK,OAAO,EAAE;MAC5BC,WAAW,GAAG,MAAM;IACtB;IACA,OAAOnC,aAAa,CAAC;MACnBkC,KAAK,EAAEC;IACT,CAAC,EAAEf,SAAS,CAAC;EACf,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAASoB,UAAUA,CAACC,KAAK,EAAEC,gBAAgB,EAAE;EAC3C,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BpB,OAAO,GAAGkB,KAAK,CAAClB,OAAO;IACvBZ,QAAQ,GAAG8B,KAAK,CAAC9B,QAAQ;IACzBiC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,eAAe,GAAGP,KAAK,CAACO,eAAe;IACvCC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,qBAAqB,GAAGV,KAAK,CAACU,qBAAqB;IACnDC,qBAAqB,GAAGX,KAAK,CAACY,iBAAiB;IAC/CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAChFE,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,gBAAgB,GAAGd,KAAK,CAACc,gBAAgB;IACzCC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BtB,KAAK,GAAGO,KAAK,CAACP,KAAK;IACnBuB,WAAW,GAAGhB,KAAK,CAACgB,WAAW;IAC/BC,WAAW,GAAGjB,KAAK,CAACiB,WAAW;EACjC,IAAIC,WAAW,GAAGrD,KAAK,CAACsD,OAAO,CAAC,YAAY;IAC1C,IAAIC,UAAU,GAAGtC,OAAO,IAAIb,wBAAwB,CAACC,QAAQ,CAAC,IAAI,EAAE;IACpE,OAAOW,mBAAmB,CAACuC,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC;EAChD,CAAC,EAAE,CAACvC,OAAO,EAAEZ,QAAQ,CAAC,CAAC;;EAEvB;EACA,IAAIoD,iBAAiB,GAAGzD,KAAK,CAACsD,OAAO,CAAC,YAAY;IAChD,IAAIhB,UAAU,EAAE;MACd,IAAIoB,YAAY,GAAGL,WAAW,CAACG,KAAK,CAAC,CAAC;;MAEtC;MACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIhB,qBAAqB,IAAI,CAAC,EAAE;QACvE9C,OAAO,CAAC,KAAK,EAAE,+FAA+F,CAAC;MACjH;;MAEA;MACA,IAAI,CAAC2D,YAAY,CAACI,QAAQ,CAAC7D,aAAa,CAAC,EAAE;QACzC,IAAI8D,cAAc,GAAGlB,qBAAqB,IAAI,CAAC;QAC/C,IAAIkB,cAAc,IAAI,CAAC,KAAKA,cAAc,IAAInC,KAAK,KAAK,MAAM,IAAI,CAACA,KAAK,CAAC,EAAE;UACzE8B,YAAY,CAACM,MAAM,CAACD,cAAc,EAAE,CAAC,EAAE9D,aAAa,CAAC;QACvD;QACA,IAAI2B,KAAK,KAAK,OAAO,EAAE;UACrB8B,YAAY,CAACM,MAAM,CAACX,WAAW,CAACjC,MAAM,EAAE,CAAC,EAAEnB,aAAa,CAAC;QAC3D;MACF;;MAEA;MACA,IAAI0D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIH,YAAY,CAACpD,MAAM,CAAC,UAAU2D,CAAC,EAAE;QAC5E,OAAOA,CAAC,KAAKhE,aAAa;MAC5B,CAAC,CAAC,CAACmB,MAAM,GAAG,CAAC,EAAE;QACbrB,OAAO,CAAC,KAAK,EAAE,yDAAyD,CAAC;MAC3E;MACA,IAAImE,iBAAiB,GAAGR,YAAY,CAACS,OAAO,CAAClE,aAAa,CAAC;MAC3DyD,YAAY,GAAGA,YAAY,CAACpD,MAAM,CAAC,UAAUS,MAAM,EAAEY,KAAK,EAAE;QAC1D,OAAOZ,MAAM,KAAKd,aAAa,IAAI0B,KAAK,KAAKuC,iBAAiB;MAChE,CAAC,CAAC;;MAEF;MACA,IAAIE,UAAU,GAAGf,WAAW,CAACa,iBAAiB,CAAC;MAC/C,IAAIG,WAAW;MACf,IAAIzC,KAAK,EAAE;QACTyC,WAAW,GAAGzC,KAAK;MACrB,CAAC,MAAM;QACLyC,WAAW,GAAGD,UAAU,GAAGA,UAAU,CAACxC,KAAK,GAAG,IAAI;MACpD;;MAEA;MACA,IAAI0C,YAAY,GAAG/E,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEW,mBAAmB,EAAE;QAC1IqE,SAAS,EAAE,EAAE,CAACxC,MAAM,CAACM,SAAS,EAAE,kBAAkB,CAAC;QACnDmC,UAAU,EAAE;MACd,CAAC,CAAC,EAAE,OAAO,EAAEhC,WAAW,CAAC,EAAE,OAAO,EAAE6B,WAAW,CAAC,EAAE,WAAW,EAAE,EAAE,CAACtC,MAAM,CAACM,SAAS,EAAE,uBAAuB,CAAC,CAAC,EAAE,OAAO,EAAEa,WAAW,CAAC,EAAE,QAAQ,EAAE,SAASuB,MAAMA,CAACC,CAAC,EAAEC,MAAM,EAAEhD,KAAK,EAAE;QAChL,IAAIiD,MAAM,GAAGnC,SAAS,CAACkC,MAAM,EAAEhD,KAAK,CAAC;QACrC,IAAIkD,QAAQ,GAAGtC,YAAY,CAACuC,GAAG,CAACF,MAAM,CAAC;QACvC,IAAIG,gBAAgB,GAAGnC,aAAa,GAAGA,aAAa,CAAC+B,MAAM,CAAC,GAAG,IAAI;QACnE,IAAIK,IAAI,GAAGrC,UAAU,CAAC;UACpBN,SAAS,EAAEA,SAAS;UACpBwC,QAAQ,EAAEA,QAAQ;UAClBvC,UAAU,EAAEyC,gBAAgB;UAC5BJ,MAAM,EAAEA,MAAM;UACdM,QAAQ,EAAEvC;QACZ,CAAC,CAAC;QACF,IAAIO,gBAAgB,EAAE;UACpB,OAAO,aAAajD,KAAK,CAACkF,aAAa,CAAC,MAAM,EAAE;YAC9CC,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;cAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;YAC5B;UACF,CAAC,EAAEL,IAAI,CAAC;QACV;QACA,OAAOA,IAAI;MACb,CAAC,CAAC;MACF,OAAOtB,YAAY,CAACjD,GAAG,CAAC,UAAU6E,GAAG,EAAE3D,KAAK,EAAE;QAC5C,IAAIZ,MAAM,GAAGuE,GAAG,KAAKrF,aAAa,GAAGqE,YAAY,GAAGgB,GAAG;QACvD,IAAI3D,KAAK,GAAGoB,iBAAiB,EAAE;UAC7B,OAAOrD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqB,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YAClDa,KAAK,EAAEb,MAAM,CAACa,KAAK,IAAI;UACzB,CAAC,CAAC;QACJ;QACA,OAAOb,MAAM;MACf,CAAC,CAAC;IACJ;IACA,IAAI4C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIR,WAAW,CAACS,QAAQ,CAAC7D,aAAa,CAAC,EAAE;MAChFF,OAAO,CAAC,KAAK,EAAE,0EAA0E,CAAC;IAC5F;IACA,OAAOsD,WAAW,CAAC/C,MAAM,CAAC,UAAUgF,GAAG,EAAE;MACvC,OAAOA,GAAG,KAAKrF,aAAa;IAC9B,CAAC,CAAC;IACF;EACF,CAAC,EAAE,CAACqC,UAAU,EAAEe,WAAW,EAAEZ,SAAS,EAAEF,YAAY,EAAEI,UAAU,EAAEK,SAAS,EAAED,iBAAiB,CAAC,CAAC;;EAEhG;EACA,IAAIwC,aAAa,GAAGvF,KAAK,CAACsD,OAAO,CAAC,YAAY;IAC5C,IAAIkC,YAAY,GAAG/B,iBAAiB;IACpC,IAAIrB,gBAAgB,EAAE;MACpBoD,YAAY,GAAGpD,gBAAgB,CAACoD,YAAY,CAAC;IAC/C;;IAEA;IACA,IAAI,CAACA,YAAY,CAACpE,MAAM,EAAE;MACxBoE,YAAY,GAAG,CAAC;QACdf,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;UACxB,OAAO,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IACA,OAAOe,YAAY;IACnB;EACF,CAAC,EAAE,CAACpD,gBAAgB,EAAEqB,iBAAiB,EAAET,SAAS,CAAC,CAAC;;EAEpD;EACA,IAAIyC,cAAc,GAAGzF,KAAK,CAACsD,OAAO,CAAC,YAAY;IAC7C,IAAIN,SAAS,KAAK,KAAK,EAAE;MACvB,OAAOf,YAAY,CAACZ,WAAW,CAACkE,aAAa,CAAC,CAAC;IACjD;IACA,OAAOlE,WAAW,CAACkE,aAAa,CAAC;IACjC;EACF,CAAC,EAAE,CAACA,aAAa,EAAEvC,SAAS,EAAEG,WAAW,CAAC,CAAC;;EAE3C;EACA,IAAIuC,WAAW,GAAG1F,KAAK,CAACsD,OAAO,CAAC,YAAY;IAC1C;IACA,IAAIqC,aAAa,GAAG,CAAC,CAAC;IACtB,KAAK,IAAIC,CAAC,GAAGH,cAAc,CAACrE,MAAM,GAAG,CAAC,EAAEwE,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACtD,IAAIC,QAAQ,GAAGJ,cAAc,CAACG,CAAC,CAAC,CAAChE,KAAK;MACtC,IAAIiE,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,IAAI,EAAE;QAC5CF,aAAa,GAAGC,CAAC;QACjB;MACF;IACF;IACA,IAAID,aAAa,IAAI,CAAC,EAAE;MACtB,KAAK,IAAIG,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAIH,aAAa,EAAEG,EAAE,IAAI,CAAC,EAAE;QAC7C,IAAIC,SAAS,GAAGN,cAAc,CAACK,EAAE,CAAC,CAAClE,KAAK;QACxC,IAAImE,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,IAAI,EAAE;UAC9C,OAAO,IAAI;QACb;MACF;IACF;;IAEA;IACA,IAAIC,eAAe,GAAGP,cAAc,CAACQ,SAAS,CAAC,UAAUC,KAAK,EAAE;MAC9D,IAAIL,QAAQ,GAAGK,KAAK,CAACtE,KAAK;MAC1B,OAAOiE,QAAQ,KAAK,OAAO;IAC7B,CAAC,CAAC;IACF,IAAIG,eAAe,IAAI,CAAC,EAAE;MACxB,KAAK,IAAIG,GAAG,GAAGH,eAAe,EAAEG,GAAG,GAAGV,cAAc,CAACrE,MAAM,EAAE+E,GAAG,IAAI,CAAC,EAAE;QACrE,IAAIC,UAAU,GAAGX,cAAc,CAACU,GAAG,CAAC,CAACvE,KAAK;QAC1C,IAAIwE,UAAU,KAAK,OAAO,EAAE;UAC1B,OAAO,IAAI;QACb;MACF;IACF;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAACX,cAAc,CAAC,CAAC;;EAEpB;EACA,IAAIY,gBAAgB,GAAGlG,eAAe,CAACsF,cAAc,EAAEtC,WAAW,EAAEC,WAAW,CAAC;IAC9EkD,iBAAiB,GAAGhH,cAAc,CAAC+G,gBAAgB,EAAE,CAAC,CAAC;IACvDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACxC,OAAO,CAACf,aAAa,EAAEgB,aAAa,EAAEC,eAAe,EAAEd,WAAW,CAAC;AACrE;AACA,eAAexD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
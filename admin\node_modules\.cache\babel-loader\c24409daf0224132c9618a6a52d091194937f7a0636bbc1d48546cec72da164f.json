{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useForm as useRcForm } from 'rc-field-form';\nimport { getDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport scrollIntoView from 'scroll-into-view-if-needed';\nimport { getFieldId, toArray } from '../util';\nfunction toNamePathStr(name) {\n  const namePath = toArray(name);\n  return namePath.join('_');\n}\nfunction getFieldDOMNode(name, wrapForm) {\n  const field = wrapForm.getFieldInstance(name);\n  const fieldDom = getDOM(field);\n  if (fieldDom) {\n    return fieldDom;\n  }\n  const fieldId = getFieldId(toArray(name), wrapForm.__INTERNAL__.name);\n  if (fieldId) {\n    return document.getElementById(fieldId);\n  }\n}\nexport default function useForm(form) {\n  const [rcForm] = useRcForm();\n  const itemsRef = React.useRef({});\n  const wrapForm = React.useMemo(() => form !== null && form !== void 0 ? form : Object.assign(Object.assign({}, rcForm), {\n    __INTERNAL__: {\n      itemRef: name => node => {\n        const namePathStr = toNamePathStr(name);\n        if (node) {\n          itemsRef.current[namePathStr] = node;\n        } else {\n          delete itemsRef.current[namePathStr];\n        }\n      }\n    },\n    scrollToField: function (name) {\n      let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      const {\n          focus\n        } = options,\n        restOpt = __rest(options, [\"focus\"]);\n      const node = getFieldDOMNode(name, wrapForm);\n      if (node) {\n        scrollIntoView(node, Object.assign({\n          scrollMode: 'if-needed',\n          block: 'nearest'\n        }, restOpt));\n        // Focus if scroll success\n        if (focus) {\n          wrapForm.focusField(name);\n        }\n      }\n    },\n    focusField: name => {\n      var _a, _b;\n      const itemRef = wrapForm.getFieldInstance(name);\n      if (typeof (itemRef === null || itemRef === void 0 ? void 0 : itemRef.focus) === 'function') {\n        itemRef.focus();\n      } else {\n        (_b = (_a = getFieldDOMNode(name, wrapForm)) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);\n      }\n    },\n    getFieldInstance: name => {\n      const namePathStr = toNamePathStr(name);\n      return itemsRef.current[namePathStr];\n    }\n  }), [form, rcForm]);\n  return [wrapForm];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
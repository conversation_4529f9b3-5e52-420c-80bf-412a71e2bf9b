{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar Indent = function Indent(_ref) {\n  var prefixCls = _ref.prefixCls,\n    level = _ref.level,\n    isStart = _ref.isStart,\n    isEnd = _ref.isEnd;\n  var baseClassName = \"\".concat(prefixCls, \"-indent-unit\");\n  var list = [];\n  for (var i = 0; i < level; i += 1) {\n    list.push(/*#__PURE__*/React.createElement(\"span\", {\n      key: i,\n      className: classNames(baseClassName, _defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-start\"), isStart[i]), \"\".concat(baseClassName, \"-end\"), isEnd[i]))\n    }));\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, list);\n};\nexport default /*#__PURE__*/React.memo(Indent);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
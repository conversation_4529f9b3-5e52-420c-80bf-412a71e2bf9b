{"ast": null, "code": "import { isValidElement } from 'react';\nfunction convertToTooltipProps(tooltip) {\n  // isNil\n  if (tooltip === undefined || tooltip === null) {\n    return null;\n  }\n  if (typeof tooltip === 'object' && ! /*#__PURE__*/isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nexport default convertToTooltipProps;", "map": {"version": 3, "names": ["isValidElement", "convertToTooltipProps", "tooltip", "undefined", "title"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/_util/convertToTooltipProps.js"], "sourcesContent": ["import { isValidElement } from 'react';\nfunction convertToTooltipProps(tooltip) {\n  // isNil\n  if (tooltip === undefined || tooltip === null) {\n    return null;\n  }\n  if (typeof tooltip === 'object' && ! /*#__PURE__*/isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nexport default convertToTooltipProps;"], "mappings": "AAAA,SAASA,cAAc,QAAQ,OAAO;AACtC,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACtC;EACA,IAAIA,OAAO,KAAKC,SAAS,IAAID,OAAO,KAAK,IAAI,EAAE;IAC7C,OAAO,IAAI;EACb;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,EAAE,aAAaF,cAAc,CAACE,OAAO,CAAC,EAAE;IACzE,OAAOA,OAAO;EAChB;EACA,OAAO;IACLE,KAAK,EAAEF;EACT,CAAC;AACH;AACA,eAAeD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
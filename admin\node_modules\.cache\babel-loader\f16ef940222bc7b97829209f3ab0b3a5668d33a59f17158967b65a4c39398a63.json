{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, Form, Input, Select, DatePicker, Row, Col, Statistic, message, Modal, Descriptions } from 'antd';\nimport { SearchOutlined, EyeOutlined, EditOutlined, ExportOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { orderApi } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\n\n// 使用从 types/order.ts 导入的类型，这里定义一个本地接口用于显示\n\n// 订单数据将从API获取，不再使用模拟数据\n\nconst OrderList = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState([]);\n  const [filteredOrders, setFilteredOrders] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    processing: 0,\n    pending_upload: 0,\n    shipped: 0,\n    activated: 0,\n    failed: 0,\n    cancelled: 0,\n    today_orders: 0,\n    week_orders: 0,\n    month_orders: 0\n  });\n  const navigate = useNavigate();\n\n  // 组件加载时获取订单数据\n  useEffect(() => {\n    loadOrders();\n    loadStats();\n  }, []);\n\n  // 转换API数据格式为组件使用的格式\n  const transformOrderData = apiOrder => {\n    return {\n      id: apiOrder.id,\n      orderNo: apiOrder.order_no,\n      customerName: apiOrder.customer_name,\n      customerPhone: apiOrder.customer_phone,\n      customerIdCard: apiOrder.customer_id_card,\n      productName: apiOrder.product_name,\n      operator: apiOrder.operator,\n      deliveryAddress: apiOrder.delivery_address,\n      status: apiOrder.status,\n      priority: apiOrder.priority,\n      logisticsCompany: apiOrder.logistics_company,\n      trackingNumber: apiOrder.tracking_number,\n      shippedAt: apiOrder.shipped_at,\n      estimatedDelivery: apiOrder.estimated_delivery,\n      createdAt: apiOrder.created_at,\n      updatedAt: apiOrder.updated_at\n    };\n  };\n\n  // 加载订单数据\n  const loadOrders = async params => {\n    setLoading(true);\n    try {\n      var _response$data, _response$data2, _response$data3;\n      const queryParams = {\n        page: pagination.current,\n        per_page: pagination.pageSize,\n        ...params\n      };\n      const response = await orderApi.getList(queryParams);\n      console.log('API响应数据:', response); // 调试信息\n\n      // 检查响应数据结构\n      const orderList = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.list) || ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.data) || [];\n      const pagination = ((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.pagination) || response.data;\n      const transformedOrders = orderList.map(transformOrderData);\n      setOrders(transformedOrders);\n      setFilteredOrders(transformedOrders);\n      setPagination(prev => ({\n        ...prev,\n        total: (pagination === null || pagination === void 0 ? void 0 : pagination.total) || 0,\n        current: (pagination === null || pagination === void 0 ? void 0 : pagination.current_page) || 1\n      }));\n    } catch (error) {\n      console.error('获取订单数据失败:', error);\n      message.error(`获取订单数据失败: ${error instanceof Error ? error.message : '未知错误'}`);\n      // 如果API失败，设置为空数组\n      setOrders([]);\n      setFilteredOrders([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载统计数据\n  const loadStats = async () => {\n    try {\n      const response = await orderApi.getStats();\n      setStats(response.data);\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      // 如果API失败，使用本地计算的统计数据\n    }\n  };\n\n  // 状态颜色映射\n  const getStatusColor = status => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      pending_upload: 'purple',\n      shipped: 'cyan',\n      activated: 'green',\n      failed: 'red',\n      cancelled: 'default'\n    };\n    return colors[status] || 'default';\n  };\n\n  // 状态文本映射\n  const getStatusText = status => {\n    const texts = {\n      pending: '待处理',\n      processing: '开卡中',\n      pending_upload: '待上传三证',\n      shipped: '已发货',\n      activated: '已激活',\n      failed: '开卡失败',\n      cancelled: '已取消'\n    };\n    return texts[status] || status;\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = priority => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red'\n    };\n    return colors[priority] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = priority => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急'\n    };\n    return texts[priority] || '普通';\n  };\n\n  // 如果API统计数据不可用，使用本地计算的统计数据作为备用\n  const localStats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    pending_upload: filteredOrders.filter(order => order.status === 'pending_upload').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    failed: filteredOrders.filter(order => order.status === 'failed').length,\n    cancelled: filteredOrders.filter(order => order.status === 'cancelled').length\n  };\n\n  // 使用API统计数据，如果不可用则使用本地计算的数据\n  const displayStats = stats.total > 0 ? stats : localStats;\n\n  // 表格列定义\n  const columns = [{\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '订单状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物流信息',\n    key: 'logistics',\n    width: 200,\n    render: (_, record) => {\n      if (record.status === 'shipped' || record.status === 'activated') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 600,\n              fontSize: '12px'\n            },\n            children: record.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#666'\n            },\n            children: record.trackingNumber || '暂无快递单号'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this), record.shippedAt && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#999'\n            },\n            children: [\"\\u53D1\\u8D27: \", record.shippedAt.split(' ')[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: \"\\u672A\\u53D1\\u8D27\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditOrder(record.id),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleSearch = values => {\n    const searchParams = {\n      order_no: values.orderNo,\n      customer_name: values.customerName,\n      operator: values.operator,\n      status: values.status\n    };\n\n    // 重置分页到第一页\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n    loadOrders(searchParams);\n  };\n  const handleRefresh = async () => {\n    await loadOrders();\n    await loadStats();\n    message.success('数据已刷新');\n  };\n  const handleViewOrder = order => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n  const handleEditOrder = id => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#1890ff'\n        },\n        children: \"\\u5168\\u90E8\\u8BA2\\u5355\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u7BA1\\u7406\\u6240\\u6709\\u8BA2\\u5355\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA2\\u5355\\u6570\",\n            value: displayStats.total,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\",\n            value: displayStats.pending,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\",\n            value: displayStats.pending_upload,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F00\\u5361\\u4E2D\",\n            value: displayStats.processing,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u53D1\\u8D27\",\n            value: displayStats.shipped,\n            valueStyle: {\n              color: '#13c2c2'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u6FC0\\u6D3B\",\n            value: displayStats.activated,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"orderNo\",\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",\n            style: {\n              width: 150\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"customerName\",\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",\n            style: {\n              width: 120\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"operator\",\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u79FB\\u52A8\",\n              children: \"\\u4E2D\\u56FD\\u79FB\\u52A8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u7535\\u4FE1\",\n              children: \"\\u4E2D\\u56FD\\u7535\\u4FE1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u8054\\u901A\",\n              children: \"\\u4E2D\\u56FD\\u8054\\u901A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u5E7F\\u7535\",\n              children: \"\\u4E2D\\u56FD\\u5E7F\\u7535\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending\",\n              children: \"\\u5F85\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"processing\",\n              children: \"\\u5F00\\u5361\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending_upload\",\n              children: \"\\u5F85\\u4E0A\\u4F20\\u4E09\\u8BC1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"shipped\",\n              children: \"\\u5DF2\\u53D1\\u8D27\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"activated\",\n              children: \"\\u5DF2\\u6FC0\\u6D3B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"failed\",\n              children: \"\\u5F00\\u5361\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"cancelled\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 60\n            }, this),\n            children: \"\\u641C\\u7D22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", filteredOrders.length, \" \\u6761\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleExport,\n            icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 50\n            }, this),\n            children: \"\\u5BFC\\u51FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredOrders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          current: pagination.current,\n          pageSize: pagination.pageSize,\n          total: pagination.total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setPagination(prev => ({\n              ...prev,\n              current: page,\n              pageSize: pageSize || 10\n            }));\n            loadOrders();\n          }\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BA2\\u5355\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          span: 2,\n          children: selectedOrder.orderNo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: selectedOrder.customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8054\\u7CFB\\u7535\\u8BDD\",\n          children: selectedOrder.customerPhone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n          span: 2,\n          children: selectedOrder.productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: selectedOrder.operator\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(selectedOrder.status),\n            children: getStatusText(selectedOrder.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 13\n        }, this), (selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7269\\u6D41\\u516C\\u53F8\",\n            children: selectedOrder.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5FEB\\u9012\\u5355\\u53F7\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              code: true,\n              children: selectedOrder.trackingNumber || '暂无'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u53D1\\u8D27\\u65F6\\u95F4\",\n            children: selectedOrder.shippedAt || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u9884\\u8BA1\\u9001\\u8FBE\",\n            children: selectedOrder.estimatedDelivery || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: selectedOrder.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: selectedOrder.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 381,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderList, \"C4aHSdeOMyHo5roTHgJQrRzODU0=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = OrderList;\nexport default OrderList;\nvar _c;\n$RefreshReg$(_c, \"OrderList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Form", "Input", "Select", "DatePicker", "Row", "Col", "Statistic", "message", "Modal", "Descriptions", "SearchOutlined", "EyeOutlined", "EditOutlined", "ExportOutlined", "ReloadOutlined", "useNavigate", "orderApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "RangePicker", "OrderList", "_s", "form", "useForm", "orders", "setOrders", "filteredOrders", "setFilteredOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "detailModalVisible", "setDetailModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "pagination", "setPagination", "current", "pageSize", "total", "stats", "setStats", "pending", "processing", "pending_upload", "shipped", "activated", "failed", "cancelled", "today_orders", "week_orders", "month_orders", "navigate", "loadOrders", "loadStats", "transformOrderData", "apiOrder", "id", "orderNo", "order_no", "customerName", "customer_name", "customerPhone", "customer_phone", "customerIdCard", "customer_id_card", "productName", "product_name", "operator", "deliveryAddress", "delivery_address", "status", "priority", "logisticsCompany", "logistics_company", "trackingNumber", "tracking_number", "shippedAt", "shipped_at", "estimatedDelivery", "estimated_delivery", "createdAt", "created_at", "updatedAt", "updated_at", "params", "_response$data", "_response$data2", "_response$data3", "queryParams", "page", "per_page", "response", "getList", "console", "log", "orderList", "data", "list", "transformedOrders", "map", "prev", "current_page", "error", "Error", "getStats", "getStatusColor", "colors", "getStatusText", "texts", "getPriorityColor", "low", "normal", "high", "urgent", "getPriorityText", "localStats", "length", "filter", "order", "displayStats", "columns", "title", "dataIndex", "key", "width", "render", "text", "code", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "record", "fontWeight", "color", "ellipsis", "split", "type", "size", "icon", "onClick", "handleViewOrder", "handleEditOrder", "handleSearch", "values", "searchParams", "handleRefresh", "success", "info", "handleExport", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "placeholder", "Option", "htmlType", "display", "justifyContent", "alignItems", "strong", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "rowSelection", "open", "onCancel", "footer", "column", "bordered", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Modal,\n  Descriptions,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  FilterOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { useNavigate } from 'react-router-dom';\nimport { orderApi } from '../services/api';\nimport type { Order as OrderType, OrderListParams, OrderStats } from '../types/order';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\n// 使用从 types/order.ts 导入的类型，这里定义一个本地接口用于显示\ninterface Order {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  customerIdCard: string;\n  productName: string;\n  operator: string;\n  deliveryAddress: string;\n  status: 'pending' | 'processing' | 'pending_upload' | 'shipped' | 'activated' | 'cancelled' | 'failed';\n  priority?: 'low' | 'normal' | 'high' | 'urgent';\n  logisticsCompany?: string; // 物流公司\n  trackingNumber?: string; // 快递单号\n  shippedAt?: string; // 发货时间\n  estimatedDelivery?: string; // 预计送达时间\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 订单数据将从API获取，不再使用模拟数据\n\nconst OrderList: React.FC = () => {\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0,\n  });\n  const [stats, setStats] = useState<OrderStats>({\n    total: 0,\n    pending: 0,\n    processing: 0,\n    pending_upload: 0,\n    shipped: 0,\n    activated: 0,\n    failed: 0,\n    cancelled: 0,\n    today_orders: 0,\n    week_orders: 0,\n    month_orders: 0,\n  });\n  const navigate = useNavigate();\n\n  // 组件加载时获取订单数据\n  useEffect(() => {\n    loadOrders();\n    loadStats();\n  }, []);\n\n  // 转换API数据格式为组件使用的格式\n  const transformOrderData = (apiOrder: OrderType): Order => {\n    return {\n      id: apiOrder.id,\n      orderNo: apiOrder.order_no,\n      customerName: apiOrder.customer_name,\n      customerPhone: apiOrder.customer_phone,\n      customerIdCard: apiOrder.customer_id_card,\n      productName: apiOrder.product_name,\n      operator: apiOrder.operator,\n      deliveryAddress: apiOrder.delivery_address,\n      status: apiOrder.status,\n      priority: apiOrder.priority,\n      logisticsCompany: apiOrder.logistics_company,\n      trackingNumber: apiOrder.tracking_number,\n      shippedAt: apiOrder.shipped_at,\n      estimatedDelivery: apiOrder.estimated_delivery,\n      createdAt: apiOrder.created_at,\n      updatedAt: apiOrder.updated_at,\n    };\n  };\n\n  // 加载订单数据\n  const loadOrders = async (params?: OrderListParams) => {\n    setLoading(true);\n    try {\n      const queryParams = {\n        page: pagination.current,\n        per_page: pagination.pageSize,\n        ...params,\n      };\n\n      const response = await orderApi.getList(queryParams);\n      console.log('API响应数据:', response); // 调试信息\n\n      // 检查响应数据结构\n      const orderList = response.data?.list || response.data?.data || [];\n      const pagination = response.data?.pagination || response.data;\n\n      const transformedOrders = orderList.map(transformOrderData);\n\n      setOrders(transformedOrders);\n      setFilteredOrders(transformedOrders);\n      setPagination(prev => ({\n        ...prev,\n        total: pagination?.total || 0,\n        current: pagination?.current_page || 1,\n      }));\n    } catch (error) {\n      console.error('获取订单数据失败:', error);\n      message.error(`获取订单数据失败: ${error instanceof Error ? error.message : '未知错误'}`);\n      // 如果API失败，设置为空数组\n      setOrders([]);\n      setFilteredOrders([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载统计数据\n  const loadStats = async () => {\n    try {\n      const response = await orderApi.getStats();\n      setStats(response.data);\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      // 如果API失败，使用本地计算的统计数据\n    }\n  };\n\n  // 状态颜色映射\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      pending_upload: 'purple',\n      shipped: 'cyan',\n      activated: 'green',\n      failed: 'red',\n      cancelled: 'default',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n\n\n  // 状态文本映射\n  const getStatusText = (status: string) => {\n    const texts = {\n      pending: '待处理',\n      processing: '开卡中',\n      pending_upload: '待上传三证',\n      shipped: '已发货',\n      activated: '已激活',\n      failed: '开卡失败',\n      cancelled: '已取消',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  // 优先级颜色映射\n  const getPriorityColor = (priority?: string) => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red',\n    };\n    return colors[priority as keyof typeof colors] || 'default';\n  };\n\n  // 优先级文本映射\n  const getPriorityText = (priority?: string) => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急',\n    };\n    return texts[priority as keyof typeof texts] || '普通';\n  };\n\n\n\n  // 如果API统计数据不可用，使用本地计算的统计数据作为备用\n  const localStats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    pending_upload: filteredOrders.filter(order => order.status === 'pending_upload').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    failed: filteredOrders.filter(order => order.status === 'failed').length,\n    cancelled: filteredOrders.filter(order => order.status === 'cancelled').length,\n  };\n\n  // 使用API统计数据，如果不可用则使用本地计算的数据\n  const displayStats = stats.total > 0 ? stats : localStats;\n\n  // 表格列定义\n  const columns: ColumnsType<Order> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n    {\n      title: '订单状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '物流信息',\n      key: 'logistics',\n      width: 200,\n      render: (_, record) => {\n        if (record.status === 'shipped' || record.status === 'activated') {\n          return (\n            <div>\n              <div style={{ fontWeight: 600, fontSize: '12px' }}>\n                {record.logisticsCompany || '暂无'}\n              </div>\n              <div style={{ fontSize: '11px', color: '#666' }}>\n                {record.trackingNumber || '暂无快递单号'}\n              </div>\n              {record.shippedAt && (\n                <div style={{ fontSize: '11px', color: '#999' }}>\n                  发货: {record.shippedAt.split(' ')[0]}\n                </div>\n              )}\n            </div>\n          );\n        }\n        return <Text type=\"secondary\" style={{ fontSize: '12px' }}>未发货</Text>;\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditOrder(record.id)}\n          >\n            编辑\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleSearch = (values: any) => {\n    const searchParams: OrderListParams = {\n      order_no: values.orderNo,\n      customer_name: values.customerName,\n      operator: values.operator,\n      status: values.status,\n    };\n\n    // 重置分页到第一页\n    setPagination(prev => ({ ...prev, current: 1 }));\n    loadOrders(searchParams);\n  };\n\n  const handleRefresh = async () => {\n    await loadOrders();\n    await loadStats();\n    message.success('数据已刷新');\n  };\n\n  const handleViewOrder = (order: Order) => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n\n  const handleEditOrder = (id: number) => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n          全部订单\n        </Title>\n        <Text type=\"secondary\">\n          管理所有订单信息\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={displayStats.total}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"待处理\"\n              value={displayStats.pending}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"待上传三证\"\n              value={displayStats.pending_upload}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"开卡中\"\n              value={displayStats.processing}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"已发货\"\n              value={displayStats.shipped}\n              valueStyle={{ color: '#13c2c2' }}\n            />\n          </Card>\n        </Col>\n        <Col span={4}>\n          <Card>\n            <Statistic\n              title=\"已激活\"\n              value={displayStats.activated}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 搜索表单 */}\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ marginBottom: 16 }}\n        >\n          <Form.Item name=\"orderNo\" label=\"订单号\">\n            <Input placeholder=\"请输入订单号\" style={{ width: 150 }} />\n          </Form.Item>\n          <Form.Item name=\"customerName\" label=\"客户姓名\">\n            <Input placeholder=\"请输入客户姓名\" style={{ width: 120 }} />\n          </Form.Item>\n          <Form.Item name=\"operator\" label=\"运营商\">\n            <Select placeholder=\"请选择运营商\" style={{ width: 120 }}>\n              <Select.Option value=\"中国移动\">中国移动</Select.Option>\n              <Select.Option value=\"中国电信\">中国电信</Select.Option>\n              <Select.Option value=\"中国联通\">中国联通</Select.Option>\n              <Select.Option value=\"中国广电\">中国广电</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"status\" label=\"订单状态\">\n            <Select placeholder=\"请选择状态\" style={{ width: 120 }}>\n              <Select.Option value=\"pending\">待处理</Select.Option>\n              <Select.Option value=\"processing\">开卡中</Select.Option>\n              <Select.Option value=\"pending_upload\">待上传三证</Select.Option>\n              <Select.Option value=\"shipped\">已发货</Select.Option>\n              <Select.Option value=\"activated\">已激活</Select.Option>\n              <Select.Option value=\"failed\">开卡失败</Select.Option>\n              <Select.Option value=\"cancelled\">已取消</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item>\n            <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n              搜索\n            </Button>\n          </Form.Item>\n        </Form>\n\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {filteredOrders.length} 条订单\n            </Text>\n          </div>\n          <Space>\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n            <Button onClick={handleExport} icon={<ExportOutlined />}>\n              导出\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={filteredOrders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || 10 }));\n              loadOrders();\n            },\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 订单详情弹窗 */}\n      <Modal\n        title=\"订单详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={600}\n      >\n        {selectedOrder && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"订单号\" span={2}>\n              {selectedOrder.orderNo}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"客户姓名\">\n              {selectedOrder.customerName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"联系电话\">\n              {selectedOrder.customerPhone}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"产品名称\" span={2}>\n              {selectedOrder.productName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"运营商\">\n              <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"订单状态\">\n              <Tag color={getStatusColor(selectedOrder.status)}>\n                {getStatusText(selectedOrder.status)}\n              </Tag>\n            </Descriptions.Item>\n            {(selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && (\n              <>\n                <Descriptions.Item label=\"物流公司\">\n                  {selectedOrder.logisticsCompany || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"快递单号\">\n                  <Text code>{selectedOrder.trackingNumber || '暂无'}</Text>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"发货时间\">\n                  {selectedOrder.shippedAt || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"预计送达\">\n                  {selectedOrder.estimatedDelivery || '暂无'}\n                </Descriptions.Item>\n              </>\n            )}\n            <Descriptions.Item label=\"创建时间\">\n              {selectedOrder.createdAt}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"更新时间\">\n              {selectedOrder.updatedAt}\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,QACP,MAAM;AACb,SACEC,cAAc,EACdC,WAAW,EACXC,YAAY,EAEZC,cAAc,EACdC,cAAc,QAET,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG3C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGxB,UAAU;AAClC,MAAM;EAAEyB;AAAY,CAAC,GAAGpB,UAAU;;AAElC;;AAoBA;;AAEA,MAAMqB,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAG1B,IAAI,CAAC2B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAU,EAAE,CAAC;EACjE,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC4C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC;IAC3CkD,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAa;IAC7CoD,KAAK,EAAE,CAAC;IACRG,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;;EAE9B;EACAtB,SAAS,CAAC,MAAM;IACdiE,UAAU,CAAC,CAAC;IACZC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,kBAAkB,GAAIC,QAAmB,IAAY;IACzD,OAAO;MACLC,EAAE,EAAED,QAAQ,CAACC,EAAE;MACfC,OAAO,EAAEF,QAAQ,CAACG,QAAQ;MAC1BC,YAAY,EAAEJ,QAAQ,CAACK,aAAa;MACpCC,aAAa,EAAEN,QAAQ,CAACO,cAAc;MACtCC,cAAc,EAAER,QAAQ,CAACS,gBAAgB;MACzCC,WAAW,EAAEV,QAAQ,CAACW,YAAY;MAClCC,QAAQ,EAAEZ,QAAQ,CAACY,QAAQ;MAC3BC,eAAe,EAAEb,QAAQ,CAACc,gBAAgB;MAC1CC,MAAM,EAAEf,QAAQ,CAACe,MAAM;MACvBC,QAAQ,EAAEhB,QAAQ,CAACgB,QAAQ;MAC3BC,gBAAgB,EAAEjB,QAAQ,CAACkB,iBAAiB;MAC5CC,cAAc,EAAEnB,QAAQ,CAACoB,eAAe;MACxCC,SAAS,EAAErB,QAAQ,CAACsB,UAAU;MAC9BC,iBAAiB,EAAEvB,QAAQ,CAACwB,kBAAkB;MAC9CC,SAAS,EAAEzB,QAAQ,CAAC0B,UAAU;MAC9BC,SAAS,EAAE3B,QAAQ,CAAC4B;IACtB,CAAC;EACH,CAAC;;EAED;EACA,MAAM/B,UAAU,GAAG,MAAOgC,MAAwB,IAAK;IACrDzD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAA0D,cAAA,EAAAC,eAAA,EAAAC,eAAA;MACF,MAAMC,WAAW,GAAG;QAClBC,IAAI,EAAEvD,UAAU,CAACE,OAAO;QACxBsD,QAAQ,EAAExD,UAAU,CAACG,QAAQ;QAC7B,GAAG+C;MACL,CAAC;MAED,MAAMO,QAAQ,GAAG,MAAMjF,QAAQ,CAACkF,OAAO,CAACJ,WAAW,CAAC;MACpDK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,QAAQ,CAAC,CAAC,CAAC;;MAEnC;MACA,MAAMI,SAAS,GAAG,EAAAV,cAAA,GAAAM,QAAQ,CAACK,IAAI,cAAAX,cAAA,uBAAbA,cAAA,CAAeY,IAAI,OAAAX,eAAA,GAAIK,QAAQ,CAACK,IAAI,cAAAV,eAAA,uBAAbA,eAAA,CAAeU,IAAI,KAAI,EAAE;MAClE,MAAM9D,UAAU,GAAG,EAAAqD,eAAA,GAAAI,QAAQ,CAACK,IAAI,cAAAT,eAAA,uBAAbA,eAAA,CAAerD,UAAU,KAAIyD,QAAQ,CAACK,IAAI;MAE7D,MAAME,iBAAiB,GAAGH,SAAS,CAACI,GAAG,CAAC7C,kBAAkB,CAAC;MAE3D/B,SAAS,CAAC2E,iBAAiB,CAAC;MAC5BzE,iBAAiB,CAACyE,iBAAiB,CAAC;MACpC/D,aAAa,CAACiE,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP9D,KAAK,EAAE,CAAAJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,KAAK,KAAI,CAAC;QAC7BF,OAAO,EAAE,CAAAF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEmE,YAAY,KAAI;MACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCrG,OAAO,CAACqG,KAAK,CAAC,aAAaA,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACrG,OAAO,GAAG,MAAM,EAAE,CAAC;MAC7E;MACAsB,SAAS,CAAC,EAAE,CAAC;MACbE,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAMjF,QAAQ,CAAC8F,QAAQ,CAAC,CAAC;MAC1ChE,QAAQ,CAACmD,QAAQ,CAACK,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,cAAc,GAAInC,MAAc,IAAK;IACzC,MAAMoC,MAAM,GAAG;MACbjE,OAAO,EAAE,QAAQ;MACjBC,UAAU,EAAE,MAAM;MAClBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE;IACb,CAAC;IACD,OAAO2D,MAAM,CAACpC,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;;EAID;EACA,MAAMqC,aAAa,GAAIrC,MAAc,IAAK;IACxC,MAAMsC,KAAK,GAAG;MACZnE,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,OAAO;MACvBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE;IACb,CAAC;IACD,OAAO6D,KAAK,CAACtC,MAAM,CAAuB,IAAIA,MAAM;EACtD,CAAC;;EAED;EACA,MAAMuC,gBAAgB,GAAItC,QAAiB,IAAK;IAC9C,MAAMmC,MAAM,GAAG;MACbI,GAAG,EAAE,SAAS;MACdC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAOP,MAAM,CAACnC,QAAQ,CAAwB,IAAI,SAAS;EAC7D,CAAC;;EAED;EACA,MAAM2C,eAAe,GAAI3C,QAAiB,IAAK;IAC7C,MAAMqC,KAAK,GAAG;MACZE,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE;IACV,CAAC;IACD,OAAOL,KAAK,CAACrC,QAAQ,CAAuB,IAAI,IAAI;EACtD,CAAC;;EAID;EACA,MAAM4C,UAAU,GAAG;IACjB7E,KAAK,EAAEd,cAAc,CAAC4F,MAAM;IAC5B3E,OAAO,EAAEjB,cAAc,CAAC6F,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChD,MAAM,KAAK,SAAS,CAAC,CAAC8C,MAAM;IAC1E1E,UAAU,EAAElB,cAAc,CAAC6F,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChD,MAAM,KAAK,YAAY,CAAC,CAAC8C,MAAM;IAChFzE,cAAc,EAAEnB,cAAc,CAAC6F,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChD,MAAM,KAAK,gBAAgB,CAAC,CAAC8C,MAAM;IACxFxE,OAAO,EAAEpB,cAAc,CAAC6F,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChD,MAAM,KAAK,SAAS,CAAC,CAAC8C,MAAM;IAC1EvE,SAAS,EAAErB,cAAc,CAAC6F,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChD,MAAM,KAAK,WAAW,CAAC,CAAC8C,MAAM;IAC9EtE,MAAM,EAAEtB,cAAc,CAAC6F,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChD,MAAM,KAAK,QAAQ,CAAC,CAAC8C,MAAM;IACxErE,SAAS,EAAEvB,cAAc,CAAC6F,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChD,MAAM,KAAK,WAAW,CAAC,CAAC8C;EAC1E,CAAC;;EAED;EACA,MAAMG,YAAY,GAAGhF,KAAK,CAACD,KAAK,GAAG,CAAC,GAAGC,KAAK,GAAG4E,UAAU;;EAEzD;EACA,MAAMK,OAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBlH,OAAA,CAACI,IAAI;MAAC+G,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EACpCJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChB5H,OAAA;MAAAsH,QAAA,gBACEtH,OAAA;QAAKoH,KAAK,EAAE;UAAES,UAAU,EAAE;QAAI,CAAE;QAAAP,QAAA,EAAEM,MAAM,CAAC7E;MAAY;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5D1H,OAAA;QAAKoH,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAES,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,EAC7CM,MAAM,CAAC3E;MAAa;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBgB,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBlH,OAAA,CAACnB,GAAG;MAACiJ,KAAK,EAAC,MAAM;MAAAR,QAAA,EAAEJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGvD,MAAc,iBACrB1D,OAAA,CAACnB,GAAG;MAACiJ,KAAK,EAAEjC,cAAc,CAACnC,MAAM,CAAE;MAAA4D,QAAA,EAChCvB,aAAa,CAACrC,MAAM;IAAC;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,KAAK;MACrB,IAAIA,MAAM,CAAClE,MAAM,KAAK,SAAS,IAAIkE,MAAM,CAAClE,MAAM,KAAK,WAAW,EAAE;QAChE,oBACE1D,OAAA;UAAAsH,QAAA,gBACEtH,OAAA;YAAKoH,KAAK,EAAE;cAAES,UAAU,EAAE,GAAG;cAAER,QAAQ,EAAE;YAAO,CAAE;YAAAC,QAAA,EAC/CM,MAAM,CAAChE,gBAAgB,IAAI;UAAI;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACN1H,OAAA;YAAKoH,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,EAC7CM,MAAM,CAAC9D,cAAc,IAAI;UAAQ;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACLE,MAAM,CAAC5D,SAAS,iBACfhE,OAAA;YAAKoH,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAC,gBAC3C,EAACM,MAAM,CAAC5D,SAAS,CAACgE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEV;MACA,oBAAO1H,OAAA,CAACI,IAAI;QAAC6H,IAAI,EAAC,WAAW;QAACb,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAC,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACvE;EACF,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBlH,OAAA;MAAKoH,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EAC9BJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChB5H,OAAA,CAACrB,KAAK;MAACuJ,IAAI,EAAC,OAAO;MAAAZ,QAAA,gBACjBtH,OAAA,CAACtB,MAAM;QACLuJ,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEnI,OAAA,CAACP,WAAW;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBU,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACT,MAAM,CAAE;QAAAN,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1H,OAAA,CAACtB,MAAM;QACLuJ,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEnI,OAAA,CAACN,YAAY;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBU,OAAO,EAAEA,CAAA,KAAME,eAAe,CAACV,MAAM,CAAChF,EAAE,CAAE;QAAA0E,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMa,YAAY,GAAIC,MAAW,IAAK;IACpC,MAAMC,YAA6B,GAAG;MACpC3F,QAAQ,EAAE0F,MAAM,CAAC3F,OAAO;MACxBG,aAAa,EAAEwF,MAAM,CAACzF,YAAY;MAClCQ,QAAQ,EAAEiF,MAAM,CAACjF,QAAQ;MACzBG,MAAM,EAAE8E,MAAM,CAAC9E;IACjB,CAAC;;IAED;IACAnC,aAAa,CAACiE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhE,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;IAChDgB,UAAU,CAACiG,YAAY,CAAC;EAC1B,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMlG,UAAU,CAAC,CAAC;IAClB,MAAMC,SAAS,CAAC,CAAC;IACjBpD,OAAO,CAACsJ,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;EAED,MAAMN,eAAe,GAAI3B,KAAY,IAAK;IACxCrF,gBAAgB,CAACqF,KAAK,CAAC;IACvBvF,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMmH,eAAe,GAAI1F,EAAU,IAAK;IACtCvD,OAAO,CAACuJ,IAAI,CAAC,QAAQhG,EAAE,WAAW,CAAC;EACrC,CAAC;EAED,MAAMiG,YAAY,GAAGA,CAAA,KAAM;IACzBxJ,OAAO,CAACuJ,IAAI,CAAC,YAAY,CAAC;EAC5B,CAAC;EAED,oBACE5I,OAAA;IAAAsH,QAAA,gBACEtH,OAAA;MAAKoH,KAAK,EAAE;QAAE0B,YAAY,EAAE;MAAO,CAAE;MAAAxB,QAAA,gBACnCtH,OAAA,CAACG,KAAK;QAAC4I,KAAK,EAAE,CAAE;QAAC3B,KAAK,EAAE;UAAE4B,MAAM,EAAE,CAAC;UAAElB,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR1H,OAAA,CAACI,IAAI;QAAC6H,IAAI,EAAC,WAAW;QAAAX,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN1H,OAAA,CAACd,GAAG;MAAC+J,MAAM,EAAE,EAAG;MAAC7B,KAAK,EAAE;QAAE0B,YAAY,EAAE;MAAO,CAAE;MAAAxB,QAAA,gBAC/CtH,OAAA,CAACb,GAAG;QAAC+J,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXtH,OAAA,CAACxB,IAAI;UAAA8I,QAAA,eACHtH,OAAA,CAACZ,SAAS;YACRyH,KAAK,EAAC,0BAAM;YACZsC,KAAK,EAAExC,YAAY,CAACjF,KAAM;YAC1B0H,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1H,OAAA,CAACb,GAAG;QAAC+J,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXtH,OAAA,CAACxB,IAAI;UAAA8I,QAAA,eACHtH,OAAA,CAACZ,SAAS;YACRyH,KAAK,EAAC,oBAAK;YACXsC,KAAK,EAAExC,YAAY,CAAC9E,OAAQ;YAC5BuH,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1H,OAAA,CAACb,GAAG;QAAC+J,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXtH,OAAA,CAACxB,IAAI;UAAA8I,QAAA,eACHtH,OAAA,CAACZ,SAAS;YACRyH,KAAK,EAAC,gCAAO;YACbsC,KAAK,EAAExC,YAAY,CAAC5E,cAAe;YACnCqH,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1H,OAAA,CAACb,GAAG;QAAC+J,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXtH,OAAA,CAACxB,IAAI;UAAA8I,QAAA,eACHtH,OAAA,CAACZ,SAAS;YACRyH,KAAK,EAAC,oBAAK;YACXsC,KAAK,EAAExC,YAAY,CAAC7E,UAAW;YAC/BsH,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1H,OAAA,CAACb,GAAG;QAAC+J,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXtH,OAAA,CAACxB,IAAI;UAAA8I,QAAA,eACHtH,OAAA,CAACZ,SAAS;YACRyH,KAAK,EAAC,oBAAK;YACXsC,KAAK,EAAExC,YAAY,CAAC3E,OAAQ;YAC5BoH,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1H,OAAA,CAACb,GAAG;QAAC+J,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXtH,OAAA,CAACxB,IAAI;UAAA8I,QAAA,eACHtH,OAAA,CAACZ,SAAS;YACRyH,KAAK,EAAC,oBAAK;YACXsC,KAAK,EAAExC,YAAY,CAAC1E,SAAU;YAC9BmH,UAAU,EAAE;cAAEtB,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1H,OAAA,CAACxB,IAAI;MAAA8I,QAAA,gBAEHtH,OAAA,CAAClB,IAAI;QACH0B,IAAI,EAAEA,IAAK;QACX6I,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEf,YAAa;QACvBnB,KAAK,EAAE;UAAE0B,YAAY,EAAE;QAAG,CAAE;QAAAxB,QAAA,gBAE5BtH,OAAA,CAAClB,IAAI,CAACyK,IAAI;UAACC,IAAI,EAAC,SAAS;UAACC,KAAK,EAAC,oBAAK;UAAAnC,QAAA,eACnCtH,OAAA,CAACjB,KAAK;YAAC2K,WAAW,EAAC,sCAAQ;YAACtC,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACZ1H,OAAA,CAAClB,IAAI,CAACyK,IAAI;UAACC,IAAI,EAAC,cAAc;UAACC,KAAK,EAAC,0BAAM;UAAAnC,QAAA,eACzCtH,OAAA,CAACjB,KAAK;YAAC2K,WAAW,EAAC,4CAAS;YAACtC,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACZ1H,OAAA,CAAClB,IAAI,CAACyK,IAAI;UAACC,IAAI,EAAC,UAAU;UAACC,KAAK,EAAC,oBAAK;UAAAnC,QAAA,eACpCtH,OAAA,CAAChB,MAAM;YAAC0K,WAAW,EAAC,sCAAQ;YAACtC,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACjDtH,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChD1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ1H,OAAA,CAAClB,IAAI,CAACyK,IAAI;UAACC,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC,0BAAM;UAAAnC,QAAA,eACnCtH,OAAA,CAAChB,MAAM;YAAC0K,WAAW,EAAC,gCAAO;YAACtC,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBAChDtH,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,SAAS;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,YAAY;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACrD1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,gBAAgB;cAAA7B,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC3D1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,SAAS;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,WAAW;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACpD1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,QAAQ;cAAA7B,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClD1H,OAAA,CAAChB,MAAM,CAAC2K,MAAM;cAACR,KAAK,EAAC,WAAW;cAAA7B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ1H,OAAA,CAAClB,IAAI,CAACyK,IAAI;UAAAjC,QAAA,eACRtH,OAAA,CAACtB,MAAM;YAACuJ,IAAI,EAAC,SAAS;YAAC2B,QAAQ,EAAC,QAAQ;YAACzB,IAAI,eAAEnI,OAAA,CAACR,cAAc;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEnE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGP1H,OAAA;QAAKoH,KAAK,EAAE;UACVyC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBjB,YAAY,EAAE;QAChB,CAAE;QAAAxB,QAAA,gBACAtH,OAAA;UAAAsH,QAAA,eACEtH,OAAA,CAACI,IAAI;YAAC4J,MAAM;YAAA1C,QAAA,GAAC,SACT,EAAC1G,cAAc,CAAC4F,MAAM,EAAC,qBAC3B;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN1H,OAAA,CAACrB,KAAK;UAAA2I,QAAA,gBACJtH,OAAA,CAACtB,MAAM;YAAC0J,OAAO,EAAEM,aAAc;YAACP,IAAI,eAAEnI,OAAA,CAACJ,cAAc;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1H,OAAA,CAACtB,MAAM;YAAC0J,OAAO,EAAES,YAAa;YAACV,IAAI,eAAEnI,OAAA,CAACL,cAAc;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1H,OAAA,CAACvB,KAAK;QACJmI,OAAO,EAAEA,OAAQ;QACjBqD,UAAU,EAAErJ,cAAe;QAC3BsJ,MAAM,EAAC,IAAI;QACXpJ,OAAO,EAAEA,OAAQ;QACjBqJ,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpB9I,UAAU,EAAE;UACVE,OAAO,EAAEF,UAAU,CAACE,OAAO;UAC3BC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;UAC7BC,KAAK,EAAEJ,UAAU,CAACI,KAAK;UACvB2I,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC7I,KAAK,EAAE8I,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAAS9I,KAAK,IAAI;UAC7C+I,QAAQ,EAAEA,CAAC5F,IAAI,EAAEpD,QAAQ,KAAK;YAC5BF,aAAa,CAACiE,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEhE,OAAO,EAAEqD,IAAI;cAAEpD,QAAQ,EAAEA,QAAQ,IAAI;YAAG,CAAC,CAAC,CAAC;YAC7Ee,UAAU,CAAC,CAAC;UACd;QACF,CAAE;QACFkI,YAAY,EAAE;UACZ1J,eAAe;UACfyJ,QAAQ,EAAExJ;QACZ;MAAE;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP1H,OAAA,CAACV,KAAK;MACJuH,KAAK,EAAC,0BAAM;MACZ8D,IAAI,EAAEzJ,kBAAmB;MACzB0J,QAAQ,EAAEA,CAAA,KAAMzJ,qBAAqB,CAAC,KAAK,CAAE;MAC7C0J,MAAM,EAAE,cACN7K,OAAA,CAACtB,MAAM;QAAa0J,OAAO,EAAEA,CAAA,KAAMjH,qBAAqB,CAAC,KAAK,CAAE;QAAAmG,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFV,KAAK,EAAE,GAAI;MAAAM,QAAA,EAEVlG,aAAa,iBACZpB,OAAA,CAACT,YAAY;QAACuL,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAAzD,QAAA,gBAC/BtH,OAAA,CAACT,YAAY,CAACgK,IAAI;UAACE,KAAK,EAAC,oBAAK;UAACP,IAAI,EAAE,CAAE;UAAA5B,QAAA,EACpClG,aAAa,CAACyB;QAAO;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,EAC5BlG,aAAa,CAAC2B;QAAY;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,EAC5BlG,aAAa,CAAC6B;QAAa;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;UAACE,KAAK,EAAC,0BAAM;UAACP,IAAI,EAAE,CAAE;UAAA5B,QAAA,EACrClG,aAAa,CAACiC;QAAW;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;UAACE,KAAK,EAAC,oBAAK;UAAAnC,QAAA,eAC5BtH,OAAA,CAACnB,GAAG;YAACiJ,KAAK,EAAC,MAAM;YAAAR,QAAA,EAAElG,aAAa,CAACmC;UAAQ;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,eAC7BtH,OAAA,CAACnB,GAAG;YAACiJ,KAAK,EAAEjC,cAAc,CAACzE,aAAa,CAACsC,MAAM,CAAE;YAAA4D,QAAA,EAC9CvB,aAAa,CAAC3E,aAAa,CAACsC,MAAM;UAAC;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,EACnB,CAACtG,aAAa,CAACsC,MAAM,KAAK,SAAS,IAAItC,aAAa,CAACsC,MAAM,KAAK,WAAW,kBAC1E1D,OAAA,CAAAE,SAAA;UAAAoH,QAAA,gBACEtH,OAAA,CAACT,YAAY,CAACgK,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAnC,QAAA,EAC5BlG,aAAa,CAACwC,gBAAgB,IAAI;UAAI;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAnC,QAAA,eAC7BtH,OAAA,CAACI,IAAI;cAAC+G,IAAI;cAAAG,QAAA,EAAElG,aAAa,CAAC0C,cAAc,IAAI;YAAI;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAnC,QAAA,EAC5BlG,aAAa,CAAC4C,SAAS,IAAI;UAAI;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAnC,QAAA,EAC5BlG,aAAa,CAAC8C,iBAAiB,IAAI;UAAI;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA,eACpB,CACH,eACD1H,OAAA,CAACT,YAAY,CAACgK,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,EAC5BlG,aAAa,CAACgD;QAAS;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACpB1H,OAAA,CAACT,YAAY,CAACgK,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAnC,QAAA,EAC5BlG,aAAa,CAACkD;QAAS;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnH,EAAA,CA5hBID,SAAmB;EAAA,QACRxB,IAAI,CAAC2B,OAAO,EAyBVZ,WAAW;AAAA;AAAAmL,EAAA,GA1BxB1K,SAAmB;AA8hBzB,eAAeA,SAAS;AAAC,IAAA0K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
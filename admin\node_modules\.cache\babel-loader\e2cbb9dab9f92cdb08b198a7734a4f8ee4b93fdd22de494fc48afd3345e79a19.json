{"ast": null, "code": "\"use client\";\n\nimport Col from './col';\nimport useInternalBreakpoint from './hooks/useBreakpoint';\nimport Row from './row';\n// Do not export params\nfunction useBreakpoint() {\n  return useInternalBreakpoint();\n}\nexport { Col, Row };\nexport default {\n  useBreakpoint\n};", "map": {"version": 3, "names": ["Col", "useInternalBreakpoint", "Row", "useBreakpoint"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/grid/index.js"], "sourcesContent": ["\"use client\";\n\nimport Col from './col';\nimport useInternalBreakpoint from './hooks/useBreakpoint';\nimport Row from './row';\n// Do not export params\nfunction useBreakpoint() {\n  return useInternalBreakpoint();\n}\nexport { Col, Row };\nexport default {\n  useBreakpoint\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,GAAG,MAAM,OAAO;AACvB,OAAOC,qBAAqB,MAAM,uBAAuB;AACzD,OAAOC,GAAG,MAAM,OAAO;AACvB;AACA,SAASC,aAAaA,CAAA,EAAG;EACvB,OAAOF,qBAAqB,CAAC,CAAC;AAChC;AACA,SAASD,GAAG,EAAEE,GAAG;AACjB,eAAe;EACbC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
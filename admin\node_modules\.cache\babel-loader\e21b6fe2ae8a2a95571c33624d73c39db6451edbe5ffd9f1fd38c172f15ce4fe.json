{"ast": null, "code": "import Trigger from '@rc-component/trigger';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport DropdownMenu from \"./DropdownMenu\";\nvar BUILT_IN_PLACEMENTS = {\n  bottomRight: {\n    points: ['tl', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomLeft: {\n    points: ['tr', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['bl', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['br', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  }\n};\nvar KeywordTrigger = function KeywordTrigger(props) {\n  var prefixCls = props.prefixCls,\n    options = props.options,\n    children = props.children,\n    visible = props.visible,\n    transitionName = props.transitionName,\n    getPopupContainer = props.getPopupContainer,\n    dropdownClassName = props.dropdownClassName,\n    direction = props.direction,\n    placement = props.placement;\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var dropdownElement = /*#__PURE__*/React.createElement(DropdownMenu, {\n    prefixCls: dropdownPrefix,\n    options: options\n  });\n  var dropdownPlacement = useMemo(function () {\n    var popupPlacement;\n    if (direction === 'rtl') {\n      popupPlacement = placement === 'top' ? 'topLeft' : 'bottomLeft';\n    } else {\n      popupPlacement = placement === 'top' ? 'topRight' : 'bottomRight';\n    }\n    return popupPlacement;\n  }, [direction, placement]);\n  return /*#__PURE__*/React.createElement(Trigger, {\n    prefixCls: dropdownPrefix,\n    popupVisible: visible,\n    popup: dropdownElement,\n    popupPlacement: dropdownPlacement,\n    popupTransitionName: transitionName,\n    builtinPlacements: BUILT_IN_PLACEMENTS,\n    getPopupContainer: getPopupContainer,\n    popupClassName: dropdownClassName\n  }, children);\n};\nexport default KeywordTrigger;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "React", "useMemo", "DropdownMenu", "BUILT_IN_PLACEMENTS", "bottomRight", "points", "offset", "overflow", "adjustX", "adjustY", "bottomLeft", "topRight", "topLeft", "KeywordTrigger", "props", "prefixCls", "options", "children", "visible", "transitionName", "getPopupContainer", "dropdownClassName", "direction", "placement", "dropdownPrefix", "concat", "dropdownElement", "createElement", "dropdownPlacement", "popupPlacement", "popupVisible", "popup", "popupTransitionName", "builtinPlacements", "popupClassName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-mentions/es/KeywordTrigger.js"], "sourcesContent": ["import Trigger from '@rc-component/trigger';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport DropdownMenu from \"./DropdownMenu\";\nvar BUILT_IN_PLACEMENTS = {\n  bottomRight: {\n    points: ['tl', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomLeft: {\n    points: ['tr', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['bl', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['br', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  }\n};\nvar KeywordTrigger = function KeywordTrigger(props) {\n  var prefixCls = props.prefixCls,\n    options = props.options,\n    children = props.children,\n    visible = props.visible,\n    transitionName = props.transitionName,\n    getPopupContainer = props.getPopupContainer,\n    dropdownClassName = props.dropdownClassName,\n    direction = props.direction,\n    placement = props.placement;\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var dropdownElement = /*#__PURE__*/React.createElement(DropdownMenu, {\n    prefixCls: dropdownPrefix,\n    options: options\n  });\n  var dropdownPlacement = useMemo(function () {\n    var popupPlacement;\n    if (direction === 'rtl') {\n      popupPlacement = placement === 'top' ? 'topLeft' : 'bottomLeft';\n    } else {\n      popupPlacement = placement === 'top' ? 'topRight' : 'bottomRight';\n    }\n    return popupPlacement;\n  }, [direction, placement]);\n  return /*#__PURE__*/React.createElement(Trigger, {\n    prefixCls: dropdownPrefix,\n    popupVisible: visible,\n    popup: dropdownElement,\n    popupPlacement: dropdownPlacement,\n    popupTransitionName: transitionName,\n    builtinPlacements: BUILT_IN_PLACEMENTS,\n    getPopupContainer: getPopupContainer,\n    popupClassName: dropdownClassName\n  }, children);\n};\nexport default KeywordTrigger;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,mBAAmB,GAAG;EACxBC,WAAW,EAAE;IACXC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,UAAU,EAAE;IACVL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDE,QAAQ,EAAE;IACRN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDG,OAAO,EAAE;IACPP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfC,QAAQ,EAAE;MACRC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX;EACF;AACF,CAAC;AACD,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvBC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,iBAAiB,GAAGN,KAAK,CAACM,iBAAiB;IAC3CC,iBAAiB,GAAGP,KAAK,CAACO,iBAAiB;IAC3CC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,SAAS,GAAGT,KAAK,CAACS,SAAS;EAC7B,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACV,SAAS,EAAE,WAAW,CAAC;EACtD,IAAIW,eAAe,GAAG,aAAa1B,KAAK,CAAC2B,aAAa,CAACzB,YAAY,EAAE;IACnEa,SAAS,EAAES,cAAc;IACzBR,OAAO,EAAEA;EACX,CAAC,CAAC;EACF,IAAIY,iBAAiB,GAAG3B,OAAO,CAAC,YAAY;IAC1C,IAAI4B,cAAc;IAClB,IAAIP,SAAS,KAAK,KAAK,EAAE;MACvBO,cAAc,GAAGN,SAAS,KAAK,KAAK,GAAG,SAAS,GAAG,YAAY;IACjE,CAAC,MAAM;MACLM,cAAc,GAAGN,SAAS,KAAK,KAAK,GAAG,UAAU,GAAG,aAAa;IACnE;IACA,OAAOM,cAAc;EACvB,CAAC,EAAE,CAACP,SAAS,EAAEC,SAAS,CAAC,CAAC;EAC1B,OAAO,aAAavB,KAAK,CAAC2B,aAAa,CAAC5B,OAAO,EAAE;IAC/CgB,SAAS,EAAES,cAAc;IACzBM,YAAY,EAAEZ,OAAO;IACrBa,KAAK,EAAEL,eAAe;IACtBG,cAAc,EAAED,iBAAiB;IACjCI,mBAAmB,EAAEb,cAAc;IACnCc,iBAAiB,EAAE9B,mBAAmB;IACtCiB,iBAAiB,EAAEA,iBAAiB;IACpCc,cAAc,EAAEb;EAClB,CAAC,EAAEJ,QAAQ,CAAC;AACd,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
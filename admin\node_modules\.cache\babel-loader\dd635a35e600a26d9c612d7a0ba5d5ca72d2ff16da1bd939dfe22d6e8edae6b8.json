{"ast": null, "code": "\"use client\";\n\n/* eslint-disable react/no-array-index-key */\nimport React, { useState } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useItems from './hooks/useItems';\nimport useResizable from './hooks/useResizable';\nimport useResize from './hooks/useResize';\nimport useSizes from './hooks/useSizes';\nimport { InternalPanel } from './Panel';\nimport SplitBar from './SplitBar';\nimport useStyle from './style';\nconst Splitter = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    style,\n    layout = 'horizontal',\n    children,\n    rootClassName,\n    onResizeStart,\n    onResize,\n    onResizeEnd,\n    lazy\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('splitter');\n  const prefixCls = getPrefixCls('splitter', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  // ======================== Direct ========================\n  const isVertical = layout === 'vertical';\n  const isRTL = direction === 'rtl';\n  const reverse = !isVertical && isRTL;\n  // ====================== Items Data ======================\n  const items = useItems(children);\n  // >>> Warning for uncontrolled\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Splitter');\n    let existSize = false;\n    let existUndefinedSize = false;\n    items.forEach(item => {\n      if (item.size !== undefined) {\n        existSize = true;\n      } else {\n        existUndefinedSize = true;\n      }\n    });\n    if (existSize && existUndefinedSize && !onResize) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', 'When part of `Splitter.Panel` has `size`, `onResize` is required or change `size` to `defaultSize`.') : void 0;\n    }\n  }\n  // ====================== Container =======================\n  const [containerSize, setContainerSize] = useState();\n  const onContainerResize = size => {\n    const {\n      offsetWidth,\n      offsetHeight\n    } = size;\n    const containerSize = isVertical ? offsetHeight : offsetWidth;\n    // Skip when container has no size, Such as nested in a hidden tab panel\n    // to fix: https://github.com/ant-design/ant-design/issues/51106\n    if (containerSize === 0) {\n      return;\n    }\n    setContainerSize(containerSize);\n  };\n  // ========================= Size =========================\n  const [panelSizes, itemPxSizes, itemPtgSizes, itemPtgMinSizes, itemPtgMaxSizes, updateSizes] = useSizes(items, containerSize);\n  // ====================== Resizable =======================\n  const resizableInfos = useResizable(items, itemPxSizes, isRTL);\n  const [onOffsetStart, onOffsetUpdate, onOffsetEnd, onCollapse, movingIndex] = useResize(items, resizableInfos, itemPtgSizes, containerSize, updateSizes, isRTL);\n  // ======================== Events ========================\n  const onInternalResizeStart = useEvent(index => {\n    onOffsetStart(index);\n    onResizeStart === null || onResizeStart === void 0 ? void 0 : onResizeStart(itemPxSizes);\n  });\n  const onInternalResizeUpdate = useEvent((index, offset, lazyEnd) => {\n    const nextSizes = onOffsetUpdate(index, offset);\n    if (lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n    } else {\n      onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    }\n  });\n  const onInternalResizeEnd = useEvent(lazyEnd => {\n    onOffsetEnd();\n    if (!lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(itemPxSizes);\n    }\n  });\n  const onInternalCollapse = useEvent((index, type) => {\n    const nextSizes = onCollapse(index, type);\n    onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n  });\n  // ======================== Styles ========================\n  const containerClassName = classNames(prefixCls, className, `${prefixCls}-${layout}`, {\n    [`${prefixCls}-rtl`]: isRTL\n  }, rootClassName, contextClassName, cssVarCls, rootCls, hashId);\n  // ======================== Render ========================\n  const maskCls = `${prefixCls}-mask`;\n  const stackSizes = React.useMemo(() => {\n    const mergedSizes = [];\n    let stack = 0;\n    for (let i = 0; i < items.length; i += 1) {\n      stack += itemPtgSizes[i];\n      mergedSizes.push(stack);\n    }\n    return mergedSizes;\n  }, [itemPtgSizes]);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onContainerResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: mergedStyle,\n    className: containerClassName\n  }, items.map((item, idx) => {\n    // Panel\n    const panel = /*#__PURE__*/React.createElement(InternalPanel, Object.assign({}, item, {\n      prefixCls: prefixCls,\n      size: panelSizes[idx]\n    }));\n    // Split Bar\n    let splitBar = null;\n    const resizableInfo = resizableInfos[idx];\n    if (resizableInfo) {\n      const ariaMinStart = (stackSizes[idx - 1] || 0) + itemPtgMinSizes[idx];\n      const ariaMinEnd = (stackSizes[idx + 1] || 100) - itemPtgMaxSizes[idx + 1];\n      const ariaMaxStart = (stackSizes[idx - 1] || 0) + itemPtgMaxSizes[idx];\n      const ariaMaxEnd = (stackSizes[idx + 1] || 100) - itemPtgMinSizes[idx + 1];\n      splitBar = /*#__PURE__*/React.createElement(SplitBar, {\n        lazy: lazy,\n        index: idx,\n        active: movingIndex === idx,\n        prefixCls: prefixCls,\n        vertical: isVertical,\n        resizable: resizableInfo.resizable,\n        ariaNow: stackSizes[idx] * 100,\n        ariaMin: Math.max(ariaMinStart, ariaMinEnd) * 100,\n        ariaMax: Math.min(ariaMaxStart, ariaMaxEnd) * 100,\n        startCollapsible: resizableInfo.startCollapsible,\n        endCollapsible: resizableInfo.endCollapsible,\n        onOffsetStart: onInternalResizeStart,\n        onOffsetUpdate: (index, offsetX, offsetY, lazyEnd) => {\n          let offset = isVertical ? offsetY : offsetX;\n          if (reverse) {\n            offset = -offset;\n          }\n          onInternalResizeUpdate(index, offset, lazyEnd);\n        },\n        onOffsetEnd: onInternalResizeEnd,\n        onCollapse: onInternalCollapse,\n        containerSize: containerSize || 0\n      });\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: `split-panel-${idx}`\n    }, panel, splitBar);\n  }), typeof movingIndex === 'number' && (/*#__PURE__*/React.createElement(\"div\", {\n    \"aria-hidden\": true,\n    className: classNames(maskCls, `${maskCls}-${layout}`)\n  })))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Splitter.displayName = 'Splitter';\n}\nexport default Splitter;", "map": {"version": 3, "names": ["React", "useState", "classNames", "ResizeObserver", "useEvent", "devUseW<PERSON>ning", "useComponentConfig", "useCSSVarCls", "useItems", "useResizable", "useResize", "useSizes", "InternalPanel", "SplitBar", "useStyle", "Splitter", "props", "prefixCls", "customizePrefixCls", "className", "style", "layout", "children", "rootClassName", "onResizeStart", "onResize", "onResizeEnd", "lazy", "getPrefixCls", "direction", "contextClassName", "contextStyle", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "isVertical", "isRTL", "reverse", "items", "process", "env", "NODE_ENV", "warning", "existSize", "existUndefinedSize", "for<PERSON>ach", "item", "size", "undefined", "containerSize", "setContainerSize", "onContainerResize", "offsetWidth", "offsetHeight", "panelSizes", "itemPxSizes", "itemPtgSizes", "itemPtgMinSizes", "itemPtgMaxSizes", "updateSizes", "resizableInfos", "onOffsetStart", "onOffsetUpdate", "onOffsetEnd", "onCollapse", "movingIndex", "onInternalResizeStart", "index", "onInternalResizeUpdate", "offset", "lazyEnd", "nextSizes", "onInternalResizeEnd", "onInternalCollapse", "type", "containerClassName", "maskCls", "stackSizes", "useMemo", "mergedSizes", "stack", "i", "length", "push", "mergedStyle", "Object", "assign", "createElement", "map", "idx", "panel", "splitBar", "resizableInfo", "ariaMinStart", "ariaMinEnd", "ariaMaxStart", "ariaMaxEnd", "active", "vertical", "resizable", "ariaNow", "aria<PERSON><PERSON>", "Math", "max", "ariaMax", "min", "startCollapsible", "endCollapsible", "offsetX", "offsetY", "Fragment", "key", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/splitter/Splitter.js"], "sourcesContent": ["\"use client\";\n\n/* eslint-disable react/no-array-index-key */\nimport React, { useState } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useItems from './hooks/useItems';\nimport useResizable from './hooks/useResizable';\nimport useResize from './hooks/useResize';\nimport useSizes from './hooks/useSizes';\nimport { InternalPanel } from './Panel';\nimport SplitBar from './SplitBar';\nimport useStyle from './style';\nconst Splitter = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    style,\n    layout = 'horizontal',\n    children,\n    rootClassName,\n    onResizeStart,\n    onResize,\n    onResizeEnd,\n    lazy\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('splitter');\n  const prefixCls = getPrefixCls('splitter', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  // ======================== Direct ========================\n  const isVertical = layout === 'vertical';\n  const isRTL = direction === 'rtl';\n  const reverse = !isVertical && isRTL;\n  // ====================== Items Data ======================\n  const items = useItems(children);\n  // >>> Warning for uncontrolled\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Splitter');\n    let existSize = false;\n    let existUndefinedSize = false;\n    items.forEach(item => {\n      if (item.size !== undefined) {\n        existSize = true;\n      } else {\n        existUndefinedSize = true;\n      }\n    });\n    if (existSize && existUndefinedSize && !onResize) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', 'When part of `Splitter.Panel` has `size`, `onResize` is required or change `size` to `defaultSize`.') : void 0;\n    }\n  }\n  // ====================== Container =======================\n  const [containerSize, setContainerSize] = useState();\n  const onContainerResize = size => {\n    const {\n      offsetWidth,\n      offsetHeight\n    } = size;\n    const containerSize = isVertical ? offsetHeight : offsetWidth;\n    // Skip when container has no size, Such as nested in a hidden tab panel\n    // to fix: https://github.com/ant-design/ant-design/issues/51106\n    if (containerSize === 0) {\n      return;\n    }\n    setContainerSize(containerSize);\n  };\n  // ========================= Size =========================\n  const [panelSizes, itemPxSizes, itemPtgSizes, itemPtgMinSizes, itemPtgMaxSizes, updateSizes] = useSizes(items, containerSize);\n  // ====================== Resizable =======================\n  const resizableInfos = useResizable(items, itemPxSizes, isRTL);\n  const [onOffsetStart, onOffsetUpdate, onOffsetEnd, onCollapse, movingIndex] = useResize(items, resizableInfos, itemPtgSizes, containerSize, updateSizes, isRTL);\n  // ======================== Events ========================\n  const onInternalResizeStart = useEvent(index => {\n    onOffsetStart(index);\n    onResizeStart === null || onResizeStart === void 0 ? void 0 : onResizeStart(itemPxSizes);\n  });\n  const onInternalResizeUpdate = useEvent((index, offset, lazyEnd) => {\n    const nextSizes = onOffsetUpdate(index, offset);\n    if (lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n    } else {\n      onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    }\n  });\n  const onInternalResizeEnd = useEvent(lazyEnd => {\n    onOffsetEnd();\n    if (!lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(itemPxSizes);\n    }\n  });\n  const onInternalCollapse = useEvent((index, type) => {\n    const nextSizes = onCollapse(index, type);\n    onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n  });\n  // ======================== Styles ========================\n  const containerClassName = classNames(prefixCls, className, `${prefixCls}-${layout}`, {\n    [`${prefixCls}-rtl`]: isRTL\n  }, rootClassName, contextClassName, cssVarCls, rootCls, hashId);\n  // ======================== Render ========================\n  const maskCls = `${prefixCls}-mask`;\n  const stackSizes = React.useMemo(() => {\n    const mergedSizes = [];\n    let stack = 0;\n    for (let i = 0; i < items.length; i += 1) {\n      stack += itemPtgSizes[i];\n      mergedSizes.push(stack);\n    }\n    return mergedSizes;\n  }, [itemPtgSizes]);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onContainerResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: mergedStyle,\n    className: containerClassName\n  }, items.map((item, idx) => {\n    // Panel\n    const panel = /*#__PURE__*/React.createElement(InternalPanel, Object.assign({}, item, {\n      prefixCls: prefixCls,\n      size: panelSizes[idx]\n    }));\n    // Split Bar\n    let splitBar = null;\n    const resizableInfo = resizableInfos[idx];\n    if (resizableInfo) {\n      const ariaMinStart = (stackSizes[idx - 1] || 0) + itemPtgMinSizes[idx];\n      const ariaMinEnd = (stackSizes[idx + 1] || 100) - itemPtgMaxSizes[idx + 1];\n      const ariaMaxStart = (stackSizes[idx - 1] || 0) + itemPtgMaxSizes[idx];\n      const ariaMaxEnd = (stackSizes[idx + 1] || 100) - itemPtgMinSizes[idx + 1];\n      splitBar = /*#__PURE__*/React.createElement(SplitBar, {\n        lazy: lazy,\n        index: idx,\n        active: movingIndex === idx,\n        prefixCls: prefixCls,\n        vertical: isVertical,\n        resizable: resizableInfo.resizable,\n        ariaNow: stackSizes[idx] * 100,\n        ariaMin: Math.max(ariaMinStart, ariaMinEnd) * 100,\n        ariaMax: Math.min(ariaMaxStart, ariaMaxEnd) * 100,\n        startCollapsible: resizableInfo.startCollapsible,\n        endCollapsible: resizableInfo.endCollapsible,\n        onOffsetStart: onInternalResizeStart,\n        onOffsetUpdate: (index, offsetX, offsetY, lazyEnd) => {\n          let offset = isVertical ? offsetY : offsetX;\n          if (reverse) {\n            offset = -offset;\n          }\n          onInternalResizeUpdate(index, offset, lazyEnd);\n        },\n        onOffsetEnd: onInternalResizeEnd,\n        onCollapse: onInternalCollapse,\n        containerSize: containerSize || 0\n      });\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: `split-panel-${idx}`\n    }, panel, splitBar);\n  }), typeof movingIndex === 'number' && (/*#__PURE__*/React.createElement(\"div\", {\n    \"aria-hidden\": true,\n    className: classNames(maskCls, `${maskCls}-${layout}`)\n  })))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Splitter.displayName = 'Splitter';\n}\nexport default Splitter;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,KAAK;IACLC,MAAM,GAAG,YAAY;IACrBC,QAAQ;IACRC,aAAa;IACbC,aAAa;IACbC,QAAQ;IACRC,WAAW;IACXC;EACF,CAAC,GAAGX,KAAK;EACT,MAAM;IACJY,YAAY;IACZC,SAAS;IACTV,SAAS,EAAEW,gBAAgB;IAC3BV,KAAK,EAAEW;EACT,CAAC,GAAGzB,kBAAkB,CAAC,UAAU,CAAC;EAClC,MAAMW,SAAS,GAAGW,YAAY,CAAC,UAAU,EAAEV,kBAAkB,CAAC;EAC9D,MAAMc,OAAO,GAAGzB,YAAY,CAACU,SAAS,CAAC;EACvC,MAAM,CAACgB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAACG,SAAS,EAAEe,OAAO,CAAC;EACpE;EACA,MAAMI,UAAU,GAAGf,MAAM,KAAK,UAAU;EACxC,MAAMgB,KAAK,GAAGR,SAAS,KAAK,KAAK;EACjC,MAAMS,OAAO,GAAG,CAACF,UAAU,IAAIC,KAAK;EACpC;EACA,MAAME,KAAK,GAAG/B,QAAQ,CAACc,QAAQ,CAAC;EAChC;EACA,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGtC,aAAa,CAAC,UAAU,CAAC;IACzC,IAAIuC,SAAS,GAAG,KAAK;IACrB,IAAIC,kBAAkB,GAAG,KAAK;IAC9BN,KAAK,CAACO,OAAO,CAACC,IAAI,IAAI;MACpB,IAAIA,IAAI,CAACC,IAAI,KAAKC,SAAS,EAAE;QAC3BL,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM;QACLC,kBAAkB,GAAG,IAAI;MAC3B;IACF,CAAC,CAAC;IACF,IAAID,SAAS,IAAIC,kBAAkB,IAAI,CAACpB,QAAQ,EAAE;MAChDe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,qGAAqG,CAAC,GAAG,KAAK,CAAC;IACjL;EACF;EACA;EACA,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,CAAC;EACpD,MAAMmD,iBAAiB,GAAGJ,IAAI,IAAI;IAChC,MAAM;MACJK,WAAW;MACXC;IACF,CAAC,GAAGN,IAAI;IACR,MAAME,aAAa,GAAGd,UAAU,GAAGkB,YAAY,GAAGD,WAAW;IAC7D;IACA;IACA,IAAIH,aAAa,KAAK,CAAC,EAAE;MACvB;IACF;IACAC,gBAAgB,CAACD,aAAa,CAAC;EACjC,CAAC;EACD;EACA,MAAM,CAACK,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC4B,KAAK,EAAEW,aAAa,CAAC;EAC7H;EACA,MAAMW,cAAc,GAAGpD,YAAY,CAAC8B,KAAK,EAAEiB,WAAW,EAAEnB,KAAK,CAAC;EAC9D,MAAM,CAACyB,aAAa,EAAEC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,WAAW,CAAC,GAAGxD,SAAS,CAAC6B,KAAK,EAAEsB,cAAc,EAAEJ,YAAY,EAAEP,aAAa,EAAEU,WAAW,EAAEvB,KAAK,CAAC;EAC/J;EACA,MAAM8B,qBAAqB,GAAG/D,QAAQ,CAACgE,KAAK,IAAI;IAC9CN,aAAa,CAACM,KAAK,CAAC;IACpB5C,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgC,WAAW,CAAC;EAC1F,CAAC,CAAC;EACF,MAAMa,sBAAsB,GAAGjE,QAAQ,CAAC,CAACgE,KAAK,EAAEE,MAAM,EAAEC,OAAO,KAAK;IAClE,MAAMC,SAAS,GAAGT,cAAc,CAACK,KAAK,EAAEE,MAAM,CAAC;IAC/C,IAAIC,OAAO,EAAE;MACX7C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC8C,SAAS,CAAC;IAClF,CAAC,MAAM;MACL/C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+C,SAAS,CAAC;IACzE;EACF,CAAC,CAAC;EACF,MAAMC,mBAAmB,GAAGrE,QAAQ,CAACmE,OAAO,IAAI;IAC9CP,WAAW,CAAC,CAAC;IACb,IAAI,CAACO,OAAO,EAAE;MACZ7C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC8B,WAAW,CAAC;IACpF;EACF,CAAC,CAAC;EACF,MAAMkB,kBAAkB,GAAGtE,QAAQ,CAAC,CAACgE,KAAK,EAAEO,IAAI,KAAK;IACnD,MAAMH,SAAS,GAAGP,UAAU,CAACG,KAAK,EAAEO,IAAI,CAAC;IACzClD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+C,SAAS,CAAC;IACvE9C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC8C,SAAS,CAAC;EAClF,CAAC,CAAC;EACF;EACA,MAAMI,kBAAkB,GAAG1E,UAAU,CAACe,SAAS,EAAEE,SAAS,EAAE,GAAGF,SAAS,IAAII,MAAM,EAAE,EAAE;IACpF,CAAC,GAAGJ,SAAS,MAAM,GAAGoB;EACxB,CAAC,EAAEd,aAAa,EAAEO,gBAAgB,EAAEK,SAAS,EAAEH,OAAO,EAAEE,MAAM,CAAC;EAC/D;EACA,MAAM2C,OAAO,GAAG,GAAG5D,SAAS,OAAO;EACnC,MAAM6D,UAAU,GAAG9E,KAAK,CAAC+E,OAAO,CAAC,MAAM;IACrC,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,KAAK,CAAC4C,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACxCD,KAAK,IAAIxB,YAAY,CAACyB,CAAC,CAAC;MACxBF,WAAW,CAACI,IAAI,CAACH,KAAK,CAAC;IACzB;IACA,OAAOD,WAAW;EACpB,CAAC,EAAE,CAACvB,YAAY,CAAC,CAAC;EAClB,MAAM4B,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExD,YAAY,CAAC,EAAEX,KAAK,CAAC;EACzE,OAAOa,UAAU,CAAC,aAAajC,KAAK,CAACwF,aAAa,CAACrF,cAAc,EAAE;IACjEsB,QAAQ,EAAE2B;EACZ,CAAC,EAAE,aAAapD,KAAK,CAACwF,aAAa,CAAC,KAAK,EAAE;IACzCpE,KAAK,EAAEiE,WAAW;IAClBlE,SAAS,EAAEyD;EACb,CAAC,EAAErC,KAAK,CAACkD,GAAG,CAAC,CAAC1C,IAAI,EAAE2C,GAAG,KAAK;IAC1B;IACA,MAAMC,KAAK,GAAG,aAAa3F,KAAK,CAACwF,aAAa,CAAC5E,aAAa,EAAE0E,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExC,IAAI,EAAE;MACpF9B,SAAS,EAAEA,SAAS;MACpB+B,IAAI,EAAEO,UAAU,CAACmC,GAAG;IACtB,CAAC,CAAC,CAAC;IACH;IACA,IAAIE,QAAQ,GAAG,IAAI;IACnB,MAAMC,aAAa,GAAGhC,cAAc,CAAC6B,GAAG,CAAC;IACzC,IAAIG,aAAa,EAAE;MACjB,MAAMC,YAAY,GAAG,CAAChB,UAAU,CAACY,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAIhC,eAAe,CAACgC,GAAG,CAAC;MACtE,MAAMK,UAAU,GAAG,CAACjB,UAAU,CAACY,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI/B,eAAe,CAAC+B,GAAG,GAAG,CAAC,CAAC;MAC1E,MAAMM,YAAY,GAAG,CAAClB,UAAU,CAACY,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI/B,eAAe,CAAC+B,GAAG,CAAC;MACtE,MAAMO,UAAU,GAAG,CAACnB,UAAU,CAACY,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAIhC,eAAe,CAACgC,GAAG,GAAG,CAAC,CAAC;MAC1EE,QAAQ,GAAG,aAAa5F,KAAK,CAACwF,aAAa,CAAC3E,QAAQ,EAAE;QACpDc,IAAI,EAAEA,IAAI;QACVyC,KAAK,EAAEsB,GAAG;QACVQ,MAAM,EAAEhC,WAAW,KAAKwB,GAAG;QAC3BzE,SAAS,EAAEA,SAAS;QACpBkF,QAAQ,EAAE/D,UAAU;QACpBgE,SAAS,EAAEP,aAAa,CAACO,SAAS;QAClCC,OAAO,EAAEvB,UAAU,CAACY,GAAG,CAAC,GAAG,GAAG;QAC9BY,OAAO,EAAEC,IAAI,CAACC,GAAG,CAACV,YAAY,EAAEC,UAAU,CAAC,GAAG,GAAG;QACjDU,OAAO,EAAEF,IAAI,CAACG,GAAG,CAACV,YAAY,EAAEC,UAAU,CAAC,GAAG,GAAG;QACjDU,gBAAgB,EAAEd,aAAa,CAACc,gBAAgB;QAChDC,cAAc,EAAEf,aAAa,CAACe,cAAc;QAC5C9C,aAAa,EAAEK,qBAAqB;QACpCJ,cAAc,EAAEA,CAACK,KAAK,EAAEyC,OAAO,EAAEC,OAAO,EAAEvC,OAAO,KAAK;UACpD,IAAID,MAAM,GAAGlC,UAAU,GAAG0E,OAAO,GAAGD,OAAO;UAC3C,IAAIvE,OAAO,EAAE;YACXgC,MAAM,GAAG,CAACA,MAAM;UAClB;UACAD,sBAAsB,CAACD,KAAK,EAAEE,MAAM,EAAEC,OAAO,CAAC;QAChD,CAAC;QACDP,WAAW,EAAES,mBAAmB;QAChCR,UAAU,EAAES,kBAAkB;QAC9BxB,aAAa,EAAEA,aAAa,IAAI;MAClC,CAAC,CAAC;IACJ;IACA,OAAO,aAAalD,KAAK,CAACwF,aAAa,CAACxF,KAAK,CAAC+G,QAAQ,EAAE;MACtDC,GAAG,EAAE,eAAetB,GAAG;IACzB,CAAC,EAAEC,KAAK,EAAEC,QAAQ,CAAC;EACrB,CAAC,CAAC,EAAE,OAAO1B,WAAW,KAAK,QAAQ,KAAK,aAAalE,KAAK,CAACwF,aAAa,CAAC,KAAK,EAAE;IAC9E,aAAa,EAAE,IAAI;IACnBrE,SAAS,EAAEjB,UAAU,CAAC2E,OAAO,EAAE,GAAGA,OAAO,IAAIxD,MAAM,EAAE;EACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AACD,IAAImB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC3B,QAAQ,CAACkG,WAAW,GAAG,UAAU;AACnC;AACA,eAAelG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\nimport { ConfigContext } from '../../config-provider';\nimport useToken from '../../theme/useToken';\nimport { TARGET_CLS } from './interface';\nimport showWaveEffect from './WaveEffect';\nconst useWave = (nodeRef, className, component) => {\n  const {\n    wave\n  } = React.useContext(ConfigContext);\n  const [, token, hashId] = useToken();\n  const showWave = useEvent(event => {\n    const node = nodeRef.current;\n    if ((wave === null || wave === void 0 ? void 0 : wave.disabled) || !node) {\n      return;\n    }\n    const targetNode = node.querySelector(\".\".concat(TARGET_CLS)) || node;\n    const {\n      showEffect\n    } = wave || {};\n    // Customize wave effect\n    (showEffect || showWaveEffect)(targetNode, {\n      className,\n      token,\n      component,\n      event,\n      hashId\n    });\n  });\n  const rafId = React.useRef(null);\n  // Merge trigger event into one for each frame\n  const showDebounceWave = event => {\n    raf.cancel(rafId.current);\n    rafId.current = raf(() => {\n      showWave(event);\n    });\n  };\n  return showDebounceWave;\n};\nexport default useWave;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
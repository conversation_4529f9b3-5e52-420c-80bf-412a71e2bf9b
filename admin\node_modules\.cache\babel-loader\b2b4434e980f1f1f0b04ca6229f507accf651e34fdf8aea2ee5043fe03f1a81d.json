{"ast": null, "code": "import React from 'react';\nimport raf from \"rc-util/es/raf\";\n/**\n * When click on the label,\n * the event will be stopped to prevent the label from being clicked twice.\n * label click -> input click -> label click again\n */\nexport default function useBubbleLock(onOriginInputClick) {\n  const labelClickLockRef = React.useRef(null);\n  const clearLock = () => {\n    raf.cancel(labelClickLockRef.current);\n    labelClickLockRef.current = null;\n  };\n  const onLabelClick = () => {\n    clearLock();\n    labelClickLockRef.current = raf(() => {\n      labelClickLockRef.current = null;\n    });\n  };\n  const onInputClick = e => {\n    if (labelClickLockRef.current) {\n      e.stopPropagation();\n      clearLock();\n    }\n    onOriginInputClick === null || onOriginInputClick === void 0 ? void 0 : onOriginInputClick(e);\n  };\n  return [onLabelClick, onInputClick];\n}", "map": {"version": 3, "names": ["React", "raf", "useBubbleLock", "onOriginInputClick", "labelClickLockRef", "useRef", "clearLock", "cancel", "current", "onLabelClick", "onInputClick", "e", "stopPropagation"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/checkbox/useBubbleLock.js"], "sourcesContent": ["import React from 'react';\nimport raf from \"rc-util/es/raf\";\n/**\n * When click on the label,\n * the event will be stopped to prevent the label from being clicked twice.\n * label click -> input click -> label click again\n */\nexport default function useBubbleLock(onOriginInputClick) {\n  const labelClickLockRef = React.useRef(null);\n  const clearLock = () => {\n    raf.cancel(labelClickLockRef.current);\n    labelClickLockRef.current = null;\n  };\n  const onLabelClick = () => {\n    clearLock();\n    labelClickLockRef.current = raf(() => {\n      labelClickLockRef.current = null;\n    });\n  };\n  const onInputClick = e => {\n    if (labelClickLockRef.current) {\n      e.stopPropagation();\n      clearLock();\n    }\n    onOriginInputClick === null || onOriginInputClick === void 0 ? void 0 : onOriginInputClick(e);\n  };\n  return [onLabelClick, onInputClick];\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,gBAAgB;AAChC;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,kBAAkB,EAAE;EACxD,MAAMC,iBAAiB,GAAGJ,KAAK,CAACK,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtBL,GAAG,CAACM,MAAM,CAACH,iBAAiB,CAACI,OAAO,CAAC;IACrCJ,iBAAiB,CAACI,OAAO,GAAG,IAAI;EAClC,CAAC;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBH,SAAS,CAAC,CAAC;IACXF,iBAAiB,CAACI,OAAO,GAAGP,GAAG,CAAC,MAAM;MACpCG,iBAAiB,CAACI,OAAO,GAAG,IAAI;IAClC,CAAC,CAAC;EACJ,CAAC;EACD,MAAME,YAAY,GAAGC,CAAC,IAAI;IACxB,IAAIP,iBAAiB,CAACI,OAAO,EAAE;MAC7BG,CAAC,CAACC,eAAe,CAAC,CAAC;MACnBN,SAAS,CAAC,CAAC;IACb;IACAH,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACQ,CAAC,CAAC;EAC/F,CAAC;EACD,OAAO,CAACF,YAAY,EAAEC,YAAY,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
# Nginx配置 - h5.haokajiyun.com (修正API 404问题)
# 后台管理系统 + API服务器完整配置

server {
    listen 80;
    listen 443 ssl;
    http2 on;
    server_name h5.haokajiyun.com;
    
    # SSL证书配置
    ssl_certificate /www/server/panel/vhost/cert/h5.haokajiyun.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/h5.haokajiyun.com/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 根目录重定向到admin
    location = / {
        return 301 /admin/;
    }
    
    # API路由配置 - Laravel后端
    # 修正：直接指向API的public目录
    location /api/ {
        alias /www/wwwroot/h5.haokajiyun.com/api/public/;
        try_files $uri $uri/ @api_fallback;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept" always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept";
            add_header Access-Control-Max-Age 86400;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
        
        # PHP处理 - 在API location内
        location ~ \.php$ {
            fastcgi_pass unix:/tmp/php-cgi-83.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME /www/wwwroot/h5.haokajiyun.com/api/public$fastcgi_script_name;
            include fastcgi_params;
            
            # 超时设置
            fastcgi_read_timeout 300;
            fastcgi_connect_timeout 300;
            fastcgi_send_timeout 300;
        }
    }
    
    # API fallback处理
    location @api_fallback {
        fastcgi_pass unix:/tmp/php-cgi-83.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME /www/wwwroot/h5.haokajiyun.com/api/public/index.php;
        fastcgi_param QUERY_STRING $query_string;
        include fastcgi_params;
    }
    
    # Admin后台管理系统
    location /admin/ {
        alias /www/wwwroot/h5.haokajiyun.com/admin/;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
        
        # 静态资源缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # HTML文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # Laravel存储文件访问
    location /storage/ {
        alias /www/wwwroot/h5.haokajiyun.com/api/public/storage/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 禁止访问敏感文件
    location ~ /\.(ht|env) {
        deny all;
    }
    
    # 禁止直接访问Laravel目录
    location ~ ^/(vendor|bootstrap|config|database|resources|routes|tests)/ {
        deny all;
    }
    
    # Gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
    
    # 请求大小限制
    client_max_body_size 20M;
    
    # 日志
    access_log /www/wwwlogs/h5.haokajiyun.com.log;
    error_log /www/wwwlogs/h5.haokajiyun.com.error.log;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name h5.haokajiyun.com;
    return 301 https://$server_name$request_uri;
}

{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fallback\", \"src\", \"imgRef\"],\n  _excluded2 = [\"prefixCls\", \"src\", \"alt\", \"imageInfo\", \"fallback\", \"movable\", \"onClose\", \"visible\", \"icons\", \"rootClassName\", \"closeIcon\", \"getContainer\", \"current\", \"count\", \"countRender\", \"scaleStep\", \"minScale\", \"maxScale\", \"transitionName\", \"maskTransitionName\", \"imageRender\", \"imgCommonProps\", \"toolbarRender\", \"onTransform\", \"onChange\"];\nimport classnames from 'classnames';\nimport Dialog from 'rc-dialog';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport React, { useContext, useEffect, useRef, useState } from 'react';\nimport Operations from \"./Operations\";\nimport { PreviewGroupContext } from \"./context\";\nimport useImageTransform from \"./hooks/useImageTransform\";\nimport useMouseEvent from \"./hooks/useMouseEvent\";\nimport useStatus from \"./hooks/useStatus\";\nimport useTouchEvent from \"./hooks/useTouchEvent\";\nimport { BASE_SCALE_RATIO } from \"./previewConfig\";\nvar PreviewImage = function PreviewImage(_ref) {\n  var fallback = _ref.fallback,\n    src = _ref.src,\n    imgRef = _ref.imgRef,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _useStatus = useStatus({\n      src: src,\n      fallback: fallback\n    }),\n    _useStatus2 = _slicedToArray(_useStatus, 2),\n    getImgRef = _useStatus2[0],\n    srcAndOnload = _useStatus2[1];\n  return /*#__PURE__*/React.createElement(\"img\", _extends({\n    ref: function ref(_ref2) {\n      imgRef.current = _ref2;\n      getImgRef(_ref2);\n    }\n  }, props, srcAndOnload));\n};\nvar Preview = function Preview(props) {\n  var prefixCls = props.prefixCls,\n    src = props.src,\n    alt = props.alt,\n    imageInfo = props.imageInfo,\n    fallback = props.fallback,\n    _props$movable = props.movable,\n    movable = _props$movable === void 0 ? true : _props$movable,\n    onClose = props.onClose,\n    visible = props.visible,\n    _props$icons = props.icons,\n    icons = _props$icons === void 0 ? {} : _props$icons,\n    rootClassName = props.rootClassName,\n    closeIcon = props.closeIcon,\n    getContainer = props.getContainer,\n    _props$current = props.current,\n    current = _props$current === void 0 ? 0 : _props$current,\n    _props$count = props.count,\n    count = _props$count === void 0 ? 1 : _props$count,\n    countRender = props.countRender,\n    _props$scaleStep = props.scaleStep,\n    scaleStep = _props$scaleStep === void 0 ? 0.5 : _props$scaleStep,\n    _props$minScale = props.minScale,\n    minScale = _props$minScale === void 0 ? 1 : _props$minScale,\n    _props$maxScale = props.maxScale,\n    maxScale = _props$maxScale === void 0 ? 50 : _props$maxScale,\n    _props$transitionName = props.transitionName,\n    transitionName = _props$transitionName === void 0 ? 'zoom' : _props$transitionName,\n    _props$maskTransition = props.maskTransitionName,\n    maskTransitionName = _props$maskTransition === void 0 ? 'fade' : _props$maskTransition,\n    imageRender = props.imageRender,\n    imgCommonProps = props.imgCommonProps,\n    toolbarRender = props.toolbarRender,\n    onTransform = props.onTransform,\n    onChange = props.onChange,\n    restProps = _objectWithoutProperties(props, _excluded2);\n  var imgRef = useRef();\n  var groupContext = useContext(PreviewGroupContext);\n  var showLeftOrRightSwitches = groupContext && count > 1;\n  var showOperationsProgress = groupContext && count >= 1;\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    enableTransition = _useState2[0],\n    setEnableTransition = _useState2[1];\n  var _useImageTransform = useImageTransform(imgRef, minScale, maxScale, onTransform),\n    transform = _useImageTransform.transform,\n    resetTransform = _useImageTransform.resetTransform,\n    updateTransform = _useImageTransform.updateTransform,\n    dispatchZoomChange = _useImageTransform.dispatchZoomChange;\n  var _useMouseEvent = useMouseEvent(imgRef, movable, visible, scaleStep, transform, updateTransform, dispatchZoomChange),\n    isMoving = _useMouseEvent.isMoving,\n    onMouseDown = _useMouseEvent.onMouseDown,\n    onWheel = _useMouseEvent.onWheel;\n  var _useTouchEvent = useTouchEvent(imgRef, movable, visible, minScale, transform, updateTransform, dispatchZoomChange),\n    isTouching = _useTouchEvent.isTouching,\n    onTouchStart = _useTouchEvent.onTouchStart,\n    onTouchMove = _useTouchEvent.onTouchMove,\n    onTouchEnd = _useTouchEvent.onTouchEnd;\n  var rotate = transform.rotate,\n    scale = transform.scale;\n  var wrapClassName = classnames(_defineProperty({}, \"\".concat(prefixCls, \"-moving\"), isMoving));\n  useEffect(function () {\n    if (!enableTransition) {\n      setEnableTransition(true);\n    }\n  }, [enableTransition]);\n  var onAfterClose = function onAfterClose() {\n    resetTransform('close');\n  };\n  var onZoomIn = function onZoomIn() {\n    dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'zoomIn');\n  };\n  var onZoomOut = function onZoomOut() {\n    dispatchZoomChange(BASE_SCALE_RATIO / (BASE_SCALE_RATIO + scaleStep), 'zoomOut');\n  };\n  var onRotateRight = function onRotateRight() {\n    updateTransform({\n      rotate: rotate + 90\n    }, 'rotateRight');\n  };\n  var onRotateLeft = function onRotateLeft() {\n    updateTransform({\n      rotate: rotate - 90\n    }, 'rotateLeft');\n  };\n  var onFlipX = function onFlipX() {\n    updateTransform({\n      flipX: !transform.flipX\n    }, 'flipX');\n  };\n  var onFlipY = function onFlipY() {\n    updateTransform({\n      flipY: !transform.flipY\n    }, 'flipY');\n  };\n  var onReset = function onReset() {\n    resetTransform('reset');\n  };\n  var onActive = function onActive(offset) {\n    var position = current + offset;\n    if (!Number.isInteger(position) || position < 0 || position > count - 1) {\n      return;\n    }\n    setEnableTransition(false);\n    resetTransform(offset < 0 ? 'prev' : 'next');\n    onChange === null || onChange === void 0 || onChange(position, current);\n  };\n  var onKeyDown = function onKeyDown(event) {\n    if (!visible || !showLeftOrRightSwitches) return;\n    if (event.keyCode === KeyCode.LEFT) {\n      onActive(-1);\n    } else if (event.keyCode === KeyCode.RIGHT) {\n      onActive(1);\n    }\n  };\n  var onDoubleClick = function onDoubleClick(event) {\n    if (visible) {\n      if (scale !== 1) {\n        updateTransform({\n          x: 0,\n          y: 0,\n          scale: 1\n        }, 'doubleClick');\n      } else {\n        dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'doubleClick', event.clientX, event.clientY);\n      }\n    }\n  };\n  useEffect(function () {\n    var onKeyDownListener = addEventListener(window, 'keydown', onKeyDown, false);\n    return function () {\n      onKeyDownListener.remove();\n    };\n  }, [visible, showLeftOrRightSwitches, current]);\n  var imgNode = /*#__PURE__*/React.createElement(PreviewImage, _extends({}, imgCommonProps, {\n    width: props.width,\n    height: props.height,\n    imgRef: imgRef,\n    className: \"\".concat(prefixCls, \"-img\"),\n    alt: alt,\n    style: {\n      transform: \"translate3d(\".concat(transform.x, \"px, \").concat(transform.y, \"px, 0) scale3d(\").concat(transform.flipX ? '-' : '').concat(scale, \", \").concat(transform.flipY ? '-' : '').concat(scale, \", 1) rotate(\").concat(rotate, \"deg)\"),\n      transitionDuration: (!enableTransition || isTouching) && '0s'\n    },\n    fallback: fallback,\n    src: src,\n    onWheel: onWheel,\n    onMouseDown: onMouseDown,\n    onDoubleClick: onDoubleClick,\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onTouchCancel: onTouchEnd\n  }));\n  var image = _objectSpread({\n    url: src,\n    alt: alt\n  }, imageInfo);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Dialog, _extends({\n    transitionName: transitionName,\n    maskTransitionName: maskTransitionName,\n    closable: false,\n    keyboard: true,\n    prefixCls: prefixCls,\n    onClose: onClose,\n    visible: visible,\n    classNames: {\n      wrapper: wrapClassName\n    },\n    rootClassName: rootClassName,\n    getContainer: getContainer\n  }, restProps, {\n    afterClose: onAfterClose\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-img-wrapper\")\n  }, imageRender ? imageRender(imgNode, _objectSpread({\n    transform: transform,\n    image: image\n  }, groupContext ? {\n    current: current\n  } : {})) : imgNode)), /*#__PURE__*/React.createElement(Operations, {\n    visible: visible,\n    transform: transform,\n    maskTransitionName: maskTransitionName,\n    closeIcon: closeIcon,\n    getContainer: getContainer,\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    icons: icons,\n    countRender: countRender,\n    showSwitch: showLeftOrRightSwitches,\n    showProgress: showOperationsProgress,\n    current: current,\n    count: count,\n    scale: scale,\n    minScale: minScale,\n    maxScale: maxScale,\n    toolbarRender: toolbarRender,\n    onActive: onActive,\n    onZoomIn: onZoomIn,\n    onZoomOut: onZoomOut,\n    onRotateRight: onRotateRight,\n    onRotateLeft: onRotateLeft,\n    onFlipX: onFlipX,\n    onFlipY: onFlipY,\n    onClose: onClose,\n    onReset: onReset,\n    zIndex: restProps.zIndex !== undefined ? restProps.zIndex + 1 : undefined,\n    image: image\n  }));\n};\nexport default Preview;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
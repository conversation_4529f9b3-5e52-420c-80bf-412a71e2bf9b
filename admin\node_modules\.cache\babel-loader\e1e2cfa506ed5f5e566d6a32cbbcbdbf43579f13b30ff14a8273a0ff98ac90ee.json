{"ast": null, "code": "import React,{useState}from'react';import{Card,Table,Button,Space,Typography,Tag,Form,Input,Select,DatePicker,Row,Col,Statistic,message,Modal,Descriptions,Rate}from'antd';import{SearchOutlined,EyeOutlined,ExportOutlined,ReloadOutlined,ClearOutlined,StarOutlined,DownloadOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{RangePicker}=DatePicker;// 模拟已激活订单数据\nconst mockActivatedOrders=[{id:1,orderNo:'ORD202401150001',customerName:'张三',customerPhone:'13800138001',productName:'中国移动5G畅享套餐',operator:'中国移动',completedAt:'2024-01-15 11:00:00',processingTime:0.5,rating:5,feedback:'服务很好，办理很快',processor:'李客服'},{id:2,orderNo:'ORD202401140008',customerName:'陈九',customerPhone:'13800138009',productName:'中国电信5G精选套餐',operator:'中国电信',completedAt:'2024-01-14 16:30:00',processingTime:1.2,rating:4,feedback:'整体满意，价格合理',processor:'王客服'},{id:3,orderNo:'ORD202401140007',customerName:'周十',customerPhone:'13800138010',productName:'中国联通青春套餐',operator:'中国联通',completedAt:'2024-01-14 14:15:00',processingTime:0.8,rating:5,feedback:'非常满意，推荐朋友',processor:'张客服'},{id:4,orderNo:'ORD202401140006',customerName:'吴十一',customerPhone:'13800138011',productName:'中国广电智慧套餐',operator:'中国广电',completedAt:'2024-01-14 10:45:00',processingTime:2.1,rating:3,feedback:'办理时间稍长，但结果满意',processor:'李客服'},{id:5,orderNo:'ORD202401130012',customerName:'郑十二',customerPhone:'13800138012',productName:'中国移动商务套餐',operator:'中国移动',completedAt:'2024-01-13 17:20:00',processingTime:0.3,rating:5,feedback:'专业高效，值得信赖',processor:'王客服'}];const OrderCompleted=()=>{const[form]=Form.useForm();const[orders,setOrders]=useState(mockActivatedOrders);const[filteredOrders,setFilteredOrders]=useState(mockActivatedOrders);const[loading,setLoading]=useState(false);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[detailModalVisible,setDetailModalVisible]=useState(false);const[selectedOrder,setSelectedOrder]=useState(null);// 统计数据\nconst stats={total:filteredOrders.length,highRating:filteredOrders.filter(order=>order.rating>=4).length,avgRating:filteredOrders.reduce((sum,order)=>sum+order.rating,0)/filteredOrders.length,avgProcessingTime:filteredOrders.reduce((sum,order)=>sum+order.processingTime,0)/filteredOrders.length};// 表格列定义\nconst columns=[{title:'订单号',dataIndex:'orderNo',key:'orderNo',width:160,render:text=>/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:'12px'},children:text})},{title:'客户信息',key:'customer',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600},children:record.customerName}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#666'},children:record.customerPhone})]})},{title:'产品名称',dataIndex:'productName',key:'productName',ellipsis:true},{title:'运营商',dataIndex:'operator',key:'operator',width:100,render:text=>/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:text})},{title:'客户评分',dataIndex:'rating',key:'rating',width:120,render:rating=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Rate,{disabled:true,defaultValue:rating,style:{fontSize:'14px'}}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[rating,\".0\\u5206\"]})]}),sorter:(a,b)=>a.rating-b.rating},{title:'处理时长',dataIndex:'processingTime',key:'processingTime',width:100,render:time=>/*#__PURE__*/_jsx(Text,{style:{color:time>2?'#f5222d':'#52c41a'},children:time<1?\"\".concat(Math.round(time*60),\"\\u5206\\u949F\"):\"\".concat(time.toFixed(1),\"\\u5C0F\\u65F6\")}),sorter:(a,b)=>a.processingTime-b.processingTime},{title:'处理人员',dataIndex:'processor',key:'processor',width:100},{title:'完成时间',dataIndex:'completedAt',key:'completedAt',width:150,render:text=>/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px'},children:text}),sorter:(a,b)=>new Date(a.completedAt).getTime()-new Date(b.completedAt).getTime()},{title:'操作',key:'action',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewOrder(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>handleDownloadReceipt(record.id),children:\"\\u51ED\\u8BC1\"})]})}];// 事件处理函数\nconst handleSearch=values=>{setLoading(true);setTimeout(()=>{let filtered=[...orders];if(values.orderNo){filtered=filtered.filter(order=>order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase()));}if(values.customerName){filtered=filtered.filter(order=>order.customerName.toLowerCase().includes(values.customerName.toLowerCase()));}if(values.operator){filtered=filtered.filter(order=>order.operator===values.operator);}if(values.processor){filtered=filtered.filter(order=>order.processor===values.processor);}if(values.rating){filtered=filtered.filter(order=>order.rating>=values.rating);}setFilteredOrders(filtered);setLoading(false);},500);};const handleReset=()=>{form.resetFields();setFilteredOrders(orders);};const handleRefresh=()=>{setLoading(true);setTimeout(()=>{setOrders(mockActivatedOrders);setFilteredOrders(mockActivatedOrders);setLoading(false);message.success('数据已刷新');},1000);};const handleViewOrder=order=>{setSelectedOrder(order);setDetailModalVisible(true);};const handleDownloadReceipt=id=>{message.info(\"\\u4E0B\\u8F7D\\u8BA2\\u5355 \".concat(id,\" \\u51ED\\u8BC1\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\"));};const handleExport=()=>{message.info('导出功能开发中...');};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Title,{level:2,style:{margin:0,color:'#52c41a'},children:\"\\u5DF2\\u6FC0\\u6D3B\\u8BA2\\u5355\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u67E5\\u770B\\u5DF2\\u6FC0\\u6D3B\\u7684\\u8BA2\\u5355\\u8BB0\\u5F55\\u548C\\u5BA2\\u6237\\u53CD\\u9988\"})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6FC0\\u6D3B\\u8BA2\\u5355\\u6570\",value:stats.total,valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u9AD8\\u8BC4\\u5206\\u8BA2\\u5355\",value:stats.highRating,suffix:\"\\u4E2A\",valueStyle:{color:'#f5222d'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E73\\u5747\\u8BC4\\u5206\",value:stats.avgRating,precision:1,suffix:/*#__PURE__*/_jsx(StarOutlined,{style:{color:'#faad14'}}),valueStyle:{color:'#faad14'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E73\\u5747\\u5904\\u7406\\u65F6\\u957F\",value:stats.avgProcessingTime,precision:1,suffix:\"\\u5C0F\\u65F6\",valueStyle:{color:'#1890ff'}})})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Form,{form:form,layout:\"inline\",onFinish:handleSearch,style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Form.Item,{name:\"orderNo\",label:\"\\u8BA2\\u5355\\u53F7\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",style:{width:150}})}),/*#__PURE__*/_jsx(Form.Item,{name:\"customerName\",label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",style:{width:120}})}),/*#__PURE__*/_jsx(Form.Item,{name:\"operator\",label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",style:{width:120},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u79FB\\u52A8\",children:\"\\u4E2D\\u56FD\\u79FB\\u52A8\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u7535\\u4FE1\",children:\"\\u4E2D\\u56FD\\u7535\\u4FE1\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u8054\\u901A\",children:\"\\u4E2D\\u56FD\\u8054\\u901A\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4E2D\\u56FD\\u5E7F\\u7535\",children:\"\\u4E2D\\u56FD\\u5E7F\\u7535\"})]})}),/*#__PURE__*/_jsx(Form.Item,{name:\"processor\",label:\"\\u5904\\u7406\\u4EBA\\u5458\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u5904\\u7406\\u4EBA\\u5458\",style:{width:120},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"\\u674E\\u5BA2\\u670D\",children:\"\\u674E\\u5BA2\\u670D\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u738B\\u5BA2\\u670D\",children:\"\\u738B\\u5BA2\\u670D\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u5F20\\u5BA2\\u670D\",children:\"\\u5F20\\u5BA2\\u670D\"})]})}),/*#__PURE__*/_jsx(Form.Item,{name:\"rating\",label:\"\\u6700\\u4F4E\\u8BC4\\u5206\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8BC4\\u5206\",style:{width:100},children:[/*#__PURE__*/_jsx(Select.Option,{value:5,children:\"5\\u661F\"}),/*#__PURE__*/_jsx(Select.Option,{value:4,children:\"4\\u661F\\u53CA\\u4EE5\\u4E0A\"}),/*#__PURE__*/_jsx(Select.Option,{value:3,children:\"3\\u661F\\u53CA\\u4EE5\\u4E0A\"}),/*#__PURE__*/_jsx(Select.Option,{value:2,children:\"2\\u661F\\u53CA\\u4EE5\\u4E0A\"}),/*#__PURE__*/_jsx(Select.Option,{value:1,children:\"1\\u661F\\u53CA\\u4EE5\\u4E0A\"})]})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",icon:/*#__PURE__*/_jsx(SearchOutlined,{}),children:\"\\u641C\\u7D22\"}),/*#__PURE__*/_jsx(Button,{onClick:handleReset,icon:/*#__PURE__*/_jsx(ClearOutlined,{}),children:\"\\u91CD\\u7F6E\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:16},children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Text,{strong:true,children:[\"\\u5171 \",filteredOrders.length,\" \\u6761\\u5DF2\\u6FC0\\u6D3B\\u8BA2\\u5355\"]})}),/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleRefresh,icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),children:\"\\u5237\\u65B0\"}),/*#__PURE__*/_jsx(Button,{onClick:handleExport,icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\"})]})]}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:filteredOrders,rowKey:\"id\",loading:loading,scroll:{x:1400},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u603B\\u5171 \").concat(total,\" \\u6761\")},rowSelection:{selectedRowKeys,onChange:setSelectedRowKeys}})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5DF2\\u6FC0\\u6D3B\\u8BA2\\u5355\\u8BE6\\u60C5\",open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:700,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Descriptions,{column:2,bordered:true,style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u53F7\",span:2,children:selectedOrder.orderNo}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:selectedOrder.customerName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8054\\u7CFB\\u7535\\u8BDD\",children:selectedOrder.customerPhone}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u540D\\u79F0\",span:2,children:selectedOrder.productName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5904\\u7406\\u4EBA\\u5458\",children:selectedOrder.processor}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5904\\u7406\\u65F6\\u957F\",children:selectedOrder.processingTime<1?\"\".concat(Math.round(selectedOrder.processingTime*60),\"\\u5206\\u949F\"):\"\".concat(selectedOrder.processingTime.toFixed(1),\"\\u5C0F\\u65F6\")}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5B8C\\u6210\\u65F6\\u95F4\",span:2,children:selectedOrder.completedAt})]}),/*#__PURE__*/_jsxs(Card,{title:\"\\u5BA2\\u6237\\u53CD\\u9988\",size:\"small\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:12},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BC4\\u5206\\uFF1A\"}),/*#__PURE__*/_jsx(Rate,{disabled:true,defaultValue:selectedOrder.rating,style:{marginLeft:8}}),/*#__PURE__*/_jsxs(Text,{style:{marginLeft:8},children:[selectedOrder.rating,\".0\\u5206\"]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u53CD\\u9988\\u5185\\u5BB9\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8,padding:12,background:'#f5f5f5',borderRadius:6,minHeight:60},children:selectedOrder.feedback||'客户未留下反馈'})]})]})]})})]});};export default OrderCompleted;", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Form", "Input", "Select", "DatePicker", "Row", "Col", "Statistic", "message", "Modal", "Descriptions", "Rate", "SearchOutlined", "EyeOutlined", "ExportOutlined", "ReloadOutlined", "ClearOutlined", "StarOutlined", "DownloadOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "RangePicker", "mockActivatedOrders", "id", "orderNo", "customerName", "customerPhone", "productName", "operator", "completedAt", "processingTime", "rating", "feedback", "processor", "OrderCompleted", "form", "useForm", "orders", "setOrders", "filteredOrders", "setFilteredOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "detailModalVisible", "setDetailModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "stats", "total", "length", "highRating", "filter", "order", "avgRating", "reduce", "sum", "avgProcessingTime", "columns", "title", "dataIndex", "key", "width", "render", "text", "code", "style", "fontSize", "children", "_", "record", "fontWeight", "color", "ellipsis", "disabled", "defaultValue", "sorter", "a", "b", "time", "concat", "Math", "round", "toFixed", "Date", "getTime", "size", "type", "icon", "onClick", "handleViewOrder", "handleDownloadReceipt", "handleSearch", "values", "setTimeout", "filtered", "toLowerCase", "includes", "handleReset", "resetFields", "handleRefresh", "success", "info", "handleExport", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "suffix", "precision", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "placeholder", "Option", "htmlType", "display", "justifyContent", "alignItems", "strong", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "footer", "column", "bordered", "marginLeft", "marginTop", "padding", "background", "borderRadius", "minHeight"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderCompleted.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Modal,\n  Descriptions,\n  Rate,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  ClearOutlined,\n  StarOutlined,\n  DownloadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\ninterface ActivatedOrder {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  productName: string;\n  operator: string;\n  completedAt: string;\n  processingTime: number; // 处理时长（小时）\n  rating: number; // 客户评分 1-5\n  feedback: string; // 客户反馈\n  processor: string; // 处理人员\n}\n\n// 模拟已激活订单数据\nconst mockActivatedOrders: ActivatedOrder[] = [\n  {\n    id: 1,\n    orderNo: 'ORD202401150001',\n    customerName: '张三',\n    customerPhone: '13800138001',\n    productName: '中国移动5G畅享套餐',\n    operator: '中国移动',\n\n    completedAt: '2024-01-15 11:00:00',\n    processingTime: 0.5,\n    rating: 5,\n    feedback: '服务很好，办理很快',\n    processor: '李客服',\n  },\n  {\n    id: 2,\n    orderNo: 'ORD202401140008',\n    customerName: '陈九',\n    customerPhone: '13800138009',\n    productName: '中国电信5G精选套餐',\n    operator: '中国电信',\n\n    completedAt: '2024-01-14 16:30:00',\n    processingTime: 1.2,\n    rating: 4,\n    feedback: '整体满意，价格合理',\n    processor: '王客服',\n  },\n  {\n    id: 3,\n    orderNo: 'ORD202401140007',\n    customerName: '周十',\n    customerPhone: '13800138010',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n\n    completedAt: '2024-01-14 14:15:00',\n    processingTime: 0.8,\n    rating: 5,\n    feedback: '非常满意，推荐朋友',\n    processor: '张客服',\n  },\n  {\n    id: 4,\n    orderNo: 'ORD202401140006',\n    customerName: '吴十一',\n    customerPhone: '13800138011',\n    productName: '中国广电智慧套餐',\n    operator: '中国广电',\n\n    completedAt: '2024-01-14 10:45:00',\n    processingTime: 2.1,\n    rating: 3,\n    feedback: '办理时间稍长，但结果满意',\n    processor: '李客服',\n  },\n  {\n    id: 5,\n    orderNo: 'ORD202401130012',\n    customerName: '郑十二',\n    customerPhone: '13800138012',\n    productName: '中国移动商务套餐',\n    operator: '中国移动',\n\n    completedAt: '2024-01-13 17:20:00',\n    processingTime: 0.3,\n    rating: 5,\n    feedback: '专业高效，值得信赖',\n    processor: '王客服',\n  },\n];\n\nconst OrderCompleted: React.FC = () => {\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState<ActivatedOrder[]>(mockActivatedOrders);\n  const [filteredOrders, setFilteredOrders] = useState<ActivatedOrder[]>(mockActivatedOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<ActivatedOrder | null>(null);\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    highRating: filteredOrders.filter(order => order.rating >= 4).length,\n    avgRating: filteredOrders.reduce((sum, order) => sum + order.rating, 0) / filteredOrders.length,\n    avgProcessingTime: filteredOrders.reduce((sum, order) => sum + order.processingTime, 0) / filteredOrders.length,\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<ActivatedOrder> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n\n    {\n      title: '客户评分',\n      dataIndex: 'rating',\n      key: 'rating',\n      width: 120,\n      render: (rating: number) => (\n        <div>\n          <Rate disabled defaultValue={rating} style={{ fontSize: '14px' }} />\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {rating}.0分\n          </div>\n        </div>\n      ),\n      sorter: (a, b) => a.rating - b.rating,\n    },\n    {\n      title: '处理时长',\n      dataIndex: 'processingTime',\n      key: 'processingTime',\n      width: 100,\n      render: (time: number) => (\n        <Text style={{ color: time > 2 ? '#f5222d' : '#52c41a' }}>\n          {time < 1 ? `${Math.round(time * 60)}分钟` : `${time.toFixed(1)}小时`}\n        </Text>\n      ),\n      sorter: (a, b) => a.processingTime - b.processingTime,\n    },\n    {\n      title: '处理人员',\n      dataIndex: 'processor',\n      key: 'processor',\n      width: 100,\n    },\n    {\n      title: '完成时间',\n      dataIndex: 'completedAt',\n      key: 'completedAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n      sorter: (a, b) => new Date(a.completedAt).getTime() - new Date(b.completedAt).getTime(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<DownloadOutlined />}\n            onClick={() => handleDownloadReceipt(record.id)}\n          >\n            凭证\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleSearch = (values: any) => {\n    setLoading(true);\n    \n    setTimeout(() => {\n      let filtered = [...orders];\n      \n      if (values.orderNo) {\n        filtered = filtered.filter(order => \n          order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase())\n        );\n      }\n      \n      if (values.customerName) {\n        filtered = filtered.filter(order => \n          order.customerName.toLowerCase().includes(values.customerName.toLowerCase())\n        );\n      }\n      \n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      \n      if (values.processor) {\n        filtered = filtered.filter(order => order.processor === values.processor);\n      }\n      \n      if (values.rating) {\n        filtered = filtered.filter(order => order.rating >= values.rating);\n      }\n      \n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n\n  const handleReset = () => {\n    form.resetFields();\n    setFilteredOrders(orders);\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockActivatedOrders);\n      setFilteredOrders(mockActivatedOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n\n  const handleViewOrder = (order: ActivatedOrder) => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n\n  const handleDownloadReceipt = (id: number) => {\n    message.info(`下载订单 ${id} 凭证功能开发中...`);\n  };\n\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#52c41a' }}>\n          已激活订单\n        </Title>\n        <Text type=\"secondary\">\n          查看已激活的订单记录和客户反馈\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"激活订单数\"\n              value={stats.total}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"高评分订单\"\n              value={stats.highRating}\n              suffix=\"个\"\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"平均评分\"\n              value={stats.avgRating}\n              precision={1}\n              suffix={<StarOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"平均处理时长\"\n              value={stats.avgProcessingTime}\n              precision={1}\n              suffix=\"小时\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 搜索表单 */}\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ marginBottom: 16 }}\n        >\n          <Form.Item name=\"orderNo\" label=\"订单号\">\n            <Input placeholder=\"请输入订单号\" style={{ width: 150 }} />\n          </Form.Item>\n          <Form.Item name=\"customerName\" label=\"客户姓名\">\n            <Input placeholder=\"请输入客户姓名\" style={{ width: 120 }} />\n          </Form.Item>\n          <Form.Item name=\"operator\" label=\"运营商\">\n            <Select placeholder=\"请选择运营商\" style={{ width: 120 }}>\n              <Select.Option value=\"中国移动\">中国移动</Select.Option>\n              <Select.Option value=\"中国电信\">中国电信</Select.Option>\n              <Select.Option value=\"中国联通\">中国联通</Select.Option>\n              <Select.Option value=\"中国广电\">中国广电</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"processor\" label=\"处理人员\">\n            <Select placeholder=\"请选择处理人员\" style={{ width: 120 }}>\n              <Select.Option value=\"李客服\">李客服</Select.Option>\n              <Select.Option value=\"王客服\">王客服</Select.Option>\n              <Select.Option value=\"张客服\">张客服</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"rating\" label=\"最低评分\">\n            <Select placeholder=\"请选择评分\" style={{ width: 100 }}>\n              <Select.Option value={5}>5星</Select.Option>\n              <Select.Option value={4}>4星及以上</Select.Option>\n              <Select.Option value={3}>3星及以上</Select.Option>\n              <Select.Option value={2}>2星及以上</Select.Option>\n              <Select.Option value={1}>1星及以上</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                搜索\n              </Button>\n              <Button onClick={handleReset} icon={<ClearOutlined />}>\n                重置\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {filteredOrders.length} 条已激活订单\n            </Text>\n          </div>\n          <Space>\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n            <Button onClick={handleExport} icon={<ExportOutlined />}>\n              导出\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={filteredOrders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 订单详情弹窗 */}\n      <Modal\n        title=\"已激活订单详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={700}\n      >\n        {selectedOrder && (\n          <div>\n            <Descriptions column={2} bordered style={{ marginBottom: 16 }}>\n              <Descriptions.Item label=\"订单号\" span={2}>\n                {selectedOrder.orderNo}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"客户姓名\">\n                {selectedOrder.customerName}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"联系电话\">\n                {selectedOrder.customerPhone}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"产品名称\" span={2}>\n                {selectedOrder.productName}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"运营商\">\n                <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"处理人员\">\n                {selectedOrder.processor}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"处理时长\">\n                {selectedOrder.processingTime < 1 \n                  ? `${Math.round(selectedOrder.processingTime * 60)}分钟` \n                  : `${selectedOrder.processingTime.toFixed(1)}小时`}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"完成时间\" span={2}>\n                {selectedOrder.completedAt}\n              </Descriptions.Item>\n            </Descriptions>\n            \n            <Card title=\"客户反馈\" size=\"small\">\n              <div style={{ marginBottom: 12 }}>\n                <Text strong>评分：</Text>\n                <Rate disabled defaultValue={selectedOrder.rating} style={{ marginLeft: 8 }} />\n                <Text style={{ marginLeft: 8 }}>{selectedOrder.rating}.0分</Text>\n              </div>\n              <div>\n                <Text strong>反馈内容：</Text>\n                <div style={{ \n                  marginTop: 8, \n                  padding: 12, \n                  background: '#f5f5f5', \n                  borderRadius: 6,\n                  minHeight: 60 \n                }}>\n                  {selectedOrder.feedback || '客户未留下反馈'}\n                </div>\n              </div>\n            </Card>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderCompleted;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,UAAU,CACVC,GAAG,CACHC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,OAAO,CACPC,KAAK,CACLC,YAAY,CACZC,IAAI,KACC,MAAM,CACb,OACEC,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,cAAc,CACdC,aAAa,CACbC,YAAY,CACZC,gBAAgB,KACX,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG3B,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGzB,UAAU,CAClC,KAAM,CAAE0B,WAAY,CAAC,CAAGrB,UAAU,CAgBlC;AACA,KAAM,CAAAsB,mBAAqC,CAAG,CAC5C,CACEC,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,aAAa,CAC5BC,WAAW,CAAE,YAAY,CACzBC,QAAQ,CAAE,MAAM,CAEhBC,WAAW,CAAE,qBAAqB,CAClCC,cAAc,CAAE,GAAG,CACnBC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,KACb,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,aAAa,CAC5BC,WAAW,CAAE,YAAY,CACzBC,QAAQ,CAAE,MAAM,CAEhBC,WAAW,CAAE,qBAAqB,CAClCC,cAAc,CAAE,GAAG,CACnBC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,KACb,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,aAAa,CAC5BC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAEhBC,WAAW,CAAE,qBAAqB,CAClCC,cAAc,CAAE,GAAG,CACnBC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,KACb,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,aAAa,CAC5BC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAEhBC,WAAW,CAAE,qBAAqB,CAClCC,cAAc,CAAE,GAAG,CACnBC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,cAAc,CACxBC,SAAS,CAAE,KACb,CAAC,CACD,CACEV,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,aAAa,CAC5BC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAEhBC,WAAW,CAAE,qBAAqB,CAClCC,cAAc,CAAE,GAAG,CACnBC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,KACb,CAAC,CACF,CAED,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,IAAI,CAAC,CAAGtC,IAAI,CAACuC,OAAO,CAAC,CAAC,CAC7B,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGhD,QAAQ,CAAmBgC,mBAAmB,CAAC,CAC3E,KAAM,CAACiB,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAmBgC,mBAAmB,CAAC,CAC3F,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqD,eAAe,CAAEC,kBAAkB,CAAC,CAAGtD,QAAQ,CAAc,EAAE,CAAC,CACvE,KAAM,CAACuD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxD,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACyD,aAAa,CAAEC,gBAAgB,CAAC,CAAG1D,QAAQ,CAAwB,IAAI,CAAC,CAE/E;AACA,KAAM,CAAA2D,KAAK,CAAG,CACZC,KAAK,CAAEX,cAAc,CAACY,MAAM,CAC5BC,UAAU,CAAEb,cAAc,CAACc,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACvB,MAAM,EAAI,CAAC,CAAC,CAACoB,MAAM,CACpEI,SAAS,CAAEhB,cAAc,CAACiB,MAAM,CAAC,CAACC,GAAG,CAAEH,KAAK,GAAKG,GAAG,CAAGH,KAAK,CAACvB,MAAM,CAAE,CAAC,CAAC,CAAGQ,cAAc,CAACY,MAAM,CAC/FO,iBAAiB,CAAEnB,cAAc,CAACiB,MAAM,CAAC,CAACC,GAAG,CAAEH,KAAK,GAAKG,GAAG,CAAGH,KAAK,CAACxB,cAAc,CAAE,CAAC,CAAC,CAAGS,cAAc,CAACY,MAC3G,CAAC,CAED;AACA,KAAM,CAAAQ,OAAoC,CAAG,CAC3C,CACEC,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGC,IAAY,eACnBjD,IAAA,CAACI,IAAI,EAAC8C,IAAI,MAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAC,QAAA,CACpCJ,IAAI,CACD,CAEV,CAAC,CACD,CACEL,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACM,CAAC,CAAEC,MAAM,gBAChBrD,KAAA,QAAAmD,QAAA,eACErD,IAAA,QAAKmD,KAAK,CAAE,CAAEK,UAAU,CAAE,GAAI,CAAE,CAAAH,QAAA,CAAEE,MAAM,CAAC9C,YAAY,CAAM,CAAC,cAC5DT,IAAA,QAAKmD,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAC7CE,MAAM,CAAC7C,aAAa,CAClB,CAAC,EACH,CAET,CAAC,CACD,CACEkC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBY,QAAQ,CAAE,IACZ,CAAC,CACD,CACEd,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGC,IAAY,eACnBjD,IAAA,CAACpB,GAAG,EAAC6E,KAAK,CAAC,MAAM,CAAAJ,QAAA,CAAEJ,IAAI,CAAM,CAEjC,CAAC,CAED,CACEL,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGjC,MAAc,eACrBb,KAAA,QAAAmD,QAAA,eACErD,IAAA,CAACT,IAAI,EAACoE,QAAQ,MAACC,YAAY,CAAE7C,MAAO,CAACoC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAC,cACpElD,KAAA,QAAKiD,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,EAC7CtC,MAAM,CAAC,UACV,EAAK,CAAC,EACH,CACN,CACD8C,MAAM,CAAEA,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAC/C,MAAM,CAAGgD,CAAC,CAAChD,MACjC,CAAC,CACD,CACE6B,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,gBAAgB,CAC3BC,GAAG,CAAE,gBAAgB,CACrBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGgB,IAAY,eACnBhE,IAAA,CAACI,IAAI,EAAC+C,KAAK,CAAE,CAAEM,KAAK,CAAEO,IAAI,CAAG,CAAC,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAX,QAAA,CACtDW,IAAI,CAAG,CAAC,IAAAC,MAAA,CAAMC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAG,EAAE,CAAC,oBAAAC,MAAA,CAAUD,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,gBAAI,CAC7D,CACP,CACDP,MAAM,CAAEA,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAChD,cAAc,CAAGiD,CAAC,CAACjD,cACzC,CAAC,CACD,CACE8B,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGC,IAAY,eACnBjD,IAAA,QAAKmD,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAC,QAAA,CAC9BJ,IAAI,CACF,CACN,CACDY,MAAM,CAAEA,CAACC,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAAM,IAAI,CAACP,CAAC,CAACjD,WAAW,CAAC,CAACyD,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACN,CAAC,CAAClD,WAAW,CAAC,CAACyD,OAAO,CAAC,CACxF,CAAC,CACD,CACE1B,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACM,CAAC,CAAEC,MAAM,gBAChBrD,KAAA,CAACxB,KAAK,EAAC6F,IAAI,CAAC,OAAO,CAAAlB,QAAA,eACjBrD,IAAA,CAACvB,MAAM,EACL+F,IAAI,CAAC,MAAM,CACXD,IAAI,CAAC,OAAO,CACZE,IAAI,cAAEzE,IAAA,CAACP,WAAW,GAAE,CAAE,CACtBiF,OAAO,CAAEA,CAAA,GAAMC,eAAe,CAACpB,MAAM,CAAE,CAAAF,QAAA,CACxC,cAED,CAAQ,CAAC,cACTrD,IAAA,CAACvB,MAAM,EACL+F,IAAI,CAAC,MAAM,CACXD,IAAI,CAAC,OAAO,CACZE,IAAI,cAAEzE,IAAA,CAACF,gBAAgB,GAAE,CAAE,CAC3B4E,OAAO,CAAEA,CAAA,GAAME,qBAAqB,CAACrB,MAAM,CAAChD,EAAE,CAAE,CAAA8C,QAAA,CACjD,cAED,CAAQ,CAAC,EACJ,CAEX,CAAC,CACF,CAED;AACA,KAAM,CAAAwB,YAAY,CAAIC,MAAW,EAAK,CACpCpD,UAAU,CAAC,IAAI,CAAC,CAEhBqD,UAAU,CAAC,IAAM,CACf,GAAI,CAAAC,QAAQ,CAAG,CAAC,GAAG3D,MAAM,CAAC,CAE1B,GAAIyD,MAAM,CAACtE,OAAO,CAAE,CAClBwE,QAAQ,CAAGA,QAAQ,CAAC3C,MAAM,CAACC,KAAK,EAC9BA,KAAK,CAAC9B,OAAO,CAACyE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAACtE,OAAO,CAACyE,WAAW,CAAC,CAAC,CACnE,CAAC,CACH,CAEA,GAAIH,MAAM,CAACrE,YAAY,CAAE,CACvBuE,QAAQ,CAAGA,QAAQ,CAAC3C,MAAM,CAACC,KAAK,EAC9BA,KAAK,CAAC7B,YAAY,CAACwE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAACrE,YAAY,CAACwE,WAAW,CAAC,CAAC,CAC7E,CAAC,CACH,CAEA,GAAIH,MAAM,CAAClE,QAAQ,CAAE,CACnBoE,QAAQ,CAAGA,QAAQ,CAAC3C,MAAM,CAACC,KAAK,EAAIA,KAAK,CAAC1B,QAAQ,GAAKkE,MAAM,CAAClE,QAAQ,CAAC,CACzE,CAEA,GAAIkE,MAAM,CAAC7D,SAAS,CAAE,CACpB+D,QAAQ,CAAGA,QAAQ,CAAC3C,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACrB,SAAS,GAAK6D,MAAM,CAAC7D,SAAS,CAAC,CAC3E,CAEA,GAAI6D,MAAM,CAAC/D,MAAM,CAAE,CACjBiE,QAAQ,CAAGA,QAAQ,CAAC3C,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACvB,MAAM,EAAI+D,MAAM,CAAC/D,MAAM,CAAC,CACpE,CAEAS,iBAAiB,CAACwD,QAAQ,CAAC,CAC3BtD,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAED,KAAM,CAAAyD,WAAW,CAAGA,CAAA,GAAM,CACxBhE,IAAI,CAACiE,WAAW,CAAC,CAAC,CAClB5D,iBAAiB,CAACH,MAAM,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAgE,aAAa,CAAGA,CAAA,GAAM,CAC1B3D,UAAU,CAAC,IAAI,CAAC,CAChBqD,UAAU,CAAC,IAAM,CACfzD,SAAS,CAAChB,mBAAmB,CAAC,CAC9BkB,iBAAiB,CAAClB,mBAAmB,CAAC,CACtCoB,UAAU,CAAC,KAAK,CAAC,CACjBtC,OAAO,CAACkG,OAAO,CAAC,OAAO,CAAC,CAC1B,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,KAAM,CAAAX,eAAe,CAAIrC,KAAqB,EAAK,CACjDN,gBAAgB,CAACM,KAAK,CAAC,CACvBR,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA8C,qBAAqB,CAAIrE,EAAU,EAAK,CAC5CnB,OAAO,CAACmG,IAAI,6BAAAtB,MAAA,CAAS1D,EAAE,kDAAa,CAAC,CACvC,CAAC,CAED,KAAM,CAAAiF,YAAY,CAAGA,CAAA,GAAM,CACzBpG,OAAO,CAACmG,IAAI,CAAC,YAAY,CAAC,CAC5B,CAAC,CAED,mBACErF,KAAA,QAAAmD,QAAA,eACEnD,KAAA,QAAKiD,KAAK,CAAE,CAAEsC,YAAY,CAAE,MAAO,CAAE,CAAApC,QAAA,eACnCrD,IAAA,CAACG,KAAK,EAACuF,KAAK,CAAE,CAAE,CAACvC,KAAK,CAAE,CAAEwC,MAAM,CAAE,CAAC,CAAElC,KAAK,CAAE,SAAU,CAAE,CAAAJ,QAAA,CAAC,gCAEzD,CAAO,CAAC,cACRrD,IAAA,CAACI,IAAI,EAACoE,IAAI,CAAC,WAAW,CAAAnB,QAAA,CAAC,4FAEvB,CAAM,CAAC,EACJ,CAAC,cAGNnD,KAAA,CAACjB,GAAG,EAAC2G,MAAM,CAAE,EAAG,CAACzC,KAAK,CAAE,CAAEsC,YAAY,CAAE,MAAO,CAAE,CAAApC,QAAA,eAC/CrD,IAAA,CAACd,GAAG,EAAC2G,IAAI,CAAE,CAAE,CAAAxC,QAAA,cACXrD,IAAA,CAACzB,IAAI,EAAA8E,QAAA,cACHrD,IAAA,CAACb,SAAS,EACRyD,KAAK,CAAC,gCAAO,CACbkD,KAAK,CAAE7D,KAAK,CAACC,KAAM,CACnB6D,UAAU,CAAE,CAAEtC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNzD,IAAA,CAACd,GAAG,EAAC2G,IAAI,CAAE,CAAE,CAAAxC,QAAA,cACXrD,IAAA,CAACzB,IAAI,EAAA8E,QAAA,cACHrD,IAAA,CAACb,SAAS,EACRyD,KAAK,CAAC,gCAAO,CACbkD,KAAK,CAAE7D,KAAK,CAACG,UAAW,CACxB4D,MAAM,CAAC,QAAG,CACVD,UAAU,CAAE,CAAEtC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNzD,IAAA,CAACd,GAAG,EAAC2G,IAAI,CAAE,CAAE,CAAAxC,QAAA,cACXrD,IAAA,CAACzB,IAAI,EAAA8E,QAAA,cACHrD,IAAA,CAACb,SAAS,EACRyD,KAAK,CAAC,0BAAM,CACZkD,KAAK,CAAE7D,KAAK,CAACM,SAAU,CACvB0D,SAAS,CAAE,CAAE,CACbD,MAAM,cAAEhG,IAAA,CAACH,YAAY,EAACsD,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAU,CAAE,CAAE,CAAE,CACtDsC,UAAU,CAAE,CAAEtC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNzD,IAAA,CAACd,GAAG,EAAC2G,IAAI,CAAE,CAAE,CAAAxC,QAAA,cACXrD,IAAA,CAACzB,IAAI,EAAA8E,QAAA,cACHrD,IAAA,CAACb,SAAS,EACRyD,KAAK,CAAC,sCAAQ,CACdkD,KAAK,CAAE7D,KAAK,CAACS,iBAAkB,CAC/BuD,SAAS,CAAE,CAAE,CACbD,MAAM,CAAC,cAAI,CACXD,UAAU,CAAE,CAAEtC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAENvD,KAAA,CAAC3B,IAAI,EAAA8E,QAAA,eAEHnD,KAAA,CAACrB,IAAI,EACHsC,IAAI,CAAEA,IAAK,CACX+E,MAAM,CAAC,QAAQ,CACfC,QAAQ,CAAEtB,YAAa,CACvB1B,KAAK,CAAE,CAAEsC,YAAY,CAAE,EAAG,CAAE,CAAApC,QAAA,eAE5BrD,IAAA,CAACnB,IAAI,CAACuH,IAAI,EAACC,IAAI,CAAC,SAAS,CAACC,KAAK,CAAC,oBAAK,CAAAjD,QAAA,cACnCrD,IAAA,CAAClB,KAAK,EAACyH,WAAW,CAAC,sCAAQ,CAACpD,KAAK,CAAE,CAAEJ,KAAK,CAAE,GAAI,CAAE,CAAE,CAAC,CAC5C,CAAC,cACZ/C,IAAA,CAACnB,IAAI,CAACuH,IAAI,EAACC,IAAI,CAAC,cAAc,CAACC,KAAK,CAAC,0BAAM,CAAAjD,QAAA,cACzCrD,IAAA,CAAClB,KAAK,EAACyH,WAAW,CAAC,4CAAS,CAACpD,KAAK,CAAE,CAAEJ,KAAK,CAAE,GAAI,CAAE,CAAE,CAAC,CAC7C,CAAC,cACZ/C,IAAA,CAACnB,IAAI,CAACuH,IAAI,EAACC,IAAI,CAAC,UAAU,CAACC,KAAK,CAAC,oBAAK,CAAAjD,QAAA,cACpCnD,KAAA,CAACnB,MAAM,EAACwH,WAAW,CAAC,sCAAQ,CAACpD,KAAK,CAAE,CAAEJ,KAAK,CAAE,GAAI,CAAE,CAAAM,QAAA,eACjDrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAC,0BAAM,CAAAzC,QAAA,CAAC,0BAAI,CAAe,CAAC,cAChDrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAC,0BAAM,CAAAzC,QAAA,CAAC,0BAAI,CAAe,CAAC,cAChDrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAC,0BAAM,CAAAzC,QAAA,CAAC,0BAAI,CAAe,CAAC,cAChDrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAC,0BAAM,CAAAzC,QAAA,CAAC,0BAAI,CAAe,CAAC,EAC1C,CAAC,CACA,CAAC,cACZrD,IAAA,CAACnB,IAAI,CAACuH,IAAI,EAACC,IAAI,CAAC,WAAW,CAACC,KAAK,CAAC,0BAAM,CAAAjD,QAAA,cACtCnD,KAAA,CAACnB,MAAM,EAACwH,WAAW,CAAC,4CAAS,CAACpD,KAAK,CAAE,CAAEJ,KAAK,CAAE,GAAI,CAAE,CAAAM,QAAA,eAClDrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAC,oBAAK,CAAAzC,QAAA,CAAC,oBAAG,CAAe,CAAC,cAC9CrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAC,oBAAK,CAAAzC,QAAA,CAAC,oBAAG,CAAe,CAAC,cAC9CrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAC,oBAAK,CAAAzC,QAAA,CAAC,oBAAG,CAAe,CAAC,EACxC,CAAC,CACA,CAAC,cACZrD,IAAA,CAACnB,IAAI,CAACuH,IAAI,EAACC,IAAI,CAAC,QAAQ,CAACC,KAAK,CAAC,0BAAM,CAAAjD,QAAA,cACnCnD,KAAA,CAACnB,MAAM,EAACwH,WAAW,CAAC,gCAAO,CAACpD,KAAK,CAAE,CAAEJ,KAAK,CAAE,GAAI,CAAE,CAAAM,QAAA,eAChDrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAE,CAAE,CAAAzC,QAAA,CAAC,SAAE,CAAe,CAAC,cAC3CrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAE,CAAE,CAAAzC,QAAA,CAAC,2BAAK,CAAe,CAAC,cAC9CrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAE,CAAE,CAAAzC,QAAA,CAAC,2BAAK,CAAe,CAAC,cAC9CrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAE,CAAE,CAAAzC,QAAA,CAAC,2BAAK,CAAe,CAAC,cAC9CrD,IAAA,CAACjB,MAAM,CAACyH,MAAM,EAACV,KAAK,CAAE,CAAE,CAAAzC,QAAA,CAAC,2BAAK,CAAe,CAAC,EACxC,CAAC,CACA,CAAC,cACZrD,IAAA,CAACnB,IAAI,CAACuH,IAAI,EAAA/C,QAAA,cACRnD,KAAA,CAACxB,KAAK,EAAA2E,QAAA,eACJrD,IAAA,CAACvB,MAAM,EAAC+F,IAAI,CAAC,SAAS,CAACiC,QAAQ,CAAC,QAAQ,CAAChC,IAAI,cAAEzE,IAAA,CAACR,cAAc,GAAE,CAAE,CAAA6D,QAAA,CAAC,cAEnE,CAAQ,CAAC,cACTrD,IAAA,CAACvB,MAAM,EAACiG,OAAO,CAAES,WAAY,CAACV,IAAI,cAAEzE,IAAA,CAACJ,aAAa,GAAE,CAAE,CAAAyD,QAAA,CAAC,cAEvD,CAAQ,CAAC,EACJ,CAAC,CACC,CAAC,EACR,CAAC,cAGPnD,KAAA,QAAKiD,KAAK,CAAE,CACVuD,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBnB,YAAY,CAAE,EAChB,CAAE,CAAApC,QAAA,eACArD,IAAA,QAAAqD,QAAA,cACEnD,KAAA,CAACE,IAAI,EAACyG,MAAM,MAAAxD,QAAA,EAAC,SACT,CAAC9B,cAAc,CAACY,MAAM,CAAC,uCAC3B,EAAM,CAAC,CACJ,CAAC,cACNjC,KAAA,CAACxB,KAAK,EAAA2E,QAAA,eACJrD,IAAA,CAACvB,MAAM,EAACiG,OAAO,CAAEW,aAAc,CAACZ,IAAI,cAAEzE,IAAA,CAACL,cAAc,GAAE,CAAE,CAAA0D,QAAA,CAAC,cAE1D,CAAQ,CAAC,cACTrD,IAAA,CAACvB,MAAM,EAACiG,OAAO,CAAEc,YAAa,CAACf,IAAI,cAAEzE,IAAA,CAACN,cAAc,GAAE,CAAE,CAAA2D,QAAA,CAAC,cAEzD,CAAQ,CAAC,EACJ,CAAC,EACL,CAAC,cAGNrD,IAAA,CAACxB,KAAK,EACJmE,OAAO,CAAEA,OAAQ,CACjBmE,UAAU,CAAEvF,cAAe,CAC3BwF,MAAM,CAAC,IAAI,CACXtF,OAAO,CAAEA,OAAQ,CACjBuF,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACnF,KAAK,CAAEoF,KAAK,aAAArD,MAAA,CACjBqD,KAAK,CAAC,CAAC,CAAC,MAAArD,MAAA,CAAIqD,KAAK,CAAC,CAAC,CAAC,0BAAArD,MAAA,CAAS/B,KAAK,WAC3C,CAAE,CACFqF,YAAY,CAAE,CACZ5F,eAAe,CACf6F,QAAQ,CAAE5F,kBACZ,CAAE,CACH,CAAC,EACE,CAAC,cAGP5B,IAAA,CAACX,KAAK,EACJuD,KAAK,CAAC,4CAAS,CACf6E,IAAI,CAAE5F,kBAAmB,CACzB6F,QAAQ,CAAEA,CAAA,GAAM5F,qBAAqB,CAAC,KAAK,CAAE,CAC7C6F,MAAM,CAAE,cACN3H,IAAA,CAACvB,MAAM,EAAaiG,OAAO,CAAEA,CAAA,GAAM5C,qBAAqB,CAAC,KAAK,CAAE,CAAAuB,QAAA,CAAC,cAEjE,EAFY,OAEJ,CAAC,CACT,CACFN,KAAK,CAAE,GAAI,CAAAM,QAAA,CAEVtB,aAAa,eACZ7B,KAAA,QAAAmD,QAAA,eACEnD,KAAA,CAACZ,YAAY,EAACsI,MAAM,CAAE,CAAE,CAACC,QAAQ,MAAC1E,KAAK,CAAE,CAAEsC,YAAY,CAAE,EAAG,CAAE,CAAApC,QAAA,eAC5DrD,IAAA,CAACV,YAAY,CAAC8G,IAAI,EAACE,KAAK,CAAC,oBAAK,CAACT,IAAI,CAAE,CAAE,CAAAxC,QAAA,CACpCtB,aAAa,CAACvB,OAAO,CACL,CAAC,cACpBR,IAAA,CAACV,YAAY,CAAC8G,IAAI,EAACE,KAAK,CAAC,0BAAM,CAAAjD,QAAA,CAC5BtB,aAAa,CAACtB,YAAY,CACV,CAAC,cACpBT,IAAA,CAACV,YAAY,CAAC8G,IAAI,EAACE,KAAK,CAAC,0BAAM,CAAAjD,QAAA,CAC5BtB,aAAa,CAACrB,aAAa,CACX,CAAC,cACpBV,IAAA,CAACV,YAAY,CAAC8G,IAAI,EAACE,KAAK,CAAC,0BAAM,CAACT,IAAI,CAAE,CAAE,CAAAxC,QAAA,CACrCtB,aAAa,CAACpB,WAAW,CACT,CAAC,cACpBX,IAAA,CAACV,YAAY,CAAC8G,IAAI,EAACE,KAAK,CAAC,oBAAK,CAAAjD,QAAA,cAC5BrD,IAAA,CAACpB,GAAG,EAAC6E,KAAK,CAAC,MAAM,CAAAJ,QAAA,CAAEtB,aAAa,CAACnB,QAAQ,CAAM,CAAC,CAC/B,CAAC,cACpBZ,IAAA,CAACV,YAAY,CAAC8G,IAAI,EAACE,KAAK,CAAC,0BAAM,CAAAjD,QAAA,CAC5BtB,aAAa,CAACd,SAAS,CACP,CAAC,cACpBjB,IAAA,CAACV,YAAY,CAAC8G,IAAI,EAACE,KAAK,CAAC,0BAAM,CAAAjD,QAAA,CAC5BtB,aAAa,CAACjB,cAAc,CAAG,CAAC,IAAAmD,MAAA,CAC1BC,IAAI,CAACC,KAAK,CAACpC,aAAa,CAACjB,cAAc,CAAG,EAAE,CAAC,oBAAAmD,MAAA,CAC7ClC,aAAa,CAACjB,cAAc,CAACsD,OAAO,CAAC,CAAC,CAAC,gBAAI,CACjC,CAAC,cACpBpE,IAAA,CAACV,YAAY,CAAC8G,IAAI,EAACE,KAAK,CAAC,0BAAM,CAACT,IAAI,CAAE,CAAE,CAAAxC,QAAA,CACrCtB,aAAa,CAAClB,WAAW,CACT,CAAC,EACR,CAAC,cAEfX,KAAA,CAAC3B,IAAI,EAACqE,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,OAAO,CAAAlB,QAAA,eAC7BnD,KAAA,QAAKiD,KAAK,CAAE,CAAEsC,YAAY,CAAE,EAAG,CAAE,CAAApC,QAAA,eAC/BrD,IAAA,CAACI,IAAI,EAACyG,MAAM,MAAAxD,QAAA,CAAC,oBAAG,CAAM,CAAC,cACvBrD,IAAA,CAACT,IAAI,EAACoE,QAAQ,MAACC,YAAY,CAAE7B,aAAa,CAAChB,MAAO,CAACoC,KAAK,CAAE,CAAE2E,UAAU,CAAE,CAAE,CAAE,CAAE,CAAC,cAC/E5H,KAAA,CAACE,IAAI,EAAC+C,KAAK,CAAE,CAAE2E,UAAU,CAAE,CAAE,CAAE,CAAAzE,QAAA,EAAEtB,aAAa,CAAChB,MAAM,CAAC,UAAG,EAAM,CAAC,EAC7D,CAAC,cACNb,KAAA,QAAAmD,QAAA,eACErD,IAAA,CAACI,IAAI,EAACyG,MAAM,MAAAxD,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBrD,IAAA,QAAKmD,KAAK,CAAE,CACV4E,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,EAAE,CACXC,UAAU,CAAE,SAAS,CACrBC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,EACb,CAAE,CAAA9E,QAAA,CACCtB,aAAa,CAACf,QAAQ,EAAI,SAAS,CACjC,CAAC,EACH,CAAC,EACF,CAAC,EACJ,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
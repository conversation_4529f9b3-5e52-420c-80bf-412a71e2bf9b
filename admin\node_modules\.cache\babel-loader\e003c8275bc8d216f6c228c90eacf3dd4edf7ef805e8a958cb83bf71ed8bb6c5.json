{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\components\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Layout as AntLayout, Menu, Button, theme, Typography, Space, Dropdown, Avatar, Badge } from 'antd';\nimport { MenuFoldOutlined, MenuUnfoldOutlined, DashboardOutlined, ShoppingOutlined, SettingOutlined, UserOutlined, LogoutOutlined, BellOutlined, FileTextOutlined, MobileOutlined, TeamOutlined } from '@ant-design/icons';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst {\n  Title\n} = Typography;\nconst Layout = () => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    token: {\n      colorBgContainer,\n      borderRadiusLG\n    }\n  } = theme.useToken();\n\n  // 菜单项配置\n  const menuItems = [{\n    key: '/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: '/products',\n    icon: /*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this),\n    label: '产品管理',\n    children: [{\n      key: '/products/list',\n      label: '产品列表'\n    }, {\n      key: '/products/category',\n      label: '分类管理'\n    }]\n  }, {\n    key: '/orders',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    label: '订单管理',\n    children: [{\n      key: '/orders/list',\n      label: '全部订单'\n    }, {\n      key: '/orders/pending',\n      label: '待处理订单'\n    }, {\n      key: '/orders/reviewing',\n      label: '审核中订单'\n    }, {\n      key: '/orders/pending-upload',\n      label: '待上传三证'\n    }, {\n      key: '/orders/shipped',\n      label: '已发货订单'\n    }, {\n      key: '/orders/failed',\n      label: '失败订单'\n    }, {\n      key: '/orders/completed',\n      label: '已激活订单'\n    }]\n  }, {\n    key: '/users',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this),\n    label: '用户管理',\n    children: [{\n      key: '/users/list',\n      label: '用户列表'\n    }, {\n      key: '/users/create',\n      label: '添加用户'\n    }]\n  }, {\n    key: '/app',\n    icon: /*#__PURE__*/_jsxDEV(MobileOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this),\n    label: 'APP管理',\n    children: [{\n      key: '/app/config',\n      label: '配置信息'\n    }]\n  }, {\n    key: '/settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this),\n    label: '系统设置'\n  }];\n\n  // 用户菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this),\n    label: '账户设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    danger: true\n  }];\n\n  // 菜单点击处理\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n\n  // 用户菜单点击处理\n  const handleUserMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      case 'profile':\n        // 处理个人资料\n        console.log('个人资料');\n        break;\n      case 'settings':\n        // 处理账户设置\n        console.log('账户设置');\n        break;\n      default:\n        break;\n    }\n  };\n\n  // 获取当前选中的菜单项\n  const getSelectedKeys = () => {\n    const path = location.pathname;\n    if (path.startsWith('/products') || path.startsWith('/orders') || path.startsWith('/users') || path.startsWith('/app')) {\n      return [path];\n    }\n    return [path];\n  };\n\n  // 获取当前展开的菜单项\n  const getOpenKeys = () => {\n    const path = location.pathname;\n    if (path.startsWith('/products')) {\n      return ['/products'];\n    }\n    if (path.startsWith('/orders')) {\n      return ['/orders'];\n    }\n    if (path.startsWith('/app')) {\n      return ['/app'];\n    }\n    return [];\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      style: {\n        overflow: 'auto',\n        height: '100vh',\n        position: 'fixed',\n        left: 0,\n        top: 0,\n        bottom: 0,\n        background: '#f5f5f5',\n        // 浅灰色背景\n        borderRight: '1px solid #e8e8e8'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: 64,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: '#ffffff',\n          margin: 16,\n          borderRadius: borderRadiusLG,\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          style: {\n            color: '#1890ff',\n            margin: 0,\n            fontSize: collapsed ? 14 : 16,\n            fontWeight: 'bold'\n          },\n          children: collapsed ? '产品' : '产品管理后台'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        theme: \"light\",\n        mode: \"inline\",\n        selectedKeys: getSelectedKeys(),\n        defaultOpenKeys: getOpenKeys(),\n        items: menuItems,\n        onClick: handleMenuClick,\n        style: {\n          background: 'transparent',\n          border: 'none',\n          fontSize: '14px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      style: {\n        marginLeft: collapsed ? 80 : 200\n      },\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 24px',\n          background: colorBgContainer,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderBottom: '1px solid #f0f0f0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              fontSize: '16px',\n              width: 64,\n              height: 64\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Badge, {\n            count: 5,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 23\n              }, this),\n              style: {\n                fontSize: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems,\n              onClick: handleUserMenuClick\n            },\n            placement: \"bottomRight\",\n            arrow: true,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u7BA1\\u7406\\u5458\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '24px',\n          padding: '24px',\n          minHeight: 280,\n          background: colorBgContainer,\n          borderRadius: borderRadiusLG,\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"PW+m35bZYq+XFywbQ/n3HkN9cPE=\", false, function () {\n  return [useNavigate, useLocation, theme.useToken];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Layout", "AntLayout", "<PERSON><PERSON>", "<PERSON><PERSON>", "theme", "Typography", "Space", "Dropdown", "Avatar", "Badge", "MenuFoldOutlined", "MenuUnfoldOutlined", "DashboardOutlined", "ShoppingOutlined", "SettingOutlined", "UserOutlined", "LogoutOutlined", "BellOutlined", "FileTextOutlined", "MobileOutlined", "TeamOutlined", "Outlet", "useNavigate", "useLocation", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Title", "_s", "collapsed", "setCollapsed", "navigate", "location", "token", "colorBgContainer", "borderRadiusLG", "useToken", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "children", "userMenuItems", "type", "danger", "handleMenuClick", "handleUserMenuClick", "console", "log", "getSelectedKeys", "path", "pathname", "startsWith", "get<PERSON><PERSON><PERSON><PERSON>s", "style", "minHeight", "trigger", "collapsible", "overflow", "height", "position", "left", "top", "bottom", "background", "borderRight", "display", "alignItems", "justifyContent", "margin", "borderRadius", "boxShadow", "level", "color", "fontSize", "fontWeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "defaultOpenKeys", "items", "onClick", "border", "marginLeft", "padding", "borderBottom", "width", "size", "count", "menu", "placement", "arrow", "cursor", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/components/Layout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Layout as AntLay<PERSON>,\n  <PERSON>u,\n  Button,\n  theme,\n  Typography,\n  Space,\n  Dropdown,\n  Avatar,\n  Badge,\n} from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  DashboardOutlined,\n  ShoppingOutlined,\n  SettingOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  FileTextOutlined,\n  MobileOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\n\nconst { Header, Sider, Content } = AntLayout;\nconst { Title } = Typography;\n\nconst Layout: React.FC = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    token: { colorBgContainer, borderRadiusLG },\n  } = theme.useToken();\n\n  // 菜单项配置\n  const menuItems = [\n    {\n      key: '/dashboard',\n      icon: <DashboardOutlined />,\n      label: '仪表盘',\n    },\n    {\n      key: '/products',\n      icon: <ShoppingOutlined />,\n      label: '产品管理',\n      children: [\n        {\n          key: '/products/list',\n          label: '产品列表',\n        },\n        {\n          key: '/products/category',\n          label: '分类管理',\n        },\n      ],\n    },\n    {\n      key: '/orders',\n      icon: <FileTextOutlined />,\n      label: '订单管理',\n      children: [\n        {\n          key: '/orders/list',\n          label: '全部订单',\n        },\n        {\n          key: '/orders/pending',\n          label: '待处理订单',\n        },\n        {\n          key: '/orders/reviewing',\n          label: '审核中订单',\n        },\n        {\n          key: '/orders/pending-upload',\n          label: '待上传三证',\n        },\n        {\n          key: '/orders/shipped',\n          label: '已发货订单',\n        },\n        {\n          key: '/orders/failed',\n          label: '失败订单',\n        },\n        {\n          key: '/orders/completed',\n          label: '已激活订单',\n        },\n      ],\n    },\n    {\n      key: '/users',\n      icon: <TeamOutlined />,\n      label: '用户管理',\n      children: [\n        {\n          key: '/users/list',\n          label: '用户列表',\n        },\n        {\n          key: '/users/create',\n          label: '添加用户',\n        },\n      ],\n    },\n    {\n      key: '/app',\n      icon: <MobileOutlined />,\n      label: 'APP管理',\n      children: [\n        {\n          key: '/app/config',\n          label: '配置信息',\n        },\n      ],\n    },\n    {\n      key: '/settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '账户设置',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      danger: true,\n    },\n  ];\n\n  // 菜单点击处理\n  const handleMenuClick = ({ key }: { key: string }) => {\n    navigate(key);\n  };\n\n  // 用户菜单点击处理\n  const handleUserMenuClick = ({ key }: { key: string }) => {\n    switch (key) {\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      case 'profile':\n        // 处理个人资料\n        console.log('个人资料');\n        break;\n      case 'settings':\n        // 处理账户设置\n        console.log('账户设置');\n        break;\n      default:\n        break;\n    }\n  };\n\n  // 获取当前选中的菜单项\n  const getSelectedKeys = () => {\n    const path = location.pathname;\n    if (path.startsWith('/products') || path.startsWith('/orders') || path.startsWith('/users') || path.startsWith('/app')) {\n      return [path];\n    }\n    return [path];\n  };\n\n  // 获取当前展开的菜单项\n  const getOpenKeys = () => {\n    const path = location.pathname;\n    if (path.startsWith('/products')) {\n      return ['/products'];\n    }\n    if (path.startsWith('/orders')) {\n      return ['/orders'];\n    }\n    if (path.startsWith('/app')) {\n      return ['/app'];\n    }\n    return [];\n  };\n\n  return (\n    <AntLayout style={{ minHeight: '100vh' }}>\n      {/* 侧边栏 */}\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={collapsed}\n        style={{\n          overflow: 'auto',\n          height: '100vh',\n          position: 'fixed',\n          left: 0,\n          top: 0,\n          bottom: 0,\n          background: '#f5f5f5', // 浅灰色背景\n          borderRight: '1px solid #e8e8e8',\n        }}\n      >\n        {/* Logo */}\n        <div\n          style={{\n            height: 64,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            background: '#ffffff',\n            margin: 16,\n            borderRadius: borderRadiusLG,\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n          }}\n        >\n          <Title\n            level={4}\n            style={{\n              color: '#1890ff',\n              margin: 0,\n              fontSize: collapsed ? 14 : 16,\n              fontWeight: 'bold',\n            }}\n          >\n            {collapsed ? '产品' : '产品管理后台'}\n          </Title>\n        </div>\n\n        {/* 菜单 */}\n        <Menu\n          theme=\"light\"\n          mode=\"inline\"\n          selectedKeys={getSelectedKeys()}\n          defaultOpenKeys={getOpenKeys()}\n          items={menuItems}\n          onClick={handleMenuClick}\n          style={{\n            background: 'transparent',\n            border: 'none',\n            fontSize: '14px',\n          }}\n        />\n      </Sider>\n\n      {/* 主内容区 */}\n      <AntLayout style={{ marginLeft: collapsed ? 80 : 200 }}>\n        {/* 顶部导航 */}\n        <Header\n          style={{\n            padding: '0 24px',\n            background: colorBgContainer,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            borderBottom: '1px solid #f0f0f0',\n          }}\n        >\n          <Space>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{\n                fontSize: '16px',\n                width: 64,\n                height: 64,\n              }}\n            />\n          </Space>\n\n          <Space size=\"large\">\n            {/* 通知 */}\n            <Badge count={5} size=\"small\">\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                style={{ fontSize: '16px' }}\n              />\n            </Badge>\n\n            {/* 用户菜单 */}\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>管理员</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        {/* 内容区域 */}\n        <Content\n          style={{\n            margin: '24px',\n            padding: '24px',\n            minHeight: 280,\n            background: colorBgContainer,\n            borderRadius: borderRadiusLG,\n            overflow: 'auto',\n          }}\n        >\n          <Outlet />\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,IAAIC,SAAS,EACnBC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,KAAK,QACA,MAAM;AACb,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,YAAY,QACP,mBAAmB;AAC1B,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG3B,SAAS;AAC5C,MAAM;EAAE4B;AAAM,CAAC,GAAGxB,UAAU;AAE5B,MAAML,MAAgB,GAAGA,CAAA,KAAM;EAAA8B,EAAA;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMkC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IACJY,KAAK,EAAE;MAAEC,gBAAgB;MAAEC;IAAe;EAC5C,CAAC,GAAGjC,KAAK,CAACkC,QAAQ,CAAC,CAAC;;EAEpB;EACA,MAAMC,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAEhB,OAAA,CAACb,iBAAiB;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEhB,OAAA,CAACZ,gBAAgB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,oBAAoB;MACzBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEhB,OAAA,CAACP,gBAAgB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,cAAc;MACnBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,iBAAiB;MACtBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,mBAAmB;MACxBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,wBAAwB;MAC7BM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,iBAAiB;MACtBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,mBAAmB;MACxBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEhB,OAAA,CAACL,YAAY;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,aAAa;MAClBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,eAAe;MACpBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,MAAM;IACXC,IAAI,eAAEhB,OAAA,CAACN,cAAc;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,aAAa;MAClBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEhB,OAAA,CAACX,eAAe;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAME,aAAa,GAAG,CACpB;IACER,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEhB,OAAA,CAACV,YAAY;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEhB,OAAA,CAACX,eAAe;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEG,IAAI,EAAE;EACR,CAAC,EACD;IACET,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEhB,OAAA,CAACT,cAAc;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbI,MAAM,EAAE;EACV,CAAC,CACF;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAC;IAAEX;EAAqB,CAAC,KAAK;IACpDP,QAAQ,CAACO,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMY,mBAAmB,GAAGA,CAAC;IAAEZ;EAAqB,CAAC,KAAK;IACxD,QAAQA,GAAG;MACT,KAAK,QAAQ;QACX;QACAa,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;QACnB;MACF,KAAK,SAAS;QACZ;QACAD,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;QACnB;MACF,KAAK,UAAU;QACb;QACAD,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;QACnB;MACF;QACE;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,IAAI,GAAGtB,QAAQ,CAACuB,QAAQ;IAC9B,IAAID,IAAI,CAACE,UAAU,CAAC,WAAW,CAAC,IAAIF,IAAI,CAACE,UAAU,CAAC,SAAS,CAAC,IAAIF,IAAI,CAACE,UAAU,CAAC,QAAQ,CAAC,IAAIF,IAAI,CAACE,UAAU,CAAC,MAAM,CAAC,EAAE;MACtH,OAAO,CAACF,IAAI,CAAC;IACf;IACA,OAAO,CAACA,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMH,IAAI,GAAGtB,QAAQ,CAACuB,QAAQ;IAC9B,IAAID,IAAI,CAACE,UAAU,CAAC,WAAW,CAAC,EAAE;MAChC,OAAO,CAAC,WAAW,CAAC;IACtB;IACA,IAAIF,IAAI,CAACE,UAAU,CAAC,SAAS,CAAC,EAAE;MAC9B,OAAO,CAAC,SAAS,CAAC;IACpB;IACA,IAAIF,IAAI,CAACE,UAAU,CAAC,MAAM,CAAC,EAAE;MAC3B,OAAO,CAAC,MAAM,CAAC;IACjB;IACA,OAAO,EAAE;EACX,CAAC;EAED,oBACEjC,OAAA,CAACxB,SAAS;IAAC2D,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAd,QAAA,gBAEvCtB,OAAA,CAACE,KAAK;MACJmC,OAAO,EAAE,IAAK;MACdC,WAAW;MACXhC,SAAS,EAAEA,SAAU;MACrB6B,KAAK,EAAE;QACLI,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE,OAAO;QACjBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE,CAAC;QACTC,UAAU,EAAE,SAAS;QAAE;QACvBC,WAAW,EAAE;MACf,CAAE;MAAAxB,QAAA,gBAGFtB,OAAA;QACEmC,KAAK,EAAE;UACLK,MAAM,EAAE,EAAE;UACVO,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBJ,UAAU,EAAE,SAAS;UACrBK,MAAM,EAAE,EAAE;UACVC,YAAY,EAAEvC,cAAc;UAC5BwC,SAAS,EAAE;QACb,CAAE;QAAA9B,QAAA,eAEFtB,OAAA,CAACI,KAAK;UACJiD,KAAK,EAAE,CAAE;UACTlB,KAAK,EAAE;YACLmB,KAAK,EAAE,SAAS;YAChBJ,MAAM,EAAE,CAAC;YACTK,QAAQ,EAAEjD,SAAS,GAAG,EAAE,GAAG,EAAE;YAC7BkD,UAAU,EAAE;UACd,CAAE;UAAAlC,QAAA,EAEDhB,SAAS,GAAG,IAAI,GAAG;QAAQ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNpB,OAAA,CAACvB,IAAI;QACHE,KAAK,EAAC,OAAO;QACb8E,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE5B,eAAe,CAAC,CAAE;QAChC6B,eAAe,EAAEzB,WAAW,CAAC,CAAE;QAC/B0B,KAAK,EAAE9C,SAAU;QACjB+C,OAAO,EAAEnC,eAAgB;QACzBS,KAAK,EAAE;UACLU,UAAU,EAAE,aAAa;UACzBiB,MAAM,EAAE,MAAM;UACdP,QAAQ,EAAE;QACZ;MAAE;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRpB,OAAA,CAACxB,SAAS;MAAC2D,KAAK,EAAE;QAAE4B,UAAU,EAAEzD,SAAS,GAAG,EAAE,GAAG;MAAI,CAAE;MAAAgB,QAAA,gBAErDtB,OAAA,CAACC,MAAM;QACLkC,KAAK,EAAE;UACL6B,OAAO,EAAE,QAAQ;UACjBnB,UAAU,EAAElC,gBAAgB;UAC5BoC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BgB,YAAY,EAAE;QAChB,CAAE;QAAA3C,QAAA,gBAEFtB,OAAA,CAACnB,KAAK;UAAAyC,QAAA,eACJtB,OAAA,CAACtB,MAAM;YACL8C,IAAI,EAAC,MAAM;YACXR,IAAI,EAAEV,SAAS,gBAAGN,OAAA,CAACd,kBAAkB;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACf,gBAAgB;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEyC,OAAO,EAAEA,CAAA,KAAMtD,YAAY,CAAC,CAACD,SAAS,CAAE;YACxC6B,KAAK,EAAE;cACLoB,QAAQ,EAAE,MAAM;cAChBW,KAAK,EAAE,EAAE;cACT1B,MAAM,EAAE;YACV;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAERpB,OAAA,CAACnB,KAAK;UAACsF,IAAI,EAAC,OAAO;UAAA7C,QAAA,gBAEjBtB,OAAA,CAAChB,KAAK;YAACoF,KAAK,EAAE,CAAE;YAACD,IAAI,EAAC,OAAO;YAAA7C,QAAA,eAC3BtB,OAAA,CAACtB,MAAM;cACL8C,IAAI,EAAC,MAAM;cACXR,IAAI,eAAEhB,OAAA,CAACR,YAAY;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBe,KAAK,EAAE;gBAAEoB,QAAQ,EAAE;cAAO;YAAE;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGRpB,OAAA,CAAClB,QAAQ;YACPuF,IAAI,EAAE;cACJT,KAAK,EAAErC,aAAa;cACpBsC,OAAO,EAAElC;YACX,CAAE;YACF2C,SAAS,EAAC,aAAa;YACvBC,KAAK;YAAAjD,QAAA,eAELtB,OAAA,CAACnB,KAAK;cAACsD,KAAK,EAAE;gBAAEqC,MAAM,EAAE;cAAU,CAAE;cAAAlD,QAAA,gBAClCtB,OAAA,CAACjB,MAAM;gBAACiC,IAAI,eAAEhB,OAAA,CAACV,YAAY;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCpB,OAAA;gBAAAsB,QAAA,EAAM;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGTpB,OAAA,CAACG,OAAO;QACNgC,KAAK,EAAE;UACLe,MAAM,EAAE,MAAM;UACdc,OAAO,EAAE,MAAM;UACf5B,SAAS,EAAE,GAAG;UACdS,UAAU,EAAElC,gBAAgB;UAC5BwC,YAAY,EAAEvC,cAAc;UAC5B2B,QAAQ,EAAE;QACZ,CAAE;QAAAjB,QAAA,eAEFtB,OAAA,CAACJ,MAAM;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACf,EAAA,CA3SI9B,MAAgB;EAAA,QAEHsB,WAAW,EACXC,WAAW,EAGxBnB,KAAK,CAACkC,QAAQ;AAAA;AAAA4D,EAAA,GANdlG,MAAgB;AA6StB,eAAeA,MAAM;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
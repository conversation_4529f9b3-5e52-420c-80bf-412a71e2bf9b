# Nginx配置文件 - h5.haokajiyun.com
# 将此配置添加到您的Nginx配置中

server {
    listen 80;
    listen 443 ssl http2;
    server_name h5.haokajiyun.com;
    
    # SSL配置 - 请根据实际证书路径修改
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 根目录重定向到admin
    location = / {
        return 301 /admin/;
    }
    
    # API代理配置
    location /api/ {
        root /www/wwwroot/h5.haokajiyun.com/public;
        try_files $uri $uri/ /index.php?$query_string;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With";
            add_header Access-Control-Max-Age 86400;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
        
        # PHP处理
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
            
            # 增加超时时间
            fastcgi_read_timeout 300;
            fastcgi_connect_timeout 300;
            fastcgi_send_timeout 300;
        }
    }
    
    # Admin后台配置
    location /admin/ {
        alias /www/wwwroot/h5.haokajiyun.com/admin/;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
        
        # 静态资源缓存策略
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
        }
        
        # HTML文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
        
        # JSON文件不缓存
        location ~* \.json$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log|md)$ {
        deny all;
    }
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 限制请求大小
    client_max_body_size 10M;
    
    # 日志配置
    access_log /var/log/nginx/h5.haokajiyun.com.access.log;
    error_log /var/log/nginx/h5.haokajiyun.com.error.log;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name h5.haokajiyun.com;
    return 301 https://$server_name$request_uri;
}

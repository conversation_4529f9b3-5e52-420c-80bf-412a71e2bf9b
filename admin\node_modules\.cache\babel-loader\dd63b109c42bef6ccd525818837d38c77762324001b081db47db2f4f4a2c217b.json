{"ast": null, "code": "import { useToken } from '../../theme/internal';\n/**\n * This hook is only for cssVar to add root className for components.\n * If root ClassName is needed, this hook could be refactored with `-root`\n * @param prefixCls\n */\nconst useCSSVarCls = prefixCls => {\n  const [,,,, cssVar] = useToken();\n  return cssVar ? \"\".concat(prefixCls, \"-css-var\") : '';\n};\nexport default useCSSVarCls;", "map": {"version": 3, "names": ["useToken", "useCSSVarCls", "prefixCls", "cssVar", "concat"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js"], "sourcesContent": ["import { useToken } from '../../theme/internal';\n/**\n * This hook is only for cssVar to add root className for components.\n * If root ClassName is needed, this hook could be refactored with `-root`\n * @param prefixCls\n */\nconst useCSSVarCls = prefixCls => {\n  const [,,,, cssVar] = useToken();\n  return cssVar ? `${prefixCls}-css-var` : '';\n};\nexport default useCSSVarCls;"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,sBAAsB;AAC/C;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGC,SAAS,IAAI;EAChC,MAAM,MAAMC,MAAM,CAAC,GAAGH,QAAQ,CAAC,CAAC;EAChC,OAAOG,MAAM,MAAAC,MAAA,CAAMF,SAAS,gBAAa,EAAE;AAC7C,CAAC;AACD,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
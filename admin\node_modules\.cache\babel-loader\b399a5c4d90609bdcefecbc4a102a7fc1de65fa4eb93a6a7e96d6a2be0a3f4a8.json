{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { UnstableContext } from \"../context\";\n/** Drag to delete offset. It's a user experience number for dragging out */\nvar REMOVE_DIST = 130;\nfunction getPosition(e) {\n  var obj = 'targetTouches' in e ? e.targetTouches[0] : e;\n  return {\n    pageX: obj.pageX,\n    pageY: obj.pageY\n  };\n}\nfunction useDrag(containerRef, direction, rawValues, min, max, formatValue, triggerChange, finishChange, offsetValues, editable, minCount) {\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    draggingValue = _React$useState2[0],\n    setDraggingValue = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    draggingIndex = _React$useState4[0],\n    setDraggingIndex = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    draggingDelete = _React$useState6[0],\n    setDraggingDelete = _React$useState6[1];\n  var _React$useState7 = React.useState(rawValues),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    cacheValues = _React$useState8[0],\n    setCacheValues = _React$useState8[1];\n  var _React$useState9 = React.useState(rawValues),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    originValues = _React$useState10[0],\n    setOriginValues = _React$useState10[1];\n  var mouseMoveEventRef = React.useRef(null);\n  var mouseUpEventRef = React.useRef(null);\n  var touchEventTargetRef = React.useRef(null);\n  var _React$useContext = React.useContext(UnstableContext),\n    onDragStart = _React$useContext.onDragStart,\n    onDragChange = _React$useContext.onDragChange;\n  useLayoutEffect(function () {\n    if (draggingIndex === -1) {\n      setCacheValues(rawValues);\n    }\n  }, [rawValues, draggingIndex]);\n\n  // Clean up event\n  React.useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveEventRef.current);\n      document.removeEventListener('mouseup', mouseUpEventRef.current);\n      if (touchEventTargetRef.current) {\n        touchEventTargetRef.current.removeEventListener('touchmove', mouseMoveEventRef.current);\n        touchEventTargetRef.current.removeEventListener('touchend', mouseUpEventRef.current);\n      }\n    };\n  }, []);\n  var flushValues = function flushValues(nextValues, nextValue, deleteMark) {\n    // Perf: Only update state when value changed\n    if (nextValue !== undefined) {\n      setDraggingValue(nextValue);\n    }\n    setCacheValues(nextValues);\n    var changeValues = nextValues;\n    if (deleteMark) {\n      changeValues = nextValues.filter(function (_, i) {\n        return i !== draggingIndex;\n      });\n    }\n    triggerChange(changeValues);\n    if (onDragChange) {\n      onDragChange({\n        rawValues: nextValues,\n        deleteIndex: deleteMark ? draggingIndex : -1,\n        draggingIndex: draggingIndex,\n        draggingValue: nextValue\n      });\n    }\n  };\n  var updateCacheValue = useEvent(function (valueIndex, offsetPercent, deleteMark) {\n    if (valueIndex === -1) {\n      // >>>> Dragging on the track\n      var startValue = originValues[0];\n      var endValue = originValues[originValues.length - 1];\n      var maxStartOffset = min - startValue;\n      var maxEndOffset = max - endValue;\n\n      // Get valid offset\n      var offset = offsetPercent * (max - min);\n      offset = Math.max(offset, maxStartOffset);\n      offset = Math.min(offset, maxEndOffset);\n\n      // Use first value to revert back of valid offset (like steps marks)\n      var formatStartValue = formatValue(startValue + offset);\n      offset = formatStartValue - startValue;\n      var cloneCacheValues = originValues.map(function (val) {\n        return val + offset;\n      });\n      flushValues(cloneCacheValues);\n    } else {\n      // >>>> Dragging on the handle\n      var offsetDist = (max - min) * offsetPercent;\n\n      // Always start with the valueIndex origin value\n      var cloneValues = _toConsumableArray(cacheValues);\n      cloneValues[valueIndex] = originValues[valueIndex];\n      var next = offsetValues(cloneValues, offsetDist, valueIndex, 'dist');\n      flushValues(next.values, next.value, deleteMark);\n    }\n  });\n  var onStartMove = function onStartMove(e, valueIndex, startValues) {\n    e.stopPropagation();\n\n    // 如果是点击 track 触发的，需要传入变化后的初始值，而不能直接用 rawValues\n    var initialValues = startValues || rawValues;\n    var originValue = initialValues[valueIndex];\n    setDraggingIndex(valueIndex);\n    setDraggingValue(originValue);\n    setOriginValues(initialValues);\n    setCacheValues(initialValues);\n    setDraggingDelete(false);\n    var _getPosition = getPosition(e),\n      startX = _getPosition.pageX,\n      startY = _getPosition.pageY;\n\n    // We declare it here since closure can't get outer latest value\n    var deleteMark = false;\n\n    // Internal trigger event\n    if (onDragStart) {\n      onDragStart({\n        rawValues: initialValues,\n        draggingIndex: valueIndex,\n        draggingValue: originValue\n      });\n    }\n\n    // Moving\n    var onMouseMove = function onMouseMove(event) {\n      event.preventDefault();\n      var _getPosition2 = getPosition(event),\n        moveX = _getPosition2.pageX,\n        moveY = _getPosition2.pageY;\n      var offsetX = moveX - startX;\n      var offsetY = moveY - startY;\n      var _containerRef$current = containerRef.current.getBoundingClientRect(),\n        width = _containerRef$current.width,\n        height = _containerRef$current.height;\n      var offSetPercent;\n      var removeDist;\n      switch (direction) {\n        case 'btt':\n          offSetPercent = -offsetY / height;\n          removeDist = offsetX;\n          break;\n        case 'ttb':\n          offSetPercent = offsetY / height;\n          removeDist = offsetX;\n          break;\n        case 'rtl':\n          offSetPercent = -offsetX / width;\n          removeDist = offsetY;\n          break;\n        default:\n          offSetPercent = offsetX / width;\n          removeDist = offsetY;\n      }\n\n      // Check if need mark remove\n      deleteMark = editable ? Math.abs(removeDist) > REMOVE_DIST && minCount < cacheValues.length : false;\n      setDraggingDelete(deleteMark);\n      updateCacheValue(valueIndex, offSetPercent, deleteMark);\n    };\n\n    // End\n    var onMouseUp = function onMouseUp(event) {\n      event.preventDefault();\n      document.removeEventListener('mouseup', onMouseUp);\n      document.removeEventListener('mousemove', onMouseMove);\n      if (touchEventTargetRef.current) {\n        touchEventTargetRef.current.removeEventListener('touchmove', mouseMoveEventRef.current);\n        touchEventTargetRef.current.removeEventListener('touchend', mouseUpEventRef.current);\n      }\n      mouseMoveEventRef.current = null;\n      mouseUpEventRef.current = null;\n      touchEventTargetRef.current = null;\n      finishChange(deleteMark);\n      setDraggingIndex(-1);\n      setDraggingDelete(false);\n    };\n    document.addEventListener('mouseup', onMouseUp);\n    document.addEventListener('mousemove', onMouseMove);\n    e.currentTarget.addEventListener('touchend', onMouseUp);\n    e.currentTarget.addEventListener('touchmove', onMouseMove);\n    mouseMoveEventRef.current = onMouseMove;\n    mouseUpEventRef.current = onMouseUp;\n    touchEventTargetRef.current = e.currentTarget;\n  };\n\n  // Only return cache value when it mapping with rawValues\n  var returnValues = React.useMemo(function () {\n    var sourceValues = _toConsumableArray(rawValues).sort(function (a, b) {\n      return a - b;\n    });\n    var targetValues = _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n    var counts = {};\n    targetValues.forEach(function (val) {\n      counts[val] = (counts[val] || 0) + 1;\n    });\n    sourceValues.forEach(function (val) {\n      counts[val] = (counts[val] || 0) - 1;\n    });\n    var maxDiffCount = editable ? 1 : 0;\n    var diffCount = Object.values(counts).reduce(function (prev, next) {\n      return prev + Math.abs(next);\n    }, 0);\n    return diffCount <= maxDiffCount ? cacheValues : rawValues;\n  }, [rawValues, cacheValues, editable]);\n  return [draggingIndex, draggingValue, draggingDelete, returnValues, onStartMove];\n}\nexport default useDrag;", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "React", "useEvent", "useLayoutEffect", "UnstableContext", "REMOVE_DIST", "getPosition", "e", "obj", "targetTouches", "pageX", "pageY", "useDrag", "containerRef", "direction", "rawValues", "min", "max", "formatValue", "trigger<PERSON>hange", "finishChange", "offsetValues", "editable", "minCount", "_React$useState", "useState", "_React$useState2", "draggingValue", "setDraggingValue", "_React$useState3", "_React$useState4", "draggingIndex", "setDraggingIndex", "_React$useState5", "_React$useState6", "draggingDelete", "setDraggingDelete", "_React$useState7", "_React$useState8", "cacheValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_React$useState9", "_React$useState10", "originValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mouseMoveEventRef", "useRef", "mouseUpEventRef", "touchEventTargetRef", "_React$useContext", "useContext", "onDragStart", "onDragChange", "useEffect", "document", "removeEventListener", "current", "flushV<PERSON>ues", "nextV<PERSON>ues", "nextValue", "deleteMark", "undefined", "changeValues", "filter", "_", "i", "deleteIndex", "updateCacheValue", "valueIndex", "offsetPercent", "startValue", "endValue", "length", "maxStartOffset", "maxEndOffset", "offset", "Math", "formatStartValue", "clone<PERSON>ache<PERSON><PERSON><PERSON>", "map", "val", "offsetDist", "clone<PERSON><PERSON>ues", "next", "values", "value", "onStartMove", "startValues", "stopPropagation", "initialValues", "originValue", "_getPosition", "startX", "startY", "onMouseMove", "event", "preventDefault", "_getPosition2", "moveX", "moveY", "offsetX", "offsetY", "_containerRef$current", "getBoundingClientRect", "width", "height", "offSetPercent", "removeDist", "abs", "onMouseUp", "addEventListener", "currentTarget", "returnV<PERSON>ues", "useMemo", "sourceValues", "sort", "a", "b", "targetValues", "counts", "for<PERSON>ach", "max<PERSON>iff<PERSON>ount", "diffCount", "Object", "reduce", "prev"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-slider/es/hooks/useDrag.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { UnstableContext } from \"../context\";\n/** Drag to delete offset. It's a user experience number for dragging out */\nvar REMOVE_DIST = 130;\nfunction getPosition(e) {\n  var obj = 'targetTouches' in e ? e.targetTouches[0] : e;\n  return {\n    pageX: obj.pageX,\n    pageY: obj.pageY\n  };\n}\nfunction useDrag(containerRef, direction, rawValues, min, max, formatValue, triggerChange, finishChange, offsetValues, editable, minCount) {\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    draggingValue = _React$useState2[0],\n    setDraggingValue = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    draggingIndex = _React$useState4[0],\n    setDraggingIndex = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    draggingDelete = _React$useState6[0],\n    setDraggingDelete = _React$useState6[1];\n  var _React$useState7 = React.useState(rawValues),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    cacheValues = _React$useState8[0],\n    setCacheValues = _React$useState8[1];\n  var _React$useState9 = React.useState(rawValues),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    originValues = _React$useState10[0],\n    setOriginValues = _React$useState10[1];\n  var mouseMoveEventRef = React.useRef(null);\n  var mouseUpEventRef = React.useRef(null);\n  var touchEventTargetRef = React.useRef(null);\n  var _React$useContext = React.useContext(UnstableContext),\n    onDragStart = _React$useContext.onDragStart,\n    onDragChange = _React$useContext.onDragChange;\n  useLayoutEffect(function () {\n    if (draggingIndex === -1) {\n      setCacheValues(rawValues);\n    }\n  }, [rawValues, draggingIndex]);\n\n  // Clean up event\n  React.useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveEventRef.current);\n      document.removeEventListener('mouseup', mouseUpEventRef.current);\n      if (touchEventTargetRef.current) {\n        touchEventTargetRef.current.removeEventListener('touchmove', mouseMoveEventRef.current);\n        touchEventTargetRef.current.removeEventListener('touchend', mouseUpEventRef.current);\n      }\n    };\n  }, []);\n  var flushValues = function flushValues(nextValues, nextValue, deleteMark) {\n    // Perf: Only update state when value changed\n    if (nextValue !== undefined) {\n      setDraggingValue(nextValue);\n    }\n    setCacheValues(nextValues);\n    var changeValues = nextValues;\n    if (deleteMark) {\n      changeValues = nextValues.filter(function (_, i) {\n        return i !== draggingIndex;\n      });\n    }\n    triggerChange(changeValues);\n    if (onDragChange) {\n      onDragChange({\n        rawValues: nextValues,\n        deleteIndex: deleteMark ? draggingIndex : -1,\n        draggingIndex: draggingIndex,\n        draggingValue: nextValue\n      });\n    }\n  };\n  var updateCacheValue = useEvent(function (valueIndex, offsetPercent, deleteMark) {\n    if (valueIndex === -1) {\n      // >>>> Dragging on the track\n      var startValue = originValues[0];\n      var endValue = originValues[originValues.length - 1];\n      var maxStartOffset = min - startValue;\n      var maxEndOffset = max - endValue;\n\n      // Get valid offset\n      var offset = offsetPercent * (max - min);\n      offset = Math.max(offset, maxStartOffset);\n      offset = Math.min(offset, maxEndOffset);\n\n      // Use first value to revert back of valid offset (like steps marks)\n      var formatStartValue = formatValue(startValue + offset);\n      offset = formatStartValue - startValue;\n      var cloneCacheValues = originValues.map(function (val) {\n        return val + offset;\n      });\n      flushValues(cloneCacheValues);\n    } else {\n      // >>>> Dragging on the handle\n      var offsetDist = (max - min) * offsetPercent;\n\n      // Always start with the valueIndex origin value\n      var cloneValues = _toConsumableArray(cacheValues);\n      cloneValues[valueIndex] = originValues[valueIndex];\n      var next = offsetValues(cloneValues, offsetDist, valueIndex, 'dist');\n      flushValues(next.values, next.value, deleteMark);\n    }\n  });\n  var onStartMove = function onStartMove(e, valueIndex, startValues) {\n    e.stopPropagation();\n\n    // 如果是点击 track 触发的，需要传入变化后的初始值，而不能直接用 rawValues\n    var initialValues = startValues || rawValues;\n    var originValue = initialValues[valueIndex];\n    setDraggingIndex(valueIndex);\n    setDraggingValue(originValue);\n    setOriginValues(initialValues);\n    setCacheValues(initialValues);\n    setDraggingDelete(false);\n    var _getPosition = getPosition(e),\n      startX = _getPosition.pageX,\n      startY = _getPosition.pageY;\n\n    // We declare it here since closure can't get outer latest value\n    var deleteMark = false;\n\n    // Internal trigger event\n    if (onDragStart) {\n      onDragStart({\n        rawValues: initialValues,\n        draggingIndex: valueIndex,\n        draggingValue: originValue\n      });\n    }\n\n    // Moving\n    var onMouseMove = function onMouseMove(event) {\n      event.preventDefault();\n      var _getPosition2 = getPosition(event),\n        moveX = _getPosition2.pageX,\n        moveY = _getPosition2.pageY;\n      var offsetX = moveX - startX;\n      var offsetY = moveY - startY;\n      var _containerRef$current = containerRef.current.getBoundingClientRect(),\n        width = _containerRef$current.width,\n        height = _containerRef$current.height;\n      var offSetPercent;\n      var removeDist;\n      switch (direction) {\n        case 'btt':\n          offSetPercent = -offsetY / height;\n          removeDist = offsetX;\n          break;\n        case 'ttb':\n          offSetPercent = offsetY / height;\n          removeDist = offsetX;\n          break;\n        case 'rtl':\n          offSetPercent = -offsetX / width;\n          removeDist = offsetY;\n          break;\n        default:\n          offSetPercent = offsetX / width;\n          removeDist = offsetY;\n      }\n\n      // Check if need mark remove\n      deleteMark = editable ? Math.abs(removeDist) > REMOVE_DIST && minCount < cacheValues.length : false;\n      setDraggingDelete(deleteMark);\n      updateCacheValue(valueIndex, offSetPercent, deleteMark);\n    };\n\n    // End\n    var onMouseUp = function onMouseUp(event) {\n      event.preventDefault();\n      document.removeEventListener('mouseup', onMouseUp);\n      document.removeEventListener('mousemove', onMouseMove);\n      if (touchEventTargetRef.current) {\n        touchEventTargetRef.current.removeEventListener('touchmove', mouseMoveEventRef.current);\n        touchEventTargetRef.current.removeEventListener('touchend', mouseUpEventRef.current);\n      }\n      mouseMoveEventRef.current = null;\n      mouseUpEventRef.current = null;\n      touchEventTargetRef.current = null;\n      finishChange(deleteMark);\n      setDraggingIndex(-1);\n      setDraggingDelete(false);\n    };\n    document.addEventListener('mouseup', onMouseUp);\n    document.addEventListener('mousemove', onMouseMove);\n    e.currentTarget.addEventListener('touchend', onMouseUp);\n    e.currentTarget.addEventListener('touchmove', onMouseMove);\n    mouseMoveEventRef.current = onMouseMove;\n    mouseUpEventRef.current = onMouseUp;\n    touchEventTargetRef.current = e.currentTarget;\n  };\n\n  // Only return cache value when it mapping with rawValues\n  var returnValues = React.useMemo(function () {\n    var sourceValues = _toConsumableArray(rawValues).sort(function (a, b) {\n      return a - b;\n    });\n    var targetValues = _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n    var counts = {};\n    targetValues.forEach(function (val) {\n      counts[val] = (counts[val] || 0) + 1;\n    });\n    sourceValues.forEach(function (val) {\n      counts[val] = (counts[val] || 0) - 1;\n    });\n    var maxDiffCount = editable ? 1 : 0;\n    var diffCount = Object.values(counts).reduce(function (prev, next) {\n      return prev + Math.abs(next);\n    }, 0);\n    return diffCount <= maxDiffCount ? cacheValues : rawValues;\n  }, [rawValues, cacheValues, editable]);\n  return [draggingIndex, draggingValue, draggingDelete, returnValues, onStartMove];\n}\nexport default useDrag;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,eAAe,QAAQ,YAAY;AAC5C;AACA,IAAIC,WAAW,GAAG,GAAG;AACrB,SAASC,WAAWA,CAACC,CAAC,EAAE;EACtB,IAAIC,GAAG,GAAG,eAAe,IAAID,CAAC,GAAGA,CAAC,CAACE,aAAa,CAAC,CAAC,CAAC,GAAGF,CAAC;EACvD,OAAO;IACLG,KAAK,EAAEF,GAAG,CAACE,KAAK;IAChBC,KAAK,EAAEH,GAAG,CAACG;EACb,CAAC;AACH;AACA,SAASC,OAAOA,CAACC,YAAY,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACzI,IAAIC,eAAe,GAAGvB,KAAK,CAACwB,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAG1B,cAAc,CAACwB,eAAe,EAAE,CAAC,CAAC;IACrDG,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,gBAAgB,GAAG5B,KAAK,CAACwB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvCK,gBAAgB,GAAG9B,cAAc,CAAC6B,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,gBAAgB,GAAGhC,KAAK,CAACwB,QAAQ,CAAC,KAAK,CAAC;IAC1CS,gBAAgB,GAAGlC,cAAc,CAACiC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,gBAAgB,GAAGpC,KAAK,CAACwB,QAAQ,CAACV,SAAS,CAAC;IAC9CuB,gBAAgB,GAAGtC,cAAc,CAACqC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAGxC,KAAK,CAACwB,QAAQ,CAACV,SAAS,CAAC;IAC9C2B,iBAAiB,GAAG1C,cAAc,CAACyC,gBAAgB,EAAE,CAAC,CAAC;IACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACxC,IAAIG,iBAAiB,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EAC1C,IAAIC,eAAe,GAAG9C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACxC,IAAIE,mBAAmB,GAAG/C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EAC5C,IAAIG,iBAAiB,GAAGhD,KAAK,CAACiD,UAAU,CAAC9C,eAAe,CAAC;IACvD+C,WAAW,GAAGF,iBAAiB,CAACE,WAAW;IAC3CC,YAAY,GAAGH,iBAAiB,CAACG,YAAY;EAC/CjD,eAAe,CAAC,YAAY;IAC1B,IAAI4B,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBS,cAAc,CAACzB,SAAS,CAAC;IAC3B;EACF,CAAC,EAAE,CAACA,SAAS,EAAEgB,aAAa,CAAC,CAAC;;EAE9B;EACA9B,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBC,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEV,iBAAiB,CAACW,OAAO,CAAC;MACpEF,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAER,eAAe,CAACS,OAAO,CAAC;MAChE,IAAIR,mBAAmB,CAACQ,OAAO,EAAE;QAC/BR,mBAAmB,CAACQ,OAAO,CAACD,mBAAmB,CAAC,WAAW,EAAEV,iBAAiB,CAACW,OAAO,CAAC;QACvFR,mBAAmB,CAACQ,OAAO,CAACD,mBAAmB,CAAC,UAAU,EAAER,eAAe,CAACS,OAAO,CAAC;MACtF;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,UAAU,EAAEC,SAAS,EAAEC,UAAU,EAAE;IACxE;IACA,IAAID,SAAS,KAAKE,SAAS,EAAE;MAC3BjC,gBAAgB,CAAC+B,SAAS,CAAC;IAC7B;IACAnB,cAAc,CAACkB,UAAU,CAAC;IAC1B,IAAII,YAAY,GAAGJ,UAAU;IAC7B,IAAIE,UAAU,EAAE;MACdE,YAAY,GAAGJ,UAAU,CAACK,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAC/C,OAAOA,CAAC,KAAKlC,aAAa;MAC5B,CAAC,CAAC;IACJ;IACAZ,aAAa,CAAC2C,YAAY,CAAC;IAC3B,IAAIV,YAAY,EAAE;MAChBA,YAAY,CAAC;QACXrC,SAAS,EAAE2C,UAAU;QACrBQ,WAAW,EAAEN,UAAU,GAAG7B,aAAa,GAAG,CAAC,CAAC;QAC5CA,aAAa,EAAEA,aAAa;QAC5BJ,aAAa,EAAEgC;MACjB,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIQ,gBAAgB,GAAGjE,QAAQ,CAAC,UAAUkE,UAAU,EAAEC,aAAa,EAAET,UAAU,EAAE;IAC/E,IAAIQ,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB;MACA,IAAIE,UAAU,GAAG3B,YAAY,CAAC,CAAC,CAAC;MAChC,IAAI4B,QAAQ,GAAG5B,YAAY,CAACA,YAAY,CAAC6B,MAAM,GAAG,CAAC,CAAC;MACpD,IAAIC,cAAc,GAAGzD,GAAG,GAAGsD,UAAU;MACrC,IAAII,YAAY,GAAGzD,GAAG,GAAGsD,QAAQ;;MAEjC;MACA,IAAII,MAAM,GAAGN,aAAa,IAAIpD,GAAG,GAAGD,GAAG,CAAC;MACxC2D,MAAM,GAAGC,IAAI,CAAC3D,GAAG,CAAC0D,MAAM,EAAEF,cAAc,CAAC;MACzCE,MAAM,GAAGC,IAAI,CAAC5D,GAAG,CAAC2D,MAAM,EAAED,YAAY,CAAC;;MAEvC;MACA,IAAIG,gBAAgB,GAAG3D,WAAW,CAACoD,UAAU,GAAGK,MAAM,CAAC;MACvDA,MAAM,GAAGE,gBAAgB,GAAGP,UAAU;MACtC,IAAIQ,gBAAgB,GAAGnC,YAAY,CAACoC,GAAG,CAAC,UAAUC,GAAG,EAAE;QACrD,OAAOA,GAAG,GAAGL,MAAM;MACrB,CAAC,CAAC;MACFlB,WAAW,CAACqB,gBAAgB,CAAC;IAC/B,CAAC,MAAM;MACL;MACA,IAAIG,UAAU,GAAG,CAAChE,GAAG,GAAGD,GAAG,IAAIqD,aAAa;;MAE5C;MACA,IAAIa,WAAW,GAAGnF,kBAAkB,CAACwC,WAAW,CAAC;MACjD2C,WAAW,CAACd,UAAU,CAAC,GAAGzB,YAAY,CAACyB,UAAU,CAAC;MAClD,IAAIe,IAAI,GAAG9D,YAAY,CAAC6D,WAAW,EAAED,UAAU,EAAEb,UAAU,EAAE,MAAM,CAAC;MACpEX,WAAW,CAAC0B,IAAI,CAACC,MAAM,EAAED,IAAI,CAACE,KAAK,EAAEzB,UAAU,CAAC;IAClD;EACF,CAAC,CAAC;EACF,IAAI0B,WAAW,GAAG,SAASA,WAAWA,CAAC/E,CAAC,EAAE6D,UAAU,EAAEmB,WAAW,EAAE;IACjEhF,CAAC,CAACiF,eAAe,CAAC,CAAC;;IAEnB;IACA,IAAIC,aAAa,GAAGF,WAAW,IAAIxE,SAAS;IAC5C,IAAI2E,WAAW,GAAGD,aAAa,CAACrB,UAAU,CAAC;IAC3CpC,gBAAgB,CAACoC,UAAU,CAAC;IAC5BxC,gBAAgB,CAAC8D,WAAW,CAAC;IAC7B9C,eAAe,CAAC6C,aAAa,CAAC;IAC9BjD,cAAc,CAACiD,aAAa,CAAC;IAC7BrD,iBAAiB,CAAC,KAAK,CAAC;IACxB,IAAIuD,YAAY,GAAGrF,WAAW,CAACC,CAAC,CAAC;MAC/BqF,MAAM,GAAGD,YAAY,CAACjF,KAAK;MAC3BmF,MAAM,GAAGF,YAAY,CAAChF,KAAK;;IAE7B;IACA,IAAIiD,UAAU,GAAG,KAAK;;IAEtB;IACA,IAAIT,WAAW,EAAE;MACfA,WAAW,CAAC;QACVpC,SAAS,EAAE0E,aAAa;QACxB1D,aAAa,EAAEqC,UAAU;QACzBzC,aAAa,EAAE+D;MACjB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAII,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;MAC5CA,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB,IAAIC,aAAa,GAAG3F,WAAW,CAACyF,KAAK,CAAC;QACpCG,KAAK,GAAGD,aAAa,CAACvF,KAAK;QAC3ByF,KAAK,GAAGF,aAAa,CAACtF,KAAK;MAC7B,IAAIyF,OAAO,GAAGF,KAAK,GAAGN,MAAM;MAC5B,IAAIS,OAAO,GAAGF,KAAK,GAAGN,MAAM;MAC5B,IAAIS,qBAAqB,GAAGzF,YAAY,CAAC2C,OAAO,CAAC+C,qBAAqB,CAAC,CAAC;QACtEC,KAAK,GAAGF,qBAAqB,CAACE,KAAK;QACnCC,MAAM,GAAGH,qBAAqB,CAACG,MAAM;MACvC,IAAIC,aAAa;MACjB,IAAIC,UAAU;MACd,QAAQ7F,SAAS;QACf,KAAK,KAAK;UACR4F,aAAa,GAAG,CAACL,OAAO,GAAGI,MAAM;UACjCE,UAAU,GAAGP,OAAO;UACpB;QACF,KAAK,KAAK;UACRM,aAAa,GAAGL,OAAO,GAAGI,MAAM;UAChCE,UAAU,GAAGP,OAAO;UACpB;QACF,KAAK,KAAK;UACRM,aAAa,GAAG,CAACN,OAAO,GAAGI,KAAK;UAChCG,UAAU,GAAGN,OAAO;UACpB;QACF;UACEK,aAAa,GAAGN,OAAO,GAAGI,KAAK;UAC/BG,UAAU,GAAGN,OAAO;MACxB;;MAEA;MACAzC,UAAU,GAAGtC,QAAQ,GAAGsD,IAAI,CAACgC,GAAG,CAACD,UAAU,CAAC,GAAGtG,WAAW,IAAIkB,QAAQ,GAAGgB,WAAW,CAACiC,MAAM,GAAG,KAAK;MACnGpC,iBAAiB,CAACwB,UAAU,CAAC;MAC7BO,gBAAgB,CAACC,UAAU,EAAEsC,aAAa,EAAE9C,UAAU,CAAC;IACzD,CAAC;;IAED;IACA,IAAIiD,SAAS,GAAG,SAASA,SAASA,CAACd,KAAK,EAAE;MACxCA,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB1C,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEsD,SAAS,CAAC;MAClDvD,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEuC,WAAW,CAAC;MACtD,IAAI9C,mBAAmB,CAACQ,OAAO,EAAE;QAC/BR,mBAAmB,CAACQ,OAAO,CAACD,mBAAmB,CAAC,WAAW,EAAEV,iBAAiB,CAACW,OAAO,CAAC;QACvFR,mBAAmB,CAACQ,OAAO,CAACD,mBAAmB,CAAC,UAAU,EAAER,eAAe,CAACS,OAAO,CAAC;MACtF;MACAX,iBAAiB,CAACW,OAAO,GAAG,IAAI;MAChCT,eAAe,CAACS,OAAO,GAAG,IAAI;MAC9BR,mBAAmB,CAACQ,OAAO,GAAG,IAAI;MAClCpC,YAAY,CAACwC,UAAU,CAAC;MACxB5B,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACpBI,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC;IACDkB,QAAQ,CAACwD,gBAAgB,CAAC,SAAS,EAAED,SAAS,CAAC;IAC/CvD,QAAQ,CAACwD,gBAAgB,CAAC,WAAW,EAAEhB,WAAW,CAAC;IACnDvF,CAAC,CAACwG,aAAa,CAACD,gBAAgB,CAAC,UAAU,EAAED,SAAS,CAAC;IACvDtG,CAAC,CAACwG,aAAa,CAACD,gBAAgB,CAAC,WAAW,EAAEhB,WAAW,CAAC;IAC1DjD,iBAAiB,CAACW,OAAO,GAAGsC,WAAW;IACvC/C,eAAe,CAACS,OAAO,GAAGqD,SAAS;IACnC7D,mBAAmB,CAACQ,OAAO,GAAGjD,CAAC,CAACwG,aAAa;EAC/C,CAAC;;EAED;EACA,IAAIC,YAAY,GAAG/G,KAAK,CAACgH,OAAO,CAAC,YAAY;IAC3C,IAAIC,YAAY,GAAGnH,kBAAkB,CAACgB,SAAS,CAAC,CAACoG,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACpE,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC,CAAC;IACF,IAAIC,YAAY,GAAGvH,kBAAkB,CAACwC,WAAW,CAAC,CAAC4E,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACtE,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC,CAAC;IACF,IAAIE,MAAM,GAAG,CAAC,CAAC;IACfD,YAAY,CAACE,OAAO,CAAC,UAAUxC,GAAG,EAAE;MAClCuC,MAAM,CAACvC,GAAG,CAAC,GAAG,CAACuC,MAAM,CAACvC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACtC,CAAC,CAAC;IACFkC,YAAY,CAACM,OAAO,CAAC,UAAUxC,GAAG,EAAE;MAClCuC,MAAM,CAACvC,GAAG,CAAC,GAAG,CAACuC,MAAM,CAACvC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACtC,CAAC,CAAC;IACF,IAAIyC,YAAY,GAAGnG,QAAQ,GAAG,CAAC,GAAG,CAAC;IACnC,IAAIoG,SAAS,GAAGC,MAAM,CAACvC,MAAM,CAACmC,MAAM,CAAC,CAACK,MAAM,CAAC,UAAUC,IAAI,EAAE1C,IAAI,EAAE;MACjE,OAAO0C,IAAI,GAAGjD,IAAI,CAACgC,GAAG,CAACzB,IAAI,CAAC;IAC9B,CAAC,EAAE,CAAC,CAAC;IACL,OAAOuC,SAAS,IAAID,YAAY,GAAGlF,WAAW,GAAGxB,SAAS;EAC5D,CAAC,EAAE,CAACA,SAAS,EAAEwB,WAAW,EAAEjB,QAAQ,CAAC,CAAC;EACtC,OAAO,CAACS,aAAa,EAAEJ,aAAa,EAAEQ,cAAc,EAAE6E,YAAY,EAAE1B,WAAW,CAAC;AAClF;AACA,eAAe1E,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"rootClassName\", \"style\", \"className\", \"tabIndex\", \"items\", \"children\", \"direction\", \"id\", \"mode\", \"inlineCollapsed\", \"disabled\", \"disabledOverflow\", \"subMenuOpenDelay\", \"subMenuCloseDelay\", \"forceSubMenuRender\", \"defaultOpenKeys\", \"openKeys\", \"activeKey\", \"defaultActiveFirst\", \"selectable\", \"multiple\", \"defaultSelectedKeys\", \"selectedKeys\", \"onSelect\", \"onDeselect\", \"inlineIndent\", \"motion\", \"defaultMotions\", \"triggerSubMenuAction\", \"builtinPlacements\", \"itemIcon\", \"expandIcon\", \"overflowedIndicator\", \"overflowedIndicatorPopupClassName\", \"getPopupContainer\", \"onClick\", \"onOpenChange\", \"onKeyDown\", \"openAnimation\", \"openTransitionName\", \"_internalRenderMenuItem\", \"_internalRenderSubMenuItem\", \"_internalComponents\"];\nimport classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { useImperativeHandle } from 'react';\nimport { flushSync } from 'react-dom';\nimport { IdContext } from \"./context/IdContext\";\nimport MenuContextProvider from \"./context/MenuContext\";\nimport { PathRegisterContext, PathUserContext } from \"./context/PathContext\";\nimport PrivateContext from \"./context/PrivateContext\";\nimport { getFocusableElements, refreshElements, useAccessibility } from \"./hooks/useAccessibility\";\nimport useKeyRecords, { OVERFLOW_KEY } from \"./hooks/useKeyRecords\";\nimport useMemoCallback from \"./hooks/useMemoCallback\";\nimport useUUID from \"./hooks/useUUID\";\nimport MenuItem from \"./MenuItem\";\nimport SubMenu from \"./SubMenu\";\nimport { parseItems } from \"./utils/nodeUtil\";\nimport { warnItemProp } from \"./utils/warnUtil\";\n\n/**\n * Menu modify after refactor:\n * ## Add\n * - disabled\n *\n * ## Remove\n * - openTransitionName\n * - openAnimation\n * - onDestroy\n * - siderCollapsed: Seems antd do not use this prop (Need test in antd)\n * - collapsedWidth: Seems this logic should be handle by antd Layout.Sider\n */\n\n// optimize for render\nvar EMPTY_LIST = [];\nvar Menu = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _childList$;\n  var _ref = props,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-menu' : _ref$prefixCls,\n    rootClassName = _ref.rootClassName,\n    style = _ref.style,\n    className = _ref.className,\n    _ref$tabIndex = _ref.tabIndex,\n    tabIndex = _ref$tabIndex === void 0 ? 0 : _ref$tabIndex,\n    items = _ref.items,\n    children = _ref.children,\n    direction = _ref.direction,\n    id = _ref.id,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'vertical' : _ref$mode,\n    inlineCollapsed = _ref.inlineCollapsed,\n    disabled = _ref.disabled,\n    disabledOverflow = _ref.disabledOverflow,\n    _ref$subMenuOpenDelay = _ref.subMenuOpenDelay,\n    subMenuOpenDelay = _ref$subMenuOpenDelay === void 0 ? 0.1 : _ref$subMenuOpenDelay,\n    _ref$subMenuCloseDela = _ref.subMenuCloseDelay,\n    subMenuCloseDelay = _ref$subMenuCloseDela === void 0 ? 0.1 : _ref$subMenuCloseDela,\n    forceSubMenuRender = _ref.forceSubMenuRender,\n    defaultOpenKeys = _ref.defaultOpenKeys,\n    openKeys = _ref.openKeys,\n    activeKey = _ref.activeKey,\n    defaultActiveFirst = _ref.defaultActiveFirst,\n    _ref$selectable = _ref.selectable,\n    selectable = _ref$selectable === void 0 ? true : _ref$selectable,\n    _ref$multiple = _ref.multiple,\n    multiple = _ref$multiple === void 0 ? false : _ref$multiple,\n    defaultSelectedKeys = _ref.defaultSelectedKeys,\n    selectedKeys = _ref.selectedKeys,\n    onSelect = _ref.onSelect,\n    onDeselect = _ref.onDeselect,\n    _ref$inlineIndent = _ref.inlineIndent,\n    inlineIndent = _ref$inlineIndent === void 0 ? 24 : _ref$inlineIndent,\n    motion = _ref.motion,\n    defaultMotions = _ref.defaultMotions,\n    _ref$triggerSubMenuAc = _ref.triggerSubMenuAction,\n    triggerSubMenuAction = _ref$triggerSubMenuAc === void 0 ? 'hover' : _ref$triggerSubMenuAc,\n    builtinPlacements = _ref.builtinPlacements,\n    itemIcon = _ref.itemIcon,\n    expandIcon = _ref.expandIcon,\n    _ref$overflowedIndica = _ref.overflowedIndicator,\n    overflowedIndicator = _ref$overflowedIndica === void 0 ? '...' : _ref$overflowedIndica,\n    overflowedIndicatorPopupClassName = _ref.overflowedIndicatorPopupClassName,\n    getPopupContainer = _ref.getPopupContainer,\n    onClick = _ref.onClick,\n    onOpenChange = _ref.onOpenChange,\n    onKeyDown = _ref.onKeyDown,\n    openAnimation = _ref.openAnimation,\n    openTransitionName = _ref.openTransitionName,\n    _internalRenderMenuItem = _ref._internalRenderMenuItem,\n    _internalRenderSubMenuItem = _ref._internalRenderSubMenuItem,\n    _internalComponents = _ref._internalComponents,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useMemo = React.useMemo(function () {\n      return [parseItems(children, items, EMPTY_LIST, _internalComponents, prefixCls), parseItems(children, items, EMPTY_LIST, {}, prefixCls)];\n    }, [children, items, _internalComponents]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    childList = _React$useMemo2[0],\n    measureChildList = _React$useMemo2[1];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mounted = _React$useState2[0],\n    setMounted = _React$useState2[1];\n  var containerRef = React.useRef();\n  var uuid = useUUID(id);\n  var isRtl = direction === 'rtl';\n\n  // ========================= Warn =========================\n  if (process.env.NODE_ENV !== 'production') {\n    warning(!openAnimation && !openTransitionName, '`openAnimation` and `openTransitionName` is removed. Please use `motion` or `defaultMotion` instead.');\n  }\n\n  // ========================= Open =========================\n  var _useMergedState = useMergedState(defaultOpenKeys, {\n      value: openKeys,\n      postState: function postState(keys) {\n        return keys || EMPTY_LIST;\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedOpenKeys = _useMergedState2[0],\n    setMergedOpenKeys = _useMergedState2[1];\n\n  // React 18 will merge mouse event which means we open key will not sync\n  // ref: https://github.com/ant-design/ant-design/issues/38818\n  var triggerOpenKeys = function triggerOpenKeys(keys) {\n    var forceFlush = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    function doUpdate() {\n      setMergedOpenKeys(keys);\n      onOpenChange === null || onOpenChange === void 0 || onOpenChange(keys);\n    }\n    if (forceFlush) {\n      flushSync(doUpdate);\n    } else {\n      doUpdate();\n    }\n  };\n\n  // >>>>> Cache & Reset open keys when inlineCollapsed changed\n  var _React$useState3 = React.useState(mergedOpenKeys),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    inlineCacheOpenKeys = _React$useState4[0],\n    setInlineCacheOpenKeys = _React$useState4[1];\n  var mountRef = React.useRef(false);\n\n  // ========================= Mode =========================\n  var _React$useMemo3 = React.useMemo(function () {\n      if ((mode === 'inline' || mode === 'vertical') && inlineCollapsed) {\n        return ['vertical', inlineCollapsed];\n      }\n      return [mode, false];\n    }, [mode, inlineCollapsed]),\n    _React$useMemo4 = _slicedToArray(_React$useMemo3, 2),\n    mergedMode = _React$useMemo4[0],\n    mergedInlineCollapsed = _React$useMemo4[1];\n  var isInlineMode = mergedMode === 'inline';\n  var _React$useState5 = React.useState(mergedMode),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    internalMode = _React$useState6[0],\n    setInternalMode = _React$useState6[1];\n  var _React$useState7 = React.useState(mergedInlineCollapsed),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    internalInlineCollapsed = _React$useState8[0],\n    setInternalInlineCollapsed = _React$useState8[1];\n  React.useEffect(function () {\n    setInternalMode(mergedMode);\n    setInternalInlineCollapsed(mergedInlineCollapsed);\n    if (!mountRef.current) {\n      return;\n    }\n    // Synchronously update MergedOpenKeys\n    if (isInlineMode) {\n      setMergedOpenKeys(inlineCacheOpenKeys);\n    } else {\n      // Trigger open event in case its in control\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  }, [mergedMode, mergedInlineCollapsed]);\n\n  // ====================== Responsive ======================\n  var _React$useState9 = React.useState(0),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    lastVisibleIndex = _React$useState10[0],\n    setLastVisibleIndex = _React$useState10[1];\n  var allVisible = lastVisibleIndex >= childList.length - 1 || internalMode !== 'horizontal' || disabledOverflow;\n\n  // Cache\n  React.useEffect(function () {\n    if (isInlineMode) {\n      setInlineCacheOpenKeys(mergedOpenKeys);\n    }\n  }, [mergedOpenKeys]);\n  React.useEffect(function () {\n    mountRef.current = true;\n    return function () {\n      mountRef.current = false;\n    };\n  }, []);\n\n  // ========================= Path =========================\n  var _useKeyRecords = useKeyRecords(),\n    registerPath = _useKeyRecords.registerPath,\n    unregisterPath = _useKeyRecords.unregisterPath,\n    refreshOverflowKeys = _useKeyRecords.refreshOverflowKeys,\n    isSubPathKey = _useKeyRecords.isSubPathKey,\n    getKeyPath = _useKeyRecords.getKeyPath,\n    getKeys = _useKeyRecords.getKeys,\n    getSubPathKeys = _useKeyRecords.getSubPathKeys;\n  var registerPathContext = React.useMemo(function () {\n    return {\n      registerPath: registerPath,\n      unregisterPath: unregisterPath\n    };\n  }, [registerPath, unregisterPath]);\n  var pathUserContext = React.useMemo(function () {\n    return {\n      isSubPathKey: isSubPathKey\n    };\n  }, [isSubPathKey]);\n  React.useEffect(function () {\n    refreshOverflowKeys(allVisible ? EMPTY_LIST : childList.slice(lastVisibleIndex + 1).map(function (child) {\n      return child.key;\n    }));\n  }, [lastVisibleIndex, allVisible]);\n\n  // ======================== Active ========================\n  var _useMergedState3 = useMergedState(activeKey || defaultActiveFirst && ((_childList$ = childList[0]) === null || _childList$ === void 0 ? void 0 : _childList$.key), {\n      value: activeKey\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedActiveKey = _useMergedState4[0],\n    setMergedActiveKey = _useMergedState4[1];\n  var onActive = useMemoCallback(function (key) {\n    setMergedActiveKey(key);\n  });\n  var onInactive = useMemoCallback(function () {\n    setMergedActiveKey(undefined);\n  });\n  useImperativeHandle(ref, function () {\n    return {\n      list: containerRef.current,\n      focus: function focus(options) {\n        var _childList$find;\n        var keys = getKeys();\n        var _refreshElements = refreshElements(keys, uuid),\n          elements = _refreshElements.elements,\n          key2element = _refreshElements.key2element,\n          element2key = _refreshElements.element2key;\n        var focusableElements = getFocusableElements(containerRef.current, elements);\n        var shouldFocusKey = mergedActiveKey !== null && mergedActiveKey !== void 0 ? mergedActiveKey : focusableElements[0] ? element2key.get(focusableElements[0]) : (_childList$find = childList.find(function (node) {\n          return !node.props.disabled;\n        })) === null || _childList$find === void 0 ? void 0 : _childList$find.key;\n        var elementToFocus = key2element.get(shouldFocusKey);\n        if (shouldFocusKey && elementToFocus) {\n          var _elementToFocus$focus;\n          elementToFocus === null || elementToFocus === void 0 || (_elementToFocus$focus = elementToFocus.focus) === null || _elementToFocus$focus === void 0 || _elementToFocus$focus.call(elementToFocus, options);\n        }\n      }\n    };\n  });\n\n  // ======================== Select ========================\n  // >>>>> Select keys\n  var _useMergedState5 = useMergedState(defaultSelectedKeys || [], {\n      value: selectedKeys,\n      // Legacy convert key to array\n      postState: function postState(keys) {\n        if (Array.isArray(keys)) {\n          return keys;\n        }\n        if (keys === null || keys === undefined) {\n          return EMPTY_LIST;\n        }\n        return [keys];\n      }\n    }),\n    _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n    mergedSelectKeys = _useMergedState6[0],\n    setMergedSelectKeys = _useMergedState6[1];\n\n  // >>>>> Trigger select\n  var triggerSelection = function triggerSelection(info) {\n    if (selectable) {\n      // Insert or Remove\n      var targetKey = info.key;\n      var exist = mergedSelectKeys.includes(targetKey);\n      var newSelectKeys;\n      if (multiple) {\n        if (exist) {\n          newSelectKeys = mergedSelectKeys.filter(function (key) {\n            return key !== targetKey;\n          });\n        } else {\n          newSelectKeys = [].concat(_toConsumableArray(mergedSelectKeys), [targetKey]);\n        }\n      } else {\n        newSelectKeys = [targetKey];\n      }\n      setMergedSelectKeys(newSelectKeys);\n\n      // Trigger event\n      var selectInfo = _objectSpread(_objectSpread({}, info), {}, {\n        selectedKeys: newSelectKeys\n      });\n      if (exist) {\n        onDeselect === null || onDeselect === void 0 || onDeselect(selectInfo);\n      } else {\n        onSelect === null || onSelect === void 0 || onSelect(selectInfo);\n      }\n    }\n\n    // Whatever selectable, always close it\n    if (!multiple && mergedOpenKeys.length && internalMode !== 'inline') {\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  };\n\n  // ========================= Open =========================\n  /**\n   * Click for item. SubMenu do not have selection status\n   */\n  var onInternalClick = useMemoCallback(function (info) {\n    onClick === null || onClick === void 0 || onClick(warnItemProp(info));\n    triggerSelection(info);\n  });\n  var onInternalOpenChange = useMemoCallback(function (key, open) {\n    var newOpenKeys = mergedOpenKeys.filter(function (k) {\n      return k !== key;\n    });\n    if (open) {\n      newOpenKeys.push(key);\n    } else if (internalMode !== 'inline') {\n      // We need find all related popup to close\n      var subPathKeys = getSubPathKeys(key);\n      newOpenKeys = newOpenKeys.filter(function (k) {\n        return !subPathKeys.has(k);\n      });\n    }\n    if (!isEqual(mergedOpenKeys, newOpenKeys, true)) {\n      triggerOpenKeys(newOpenKeys, true);\n    }\n  });\n\n  // ==================== Accessibility =====================\n  var triggerAccessibilityOpen = function triggerAccessibilityOpen(key, open) {\n    var nextOpen = open !== null && open !== void 0 ? open : !mergedOpenKeys.includes(key);\n    onInternalOpenChange(key, nextOpen);\n  };\n  var onInternalKeyDown = useAccessibility(internalMode, mergedActiveKey, isRtl, uuid, containerRef, getKeys, getKeyPath, setMergedActiveKey, triggerAccessibilityOpen, onKeyDown);\n\n  // ======================== Effect ========================\n  React.useEffect(function () {\n    setMounted(true);\n  }, []);\n\n  // ======================= Context ========================\n  var privateContext = React.useMemo(function () {\n    return {\n      _internalRenderMenuItem: _internalRenderMenuItem,\n      _internalRenderSubMenuItem: _internalRenderSubMenuItem\n    };\n  }, [_internalRenderMenuItem, _internalRenderSubMenuItem]);\n\n  // ======================== Render ========================\n\n  // >>>>> Children\n  var wrappedChildList = internalMode !== 'horizontal' || disabledOverflow ? childList :\n  // Need wrap for overflow dropdown that do not response for open\n  childList.map(function (child, index) {\n    return (/*#__PURE__*/\n      // Always wrap provider to avoid sub node re-mount\n      React.createElement(MenuContextProvider, {\n        key: child.key,\n        overflowDisabled: index > lastVisibleIndex\n      }, child)\n    );\n  });\n\n  // >>>>> Container\n  var container = /*#__PURE__*/React.createElement(Overflow, _extends({\n    id: id,\n    ref: containerRef,\n    prefixCls: \"\".concat(prefixCls, \"-overflow\"),\n    component: \"ul\",\n    itemComponent: MenuItem,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-root\"), \"\".concat(prefixCls, \"-\").concat(internalMode), className, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-inline-collapsed\"), internalInlineCollapsed), \"\".concat(prefixCls, \"-rtl\"), isRtl), rootClassName),\n    dir: direction,\n    style: style,\n    role: \"menu\",\n    tabIndex: tabIndex,\n    data: wrappedChildList,\n    renderRawItem: function renderRawItem(node) {\n      return node;\n    },\n    renderRawRest: function renderRawRest(omitItems) {\n      // We use origin list since wrapped list use context to prevent open\n      var len = omitItems.length;\n      var originOmitItems = len ? childList.slice(-len) : null;\n      return /*#__PURE__*/React.createElement(SubMenu, {\n        eventKey: OVERFLOW_KEY,\n        title: overflowedIndicator,\n        disabled: allVisible,\n        internalPopupClose: len === 0,\n        popupClassName: overflowedIndicatorPopupClassName\n      }, originOmitItems);\n    },\n    maxCount: internalMode !== 'horizontal' || disabledOverflow ? Overflow.INVALIDATE : Overflow.RESPONSIVE,\n    ssr: \"full\",\n    \"data-menu-list\": true,\n    onVisibleChange: function onVisibleChange(newLastIndex) {\n      setLastVisibleIndex(newLastIndex);\n    },\n    onKeyDown: onInternalKeyDown\n  }, restProps));\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(PrivateContext.Provider, {\n    value: privateContext\n  }, /*#__PURE__*/React.createElement(IdContext.Provider, {\n    value: uuid\n  }, /*#__PURE__*/React.createElement(MenuContextProvider, {\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    mode: internalMode,\n    openKeys: mergedOpenKeys,\n    rtl: isRtl\n    // Disabled\n    ,\n\n    disabled: disabled\n    // Motion\n    ,\n\n    motion: mounted ? motion : null,\n    defaultMotions: mounted ? defaultMotions : null\n    // Active\n    ,\n\n    activeKey: mergedActiveKey,\n    onActive: onActive,\n    onInactive: onInactive\n    // Selection\n    ,\n\n    selectedKeys: mergedSelectKeys\n    // Level\n    ,\n\n    inlineIndent: inlineIndent\n    // Popup\n    ,\n\n    subMenuOpenDelay: subMenuOpenDelay,\n    subMenuCloseDelay: subMenuCloseDelay,\n    forceSubMenuRender: forceSubMenuRender,\n    builtinPlacements: builtinPlacements,\n    triggerSubMenuAction: triggerSubMenuAction,\n    getPopupContainer: getPopupContainer\n    // Icon\n    ,\n\n    itemIcon: itemIcon,\n    expandIcon: expandIcon\n    // Events\n    ,\n\n    onItemClick: onInternalClick,\n    onOpenChange: onInternalOpenChange\n  }, /*#__PURE__*/React.createElement(PathUserContext.Provider, {\n    value: pathUserContext\n  }, container), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none'\n    },\n    \"aria-hidden\": true\n  }, /*#__PURE__*/React.createElement(PathRegisterContext.Provider, {\n    value: registerPathContext\n  }, measureChildList)))));\n});\nexport default Menu;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_toConsumableArray", "_slicedToArray", "_objectWithoutProperties", "_excluded", "classNames", "Overflow", "useMergedState", "isEqual", "warning", "React", "useImperativeHandle", "flushSync", "IdContext", "MenuContextProvider", "PathRegisterContext", "PathUserContext", "PrivateContext", "getFocusableElements", "refreshElements", "useAccessibility", "useKeyRecords", "OVERFLOW_KEY", "useMemoCallback", "useUUID", "MenuItem", "SubMenu", "parseItems", "warnItemProp", "EMPTY_LIST", "<PERSON><PERSON>", "forwardRef", "props", "ref", "_childList$", "_ref", "_ref$prefixCls", "prefixCls", "rootClassName", "style", "className", "_ref$tabIndex", "tabIndex", "items", "children", "direction", "id", "_ref$mode", "mode", "inlineCollapsed", "disabled", "disabledOverflow", "_ref$subMenuOpenDelay", "subMenuOpenDelay", "_ref$subMenuCloseDela", "subMenuCloseDelay", "forceSubMenuRender", "defaultOpenKeys", "openKeys", "active<PERSON><PERSON>", "defaultActiveFirst", "_ref$selectable", "selectable", "_ref$multiple", "multiple", "defaultSelectedKeys", "<PERSON><PERSON><PERSON><PERSON>", "onSelect", "onDeselect", "_ref$inlineIndent", "inlineIndent", "motion", "defaultMotions", "_ref$triggerSubMenuAc", "triggerSubMenuAction", "builtinPlacements", "itemIcon", "expandIcon", "_ref$overflowedIndica", "overflowedIndicator", "overflowedIndicatorPopupClassName", "getPopupContainer", "onClick", "onOpenChange", "onKeyDown", "openAnimation", "openTransitionName", "_internalRenderMenuItem", "_internalRenderSubMenuItem", "_internalComponents", "restProps", "_React$useMemo", "useMemo", "_React$useMemo2", "childList", "measureChildList", "_React$useState", "useState", "_React$useState2", "mounted", "setMounted", "containerRef", "useRef", "uuid", "isRtl", "process", "env", "NODE_ENV", "_useMergedState", "value", "postState", "keys", "_useMergedState2", "mergedOpenKeys", "setMergedOpenKeys", "triggerOpenKeys", "forceFlush", "arguments", "length", "undefined", "doUpdate", "_React$useState3", "_React$useState4", "inlineCacheOpenKeys", "setInlineCacheOpenKeys", "mountRef", "_React$useMemo3", "_React$useMemo4", "mergedMode", "mergedInlineCollapsed", "isInlineMode", "_React$useState5", "_React$useState6", "internalMode", "setInternalMode", "_React$useState7", "_React$useState8", "internalInlineCollapsed", "setInternalInlineCollapsed", "useEffect", "current", "_React$useState9", "_React$useState10", "lastVisibleIndex", "setLastVisibleIndex", "allVisible", "_useKeyRecords", "registerPath", "unregisterPath", "refreshOverflowKeys", "isSubPath<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "getSub<PERSON><PERSON><PERSON><PERSON><PERSON>", "registerPathContext", "pathUserContext", "slice", "map", "child", "key", "_useMergedState3", "_useMergedState4", "mergedActiveKey", "setMergedActiveKey", "onActive", "onInactive", "list", "focus", "options", "_childList$find", "_refreshElements", "elements", "key2element", "element2key", "focusableElements", "shouldFocus<PERSON>ey", "get", "find", "node", "elementToFocus", "_elementToFocus$focus", "call", "_useMergedState5", "Array", "isArray", "_useMergedState6", "mergedSelectKeys", "setMergedSelectKeys", "triggerSelection", "info", "<PERSON><PERSON><PERSON>", "exist", "includes", "newSelectKeys", "filter", "concat", "selectInfo", "onInternalClick", "onInternalOpenChange", "open", "newOpenKeys", "k", "push", "subPath<PERSON><PERSON>s", "has", "triggerAccessibilityOpen", "nextOpen", "onInternalKeyDown", "privateContext", "wrappedChildList", "index", "createElement", "overflowDisabled", "container", "component", "itemComponent", "dir", "role", "data", "renderRawItem", "renderRawRest", "omitItems", "len", "originOmitItems", "eventKey", "title", "internalPopupClose", "popupClassName", "maxCount", "INVALIDATE", "RESPONSIVE", "ssr", "onVisibleChange", "newLastIndex", "Provider", "rtl", "onItemClick", "display"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-menu/es/Menu.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"rootClassName\", \"style\", \"className\", \"tabIndex\", \"items\", \"children\", \"direction\", \"id\", \"mode\", \"inlineCollapsed\", \"disabled\", \"disabledOverflow\", \"subMenuOpenDelay\", \"subMenuCloseDelay\", \"forceSubMenuRender\", \"defaultOpenKeys\", \"openKeys\", \"activeKey\", \"defaultActiveFirst\", \"selectable\", \"multiple\", \"defaultSelectedKeys\", \"selectedKeys\", \"onSelect\", \"onDeselect\", \"inlineIndent\", \"motion\", \"defaultMotions\", \"triggerSubMenuAction\", \"builtinPlacements\", \"itemIcon\", \"expandIcon\", \"overflowedIndicator\", \"overflowedIndicatorPopupClassName\", \"getPopupContainer\", \"onClick\", \"onOpenChange\", \"onKeyDown\", \"openAnimation\", \"openTransitionName\", \"_internalRenderMenuItem\", \"_internalRenderSubMenuItem\", \"_internalComponents\"];\nimport classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { useImperativeHandle } from 'react';\nimport { flushSync } from 'react-dom';\nimport { IdContext } from \"./context/IdContext\";\nimport MenuContextProvider from \"./context/MenuContext\";\nimport { PathRegisterContext, PathUserContext } from \"./context/PathContext\";\nimport PrivateContext from \"./context/PrivateContext\";\nimport { getFocusableElements, refreshElements, useAccessibility } from \"./hooks/useAccessibility\";\nimport useKeyRecords, { OVERFLOW_KEY } from \"./hooks/useKeyRecords\";\nimport useMemoCallback from \"./hooks/useMemoCallback\";\nimport useUUID from \"./hooks/useUUID\";\nimport MenuItem from \"./MenuItem\";\nimport SubMenu from \"./SubMenu\";\nimport { parseItems } from \"./utils/nodeUtil\";\nimport { warnItemProp } from \"./utils/warnUtil\";\n\n/**\n * Menu modify after refactor:\n * ## Add\n * - disabled\n *\n * ## Remove\n * - openTransitionName\n * - openAnimation\n * - onDestroy\n * - siderCollapsed: Seems antd do not use this prop (Need test in antd)\n * - collapsedWidth: Seems this logic should be handle by antd Layout.Sider\n */\n\n// optimize for render\nvar EMPTY_LIST = [];\nvar Menu = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _childList$;\n  var _ref = props,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-menu' : _ref$prefixCls,\n    rootClassName = _ref.rootClassName,\n    style = _ref.style,\n    className = _ref.className,\n    _ref$tabIndex = _ref.tabIndex,\n    tabIndex = _ref$tabIndex === void 0 ? 0 : _ref$tabIndex,\n    items = _ref.items,\n    children = _ref.children,\n    direction = _ref.direction,\n    id = _ref.id,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'vertical' : _ref$mode,\n    inlineCollapsed = _ref.inlineCollapsed,\n    disabled = _ref.disabled,\n    disabledOverflow = _ref.disabledOverflow,\n    _ref$subMenuOpenDelay = _ref.subMenuOpenDelay,\n    subMenuOpenDelay = _ref$subMenuOpenDelay === void 0 ? 0.1 : _ref$subMenuOpenDelay,\n    _ref$subMenuCloseDela = _ref.subMenuCloseDelay,\n    subMenuCloseDelay = _ref$subMenuCloseDela === void 0 ? 0.1 : _ref$subMenuCloseDela,\n    forceSubMenuRender = _ref.forceSubMenuRender,\n    defaultOpenKeys = _ref.defaultOpenKeys,\n    openKeys = _ref.openKeys,\n    activeKey = _ref.activeKey,\n    defaultActiveFirst = _ref.defaultActiveFirst,\n    _ref$selectable = _ref.selectable,\n    selectable = _ref$selectable === void 0 ? true : _ref$selectable,\n    _ref$multiple = _ref.multiple,\n    multiple = _ref$multiple === void 0 ? false : _ref$multiple,\n    defaultSelectedKeys = _ref.defaultSelectedKeys,\n    selectedKeys = _ref.selectedKeys,\n    onSelect = _ref.onSelect,\n    onDeselect = _ref.onDeselect,\n    _ref$inlineIndent = _ref.inlineIndent,\n    inlineIndent = _ref$inlineIndent === void 0 ? 24 : _ref$inlineIndent,\n    motion = _ref.motion,\n    defaultMotions = _ref.defaultMotions,\n    _ref$triggerSubMenuAc = _ref.triggerSubMenuAction,\n    triggerSubMenuAction = _ref$triggerSubMenuAc === void 0 ? 'hover' : _ref$triggerSubMenuAc,\n    builtinPlacements = _ref.builtinPlacements,\n    itemIcon = _ref.itemIcon,\n    expandIcon = _ref.expandIcon,\n    _ref$overflowedIndica = _ref.overflowedIndicator,\n    overflowedIndicator = _ref$overflowedIndica === void 0 ? '...' : _ref$overflowedIndica,\n    overflowedIndicatorPopupClassName = _ref.overflowedIndicatorPopupClassName,\n    getPopupContainer = _ref.getPopupContainer,\n    onClick = _ref.onClick,\n    onOpenChange = _ref.onOpenChange,\n    onKeyDown = _ref.onKeyDown,\n    openAnimation = _ref.openAnimation,\n    openTransitionName = _ref.openTransitionName,\n    _internalRenderMenuItem = _ref._internalRenderMenuItem,\n    _internalRenderSubMenuItem = _ref._internalRenderSubMenuItem,\n    _internalComponents = _ref._internalComponents,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useMemo = React.useMemo(function () {\n      return [parseItems(children, items, EMPTY_LIST, _internalComponents, prefixCls), parseItems(children, items, EMPTY_LIST, {}, prefixCls)];\n    }, [children, items, _internalComponents]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    childList = _React$useMemo2[0],\n    measureChildList = _React$useMemo2[1];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mounted = _React$useState2[0],\n    setMounted = _React$useState2[1];\n  var containerRef = React.useRef();\n  var uuid = useUUID(id);\n  var isRtl = direction === 'rtl';\n\n  // ========================= Warn =========================\n  if (process.env.NODE_ENV !== 'production') {\n    warning(!openAnimation && !openTransitionName, '`openAnimation` and `openTransitionName` is removed. Please use `motion` or `defaultMotion` instead.');\n  }\n\n  // ========================= Open =========================\n  var _useMergedState = useMergedState(defaultOpenKeys, {\n      value: openKeys,\n      postState: function postState(keys) {\n        return keys || EMPTY_LIST;\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedOpenKeys = _useMergedState2[0],\n    setMergedOpenKeys = _useMergedState2[1];\n\n  // React 18 will merge mouse event which means we open key will not sync\n  // ref: https://github.com/ant-design/ant-design/issues/38818\n  var triggerOpenKeys = function triggerOpenKeys(keys) {\n    var forceFlush = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    function doUpdate() {\n      setMergedOpenKeys(keys);\n      onOpenChange === null || onOpenChange === void 0 || onOpenChange(keys);\n    }\n    if (forceFlush) {\n      flushSync(doUpdate);\n    } else {\n      doUpdate();\n    }\n  };\n\n  // >>>>> Cache & Reset open keys when inlineCollapsed changed\n  var _React$useState3 = React.useState(mergedOpenKeys),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    inlineCacheOpenKeys = _React$useState4[0],\n    setInlineCacheOpenKeys = _React$useState4[1];\n  var mountRef = React.useRef(false);\n\n  // ========================= Mode =========================\n  var _React$useMemo3 = React.useMemo(function () {\n      if ((mode === 'inline' || mode === 'vertical') && inlineCollapsed) {\n        return ['vertical', inlineCollapsed];\n      }\n      return [mode, false];\n    }, [mode, inlineCollapsed]),\n    _React$useMemo4 = _slicedToArray(_React$useMemo3, 2),\n    mergedMode = _React$useMemo4[0],\n    mergedInlineCollapsed = _React$useMemo4[1];\n  var isInlineMode = mergedMode === 'inline';\n  var _React$useState5 = React.useState(mergedMode),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    internalMode = _React$useState6[0],\n    setInternalMode = _React$useState6[1];\n  var _React$useState7 = React.useState(mergedInlineCollapsed),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    internalInlineCollapsed = _React$useState8[0],\n    setInternalInlineCollapsed = _React$useState8[1];\n  React.useEffect(function () {\n    setInternalMode(mergedMode);\n    setInternalInlineCollapsed(mergedInlineCollapsed);\n    if (!mountRef.current) {\n      return;\n    }\n    // Synchronously update MergedOpenKeys\n    if (isInlineMode) {\n      setMergedOpenKeys(inlineCacheOpenKeys);\n    } else {\n      // Trigger open event in case its in control\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  }, [mergedMode, mergedInlineCollapsed]);\n\n  // ====================== Responsive ======================\n  var _React$useState9 = React.useState(0),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    lastVisibleIndex = _React$useState10[0],\n    setLastVisibleIndex = _React$useState10[1];\n  var allVisible = lastVisibleIndex >= childList.length - 1 || internalMode !== 'horizontal' || disabledOverflow;\n\n  // Cache\n  React.useEffect(function () {\n    if (isInlineMode) {\n      setInlineCacheOpenKeys(mergedOpenKeys);\n    }\n  }, [mergedOpenKeys]);\n  React.useEffect(function () {\n    mountRef.current = true;\n    return function () {\n      mountRef.current = false;\n    };\n  }, []);\n\n  // ========================= Path =========================\n  var _useKeyRecords = useKeyRecords(),\n    registerPath = _useKeyRecords.registerPath,\n    unregisterPath = _useKeyRecords.unregisterPath,\n    refreshOverflowKeys = _useKeyRecords.refreshOverflowKeys,\n    isSubPathKey = _useKeyRecords.isSubPathKey,\n    getKeyPath = _useKeyRecords.getKeyPath,\n    getKeys = _useKeyRecords.getKeys,\n    getSubPathKeys = _useKeyRecords.getSubPathKeys;\n  var registerPathContext = React.useMemo(function () {\n    return {\n      registerPath: registerPath,\n      unregisterPath: unregisterPath\n    };\n  }, [registerPath, unregisterPath]);\n  var pathUserContext = React.useMemo(function () {\n    return {\n      isSubPathKey: isSubPathKey\n    };\n  }, [isSubPathKey]);\n  React.useEffect(function () {\n    refreshOverflowKeys(allVisible ? EMPTY_LIST : childList.slice(lastVisibleIndex + 1).map(function (child) {\n      return child.key;\n    }));\n  }, [lastVisibleIndex, allVisible]);\n\n  // ======================== Active ========================\n  var _useMergedState3 = useMergedState(activeKey || defaultActiveFirst && ((_childList$ = childList[0]) === null || _childList$ === void 0 ? void 0 : _childList$.key), {\n      value: activeKey\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedActiveKey = _useMergedState4[0],\n    setMergedActiveKey = _useMergedState4[1];\n  var onActive = useMemoCallback(function (key) {\n    setMergedActiveKey(key);\n  });\n  var onInactive = useMemoCallback(function () {\n    setMergedActiveKey(undefined);\n  });\n  useImperativeHandle(ref, function () {\n    return {\n      list: containerRef.current,\n      focus: function focus(options) {\n        var _childList$find;\n        var keys = getKeys();\n        var _refreshElements = refreshElements(keys, uuid),\n          elements = _refreshElements.elements,\n          key2element = _refreshElements.key2element,\n          element2key = _refreshElements.element2key;\n        var focusableElements = getFocusableElements(containerRef.current, elements);\n        var shouldFocusKey = mergedActiveKey !== null && mergedActiveKey !== void 0 ? mergedActiveKey : focusableElements[0] ? element2key.get(focusableElements[0]) : (_childList$find = childList.find(function (node) {\n          return !node.props.disabled;\n        })) === null || _childList$find === void 0 ? void 0 : _childList$find.key;\n        var elementToFocus = key2element.get(shouldFocusKey);\n        if (shouldFocusKey && elementToFocus) {\n          var _elementToFocus$focus;\n          elementToFocus === null || elementToFocus === void 0 || (_elementToFocus$focus = elementToFocus.focus) === null || _elementToFocus$focus === void 0 || _elementToFocus$focus.call(elementToFocus, options);\n        }\n      }\n    };\n  });\n\n  // ======================== Select ========================\n  // >>>>> Select keys\n  var _useMergedState5 = useMergedState(defaultSelectedKeys || [], {\n      value: selectedKeys,\n      // Legacy convert key to array\n      postState: function postState(keys) {\n        if (Array.isArray(keys)) {\n          return keys;\n        }\n        if (keys === null || keys === undefined) {\n          return EMPTY_LIST;\n        }\n        return [keys];\n      }\n    }),\n    _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n    mergedSelectKeys = _useMergedState6[0],\n    setMergedSelectKeys = _useMergedState6[1];\n\n  // >>>>> Trigger select\n  var triggerSelection = function triggerSelection(info) {\n    if (selectable) {\n      // Insert or Remove\n      var targetKey = info.key;\n      var exist = mergedSelectKeys.includes(targetKey);\n      var newSelectKeys;\n      if (multiple) {\n        if (exist) {\n          newSelectKeys = mergedSelectKeys.filter(function (key) {\n            return key !== targetKey;\n          });\n        } else {\n          newSelectKeys = [].concat(_toConsumableArray(mergedSelectKeys), [targetKey]);\n        }\n      } else {\n        newSelectKeys = [targetKey];\n      }\n      setMergedSelectKeys(newSelectKeys);\n\n      // Trigger event\n      var selectInfo = _objectSpread(_objectSpread({}, info), {}, {\n        selectedKeys: newSelectKeys\n      });\n      if (exist) {\n        onDeselect === null || onDeselect === void 0 || onDeselect(selectInfo);\n      } else {\n        onSelect === null || onSelect === void 0 || onSelect(selectInfo);\n      }\n    }\n\n    // Whatever selectable, always close it\n    if (!multiple && mergedOpenKeys.length && internalMode !== 'inline') {\n      triggerOpenKeys(EMPTY_LIST);\n    }\n  };\n\n  // ========================= Open =========================\n  /**\n   * Click for item. SubMenu do not have selection status\n   */\n  var onInternalClick = useMemoCallback(function (info) {\n    onClick === null || onClick === void 0 || onClick(warnItemProp(info));\n    triggerSelection(info);\n  });\n  var onInternalOpenChange = useMemoCallback(function (key, open) {\n    var newOpenKeys = mergedOpenKeys.filter(function (k) {\n      return k !== key;\n    });\n    if (open) {\n      newOpenKeys.push(key);\n    } else if (internalMode !== 'inline') {\n      // We need find all related popup to close\n      var subPathKeys = getSubPathKeys(key);\n      newOpenKeys = newOpenKeys.filter(function (k) {\n        return !subPathKeys.has(k);\n      });\n    }\n    if (!isEqual(mergedOpenKeys, newOpenKeys, true)) {\n      triggerOpenKeys(newOpenKeys, true);\n    }\n  });\n\n  // ==================== Accessibility =====================\n  var triggerAccessibilityOpen = function triggerAccessibilityOpen(key, open) {\n    var nextOpen = open !== null && open !== void 0 ? open : !mergedOpenKeys.includes(key);\n    onInternalOpenChange(key, nextOpen);\n  };\n  var onInternalKeyDown = useAccessibility(internalMode, mergedActiveKey, isRtl, uuid, containerRef, getKeys, getKeyPath, setMergedActiveKey, triggerAccessibilityOpen, onKeyDown);\n\n  // ======================== Effect ========================\n  React.useEffect(function () {\n    setMounted(true);\n  }, []);\n\n  // ======================= Context ========================\n  var privateContext = React.useMemo(function () {\n    return {\n      _internalRenderMenuItem: _internalRenderMenuItem,\n      _internalRenderSubMenuItem: _internalRenderSubMenuItem\n    };\n  }, [_internalRenderMenuItem, _internalRenderSubMenuItem]);\n\n  // ======================== Render ========================\n\n  // >>>>> Children\n  var wrappedChildList = internalMode !== 'horizontal' || disabledOverflow ? childList :\n  // Need wrap for overflow dropdown that do not response for open\n  childList.map(function (child, index) {\n    return (\n      /*#__PURE__*/\n      // Always wrap provider to avoid sub node re-mount\n      React.createElement(MenuContextProvider, {\n        key: child.key,\n        overflowDisabled: index > lastVisibleIndex\n      }, child)\n    );\n  });\n\n  // >>>>> Container\n  var container = /*#__PURE__*/React.createElement(Overflow, _extends({\n    id: id,\n    ref: containerRef,\n    prefixCls: \"\".concat(prefixCls, \"-overflow\"),\n    component: \"ul\",\n    itemComponent: MenuItem,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-root\"), \"\".concat(prefixCls, \"-\").concat(internalMode), className, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-inline-collapsed\"), internalInlineCollapsed), \"\".concat(prefixCls, \"-rtl\"), isRtl), rootClassName),\n    dir: direction,\n    style: style,\n    role: \"menu\",\n    tabIndex: tabIndex,\n    data: wrappedChildList,\n    renderRawItem: function renderRawItem(node) {\n      return node;\n    },\n    renderRawRest: function renderRawRest(omitItems) {\n      // We use origin list since wrapped list use context to prevent open\n      var len = omitItems.length;\n      var originOmitItems = len ? childList.slice(-len) : null;\n      return /*#__PURE__*/React.createElement(SubMenu, {\n        eventKey: OVERFLOW_KEY,\n        title: overflowedIndicator,\n        disabled: allVisible,\n        internalPopupClose: len === 0,\n        popupClassName: overflowedIndicatorPopupClassName\n      }, originOmitItems);\n    },\n    maxCount: internalMode !== 'horizontal' || disabledOverflow ? Overflow.INVALIDATE : Overflow.RESPONSIVE,\n    ssr: \"full\",\n    \"data-menu-list\": true,\n    onVisibleChange: function onVisibleChange(newLastIndex) {\n      setLastVisibleIndex(newLastIndex);\n    },\n    onKeyDown: onInternalKeyDown\n  }, restProps));\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(PrivateContext.Provider, {\n    value: privateContext\n  }, /*#__PURE__*/React.createElement(IdContext.Provider, {\n    value: uuid\n  }, /*#__PURE__*/React.createElement(MenuContextProvider, {\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    mode: internalMode,\n    openKeys: mergedOpenKeys,\n    rtl: isRtl\n    // Disabled\n    ,\n    disabled: disabled\n    // Motion\n    ,\n    motion: mounted ? motion : null,\n    defaultMotions: mounted ? defaultMotions : null\n    // Active\n    ,\n    activeKey: mergedActiveKey,\n    onActive: onActive,\n    onInactive: onInactive\n    // Selection\n    ,\n    selectedKeys: mergedSelectKeys\n    // Level\n    ,\n    inlineIndent: inlineIndent\n    // Popup\n    ,\n    subMenuOpenDelay: subMenuOpenDelay,\n    subMenuCloseDelay: subMenuCloseDelay,\n    forceSubMenuRender: forceSubMenuRender,\n    builtinPlacements: builtinPlacements,\n    triggerSubMenuAction: triggerSubMenuAction,\n    getPopupContainer: getPopupContainer\n    // Icon\n    ,\n    itemIcon: itemIcon,\n    expandIcon: expandIcon\n    // Events\n    ,\n    onItemClick: onInternalClick,\n    onOpenChange: onInternalOpenChange\n  }, /*#__PURE__*/React.createElement(PathUserContext.Provider, {\n    value: pathUserContext\n  }, container), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none'\n    },\n    \"aria-hidden\": true\n  }, /*#__PURE__*/React.createElement(PathRegisterContext.Provider, {\n    value: registerPathContext\n  }, measureChildList)))));\n});\nexport default Menu;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,UAAU,EAAE,WAAW,EAAE,oBAAoB,EAAE,YAAY,EAAE,UAAU,EAAE,qBAAqB,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,UAAU,EAAE,YAAY,EAAE,qBAAqB,EAAE,mCAAmC,EAAE,mBAAmB,EAAE,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,oBAAoB,EAAE,yBAAyB,EAAE,4BAA4B,EAAE,qBAAqB,CAAC;AAClvB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,QAAQ,OAAO;AAC3C,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,uBAAuB;AAC5E,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,0BAA0B;AAClG,OAAOC,aAAa,IAAIC,YAAY,QAAQ,uBAAuB;AACnE,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,YAAY,QAAQ,kBAAkB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,IAAI,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,WAAW;EACf,IAAIC,IAAI,GAAGH,KAAK;IACdI,cAAc,GAAGD,IAAI,CAACE,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,cAAc;IAClEE,aAAa,GAAGH,IAAI,CAACG,aAAa;IAClCC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,SAAS,GAAGL,IAAI,CAACK,SAAS;IAC1BC,aAAa,GAAGN,IAAI,CAACO,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,aAAa;IACvDE,KAAK,GAAGR,IAAI,CAACQ,KAAK;IAClBC,QAAQ,GAAGT,IAAI,CAACS,QAAQ;IACxBC,SAAS,GAAGV,IAAI,CAACU,SAAS;IAC1BC,EAAE,GAAGX,IAAI,CAACW,EAAE;IACZC,SAAS,GAAGZ,IAAI,CAACa,IAAI;IACrBA,IAAI,GAAGD,SAAS,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,SAAS;IACpDE,eAAe,GAAGd,IAAI,CAACc,eAAe;IACtCC,QAAQ,GAAGf,IAAI,CAACe,QAAQ;IACxBC,gBAAgB,GAAGhB,IAAI,CAACgB,gBAAgB;IACxCC,qBAAqB,GAAGjB,IAAI,CAACkB,gBAAgB;IAC7CA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IACjFE,qBAAqB,GAAGnB,IAAI,CAACoB,iBAAiB;IAC9CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IAClFE,kBAAkB,GAAGrB,IAAI,CAACqB,kBAAkB;IAC5CC,eAAe,GAAGtB,IAAI,CAACsB,eAAe;IACtCC,QAAQ,GAAGvB,IAAI,CAACuB,QAAQ;IACxBC,SAAS,GAAGxB,IAAI,CAACwB,SAAS;IAC1BC,kBAAkB,GAAGzB,IAAI,CAACyB,kBAAkB;IAC5CC,eAAe,GAAG1B,IAAI,CAAC2B,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAChEE,aAAa,GAAG5B,IAAI,CAAC6B,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,aAAa;IAC3DE,mBAAmB,GAAG9B,IAAI,CAAC8B,mBAAmB;IAC9CC,YAAY,GAAG/B,IAAI,CAAC+B,YAAY;IAChCC,QAAQ,GAAGhC,IAAI,CAACgC,QAAQ;IACxBC,UAAU,GAAGjC,IAAI,CAACiC,UAAU;IAC5BC,iBAAiB,GAAGlC,IAAI,CAACmC,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,iBAAiB;IACpEE,MAAM,GAAGpC,IAAI,CAACoC,MAAM;IACpBC,cAAc,GAAGrC,IAAI,CAACqC,cAAc;IACpCC,qBAAqB,GAAGtC,IAAI,CAACuC,oBAAoB;IACjDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,qBAAqB;IACzFE,iBAAiB,GAAGxC,IAAI,CAACwC,iBAAiB;IAC1CC,QAAQ,GAAGzC,IAAI,CAACyC,QAAQ;IACxBC,UAAU,GAAG1C,IAAI,CAAC0C,UAAU;IAC5BC,qBAAqB,GAAG3C,IAAI,CAAC4C,mBAAmB;IAChDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACtFE,iCAAiC,GAAG7C,IAAI,CAAC6C,iCAAiC;IAC1EC,iBAAiB,GAAG9C,IAAI,CAAC8C,iBAAiB;IAC1CC,OAAO,GAAG/C,IAAI,CAAC+C,OAAO;IACtBC,YAAY,GAAGhD,IAAI,CAACgD,YAAY;IAChCC,SAAS,GAAGjD,IAAI,CAACiD,SAAS;IAC1BC,aAAa,GAAGlD,IAAI,CAACkD,aAAa;IAClCC,kBAAkB,GAAGnD,IAAI,CAACmD,kBAAkB;IAC5CC,uBAAuB,GAAGpD,IAAI,CAACoD,uBAAuB;IACtDC,0BAA0B,GAAGrD,IAAI,CAACqD,0BAA0B;IAC5DC,mBAAmB,GAAGtD,IAAI,CAACsD,mBAAmB;IAC9CC,SAAS,GAAGvF,wBAAwB,CAACgC,IAAI,EAAE/B,SAAS,CAAC;EACvD,IAAIuF,cAAc,GAAGjF,KAAK,CAACkF,OAAO,CAAC,YAAY;MAC3C,OAAO,CAACjE,UAAU,CAACiB,QAAQ,EAAED,KAAK,EAAEd,UAAU,EAAE4D,mBAAmB,EAAEpD,SAAS,CAAC,EAAEV,UAAU,CAACiB,QAAQ,EAAED,KAAK,EAAEd,UAAU,EAAE,CAAC,CAAC,EAAEQ,SAAS,CAAC,CAAC;IAC1I,CAAC,EAAE,CAACO,QAAQ,EAAED,KAAK,EAAE8C,mBAAmB,CAAC,CAAC;IAC1CI,eAAe,GAAG3F,cAAc,CAACyF,cAAc,EAAE,CAAC,CAAC;IACnDG,SAAS,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC9BE,gBAAgB,GAAGF,eAAe,CAAC,CAAC,CAAC;EACvC,IAAIG,eAAe,GAAGtF,KAAK,CAACuF,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGhG,cAAc,CAAC8F,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,YAAY,GAAG3F,KAAK,CAAC4F,MAAM,CAAC,CAAC;EACjC,IAAIC,IAAI,GAAG/E,OAAO,CAACsB,EAAE,CAAC;EACtB,IAAI0D,KAAK,GAAG3D,SAAS,KAAK,KAAK;;EAE/B;EACA,IAAI4D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzClG,OAAO,CAAC,CAAC4E,aAAa,IAAI,CAACC,kBAAkB,EAAE,sGAAsG,CAAC;EACxJ;;EAEA;EACA,IAAIsB,eAAe,GAAGrG,cAAc,CAACkD,eAAe,EAAE;MAClDoD,KAAK,EAAEnD,QAAQ;MACfoD,SAAS,EAAE,SAASA,SAASA,CAACC,IAAI,EAAE;QAClC,OAAOA,IAAI,IAAIlF,UAAU;MAC3B;IACF,CAAC,CAAC;IACFmF,gBAAgB,GAAG9G,cAAc,CAAC0G,eAAe,EAAE,CAAC,CAAC;IACrDK,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEzC;EACA;EACA,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACJ,IAAI,EAAE;IACnD,IAAIK,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC1F,SAASG,QAAQA,CAAA,EAAG;MAClBN,iBAAiB,CAACH,IAAI,CAAC;MACvB5B,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC4B,IAAI,CAAC;IACxE;IACA,IAAIK,UAAU,EAAE;MACdxG,SAAS,CAAC4G,QAAQ,CAAC;IACrB,CAAC,MAAM;MACLA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;;EAED;EACA,IAAIC,gBAAgB,GAAG/G,KAAK,CAACuF,QAAQ,CAACgB,cAAc,CAAC;IACnDS,gBAAgB,GAAGxH,cAAc,CAACuH,gBAAgB,EAAE,CAAC,CAAC;IACtDE,mBAAmB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACzCE,sBAAsB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC9C,IAAIG,QAAQ,GAAGnH,KAAK,CAAC4F,MAAM,CAAC,KAAK,CAAC;;EAElC;EACA,IAAIwB,eAAe,GAAGpH,KAAK,CAACkF,OAAO,CAAC,YAAY;MAC5C,IAAI,CAAC5C,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,KAAKC,eAAe,EAAE;QACjE,OAAO,CAAC,UAAU,EAAEA,eAAe,CAAC;MACtC;MACA,OAAO,CAACD,IAAI,EAAE,KAAK,CAAC;IACtB,CAAC,EAAE,CAACA,IAAI,EAAEC,eAAe,CAAC,CAAC;IAC3B8E,eAAe,GAAG7H,cAAc,CAAC4H,eAAe,EAAE,CAAC,CAAC;IACpDE,UAAU,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC/BE,qBAAqB,GAAGF,eAAe,CAAC,CAAC,CAAC;EAC5C,IAAIG,YAAY,GAAGF,UAAU,KAAK,QAAQ;EAC1C,IAAIG,gBAAgB,GAAGzH,KAAK,CAACuF,QAAQ,CAAC+B,UAAU,CAAC;IAC/CI,gBAAgB,GAAGlI,cAAc,CAACiI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,gBAAgB,GAAG7H,KAAK,CAACuF,QAAQ,CAACgC,qBAAqB,CAAC;IAC1DO,gBAAgB,GAAGtI,cAAc,CAACqI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,uBAAuB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7CE,0BAA0B,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClD9H,KAAK,CAACiI,SAAS,CAAC,YAAY;IAC1BL,eAAe,CAACN,UAAU,CAAC;IAC3BU,0BAA0B,CAACT,qBAAqB,CAAC;IACjD,IAAI,CAACJ,QAAQ,CAACe,OAAO,EAAE;MACrB;IACF;IACA;IACA,IAAIV,YAAY,EAAE;MAChBhB,iBAAiB,CAACS,mBAAmB,CAAC;IACxC,CAAC,MAAM;MACL;MACAR,eAAe,CAACtF,UAAU,CAAC;IAC7B;EACF,CAAC,EAAE,CAACmG,UAAU,EAAEC,qBAAqB,CAAC,CAAC;;EAEvC;EACA,IAAIY,gBAAgB,GAAGnI,KAAK,CAACuF,QAAQ,CAAC,CAAC,CAAC;IACtC6C,iBAAiB,GAAG5I,cAAc,CAAC2I,gBAAgB,EAAE,CAAC,CAAC;IACvDE,gBAAgB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACvCE,mBAAmB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC5C,IAAIG,UAAU,GAAGF,gBAAgB,IAAIjD,SAAS,CAACwB,MAAM,GAAG,CAAC,IAAIe,YAAY,KAAK,YAAY,IAAIlF,gBAAgB;;EAE9G;EACAzC,KAAK,CAACiI,SAAS,CAAC,YAAY;IAC1B,IAAIT,YAAY,EAAE;MAChBN,sBAAsB,CAACX,cAAc,CAAC;IACxC;EACF,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACpBvG,KAAK,CAACiI,SAAS,CAAC,YAAY;IAC1Bd,QAAQ,CAACe,OAAO,GAAG,IAAI;IACvB,OAAO,YAAY;MACjBf,QAAQ,CAACe,OAAO,GAAG,KAAK;IAC1B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIM,cAAc,GAAG7H,aAAa,CAAC,CAAC;IAClC8H,YAAY,GAAGD,cAAc,CAACC,YAAY;IAC1CC,cAAc,GAAGF,cAAc,CAACE,cAAc;IAC9CC,mBAAmB,GAAGH,cAAc,CAACG,mBAAmB;IACxDC,YAAY,GAAGJ,cAAc,CAACI,YAAY;IAC1CC,UAAU,GAAGL,cAAc,CAACK,UAAU;IACtCC,OAAO,GAAGN,cAAc,CAACM,OAAO;IAChCC,cAAc,GAAGP,cAAc,CAACO,cAAc;EAChD,IAAIC,mBAAmB,GAAGhJ,KAAK,CAACkF,OAAO,CAAC,YAAY;IAClD,OAAO;MACLuD,YAAY,EAAEA,YAAY;MAC1BC,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACD,YAAY,EAAEC,cAAc,CAAC,CAAC;EAClC,IAAIO,eAAe,GAAGjJ,KAAK,CAACkF,OAAO,CAAC,YAAY;IAC9C,OAAO;MACL0D,YAAY,EAAEA;IAChB,CAAC;EACH,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAClB5I,KAAK,CAACiI,SAAS,CAAC,YAAY;IAC1BU,mBAAmB,CAACJ,UAAU,GAAGpH,UAAU,GAAGiE,SAAS,CAAC8D,KAAK,CAACb,gBAAgB,GAAG,CAAC,CAAC,CAACc,GAAG,CAAC,UAAUC,KAAK,EAAE;MACvG,OAAOA,KAAK,CAACC,GAAG;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAChB,gBAAgB,EAAEE,UAAU,CAAC,CAAC;;EAElC;EACA,IAAIe,gBAAgB,GAAGzJ,cAAc,CAACoD,SAAS,IAAIC,kBAAkB,KAAK,CAAC1B,WAAW,GAAG4D,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI5D,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC6H,GAAG,CAAC,EAAE;MACnKlD,KAAK,EAAElD;IACT,CAAC,CAAC;IACFsG,gBAAgB,GAAG/J,cAAc,CAAC8J,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,QAAQ,GAAG7I,eAAe,CAAC,UAAUwI,GAAG,EAAE;IAC5CI,kBAAkB,CAACJ,GAAG,CAAC;EACzB,CAAC,CAAC;EACF,IAAIM,UAAU,GAAG9I,eAAe,CAAC,YAAY;IAC3C4I,kBAAkB,CAAC5C,SAAS,CAAC;EAC/B,CAAC,CAAC;EACF5G,mBAAmB,CAACsB,GAAG,EAAE,YAAY;IACnC,OAAO;MACLqI,IAAI,EAAEjE,YAAY,CAACuC,OAAO;MAC1B2B,KAAK,EAAE,SAASA,KAAKA,CAACC,OAAO,EAAE;QAC7B,IAAIC,eAAe;QACnB,IAAI1D,IAAI,GAAGyC,OAAO,CAAC,CAAC;QACpB,IAAIkB,gBAAgB,GAAGvJ,eAAe,CAAC4F,IAAI,EAAER,IAAI,CAAC;UAChDoE,QAAQ,GAAGD,gBAAgB,CAACC,QAAQ;UACpCC,WAAW,GAAGF,gBAAgB,CAACE,WAAW;UAC1CC,WAAW,GAAGH,gBAAgB,CAACG,WAAW;QAC5C,IAAIC,iBAAiB,GAAG5J,oBAAoB,CAACmF,YAAY,CAACuC,OAAO,EAAE+B,QAAQ,CAAC;QAC5E,IAAII,cAAc,GAAGb,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGY,iBAAiB,CAAC,CAAC,CAAC,GAAGD,WAAW,CAACG,GAAG,CAACF,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAACL,eAAe,GAAG3E,SAAS,CAACmF,IAAI,CAAC,UAAUC,IAAI,EAAE;UAC/M,OAAO,CAACA,IAAI,CAAClJ,KAAK,CAACkB,QAAQ;QAC7B,CAAC,CAAC,MAAM,IAAI,IAAIuH,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACV,GAAG;QACzE,IAAIoB,cAAc,GAAGP,WAAW,CAACI,GAAG,CAACD,cAAc,CAAC;QACpD,IAAIA,cAAc,IAAII,cAAc,EAAE;UACpC,IAAIC,qBAAqB;UACzBD,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,IAAI,CAACC,qBAAqB,GAAGD,cAAc,CAACZ,KAAK,MAAM,IAAI,IAAIa,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,IAAI,CAACF,cAAc,EAAEX,OAAO,CAAC;QAC5M;MACF;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA;EACA,IAAIc,gBAAgB,GAAG/K,cAAc,CAAC0D,mBAAmB,IAAI,EAAE,EAAE;MAC7D4C,KAAK,EAAE3C,YAAY;MACnB;MACA4C,SAAS,EAAE,SAASA,SAASA,CAACC,IAAI,EAAE;QAClC,IAAIwE,KAAK,CAACC,OAAO,CAACzE,IAAI,CAAC,EAAE;UACvB,OAAOA,IAAI;QACb;QACA,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKQ,SAAS,EAAE;UACvC,OAAO1F,UAAU;QACnB;QACA,OAAO,CAACkF,IAAI,CAAC;MACf;IACF,CAAC,CAAC;IACF0E,gBAAgB,GAAGvL,cAAc,CAACoL,gBAAgB,EAAE,CAAC,CAAC;IACtDI,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAE3C;EACA,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;IACrD,IAAI/H,UAAU,EAAE;MACd;MACA,IAAIgI,SAAS,GAAGD,IAAI,CAAC9B,GAAG;MACxB,IAAIgC,KAAK,GAAGL,gBAAgB,CAACM,QAAQ,CAACF,SAAS,CAAC;MAChD,IAAIG,aAAa;MACjB,IAAIjI,QAAQ,EAAE;QACZ,IAAI+H,KAAK,EAAE;UACTE,aAAa,GAAGP,gBAAgB,CAACQ,MAAM,CAAC,UAAUnC,GAAG,EAAE;YACrD,OAAOA,GAAG,KAAK+B,SAAS;UAC1B,CAAC,CAAC;QACJ,CAAC,MAAM;UACLG,aAAa,GAAG,EAAE,CAACE,MAAM,CAAClM,kBAAkB,CAACyL,gBAAgB,CAAC,EAAE,CAACI,SAAS,CAAC,CAAC;QAC9E;MACF,CAAC,MAAM;QACLG,aAAa,GAAG,CAACH,SAAS,CAAC;MAC7B;MACAH,mBAAmB,CAACM,aAAa,CAAC;;MAElC;MACA,IAAIG,UAAU,GAAGpM,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6L,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1D3H,YAAY,EAAE+H;MAChB,CAAC,CAAC;MACF,IAAIF,KAAK,EAAE;QACT3H,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACgI,UAAU,CAAC;MACxE,CAAC,MAAM;QACLjI,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACiI,UAAU,CAAC;MAClE;IACF;;IAEA;IACA,IAAI,CAACpI,QAAQ,IAAIiD,cAAc,CAACK,MAAM,IAAIe,YAAY,KAAK,QAAQ,EAAE;MACnElB,eAAe,CAACtF,UAAU,CAAC;IAC7B;EACF,CAAC;;EAED;EACA;AACF;AACA;EACE,IAAIwK,eAAe,GAAG9K,eAAe,CAAC,UAAUsK,IAAI,EAAE;IACpD3G,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACtD,YAAY,CAACiK,IAAI,CAAC,CAAC;IACrED,gBAAgB,CAACC,IAAI,CAAC;EACxB,CAAC,CAAC;EACF,IAAIS,oBAAoB,GAAG/K,eAAe,CAAC,UAAUwI,GAAG,EAAEwC,IAAI,EAAE;IAC9D,IAAIC,WAAW,GAAGvF,cAAc,CAACiF,MAAM,CAAC,UAAUO,CAAC,EAAE;MACnD,OAAOA,CAAC,KAAK1C,GAAG;IAClB,CAAC,CAAC;IACF,IAAIwC,IAAI,EAAE;MACRC,WAAW,CAACE,IAAI,CAAC3C,GAAG,CAAC;IACvB,CAAC,MAAM,IAAI1B,YAAY,KAAK,QAAQ,EAAE;MACpC;MACA,IAAIsE,WAAW,GAAGlD,cAAc,CAACM,GAAG,CAAC;MACrCyC,WAAW,GAAGA,WAAW,CAACN,MAAM,CAAC,UAAUO,CAAC,EAAE;QAC5C,OAAO,CAACE,WAAW,CAACC,GAAG,CAACH,CAAC,CAAC;MAC5B,CAAC,CAAC;IACJ;IACA,IAAI,CAACjM,OAAO,CAACyG,cAAc,EAAEuF,WAAW,EAAE,IAAI,CAAC,EAAE;MAC/CrF,eAAe,CAACqF,WAAW,EAAE,IAAI,CAAC;IACpC;EACF,CAAC,CAAC;;EAEF;EACA,IAAIK,wBAAwB,GAAG,SAASA,wBAAwBA,CAAC9C,GAAG,EAAEwC,IAAI,EAAE;IAC1E,IAAIO,QAAQ,GAAGP,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAACtF,cAAc,CAAC+E,QAAQ,CAACjC,GAAG,CAAC;IACtFuC,oBAAoB,CAACvC,GAAG,EAAE+C,QAAQ,CAAC;EACrC,CAAC;EACD,IAAIC,iBAAiB,GAAG3L,gBAAgB,CAACiH,YAAY,EAAE6B,eAAe,EAAE1D,KAAK,EAAED,IAAI,EAAEF,YAAY,EAAEmD,OAAO,EAAED,UAAU,EAAEY,kBAAkB,EAAE0C,wBAAwB,EAAEzH,SAAS,CAAC;;EAEhL;EACA1E,KAAK,CAACiI,SAAS,CAAC,YAAY;IAC1BvC,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI4G,cAAc,GAAGtM,KAAK,CAACkF,OAAO,CAAC,YAAY;IAC7C,OAAO;MACLL,uBAAuB,EAAEA,uBAAuB;MAChDC,0BAA0B,EAAEA;IAC9B,CAAC;EACH,CAAC,EAAE,CAACD,uBAAuB,EAAEC,0BAA0B,CAAC,CAAC;;EAEzD;;EAEA;EACA,IAAIyH,gBAAgB,GAAG5E,YAAY,KAAK,YAAY,IAAIlF,gBAAgB,GAAG2C,SAAS;EACpF;EACAA,SAAS,CAAC+D,GAAG,CAAC,UAAUC,KAAK,EAAEoD,KAAK,EAAE;IACpC,QACE;MACA;MACAxM,KAAK,CAACyM,aAAa,CAACrM,mBAAmB,EAAE;QACvCiJ,GAAG,EAAED,KAAK,CAACC,GAAG;QACdqD,gBAAgB,EAAEF,KAAK,GAAGnE;MAC5B,CAAC,EAAEe,KAAK;IAAC;EAEb,CAAC,CAAC;;EAEF;EACA,IAAIuD,SAAS,GAAG,aAAa3M,KAAK,CAACyM,aAAa,CAAC7M,QAAQ,EAAER,QAAQ,CAAC;IAClEgD,EAAE,EAAEA,EAAE;IACNb,GAAG,EAAEoE,YAAY;IACjBhE,SAAS,EAAE,EAAE,CAAC8J,MAAM,CAAC9J,SAAS,EAAE,WAAW,CAAC;IAC5CiL,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE9L,QAAQ;IACvBe,SAAS,EAAEnC,UAAU,CAACgC,SAAS,EAAE,EAAE,CAAC8J,MAAM,CAAC9J,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC8J,MAAM,CAAC9J,SAAS,EAAE,GAAG,CAAC,CAAC8J,MAAM,CAAC9D,YAAY,CAAC,EAAE7F,SAAS,EAAEzC,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACoM,MAAM,CAAC9J,SAAS,EAAE,mBAAmB,CAAC,EAAEoG,uBAAuB,CAAC,EAAE,EAAE,CAAC0D,MAAM,CAAC9J,SAAS,EAAE,MAAM,CAAC,EAAEmE,KAAK,CAAC,EAAElE,aAAa,CAAC;IACxRkL,GAAG,EAAE3K,SAAS;IACdN,KAAK,EAAEA,KAAK;IACZkL,IAAI,EAAE,MAAM;IACZ/K,QAAQ,EAAEA,QAAQ;IAClBgL,IAAI,EAAET,gBAAgB;IACtBU,aAAa,EAAE,SAASA,aAAaA,CAACzC,IAAI,EAAE;MAC1C,OAAOA,IAAI;IACb,CAAC;IACD0C,aAAa,EAAE,SAASA,aAAaA,CAACC,SAAS,EAAE;MAC/C;MACA,IAAIC,GAAG,GAAGD,SAAS,CAACvG,MAAM;MAC1B,IAAIyG,eAAe,GAAGD,GAAG,GAAGhI,SAAS,CAAC8D,KAAK,CAAC,CAACkE,GAAG,CAAC,GAAG,IAAI;MACxD,OAAO,aAAapN,KAAK,CAACyM,aAAa,CAACzL,OAAO,EAAE;QAC/CsM,QAAQ,EAAE1M,YAAY;QACtB2M,KAAK,EAAElJ,mBAAmB;QAC1B7B,QAAQ,EAAE+F,UAAU;QACpBiF,kBAAkB,EAAEJ,GAAG,KAAK,CAAC;QAC7BK,cAAc,EAAEnJ;MAClB,CAAC,EAAE+I,eAAe,CAAC;IACrB,CAAC;IACDK,QAAQ,EAAE/F,YAAY,KAAK,YAAY,IAAIlF,gBAAgB,GAAG7C,QAAQ,CAAC+N,UAAU,GAAG/N,QAAQ,CAACgO,UAAU;IACvGC,GAAG,EAAE,MAAM;IACX,gBAAgB,EAAE,IAAI;IACtBC,eAAe,EAAE,SAASA,eAAeA,CAACC,YAAY,EAAE;MACtDzF,mBAAmB,CAACyF,YAAY,CAAC;IACnC,CAAC;IACDrJ,SAAS,EAAE2H;EACb,CAAC,EAAErH,SAAS,CAAC,CAAC;;EAEd;EACA,OAAO,aAAahF,KAAK,CAACyM,aAAa,CAAClM,cAAc,CAACyN,QAAQ,EAAE;IAC/D7H,KAAK,EAAEmG;EACT,CAAC,EAAE,aAAatM,KAAK,CAACyM,aAAa,CAACtM,SAAS,CAAC6N,QAAQ,EAAE;IACtD7H,KAAK,EAAEN;EACT,CAAC,EAAE,aAAa7F,KAAK,CAACyM,aAAa,CAACrM,mBAAmB,EAAE;IACvDuB,SAAS,EAAEA,SAAS;IACpBC,aAAa,EAAEA,aAAa;IAC5BU,IAAI,EAAEqF,YAAY;IAClB3E,QAAQ,EAAEuD,cAAc;IACxB0H,GAAG,EAAEnI;IACL;IAAA;;IAEAtD,QAAQ,EAAEA;IACV;IAAA;;IAEAqB,MAAM,EAAE4B,OAAO,GAAG5B,MAAM,GAAG,IAAI;IAC/BC,cAAc,EAAE2B,OAAO,GAAG3B,cAAc,GAAG;IAC3C;IAAA;;IAEAb,SAAS,EAAEuG,eAAe;IAC1BE,QAAQ,EAAEA,QAAQ;IAClBC,UAAU,EAAEA;IACZ;IAAA;;IAEAnG,YAAY,EAAEwH;IACd;IAAA;;IAEApH,YAAY,EAAEA;IACd;IAAA;;IAEAjB,gBAAgB,EAAEA,gBAAgB;IAClCE,iBAAiB,EAAEA,iBAAiB;IACpCC,kBAAkB,EAAEA,kBAAkB;IACtCmB,iBAAiB,EAAEA,iBAAiB;IACpCD,oBAAoB,EAAEA,oBAAoB;IAC1CO,iBAAiB,EAAEA;IACnB;IAAA;;IAEAL,QAAQ,EAAEA,QAAQ;IAClBC,UAAU,EAAEA;IACZ;IAAA;;IAEA+J,WAAW,EAAEvC,eAAe;IAC5BlH,YAAY,EAAEmH;EAChB,CAAC,EAAE,aAAa5L,KAAK,CAACyM,aAAa,CAACnM,eAAe,CAAC0N,QAAQ,EAAE;IAC5D7H,KAAK,EAAE8C;EACT,CAAC,EAAE0D,SAAS,CAAC,EAAE,aAAa3M,KAAK,CAACyM,aAAa,CAAC,KAAK,EAAE;IACrD5K,KAAK,EAAE;MACLsM,OAAO,EAAE;IACX,CAAC;IACD,aAAa,EAAE;EACjB,CAAC,EAAE,aAAanO,KAAK,CAACyM,aAAa,CAACpM,mBAAmB,CAAC2N,QAAQ,EAAE;IAChE7H,KAAK,EAAE6C;EACT,CAAC,EAAE3D,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC;AACF,eAAejE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\components\\\\ProductForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Form, Input, InputNumber, Select, Switch, Button, Row, Col, Card, Space, message } from 'antd';\n\n// 图标导入（预留用于未来功能）\n// import {\n//   UploadOutlined,\n//   InfoCircleOutlined,\n//   CopyOutlined,\n//   SaveOutlined,\n// } from '@ant-design/icons';\n\n// import { productApi } from '../services/api'; // 预留用于API调用\n\nimport { OPERATOR_OPTIONS, LOCATION_OPTIONS, PRODUCT_STATUS_OPTIONS } from '../utils/constants';\nimport { arrayToString, copyToClipboard } from '../utils/helpers';\n\n// 分类接口定义\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 临时常量定义\nconst LOGISTICS_OPTIONS = [{\n  label: '快递',\n  value: 'express'\n}, {\n  label: '自提',\n  value: 'pickup'\n}];\nconst PRODUCT_WEIGHT_OPTIONS = [{\n  label: '1',\n  value: 1\n}, {\n  label: '2',\n  value: 2\n}, {\n  label: '3',\n  value: 3\n}, {\n  label: '4',\n  value: 4\n}, {\n  label: '5',\n  value: 5\n}, {\n  label: '6',\n  value: 6\n}, {\n  label: '7',\n  value: 7\n}, {\n  label: '8',\n  value: 8\n}, {\n  label: '9',\n  value: 9\n}, {\n  label: '10',\n  value: 10\n}];\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst ProductForm = ({\n  form,\n  initialValues,\n  onSubmit,\n  loading = false\n}) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState('');\n  const [uploading, setUploading] = useState(false);\n  const [categories, setCategories] = useState([]);\n\n  // 获取分类数据\n  const loadCategories = () => {\n    try {\n      const savedCategories = localStorage.getItem('categories');\n      if (savedCategories) {\n        const categoriesData = JSON.parse(savedCategories);\n        // 只显示激活状态的分类\n        const activeCategories = categoriesData.filter(cat => cat.status === 'active');\n        setCategories(activeCategories);\n      }\n    } catch (error) {\n      console.error('加载分类数据失败:', error);\n    }\n  };\n\n  // 组件挂载时加载分类数据\n  useEffect(() => {\n    loadCategories();\n\n    // 监听localStorage变化，实时更新分类数据\n    const handleStorageChange = e => {\n      if (e.key === 'categories') {\n        loadCategories();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n\n    // 也监听自定义事件，用于同一页面内的更新\n    const handleCategoriesUpdate = () => {\n      loadCategories();\n    };\n    window.addEventListener('categoriesUpdated', handleCategoriesUpdate);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('categoriesUpdated', handleCategoriesUpdate);\n    };\n  }, []);\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (initialValues) {\n      const formData = {\n        ...initialValues,\n        monthly_fee: parseFloat(initialValues.monthly_fee),\n        general_traffic: parseFloat(initialValues.general_traffic),\n        directional_traffic: parseFloat(initialValues.directional_traffic),\n        tag1: initialValues.tags.tag1,\n        tag2: initialValues.tags.tag2,\n        tag3: initialValues.tags.tag3,\n        forbidden_areas: initialValues.forbidden_areas_array\n      };\n      form.setFieldsValue(formData);\n      setImageUrl(initialValues.main_image || '');\n    }\n  }, [initialValues, form]);\n\n  // 表单提交处理\n  const handleSubmit = async values => {\n    try {\n      const submitData = {\n        ...values,\n        main_image: imageUrl || values.main_image,\n        forbidden_areas: Array.isArray(values.forbidden_areas) ? arrayToString(values.forbidden_areas) : values.forbidden_areas\n      };\n      await onSubmit(submitData);\n    } catch (error) {\n      console.error('表单提交失败:', error);\n    }\n  };\n\n  // 复制产品信息\n  const handleCopyProduct = async () => {\n    const values = form.getFieldsValue();\n    const productInfo = JSON.stringify(values, null, 2);\n    const success = await copyToClipboard(productInfo);\n    if (success) {\n      message.success('产品信息已复制到剪贴板');\n    } else {\n      message.error('复制失败');\n    }\n  };\n\n  // 保存草稿\n  const handleSaveDraft = () => {\n    const values = form.getFieldsValue();\n    localStorage.setItem('product_draft', JSON.stringify(values));\n    message.success('草稿已保存');\n  };\n\n  // 加载草稿\n  const handleLoadDraft = () => {\n    const draft = localStorage.getItem('product_draft');\n    if (draft) {\n      try {\n        const draftData = JSON.parse(draft);\n        form.setFieldsValue(draftData);\n        message.success('草稿已加载');\n      } catch (error) {\n        message.error('草稿格式错误');\n      }\n    } else {\n      message.info('没有找到草稿');\n    }\n  };\n\n  // 图片上传处理\n  const handleImageUpload = file => {\n    // 这里应该实现真实的图片上传逻辑\n    // 目前只是模拟\n    setUploading(true);\n    setTimeout(() => {\n      const mockUrl = URL.createObjectURL(file);\n      setImageUrl(mockUrl);\n      form.setFieldsValue({\n        main_image: mockUrl\n      });\n      setUploading(false);\n      message.success('图片上传成功');\n    }, 1000);\n    return false; // 阻止默认上传行为\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    layout: \"vertical\",\n    onFinish: handleSubmit,\n    initialValues: {\n      status: 'online',\n      need_id_photo: true,\n      sms_notification: false\n    },\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"name\",\n                label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n                rules: [{\n                  required: true,\n                  message: '请输入产品名称'\n                }, {\n                  max: 255,\n                  message: '产品名称不能超过255个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u540D\\u79F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"status\",\n                label: \"\\u4EA7\\u54C1\\u72B6\\u6001\",\n                rules: [{\n                  required: true,\n                  message: '请选择产品状态'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u72B6\\u6001\",\n                  children: PRODUCT_STATUS_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"description\",\n                label: \"\\u4EA7\\u54C1\\u4ECB\\u7ECD\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 3,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u4ECB\\u7ECD\",\n                  maxLength: 500,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5957\\u9910\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"monthly_fee\",\n                label: \"\\u5957\\u9910\\u6708\\u79DF\\uFF08\\u5143\\uFF09\",\n                rules: [{\n                  required: true,\n                  message: '请输入套餐月租'\n                }, {\n                  type: 'number',\n                  min: 0,\n                  message: '月租不能小于0'\n                }],\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6708\\u79DF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"general_traffic\",\n                label: \"\\u901A\\u7528\\u6D41\\u91CF\\uFF08G\\uFF09\",\n                rules: [{\n                  required: true,\n                  message: '请输入通用流量'\n                }, {\n                  type: 'number',\n                  min: 0,\n                  message: '流量不能小于0'\n                }],\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u901A\\u7528\\u6D41\\u91CF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"directional_traffic\",\n                label: \"\\u5B9A\\u5411\\u6D41\\u91CF\\uFF08G\\uFF09\",\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B9A\\u5411\\u6D41\\u91CF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"free_minutes\",\n                label: \"\\u514D\\u8D39\\u901A\\u8BDD\\uFF08\\u5206\\u949F\\uFF09\",\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u514D\\u8D39\\u901A\\u8BDD\\u65F6\\u957F\",\n                  min: 0,\n                  max: 99999\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"package_duration\",\n                label: \"\\u5957\\u9910\\u65F6\\u957F\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5957\\u9910\\u65F6\\u957F\\uFF0C\\u5982\\uFF1A1\\u4E2A\\u6708\\u30013\\u4E2A\\u6708\\u300112\\u4E2A\\u6708\\u7B49\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"product_weight\",\n                label: \"\\u4EA7\\u54C1\\u6743\\u91CD\",\n                tooltip: \"\\u6743\\u91CD\\u8D8A\\u9AD8\\uFF0C\\u4EA7\\u54C1\\u5728\\u5217\\u8868\\u4E2D\\u7684\\u6392\\u5E8F\\u8D8A\\u9760\\u524D\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u6743\\u91CD\",\n                  children: PRODUCT_WEIGHT_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8FD0\\u8425\\u5546\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"category_id\",\n                label: \"\\u4EA7\\u54C1\\u5206\\u7C7B\",\n                rules: [{\n                  required: true,\n                  message: '请选择产品分类'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u5206\\u7C7B\",\n                  showSearch: true,\n                  filterOption: (input, option) => {\n                    var _option$children;\n                    return option === null || option === void 0 ? void 0 : (_option$children = option.children) === null || _option$children === void 0 ? void 0 : _option$children.toLowerCase().includes(input.toLowerCase());\n                  },\n                  children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n                    value: category.id,\n                    children: category.name\n                  }, category.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"operator\",\n                label: \"\\u8FD0\\u8425\\u5546\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n                  children: OPERATOR_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"location\",\n                label: \"\\u5F52\\u5C5E\\u5730\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F52\\u5C5E\\u5730\",\n                  children: LOCATION_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5546\\u54C1\\u6807\\u7B7E\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag1\",\n                label: \"\\u6807\\u7B7E1\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E1\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag2\",\n                label: \"\\u6807\\u7B7E2\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E2\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag3\",\n                label: \"\\u6807\\u7B7E3\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E3\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7269\\u6D41\\u914D\\u7F6E\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"logistics_method\",\n                label: \"\\u7269\\u6D41\\u65B9\\u5F0F\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u7269\\u6D41\\u65B9\\u5F0F\",\n                  children: LOGISTICS_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"forbidden_areas\",\n                label: \"\\u7981\\u53D1\\u5730\\u533A\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  mode: \"tags\",\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u7981\\u53D1\\u5730\\u533A\\uFF0C\\u652F\\u6301\\u591A\\u4E2A\",\n                  style: {\n                    width: '100%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5176\\u4ED6\\u8BBE\\u7F6E\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"need_id_photo\",\n                label: \"\\u9700\\u8981\\u4E0A\\u4F20\\u8EAB\\u4EFD\\u8BC1\\u7167\\u7247\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"sms_notification\",\n                label: \"\\u5F00\\u542F\\u7528\\u6237\\u4E0B\\u5355\\u6210\\u529F\\u77ED\\u4FE1\\u63D0\\u9192\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"main_image\",\n                label: \"\\u4EA7\\u54C1\\u4E3B\\u56FE\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u4E3B\\u56FEURL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"detail_description\",\n                label: \"\\u5546\\u54C1\\u8BE6\\u60C5\\u63CF\\u8FF0\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 6,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u8BE6\\u60C5\\u63CF\\u8FF0\\uFF0C\\u652F\\u6301HTML\",\n                  maxLength: 2000,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              size: \"large\",\n              children: initialValues ? '更新产品' : '创建产品'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"large\",\n              onClick: () => form.resetFields(),\n              children: \"\\u91CD\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductForm, \"tQ0p/TMYRMYuI2d9fAqcD5HS9Bw=\");\n_c = ProductForm;\nexport default ProductForm;\nvar _c;\n$RefreshReg$(_c, \"ProductForm\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Form", "Input", "InputNumber", "Select", "Switch", "<PERSON><PERSON>", "Row", "Col", "Card", "Space", "message", "OPERATOR_OPTIONS", "LOCATION_OPTIONS", "PRODUCT_STATUS_OPTIONS", "arrayToString", "copyToClipboard", "jsxDEV", "_jsxDEV", "LOGISTICS_OPTIONS", "label", "value", "PRODUCT_WEIGHT_OPTIONS", "Option", "TextArea", "ProductForm", "form", "initialValues", "onSubmit", "loading", "_s", "imageUrl", "setImageUrl", "uploading", "setUploading", "categories", "setCategories", "loadCategories", "savedCategories", "localStorage", "getItem", "categoriesData", "JSON", "parse", "activeCategories", "filter", "cat", "status", "error", "console", "handleStorageChange", "e", "key", "window", "addEventListener", "handleCategoriesUpdate", "removeEventListener", "formData", "monthly_fee", "parseFloat", "general_traffic", "directional_traffic", "tag1", "tags", "tag2", "tag3", "forbidden_areas", "forbidden_areas_array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "main_image", "handleSubmit", "values", "submitData", "Array", "isArray", "handleCopyProduct", "getFieldsValue", "productInfo", "stringify", "success", "handleSaveDraft", "setItem", "handleLoadDraft", "draft", "draftData", "info", "handleImageUpload", "file", "setTimeout", "mockUrl", "URL", "createObjectURL", "layout", "onFinish", "need_id_photo", "sms_notification", "children", "gutter", "span", "title", "style", "marginBottom", "<PERSON><PERSON>", "name", "rules", "required", "max", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "option", "rows", "max<PERSON><PERSON><PERSON>", "showCount", "type", "min", "width", "precision", "tooltip", "showSearch", "filterOption", "input", "_option$children", "toLowerCase", "includes", "category", "id", "mode", "valuePropName", "htmlType", "size", "onClick", "resetFields", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/components/ProductForm.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport {\n  Form,\n  Input,\n  InputNumber,\n  Select,\n  Switch,\n  Button,\n  Row,\n  Col,\n  Card,\n  Space,\n  message,\n} from 'antd';\nimport RichTextEditor from './RichTextEditor';\n// 图标导入（预留用于未来功能）\n// import {\n//   UploadOutlined,\n//   InfoCircleOutlined,\n//   CopyOutlined,\n//   SaveOutlined,\n// } from '@ant-design/icons';\nimport type { FormInstance } from 'antd/es/form';\nimport type { UploadFile } from 'antd/es/upload/interface';\n// import { productApi } from '../services/api'; // 预留用于API调用\nimport type { Product, ProductRequest } from '../types/product';\nimport {\n  OPERATOR_OPTIONS,\n  LOCATION_OPTIONS,\n  PRODUCT_STATUS_OPTIONS,\n} from '../utils/constants';\nimport { arrayToString, copyToClipboard } from '../utils/helpers';\n\n// 分类接口定义\ninterface Category {\n  id: number;\n  name: string;\n  description: string;\n  productCount: number;\n  status: 'active' | 'inactive';\n  createdAt: string;\n}\n\n// 临时常量定义\nconst LOGISTICS_OPTIONS = [\n  { label: '快递', value: 'express' },\n  { label: '自提', value: 'pickup' },\n];\n\n\n\nconst PRODUCT_WEIGHT_OPTIONS = [\n  { label: '1', value: 1 },\n  { label: '2', value: 2 },\n  { label: '3', value: 3 },\n  { label: '4', value: 4 },\n  { label: '5', value: 5 },\n  { label: '6', value: 6 },\n  { label: '7', value: 7 },\n  { label: '8', value: 8 },\n  { label: '9', value: 9 },\n  { label: '10', value: 10 },\n];\n\nconst { Option } = Select;\nconst { TextArea } = Input;\n\ninterface ProductFormProps {\n  form: FormInstance;\n  initialValues?: Product;\n  onSubmit: (values: ProductRequest) => Promise<void>;\n  loading?: boolean;\n}\n\nconst ProductForm: React.FC<ProductFormProps> = ({\n  form,\n  initialValues,\n  onSubmit,\n  loading = false,\n}) => {\n  const [imageUrl, setImageUrl] = useState<string>('');\n  const [uploading, setUploading] = useState(false);\n  const [categories, setCategories] = useState<Category[]>([]);\n\n  // 获取分类数据\n  const loadCategories = () => {\n    try {\n      const savedCategories = localStorage.getItem('categories');\n      if (savedCategories) {\n        const categoriesData = JSON.parse(savedCategories);\n        // 只显示激活状态的分类\n        const activeCategories = categoriesData.filter((cat: Category) => cat.status === 'active');\n        setCategories(activeCategories);\n      }\n    } catch (error) {\n      console.error('加载分类数据失败:', error);\n    }\n  };\n\n  // 组件挂载时加载分类数据\n  useEffect(() => {\n    loadCategories();\n\n    // 监听localStorage变化，实时更新分类数据\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'categories') {\n        loadCategories();\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n\n    // 也监听自定义事件，用于同一页面内的更新\n    const handleCategoriesUpdate = () => {\n      loadCategories();\n    };\n\n    window.addEventListener('categoriesUpdated', handleCategoriesUpdate);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('categoriesUpdated', handleCategoriesUpdate);\n    };\n  }, []);\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (initialValues) {\n      const formData = {\n        ...initialValues,\n        monthly_fee: parseFloat(initialValues.monthly_fee),\n        general_traffic: parseFloat(initialValues.general_traffic),\n        directional_traffic: parseFloat(initialValues.directional_traffic),\n        tag1: initialValues.tags.tag1,\n        tag2: initialValues.tags.tag2,\n        tag3: initialValues.tags.tag3,\n        forbidden_areas: initialValues.forbidden_areas_array,\n      };\n      form.setFieldsValue(formData);\n      setImageUrl(initialValues.main_image || '');\n    }\n  }, [initialValues, form]);\n\n  // 表单提交处理\n  const handleSubmit = async (values: any) => {\n    try {\n      const submitData: ProductRequest = {\n        ...values,\n        main_image: imageUrl || values.main_image,\n        forbidden_areas: Array.isArray(values.forbidden_areas)\n          ? arrayToString(values.forbidden_areas)\n          : values.forbidden_areas,\n      };\n\n      await onSubmit(submitData);\n    } catch (error) {\n      console.error('表单提交失败:', error);\n    }\n  };\n\n  // 复制产品信息\n  const handleCopyProduct = async () => {\n    const values = form.getFieldsValue();\n    const productInfo = JSON.stringify(values, null, 2);\n    const success = await copyToClipboard(productInfo);\n    if (success) {\n      message.success('产品信息已复制到剪贴板');\n    } else {\n      message.error('复制失败');\n    }\n  };\n\n  // 保存草稿\n  const handleSaveDraft = () => {\n    const values = form.getFieldsValue();\n    localStorage.setItem('product_draft', JSON.stringify(values));\n    message.success('草稿已保存');\n  };\n\n  // 加载草稿\n  const handleLoadDraft = () => {\n    const draft = localStorage.getItem('product_draft');\n    if (draft) {\n      try {\n        const draftData = JSON.parse(draft);\n        form.setFieldsValue(draftData);\n        message.success('草稿已加载');\n      } catch (error) {\n        message.error('草稿格式错误');\n      }\n    } else {\n      message.info('没有找到草稿');\n    }\n  };\n\n  // 图片上传处理\n  const handleImageUpload = (file: UploadFile) => {\n    // 这里应该实现真实的图片上传逻辑\n    // 目前只是模拟\n    setUploading(true);\n    setTimeout(() => {\n      const mockUrl = URL.createObjectURL(file as any);\n      setImageUrl(mockUrl);\n      form.setFieldsValue({ main_image: mockUrl });\n      setUploading(false);\n      message.success('图片上传成功');\n    }, 1000);\n    return false; // 阻止默认上传行为\n  };\n\n  return (\n    <Form\n      form={form}\n      layout=\"vertical\"\n      onFinish={handleSubmit}\n      initialValues={{\n        status: 'online',\n        need_id_photo: true,\n        sms_notification: false,\n      }}\n    >\n      <Row gutter={24}>\n        {/* 基本信息 */}\n        <Col span={24}>\n          <Card title=\"基本信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"name\"\n                  label=\"产品名称\"\n                  rules={[\n                    { required: true, message: '请输入产品名称' },\n                    { max: 255, message: '产品名称不能超过255个字符' },\n                  ]}\n                >\n                  <Input placeholder=\"请输入产品名称\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"status\"\n                  label=\"产品状态\"\n                  rules={[{ required: true, message: '请选择产品状态' }]}\n                >\n                  <Select placeholder=\"请选择产品状态\">\n                    {PRODUCT_STATUS_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"description\"\n                  label=\"产品介绍\"\n                >\n                  <TextArea\n                    rows={3}\n                    placeholder=\"请输入产品介绍\"\n                    maxLength={500}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 套餐信息 */}\n        <Col span={24}>\n          <Card title=\"套餐信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"monthly_fee\"\n                  label=\"套餐月租（元）\"\n                  rules={[\n                    { required: true, message: '请输入套餐月租' },\n                    { type: 'number', min: 0, message: '月租不能小于0' },\n                  ]}\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入月租\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"general_traffic\"\n                  label=\"通用流量（G）\"\n                  rules={[\n                    { required: true, message: '请输入通用流量' },\n                    { type: 'number', min: 0, message: '流量不能小于0' },\n                  ]}\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入通用流量\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"directional_traffic\"\n                  label=\"定向流量（G）\"\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入定向流量\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"free_minutes\"\n                  label=\"免费通话（分钟）\"\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入免费通话时长\"\n                    min={0}\n                    max={99999}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"package_duration\"\n                  label=\"套餐时长\"\n                >\n                  <Input placeholder=\"请输入套餐时长，如：1个月、3个月、12个月等\" />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"product_weight\"\n                  label=\"产品权重\"\n                  tooltip=\"权重越高，产品在列表中的排序越靠前\"\n                >\n                  <Select placeholder=\"请选择产品权重\">\n                    {PRODUCT_WEIGHT_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 运营商信息 */}\n        <Col span={24}>\n          <Card title=\"运营商信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"category_id\"\n                  label=\"产品分类\"\n                  rules={[{ required: true, message: '请选择产品分类' }]}\n                >\n                  <Select\n                    placeholder=\"请选择产品分类\"\n                    showSearch\n                    filterOption={(input, option) =>\n                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                    }\n                  >\n                    {categories.map(category => (\n                      <Option key={category.id} value={category.id}>\n                        {category.name}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"operator\"\n                  label=\"运营商\"\n                >\n                  <Select placeholder=\"请选择运营商\">\n                    {OPERATOR_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"location\"\n                  label=\"归属地\"\n                >\n                  <Select placeholder=\"请选择归属地\">\n                    {LOCATION_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 商品标签 */}\n        <Col span={24}>\n          <Card title=\"商品标签\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag1\"\n                  label=\"标签1\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签1\" maxLength={6} />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag2\"\n                  label=\"标签2\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签2\" maxLength={6} />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag3\"\n                  label=\"标签3\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签3\" maxLength={6} />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 物流配置 */}\n        <Col span={24}>\n          <Card title=\"物流配置\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"logistics_method\"\n                  label=\"物流方式\"\n                >\n                  <Select placeholder=\"请选择物流方式\">\n                    {LOGISTICS_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"forbidden_areas\"\n                  label=\"禁发地区\"\n                >\n                  <Select\n                    mode=\"tags\"\n                    placeholder=\"请输入禁发地区，支持多个\"\n                    style={{ width: '100%' }}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 其他设置 */}\n        <Col span={24}>\n          <Card title=\"其他设置\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"need_id_photo\"\n                  label=\"需要上传身份证照片\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"sms_notification\"\n                  label=\"开启用户下单成功短信提醒\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"main_image\"\n                  label=\"产品主图\"\n                >\n                  <Input placeholder=\"请输入产品主图URL\" />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"detail_description\"\n                  label=\"商品详情描述\"\n                >\n                  <TextArea\n                    rows={6}\n                    placeholder=\"请输入商品详情描述，支持HTML\"\n                    maxLength={2000}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 提交按钮 */}\n        <Col span={24}>\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                size=\"large\"\n              >\n                {initialValues ? '更新产品' : '创建产品'}\n              </Button>\n              <Button\n                size=\"large\"\n                onClick={() => form.resetFields()}\n              >\n                重置\n              </Button>\n            </Space>\n          </Form.Item>\n        </Col>\n      </Row>\n    </Form>\n  );\n};\n\nexport default ProductForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,MAAM;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAEA,SACEC,gBAAgB,EAChBC,gBAAgB,EAChBC,sBAAsB,QACjB,oBAAoB;AAC3B,SAASC,aAAa,EAAEC,eAAe,QAAQ,kBAAkB;;AAEjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAUA;AACA,MAAMC,iBAAiB,GAAG,CACxB;EAAEC,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAS,CAAC,CACjC;AAID,MAAMC,sBAAsB,GAAG,CAC7B;EAAEF,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAG,CAAC,CAC3B;AAED,MAAM;EAAEE;AAAO,CAAC,GAAGnB,MAAM;AACzB,MAAM;EAAEoB;AAAS,CAAC,GAAGtB,KAAK;AAS1B,MAAMuB,WAAuC,GAAGA,CAAC;EAC/CC,IAAI;EACJC,aAAa;EACbC,QAAQ;EACRC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAa,EAAE,CAAC;;EAE5D;EACA,MAAMqC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI;MACF,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAC1D,IAAIF,eAAe,EAAE;QACnB,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,eAAe,CAAC;QAClD;QACA,MAAMM,gBAAgB,GAAGH,cAAc,CAACI,MAAM,CAAEC,GAAa,IAAKA,GAAG,CAACC,MAAM,KAAK,QAAQ,CAAC;QAC1FX,aAAa,CAACQ,gBAAgB,CAAC;MACjC;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACAjD,SAAS,CAAC,MAAM;IACdsC,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAMa,mBAAmB,GAAIC,CAAe,IAAK;MAC/C,IAAIA,CAAC,CAACC,GAAG,KAAK,YAAY,EAAE;QAC1Bf,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IAEDgB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;;IAEvD;IACA,MAAMK,sBAAsB,GAAGA,CAAA,KAAM;MACnClB,cAAc,CAAC,CAAC;IAClB,CAAC;IAEDgB,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAEC,sBAAsB,CAAC;IAEpE,OAAO,MAAM;MACXF,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAEN,mBAAmB,CAAC;MAC1DG,MAAM,CAACG,mBAAmB,CAAC,mBAAmB,EAAED,sBAAsB,CAAC;IACzE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI4B,aAAa,EAAE;MACjB,MAAM8B,QAAQ,GAAG;QACf,GAAG9B,aAAa;QAChB+B,WAAW,EAAEC,UAAU,CAAChC,aAAa,CAAC+B,WAAW,CAAC;QAClDE,eAAe,EAAED,UAAU,CAAChC,aAAa,CAACiC,eAAe,CAAC;QAC1DC,mBAAmB,EAAEF,UAAU,CAAChC,aAAa,CAACkC,mBAAmB,CAAC;QAClEC,IAAI,EAAEnC,aAAa,CAACoC,IAAI,CAACD,IAAI;QAC7BE,IAAI,EAAErC,aAAa,CAACoC,IAAI,CAACC,IAAI;QAC7BC,IAAI,EAAEtC,aAAa,CAACoC,IAAI,CAACE,IAAI;QAC7BC,eAAe,EAAEvC,aAAa,CAACwC;MACjC,CAAC;MACDzC,IAAI,CAAC0C,cAAc,CAACX,QAAQ,CAAC;MAC7BzB,WAAW,CAACL,aAAa,CAAC0C,UAAU,IAAI,EAAE,CAAC;IAC7C;EACF,CAAC,EAAE,CAAC1C,aAAa,EAAED,IAAI,CAAC,CAAC;;EAEzB;EACA,MAAM4C,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,MAAMC,UAA0B,GAAG;QACjC,GAAGD,MAAM;QACTF,UAAU,EAAEtC,QAAQ,IAAIwC,MAAM,CAACF,UAAU;QACzCH,eAAe,EAAEO,KAAK,CAACC,OAAO,CAACH,MAAM,CAACL,eAAe,CAAC,GAClDnD,aAAa,CAACwD,MAAM,CAACL,eAAe,CAAC,GACrCK,MAAM,CAACL;MACb,CAAC;MAED,MAAMtC,QAAQ,CAAC4C,UAAU,CAAC;IAC5B,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAMJ,MAAM,GAAG7C,IAAI,CAACkD,cAAc,CAAC,CAAC;IACpC,MAAMC,WAAW,GAAGnC,IAAI,CAACoC,SAAS,CAACP,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMQ,OAAO,GAAG,MAAM/D,eAAe,CAAC6D,WAAW,CAAC;IAClD,IAAIE,OAAO,EAAE;MACXpE,OAAO,CAACoE,OAAO,CAAC,aAAa,CAAC;IAChC,CAAC,MAAM;MACLpE,OAAO,CAACqC,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMgC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMT,MAAM,GAAG7C,IAAI,CAACkD,cAAc,CAAC,CAAC;IACpCrC,YAAY,CAAC0C,OAAO,CAAC,eAAe,EAAEvC,IAAI,CAACoC,SAAS,CAACP,MAAM,CAAC,CAAC;IAC7D5D,OAAO,CAACoE,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,KAAK,GAAG5C,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACnD,IAAI2C,KAAK,EAAE;MACT,IAAI;QACF,MAAMC,SAAS,GAAG1C,IAAI,CAACC,KAAK,CAACwC,KAAK,CAAC;QACnCzD,IAAI,CAAC0C,cAAc,CAACgB,SAAS,CAAC;QAC9BzE,OAAO,CAACoE,OAAO,CAAC,OAAO,CAAC;MAC1B,CAAC,CAAC,OAAO/B,KAAK,EAAE;QACdrC,OAAO,CAACqC,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,MAAM;MACLrC,OAAO,CAAC0E,IAAI,CAAC,QAAQ,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,IAAgB,IAAK;IAC9C;IACA;IACArD,YAAY,CAAC,IAAI,CAAC;IAClBsD,UAAU,CAAC,MAAM;MACf,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAW,CAAC;MAChDvD,WAAW,CAACyD,OAAO,CAAC;MACpB/D,IAAI,CAAC0C,cAAc,CAAC;QAAEC,UAAU,EAAEoB;MAAQ,CAAC,CAAC;MAC5CvD,YAAY,CAAC,KAAK,CAAC;MACnBvB,OAAO,CAACoE,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,oBACE7D,OAAA,CAACjB,IAAI;IACHyB,IAAI,EAAEA,IAAK;IACXkE,MAAM,EAAC,UAAU;IACjBC,QAAQ,EAAEvB,YAAa;IACvB3C,aAAa,EAAE;MACboB,MAAM,EAAE,QAAQ;MAChB+C,aAAa,EAAE,IAAI;MACnBC,gBAAgB,EAAE;IACpB,CAAE;IAAAC,QAAA,eAEF9E,OAAA,CAACX,GAAG;MAAC0F,MAAM,EAAE,EAAG;MAAAD,QAAA,gBAEd9E,OAAA,CAACV,GAAG;QAAC0F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ9E,OAAA,CAACT,IAAI;UAAC0F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C9E,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXnF,KAAK,EAAC,0BAAM;gBACZoF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAE+F,GAAG,EAAE,GAAG;kBAAE/F,OAAO,EAAE;gBAAiB,CAAC,CACvC;gBAAAqF,QAAA,eAEF9E,OAAA,CAAChB,KAAK;kBAACyG,WAAW,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,QAAQ;gBACbnF,KAAK,EAAC,0BAAM;gBACZoF,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9F,OAAO,EAAE;gBAAU,CAAC,CAAE;gBAAAqF,QAAA,eAEhD9E,OAAA,CAACd,MAAM;kBAACuG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,EAC1BlF,sBAAsB,CAACkG,GAAG,CAACC,MAAM,iBAChC/F,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE4F,MAAM,CAAC5F,KAAM;oBAAA2E,QAAA,EAC5CiB,MAAM,CAAC7F;kBAAK,GADF6F,MAAM,CAAC5F,KAAK;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7F,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBnF,KAAK,EAAC,0BAAM;gBAAA4E,QAAA,eAEZ9E,OAAA,CAACM,QAAQ;kBACP0F,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,4CAAS;kBACrBQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7F,OAAA,CAACV,GAAG;QAAC0F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ9E,OAAA,CAACT,IAAI;UAAC0F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C9E,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBnF,KAAK,EAAC,4CAAS;gBACfoF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAE0G,IAAI,EAAE,QAAQ;kBAAEC,GAAG,EAAE,CAAC;kBAAE3G,OAAO,EAAE;gBAAU,CAAC,CAC9C;gBAAAqF,QAAA,eAEF9E,OAAA,CAACf,WAAW;kBACViG,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO,CAAE;kBACzBZ,WAAW,EAAC,gCAAO;kBACnBa,SAAS,EAAE,CAAE;kBACbF,GAAG,EAAE,CAAE;kBACPZ,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,iBAAiB;gBACtBnF,KAAK,EAAC,uCAAS;gBACfoF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAE0G,IAAI,EAAE,QAAQ;kBAAEC,GAAG,EAAE,CAAC;kBAAE3G,OAAO,EAAE;gBAAU,CAAC,CAC9C;gBAAAqF,QAAA,eAEF9E,OAAA,CAACf,WAAW;kBACViG,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO,CAAE;kBACzBZ,WAAW,EAAC,4CAAS;kBACrBa,SAAS,EAAE,CAAE;kBACbF,GAAG,EAAE,CAAE;kBACPZ,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,qBAAqB;gBAC1BnF,KAAK,EAAC,uCAAS;gBAAA4E,QAAA,eAEf9E,OAAA,CAACf,WAAW;kBACViG,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO,CAAE;kBACzBZ,WAAW,EAAC,4CAAS;kBACrBa,SAAS,EAAE,CAAE;kBACbF,GAAG,EAAE,CAAE;kBACPZ,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7F,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,cAAc;gBACnBnF,KAAK,EAAC,kDAAU;gBAAA4E,QAAA,eAEhB9E,OAAA,CAACf,WAAW;kBACViG,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO,CAAE;kBACzBZ,WAAW,EAAC,wDAAW;kBACvBW,GAAG,EAAE,CAAE;kBACPZ,GAAG,EAAE;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBnF,KAAK,EAAC,0BAAM;gBAAA4E,QAAA,eAEZ9E,OAAA,CAAChB,KAAK;kBAACyG,WAAW,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,gBAAgB;gBACrBnF,KAAK,EAAC,0BAAM;gBACZqG,OAAO,EAAC,wGAAmB;gBAAAzB,QAAA,eAE3B9E,OAAA,CAACd,MAAM;kBAACuG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,EAC1B1E,sBAAsB,CAAC0F,GAAG,CAACC,MAAM,iBAChC/F,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE4F,MAAM,CAAC5F,KAAM;oBAAA2E,QAAA,EAC5CiB,MAAM,CAAC7F;kBAAK,GADF6F,MAAM,CAAC5F,KAAK;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7F,OAAA,CAACV,GAAG;QAAC0F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ9E,OAAA,CAACT,IAAI;UAAC0F,KAAK,EAAC,gCAAO;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,eAC9C9E,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBnF,KAAK,EAAC,0BAAM;gBACZoF,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9F,OAAO,EAAE;gBAAU,CAAC,CAAE;gBAAAqF,QAAA,eAEhD9E,OAAA,CAACd,MAAM;kBACLuG,WAAW,EAAC,4CAAS;kBACrBe,UAAU;kBACVC,YAAY,EAAEA,CAACC,KAAK,EAAEX,MAAM;oBAAA,IAAAY,gBAAA;oBAAA,OACzBZ,MAAM,aAANA,MAAM,wBAAAY,gBAAA,GAANZ,MAAM,CAAEjB,QAAQ,cAAA6B,gBAAA,uBAAjBA,gBAAA,CAAyCC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACH,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;kBAAA,CACrF;kBAAA9B,QAAA,EAEA7D,UAAU,CAAC6E,GAAG,CAACgB,QAAQ,iBACtB9G,OAAA,CAACK,MAAM;oBAAmBF,KAAK,EAAE2G,QAAQ,CAACC,EAAG;oBAAAjC,QAAA,EAC1CgC,QAAQ,CAACzB;kBAAI,GADHyB,QAAQ,CAACC,EAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,UAAU;gBACfnF,KAAK,EAAC,oBAAK;gBAAA4E,QAAA,eAEX9E,OAAA,CAACd,MAAM;kBAACuG,WAAW,EAAC,sCAAQ;kBAAAX,QAAA,EACzBpF,gBAAgB,CAACoG,GAAG,CAACC,MAAM,iBAC1B/F,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE4F,MAAM,CAAC5F,KAAM;oBAAA2E,QAAA,EAC5CiB,MAAM,CAAC7F;kBAAK,GADF6F,MAAM,CAAC5F,KAAK;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,UAAU;gBACfnF,KAAK,EAAC,oBAAK;gBAAA4E,QAAA,eAEX9E,OAAA,CAACd,MAAM;kBAACuG,WAAW,EAAC,sCAAQ;kBAAAX,QAAA,EACzBnF,gBAAgB,CAACmG,GAAG,CAACC,MAAM,iBAC1B/F,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE4F,MAAM,CAAC5F,KAAM;oBAAA2E,QAAA,EAC5CiB,MAAM,CAAC7F;kBAAK,GADF6F,MAAM,CAAC5F,KAAK;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7F,OAAA,CAACV,GAAG;QAAC0F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ9E,OAAA,CAACT,IAAI;UAAC0F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,eAC7C9E,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXnF,KAAK,EAAC,eAAK;gBACXoF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAE/F,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAqF,QAAA,eAE3C9E,OAAA,CAAChB,KAAK;kBAACyG,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXnF,KAAK,EAAC,eAAK;gBACXoF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAE/F,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAqF,QAAA,eAE3C9E,OAAA,CAAChB,KAAK;kBAACyG,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXnF,KAAK,EAAC,eAAK;gBACXoF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAE/F,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAqF,QAAA,eAE3C9E,OAAA,CAAChB,KAAK;kBAACyG,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7F,OAAA,CAACV,GAAG;QAAC0F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ9E,OAAA,CAACT,IAAI;UAAC0F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,eAC7C9E,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBnF,KAAK,EAAC,0BAAM;gBAAA4E,QAAA,eAEZ9E,OAAA,CAACd,MAAM;kBAACuG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,EAC1B7E,iBAAiB,CAAC6F,GAAG,CAACC,MAAM,iBAC3B/F,OAAA,CAACK,MAAM;oBAAoBF,KAAK,EAAE4F,MAAM,CAAC5F,KAAM;oBAAA2E,QAAA,EAC5CiB,MAAM,CAAC7F;kBAAK,GADF6F,MAAM,CAAC5F,KAAK;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,iBAAiB;gBACtBnF,KAAK,EAAC,0BAAM;gBAAA4E,QAAA,eAEZ9E,OAAA,CAACd,MAAM;kBACL8H,IAAI,EAAC,MAAM;kBACXvB,WAAW,EAAC,0EAAc;kBAC1BP,KAAK,EAAE;oBAAEmB,KAAK,EAAE;kBAAO;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7F,OAAA,CAACV,GAAG;QAAC0F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ9E,OAAA,CAACT,IAAI;UAAC0F,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C9E,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,eAAe;gBACpBnF,KAAK,EAAC,wDAAW;gBACjB+G,aAAa,EAAC,SAAS;gBAAAnC,QAAA,eAEvB9E,OAAA,CAACb,MAAM;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7F,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBnF,KAAK,EAAC,0EAAc;gBACpB+G,aAAa,EAAC,SAAS;gBAAAnC,QAAA,eAEvB9E,OAAA,CAACb,MAAM;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7F,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,YAAY;gBACjBnF,KAAK,EAAC,0BAAM;gBAAA4E,QAAA,eAEZ9E,OAAA,CAAChB,KAAK;kBAACyG,WAAW,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7F,OAAA,CAACX,GAAG;YAAC0F,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd9E,OAAA,CAACV,GAAG;cAAC0F,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;gBACRC,IAAI,EAAC,oBAAoB;gBACzBnF,KAAK,EAAC,sCAAQ;gBAAA4E,QAAA,eAEd9E,OAAA,CAACM,QAAQ;kBACP0F,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,8EAAkB;kBAC9BQ,SAAS,EAAE,IAAK;kBAChBC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7F,OAAA,CAACV,GAAG;QAAC0F,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ9E,OAAA,CAACjB,IAAI,CAACqG,IAAI;UAAAN,QAAA,eACR9E,OAAA,CAACR,KAAK;YAAAsF,QAAA,gBACJ9E,OAAA,CAACZ,MAAM;cACL+G,IAAI,EAAC,SAAS;cACde,QAAQ,EAAC,QAAQ;cACjBvG,OAAO,EAAEA,OAAQ;cACjBwG,IAAI,EAAC,OAAO;cAAArC,QAAA,EAEXrE,aAAa,GAAG,MAAM,GAAG;YAAM;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACT7F,OAAA,CAACZ,MAAM;cACL+H,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAM5G,IAAI,CAAC6G,WAAW,CAAC,CAAE;cAAAvC,QAAA,EACnC;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACjF,EAAA,CA/eIL,WAAuC;AAAA+G,EAAA,GAAvC/G,WAAuC;AAif7C,eAAeA,WAAW;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
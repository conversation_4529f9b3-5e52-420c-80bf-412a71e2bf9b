{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst Element = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    size,\n    shape\n  } = props;\n  const sizeCls = classNames({\n    [\"\".concat(prefixCls, \"-lg\")]: size === 'large',\n    [\"\".concat(prefixCls, \"-sm\")]: size === 'small'\n  });\n  const shapeCls = classNames({\n    [\"\".concat(prefixCls, \"-circle\")]: shape === 'circle',\n    [\"\".concat(prefixCls, \"-square\")]: shape === 'square',\n    [\"\".concat(prefixCls, \"-round\")]: shape === 'round'\n  });\n  const sizeStyle = React.useMemo(() => typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: \"\".concat(size, \"px\")\n  } : {}, [size]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(prefixCls, sizeCls, shapeCls, className),\n    style: Object.assign(Object.assign({}, sizeStyle), style)\n  });\n};\nexport default Element;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
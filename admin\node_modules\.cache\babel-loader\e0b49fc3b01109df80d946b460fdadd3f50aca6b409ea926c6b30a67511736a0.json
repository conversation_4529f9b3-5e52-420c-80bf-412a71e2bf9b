{"ast": null, "code": "export var toArray = function toArray(value) {\n  return Array.isArray(value) ? value : value !== undefined ? [value] : [];\n};\nexport var fillFieldNames = function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  return {\n    _title: label ? [label] : ['title', 'label'],\n    value: value || 'value',\n    key: value || 'value',\n    children: children || 'children'\n  };\n};\nexport var isCheckDisabled = function isCheckDisabled(node) {\n  return !node || node.disabled || node.disableCheckbox || node.checkable === false;\n};\nexport var getAllKeys = function getAllKeys(treeData, fieldNames) {\n  var keys = [];\n  var dig = function dig(list) {\n    list.forEach(function (item) {\n      var children = item[fieldNames.children];\n      if (children) {\n        keys.push(item[fieldNames.value]);\n        dig(children);\n      }\n    });\n  };\n  dig(treeData);\n  return keys;\n};\nexport var isNil = function isNil(val) {\n  return val === null || val === undefined;\n};", "map": {"version": 3, "names": ["toArray", "value", "Array", "isArray", "undefined", "fillFieldNames", "fieldNames", "_ref", "label", "children", "_title", "key", "isCheckDisabled", "node", "disabled", "disableCheckbox", "checkable", "getAllKeys", "treeData", "keys", "dig", "list", "for<PERSON>ach", "item", "push", "isNil", "val"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-tree-select/es/utils/valueUtil.js"], "sourcesContent": ["export var toArray = function toArray(value) {\n  return Array.isArray(value) ? value : value !== undefined ? [value] : [];\n};\nexport var fillFieldNames = function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  return {\n    _title: label ? [label] : ['title', 'label'],\n    value: value || 'value',\n    key: value || 'value',\n    children: children || 'children'\n  };\n};\nexport var isCheckDisabled = function isCheckDisabled(node) {\n  return !node || node.disabled || node.disableCheckbox || node.checkable === false;\n};\nexport var getAllKeys = function getAllKeys(treeData, fieldNames) {\n  var keys = [];\n  var dig = function dig(list) {\n    list.forEach(function (item) {\n      var children = item[fieldNames.children];\n      if (children) {\n        keys.push(item[fieldNames.value]);\n        dig(children);\n      }\n    });\n  };\n  dig(treeData);\n  return keys;\n};\nexport var isNil = function isNil(val) {\n  return val === null || val === undefined;\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EAC3C,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAK,KAAKG,SAAS,GAAG,CAACH,KAAK,CAAC,GAAG,EAAE;AAC1E,CAAC;AACD,OAAO,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACC,UAAU,EAAE;EAC9D,IAAIC,IAAI,GAAGD,UAAU,IAAI,CAAC,CAAC;IACzBE,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBP,KAAK,GAAGM,IAAI,CAACN,KAAK;IAClBQ,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC1B,OAAO;IACLC,MAAM,EAAEF,KAAK,GAAG,CAACA,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;IAC5CP,KAAK,EAAEA,KAAK,IAAI,OAAO;IACvBU,GAAG,EAAEV,KAAK,IAAI,OAAO;IACrBQ,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC;AACH,CAAC;AACD,OAAO,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAE;EAC1D,OAAO,CAACA,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,eAAe,IAAIF,IAAI,CAACG,SAAS,KAAK,KAAK;AACnF,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,QAAQ,EAAEZ,UAAU,EAAE;EAChE,IAAIa,IAAI,GAAG,EAAE;EACb,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACC,IAAI,EAAE;IAC3BA,IAAI,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC3B,IAAId,QAAQ,GAAGc,IAAI,CAACjB,UAAU,CAACG,QAAQ,CAAC;MACxC,IAAIA,QAAQ,EAAE;QACZU,IAAI,CAACK,IAAI,CAACD,IAAI,CAACjB,UAAU,CAACL,KAAK,CAAC,CAAC;QACjCmB,GAAG,CAACX,QAAQ,CAAC;MACf;IACF,CAAC,CAAC;EACJ,CAAC;EACDW,GAAG,CAACF,QAAQ,CAAC;EACb,OAAOC,IAAI;AACb,CAAC;AACD,OAAO,IAAIM,KAAK,GAAG,SAASA,KAAKA,CAACC,GAAG,EAAE;EACrC,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKtB,SAAS;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
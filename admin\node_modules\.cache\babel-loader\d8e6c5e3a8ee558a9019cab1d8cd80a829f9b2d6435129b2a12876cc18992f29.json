{"ast": null, "code": "import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { getStyle as getCheckboxStyle } from '../../checkbox/style';\nimport { genFocusOutline, resetComponent } from '../../style';\nimport { genCollapseMotion } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { genDirectoryStyle } from './directory';\n// ============================ Keyframes =============================\nconst treeNodeFX = new Keyframes('ant-tree-node-fx-do-not-use', {\n  '0%': {\n    opacity: 0\n  },\n  '100%': {\n    opacity: 1\n  }\n});\n// ============================== Switch ==============================\nconst getSwitchStyle = (prefixCls, token) => ({\n  [\".\".concat(prefixCls, \"-switcher-icon\")]: {\n    display: 'inline-block',\n    fontSize: 10,\n    verticalAlign: 'baseline',\n    svg: {\n      transition: \"transform \".concat(token.motionDurationSlow)\n    }\n  }\n});\n// =============================== Drop ===============================\nconst getDropIndicatorStyle = (prefixCls, token) => ({\n  [\".\".concat(prefixCls, \"-drop-indicator\")]: {\n    position: 'absolute',\n    // it should displayed over the following node\n    zIndex: 1,\n    height: 2,\n    backgroundColor: token.colorPrimary,\n    borderRadius: 1,\n    pointerEvents: 'none',\n    '&:after': {\n      position: 'absolute',\n      top: -3,\n      insetInlineStart: -6,\n      width: 8,\n      height: 8,\n      backgroundColor: 'transparent',\n      border: \"\".concat(unit(token.lineWidthBold), \" solid \").concat(token.colorPrimary),\n      borderRadius: '50%',\n      content: '\"\"'\n    }\n  }\n});\nexport const genBaseStyle = (prefixCls, token) => {\n  const {\n    treeCls,\n    treeNodeCls,\n    treeNodePadding,\n    titleHeight,\n    indentSize,\n    nodeSelectedBg,\n    nodeHoverBg,\n    colorTextQuaternary,\n    controlItemBgActiveDisabled\n  } = token;\n  return {\n    [treeCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      background: token.colorBgContainer,\n      borderRadius: token.borderRadius,\n      transition: \"background-color \".concat(token.motionDurationSlow),\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      [\"&\".concat(treeCls, \"-rtl \").concat(treeCls, \"-switcher_close \").concat(treeCls, \"-switcher-icon svg\")]: {\n        transform: 'rotate(90deg)'\n      },\n      [\"&-focused:not(:hover):not(\".concat(treeCls, \"-active-focused)\")]: Object.assign({}, genFocusOutline(token)),\n      // =================== Virtual List ===================\n      [\"\".concat(treeCls, \"-list-holder-inner\")]: {\n        alignItems: 'flex-start'\n      },\n      [\"&\".concat(treeCls, \"-block-node\")]: {\n        [\"\".concat(treeCls, \"-list-holder-inner\")]: {\n          alignItems: 'stretch',\n          // >>> Title\n          [\"\".concat(treeCls, \"-node-content-wrapper\")]: {\n            flex: 'auto'\n          },\n          // >>> Drag\n          [\"\".concat(treeNodeCls, \".dragging:after\")]: {\n            position: 'absolute',\n            inset: 0,\n            border: \"1px solid \".concat(token.colorPrimary),\n            opacity: 0,\n            animationName: treeNodeFX,\n            animationDuration: token.motionDurationSlow,\n            animationPlayState: 'running',\n            animationFillMode: 'forwards',\n            content: '\"\"',\n            pointerEvents: 'none',\n            borderRadius: token.borderRadius\n          }\n        }\n      },\n      // ===================== TreeNode =====================\n      [treeNodeCls]: {\n        display: 'flex',\n        alignItems: 'flex-start',\n        marginBottom: treeNodePadding,\n        lineHeight: unit(titleHeight),\n        position: 'relative',\n        // 非常重要，避免 drop-indicator 在拖拽过程中闪烁\n        '&:before': {\n          content: '\"\"',\n          position: 'absolute',\n          zIndex: 1,\n          insetInlineStart: 0,\n          width: '100%',\n          top: '100%',\n          height: treeNodePadding\n        },\n        // Disabled\n        [\"&-disabled \".concat(treeCls, \"-node-content-wrapper\")]: {\n          color: token.colorTextDisabled,\n          cursor: 'not-allowed',\n          '&:hover': {\n            background: 'transparent'\n          }\n        },\n        [\"\".concat(treeCls, \"-checkbox-disabled + \").concat(treeCls, \"-node-selected,&\").concat(treeNodeCls, \"-disabled\").concat(treeNodeCls, \"-selected \").concat(treeCls, \"-node-content-wrapper\")]: {\n          backgroundColor: controlItemBgActiveDisabled\n        },\n        // we can not set pointer-events to none for checkbox in tree\n        // ref: https://github.com/ant-design/ant-design/issues/39822#issuecomment-2605234058\n        [\"\".concat(treeCls, \"-checkbox-disabled\")]: {\n          pointerEvents: 'unset'\n        },\n        // not disable\n        [\"&:not(\".concat(treeNodeCls, \"-disabled)\")]: {\n          // >>> Title\n          [\"\".concat(treeCls, \"-node-content-wrapper\")]: {\n            '&:hover': {\n              color: token.nodeHoverColor\n            }\n          }\n        },\n        [\"&-active \".concat(treeCls, \"-node-content-wrapper\")]: {\n          background: token.controlItemBgHover\n        },\n        [\"&:not(\".concat(treeNodeCls, \"-disabled).filter-node \").concat(treeCls, \"-title\")]: {\n          color: token.colorPrimary,\n          fontWeight: 500\n        },\n        '&-draggable': {\n          cursor: 'grab',\n          [\"\".concat(treeCls, \"-draggable-icon\")]: {\n            // https://github.com/ant-design/ant-design/issues/41915\n            flexShrink: 0,\n            width: titleHeight,\n            textAlign: 'center',\n            visibility: 'visible',\n            color: colorTextQuaternary\n          },\n          [\"&\".concat(treeNodeCls, \"-disabled \").concat(treeCls, \"-draggable-icon\")]: {\n            visibility: 'hidden'\n          }\n        }\n      },\n      // >>> Indent\n      [\"\".concat(treeCls, \"-indent\")]: {\n        alignSelf: 'stretch',\n        whiteSpace: 'nowrap',\n        userSelect: 'none',\n        '&-unit': {\n          display: 'inline-block',\n          width: indentSize\n        }\n      },\n      // >>> Drag Handler\n      [\"\".concat(treeCls, \"-draggable-icon\")]: {\n        visibility: 'hidden'\n      },\n      // Switcher / Checkbox\n      [\"\".concat(treeCls, \"-switcher, \").concat(treeCls, \"-checkbox\")]: {\n        marginInlineEnd: token.calc(token.calc(titleHeight).sub(token.controlInteractiveSize)).div(2).equal()\n      },\n      // >>> Switcher\n      [\"\".concat(treeCls, \"-switcher\")]: Object.assign(Object.assign({}, getSwitchStyle(prefixCls, token)), {\n        position: 'relative',\n        flex: 'none',\n        alignSelf: 'stretch',\n        width: titleHeight,\n        textAlign: 'center',\n        cursor: 'pointer',\n        userSelect: 'none',\n        transition: \"all \".concat(token.motionDurationSlow),\n        '&-noop': {\n          cursor: 'unset'\n        },\n        '&:before': {\n          pointerEvents: 'none',\n          content: '\"\"',\n          width: titleHeight,\n          height: titleHeight,\n          position: 'absolute',\n          left: {\n            _skip_check_: true,\n            value: 0\n          },\n          top: 0,\n          borderRadius: token.borderRadius,\n          transition: \"all \".concat(token.motionDurationSlow)\n        },\n        [\"&:not(\".concat(treeCls, \"-switcher-noop):hover:before\")]: {\n          backgroundColor: token.colorBgTextHover\n        },\n        [\"&_close \".concat(treeCls, \"-switcher-icon svg\")]: {\n          transform: 'rotate(-90deg)'\n        },\n        '&-loading-icon': {\n          color: token.colorPrimary\n        },\n        '&-leaf-line': {\n          position: 'relative',\n          zIndex: 1,\n          display: 'inline-block',\n          width: '100%',\n          height: '100%',\n          // https://github.com/ant-design/ant-design/issues/31884\n          '&:before': {\n            position: 'absolute',\n            top: 0,\n            insetInlineEnd: token.calc(titleHeight).div(2).equal(),\n            bottom: token.calc(treeNodePadding).mul(-1).equal(),\n            marginInlineStart: -1,\n            borderInlineEnd: \"1px solid \".concat(token.colorBorder),\n            content: '\"\"'\n          },\n          '&:after': {\n            position: 'absolute',\n            width: token.calc(token.calc(titleHeight).div(2).equal()).mul(0.8).equal(),\n            height: token.calc(titleHeight).div(2).equal(),\n            borderBottom: \"1px solid \".concat(token.colorBorder),\n            content: '\"\"'\n          }\n        }\n      }),\n      // >>> Title\n      // add `${treeCls}-checkbox + span` to cover checkbox `${checkboxCls} + span`\n      [\"\".concat(treeCls, \"-node-content-wrapper\")]: Object.assign(Object.assign({\n        position: 'relative',\n        minHeight: titleHeight,\n        paddingBlock: 0,\n        paddingInline: token.paddingXS,\n        background: 'transparent',\n        borderRadius: token.borderRadius,\n        cursor: 'pointer',\n        transition: \"all \".concat(token.motionDurationMid, \", border 0s, line-height 0s, box-shadow 0s\")\n      }, getDropIndicatorStyle(prefixCls, token)), {\n        '&:hover': {\n          backgroundColor: nodeHoverBg\n        },\n        [\"&\".concat(treeCls, \"-node-selected\")]: {\n          color: token.nodeSelectedColor,\n          backgroundColor: nodeSelectedBg\n        },\n        // Icon\n        [\"\".concat(treeCls, \"-iconEle\")]: {\n          display: 'inline-block',\n          width: titleHeight,\n          height: titleHeight,\n          textAlign: 'center',\n          verticalAlign: 'top',\n          '&:empty': {\n            display: 'none'\n          }\n        }\n      }),\n      // https://github.com/ant-design/ant-design/issues/28217\n      [\"\".concat(treeCls, \"-unselectable \").concat(treeCls, \"-node-content-wrapper:hover\")]: {\n        backgroundColor: 'transparent'\n      },\n      [\"\".concat(treeNodeCls, \".drop-container > [draggable]\")]: {\n        boxShadow: \"0 0 0 2px \".concat(token.colorPrimary)\n      },\n      // ==================== Show Line =====================\n      '&-show-line': {\n        // ================ Indent lines ================\n        [\"\".concat(treeCls, \"-indent-unit\")]: {\n          position: 'relative',\n          height: '100%',\n          '&:before': {\n            position: 'absolute',\n            top: 0,\n            insetInlineEnd: token.calc(titleHeight).div(2).equal(),\n            bottom: token.calc(treeNodePadding).mul(-1).equal(),\n            borderInlineEnd: \"1px solid \".concat(token.colorBorder),\n            content: '\"\"'\n          },\n          '&-end:before': {\n            display: 'none'\n          }\n        },\n        // ============== Cover Background ==============\n        [\"\".concat(treeCls, \"-switcher\")]: {\n          background: 'transparent',\n          '&-line-icon': {\n            // https://github.com/ant-design/ant-design/issues/32813\n            verticalAlign: '-0.15em'\n          }\n        }\n      },\n      [\"\".concat(treeNodeCls, \"-leaf-last \").concat(treeCls, \"-switcher-leaf-line:before\")]: {\n        top: 'auto !important',\n        bottom: 'auto !important',\n        height: \"\".concat(unit(token.calc(titleHeight).div(2).equal()), \" !important\")\n      }\n    })\n  };\n};\n// ============================== Merged ==============================\nexport const genTreeStyle = function (prefixCls, token) {\n  let enableDirectory = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  const treeCls = \".\".concat(prefixCls);\n  const treeNodeCls = \"\".concat(treeCls, \"-treenode\");\n  const treeNodePadding = token.calc(token.paddingXS).div(2).equal();\n  const treeToken = mergeToken(token, {\n    treeCls,\n    treeNodeCls,\n    treeNodePadding\n  });\n  return [\n  // Basic\n  genBaseStyle(prefixCls, treeToken),\n  // Directory\n  enableDirectory && genDirectoryStyle(treeToken)].filter(Boolean);\n};\nexport const initComponentToken = token => {\n  const {\n    controlHeightSM,\n    controlItemBgHover,\n    controlItemBgActive\n  } = token;\n  const titleHeight = controlHeightSM;\n  return {\n    titleHeight,\n    indentSize: titleHeight,\n    nodeHoverBg: controlItemBgHover,\n    nodeHoverColor: token.colorText,\n    nodeSelectedBg: controlItemBgActive,\n    nodeSelectedColor: token.colorText\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    colorTextLightSolid,\n    colorPrimary\n  } = token;\n  return Object.assign(Object.assign({}, initComponentToken(token)), {\n    directoryNodeSelectedColor: colorTextLightSolid,\n    directoryNodeSelectedBg: colorPrimary\n  });\n};\nexport default genStyleHooks('Tree', (token, _ref) => {\n  let {\n    prefixCls\n  } = _ref;\n  return [{\n    [token.componentCls]: getCheckboxStyle(\"\".concat(prefixCls, \"-checkbox\"), token)\n  }, genTreeStyle(prefixCls, token), genCollapseMotion(token)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["Keyframes", "unit", "getStyle", "getCheckboxStyle", "genFocusOutline", "resetComponent", "genCollapseMotion", "genStyleHooks", "mergeToken", "genDirectoryStyle", "treeNodeFX", "opacity", "getSwitchStyle", "prefixCls", "token", "concat", "display", "fontSize", "verticalAlign", "svg", "transition", "motionDurationSlow", "getDropIndicatorStyle", "position", "zIndex", "height", "backgroundColor", "colorPrimary", "borderRadius", "pointerEvents", "top", "insetInlineStart", "width", "border", "lineWidthBold", "content", "genBaseStyle", "treeCls", "treeNodeCls", "treeNodePadding", "titleHeight", "indentSize", "nodeSelectedBg", "nodeHoverBg", "colorTextQuaternary", "controlItemBgActiveDisabled", "Object", "assign", "background", "colorBgContainer", "direction", "transform", "alignItems", "flex", "inset", "animationName", "animationDuration", "animationPlayState", "animationFillMode", "marginBottom", "lineHeight", "color", "colorTextDisabled", "cursor", "nodeHoverColor", "controlItemBgHover", "fontWeight", "flexShrink", "textAlign", "visibility", "alignSelf", "whiteSpace", "userSelect", "marginInlineEnd", "calc", "sub", "controlInteractiveSize", "div", "equal", "left", "_skip_check_", "value", "colorBgTextHover", "insetInlineEnd", "bottom", "mul", "marginInlineStart", "borderInlineEnd", "colorBorder", "borderBottom", "minHeight", "paddingBlock", "paddingInline", "paddingXS", "motionDurationMid", "nodeSelectedColor", "boxShadow", "genTreeStyle", "enableDirectory", "arguments", "length", "undefined", "treeToken", "filter", "Boolean", "initComponentToken", "controlHeightSM", "controlItemBgActive", "colorText", "prepareComponentToken", "colorTextLightSolid", "directoryNodeSelectedColor", "directoryNodeSelectedBg", "_ref", "componentCls"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/tree/style/index.js"], "sourcesContent": ["import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { getStyle as getCheckboxStyle } from '../../checkbox/style';\nimport { genFocusOutline, resetComponent } from '../../style';\nimport { genCollapseMotion } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { genDirectoryStyle } from './directory';\n// ============================ Keyframes =============================\nconst treeNodeFX = new Keyframes('ant-tree-node-fx-do-not-use', {\n  '0%': {\n    opacity: 0\n  },\n  '100%': {\n    opacity: 1\n  }\n});\n// ============================== Switch ==============================\nconst getSwitchStyle = (prefixCls, token) => ({\n  [`.${prefixCls}-switcher-icon`]: {\n    display: 'inline-block',\n    fontSize: 10,\n    verticalAlign: 'baseline',\n    svg: {\n      transition: `transform ${token.motionDurationSlow}`\n    }\n  }\n});\n// =============================== Drop ===============================\nconst getDropIndicatorStyle = (prefixCls, token) => ({\n  [`.${prefixCls}-drop-indicator`]: {\n    position: 'absolute',\n    // it should displayed over the following node\n    zIndex: 1,\n    height: 2,\n    backgroundColor: token.colorPrimary,\n    borderRadius: 1,\n    pointerEvents: 'none',\n    '&:after': {\n      position: 'absolute',\n      top: -3,\n      insetInlineStart: -6,\n      width: 8,\n      height: 8,\n      backgroundColor: 'transparent',\n      border: `${unit(token.lineWidthBold)} solid ${token.colorPrimary}`,\n      borderRadius: '50%',\n      content: '\"\"'\n    }\n  }\n});\nexport const genBaseStyle = (prefixCls, token) => {\n  const {\n    treeCls,\n    treeNodeCls,\n    treeNodePadding,\n    titleHeight,\n    indentSize,\n    nodeSelectedBg,\n    nodeHoverBg,\n    colorTextQuaternary,\n    controlItemBgActiveDisabled\n  } = token;\n  return {\n    [treeCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      background: token.colorBgContainer,\n      borderRadius: token.borderRadius,\n      transition: `background-color ${token.motionDurationSlow}`,\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      [`&${treeCls}-rtl ${treeCls}-switcher_close ${treeCls}-switcher-icon svg`]: {\n        transform: 'rotate(90deg)'\n      },\n      [`&-focused:not(:hover):not(${treeCls}-active-focused)`]: Object.assign({}, genFocusOutline(token)),\n      // =================== Virtual List ===================\n      [`${treeCls}-list-holder-inner`]: {\n        alignItems: 'flex-start'\n      },\n      [`&${treeCls}-block-node`]: {\n        [`${treeCls}-list-holder-inner`]: {\n          alignItems: 'stretch',\n          // >>> Title\n          [`${treeCls}-node-content-wrapper`]: {\n            flex: 'auto'\n          },\n          // >>> Drag\n          [`${treeNodeCls}.dragging:after`]: {\n            position: 'absolute',\n            inset: 0,\n            border: `1px solid ${token.colorPrimary}`,\n            opacity: 0,\n            animationName: treeNodeFX,\n            animationDuration: token.motionDurationSlow,\n            animationPlayState: 'running',\n            animationFillMode: 'forwards',\n            content: '\"\"',\n            pointerEvents: 'none',\n            borderRadius: token.borderRadius\n          }\n        }\n      },\n      // ===================== TreeNode =====================\n      [treeNodeCls]: {\n        display: 'flex',\n        alignItems: 'flex-start',\n        marginBottom: treeNodePadding,\n        lineHeight: unit(titleHeight),\n        position: 'relative',\n        // 非常重要，避免 drop-indicator 在拖拽过程中闪烁\n        '&:before': {\n          content: '\"\"',\n          position: 'absolute',\n          zIndex: 1,\n          insetInlineStart: 0,\n          width: '100%',\n          top: '100%',\n          height: treeNodePadding\n        },\n        // Disabled\n        [`&-disabled ${treeCls}-node-content-wrapper`]: {\n          color: token.colorTextDisabled,\n          cursor: 'not-allowed',\n          '&:hover': {\n            background: 'transparent'\n          }\n        },\n        [`${treeCls}-checkbox-disabled + ${treeCls}-node-selected,&${treeNodeCls}-disabled${treeNodeCls}-selected ${treeCls}-node-content-wrapper`]: {\n          backgroundColor: controlItemBgActiveDisabled\n        },\n        // we can not set pointer-events to none for checkbox in tree\n        // ref: https://github.com/ant-design/ant-design/issues/39822#issuecomment-2605234058\n        [`${treeCls}-checkbox-disabled`]: {\n          pointerEvents: 'unset'\n        },\n        // not disable\n        [`&:not(${treeNodeCls}-disabled)`]: {\n          // >>> Title\n          [`${treeCls}-node-content-wrapper`]: {\n            '&:hover': {\n              color: token.nodeHoverColor\n            }\n          }\n        },\n        [`&-active ${treeCls}-node-content-wrapper`]: {\n          background: token.controlItemBgHover\n        },\n        [`&:not(${treeNodeCls}-disabled).filter-node ${treeCls}-title`]: {\n          color: token.colorPrimary,\n          fontWeight: 500\n        },\n        '&-draggable': {\n          cursor: 'grab',\n          [`${treeCls}-draggable-icon`]: {\n            // https://github.com/ant-design/ant-design/issues/41915\n            flexShrink: 0,\n            width: titleHeight,\n            textAlign: 'center',\n            visibility: 'visible',\n            color: colorTextQuaternary\n          },\n          [`&${treeNodeCls}-disabled ${treeCls}-draggable-icon`]: {\n            visibility: 'hidden'\n          }\n        }\n      },\n      // >>> Indent\n      [`${treeCls}-indent`]: {\n        alignSelf: 'stretch',\n        whiteSpace: 'nowrap',\n        userSelect: 'none',\n        '&-unit': {\n          display: 'inline-block',\n          width: indentSize\n        }\n      },\n      // >>> Drag Handler\n      [`${treeCls}-draggable-icon`]: {\n        visibility: 'hidden'\n      },\n      // Switcher / Checkbox\n      [`${treeCls}-switcher, ${treeCls}-checkbox`]: {\n        marginInlineEnd: token.calc(token.calc(titleHeight).sub(token.controlInteractiveSize)).div(2).equal()\n      },\n      // >>> Switcher\n      [`${treeCls}-switcher`]: Object.assign(Object.assign({}, getSwitchStyle(prefixCls, token)), {\n        position: 'relative',\n        flex: 'none',\n        alignSelf: 'stretch',\n        width: titleHeight,\n        textAlign: 'center',\n        cursor: 'pointer',\n        userSelect: 'none',\n        transition: `all ${token.motionDurationSlow}`,\n        '&-noop': {\n          cursor: 'unset'\n        },\n        '&:before': {\n          pointerEvents: 'none',\n          content: '\"\"',\n          width: titleHeight,\n          height: titleHeight,\n          position: 'absolute',\n          left: {\n            _skip_check_: true,\n            value: 0\n          },\n          top: 0,\n          borderRadius: token.borderRadius,\n          transition: `all ${token.motionDurationSlow}`\n        },\n        [`&:not(${treeCls}-switcher-noop):hover:before`]: {\n          backgroundColor: token.colorBgTextHover\n        },\n        [`&_close ${treeCls}-switcher-icon svg`]: {\n          transform: 'rotate(-90deg)'\n        },\n        '&-loading-icon': {\n          color: token.colorPrimary\n        },\n        '&-leaf-line': {\n          position: 'relative',\n          zIndex: 1,\n          display: 'inline-block',\n          width: '100%',\n          height: '100%',\n          // https://github.com/ant-design/ant-design/issues/31884\n          '&:before': {\n            position: 'absolute',\n            top: 0,\n            insetInlineEnd: token.calc(titleHeight).div(2).equal(),\n            bottom: token.calc(treeNodePadding).mul(-1).equal(),\n            marginInlineStart: -1,\n            borderInlineEnd: `1px solid ${token.colorBorder}`,\n            content: '\"\"'\n          },\n          '&:after': {\n            position: 'absolute',\n            width: token.calc(token.calc(titleHeight).div(2).equal()).mul(0.8).equal(),\n            height: token.calc(titleHeight).div(2).equal(),\n            borderBottom: `1px solid ${token.colorBorder}`,\n            content: '\"\"'\n          }\n        }\n      }),\n      // >>> Title\n      // add `${treeCls}-checkbox + span` to cover checkbox `${checkboxCls} + span`\n      [`${treeCls}-node-content-wrapper`]: Object.assign(Object.assign({\n        position: 'relative',\n        minHeight: titleHeight,\n        paddingBlock: 0,\n        paddingInline: token.paddingXS,\n        background: 'transparent',\n        borderRadius: token.borderRadius,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`\n      }, getDropIndicatorStyle(prefixCls, token)), {\n        '&:hover': {\n          backgroundColor: nodeHoverBg\n        },\n        [`&${treeCls}-node-selected`]: {\n          color: token.nodeSelectedColor,\n          backgroundColor: nodeSelectedBg\n        },\n        // Icon\n        [`${treeCls}-iconEle`]: {\n          display: 'inline-block',\n          width: titleHeight,\n          height: titleHeight,\n          textAlign: 'center',\n          verticalAlign: 'top',\n          '&:empty': {\n            display: 'none'\n          }\n        }\n      }),\n      // https://github.com/ant-design/ant-design/issues/28217\n      [`${treeCls}-unselectable ${treeCls}-node-content-wrapper:hover`]: {\n        backgroundColor: 'transparent'\n      },\n      [`${treeNodeCls}.drop-container > [draggable]`]: {\n        boxShadow: `0 0 0 2px ${token.colorPrimary}`\n      },\n      // ==================== Show Line =====================\n      '&-show-line': {\n        // ================ Indent lines ================\n        [`${treeCls}-indent-unit`]: {\n          position: 'relative',\n          height: '100%',\n          '&:before': {\n            position: 'absolute',\n            top: 0,\n            insetInlineEnd: token.calc(titleHeight).div(2).equal(),\n            bottom: token.calc(treeNodePadding).mul(-1).equal(),\n            borderInlineEnd: `1px solid ${token.colorBorder}`,\n            content: '\"\"'\n          },\n          '&-end:before': {\n            display: 'none'\n          }\n        },\n        // ============== Cover Background ==============\n        [`${treeCls}-switcher`]: {\n          background: 'transparent',\n          '&-line-icon': {\n            // https://github.com/ant-design/ant-design/issues/32813\n            verticalAlign: '-0.15em'\n          }\n        }\n      },\n      [`${treeNodeCls}-leaf-last ${treeCls}-switcher-leaf-line:before`]: {\n        top: 'auto !important',\n        bottom: 'auto !important',\n        height: `${unit(token.calc(titleHeight).div(2).equal())} !important`\n      }\n    })\n  };\n};\n// ============================== Merged ==============================\nexport const genTreeStyle = (prefixCls, token,\n/**\n * @descCN 是否启用目录树样式\n * @descEN Whether to enable directory style\n * @default true\n */\nenableDirectory = true) => {\n  const treeCls = `.${prefixCls}`;\n  const treeNodeCls = `${treeCls}-treenode`;\n  const treeNodePadding = token.calc(token.paddingXS).div(2).equal();\n  const treeToken = mergeToken(token, {\n    treeCls,\n    treeNodeCls,\n    treeNodePadding\n  });\n  return [\n  // Basic\n  genBaseStyle(prefixCls, treeToken),\n  // Directory\n  enableDirectory && genDirectoryStyle(treeToken)].filter(Boolean);\n};\nexport const initComponentToken = token => {\n  const {\n    controlHeightSM,\n    controlItemBgHover,\n    controlItemBgActive\n  } = token;\n  const titleHeight = controlHeightSM;\n  return {\n    titleHeight,\n    indentSize: titleHeight,\n    nodeHoverBg: controlItemBgHover,\n    nodeHoverColor: token.colorText,\n    nodeSelectedBg: controlItemBgActive,\n    nodeSelectedColor: token.colorText\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    colorTextLightSolid,\n    colorPrimary\n  } = token;\n  return Object.assign(Object.assign({}, initComponentToken(token)), {\n    directoryNodeSelectedColor: colorTextLightSolid,\n    directoryNodeSelectedBg: colorPrimary\n  });\n};\nexport default genStyleHooks('Tree', (token, {\n  prefixCls\n}) => [{\n  [token.componentCls]: getCheckboxStyle(`${prefixCls}-checkbox`, token)\n}, genTreeStyle(prefixCls, token), genCollapseMotion(token)], prepareComponentToken);"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,qBAAqB;AACrD,SAASC,QAAQ,IAAIC,gBAAgB,QAAQ,sBAAsB;AACnE,SAASC,eAAe,EAAEC,cAAc,QAAQ,aAAa;AAC7D,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,SAASC,iBAAiB,QAAQ,aAAa;AAC/C;AACA,MAAMC,UAAU,GAAG,IAAIV,SAAS,CAAC,6BAA6B,EAAE;EAC9D,IAAI,EAAE;IACJW,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACNA,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF;AACA,MAAMC,cAAc,GAAGA,CAACC,SAAS,EAAEC,KAAK,MAAM;EAC5C,KAAAC,MAAA,CAAKF,SAAS,sBAAmB;IAC/BG,OAAO,EAAE,cAAc;IACvBC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,UAAU;IACzBC,GAAG,EAAE;MACHC,UAAU,eAAAL,MAAA,CAAeD,KAAK,CAACO,kBAAkB;IACnD;EACF;AACF,CAAC,CAAC;AACF;AACA,MAAMC,qBAAqB,GAAGA,CAACT,SAAS,EAAEC,KAAK,MAAM;EACnD,KAAAC,MAAA,CAAKF,SAAS,uBAAoB;IAChCU,QAAQ,EAAE,UAAU;IACpB;IACAC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,eAAe,EAAEZ,KAAK,CAACa,YAAY;IACnCC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,MAAM;IACrB,SAAS,EAAE;MACTN,QAAQ,EAAE,UAAU;MACpBO,GAAG,EAAE,CAAC,CAAC;MACPC,gBAAgB,EAAE,CAAC,CAAC;MACpBC,KAAK,EAAE,CAAC;MACRP,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE,aAAa;MAC9BO,MAAM,KAAAlB,MAAA,CAAKd,IAAI,CAACa,KAAK,CAACoB,aAAa,CAAC,aAAAnB,MAAA,CAAUD,KAAK,CAACa,YAAY,CAAE;MAClEC,YAAY,EAAE,KAAK;MACnBO,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,YAAY,GAAGA,CAACvB,SAAS,EAAEC,KAAK,KAAK;EAChD,MAAM;IACJuB,OAAO;IACPC,WAAW;IACXC,eAAe;IACfC,WAAW;IACXC,UAAU;IACVC,cAAc;IACdC,WAAW;IACXC,mBAAmB;IACnBC;EACF,CAAC,GAAG/B,KAAK;EACT,OAAO;IACL,CAACuB,OAAO,GAAGS,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1C,cAAc,CAACS,KAAK,CAAC,CAAC,EAAE;MACjEkC,UAAU,EAAElC,KAAK,CAACmC,gBAAgB;MAClCrB,YAAY,EAAEd,KAAK,CAACc,YAAY;MAChCR,UAAU,sBAAAL,MAAA,CAAsBD,KAAK,CAACO,kBAAkB,CAAE;MAC1D,OAAO,EAAE;QACP6B,SAAS,EAAE;MACb,CAAC;MACD,KAAAnC,MAAA,CAAKsB,OAAO,WAAAtB,MAAA,CAAQsB,OAAO,sBAAAtB,MAAA,CAAmBsB,OAAO,0BAAuB;QAC1Ec,SAAS,EAAE;MACb,CAAC;MACD,8BAAApC,MAAA,CAA8BsB,OAAO,wBAAqBS,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3C,eAAe,CAACU,KAAK,CAAC,CAAC;MACnG;MACA,IAAAC,MAAA,CAAIsB,OAAO,0BAAuB;QAChCe,UAAU,EAAE;MACd,CAAC;MACD,KAAArC,MAAA,CAAKsB,OAAO,mBAAgB;QAC1B,IAAAtB,MAAA,CAAIsB,OAAO,0BAAuB;UAChCe,UAAU,EAAE,SAAS;UACrB;UACA,IAAArC,MAAA,CAAIsB,OAAO,6BAA0B;YACnCgB,IAAI,EAAE;UACR,CAAC;UACD;UACA,IAAAtC,MAAA,CAAIuB,WAAW,uBAAoB;YACjCf,QAAQ,EAAE,UAAU;YACpB+B,KAAK,EAAE,CAAC;YACRrB,MAAM,eAAAlB,MAAA,CAAeD,KAAK,CAACa,YAAY,CAAE;YACzChB,OAAO,EAAE,CAAC;YACV4C,aAAa,EAAE7C,UAAU;YACzB8C,iBAAiB,EAAE1C,KAAK,CAACO,kBAAkB;YAC3CoC,kBAAkB,EAAE,SAAS;YAC7BC,iBAAiB,EAAE,UAAU;YAC7BvB,OAAO,EAAE,IAAI;YACbN,aAAa,EAAE,MAAM;YACrBD,YAAY,EAAEd,KAAK,CAACc;UACtB;QACF;MACF,CAAC;MACD;MACA,CAACU,WAAW,GAAG;QACbtB,OAAO,EAAE,MAAM;QACfoC,UAAU,EAAE,YAAY;QACxBO,YAAY,EAAEpB,eAAe;QAC7BqB,UAAU,EAAE3D,IAAI,CAACuC,WAAW,CAAC;QAC7BjB,QAAQ,EAAE,UAAU;QACpB;QACA,UAAU,EAAE;UACVY,OAAO,EAAE,IAAI;UACbZ,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE,CAAC;UACTO,gBAAgB,EAAE,CAAC;UACnBC,KAAK,EAAE,MAAM;UACbF,GAAG,EAAE,MAAM;UACXL,MAAM,EAAEc;QACV,CAAC;QACD;QACA,eAAAxB,MAAA,CAAesB,OAAO,6BAA0B;UAC9CwB,KAAK,EAAE/C,KAAK,CAACgD,iBAAiB;UAC9BC,MAAM,EAAE,aAAa;UACrB,SAAS,EAAE;YACTf,UAAU,EAAE;UACd;QACF,CAAC;QACD,IAAAjC,MAAA,CAAIsB,OAAO,2BAAAtB,MAAA,CAAwBsB,OAAO,sBAAAtB,MAAA,CAAmBuB,WAAW,eAAAvB,MAAA,CAAYuB,WAAW,gBAAAvB,MAAA,CAAasB,OAAO,6BAA0B;UAC3IX,eAAe,EAAEmB;QACnB,CAAC;QACD;QACA;QACA,IAAA9B,MAAA,CAAIsB,OAAO,0BAAuB;UAChCR,aAAa,EAAE;QACjB,CAAC;QACD;QACA,UAAAd,MAAA,CAAUuB,WAAW,kBAAe;UAClC;UACA,IAAAvB,MAAA,CAAIsB,OAAO,6BAA0B;YACnC,SAAS,EAAE;cACTwB,KAAK,EAAE/C,KAAK,CAACkD;YACf;UACF;QACF,CAAC;QACD,aAAAjD,MAAA,CAAasB,OAAO,6BAA0B;UAC5CW,UAAU,EAAElC,KAAK,CAACmD;QACpB,CAAC;QACD,UAAAlD,MAAA,CAAUuB,WAAW,6BAAAvB,MAAA,CAA0BsB,OAAO,cAAW;UAC/DwB,KAAK,EAAE/C,KAAK,CAACa,YAAY;UACzBuC,UAAU,EAAE;QACd,CAAC;QACD,aAAa,EAAE;UACbH,MAAM,EAAE,MAAM;UACd,IAAAhD,MAAA,CAAIsB,OAAO,uBAAoB;YAC7B;YACA8B,UAAU,EAAE,CAAC;YACbnC,KAAK,EAAEQ,WAAW;YAClB4B,SAAS,EAAE,QAAQ;YACnBC,UAAU,EAAE,SAAS;YACrBR,KAAK,EAAEjB;UACT,CAAC;UACD,KAAA7B,MAAA,CAAKuB,WAAW,gBAAAvB,MAAA,CAAasB,OAAO,uBAAoB;YACtDgC,UAAU,EAAE;UACd;QACF;MACF,CAAC;MACD;MACA,IAAAtD,MAAA,CAAIsB,OAAO,eAAY;QACrBiC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE,MAAM;QAClB,QAAQ,EAAE;UACRxD,OAAO,EAAE,cAAc;UACvBgB,KAAK,EAAES;QACT;MACF,CAAC;MACD;MACA,IAAA1B,MAAA,CAAIsB,OAAO,uBAAoB;QAC7BgC,UAAU,EAAE;MACd,CAAC;MACD;MACA,IAAAtD,MAAA,CAAIsB,OAAO,iBAAAtB,MAAA,CAAcsB,OAAO,iBAAc;QAC5CoC,eAAe,EAAE3D,KAAK,CAAC4D,IAAI,CAAC5D,KAAK,CAAC4D,IAAI,CAAClC,WAAW,CAAC,CAACmC,GAAG,CAAC7D,KAAK,CAAC8D,sBAAsB,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;MACtG,CAAC;MACD;MACA,IAAA/D,MAAA,CAAIsB,OAAO,iBAAcS,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnC,cAAc,CAACC,SAAS,EAAEC,KAAK,CAAC,CAAC,EAAE;QAC1FS,QAAQ,EAAE,UAAU;QACpB8B,IAAI,EAAE,MAAM;QACZiB,SAAS,EAAE,SAAS;QACpBtC,KAAK,EAAEQ,WAAW;QAClB4B,SAAS,EAAE,QAAQ;QACnBL,MAAM,EAAE,SAAS;QACjBS,UAAU,EAAE,MAAM;QAClBpD,UAAU,SAAAL,MAAA,CAASD,KAAK,CAACO,kBAAkB,CAAE;QAC7C,QAAQ,EAAE;UACR0C,MAAM,EAAE;QACV,CAAC;QACD,UAAU,EAAE;UACVlC,aAAa,EAAE,MAAM;UACrBM,OAAO,EAAE,IAAI;UACbH,KAAK,EAAEQ,WAAW;UAClBf,MAAM,EAAEe,WAAW;UACnBjB,QAAQ,EAAE,UAAU;UACpBwD,IAAI,EAAE;YACJC,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT,CAAC;UACDnD,GAAG,EAAE,CAAC;UACNF,YAAY,EAAEd,KAAK,CAACc,YAAY;UAChCR,UAAU,SAAAL,MAAA,CAASD,KAAK,CAACO,kBAAkB;QAC7C,CAAC;QACD,UAAAN,MAAA,CAAUsB,OAAO,oCAAiC;UAChDX,eAAe,EAAEZ,KAAK,CAACoE;QACzB,CAAC;QACD,YAAAnE,MAAA,CAAYsB,OAAO,0BAAuB;UACxCc,SAAS,EAAE;QACb,CAAC;QACD,gBAAgB,EAAE;UAChBU,KAAK,EAAE/C,KAAK,CAACa;QACf,CAAC;QACD,aAAa,EAAE;UACbJ,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE,CAAC;UACTR,OAAO,EAAE,cAAc;UACvBgB,KAAK,EAAE,MAAM;UACbP,MAAM,EAAE,MAAM;UACd;UACA,UAAU,EAAE;YACVF,QAAQ,EAAE,UAAU;YACpBO,GAAG,EAAE,CAAC;YACNqD,cAAc,EAAErE,KAAK,CAAC4D,IAAI,CAAClC,WAAW,CAAC,CAACqC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACtDM,MAAM,EAAEtE,KAAK,CAAC4D,IAAI,CAACnC,eAAe,CAAC,CAAC8C,GAAG,CAAC,CAAC,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;YACnDQ,iBAAiB,EAAE,CAAC,CAAC;YACrBC,eAAe,eAAAxE,MAAA,CAAeD,KAAK,CAAC0E,WAAW,CAAE;YACjDrD,OAAO,EAAE;UACX,CAAC;UACD,SAAS,EAAE;YACTZ,QAAQ,EAAE,UAAU;YACpBS,KAAK,EAAElB,KAAK,CAAC4D,IAAI,CAAC5D,KAAK,CAAC4D,IAAI,CAAClC,WAAW,CAAC,CAACqC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACO,GAAG,CAAC,GAAG,CAAC,CAACP,KAAK,CAAC,CAAC;YAC1ErD,MAAM,EAAEX,KAAK,CAAC4D,IAAI,CAAClC,WAAW,CAAC,CAACqC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YAC9CW,YAAY,eAAA1E,MAAA,CAAeD,KAAK,CAAC0E,WAAW,CAAE;YAC9CrD,OAAO,EAAE;UACX;QACF;MACF,CAAC,CAAC;MACF;MACA;MACA,IAAApB,MAAA,CAAIsB,OAAO,6BAA0BS,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAC/DxB,QAAQ,EAAE,UAAU;QACpBmE,SAAS,EAAElD,WAAW;QACtBmD,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE9E,KAAK,CAAC+E,SAAS;QAC9B7C,UAAU,EAAE,aAAa;QACzBpB,YAAY,EAAEd,KAAK,CAACc,YAAY;QAChCmC,MAAM,EAAE,SAAS;QACjB3C,UAAU,SAAAL,MAAA,CAASD,KAAK,CAACgF,iBAAiB;MAC5C,CAAC,EAAExE,qBAAqB,CAACT,SAAS,EAAEC,KAAK,CAAC,CAAC,EAAE;QAC3C,SAAS,EAAE;UACTY,eAAe,EAAEiB;QACnB,CAAC;QACD,KAAA5B,MAAA,CAAKsB,OAAO,sBAAmB;UAC7BwB,KAAK,EAAE/C,KAAK,CAACiF,iBAAiB;UAC9BrE,eAAe,EAAEgB;QACnB,CAAC;QACD;QACA,IAAA3B,MAAA,CAAIsB,OAAO,gBAAa;UACtBrB,OAAO,EAAE,cAAc;UACvBgB,KAAK,EAAEQ,WAAW;UAClBf,MAAM,EAAEe,WAAW;UACnB4B,SAAS,EAAE,QAAQ;UACnBlD,aAAa,EAAE,KAAK;UACpB,SAAS,EAAE;YACTF,OAAO,EAAE;UACX;QACF;MACF,CAAC,CAAC;MACF;MACA,IAAAD,MAAA,CAAIsB,OAAO,oBAAAtB,MAAA,CAAiBsB,OAAO,mCAAgC;QACjEX,eAAe,EAAE;MACnB,CAAC;MACD,IAAAX,MAAA,CAAIuB,WAAW,qCAAkC;QAC/C0D,SAAS,eAAAjF,MAAA,CAAeD,KAAK,CAACa,YAAY;MAC5C,CAAC;MACD;MACA,aAAa,EAAE;QACb;QACA,IAAAZ,MAAA,CAAIsB,OAAO,oBAAiB;UAC1Bd,QAAQ,EAAE,UAAU;UACpBE,MAAM,EAAE,MAAM;UACd,UAAU,EAAE;YACVF,QAAQ,EAAE,UAAU;YACpBO,GAAG,EAAE,CAAC;YACNqD,cAAc,EAAErE,KAAK,CAAC4D,IAAI,CAAClC,WAAW,CAAC,CAACqC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACtDM,MAAM,EAAEtE,KAAK,CAAC4D,IAAI,CAACnC,eAAe,CAAC,CAAC8C,GAAG,CAAC,CAAC,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;YACnDS,eAAe,eAAAxE,MAAA,CAAeD,KAAK,CAAC0E,WAAW,CAAE;YACjDrD,OAAO,EAAE;UACX,CAAC;UACD,cAAc,EAAE;YACdnB,OAAO,EAAE;UACX;QACF,CAAC;QACD;QACA,IAAAD,MAAA,CAAIsB,OAAO,iBAAc;UACvBW,UAAU,EAAE,aAAa;UACzB,aAAa,EAAE;YACb;YACA9B,aAAa,EAAE;UACjB;QACF;MACF,CAAC;MACD,IAAAH,MAAA,CAAIuB,WAAW,iBAAAvB,MAAA,CAAcsB,OAAO,kCAA+B;QACjEP,GAAG,EAAE,iBAAiB;QACtBsD,MAAM,EAAE,iBAAiB;QACzB3D,MAAM,KAAAV,MAAA,CAAKd,IAAI,CAACa,KAAK,CAAC4D,IAAI,CAAClC,WAAW,CAAC,CAACqC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;MACzD;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMmB,YAAY,GAAG,SAAAA,CAACpF,SAAS,EAAEC,KAAK,EAMlB;EAAA,IAA3BoF,eAAe,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACpB,MAAM9D,OAAO,OAAAtB,MAAA,CAAOF,SAAS,CAAE;EAC/B,MAAMyB,WAAW,MAAAvB,MAAA,CAAMsB,OAAO,cAAW;EACzC,MAAME,eAAe,GAAGzB,KAAK,CAAC4D,IAAI,CAAC5D,KAAK,CAAC+E,SAAS,CAAC,CAAChB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EAClE,MAAMwB,SAAS,GAAG9F,UAAU,CAACM,KAAK,EAAE;IAClCuB,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,CAAC;EACF,OAAO;EACP;EACAH,YAAY,CAACvB,SAAS,EAAEyF,SAAS,CAAC;EAClC;EACAJ,eAAe,IAAIzF,iBAAiB,CAAC6F,SAAS,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;AAClE,CAAC;AACD,OAAO,MAAMC,kBAAkB,GAAG3F,KAAK,IAAI;EACzC,MAAM;IACJ4F,eAAe;IACfzC,kBAAkB;IAClB0C;EACF,CAAC,GAAG7F,KAAK;EACT,MAAM0B,WAAW,GAAGkE,eAAe;EACnC,OAAO;IACLlE,WAAW;IACXC,UAAU,EAAED,WAAW;IACvBG,WAAW,EAAEsB,kBAAkB;IAC/BD,cAAc,EAAElD,KAAK,CAAC8F,SAAS;IAC/BlE,cAAc,EAAEiE,mBAAmB;IACnCZ,iBAAiB,EAAEjF,KAAK,CAAC8F;EAC3B,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAG/F,KAAK,IAAI;EAC5C,MAAM;IACJgG,mBAAmB;IACnBnF;EACF,CAAC,GAAGb,KAAK;EACT,OAAOgC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0D,kBAAkB,CAAC3F,KAAK,CAAC,CAAC,EAAE;IACjEiG,0BAA0B,EAAED,mBAAmB;IAC/CE,uBAAuB,EAAErF;EAC3B,CAAC,CAAC;AACJ,CAAC;AACD,eAAepB,aAAa,CAAC,MAAM,EAAE,CAACO,KAAK,EAAAmG,IAAA;EAAA,IAAE;IAC3CpG;EACF,CAAC,GAAAoG,IAAA;EAAA,OAAK,CAAC;IACL,CAACnG,KAAK,CAACoG,YAAY,GAAG/G,gBAAgB,IAAAY,MAAA,CAAIF,SAAS,gBAAaC,KAAK;EACvE,CAAC,EAAEmF,YAAY,CAACpF,SAAS,EAAEC,KAAK,CAAC,EAAER,iBAAiB,CAACQ,KAAK,CAAC,CAAC;AAAA,GAAE+F,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
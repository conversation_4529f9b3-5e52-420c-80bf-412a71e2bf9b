{"ast": null, "code": "!function (e, _) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = _(require(\"dayjs\")) : \"function\" == typeof define && define.amd ? define([\"dayjs\"], _) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_locale_zh_cn = _(e.dayjs);\n}(this, function (e) {\n  \"use strict\";\n\n  function _(e) {\n    return e && \"object\" == typeof e && \"default\" in e ? e : {\n      default: e\n    };\n  }\n  var t = _(e),\n    d = {\n      name: \"zh-cn\",\n      weekdays: \"星期日_星期一_星期二_星期三_星期四_星期五_星期六\".split(\"_\"),\n      weekdaysShort: \"周日_周一_周二_周三_周四_周五_周六\".split(\"_\"),\n      weekdaysMin: \"日_一_二_三_四_五_六\".split(\"_\"),\n      months: \"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月\".split(\"_\"),\n      monthsShort: \"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月\".split(\"_\"),\n      ordinal: function (e, _) {\n        return \"W\" === _ ? e + \"周\" : e + \"日\";\n      },\n      weekStart: 1,\n      yearStart: 4,\n      formats: {\n        LT: \"HH:mm\",\n        LTS: \"HH:mm:ss\",\n        L: \"YYYY/MM/DD\",\n        LL: \"YYYY年M月D日\",\n        LLL: \"YYYY年M月D日Ah点mm分\",\n        LLLL: \"YYYY年M月D日ddddAh点mm分\",\n        l: \"YYYY/M/D\",\n        ll: \"YYYY年M月D日\",\n        lll: \"YYYY年M月D日 HH:mm\",\n        llll: \"YYYY年M月D日dddd HH:mm\"\n      },\n      relativeTime: {\n        future: \"%s内\",\n        past: \"%s前\",\n        s: \"几秒\",\n        m: \"1 分钟\",\n        mm: \"%d 分钟\",\n        h: \"1 小时\",\n        hh: \"%d 小时\",\n        d: \"1 天\",\n        dd: \"%d 天\",\n        M: \"1 个月\",\n        MM: \"%d 个月\",\n        y: \"1 年\",\n        yy: \"%d 年\"\n      },\n      meridiem: function (e, _) {\n        var t = 100 * e + _;\n        return t < 600 ? \"凌晨\" : t < 900 ? \"早上\" : t < 1100 ? \"上午\" : t < 1300 ? \"中午\" : t < 1800 ? \"下午\" : \"晚上\";\n      }\n    };\n  return t.default.locale(d, null, !0), d;\n});", "map": {"version": 3, "names": ["e", "_", "exports", "module", "require", "define", "amd", "globalThis", "self", "dayjs_locale_zh_cn", "dayjs", "default", "t", "d", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "ordinal", "weekStart", "yearStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "ll", "lll", "llll", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "meridiem", "locale"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/dayjs/locale/zh-cn.js"], "sourcesContent": ["!function(e,_){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=_(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],_):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_zh_cn=_(e.dayjs)}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),d={name:\"zh-cn\",weekdays:\"星期日_星期一_星期二_星期三_星期四_星期五_星期六\".split(\"_\"),weekdaysShort:\"周日_周一_周二_周三_周四_周五_周六\".split(\"_\"),weekdaysMin:\"日_一_二_三_四_五_六\".split(\"_\"),months:\"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月\".split(\"_\"),monthsShort:\"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月\".split(\"_\"),ordinal:function(e,_){return\"W\"===_?e+\"周\":e+\"日\"},weekStart:1,yearStart:4,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY/MM/DD\",LL:\"YYYY年M月D日\",LLL:\"YYYY年M月D日Ah点mm分\",LLLL:\"YYYY年M月D日ddddAh点mm分\",l:\"YYYY/M/D\",ll:\"YYYY年M月D日\",lll:\"YYYY年M月D日 HH:mm\",llll:\"YYYY年M月D日dddd HH:mm\"},relativeTime:{future:\"%s内\",past:\"%s前\",s:\"几秒\",m:\"1 分钟\",mm:\"%d 分钟\",h:\"1 小时\",hh:\"%d 小时\",d:\"1 天\",dd:\"%d 天\",M:\"1 个月\",MM:\"%d 个月\",y:\"1 年\",yy:\"%d 年\"},meridiem:function(e,_){var t=100*e+_;return t<600?\"凌晨\":t<900?\"早上\":t<1100?\"上午\":t<1300?\"中午\":t<1800?\"下午\":\"晚上\"}};return t.default.locale(d,null,!0),d}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAACG,OAAO,CAAC,OAAO,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOC,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAAC,CAAC,OAAO,CAAC,EAACJ,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOO,UAAU,GAACA,UAAU,GAACP,CAAC,IAAEQ,IAAI,EAAEC,kBAAkB,GAACR,CAAC,CAACD,CAAC,CAACU,KAAK,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,UAASV,CAAC,EAAC;EAAC,YAAY;;EAAC,SAASC,CAACA,CAACD,CAAC,EAAC;IAAC,OAAOA,CAAC,IAAE,QAAQ,IAAE,OAAOA,CAAC,IAAE,SAAS,IAAGA,CAAC,GAACA,CAAC,GAAC;MAACW,OAAO,EAACX;IAAC,CAAC;EAAA;EAAC,IAAIY,CAAC,GAACX,CAAC,CAACD,CAAC,CAAC;IAACa,CAAC,GAAC;MAACC,IAAI,EAAC,OAAO;MAACC,QAAQ,EAAC,6BAA6B,CAACC,KAAK,CAAC,GAAG,CAAC;MAACC,aAAa,EAAC,sBAAsB,CAACD,KAAK,CAAC,GAAG,CAAC;MAACE,WAAW,EAAC,eAAe,CAACF,KAAK,CAAC,GAAG,CAAC;MAACG,MAAM,EAAC,uCAAuC,CAACH,KAAK,CAAC,GAAG,CAAC;MAACI,WAAW,EAAC,wCAAwC,CAACJ,KAAK,CAAC,GAAG,CAAC;MAACK,OAAO,EAAC,SAAAA,CAASrB,CAAC,EAACC,CAAC,EAAC;QAAC,OAAM,GAAG,KAAGA,CAAC,GAACD,CAAC,GAAC,GAAG,GAACA,CAAC,GAAC,GAAG;MAAA,CAAC;MAACsB,SAAS,EAAC,CAAC;MAACC,SAAS,EAAC,CAAC;MAACC,OAAO,EAAC;QAACC,EAAE,EAAC,OAAO;QAACC,GAAG,EAAC,UAAU;QAACC,CAAC,EAAC,YAAY;QAACC,EAAE,EAAC,WAAW;QAACC,GAAG,EAAC,iBAAiB;QAACC,IAAI,EAAC,qBAAqB;QAACC,CAAC,EAAC,UAAU;QAACC,EAAE,EAAC,WAAW;QAACC,GAAG,EAAC,iBAAiB;QAACC,IAAI,EAAC;MAAqB,CAAC;MAACC,YAAY,EAAC;QAACC,MAAM,EAAC,KAAK;QAACC,IAAI,EAAC,KAAK;QAACC,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,MAAM;QAACC,EAAE,EAAC,OAAO;QAACC,CAAC,EAAC,MAAM;QAACC,EAAE,EAAC,OAAO;QAAC7B,CAAC,EAAC,KAAK;QAAC8B,EAAE,EAAC,MAAM;QAACC,CAAC,EAAC,MAAM;QAACC,EAAE,EAAC,OAAO;QAACC,CAAC,EAAC,KAAK;QAACC,EAAE,EAAC;MAAM,CAAC;MAACC,QAAQ,EAAC,SAAAA,CAAShD,CAAC,EAACC,CAAC,EAAC;QAAC,IAAIW,CAAC,GAAC,GAAG,GAACZ,CAAC,GAACC,CAAC;QAAC,OAAOW,CAAC,GAAC,GAAG,GAAC,IAAI,GAACA,CAAC,GAAC,GAAG,GAAC,IAAI,GAACA,CAAC,GAAC,IAAI,GAAC,IAAI,GAACA,CAAC,GAAC,IAAI,GAAC,IAAI,GAACA,CAAC,GAAC,IAAI,GAAC,IAAI,GAAC,IAAI;MAAA;IAAC,CAAC;EAAC,OAAOA,CAAC,CAACD,OAAO,CAACsC,MAAM,CAACpC,CAAC,EAAC,IAAI,EAAC,CAAC,CAAC,CAAC,EAACA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
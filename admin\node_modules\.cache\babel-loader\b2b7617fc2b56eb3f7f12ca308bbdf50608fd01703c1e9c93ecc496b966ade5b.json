{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\UserList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Card, Button, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Typography, Row, Col, Statistic } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, TeamOutlined, CrownOutlined, SearchOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst API_BASE_URL = 'http://localhost:8000/api/v1';\nconst UserList = () => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [form] = Form.useForm();\n  const [searchText, setSearchText] = useState('');\n\n  // 模拟数据\n  const mockUsers = [{\n    id: 1,\n    username: 'admin',\n    email: '<EMAIL>',\n    role: 'admin',\n    status: 'active',\n    created_at: '2024-01-01 10:00:00',\n    last_login: '2024-06-24 15:30:00'\n  }, {\n    id: 2,\n    username: 'manager1',\n    email: '<EMAIL>',\n    role: 'manager',\n    status: 'active',\n    created_at: '2024-02-15 14:20:00',\n    last_login: '2024-06-24 09:15:00'\n  }, {\n    id: 3,\n    username: 'user1',\n    email: '<EMAIL>',\n    role: 'user',\n    status: 'inactive',\n    created_at: '2024-03-10 16:45:00',\n    last_login: '2024-06-20 11:30:00'\n  }];\n  useEffect(() => {\n    loadUsers();\n  }, []);\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setUsers(mockUsers);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载用户列表失败');\n      setLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n  const handleEdit = user => {\n    setEditingUser(user);\n    form.setFieldsValue(user);\n    setIsModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 模拟API调用\n      message.success('删除成功');\n      loadUsers();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingUser) {\n        // 更新用户\n        message.success('更新成功');\n      } else {\n        // 添加用户\n        message.success('添加成功');\n      }\n      setIsModalVisible(false);\n      loadUsers();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n  const getRoleTag = role => {\n    const roleConfig = {\n      admin: {\n        color: 'red',\n        icon: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 36\n        }, this),\n        text: '超级管理员'\n      },\n      manager: {\n        color: 'blue',\n        icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 39\n        }, this),\n        text: '管理员'\n      },\n      user: {\n        color: 'green',\n        icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 37\n        }, this),\n        text: '普通用户'\n      }\n    };\n    const config = roleConfig[role];\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: config.color,\n      icon: config.icon,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this);\n  };\n  const getStatusTag = status => {\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 'active' ? 'success' : 'default',\n      children: status === 'active' ? '正常' : '禁用'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this);\n  };\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username',\n    filteredValue: searchText ? [searchText] : null,\n    onFilter: (value, record) => record.username.toLowerCase().includes(value.toString().toLowerCase()) || record.email.toLowerCase().includes(value.toString().toLowerCase())\n  }, {\n    title: '邮箱',\n    dataIndex: 'email',\n    key: 'email'\n  }, {\n    title: '角色',\n    dataIndex: 'role',\n    key: 'role',\n    render: role => getRoleTag(role),\n    filters: [{\n      text: '超级管理员',\n      value: 'admin'\n    }, {\n      text: '管理员',\n      value: 'manager'\n    }, {\n      text: '普通用户',\n      value: 'user'\n    }],\n    onFilter: (value, record) => record.role === value\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => getStatusTag(status),\n    filters: [{\n      text: '正常',\n      value: 'active'\n    }, {\n      text: '禁用',\n      value: 'inactive'\n    }],\n    onFilter: (value, record) => record.status === value\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()\n  }, {\n    title: '最后登录',\n    dataIndex: 'last_login',\n    key: 'last_login',\n    sorter: (a, b) => new Date(a.last_login).getTime() - new Date(b.last_login).getTime()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u7528\\u6237\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this)\n  }];\n  const stats = {\n    total: users.length,\n    active: users.filter(u => u.status === 'active').length,\n    admin: users.filter(u => u.role === 'admin').length,\n    manager: users.filter(u => u.role === 'manager').length\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      style: {\n        marginBottom: 24\n      },\n      children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u7528\\u6237\\u6570\",\n            value: stats.total,\n            prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D3B\\u8DC3\\u7528\\u6237\",\n            value: stats.active,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7BA1\\u7406\\u5458\",\n            value: stats.admin,\n            prefix: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u666E\\u901A\\u7BA1\\u7406\\u5458\",\n            value: stats.manager,\n            prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u641C\\u7D22\\u7528\\u6237\\u540D\\u6216\\u90AE\\u7BB1\",\n              prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 25\n              }, this),\n              value: searchText,\n              onChange: e => setSearchText(e.target.value),\n              style: {\n                width: 250\n              },\n              allowClear: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: users,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          total: users.length,\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingUser ? '编辑用户' : '添加用户',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        style: {\n          marginTop: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u7528\\u6237\\u540D\",\n          name: \"username\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }, {\n            min: 3,\n            message: '用户名至少3个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u90AE\\u7BB1\",\n          name: \"email\",\n          rules: [{\n            required: true,\n            message: '请输入邮箱'\n          }, {\n            type: 'email',\n            message: '请输入有效的邮箱地址'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), !editingUser && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5BC6\\u7801\",\n          name: \"password\",\n          rules: [{\n            required: true,\n            message: '请输入密码'\n          }, {\n            min: 6,\n            message: '密码至少6个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u89D2\\u8272\",\n          name: \"role\",\n          rules: [{\n            required: true,\n            message: '请选择角色'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u89D2\\u8272\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"admin\",\n              children: \"\\u8D85\\u7EA7\\u7BA1\\u7406\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"manager\",\n              children: \"\\u7BA1\\u7406\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"user\",\n              children: \"\\u666E\\u901A\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u72B6\\u6001\",\n          name: \"status\",\n          rules: [{\n            required: true,\n            message: '请选择状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"active\",\n              children: \"\\u6B63\\u5E38\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"inactive\",\n              children: \"\\u7981\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0,\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingUser ? '更新' : '添加'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n};\n_s(UserList, \"FMPFzPIQelqkgCcemqhe9xlxgUo=\", false, function () {\n  return [Form.useForm];\n});\n_c = UserList;\nexport default UserList;\nvar _c;\n$RefreshReg$(_c, \"UserList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "Card", "<PERSON><PERSON>", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Typography", "Row", "Col", "Statistic", "PlusOutlined", "EditOutlined", "DeleteOutlined", "UserOutlined", "TeamOutlined", "CrownOutlined", "SearchOutlined", "jsxDEV", "_jsxDEV", "Title", "Option", "API_BASE_URL", "UserList", "_s", "users", "setUsers", "loading", "setLoading", "isModalVisible", "setIsModalVisible", "editingUser", "setEditingUser", "form", "useForm", "searchText", "setSearchText", "mockUsers", "id", "username", "email", "role", "status", "created_at", "last_login", "loadUsers", "setTimeout", "error", "handleAdd", "resetFields", "handleEdit", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "success", "handleSubmit", "values", "getRoleTag", "roleConfig", "admin", "color", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "manager", "config", "children", "getStatusTag", "columns", "title", "dataIndex", "key", "width", "filteredValue", "onFilter", "value", "record", "toLowerCase", "includes", "toString", "render", "filters", "sorter", "a", "b", "Date", "getTime", "_", "size", "type", "onClick", "onConfirm", "okText", "cancelText", "danger", "stats", "total", "length", "active", "filter", "u", "level", "style", "marginBottom", "gutter", "span", "prefix", "valueStyle", "justify", "align", "placeholder", "onChange", "e", "target", "allowClear", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "footer", "layout", "onFinish", "marginTop", "<PERSON><PERSON>", "label", "name", "rules", "required", "min", "Password", "textAlign", "htmlType", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/UserList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Card,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport axios from 'axios';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  UserOutlined,\n  TeamOutlined,\n  CrownOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title } = Typography;\nconst { Option } = Select;\n\nconst API_BASE_URL = 'http://localhost:8000/api/v1';\n\ninterface User {\n  id: number;\n  name: string;\n  username: string;\n  email: string;\n  role: 'admin' | 'manager' | 'user';\n  status: 'active' | 'inactive';\n  remark?: string;\n  created_at: string;\n  updated_at: string;\n  last_login_at?: string;\n}\n\nconst UserList: React.FC = () => {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [form] = Form.useForm();\n  const [searchText, setSearchText] = useState('');\n\n  // 模拟数据\n  const mockUsers: User[] = [\n    {\n      id: 1,\n      username: 'admin',\n      email: '<EMAIL>',\n      role: 'admin',\n      status: 'active',\n      created_at: '2024-01-01 10:00:00',\n      last_login: '2024-06-24 15:30:00',\n    },\n    {\n      id: 2,\n      username: 'manager1',\n      email: '<EMAIL>',\n      role: 'manager',\n      status: 'active',\n      created_at: '2024-02-15 14:20:00',\n      last_login: '2024-06-24 09:15:00',\n    },\n    {\n      id: 3,\n      username: 'user1',\n      email: '<EMAIL>',\n      role: 'user',\n      status: 'inactive',\n      created_at: '2024-03-10 16:45:00',\n      last_login: '2024-06-20 11:30:00',\n    },\n  ];\n\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setUsers(mockUsers);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载用户列表失败');\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEdit = (user: User) => {\n    setEditingUser(user);\n    form.setFieldsValue(user);\n    setIsModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      // 模拟API调用\n      message.success('删除成功');\n      loadUsers();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      if (editingUser) {\n        // 更新用户\n        message.success('更新成功');\n      } else {\n        // 添加用户\n        message.success('添加成功');\n      }\n      setIsModalVisible(false);\n      loadUsers();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const getRoleTag = (role: string) => {\n    const roleConfig = {\n      admin: { color: 'red', icon: <CrownOutlined />, text: '超级管理员' },\n      manager: { color: 'blue', icon: <TeamOutlined />, text: '管理员' },\n      user: { color: 'green', icon: <UserOutlined />, text: '普通用户' },\n    };\n    const config = roleConfig[role as keyof typeof roleConfig];\n    return (\n      <Tag color={config.color} icon={config.icon}>\n        {config.text}\n      </Tag>\n    );\n  };\n\n  const getStatusTag = (status: string) => {\n    return (\n      <Tag color={status === 'active' ? 'success' : 'default'}>\n        {status === 'active' ? '正常' : '禁用'}\n      </Tag>\n    );\n  };\n\n  const columns: ColumnsType<User> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      filteredValue: searchText ? [searchText] : null,\n      onFilter: (value, record) =>\n        record.username.toLowerCase().includes(value.toString().toLowerCase()) ||\n        record.email.toLowerCase().includes(value.toString().toLowerCase()),\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: '角色',\n      dataIndex: 'role',\n      key: 'role',\n      render: (role: string) => getRoleTag(role),\n      filters: [\n        { text: '超级管理员', value: 'admin' },\n        { text: '管理员', value: 'manager' },\n        { text: '普通用户', value: 'user' },\n      ],\n      onFilter: (value, record) => record.role === value,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => getStatusTag(status),\n      filters: [\n        { text: '正常', value: 'active' },\n        { text: '禁用', value: 'inactive' },\n      ],\n      onFilter: (value, record) => record.status === value,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),\n    },\n    {\n      title: '最后登录',\n      dataIndex: 'last_login',\n      key: 'last_login',\n      sorter: (a, b) => new Date(a.last_login).getTime() - new Date(b.last_login).getTime(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个用户吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  const stats = {\n    total: users.length,\n    active: users.filter(u => u.status === 'active').length,\n    admin: users.filter(u => u.role === 'admin').length,\n    manager: users.filter(u => u.role === 'manager').length,\n  };\n\n  return (\n    <div>\n      <Title level={2} style={{ marginBottom: 24 }}>\n        用户管理\n      </Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总用户数\"\n              value={stats.total}\n              prefix={<TeamOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"活跃用户\"\n              value={stats.active}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"管理员\"\n              value={stats.admin}\n              prefix={<CrownOutlined />}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"普通管理员\"\n              value={stats.manager}\n              prefix={<TeamOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Input\n                placeholder=\"搜索用户名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 250 }}\n                allowClear\n              />\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              添加用户\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 用户表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={users}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            total: users.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 添加/编辑用户模态框 */}\n      <Modal\n        title={editingUser ? '编辑用户' : '添加用户'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          style={{ marginTop: 16 }}\n        >\n          <Form.Item\n            label=\"用户名\"\n            name=\"username\"\n            rules={[\n              { required: true, message: '请输入用户名' },\n              { min: 3, message: '用户名至少3个字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入用户名\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"邮箱\"\n            name=\"email\"\n            rules={[\n              { required: true, message: '请输入邮箱' },\n              { type: 'email', message: '请输入有效的邮箱地址' },\n            ]}\n          >\n            <Input placeholder=\"请输入邮箱\" />\n          </Form.Item>\n\n          {!editingUser && (\n            <Form.Item\n              label=\"密码\"\n              name=\"password\"\n              rules={[\n                { required: true, message: '请输入密码' },\n                { min: 6, message: '密码至少6个字符' },\n              ]}\n            >\n              <Input.Password placeholder=\"请输入密码\" />\n            </Form.Item>\n          )}\n\n          <Form.Item\n            label=\"角色\"\n            name=\"role\"\n            rules={[{ required: true, message: '请选择角色' }]}\n          >\n            <Select placeholder=\"请选择角色\">\n              <Option value=\"admin\">超级管理员</Option>\n              <Option value=\"manager\">管理员</Option>\n              <Option value=\"user\">普通用户</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            label=\"状态\"\n            name=\"status\"\n            rules={[{ required: true, message: '请选择状态' }]}\n          >\n            <Select placeholder=\"请选择状态\">\n              <Option value=\"active\">正常</Option>\n              <Option value=\"inactive\">禁用</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setIsModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingUser ? '更新' : '添加'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default UserList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AAEb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,cAAc,QACT,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC;AAAM,CAAC,GAAGb,UAAU;AAC5B,MAAM;EAAEc;AAAO,CAAC,GAAGjB,MAAM;AAEzB,MAAMkB,YAAY,GAAG,8BAA8B;AAenD,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACuC,IAAI,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM2C,SAAiB,GAAG,CACxB;IACEC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,qBAAqB;IACjCC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,qBAAqB;IACjCC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,UAAU;IAClBC,UAAU,EAAE,qBAAqB;IACjCC,UAAU,EAAE;EACd,CAAC,CACF;EAEDjD,SAAS,CAAC,MAAM;IACdkD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAkB,UAAU,CAAC,MAAM;QACfpB,QAAQ,CAACW,SAAS,CAAC;QACnBT,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,UAAU,CAAC;MACzBnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,SAAS,GAAGA,CAAA,KAAM;IACtBhB,cAAc,CAAC,IAAI,CAAC;IACpBC,IAAI,CAACgB,WAAW,CAAC,CAAC;IAClBnB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoB,UAAU,GAAIC,IAAU,IAAK;IACjCnB,cAAc,CAACmB,IAAI,CAAC;IACpBlB,IAAI,CAACmB,cAAc,CAACD,IAAI,CAAC;IACzBrB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMuB,YAAY,GAAG,MAAOf,EAAU,IAAK;IACzC,IAAI;MACF;MACAjC,OAAO,CAACiD,OAAO,CAAC,MAAM,CAAC;MACvBT,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,IAAIzB,WAAW,EAAE;QACf;QACA1B,OAAO,CAACiD,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACAjD,OAAO,CAACiD,OAAO,CAAC,MAAM,CAAC;MACzB;MACAxB,iBAAiB,CAAC,KAAK,CAAC;MACxBe,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,UAAU,GAAIhB,IAAY,IAAK;IACnC,MAAMiB,UAAU,GAAG;MACjBC,KAAK,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,IAAI,eAAE1C,OAAA,CAACH,aAAa;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAC/DC,OAAO,EAAE;QAAEP,KAAK,EAAE,MAAM;QAAEC,IAAI,eAAE1C,OAAA,CAACJ,YAAY;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/Df,IAAI,EAAE;QAAES,KAAK,EAAE,OAAO;QAAEC,IAAI,eAAE1C,OAAA,CAACL,YAAY;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAAEC,IAAI,EAAE;MAAO;IAC/D,CAAC;IACD,MAAME,MAAM,GAAGV,UAAU,CAACjB,IAAI,CAA4B;IAC1D,oBACEtB,OAAA,CAACnB,GAAG;MAAC4D,KAAK,EAAEQ,MAAM,CAACR,KAAM;MAACC,IAAI,EAAEO,MAAM,CAACP,IAAK;MAAAQ,QAAA,EACzCD,MAAM,CAACF;IAAI;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEV,CAAC;EAED,MAAMK,YAAY,GAAI5B,MAAc,IAAK;IACvC,oBACEvB,OAAA,CAACnB,GAAG;MAAC4D,KAAK,EAAElB,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;MAAA2B,QAAA,EACrD3B,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAI;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAEV,CAAC;EAED,MAAMM,OAA0B,GAAG,CACjC;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfE,aAAa,EAAEzC,UAAU,GAAG,CAACA,UAAU,CAAC,GAAG,IAAI;IAC/C0C,QAAQ,EAAEA,CAACC,KAAK,EAAEC,MAAM,KACtBA,MAAM,CAACxC,QAAQ,CAACyC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC,CAAC,IACtED,MAAM,CAACvC,KAAK,CAACwC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC;EACtE,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXS,MAAM,EAAG1C,IAAY,IAAKgB,UAAU,CAAChB,IAAI,CAAC;IAC1C2C,OAAO,EAAE,CACP;MAAElB,IAAI,EAAE,OAAO;MAAEY,KAAK,EAAE;IAAQ,CAAC,EACjC;MAAEZ,IAAI,EAAE,KAAK;MAAEY,KAAK,EAAE;IAAU,CAAC,EACjC;MAAEZ,IAAI,EAAE,MAAM;MAAEY,KAAK,EAAE;IAAO,CAAC,CAChC;IACDD,QAAQ,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACtC,IAAI,KAAKqC;EAC/C,CAAC,EACD;IACEN,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbS,MAAM,EAAGzC,MAAc,IAAK4B,YAAY,CAAC5B,MAAM,CAAC;IAChD0C,OAAO,EAAE,CACP;MAAElB,IAAI,EAAE,IAAI;MAAEY,KAAK,EAAE;IAAS,CAAC,EAC/B;MAAEZ,IAAI,EAAE,IAAI;MAAEY,KAAK,EAAE;IAAW,CAAC,CAClC;IACDD,QAAQ,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACrC,MAAM,KAAKoC;EACjD,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBW,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACF,CAAC,CAAC3C,UAAU,CAAC,CAAC8C,OAAO,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAAC5C,UAAU,CAAC,CAAC8C,OAAO,CAAC;EACtF,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBW,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACF,CAAC,CAAC1C,UAAU,CAAC,CAAC6C,OAAO,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAAC3C,UAAU,CAAC,CAAC6C,OAAO,CAAC;EACtF,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVQ,MAAM,EAAEA,CAACO,CAAC,EAAEX,MAAM,kBAChB5D,OAAA,CAACpB,KAAK;MAAC4F,IAAI,EAAC,OAAO;MAAAtB,QAAA,gBACjBlD,OAAA,CAACrB,MAAM;QACL8F,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZ9B,IAAI,eAAE1C,OAAA,CAACP,YAAY;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvB4B,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAAC6B,MAAM,CAAE;QAAAV,QAAA,EACnC;MAED;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9C,OAAA,CAACb,UAAU;QACTkE,KAAK,EAAC,oEAAa;QACnBsB,SAAS,EAAEA,CAAA,KAAMzC,YAAY,CAAC0B,MAAM,CAACzC,EAAE,CAAE;QACzCyD,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAA3B,QAAA,eAEflD,OAAA,CAACrB,MAAM;UACL8F,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZM,MAAM;UACNpC,IAAI,eAAE1C,OAAA,CAACN,cAAc;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAI,QAAA,EAC1B;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMiC,KAAK,GAAG;IACZC,KAAK,EAAE1E,KAAK,CAAC2E,MAAM;IACnBC,MAAM,EAAE5E,KAAK,CAAC6E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7D,MAAM,KAAK,QAAQ,CAAC,CAAC0D,MAAM;IACvDzC,KAAK,EAAElC,KAAK,CAAC6E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9D,IAAI,KAAK,OAAO,CAAC,CAAC2D,MAAM;IACnDjC,OAAO,EAAE1C,KAAK,CAAC6E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9D,IAAI,KAAK,SAAS,CAAC,CAAC2D;EACnD,CAAC;EAED,oBACEjF,OAAA;IAAAkD,QAAA,gBACElD,OAAA,CAACC,KAAK;MAACoF,KAAK,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAArC,QAAA,EAAC;IAE9C;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAGR9C,OAAA,CAACX,GAAG;MAACmG,MAAM,EAAE,EAAG;MAACF,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAArC,QAAA,gBAC3ClD,OAAA,CAACV,GAAG;QAACmG,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACXlD,OAAA,CAACtB,IAAI;UAAAwE,QAAA,eACHlD,OAAA,CAACT,SAAS;YACR8D,KAAK,EAAC,0BAAM;YACZM,KAAK,EAAEoB,KAAK,CAACC,KAAM;YACnBU,MAAM,eAAE1F,OAAA,CAACJ,YAAY;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB6C,UAAU,EAAE;cAAElD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9C,OAAA,CAACV,GAAG;QAACmG,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACXlD,OAAA,CAACtB,IAAI;UAAAwE,QAAA,eACHlD,OAAA,CAACT,SAAS;YACR8D,KAAK,EAAC,0BAAM;YACZM,KAAK,EAAEoB,KAAK,CAACG,MAAO;YACpBQ,MAAM,eAAE1F,OAAA,CAACL,YAAY;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB6C,UAAU,EAAE;cAAElD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9C,OAAA,CAACV,GAAG;QAACmG,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACXlD,OAAA,CAACtB,IAAI;UAAAwE,QAAA,eACHlD,OAAA,CAACT,SAAS;YACR8D,KAAK,EAAC,oBAAK;YACXM,KAAK,EAAEoB,KAAK,CAACvC,KAAM;YACnBkD,MAAM,eAAE1F,OAAA,CAACH,aAAa;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1B6C,UAAU,EAAE;cAAElD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9C,OAAA,CAACV,GAAG;QAACmG,IAAI,EAAE,CAAE;QAAAvC,QAAA,eACXlD,OAAA,CAACtB,IAAI;UAAAwE,QAAA,eACHlD,OAAA,CAACT,SAAS;YACR8D,KAAK,EAAC,gCAAO;YACbM,KAAK,EAAEoB,KAAK,CAAC/B,OAAQ;YACrB0C,MAAM,eAAE1F,OAAA,CAACJ,YAAY;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB6C,UAAU,EAAE;cAAElD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA,CAACtB,IAAI;MAAC4G,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAArC,QAAA,eAChClD,OAAA,CAACX,GAAG;QAACuG,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAA3C,QAAA,gBACzClD,OAAA,CAACV,GAAG;UAAA4D,QAAA,eACFlD,OAAA,CAACpB,KAAK;YAAAsE,QAAA,eACJlD,OAAA,CAAChB,KAAK;cACJ8G,WAAW,EAAC,kDAAU;cACtBJ,MAAM,eAAE1F,OAAA,CAACF,cAAc;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3Ba,KAAK,EAAE3C,UAAW;cAClB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAE;cAC/C2B,KAAK,EAAE;gBAAE9B,KAAK,EAAE;cAAI,CAAE;cACtB0C,UAAU;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9C,OAAA,CAACV,GAAG;UAAA4D,QAAA,eACFlD,OAAA,CAACrB,MAAM;YACL8F,IAAI,EAAC,SAAS;YACd/B,IAAI,eAAE1C,OAAA,CAACR,YAAY;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB4B,OAAO,EAAE7C,SAAU;YAAAqB,QAAA,EACpB;UAED;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP9C,OAAA,CAACtB,IAAI;MAAAwE,QAAA,eACHlD,OAAA,CAACvB,KAAK;QACJ2E,OAAO,EAAEA,OAAQ;QACjB+C,UAAU,EAAE7F,KAAM;QAClB8F,MAAM,EAAC,IAAI;QACX5F,OAAO,EAAEA,OAAQ;QACjB6F,UAAU,EAAE;UACVrB,KAAK,EAAE1E,KAAK,CAAC2E,MAAM;UACnBqB,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACzB,KAAK,EAAE0B,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ1B,KAAK;QAC1C;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP9C,OAAA,CAAClB,KAAK;MACJuE,KAAK,EAAEzC,WAAW,GAAG,MAAM,GAAG,MAAO;MACrC+F,IAAI,EAAEjG,cAAe;MACrBkG,QAAQ,EAAEA,CAAA,KAAMjG,iBAAiB,CAAC,KAAK,CAAE;MACzCkG,MAAM,EAAE,IAAK;MACbrD,KAAK,EAAE,GAAI;MAAAN,QAAA,eAEXlD,OAAA,CAACjB,IAAI;QACH+B,IAAI,EAAEA,IAAK;QACXgG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE3E,YAAa;QACvBkD,KAAK,EAAE;UAAE0B,SAAS,EAAE;QAAG,CAAE;QAAA9D,QAAA,gBAEzBlD,OAAA,CAACjB,IAAI,CAACkI,IAAI;UACRC,KAAK,EAAC,oBAAK;UACXC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEnI,OAAO,EAAE;UAAS,CAAC,EACrC;YAAEoI,GAAG,EAAE,CAAC;YAAEpI,OAAO,EAAE;UAAY,CAAC,CAChC;UAAAgE,QAAA,eAEFlD,OAAA,CAAChB,KAAK;YAAC8G,WAAW,EAAC;UAAQ;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZ9C,OAAA,CAACjB,IAAI,CAACkI,IAAI;UACRC,KAAK,EAAC,cAAI;UACVC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEnI,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAEuF,IAAI,EAAE,OAAO;YAAEvF,OAAO,EAAE;UAAa,CAAC,CACxC;UAAAgE,QAAA,eAEFlD,OAAA,CAAChB,KAAK;YAAC8G,WAAW,EAAC;UAAO;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,EAEX,CAAClC,WAAW,iBACXZ,OAAA,CAACjB,IAAI,CAACkI,IAAI;UACRC,KAAK,EAAC,cAAI;UACVC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEnI,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAEoI,GAAG,EAAE,CAAC;YAAEpI,OAAO,EAAE;UAAW,CAAC,CAC/B;UAAAgE,QAAA,eAEFlD,OAAA,CAAChB,KAAK,CAACuI,QAAQ;YAACzB,WAAW,EAAC;UAAO;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACZ,eAED9C,OAAA,CAACjB,IAAI,CAACkI,IAAI;UACRC,KAAK,EAAC,cAAI;UACVC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnI,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAgE,QAAA,eAE9ClD,OAAA,CAACf,MAAM;YAAC6G,WAAW,EAAC,gCAAO;YAAA5C,QAAA,gBACzBlD,OAAA,CAACE,MAAM;cAACyD,KAAK,EAAC,OAAO;cAAAT,QAAA,EAAC;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC9C,OAAA,CAACE,MAAM;cAACyD,KAAK,EAAC,SAAS;cAAAT,QAAA,EAAC;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC9C,OAAA,CAACE,MAAM;cAACyD,KAAK,EAAC,MAAM;cAAAT,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ9C,OAAA,CAACjB,IAAI,CAACkI,IAAI;UACRC,KAAK,EAAC,cAAI;UACVC,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnI,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAgE,QAAA,eAE9ClD,OAAA,CAACf,MAAM;YAAC6G,WAAW,EAAC,gCAAO;YAAA5C,QAAA,gBACzBlD,OAAA,CAACE,MAAM;cAACyD,KAAK,EAAC,QAAQ;cAAAT,QAAA,EAAC;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC9C,OAAA,CAACE,MAAM;cAACyD,KAAK,EAAC,UAAU;cAAAT,QAAA,EAAC;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ9C,OAAA,CAACjB,IAAI,CAACkI,IAAI;UAAC3B,KAAK,EAAE;YAAEC,YAAY,EAAE,CAAC;YAAEiC,SAAS,EAAE;UAAQ,CAAE;UAAAtE,QAAA,eACxDlD,OAAA,CAACpB,KAAK;YAAAsE,QAAA,gBACJlD,OAAA,CAACrB,MAAM;cAAC+F,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAAC,KAAK,CAAE;cAAAuC,QAAA,EAAC;YAEjD;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9C,OAAA,CAACrB,MAAM;cAAC8F,IAAI,EAAC,SAAS;cAACgD,QAAQ,EAAC,QAAQ;cAAAvE,QAAA,EACrCtC,WAAW,GAAG,IAAI,GAAG;YAAI;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzC,EAAA,CA3YID,QAAkB;EAAA,QAKPrB,IAAI,CAACgC,OAAO;AAAA;AAAA2G,EAAA,GALvBtH,QAAkB;AA6YxB,eAAeA,QAAQ;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
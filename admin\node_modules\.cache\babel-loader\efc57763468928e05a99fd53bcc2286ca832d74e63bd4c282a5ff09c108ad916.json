{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\UserCreate.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Card, Form, Input, Select, Button, Space, Typography, Row, Col, message, Divider } from 'antd';\nimport { UserOutlined, MailOutlined, LockOutlined, TeamOutlined, SaveOutlined, ArrowLeftOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst UserCreate = () => {\n  _s();\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const [loading, setLoading] = React.useState(false);\n  const handleSubmit = async values => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      console.log('创建用户:', values);\n\n      // 模拟网络延迟\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      message.success('用户创建成功');\n      navigate('/users/list');\n    } catch (error) {\n      message.error('创建失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReset = () => {\n    form.resetFields();\n  };\n  const handleBack = () => {\n    navigate('/users/list');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 17\n        }, this),\n        onClick: handleBack,\n        children: \"\\u8FD4\\u56DE\\u7528\\u6237\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 24\n          },\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            onFinish: handleSubmit,\n            initialValues: {\n              role: 'user',\n              status: 'active'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u7528\\u6237\\u540D\",\n                  name: \"username\",\n                  rules: [{\n                    required: true,\n                    message: '请输入用户名'\n                  }, {\n                    min: 3,\n                    max: 20,\n                    message: '用户名长度为3-20个字符'\n                  }, {\n                    pattern: /^[a-zA-Z0-9_]+$/,\n                    message: '用户名只能包含字母、数字和下划线'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 97,\n                      columnNumber: 31\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n                    size: \"large\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u90AE\\u7BB1\\u5730\\u5740\",\n                  name: \"email\",\n                  rules: [{\n                    required: true,\n                    message: '请输入邮箱地址'\n                  }, {\n                    type: 'email',\n                    message: '请输入有效的邮箱地址'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 113,\n                      columnNumber: 31\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\",\n                    size: \"large\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u5BC6\\u7801\",\n                  name: \"password\",\n                  rules: [{\n                    required: true,\n                    message: '请输入密码'\n                  }, {\n                    min: 6,\n                    message: '密码至少6个字符'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                    prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 31\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n                    size: \"large\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u786E\\u8BA4\\u5BC6\\u7801\",\n                  name: \"confirmPassword\",\n                  dependencies: ['password'],\n                  rules: [{\n                    required: true,\n                    message: '请确认密码'\n                  }, ({\n                    getFieldValue\n                  }) => ({\n                    validator(_, value) {\n                      if (!value || getFieldValue('password') === value) {\n                        return Promise.resolve();\n                      }\n                      return Promise.reject(new Error('两次输入的密码不一致'));\n                    }\n                  })],\n                  children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                    prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 31\n                    }, this),\n                    placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u5BC6\\u7801\",\n                    size: \"large\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u7528\\u6237\\u89D2\\u8272\",\n                  name: \"role\",\n                  rules: [{\n                    required: true,\n                    message: '请选择用户角色'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u7528\\u6237\\u89D2\\u8272\",\n                    size: \"large\",\n                    suffixIcon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 35\n                    }, this),\n                    children: [/*#__PURE__*/_jsxDEV(Option, {\n                      value: \"admin\",\n                      children: /*#__PURE__*/_jsxDEV(Space, {\n                        children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {\n                          style: {\n                            color: '#f5222d'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 180,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"\\u8D85\\u7EA7\\u7BA1\\u7406\\u5458\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 181,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 179,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"manager\",\n                      children: /*#__PURE__*/_jsxDEV(Space, {\n                        children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {\n                          style: {\n                            color: '#1890ff'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 186,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"\\u7BA1\\u7406\\u5458\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 187,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 185,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"user\",\n                      children: /*#__PURE__*/_jsxDEV(Space, {\n                        children: [/*#__PURE__*/_jsxDEV(UserOutlined, {\n                          style: {\n                            color: '#52c41a'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 192,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"\\u666E\\u901A\\u7528\\u6237\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 193,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u8D26\\u6237\\u72B6\\u6001\",\n                  name: \"status\",\n                  rules: [{\n                    required: true,\n                    message: '请选择账户状态'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u8D26\\u6237\\u72B6\\u6001\",\n                    size: \"large\",\n                    children: [/*#__PURE__*/_jsxDEV(Option, {\n                      value: \"active\",\n                      children: \"\\u6B63\\u5E38\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"inactive\",\n                      children: \"\\u7981\\u7528\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5907\\u6CE8\",\n              name: \"remark\",\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\\u4FE1\\u606F\\uFF08\\u53EF\\u9009\\uFF09\",\n                rows: 3,\n                maxLength: 200,\n                showCount: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              style: {\n                marginTop: 32\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                size: \"large\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  loading: loading,\n                  icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 27\n                  }, this),\n                  size: \"large\",\n                  children: \"\\u521B\\u5EFA\\u7528\\u6237\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  size: \"large\",\n                  children: \"\\u91CD\\u7F6E\\u8868\\u5355\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleBack,\n                  size: \"large\",\n                  children: \"\\u53D6\\u6D88\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u89D2\\u8272\\u8BF4\\u660E\",\n          style: {\n            marginBottom: 24\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"middle\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                style: {\n                  color: '#f5222d'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), \" \\u8D85\\u7EA7\\u7BA1\\u7406\\u5458\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: \"\\u62E5\\u6709\\u7CFB\\u7EDF\\u6240\\u6709\\u6743\\u9650\\uFF0C\\u53EF\\u4EE5\\u7BA1\\u7406\\u6240\\u6709\\u529F\\u80FD\\u6A21\\u5757\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                style: {\n                  color: '#1890ff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), \" \\u7BA1\\u7406\\u5458\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: \"\\u53EF\\u4EE5\\u7BA1\\u7406\\u4EA7\\u54C1\\u3001\\u8BA2\\u5355\\u7B49\\u4E1A\\u52A1\\u529F\\u80FD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                style: {\n                  color: '#52c41a'\n                },\n                children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), \" \\u666E\\u901A\\u7528\\u6237\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: \"\\u53EA\\u80FD\\u67E5\\u770B\\u57FA\\u672C\\u4FE1\\u606F\\uFF0C\\u6743\\u9650\\u53D7\\u9650\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5BC6\\u7801\\u8981\\u6C42\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"small\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '12px'\n              },\n              children: \"\\u2022 \\u5BC6\\u7801\\u957F\\u5EA6\\u81F3\\u5C116\\u4E2A\\u5B57\\u7B26\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '12px'\n              },\n              children: \"\\u2022 \\u5EFA\\u8BAE\\u5305\\u542B\\u5B57\\u6BCD\\u3001\\u6570\\u5B57\\u548C\\u7279\\u6B8A\\u5B57\\u7B26\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '12px'\n              },\n              children: \"\\u2022 \\u907F\\u514D\\u4F7F\\u7528\\u5E38\\u89C1\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                fontSize: '12px'\n              },\n              children: \"\\u2022 \\u5B9A\\u671F\\u66F4\\u6362\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(UserCreate, \"xhqR2qiU69Jw2kMyLWQ12w6nH2U=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = UserCreate;\nexport default UserCreate;\nvar _c;\n$RefreshReg$(_c, \"UserCreate\");", "map": {"version": 3, "names": ["React", "Card", "Form", "Input", "Select", "<PERSON><PERSON>", "Space", "Typography", "Row", "Col", "message", "Divider", "UserOutlined", "MailOutlined", "LockOutlined", "TeamOutlined", "SaveOutlined", "ArrowLeftOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "UserCreate", "_s", "form", "useForm", "navigate", "loading", "setLoading", "useState", "handleSubmit", "values", "console", "log", "Promise", "resolve", "setTimeout", "success", "error", "handleReset", "resetFields", "handleBack", "children", "style", "marginBottom", "display", "alignItems", "justifyContent", "level", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "onClick", "gutter", "span", "title", "layout", "onFinish", "initialValues", "role", "status", "<PERSON><PERSON>", "label", "name", "rules", "required", "min", "max", "pattern", "prefix", "placeholder", "size", "type", "Password", "dependencies", "getFieldValue", "validator", "_", "value", "reject", "Error", "suffixIcon", "color", "TextArea", "rows", "max<PERSON><PERSON><PERSON>", "showCount", "marginTop", "htmlType", "direction", "width", "strong", "fontSize", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/UserCreate.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Card,\n  Form,\n  Input,\n  Select,\n  Button,\n  Space,\n  Typography,\n  Row,\n  Col,\n  message,\n  Divider,\n} from 'antd';\nimport {\n  UserOutlined,\n  MailOutlined,\n  LockOutlined,\n  TeamOutlined,\n  SaveOutlined,\n  ArrowLeftOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nconst UserCreate: React.FC = () => {\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const [loading, setLoading] = React.useState(false);\n\n  const handleSubmit = async (values: any) => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      console.log('创建用户:', values);\n      \n      // 模拟网络延迟\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      message.success('用户创建成功');\n      navigate('/users/list');\n    } catch (error) {\n      message.error('创建失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    form.resetFields();\n  };\n\n  const handleBack = () => {\n    navigate('/users/list');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: 24, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Title level={2} style={{ margin: 0 }}>\n          添加用户\n        </Title>\n        <Button\n          icon={<ArrowLeftOutlined />}\n          onClick={handleBack}\n        >\n          返回用户列表\n        </Button>\n      </div>\n\n      <Row gutter={24}>\n        <Col span={16}>\n          <Card title=\"基本信息\" style={{ marginBottom: 24 }}>\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={handleSubmit}\n              initialValues={{\n                role: 'user',\n                status: 'active',\n              }}\n            >\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"用户名\"\n                    name=\"username\"\n                    rules={[\n                      { required: true, message: '请输入用户名' },\n                      { min: 3, max: 20, message: '用户名长度为3-20个字符' },\n                      { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },\n                    ]}\n                  >\n                    <Input\n                      prefix={<UserOutlined />}\n                      placeholder=\"请输入用户名\"\n                      size=\"large\"\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"邮箱地址\"\n                    name=\"email\"\n                    rules={[\n                      { required: true, message: '请输入邮箱地址' },\n                      { type: 'email', message: '请输入有效的邮箱地址' },\n                    ]}\n                  >\n                    <Input\n                      prefix={<MailOutlined />}\n                      placeholder=\"请输入邮箱地址\"\n                      size=\"large\"\n                    />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"密码\"\n                    name=\"password\"\n                    rules={[\n                      { required: true, message: '请输入密码' },\n                      { min: 6, message: '密码至少6个字符' },\n                    ]}\n                  >\n                    <Input.Password\n                      prefix={<LockOutlined />}\n                      placeholder=\"请输入密码\"\n                      size=\"large\"\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"确认密码\"\n                    name=\"confirmPassword\"\n                    dependencies={['password']}\n                    rules={[\n                      { required: true, message: '请确认密码' },\n                      ({ getFieldValue }) => ({\n                        validator(_, value) {\n                          if (!value || getFieldValue('password') === value) {\n                            return Promise.resolve();\n                          }\n                          return Promise.reject(new Error('两次输入的密码不一致'));\n                        },\n                      }),\n                    ]}\n                  >\n                    <Input.Password\n                      prefix={<LockOutlined />}\n                      placeholder=\"请再次输入密码\"\n                      size=\"large\"\n                    />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Divider />\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"用户角色\"\n                    name=\"role\"\n                    rules={[{ required: true, message: '请选择用户角色' }]}\n                  >\n                    <Select\n                      placeholder=\"请选择用户角色\"\n                      size=\"large\"\n                      suffixIcon={<TeamOutlined />}\n                    >\n                      <Option value=\"admin\">\n                        <Space>\n                          <TeamOutlined style={{ color: '#f5222d' }} />\n                          <span>超级管理员</span>\n                        </Space>\n                      </Option>\n                      <Option value=\"manager\">\n                        <Space>\n                          <TeamOutlined style={{ color: '#1890ff' }} />\n                          <span>管理员</span>\n                        </Space>\n                      </Option>\n                      <Option value=\"user\">\n                        <Space>\n                          <UserOutlined style={{ color: '#52c41a' }} />\n                          <span>普通用户</span>\n                        </Space>\n                      </Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"账户状态\"\n                    name=\"status\"\n                    rules={[{ required: true, message: '请选择账户状态' }]}\n                  >\n                    <Select placeholder=\"请选择账户状态\" size=\"large\">\n                      <Option value=\"active\">正常</Option>\n                      <Option value=\"inactive\">禁用</Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item\n                label=\"备注\"\n                name=\"remark\"\n              >\n                <Input.TextArea\n                  placeholder=\"请输入备注信息（可选）\"\n                  rows={3}\n                  maxLength={200}\n                  showCount\n                />\n              </Form.Item>\n\n              <Form.Item style={{ marginTop: 32 }}>\n                <Space size=\"large\">\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    loading={loading}\n                    icon={<SaveOutlined />}\n                    size=\"large\"\n                  >\n                    创建用户\n                  </Button>\n                  <Button\n                    onClick={handleReset}\n                    size=\"large\"\n                  >\n                    重置表单\n                  </Button>\n                  <Button\n                    onClick={handleBack}\n                    size=\"large\"\n                  >\n                    取消\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        <Col span={8}>\n          <Card title=\"角色说明\" style={{ marginBottom: 24 }}>\n            <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n              <div>\n                <Text strong style={{ color: '#f5222d' }}>\n                  <TeamOutlined /> 超级管理员\n                </Text>\n                <br />\n                <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                  拥有系统所有权限，可以管理所有功能模块\n                </Text>\n              </div>\n              \n              <div>\n                <Text strong style={{ color: '#1890ff' }}>\n                  <TeamOutlined /> 管理员\n                </Text>\n                <br />\n                <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                  可以管理产品、订单等业务功能\n                </Text>\n              </div>\n              \n              <div>\n                <Text strong style={{ color: '#52c41a' }}>\n                  <UserOutlined /> 普通用户\n                </Text>\n                <br />\n                <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                  只能查看基本信息，权限受限\n                </Text>\n              </div>\n            </Space>\n          </Card>\n\n          <Card title=\"密码要求\">\n            <Space direction=\"vertical\" size=\"small\" style={{ width: '100%' }}>\n              <Text style={{ fontSize: '12px' }}>• 密码长度至少6个字符</Text>\n              <Text style={{ fontSize: '12px' }}>• 建议包含字母、数字和特殊字符</Text>\n              <Text style={{ fontSize: '12px' }}>• 避免使用常见密码</Text>\n              <Text style={{ fontSize: '12px' }}>• 定期更换密码</Text>\n            </Space>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default UserCreate;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,iBAAiB,QACZ,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGf,UAAU;AAClC,MAAM;EAAEgB;AAAO,CAAC,GAAGnB,MAAM;AAEzB,MAAMoB,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,IAAI,CAAC,GAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAG9B,KAAK,CAAC+B,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMC,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1CH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAI,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;;MAE5B;MACA,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD3B,OAAO,CAAC6B,OAAO,CAAC,QAAQ,CAAC;MACzBX,QAAQ,CAAC,aAAa,CAAC;IACzB,CAAC,CAAC,OAAOY,KAAK,EAAE;MACd9B,OAAO,CAAC8B,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBf,IAAI,CAACgB,WAAW,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBf,QAAQ,CAAC,aAAa,CAAC;EACzB,CAAC;EAED,oBACER,OAAA;IAAAwB,QAAA,gBACExB,OAAA;MAAKyB,KAAK,EAAE;QAAEC,YAAY,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAL,QAAA,gBACvGxB,OAAA,CAACC,KAAK;QAAC6B,KAAK,EAAE,CAAE;QAACL,KAAK,EAAE;UAAEM,MAAM,EAAE;QAAE,CAAE;QAAAP,QAAA,EAAC;MAEvC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRnC,OAAA,CAACf,MAAM;QACLmD,IAAI,eAAEpC,OAAA,CAACH,iBAAiB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5BE,OAAO,EAAEd,UAAW;QAAAC,QAAA,EACrB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENnC,OAAA,CAACZ,GAAG;MAACkD,MAAM,EAAE,EAAG;MAAAd,QAAA,gBACdxB,OAAA,CAACX,GAAG;QAACkD,IAAI,EAAE,EAAG;QAAAf,QAAA,eACZxB,OAAA,CAACnB,IAAI;UAAC2D,KAAK,EAAC,0BAAM;UAACf,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAF,QAAA,eAC7CxB,OAAA,CAAClB,IAAI;YACHwB,IAAI,EAAEA,IAAK;YACXmC,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAE9B,YAAa;YACvB+B,aAAa,EAAE;cACbC,IAAI,EAAE,MAAM;cACZC,MAAM,EAAE;YACV,CAAE;YAAArB,QAAA,gBAEFxB,OAAA,CAACZ,GAAG;cAACkD,MAAM,EAAE,EAAG;cAAAd,QAAA,gBACdxB,OAAA,CAACX,GAAG;gBAACkD,IAAI,EAAE,EAAG;gBAAAf,QAAA,eACZxB,OAAA,CAAClB,IAAI,CAACgE,IAAI;kBACRC,KAAK,EAAC,oBAAK;kBACXC,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5D,OAAO,EAAE;kBAAS,CAAC,EACrC;oBAAE6D,GAAG,EAAE,CAAC;oBAAEC,GAAG,EAAE,EAAE;oBAAE9D,OAAO,EAAE;kBAAgB,CAAC,EAC7C;oBAAE+D,OAAO,EAAE,iBAAiB;oBAAE/D,OAAO,EAAE;kBAAmB,CAAC,CAC3D;kBAAAkC,QAAA,eAEFxB,OAAA,CAACjB,KAAK;oBACJuE,MAAM,eAAEtD,OAAA,CAACR,YAAY;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzBoB,WAAW,EAAC,sCAAQ;oBACpBC,IAAI,EAAC;kBAAO;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnC,OAAA,CAACX,GAAG;gBAACkD,IAAI,EAAE,EAAG;gBAAAf,QAAA,eACZxB,OAAA,CAAClB,IAAI,CAACgE,IAAI;kBACRC,KAAK,EAAC,0BAAM;kBACZC,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5D,OAAO,EAAE;kBAAU,CAAC,EACtC;oBAAEmE,IAAI,EAAE,OAAO;oBAAEnE,OAAO,EAAE;kBAAa,CAAC,CACxC;kBAAAkC,QAAA,eAEFxB,OAAA,CAACjB,KAAK;oBACJuE,MAAM,eAAEtD,OAAA,CAACP,YAAY;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzBoB,WAAW,EAAC,4CAAS;oBACrBC,IAAI,EAAC;kBAAO;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnC,OAAA,CAACZ,GAAG;cAACkD,MAAM,EAAE,EAAG;cAAAd,QAAA,gBACdxB,OAAA,CAACX,GAAG;gBAACkD,IAAI,EAAE,EAAG;gBAAAf,QAAA,eACZxB,OAAA,CAAClB,IAAI,CAACgE,IAAI;kBACRC,KAAK,EAAC,cAAI;kBACVC,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5D,OAAO,EAAE;kBAAQ,CAAC,EACpC;oBAAE6D,GAAG,EAAE,CAAC;oBAAE7D,OAAO,EAAE;kBAAW,CAAC,CAC/B;kBAAAkC,QAAA,eAEFxB,OAAA,CAACjB,KAAK,CAAC2E,QAAQ;oBACbJ,MAAM,eAAEtD,OAAA,CAACN,YAAY;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzBoB,WAAW,EAAC,gCAAO;oBACnBC,IAAI,EAAC;kBAAO;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnC,OAAA,CAACX,GAAG;gBAACkD,IAAI,EAAE,EAAG;gBAAAf,QAAA,eACZxB,OAAA,CAAClB,IAAI,CAACgE,IAAI;kBACRC,KAAK,EAAC,0BAAM;kBACZC,IAAI,EAAC,iBAAiB;kBACtBW,YAAY,EAAE,CAAC,UAAU,CAAE;kBAC3BV,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5D,OAAO,EAAE;kBAAQ,CAAC,EACpC,CAAC;oBAAEsE;kBAAc,CAAC,MAAM;oBACtBC,SAASA,CAACC,CAAC,EAAEC,KAAK,EAAE;sBAClB,IAAI,CAACA,KAAK,IAAIH,aAAa,CAAC,UAAU,CAAC,KAAKG,KAAK,EAAE;wBACjD,OAAO/C,OAAO,CAACC,OAAO,CAAC,CAAC;sBAC1B;sBACA,OAAOD,OAAO,CAACgD,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAChD;kBACF,CAAC,CAAC,CACF;kBAAAzC,QAAA,eAEFxB,OAAA,CAACjB,KAAK,CAAC2E,QAAQ;oBACbJ,MAAM,eAAEtD,OAAA,CAACN,YAAY;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzBoB,WAAW,EAAC,4CAAS;oBACrBC,IAAI,EAAC;kBAAO;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnC,OAAA,CAACT,OAAO;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEXnC,OAAA,CAACZ,GAAG;cAACkD,MAAM,EAAE,EAAG;cAAAd,QAAA,gBACdxB,OAAA,CAACX,GAAG;gBAACkD,IAAI,EAAE,EAAG;gBAAAf,QAAA,eACZxB,OAAA,CAAClB,IAAI,CAACgE,IAAI;kBACRC,KAAK,EAAC,0BAAM;kBACZC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5D,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAAkC,QAAA,eAEhDxB,OAAA,CAAChB,MAAM;oBACLuE,WAAW,EAAC,4CAAS;oBACrBC,IAAI,EAAC,OAAO;oBACZU,UAAU,eAAElE,OAAA,CAACL,YAAY;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAX,QAAA,gBAE7BxB,OAAA,CAACG,MAAM;sBAAC4D,KAAK,EAAC,OAAO;sBAAAvC,QAAA,eACnBxB,OAAA,CAACd,KAAK;wBAAAsC,QAAA,gBACJxB,OAAA,CAACL,YAAY;0BAAC8B,KAAK,EAAE;4BAAE0C,KAAK,EAAE;0BAAU;wBAAE;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CnC,OAAA;0BAAAwB,QAAA,EAAM;wBAAK;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACTnC,OAAA,CAACG,MAAM;sBAAC4D,KAAK,EAAC,SAAS;sBAAAvC,QAAA,eACrBxB,OAAA,CAACd,KAAK;wBAAAsC,QAAA,gBACJxB,OAAA,CAACL,YAAY;0BAAC8B,KAAK,EAAE;4BAAE0C,KAAK,EAAE;0BAAU;wBAAE;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CnC,OAAA;0BAAAwB,QAAA,EAAM;wBAAG;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACTnC,OAAA,CAACG,MAAM;sBAAC4D,KAAK,EAAC,MAAM;sBAAAvC,QAAA,eAClBxB,OAAA,CAACd,KAAK;wBAAAsC,QAAA,gBACJxB,OAAA,CAACR,YAAY;0BAACiC,KAAK,EAAE;4BAAE0C,KAAK,EAAE;0BAAU;wBAAE;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CnC,OAAA;0BAAAwB,QAAA,EAAM;wBAAI;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnC,OAAA,CAACX,GAAG;gBAACkD,IAAI,EAAE,EAAG;gBAAAf,QAAA,eACZxB,OAAA,CAAClB,IAAI,CAACgE,IAAI;kBACRC,KAAK,EAAC,0BAAM;kBACZC,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5D,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAAkC,QAAA,eAEhDxB,OAAA,CAAChB,MAAM;oBAACuE,WAAW,EAAC,4CAAS;oBAACC,IAAI,EAAC,OAAO;oBAAAhC,QAAA,gBACxCxB,OAAA,CAACG,MAAM;sBAAC4D,KAAK,EAAC,QAAQ;sBAAAvC,QAAA,EAAC;oBAAE;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClCnC,OAAA,CAACG,MAAM;sBAAC4D,KAAK,EAAC,UAAU;sBAAAvC,QAAA,EAAC;oBAAE;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnC,OAAA,CAAClB,IAAI,CAACgE,IAAI;cACRC,KAAK,EAAC,cAAI;cACVC,IAAI,EAAC,QAAQ;cAAAxB,QAAA,eAEbxB,OAAA,CAACjB,KAAK,CAACqF,QAAQ;gBACbb,WAAW,EAAC,oEAAa;gBACzBc,IAAI,EAAE,CAAE;gBACRC,SAAS,EAAE,GAAI;gBACfC,SAAS;cAAA;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZnC,OAAA,CAAClB,IAAI,CAACgE,IAAI;cAACrB,KAAK,EAAE;gBAAE+C,SAAS,EAAE;cAAG,CAAE;cAAAhD,QAAA,eAClCxB,OAAA,CAACd,KAAK;gBAACsE,IAAI,EAAC,OAAO;gBAAAhC,QAAA,gBACjBxB,OAAA,CAACf,MAAM;kBACLwE,IAAI,EAAC,SAAS;kBACdgB,QAAQ,EAAC,QAAQ;kBACjBhE,OAAO,EAAEA,OAAQ;kBACjB2B,IAAI,eAAEpC,OAAA,CAACJ,YAAY;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBqB,IAAI,EAAC,OAAO;kBAAAhC,QAAA,EACb;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnC,OAAA,CAACf,MAAM;kBACLoD,OAAO,EAAEhB,WAAY;kBACrBmC,IAAI,EAAC,OAAO;kBAAAhC,QAAA,EACb;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnC,OAAA,CAACf,MAAM;kBACLoD,OAAO,EAAEd,UAAW;kBACpBiC,IAAI,EAAC,OAAO;kBAAAhC,QAAA,EACb;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENnC,OAAA,CAACX,GAAG;QAACkD,IAAI,EAAE,CAAE;QAAAf,QAAA,gBACXxB,OAAA,CAACnB,IAAI;UAAC2D,KAAK,EAAC,0BAAM;UAACf,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAF,QAAA,eAC7CxB,OAAA,CAACd,KAAK;YAACwF,SAAS,EAAC,UAAU;YAAClB,IAAI,EAAC,QAAQ;YAAC/B,KAAK,EAAE;cAAEkD,KAAK,EAAE;YAAO,CAAE;YAAAnD,QAAA,gBACjExB,OAAA;cAAAwB,QAAA,gBACExB,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAACnD,KAAK,EAAE;kBAAE0C,KAAK,EAAE;gBAAU,CAAE;gBAAA3C,QAAA,gBACvCxB,OAAA,CAACL,YAAY;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mCAClB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPnC,OAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnC,OAAA,CAACE,IAAI;gBAACuD,IAAI,EAAC,WAAW;gBAAChC,KAAK,EAAE;kBAAEoD,QAAQ,EAAE;gBAAO,CAAE;gBAAArD,QAAA,EAAC;cAEpD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENnC,OAAA;cAAAwB,QAAA,gBACExB,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAACnD,KAAK,EAAE;kBAAE0C,KAAK,EAAE;gBAAU,CAAE;gBAAA3C,QAAA,gBACvCxB,OAAA,CAACL,YAAY;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAClB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPnC,OAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnC,OAAA,CAACE,IAAI;gBAACuD,IAAI,EAAC,WAAW;gBAAChC,KAAK,EAAE;kBAAEoD,QAAQ,EAAE;gBAAO,CAAE;gBAAArD,QAAA,EAAC;cAEpD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENnC,OAAA;cAAAwB,QAAA,gBACExB,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAACnD,KAAK,EAAE;kBAAE0C,KAAK,EAAE;gBAAU,CAAE;gBAAA3C,QAAA,gBACvCxB,OAAA,CAACR,YAAY;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,6BAClB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPnC,OAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnC,OAAA,CAACE,IAAI;gBAACuD,IAAI,EAAC,WAAW;gBAAChC,KAAK,EAAE;kBAAEoD,QAAQ,EAAE;gBAAO,CAAE;gBAAArD,QAAA,EAAC;cAEpD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPnC,OAAA,CAACnB,IAAI;UAAC2D,KAAK,EAAC,0BAAM;UAAAhB,QAAA,eAChBxB,OAAA,CAACd,KAAK;YAACwF,SAAS,EAAC,UAAU;YAAClB,IAAI,EAAC,OAAO;YAAC/B,KAAK,EAAE;cAAEkD,KAAK,EAAE;YAAO,CAAE;YAAAnD,QAAA,gBAChExB,OAAA,CAACE,IAAI;cAACuB,KAAK,EAAE;gBAAEoD,QAAQ,EAAE;cAAO,CAAE;cAAArD,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDnC,OAAA,CAACE,IAAI;cAACuB,KAAK,EAAE;gBAAEoD,QAAQ,EAAE;cAAO,CAAE;cAAArD,QAAA,EAAC;YAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DnC,OAAA,CAACE,IAAI;cAACuB,KAAK,EAAE;gBAAEoD,QAAQ,EAAE;cAAO,CAAE;cAAArD,QAAA,EAAC;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDnC,OAAA,CAACE,IAAI;cAACuB,KAAK,EAAE;gBAAEoD,QAAQ,EAAE;cAAO,CAAE;cAAArD,QAAA,EAAC;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAjRID,UAAoB;EAAA,QACTtB,IAAI,CAACyB,OAAO,EACVT,WAAW;AAAA;AAAAgF,EAAA,GAFxB1E,UAAoB;AAmR1B,eAAeA,UAAU;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
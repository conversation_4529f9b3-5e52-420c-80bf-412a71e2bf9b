{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { createTheme, StyleContext as CssInJsStyleContext } from '@ant-design/cssinjs';\nimport IconContext from \"@ant-design/icons/es/components/Context\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { merge } from \"rc-util/es/utils/set\";\nimport warning, { devUseWarning, WarningContext } from '../_util/warning';\nimport ValidateMessagesContext from '../form/validateMessagesContext';\nimport LocaleProvider, { ANT_MARK } from '../locale';\nimport LocaleContext from '../locale/context';\nimport defaultLocale from '../locale/en_US';\nimport { defaultTheme, DesignTokenContext } from '../theme/context';\nimport defaultSeedToken from '../theme/themes/seed';\nimport { ConfigConsumer, ConfigContext, defaultIconPrefixCls, defaultPrefixCls, Variants } from './context';\nimport { registerTheme } from './cssVariables';\nimport { DisabledContextProvider } from './DisabledContext';\nimport useConfig from './hooks/useConfig';\nimport useTheme from './hooks/useTheme';\nimport MotionWrapper from './MotionWrapper';\nimport PropWarning from './PropWarning';\nimport SizeContext, { SizeContextProvider } from './SizeContext';\nimport useStyle from './style';\nexport { Variants };\n/**\n * Since too many feedback using static method like `Modal.confirm` not getting theme, we record the\n * theme register info here to help developer get warning info.\n */\nlet existThemeConfig = false;\nexport const warnContext = process.env.NODE_ENV !== 'production' ? componentName => {\n  process.env.NODE_ENV !== \"production\" ? warning(!existThemeConfig, componentName, \"Static function can not consume context like dynamic theme. Please use 'App' component instead.\") : void 0;\n} : /* istanbul ignore next */\nnull;\nexport { ConfigConsumer, ConfigContext, defaultPrefixCls, defaultIconPrefixCls };\nexport const configConsumerProps = ['getTargetContainer', 'getPopupContainer', 'rootPrefixCls', 'getPrefixCls', 'renderEmpty', 'csp', 'autoInsertSpaceInButton', 'locale'];\n// These props is used by `useContext` directly in sub component\nconst PASSED_PROPS = ['getTargetContainer', 'getPopupContainer', 'renderEmpty', 'input', 'pagination', 'form', 'select', 'button'];\nlet globalPrefixCls;\nlet globalIconPrefixCls;\nlet globalTheme;\nlet globalHolderRender;\nfunction getGlobalPrefixCls() {\n  return globalPrefixCls || defaultPrefixCls;\n}\nfunction getGlobalIconPrefixCls() {\n  return globalIconPrefixCls || defaultIconPrefixCls;\n}\nfunction isLegacyTheme(theme) {\n  return Object.keys(theme).some(key => key.endsWith('Color'));\n}\nconst setGlobalConfig = props => {\n  const {\n    prefixCls,\n    iconPrefixCls,\n    theme,\n    holderRender\n  } = props;\n  if (prefixCls !== undefined) {\n    globalPrefixCls = prefixCls;\n  }\n  if (iconPrefixCls !== undefined) {\n    globalIconPrefixCls = iconPrefixCls;\n  }\n  if ('holderRender' in props) {\n    globalHolderRender = holderRender;\n  }\n  if (theme) {\n    if (isLegacyTheme(theme)) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', '`config` of css variable theme is not work in v5. Please use new `theme` config instead.') : void 0;\n      registerTheme(getGlobalPrefixCls(), theme);\n    } else {\n      globalTheme = theme;\n    }\n  }\n};\nexport const globalConfig = () => ({\n  getPrefixCls: (suffixCls, customizePrefixCls) => {\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    return suffixCls ? \"\".concat(getGlobalPrefixCls(), \"-\").concat(suffixCls) : getGlobalPrefixCls();\n  },\n  getIconPrefixCls: getGlobalIconPrefixCls,\n  getRootPrefixCls: () => {\n    // If Global prefixCls provided, use this\n    if (globalPrefixCls) {\n      return globalPrefixCls;\n    }\n    // Fallback to default prefixCls\n    return getGlobalPrefixCls();\n  },\n  getTheme: () => globalTheme,\n  holderRender: globalHolderRender\n});\nconst ProviderChildren = props => {\n  const {\n    children,\n    csp: customCsp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    form,\n    locale,\n    componentSize,\n    direction,\n    space,\n    splitter,\n    virtual,\n    dropdownMatchSelectWidth,\n    popupMatchSelectWidth,\n    popupOverflow,\n    legacyLocale,\n    parentContext,\n    iconPrefixCls: customIconPrefixCls,\n    theme,\n    componentDisabled,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    input,\n    textArea,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  } = props;\n  // =================================== Context ===================================\n  const getPrefixCls = React.useCallback((suffixCls, customizePrefixCls) => {\n    const {\n      prefixCls\n    } = props;\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    const mergedPrefixCls = prefixCls || parentContext.getPrefixCls('');\n    return suffixCls ? \"\".concat(mergedPrefixCls, \"-\").concat(suffixCls) : mergedPrefixCls;\n  }, [parentContext.getPrefixCls, props.prefixCls]);\n  const iconPrefixCls = customIconPrefixCls || parentContext.iconPrefixCls || defaultIconPrefixCls;\n  const csp = customCsp || parentContext.csp;\n  useStyle(iconPrefixCls, csp);\n  const mergedTheme = useTheme(theme, parentContext.theme, {\n    prefixCls: getPrefixCls('')\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    existThemeConfig = existThemeConfig || !!mergedTheme;\n  }\n  const baseConfig = {\n    csp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    locale: locale || legacyLocale,\n    direction,\n    space,\n    splitter,\n    virtual,\n    popupMatchSelectWidth: popupMatchSelectWidth !== null && popupMatchSelectWidth !== void 0 ? popupMatchSelectWidth : dropdownMatchSelectWidth,\n    popupOverflow,\n    getPrefixCls,\n    iconPrefixCls,\n    theme: mergedTheme,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    input,\n    textArea,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    const warningFn = devUseWarning('ConfigProvider');\n    warningFn(!('autoInsertSpaceInButton' in props), 'deprecated', '`autoInsertSpaceInButton` is deprecated. Please use `{ button: { autoInsertSpace: boolean }}` instead.');\n  }\n  const config = Object.assign({}, parentContext);\n  Object.keys(baseConfig).forEach(key => {\n    if (baseConfig[key] !== undefined) {\n      config[key] = baseConfig[key];\n    }\n  });\n  // Pass the props used by `useContext` directly with child component.\n  // These props should merged into `config`.\n  PASSED_PROPS.forEach(propName => {\n    const propValue = props[propName];\n    if (propValue) {\n      config[propName] = propValue;\n    }\n  });\n  if (typeof autoInsertSpaceInButton !== 'undefined') {\n    // merge deprecated api\n    config.button = Object.assign({\n      autoInsertSpace: autoInsertSpaceInButton\n    }, config.button);\n  }\n  // https://github.com/ant-design/ant-design/issues/27617\n  const memoedConfig = useMemo(() => config, config, (prevConfig, currentConfig) => {\n    const prevKeys = Object.keys(prevConfig);\n    const currentKeys = Object.keys(currentConfig);\n    return prevKeys.length !== currentKeys.length || prevKeys.some(key => prevConfig[key] !== currentConfig[key]);\n  });\n  const {\n    layer\n  } = React.useContext(CssInJsStyleContext);\n  const memoIconContextValue = React.useMemo(() => ({\n    prefixCls: iconPrefixCls,\n    csp,\n    layer: layer ? 'antd' : undefined\n  }), [iconPrefixCls, csp, layer]);\n  let childNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PropWarning, {\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }), children);\n  const validateMessages = React.useMemo(() => {\n    var _a, _b, _c, _d;\n    return merge(((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.defaultValidateMessages) || {}, ((_c = (_b = memoedConfig.locale) === null || _b === void 0 ? void 0 : _b.Form) === null || _c === void 0 ? void 0 : _c.defaultValidateMessages) || {}, ((_d = memoedConfig.form) === null || _d === void 0 ? void 0 : _d.validateMessages) || {}, (form === null || form === void 0 ? void 0 : form.validateMessages) || {});\n  }, [memoedConfig, form === null || form === void 0 ? void 0 : form.validateMessages]);\n  if (Object.keys(validateMessages).length > 0) {\n    childNode = /*#__PURE__*/React.createElement(ValidateMessagesContext.Provider, {\n      value: validateMessages\n    }, childNode);\n  }\n  if (locale) {\n    childNode = /*#__PURE__*/React.createElement(LocaleProvider, {\n      locale: locale,\n      _ANT_MARK__: ANT_MARK\n    }, childNode);\n  }\n  if (iconPrefixCls || csp) {\n    childNode = /*#__PURE__*/React.createElement(IconContext.Provider, {\n      value: memoIconContextValue\n    }, childNode);\n  }\n  if (componentSize) {\n    childNode = /*#__PURE__*/React.createElement(SizeContextProvider, {\n      size: componentSize\n    }, childNode);\n  }\n  // =================================== Motion ===================================\n  childNode = /*#__PURE__*/React.createElement(MotionWrapper, null, childNode);\n  // ================================ Dynamic theme ================================\n  const memoTheme = React.useMemo(() => {\n    const _a = mergedTheme || {},\n      {\n        algorithm,\n        token,\n        components,\n        cssVar\n      } = _a,\n      rest = __rest(_a, [\"algorithm\", \"token\", \"components\", \"cssVar\"]);\n    const themeObj = algorithm && (!Array.isArray(algorithm) || algorithm.length > 0) ? createTheme(algorithm) : defaultTheme;\n    const parsedComponents = {};\n    Object.entries(components || {}).forEach(_ref => {\n      let [componentName, componentToken] = _ref;\n      const parsedToken = Object.assign({}, componentToken);\n      if ('algorithm' in parsedToken) {\n        if (parsedToken.algorithm === true) {\n          parsedToken.theme = themeObj;\n        } else if (Array.isArray(parsedToken.algorithm) || typeof parsedToken.algorithm === 'function') {\n          parsedToken.theme = createTheme(parsedToken.algorithm);\n        }\n        delete parsedToken.algorithm;\n      }\n      parsedComponents[componentName] = parsedToken;\n    });\n    const mergedToken = Object.assign(Object.assign({}, defaultSeedToken), token);\n    return Object.assign(Object.assign({}, rest), {\n      theme: themeObj,\n      token: mergedToken,\n      components: parsedComponents,\n      override: Object.assign({\n        override: mergedToken\n      }, parsedComponents),\n      cssVar: cssVar\n    });\n  }, [mergedTheme]);\n  if (theme) {\n    childNode = /*#__PURE__*/React.createElement(DesignTokenContext.Provider, {\n      value: memoTheme\n    }, childNode);\n  }\n  // ================================== Warning ===================================\n  if (memoedConfig.warning) {\n    childNode = /*#__PURE__*/React.createElement(WarningContext.Provider, {\n      value: memoedConfig.warning\n    }, childNode);\n  }\n  // =================================== Render ===================================\n  if (componentDisabled !== undefined) {\n    childNode = /*#__PURE__*/React.createElement(DisabledContextProvider, {\n      disabled: componentDisabled\n    }, childNode);\n  }\n  return /*#__PURE__*/React.createElement(ConfigContext.Provider, {\n    value: memoedConfig\n  }, childNode);\n};\nconst ConfigProvider = props => {\n  const context = React.useContext(ConfigContext);\n  const antLocale = React.useContext(LocaleContext);\n  return /*#__PURE__*/React.createElement(ProviderChildren, Object.assign({\n    parentContext: context,\n    legacyLocale: antLocale\n  }, props));\n};\nConfigProvider.ConfigContext = ConfigContext;\nConfigProvider.SizeContext = SizeContext;\nConfigProvider.config = setGlobalConfig;\nConfigProvider.useConfig = useConfig;\nObject.defineProperty(ConfigProvider, 'SizeContext', {\n  get: () => {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'ConfigProvider.SizeContext is deprecated. Please use `ConfigProvider.useConfig().componentSize` instead.') : void 0;\n    return SizeContext;\n  }\n});\nif (process.env.NODE_ENV !== 'production') {\n  ConfigProvider.displayName = 'ConfigProvider';\n}\nexport default ConfigProvider;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "createTheme", "StyleContext", "CssInJsStyleContext", "IconContext", "useMemo", "merge", "warning", "devUseW<PERSON>ning", "WarningContext", "ValidateMessagesContext", "LocaleProvider", "ANT_MARK", "LocaleContext", "defaultLocale", "defaultTheme", "DesignTokenContext", "defaultSeedToken", "ConfigConsumer", "ConfigContext", "defaultIconPrefixCls", "defaultPrefixCls", "Variants", "registerTheme", "DisabledContextProvider", "useConfig", "useTheme", "MotionWrapper", "Prop<PERSON><PERSON>ning", "SizeContext", "SizeContextProvider", "useStyle", "existThemeConfig", "warnContext", "process", "env", "NODE_ENV", "componentName", "configConsumerProps", "PASSED_PROPS", "globalPrefixCls", "globalIconPrefixCls", "globalTheme", "globalHolderRender", "getGlobalPrefixCls", "getGlobalIconPrefixCls", "isLegacyTheme", "theme", "keys", "some", "key", "endsWith", "setGlobalConfig", "props", "prefixCls", "iconPrefixCls", "<PERSON><PERSON><PERSON>", "undefined", "globalConfig", "getPrefixCls", "suffixCls", "customizePrefixCls", "concat", "getIconPrefixCls", "getRootPrefixCls", "getTheme", "Provide<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "csp", "customCsp", "autoInsertSpaceInButton", "alert", "anchor", "form", "locale", "componentSize", "direction", "space", "splitter", "virtual", "dropdownMatchSelectWidth", "popupMatchSelectWidth", "popupOverflow", "legacyLocale", "parentContext", "customIconPrefixCls", "componentDisabled", "segmented", "statistic", "spin", "calendar", "carousel", "cascader", "collapse", "typography", "checkbox", "descriptions", "divider", "drawer", "skeleton", "steps", "image", "layout", "list", "mentions", "modal", "progress", "result", "slider", "breadcrumb", "menu", "pagination", "input", "textArea", "empty", "badge", "radio", "rate", "switch", "SWITCH", "transfer", "avatar", "message", "tag", "table", "card", "tabs", "timeline", "timePicker", "upload", "notification", "tree", "colorPicker", "datePicker", "rangePicker", "flex", "wave", "dropdown", "warningConfig", "tour", "tooltip", "popover", "popconfirm", "floatButtonGroup", "variant", "inputNumber", "treeSelect", "useCallback", "mergedPrefixCls", "mergedTheme", "baseConfig", "warningFn", "config", "assign", "for<PERSON>ach", "propName", "propValue", "button", "autoInsertSpace", "memoedConfig", "prevConfig", "currentConfig", "prevKeys", "currentKeys", "layer", "useContext", "memoIconContextValue", "childNode", "createElement", "Fragment", "validateMessages", "_a", "_b", "_c", "_d", "Form", "defaultValidateMessages", "Provider", "value", "_ANT_MARK__", "size", "memoTheme", "algorithm", "token", "components", "cssVar", "rest", "themeObj", "Array", "isArray", "parsedComponents", "entries", "_ref", "componentToken", "parsedToken", "mergedToken", "override", "disabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "context", "antLocale", "defineProperty", "get", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/config-provider/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { createTheme, StyleContext as CssInJsStyleContext } from '@ant-design/cssinjs';\nimport IconContext from \"@ant-design/icons/es/components/Context\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { merge } from \"rc-util/es/utils/set\";\nimport warning, { devUseWarning, WarningContext } from '../_util/warning';\nimport ValidateMessagesContext from '../form/validateMessagesContext';\nimport LocaleProvider, { ANT_MARK } from '../locale';\nimport LocaleContext from '../locale/context';\nimport defaultLocale from '../locale/en_US';\nimport { defaultTheme, DesignTokenContext } from '../theme/context';\nimport defaultSeedToken from '../theme/themes/seed';\nimport { ConfigConsumer, ConfigContext, defaultIconPrefixCls, defaultPrefixCls, Variants } from './context';\nimport { registerTheme } from './cssVariables';\nimport { DisabledContextProvider } from './DisabledContext';\nimport useConfig from './hooks/useConfig';\nimport useTheme from './hooks/useTheme';\nimport MotionWrapper from './MotionWrapper';\nimport PropWarning from './PropWarning';\nimport SizeContext, { SizeContextProvider } from './SizeContext';\nimport useStyle from './style';\nexport { Variants };\n/**\n * Since too many feedback using static method like `Modal.confirm` not getting theme, we record the\n * theme register info here to help developer get warning info.\n */\nlet existThemeConfig = false;\nexport const warnContext = process.env.NODE_ENV !== 'production' ? componentName => {\n  process.env.NODE_ENV !== \"production\" ? warning(!existThemeConfig, componentName, `Static function can not consume context like dynamic theme. Please use 'App' component instead.`) : void 0;\n} : /* istanbul ignore next */\nnull;\nexport { ConfigConsumer, ConfigContext, defaultPrefixCls, defaultIconPrefixCls };\nexport const configConsumerProps = ['getTargetContainer', 'getPopupContainer', 'rootPrefixCls', 'getPrefixCls', 'renderEmpty', 'csp', 'autoInsertSpaceInButton', 'locale'];\n// These props is used by `useContext` directly in sub component\nconst PASSED_PROPS = ['getTargetContainer', 'getPopupContainer', 'renderEmpty', 'input', 'pagination', 'form', 'select', 'button'];\nlet globalPrefixCls;\nlet globalIconPrefixCls;\nlet globalTheme;\nlet globalHolderRender;\nfunction getGlobalPrefixCls() {\n  return globalPrefixCls || defaultPrefixCls;\n}\nfunction getGlobalIconPrefixCls() {\n  return globalIconPrefixCls || defaultIconPrefixCls;\n}\nfunction isLegacyTheme(theme) {\n  return Object.keys(theme).some(key => key.endsWith('Color'));\n}\nconst setGlobalConfig = props => {\n  const {\n    prefixCls,\n    iconPrefixCls,\n    theme,\n    holderRender\n  } = props;\n  if (prefixCls !== undefined) {\n    globalPrefixCls = prefixCls;\n  }\n  if (iconPrefixCls !== undefined) {\n    globalIconPrefixCls = iconPrefixCls;\n  }\n  if ('holderRender' in props) {\n    globalHolderRender = holderRender;\n  }\n  if (theme) {\n    if (isLegacyTheme(theme)) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', '`config` of css variable theme is not work in v5. Please use new `theme` config instead.') : void 0;\n      registerTheme(getGlobalPrefixCls(), theme);\n    } else {\n      globalTheme = theme;\n    }\n  }\n};\nexport const globalConfig = () => ({\n  getPrefixCls: (suffixCls, customizePrefixCls) => {\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    return suffixCls ? `${getGlobalPrefixCls()}-${suffixCls}` : getGlobalPrefixCls();\n  },\n  getIconPrefixCls: getGlobalIconPrefixCls,\n  getRootPrefixCls: () => {\n    // If Global prefixCls provided, use this\n    if (globalPrefixCls) {\n      return globalPrefixCls;\n    }\n    // Fallback to default prefixCls\n    return getGlobalPrefixCls();\n  },\n  getTheme: () => globalTheme,\n  holderRender: globalHolderRender\n});\nconst ProviderChildren = props => {\n  const {\n    children,\n    csp: customCsp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    form,\n    locale,\n    componentSize,\n    direction,\n    space,\n    splitter,\n    virtual,\n    dropdownMatchSelectWidth,\n    popupMatchSelectWidth,\n    popupOverflow,\n    legacyLocale,\n    parentContext,\n    iconPrefixCls: customIconPrefixCls,\n    theme,\n    componentDisabled,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    input,\n    textArea,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  } = props;\n  // =================================== Context ===================================\n  const getPrefixCls = React.useCallback((suffixCls, customizePrefixCls) => {\n    const {\n      prefixCls\n    } = props;\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    const mergedPrefixCls = prefixCls || parentContext.getPrefixCls('');\n    return suffixCls ? `${mergedPrefixCls}-${suffixCls}` : mergedPrefixCls;\n  }, [parentContext.getPrefixCls, props.prefixCls]);\n  const iconPrefixCls = customIconPrefixCls || parentContext.iconPrefixCls || defaultIconPrefixCls;\n  const csp = customCsp || parentContext.csp;\n  useStyle(iconPrefixCls, csp);\n  const mergedTheme = useTheme(theme, parentContext.theme, {\n    prefixCls: getPrefixCls('')\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    existThemeConfig = existThemeConfig || !!mergedTheme;\n  }\n  const baseConfig = {\n    csp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    locale: locale || legacyLocale,\n    direction,\n    space,\n    splitter,\n    virtual,\n    popupMatchSelectWidth: popupMatchSelectWidth !== null && popupMatchSelectWidth !== void 0 ? popupMatchSelectWidth : dropdownMatchSelectWidth,\n    popupOverflow,\n    getPrefixCls,\n    iconPrefixCls,\n    theme: mergedTheme,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    input,\n    textArea,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    const warningFn = devUseWarning('ConfigProvider');\n    warningFn(!('autoInsertSpaceInButton' in props), 'deprecated', '`autoInsertSpaceInButton` is deprecated. Please use `{ button: { autoInsertSpace: boolean }}` instead.');\n  }\n  const config = Object.assign({}, parentContext);\n  Object.keys(baseConfig).forEach(key => {\n    if (baseConfig[key] !== undefined) {\n      config[key] = baseConfig[key];\n    }\n  });\n  // Pass the props used by `useContext` directly with child component.\n  // These props should merged into `config`.\n  PASSED_PROPS.forEach(propName => {\n    const propValue = props[propName];\n    if (propValue) {\n      config[propName] = propValue;\n    }\n  });\n  if (typeof autoInsertSpaceInButton !== 'undefined') {\n    // merge deprecated api\n    config.button = Object.assign({\n      autoInsertSpace: autoInsertSpaceInButton\n    }, config.button);\n  }\n  // https://github.com/ant-design/ant-design/issues/27617\n  const memoedConfig = useMemo(() => config, config, (prevConfig, currentConfig) => {\n    const prevKeys = Object.keys(prevConfig);\n    const currentKeys = Object.keys(currentConfig);\n    return prevKeys.length !== currentKeys.length || prevKeys.some(key => prevConfig[key] !== currentConfig[key]);\n  });\n  const {\n    layer\n  } = React.useContext(CssInJsStyleContext);\n  const memoIconContextValue = React.useMemo(() => ({\n    prefixCls: iconPrefixCls,\n    csp,\n    layer: layer ? 'antd' : undefined\n  }), [iconPrefixCls, csp, layer]);\n  let childNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PropWarning, {\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }), children);\n  const validateMessages = React.useMemo(() => {\n    var _a, _b, _c, _d;\n    return merge(((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.defaultValidateMessages) || {}, ((_c = (_b = memoedConfig.locale) === null || _b === void 0 ? void 0 : _b.Form) === null || _c === void 0 ? void 0 : _c.defaultValidateMessages) || {}, ((_d = memoedConfig.form) === null || _d === void 0 ? void 0 : _d.validateMessages) || {}, (form === null || form === void 0 ? void 0 : form.validateMessages) || {});\n  }, [memoedConfig, form === null || form === void 0 ? void 0 : form.validateMessages]);\n  if (Object.keys(validateMessages).length > 0) {\n    childNode = /*#__PURE__*/React.createElement(ValidateMessagesContext.Provider, {\n      value: validateMessages\n    }, childNode);\n  }\n  if (locale) {\n    childNode = /*#__PURE__*/React.createElement(LocaleProvider, {\n      locale: locale,\n      _ANT_MARK__: ANT_MARK\n    }, childNode);\n  }\n  if (iconPrefixCls || csp) {\n    childNode = /*#__PURE__*/React.createElement(IconContext.Provider, {\n      value: memoIconContextValue\n    }, childNode);\n  }\n  if (componentSize) {\n    childNode = /*#__PURE__*/React.createElement(SizeContextProvider, {\n      size: componentSize\n    }, childNode);\n  }\n  // =================================== Motion ===================================\n  childNode = /*#__PURE__*/React.createElement(MotionWrapper, null, childNode);\n  // ================================ Dynamic theme ================================\n  const memoTheme = React.useMemo(() => {\n    const _a = mergedTheme || {},\n      {\n        algorithm,\n        token,\n        components,\n        cssVar\n      } = _a,\n      rest = __rest(_a, [\"algorithm\", \"token\", \"components\", \"cssVar\"]);\n    const themeObj = algorithm && (!Array.isArray(algorithm) || algorithm.length > 0) ? createTheme(algorithm) : defaultTheme;\n    const parsedComponents = {};\n    Object.entries(components || {}).forEach(([componentName, componentToken]) => {\n      const parsedToken = Object.assign({}, componentToken);\n      if ('algorithm' in parsedToken) {\n        if (parsedToken.algorithm === true) {\n          parsedToken.theme = themeObj;\n        } else if (Array.isArray(parsedToken.algorithm) || typeof parsedToken.algorithm === 'function') {\n          parsedToken.theme = createTheme(parsedToken.algorithm);\n        }\n        delete parsedToken.algorithm;\n      }\n      parsedComponents[componentName] = parsedToken;\n    });\n    const mergedToken = Object.assign(Object.assign({}, defaultSeedToken), token);\n    return Object.assign(Object.assign({}, rest), {\n      theme: themeObj,\n      token: mergedToken,\n      components: parsedComponents,\n      override: Object.assign({\n        override: mergedToken\n      }, parsedComponents),\n      cssVar: cssVar\n    });\n  }, [mergedTheme]);\n  if (theme) {\n    childNode = /*#__PURE__*/React.createElement(DesignTokenContext.Provider, {\n      value: memoTheme\n    }, childNode);\n  }\n  // ================================== Warning ===================================\n  if (memoedConfig.warning) {\n    childNode = /*#__PURE__*/React.createElement(WarningContext.Provider, {\n      value: memoedConfig.warning\n    }, childNode);\n  }\n  // =================================== Render ===================================\n  if (componentDisabled !== undefined) {\n    childNode = /*#__PURE__*/React.createElement(DisabledContextProvider, {\n      disabled: componentDisabled\n    }, childNode);\n  }\n  return /*#__PURE__*/React.createElement(ConfigContext.Provider, {\n    value: memoedConfig\n  }, childNode);\n};\nconst ConfigProvider = props => {\n  const context = React.useContext(ConfigContext);\n  const antLocale = React.useContext(LocaleContext);\n  return /*#__PURE__*/React.createElement(ProviderChildren, Object.assign({\n    parentContext: context,\n    legacyLocale: antLocale\n  }, props));\n};\nConfigProvider.ConfigContext = ConfigContext;\nConfigProvider.SizeContext = SizeContext;\nConfigProvider.config = setGlobalConfig;\nConfigProvider.useConfig = useConfig;\nObject.defineProperty(ConfigProvider, 'SizeContext', {\n  get: () => {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'ConfigProvider.SizeContext is deprecated. Please use `ConfigProvider.useConfig().componentSize` instead.') : void 0;\n    return SizeContext;\n  }\n});\nif (process.env.NODE_ENV !== 'production') {\n  ConfigProvider.displayName = 'ConfigProvider';\n}\nexport default ConfigProvider;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,YAAY,IAAIC,mBAAmB,QAAQ,qBAAqB;AACtF,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,OAAOC,OAAO,IAAIC,aAAa,EAAEC,cAAc,QAAQ,kBAAkB;AACzE,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,cAAc,IAAIC,QAAQ,QAAQ,WAAW;AACpD,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAkB;AACnE,OAAOC,gBAAgB,MAAM,sBAAsB;AACnD,SAASC,cAAc,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,WAAW;AAC3G,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,IAAIC,mBAAmB,QAAQ,eAAe;AAChE,OAAOC,QAAQ,MAAM,SAAS;AAC9B,SAAST,QAAQ;AACjB;AACA;AACA;AACA;AACA,IAAIU,gBAAgB,GAAG,KAAK;AAC5B,OAAO,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,aAAa,IAAI;EAClFH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,OAAO,CAAC,CAACyB,gBAAgB,EAAEK,aAAa,mGAAmG,CAAC,GAAG,KAAK,CAAC;AAC/L,CAAC,GAAG;AACJ,IAAI;AACJ,SAASnB,cAAc,EAAEC,aAAa,EAAEE,gBAAgB,EAAED,oBAAoB;AAC9E,OAAO,MAAMkB,mBAAmB,GAAG,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,KAAK,EAAE,yBAAyB,EAAE,QAAQ,CAAC;AAC1K;AACA,MAAMC,YAAY,GAAG,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAClI,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,WAAW;AACf,IAAIC,kBAAkB;AACtB,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,OAAOJ,eAAe,IAAInB,gBAAgB;AAC5C;AACA,SAASwB,sBAAsBA,CAAA,EAAG;EAChC,OAAOJ,mBAAmB,IAAIrB,oBAAoB;AACpD;AACA,SAAS0B,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOxD,MAAM,CAACyD,IAAI,CAACD,KAAK,CAAC,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC9D;AACA,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,SAAS;IACTC,aAAa;IACbR,KAAK;IACLS;EACF,CAAC,GAAGH,KAAK;EACT,IAAIC,SAAS,KAAKG,SAAS,EAAE;IAC3BjB,eAAe,GAAGc,SAAS;EAC7B;EACA,IAAIC,aAAa,KAAKE,SAAS,EAAE;IAC/BhB,mBAAmB,GAAGc,aAAa;EACrC;EACA,IAAI,cAAc,IAAIF,KAAK,EAAE;IAC3BV,kBAAkB,GAAGa,YAAY;EACnC;EACA,IAAIT,KAAK,EAAE;IACT,IAAID,aAAa,CAACC,KAAK,CAAC,EAAE;MACxBb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,0FAA0F,CAAC,GAAG,KAAK,CAAC;MAC7KgB,aAAa,CAACqB,kBAAkB,CAAC,CAAC,EAAEG,KAAK,CAAC;IAC5C,CAAC,MAAM;MACLL,WAAW,GAAGK,KAAK;IACrB;EACF;AACF,CAAC;AACD,OAAO,MAAMW,YAAY,GAAGA,CAAA,MAAO;EACjCC,YAAY,EAAEA,CAACC,SAAS,EAAEC,kBAAkB,KAAK;IAC/C,IAAIA,kBAAkB,EAAE;MACtB,OAAOA,kBAAkB;IAC3B;IACA,OAAOD,SAAS,MAAAE,MAAA,CAAMlB,kBAAkB,CAAC,CAAC,OAAAkB,MAAA,CAAIF,SAAS,IAAKhB,kBAAkB,CAAC,CAAC;EAClF,CAAC;EACDmB,gBAAgB,EAAElB,sBAAsB;EACxCmB,gBAAgB,EAAEA,CAAA,KAAM;IACtB;IACA,IAAIxB,eAAe,EAAE;MACnB,OAAOA,eAAe;IACxB;IACA;IACA,OAAOI,kBAAkB,CAAC,CAAC;EAC7B,CAAC;EACDqB,QAAQ,EAAEA,CAAA,KAAMvB,WAAW;EAC3Bc,YAAY,EAAEb;AAChB,CAAC,CAAC;AACF,MAAMuB,gBAAgB,GAAGb,KAAK,IAAI;EAChC,MAAM;IACJc,QAAQ;IACRC,GAAG,EAAEC,SAAS;IACdC,uBAAuB;IACvBC,KAAK;IACLC,MAAM;IACNC,IAAI;IACJC,MAAM;IACNC,aAAa;IACbC,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC,OAAO;IACPC,wBAAwB;IACxBC,qBAAqB;IACrBC,aAAa;IACbC,YAAY;IACZC,aAAa;IACb7B,aAAa,EAAE8B,mBAAmB;IAClCtC,KAAK;IACLuC,iBAAiB;IACjBC,SAAS;IACTC,SAAS;IACTC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,QAAQ;IACRC,YAAY;IACZC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACRC,KAAK;IACLC,KAAK;IACLC,MAAM;IACNC,IAAI;IACJC,QAAQ;IACRC,KAAK;IACLC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC,UAAU;IACVC,IAAI;IACJC,UAAU;IACVC,KAAK;IACLC,QAAQ;IACRC,KAAK;IACLC,KAAK;IACLC,KAAK;IACLC,IAAI;IACJC,MAAM,EAAEC,MAAM;IACdC,QAAQ;IACRC,MAAM;IACNC,OAAO;IACPC,GAAG;IACHC,KAAK;IACLC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,UAAU;IACVC,MAAM;IACNC,YAAY;IACZC,IAAI;IACJC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRlI,OAAO,EAAEmI,aAAa;IACtBC,IAAI;IACJC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,gBAAgB;IAChBC,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAG7F,KAAK;EACT;EACA,MAAMM,YAAY,GAAG3D,KAAK,CAACmJ,WAAW,CAAC,CAACvF,SAAS,EAAEC,kBAAkB,KAAK;IACxE,MAAM;MACJP;IACF,CAAC,GAAGD,KAAK;IACT,IAAIQ,kBAAkB,EAAE;MACtB,OAAOA,kBAAkB;IAC3B;IACA,MAAMuF,eAAe,GAAG9F,SAAS,IAAI8B,aAAa,CAACzB,YAAY,CAAC,EAAE,CAAC;IACnE,OAAOC,SAAS,MAAAE,MAAA,CAAMsF,eAAe,OAAAtF,MAAA,CAAIF,SAAS,IAAKwF,eAAe;EACxE,CAAC,EAAE,CAAChE,aAAa,CAACzB,YAAY,EAAEN,KAAK,CAACC,SAAS,CAAC,CAAC;EACjD,MAAMC,aAAa,GAAG8B,mBAAmB,IAAID,aAAa,CAAC7B,aAAa,IAAInC,oBAAoB;EAChG,MAAMgD,GAAG,GAAGC,SAAS,IAAIe,aAAa,CAAChB,GAAG;EAC1CrC,QAAQ,CAACwB,aAAa,EAAEa,GAAG,CAAC;EAC5B,MAAMiF,WAAW,GAAG3H,QAAQ,CAACqB,KAAK,EAAEqC,aAAa,CAACrC,KAAK,EAAE;IACvDO,SAAS,EAAEK,YAAY,CAAC,EAAE;EAC5B,CAAC,CAAC;EACF,IAAIzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCJ,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC,CAACqH,WAAW;EACtD;EACA,MAAMC,UAAU,GAAG;IACjBlF,GAAG;IACHE,uBAAuB;IACvBC,KAAK;IACLC,MAAM;IACNE,MAAM,EAAEA,MAAM,IAAIS,YAAY;IAC9BP,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC,OAAO;IACPE,qBAAqB,EAAEA,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGD,wBAAwB;IAC5IE,aAAa;IACbvB,YAAY;IACZJ,aAAa;IACbR,KAAK,EAAEsG,WAAW;IAClB9D,SAAS;IACTC,SAAS;IACTC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,QAAQ;IACRC,YAAY;IACZC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACRC,KAAK;IACLC,KAAK;IACLW,KAAK;IACLC,QAAQ;IACRX,MAAM;IACNC,IAAI;IACJC,QAAQ;IACRC,KAAK;IACLC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC,UAAU;IACVC,IAAI;IACJC,UAAU;IACVG,KAAK;IACLC,KAAK;IACLC,KAAK;IACLC,IAAI;IACJC,MAAM,EAAEC,MAAM;IACdC,QAAQ;IACRC,MAAM;IACNC,OAAO;IACPC,GAAG;IACHC,KAAK;IACLC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,UAAU;IACVC,MAAM;IACNC,YAAY;IACZC,IAAI;IACJC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRlI,OAAO,EAAEmI,aAAa;IACtBC,IAAI;IACJC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,gBAAgB;IAChBC,OAAO;IACPC,WAAW;IACXC;EACF,CAAC;EACD,IAAIhH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMmH,SAAS,GAAG/I,aAAa,CAAC,gBAAgB,CAAC;IACjD+I,SAAS,CAAC,EAAE,yBAAyB,IAAIlG,KAAK,CAAC,EAAE,YAAY,EAAE,wGAAwG,CAAC;EAC1K;EACA,MAAMmG,MAAM,GAAGjK,MAAM,CAACkK,MAAM,CAAC,CAAC,CAAC,EAAErE,aAAa,CAAC;EAC/C7F,MAAM,CAACyD,IAAI,CAACsG,UAAU,CAAC,CAACI,OAAO,CAACxG,GAAG,IAAI;IACrC,IAAIoG,UAAU,CAACpG,GAAG,CAAC,KAAKO,SAAS,EAAE;MACjC+F,MAAM,CAACtG,GAAG,CAAC,GAAGoG,UAAU,CAACpG,GAAG,CAAC;IAC/B;EACF,CAAC,CAAC;EACF;EACA;EACAX,YAAY,CAACmH,OAAO,CAACC,QAAQ,IAAI;IAC/B,MAAMC,SAAS,GAAGvG,KAAK,CAACsG,QAAQ,CAAC;IACjC,IAAIC,SAAS,EAAE;MACbJ,MAAM,CAACG,QAAQ,CAAC,GAAGC,SAAS;IAC9B;EACF,CAAC,CAAC;EACF,IAAI,OAAOtF,uBAAuB,KAAK,WAAW,EAAE;IAClD;IACAkF,MAAM,CAACK,MAAM,GAAGtK,MAAM,CAACkK,MAAM,CAAC;MAC5BK,eAAe,EAAExF;IACnB,CAAC,EAAEkF,MAAM,CAACK,MAAM,CAAC;EACnB;EACA;EACA,MAAME,YAAY,GAAG1J,OAAO,CAAC,MAAMmJ,MAAM,EAAEA,MAAM,EAAE,CAACQ,UAAU,EAAEC,aAAa,KAAK;IAChF,MAAMC,QAAQ,GAAG3K,MAAM,CAACyD,IAAI,CAACgH,UAAU,CAAC;IACxC,MAAMG,WAAW,GAAG5K,MAAM,CAACyD,IAAI,CAACiH,aAAa,CAAC;IAC9C,OAAOC,QAAQ,CAACpK,MAAM,KAAKqK,WAAW,CAACrK,MAAM,IAAIoK,QAAQ,CAACjH,IAAI,CAACC,GAAG,IAAI8G,UAAU,CAAC9G,GAAG,CAAC,KAAK+G,aAAa,CAAC/G,GAAG,CAAC,CAAC;EAC/G,CAAC,CAAC;EACF,MAAM;IACJkH;EACF,CAAC,GAAGpK,KAAK,CAACqK,UAAU,CAAClK,mBAAmB,CAAC;EACzC,MAAMmK,oBAAoB,GAAGtK,KAAK,CAACK,OAAO,CAAC,OAAO;IAChDiD,SAAS,EAAEC,aAAa;IACxBa,GAAG;IACHgG,KAAK,EAAEA,KAAK,GAAG,MAAM,GAAG3G;EAC1B,CAAC,CAAC,EAAE,CAACF,aAAa,EAAEa,GAAG,EAAEgG,KAAK,CAAC,CAAC;EAChC,IAAIG,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAACxK,KAAK,CAACyK,QAAQ,EAAE,IAAI,EAAE,aAAazK,KAAK,CAACwK,aAAa,CAAC5I,WAAW,EAAE;IACnHoD,wBAAwB,EAAEA;EAC5B,CAAC,CAAC,EAAEb,QAAQ,CAAC;EACb,MAAMuG,gBAAgB,GAAG1K,KAAK,CAACK,OAAO,CAAC,MAAM;IAC3C,IAAIsK,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClB,OAAOxK,KAAK,CAAC,CAAC,CAACqK,EAAE,GAAG7J,aAAa,CAACiK,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,uBAAuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGb,YAAY,CAACrF,MAAM,MAAM,IAAI,IAAIkG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,uBAAuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAACF,EAAE,GAAGf,YAAY,CAACtF,IAAI,MAAM,IAAI,IAAIqG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACJ,gBAAgB,KAAK,CAAC,CAAC,EAAE,CAACjG,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACiG,gBAAgB,KAAK,CAAC,CAAC,CAAC;EAC/a,CAAC,EAAE,CAACX,YAAY,EAAEtF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACiG,gBAAgB,CAAC,CAAC;EACrF,IAAInL,MAAM,CAACyD,IAAI,CAAC0H,gBAAgB,CAAC,CAAC5K,MAAM,GAAG,CAAC,EAAE;IAC5CyK,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAAC9J,uBAAuB,CAACuK,QAAQ,EAAE;MAC7EC,KAAK,EAAER;IACT,CAAC,EAAEH,SAAS,CAAC;EACf;EACA,IAAI7F,MAAM,EAAE;IACV6F,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAAC7J,cAAc,EAAE;MAC3D+D,MAAM,EAAEA,MAAM;MACdyG,WAAW,EAAEvK;IACf,CAAC,EAAE2J,SAAS,CAAC;EACf;EACA,IAAIhH,aAAa,IAAIa,GAAG,EAAE;IACxBmG,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAACpK,WAAW,CAAC6K,QAAQ,EAAE;MACjEC,KAAK,EAAEZ;IACT,CAAC,EAAEC,SAAS,CAAC;EACf;EACA,IAAI5F,aAAa,EAAE;IACjB4F,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAAC1I,mBAAmB,EAAE;MAChEsJ,IAAI,EAAEzG;IACR,CAAC,EAAE4F,SAAS,CAAC;EACf;EACA;EACAA,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAAC7I,aAAa,EAAE,IAAI,EAAE4I,SAAS,CAAC;EAC5E;EACA,MAAMc,SAAS,GAAGrL,KAAK,CAACK,OAAO,CAAC,MAAM;IACpC,MAAMsK,EAAE,GAAGtB,WAAW,IAAI,CAAC,CAAC;MAC1B;QACEiC,SAAS;QACTC,KAAK;QACLC,UAAU;QACVC;MACF,CAAC,GAAGd,EAAE;MACNe,IAAI,GAAGxM,MAAM,CAACyL,EAAE,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IACnE,MAAMgB,QAAQ,GAAGL,SAAS,KAAK,CAACM,KAAK,CAACC,OAAO,CAACP,SAAS,CAAC,IAAIA,SAAS,CAACxL,MAAM,GAAG,CAAC,CAAC,GAAGG,WAAW,CAACqL,SAAS,CAAC,GAAGvK,YAAY;IACzH,MAAM+K,gBAAgB,GAAG,CAAC,CAAC;IAC3BvM,MAAM,CAACwM,OAAO,CAACP,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC9B,OAAO,CAACsC,IAAA,IAAqC;MAAA,IAApC,CAAC3J,aAAa,EAAE4J,cAAc,CAAC,GAAAD,IAAA;MACvE,MAAME,WAAW,GAAG3M,MAAM,CAACkK,MAAM,CAAC,CAAC,CAAC,EAAEwC,cAAc,CAAC;MACrD,IAAI,WAAW,IAAIC,WAAW,EAAE;QAC9B,IAAIA,WAAW,CAACZ,SAAS,KAAK,IAAI,EAAE;UAClCY,WAAW,CAACnJ,KAAK,GAAG4I,QAAQ;QAC9B,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACK,WAAW,CAACZ,SAAS,CAAC,IAAI,OAAOY,WAAW,CAACZ,SAAS,KAAK,UAAU,EAAE;UAC9FY,WAAW,CAACnJ,KAAK,GAAG9C,WAAW,CAACiM,WAAW,CAACZ,SAAS,CAAC;QACxD;QACA,OAAOY,WAAW,CAACZ,SAAS;MAC9B;MACAQ,gBAAgB,CAACzJ,aAAa,CAAC,GAAG6J,WAAW;IAC/C,CAAC,CAAC;IACF,MAAMC,WAAW,GAAG5M,MAAM,CAACkK,MAAM,CAAClK,MAAM,CAACkK,MAAM,CAAC,CAAC,CAAC,EAAExI,gBAAgB,CAAC,EAAEsK,KAAK,CAAC;IAC7E,OAAOhM,MAAM,CAACkK,MAAM,CAAClK,MAAM,CAACkK,MAAM,CAAC,CAAC,CAAC,EAAEiC,IAAI,CAAC,EAAE;MAC5C3I,KAAK,EAAE4I,QAAQ;MACfJ,KAAK,EAAEY,WAAW;MAClBX,UAAU,EAAEM,gBAAgB;MAC5BM,QAAQ,EAAE7M,MAAM,CAACkK,MAAM,CAAC;QACtB2C,QAAQ,EAAED;MACZ,CAAC,EAAEL,gBAAgB,CAAC;MACpBL,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpC,WAAW,CAAC,CAAC;EACjB,IAAItG,KAAK,EAAE;IACTwH,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAACxJ,kBAAkB,CAACiK,QAAQ,EAAE;MACxEC,KAAK,EAAEG;IACT,CAAC,EAAEd,SAAS,CAAC;EACf;EACA;EACA,IAAIR,YAAY,CAACxJ,OAAO,EAAE;IACxBgK,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAAC/J,cAAc,CAACwK,QAAQ,EAAE;MACpEC,KAAK,EAAEnB,YAAY,CAACxJ;IACtB,CAAC,EAAEgK,SAAS,CAAC;EACf;EACA;EACA,IAAIjF,iBAAiB,KAAK7B,SAAS,EAAE;IACnC8G,SAAS,GAAG,aAAavK,KAAK,CAACwK,aAAa,CAAChJ,uBAAuB,EAAE;MACpE6K,QAAQ,EAAE/G;IACZ,CAAC,EAAEiF,SAAS,CAAC;EACf;EACA,OAAO,aAAavK,KAAK,CAACwK,aAAa,CAACrJ,aAAa,CAAC8J,QAAQ,EAAE;IAC9DC,KAAK,EAAEnB;EACT,CAAC,EAAEQ,SAAS,CAAC;AACf,CAAC;AACD,MAAM+B,cAAc,GAAGjJ,KAAK,IAAI;EAC9B,MAAMkJ,OAAO,GAAGvM,KAAK,CAACqK,UAAU,CAAClJ,aAAa,CAAC;EAC/C,MAAMqL,SAAS,GAAGxM,KAAK,CAACqK,UAAU,CAACxJ,aAAa,CAAC;EACjD,OAAO,aAAab,KAAK,CAACwK,aAAa,CAACtG,gBAAgB,EAAE3E,MAAM,CAACkK,MAAM,CAAC;IACtErE,aAAa,EAAEmH,OAAO;IACtBpH,YAAY,EAAEqH;EAChB,CAAC,EAAEnJ,KAAK,CAAC,CAAC;AACZ,CAAC;AACDiJ,cAAc,CAACnL,aAAa,GAAGA,aAAa;AAC5CmL,cAAc,CAACzK,WAAW,GAAGA,WAAW;AACxCyK,cAAc,CAAC9C,MAAM,GAAGpG,eAAe;AACvCkJ,cAAc,CAAC7K,SAAS,GAAGA,SAAS;AACpClC,MAAM,CAACkN,cAAc,CAACH,cAAc,EAAE,aAAa,EAAE;EACnDI,GAAG,EAAEA,CAAA,KAAM;IACTxK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,0GAA0G,CAAC,GAAG,KAAK,CAAC;IAC7L,OAAOsB,WAAW;EACpB;AACF,CAAC,CAAC;AACF,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCkK,cAAc,CAACK,WAAW,GAAG,gBAAgB;AAC/C;AACA,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
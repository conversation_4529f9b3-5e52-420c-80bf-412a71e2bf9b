{"ast": null, "code": "function getScroll(w) {\n  var ret = w.pageXOffset;\n  var method = 'scrollLeft';\n  if (typeof ret !== 'number') {\n    var d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\nfunction getClientPosition(elem) {\n  var x;\n  var y;\n  var doc = elem.ownerDocument;\n  var body = doc.body;\n  var docElem = doc && doc.documentElement;\n  var box = elem.getBoundingClientRect();\n  x = box.left;\n  y = box.top;\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n  return {\n    left: x,\n    top: y\n  };\n}\nexport function getOffsetLeft(el) {\n  var pos = getClientPosition(el);\n  var doc = el.ownerDocument;\n  // Only IE use `parentWindow`\n  var w = doc.defaultView || doc.parentWindow;\n  pos.left += getScroll(w);\n  return pos.left;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
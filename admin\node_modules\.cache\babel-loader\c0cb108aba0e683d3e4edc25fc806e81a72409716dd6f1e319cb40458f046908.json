{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useContext, useEffect, useRef, useState } from 'react';\nimport BarsOutlined from \"@ant-design/icons/es/icons/BarsOutlined\";\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { addMediaQueryListener, removeMediaQueryListener } from '../_util/mediaQueryUtil';\nimport { ConfigContext } from '../config-provider';\nimport { LayoutContext } from './context';\nimport useStyle from './style/sider';\nconst dimensionMaxMap = {\n  xs: '479.98px',\n  sm: '575.98px',\n  md: '767.98px',\n  lg: '991.98px',\n  xl: '1199.98px',\n  xxl: '1599.98px'\n};\nconst isNumeric = value => !Number.isNaN(Number.parseFloat(value)) && isFinite(value);\nexport const SiderContext = /*#__PURE__*/React.createContext({});\nconst generateId = (() => {\n  let i = 0;\n  return function () {\n    let prefix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    i += 1;\n    return \"\".concat(prefix).concat(i);\n  };\n})();\nconst Sider = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      trigger,\n      children,\n      defaultCollapsed = false,\n      theme = 'dark',\n      style = {},\n      collapsible = false,\n      reverseArrow = false,\n      width = 200,\n      collapsedWidth = 80,\n      zeroWidthTriggerStyle,\n      breakpoint,\n      onCollapse,\n      onBreakpoint\n    } = props,\n    otherProps = __rest(props, [\"prefixCls\", \"className\", \"trigger\", \"children\", \"defaultCollapsed\", \"theme\", \"style\", \"collapsible\", \"reverseArrow\", \"width\", \"collapsedWidth\", \"zeroWidthTriggerStyle\", \"breakpoint\", \"onCollapse\", \"onBreakpoint\"]);\n  const {\n    siderHook\n  } = useContext(LayoutContext);\n  const [collapsed, setCollapsed] = useState('collapsed' in props ? props.collapsed : defaultCollapsed);\n  const [below, setBelow] = useState(false);\n  useEffect(() => {\n    if ('collapsed' in props) {\n      setCollapsed(props.collapsed);\n    }\n  }, [props.collapsed]);\n  const handleSetCollapsed = (value, type) => {\n    if (!('collapsed' in props)) {\n      setCollapsed(value);\n    }\n    onCollapse === null || onCollapse === void 0 ? void 0 : onCollapse(value, type);\n  };\n  // =========================== Prefix ===========================\n  const {\n    getPrefixCls,\n    direction\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('layout-sider', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // ========================= Responsive =========================\n  const responsiveHandlerRef = useRef(null);\n  responsiveHandlerRef.current = mql => {\n    setBelow(mql.matches);\n    onBreakpoint === null || onBreakpoint === void 0 ? void 0 : onBreakpoint(mql.matches);\n    if (collapsed !== mql.matches) {\n      handleSetCollapsed(mql.matches, 'responsive');\n    }\n  };\n  useEffect(() => {\n    function responsiveHandler(mql) {\n      var _a;\n      return (_a = responsiveHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(responsiveHandlerRef, mql);\n    }\n    let mql;\n    if (typeof (window === null || window === void 0 ? void 0 : window.matchMedia) !== 'undefined' && breakpoint && breakpoint in dimensionMaxMap) {\n      mql = window.matchMedia(\"screen and (max-width: \".concat(dimensionMaxMap[breakpoint], \")\"));\n      addMediaQueryListener(mql, responsiveHandler);\n      responsiveHandler(mql);\n    }\n    return () => {\n      removeMediaQueryListener(mql, responsiveHandler);\n    };\n  }, [breakpoint]); // in order to accept dynamic 'breakpoint' property, we need to add 'breakpoint' into dependency array.\n  useEffect(() => {\n    const uniqueId = generateId('ant-sider-');\n    siderHook.addSider(uniqueId);\n    return () => siderHook.removeSider(uniqueId);\n  }, []);\n  const toggle = () => {\n    handleSetCollapsed(!collapsed, 'clickTrigger');\n  };\n  const divProps = omit(otherProps, ['collapsed']);\n  const rawWidth = collapsed ? collapsedWidth : width;\n  // use \"px\" as fallback unit for width\n  const siderWidth = isNumeric(rawWidth) ? \"\".concat(rawWidth, \"px\") : String(rawWidth);\n  // special trigger when collapsedWidth == 0\n  const zeroWidthTrigger = parseFloat(String(collapsedWidth || 0)) === 0 ? (/*#__PURE__*/React.createElement(\"span\", {\n    onClick: toggle,\n    className: classNames(\"\".concat(prefixCls, \"-zero-width-trigger\"), \"\".concat(prefixCls, \"-zero-width-trigger-\").concat(reverseArrow ? 'right' : 'left')),\n    style: zeroWidthTriggerStyle\n  }, trigger || /*#__PURE__*/React.createElement(BarsOutlined, null))) : null;\n  const reverseIcon = direction === 'rtl' === !reverseArrow;\n  const iconObj = {\n    expanded: reverseIcon ? /*#__PURE__*/React.createElement(RightOutlined, null) : /*#__PURE__*/React.createElement(LeftOutlined, null),\n    collapsed: reverseIcon ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null)\n  };\n  const status = collapsed ? 'collapsed' : 'expanded';\n  const defaultTrigger = iconObj[status];\n  const triggerDom = trigger !== null ? zeroWidthTrigger || (/*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-trigger\"),\n    onClick: toggle,\n    style: {\n      width: siderWidth\n    }\n  }, trigger || defaultTrigger)) : null;\n  const divStyle = Object.assign(Object.assign({}, style), {\n    flex: \"0 0 \".concat(siderWidth),\n    maxWidth: siderWidth,\n    minWidth: siderWidth,\n    width: siderWidth\n  });\n  const siderCls = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(theme), {\n    [\"\".concat(prefixCls, \"-collapsed\")]: !!collapsed,\n    [\"\".concat(prefixCls, \"-has-trigger\")]: collapsible && trigger !== null && !zeroWidthTrigger,\n    [\"\".concat(prefixCls, \"-below\")]: !!below,\n    [\"\".concat(prefixCls, \"-zero-width\")]: parseFloat(siderWidth) === 0\n  }, className, hashId, cssVarCls);\n  const contextValue = React.useMemo(() => ({\n    siderCollapsed: collapsed\n  }), [collapsed]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(SiderContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(\"aside\", Object.assign({\n    className: siderCls\n  }, divProps, {\n    style: divStyle,\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-children\")\n  }, children), collapsible || below && zeroWidthTrigger ? triggerDom : null)));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Sider.displayName = 'Sider';\n}\nexport default Sider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { isSame } from \"../utils/dateUtil\";\n/**\n * Toggles the presence of a value in an array.\n * If the value exists in the array, removed it.\n * Else add it.\n */\nexport default function useToggleDates(generateConfig, locale, panelMode) {\n  function toggleDates(list, target) {\n    var index = list.findIndex(function (date) {\n      return isSame(generateConfig, locale, date, target, panelMode);\n    });\n    if (index === -1) {\n      return [].concat(_toConsumableArray(list), [target]);\n    }\n    var sliceList = _toConsumableArray(list);\n    sliceList.splice(index, 1);\n    return sliceList;\n  }\n  return toggleDates;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "isSame", "useToggleDates", "generateConfig", "locale", "panelMode", "toggleDates", "list", "target", "index", "findIndex", "date", "concat", "sliceList", "splice"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-picker/es/hooks/useToggleDates.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { isSame } from \"../utils/dateUtil\";\n/**\n * Toggles the presence of a value in an array.\n * If the value exists in the array, removed it.\n * Else add it.\n */\nexport default function useToggleDates(generateConfig, locale, panelMode) {\n  function toggleDates(list, target) {\n    var index = list.findIndex(function (date) {\n      return isSame(generateConfig, locale, date, target, panelMode);\n    });\n    if (index === -1) {\n      return [].concat(_toConsumableArray(list), [target]);\n    }\n    var sliceList = _toConsumableArray(list);\n    sliceList.splice(index, 1);\n    return sliceList;\n  }\n  return toggleDates;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,SAASC,MAAM,QAAQ,mBAAmB;AAC1C;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAE;EACxE,SAASC,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAE;IACjC,IAAIC,KAAK,GAAGF,IAAI,CAACG,SAAS,CAAC,UAAUC,IAAI,EAAE;MACzC,OAAOV,MAAM,CAACE,cAAc,EAAEC,MAAM,EAAEO,IAAI,EAAEH,MAAM,EAAEH,SAAS,CAAC;IAChE,CAAC,CAAC;IACF,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,EAAE,CAACG,MAAM,CAACZ,kBAAkB,CAACO,IAAI,CAAC,EAAE,CAACC,MAAM,CAAC,CAAC;IACtD;IACA,IAAIK,SAAS,GAAGb,kBAAkB,CAACO,IAAI,CAAC;IACxCM,SAAS,CAACC,MAAM,CAACL,KAAK,EAAE,CAAC,CAAC;IAC1B,OAAOI,SAAS;EAClB;EACA,OAAOP,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
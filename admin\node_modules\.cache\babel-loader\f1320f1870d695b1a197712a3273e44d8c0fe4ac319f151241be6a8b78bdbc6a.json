{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SettingFilledSvg from \"@ant-design/icons-svg/es/asn/SettingFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SettingFilled = function SettingFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SettingFilledSvg\n  }));\n};\n\n/**![setting](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMi41IDM5MC42Yy0yOS45IDAtNTcuOSAxMS42LTc5LjEgMzIuOC0yMS4xIDIxLjItMzIuOCA0OS4yLTMyLjggNzkuMSAwIDI5LjkgMTEuNyA1Ny45IDMyLjggNzkuMSAyMS4yIDIxLjEgNDkuMiAzMi44IDc5LjEgMzIuOCAyOS45IDAgNTcuOS0xMS43IDc5LjEtMzIuOCAyMS4xLTIxLjIgMzIuOC00OS4yIDMyLjgtNzkuMSAwLTI5LjktMTEuNy01Ny45LTMyLjgtNzkuMWExMTAuOTYgMTEwLjk2IDAgMDAtNzkuMS0zMi44em00MTIuMyAyMzUuNWwtNjUuNC01NS45YzMuMS0xOSA0LjctMzguNCA0LjctNTcuN3MtMS42LTM4LjgtNC43LTU3LjdsNjUuNC01NS45YTMyLjAzIDMyLjAzIDAgMDA5LjMtMzUuMmwtLjktMi42YTQ0Mi41IDQ0Mi41IDAgMDAtNzkuNi0xMzcuN2wtMS44LTIuMWEzMi4xMiAzMi4xMiAwIDAwLTM1LjEtOS41bC04MS4yIDI4LjljLTMwLTI0LjYtNjMuNC00NC05OS42LTU3LjVsLTE1LjctODQuOWEzMi4wNSAzMi4wNSAwIDAwLTI1LjgtMjUuN2wtMi43LS41Yy01Mi05LjQtMTA2LjgtOS40LTE1OC44IDBsLTIuNy41YTMyLjA1IDMyLjA1IDAgMDAtMjUuOCAyNS43bC0xNS44IDg1LjNhMzUzLjQ0IDM1My40NCAwIDAwLTk4LjkgNTcuM2wtODEuOC0yOS4xYTMyIDMyIDAgMDAtMzUuMSA5LjVsLTEuOCAyLjFhNDQ1LjkzIDQ0NS45MyAwIDAwLTc5LjYgMTM3LjdsLS45IDIuNmMtNC41IDEyLjUtLjggMjYuNSA5LjMgMzUuMmw2Ni4yIDU2LjVjLTMuMSAxOC44LTQuNiAzOC00LjYgNTcgMCAxOS4yIDEuNSAzOC40IDQuNiA1N2wtNjYgNTYuNWEzMi4wMyAzMi4wMyAwIDAwLTkuMyAzNS4ybC45IDIuNmMxOC4xIDUwLjMgNDQuOCA5Ni44IDc5LjYgMTM3LjdsMS44IDIuMWEzMi4xMiAzMi4xMiAwIDAwMzUuMSA5LjVsODEuOC0yOS4xYzI5LjggMjQuNSA2MyA0My45IDk4LjkgNTcuM2wxNS44IDg1LjNhMzIuMDUgMzIuMDUgMCAwMDI1LjggMjUuN2wyLjcuNWE0NDguMjcgNDQ4LjI3IDAgMDAxNTguOCAwbDIuNy0uNWEzMi4wNSAzMi4wNSAwIDAwMjUuOC0yNS43bDE1LjctODQuOWMzNi4yLTEzLjYgNjkuNi0zMi45IDk5LjYtNTcuNWw4MS4yIDI4LjlhMzIgMzIgMCAwMDM1LjEtOS41bDEuOC0yLjFjMzQuOC00MS4xIDYxLjUtODcuNCA3OS42LTEzNy43bC45LTIuNmM0LjMtMTIuNC42LTI2LjMtOS41LTM1em0tNDEyLjMgNTIuMmMtOTcuMSAwLTE3NS44LTc4LjctMTc1LjgtMTc1LjhzNzguNy0xNzUuOCAxNzUuOC0xNzUuOCAxNzUuOCA3OC43IDE3NS44IDE3NS44LTc4LjcgMTc1LjgtMTc1LjggMTc1Ljh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SettingFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SettingFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SettingFilledSvg", "AntdIcon", "SettingFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/SettingFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SettingFilledSvg from \"@ant-design/icons-svg/es/asn/SettingFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SettingFilled = function SettingFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SettingFilledSvg\n  }));\n};\n\n/**![setting](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMi41IDM5MC42Yy0yOS45IDAtNTcuOSAxMS42LTc5LjEgMzIuOC0yMS4xIDIxLjItMzIuOCA0OS4yLTMyLjggNzkuMSAwIDI5LjkgMTEuNyA1Ny45IDMyLjggNzkuMSAyMS4yIDIxLjEgNDkuMiAzMi44IDc5LjEgMzIuOCAyOS45IDAgNTcuOS0xMS43IDc5LjEtMzIuOCAyMS4xLTIxLjIgMzIuOC00OS4yIDMyLjgtNzkuMSAwLTI5LjktMTEuNy01Ny45LTMyLjgtNzkuMWExMTAuOTYgMTEwLjk2IDAgMDAtNzkuMS0zMi44em00MTIuMyAyMzUuNWwtNjUuNC01NS45YzMuMS0xOSA0LjctMzguNCA0LjctNTcuN3MtMS42LTM4LjgtNC43LTU3LjdsNjUuNC01NS45YTMyLjAzIDMyLjAzIDAgMDA5LjMtMzUuMmwtLjktMi42YTQ0Mi41IDQ0Mi41IDAgMDAtNzkuNi0xMzcuN2wtMS44LTIuMWEzMi4xMiAzMi4xMiAwIDAwLTM1LjEtOS41bC04MS4yIDI4LjljLTMwLTI0LjYtNjMuNC00NC05OS42LTU3LjVsLTE1LjctODQuOWEzMi4wNSAzMi4wNSAwIDAwLTI1LjgtMjUuN2wtMi43LS41Yy01Mi05LjQtMTA2LjgtOS40LTE1OC44IDBsLTIuNy41YTMyLjA1IDMyLjA1IDAgMDAtMjUuOCAyNS43bC0xNS44IDg1LjNhMzUzLjQ0IDM1My40NCAwIDAwLTk4LjkgNTcuM2wtODEuOC0yOS4xYTMyIDMyIDAgMDAtMzUuMSA5LjVsLTEuOCAyLjFhNDQ1LjkzIDQ0NS45MyAwIDAwLTc5LjYgMTM3LjdsLS45IDIuNmMtNC41IDEyLjUtLjggMjYuNSA5LjMgMzUuMmw2Ni4yIDU2LjVjLTMuMSAxOC44LTQuNiAzOC00LjYgNTcgMCAxOS4yIDEuNSAzOC40IDQuNiA1N2wtNjYgNTYuNWEzMi4wMyAzMi4wMyAwIDAwLTkuMyAzNS4ybC45IDIuNmMxOC4xIDUwLjMgNDQuOCA5Ni44IDc5LjYgMTM3LjdsMS44IDIuMWEzMi4xMiAzMi4xMiAwIDAwMzUuMSA5LjVsODEuOC0yOS4xYzI5LjggMjQuNSA2MyA0My45IDk4LjkgNTcuM2wxNS44IDg1LjNhMzIuMDUgMzIuMDUgMCAwMDI1LjggMjUuN2wyLjcuNWE0NDguMjcgNDQ4LjI3IDAgMDAxNTguOCAwbDIuNy0uNWEzMi4wNSAzMi4wNSAwIDAwMjUuOC0yNS43bDE1LjctODQuOWMzNi4yLTEzLjYgNjkuNi0zMi45IDk5LjYtNTcuNWw4MS4yIDI4LjlhMzIgMzIgMCAwMDM1LjEtOS41bDEuOC0yLjFjMzQuOC00MS4xIDYxLjUtODcuNCA3OS42LTEzNy43bC45LTIuNmM0LjMtMTIuNC42LTI2LjMtOS41LTM1em0tNDEyLjMgNTIuMmMtOTcuMSAwLTE3NS44LTc4LjctMTc1LjgtMTc1LjhzNzguNy0xNzUuOCAxNzUuOC0xNzUuOCAxNzUuOCA3OC43IDE3NS44IDE3NS44LTc4LjcgMTc1LjgtMTc1LjggMTc1Ljh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SettingFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SettingFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
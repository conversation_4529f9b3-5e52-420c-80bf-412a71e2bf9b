{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\components\\\\ProductForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Form, Input, InputNumber, Select, Switch, Button, Row, Col, Card, Space, message } from 'antd';\n\n// 图标导入（预留用于未来功能）\n// import {\n//   UploadOutlined,\n//   InfoCircleOutlined,\n//   CopyOutlined,\n//   SaveOutlined,\n// } from '@ant-design/icons';\n\n// import { productApi } from '../services/api'; // 预留用于API调用\n\nimport { OPERATOR_OPTIONS, LOCATION_OPTIONS, PRODUCT_STATUS_OPTIONS } from '../utils/constants';\nimport { arrayToString, copyToClipboard } from '../utils/helpers';\n\n// 分类接口定义\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 临时常量定义\n\nconst PRODUCT_WEIGHT_OPTIONS = [{\n  label: '1',\n  value: 1\n}, {\n  label: '2',\n  value: 2\n}, {\n  label: '3',\n  value: 3\n}, {\n  label: '4',\n  value: 4\n}, {\n  label: '5',\n  value: 5\n}, {\n  label: '6',\n  value: 6\n}, {\n  label: '7',\n  value: 7\n}, {\n  label: '8',\n  value: 8\n}, {\n  label: '9',\n  value: 9\n}, {\n  label: '10',\n  value: 10\n}];\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst ProductForm = ({\n  form,\n  initialValues,\n  onSubmit,\n  loading = false\n}) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState('');\n  const [uploading, setUploading] = useState(false);\n  const [categories, setCategories] = useState([]);\n\n  // 获取分类数据\n  const loadCategories = () => {\n    try {\n      const savedCategories = localStorage.getItem('categories');\n      if (savedCategories) {\n        const categoriesData = JSON.parse(savedCategories);\n        // 只显示激活状态的分类\n        const activeCategories = categoriesData.filter(cat => cat.status === 'active');\n        setCategories(activeCategories);\n      }\n    } catch (error) {\n      console.error('加载分类数据失败:', error);\n    }\n  };\n\n  // 组件挂载时加载分类数据\n  useEffect(() => {\n    loadCategories();\n\n    // 监听localStorage变化，实时更新分类数据\n    const handleStorageChange = e => {\n      if (e.key === 'categories') {\n        loadCategories();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n\n    // 也监听自定义事件，用于同一页面内的更新\n    const handleCategoriesUpdate = () => {\n      loadCategories();\n    };\n    window.addEventListener('categoriesUpdated', handleCategoriesUpdate);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('categoriesUpdated', handleCategoriesUpdate);\n    };\n  }, []);\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (initialValues) {\n      const formData = {\n        ...initialValues,\n        monthly_fee: parseFloat(initialValues.monthly_fee),\n        general_traffic: parseFloat(initialValues.general_traffic),\n        directional_traffic: parseFloat(initialValues.directional_traffic),\n        tag1: initialValues.tags.tag1,\n        tag2: initialValues.tags.tag2,\n        tag3: initialValues.tags.tag3\n      };\n      form.setFieldsValue(formData);\n      setImageUrl(initialValues.main_image || '');\n    }\n  }, [initialValues, form]);\n\n  // 表单提交处理\n  const handleSubmit = async values => {\n    try {\n      const submitData = {\n        ...values,\n        main_image: values.main_image,\n        // 直接使用表单中的值\n        no_delivery_areas: Array.isArray(values.no_delivery_areas) ? arrayToString(values.no_delivery_areas) : values.no_delivery_areas\n      };\n      await onSubmit(submitData);\n    } catch (error) {\n      console.error('表单提交失败:', error);\n    }\n  };\n\n  // 复制产品信息\n  const handleCopyProduct = async () => {\n    const values = form.getFieldsValue();\n    const productInfo = JSON.stringify(values, null, 2);\n    const success = await copyToClipboard(productInfo);\n    if (success) {\n      message.success('产品信息已复制到剪贴板');\n    } else {\n      message.error('复制失败');\n    }\n  };\n\n  // 保存草稿\n  const handleSaveDraft = () => {\n    const values = form.getFieldsValue();\n    localStorage.setItem('product_draft', JSON.stringify(values));\n    message.success('草稿已保存');\n  };\n\n  // 加载草稿\n  const handleLoadDraft = () => {\n    const draft = localStorage.getItem('product_draft');\n    if (draft) {\n      try {\n        const draftData = JSON.parse(draft);\n        form.setFieldsValue(draftData);\n        message.success('草稿已加载');\n      } catch (error) {\n        message.error('草稿格式错误');\n      }\n    } else {\n      message.info('没有找到草稿');\n    }\n  };\n\n  // 图片上传处理\n  const handleImageUpload = file => {\n    // 这里应该实现真实的图片上传逻辑\n    // 目前只是模拟\n    setUploading(true);\n    setTimeout(() => {\n      const mockUrl = URL.createObjectURL(file);\n      setImageUrl(mockUrl);\n      form.setFieldsValue({\n        main_image: mockUrl\n      });\n      setUploading(false);\n      message.success('图片上传成功');\n    }, 1000);\n    return false; // 阻止默认上传行为\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    layout: \"vertical\",\n    onFinish: handleSubmit,\n    initialValues: {\n      status: 'online',\n      need_id_photo: true,\n      sms_notification: false\n    },\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"name\",\n                label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n                rules: [{\n                  required: true,\n                  message: '请输入产品名称'\n                }, {\n                  max: 255,\n                  message: '产品名称不能超过255个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u540D\\u79F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"status\",\n                label: \"\\u4EA7\\u54C1\\u72B6\\u6001\",\n                rules: [{\n                  required: true,\n                  message: '请选择产品状态'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u72B6\\u6001\",\n                  children: PRODUCT_STATUS_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"description\",\n                label: \"\\u4EA7\\u54C1\\u4ECB\\u7ECD\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 3,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u4ECB\\u7ECD\",\n                  maxLength: 500,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"discount_details\",\n                label: \"\\u6FC0\\u6D3B\\u6D41\\u7A0B\\u56FE\",\n                tooltip: \"\\u8BF7\\u8F93\\u5165\\u6FC0\\u6D3B\\u6D41\\u7A0B\\u56FE\\u7247\\u94FE\\u63A5URL\\uFF0C\\u652F\\u6301JPG\\u3001PNG\\u3001GIF\\u7B49\\u683C\\u5F0F\",\n                rules: [{\n                  type: 'url',\n                  message: '请输入有效的激活流程图链接URL'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6FC0\\u6D3B\\u6D41\\u7A0B\\u56FE\\u7247\\u94FE\\u63A5\\uFF0C\\u5982\\uFF1Ahttps://example.com/activation-flow.jpg\",\n                  maxLength: 500,\n                  showCount: true,\n                  onChange: e => {\n                    const url = e.target.value;\n                    if (url && (url.startsWith('http://') || url.startsWith('https://'))) {\n                      // 可以在这里添加图片预览逻辑\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                shouldUpdate: true,\n                children: ({\n                  getFieldValue\n                }) => {\n                  const imageUrl = getFieldValue('discount_details');\n                  if (imageUrl && (imageUrl.startsWith('http://') || imageUrl.startsWith('https://'))) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: 8,\n                          color: '#666',\n                          fontSize: '14px'\n                        },\n                        children: \"\\u6FC0\\u6D3B\\u6D41\\u7A0B\\u56FE\\u9884\\u89C8\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 297,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: imageUrl,\n                        alt: \"\\u6FC0\\u6D3B\\u6D41\\u7A0B\\u56FE\\u9884\\u89C8\",\n                        style: {\n                          maxWidth: '100%',\n                          maxHeight: '200px',\n                          border: '1px solid #d9d9d9',\n                          borderRadius: '6px',\n                          objectFit: 'contain'\n                        },\n                        onError: e => {\n                          e.currentTarget.style.display = 'none';\n                        },\n                        onLoad: e => {\n                          e.currentTarget.style.display = 'block';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 25\n                    }, this);\n                  }\n                  return null;\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"delivery_method\",\n                label: \"\\u5FEB\\u9012\\u65B9\\u5F0F\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5FEB\\u9012\\u65B9\\u5F0F\\uFF0C\\u5982\\uFF1A\\u987A\\u4E30\\u5FEB\\u9012\\u3001\\u5706\\u901A\\u5FEB\\u9012\\u7B49\",\n                  maxLength: 100,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"activation_method\",\n                label: \"\\u6FC0\\u6D3B\\u65B9\\u5F0F\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6FC0\\u6D3B\\u65B9\\u5F0F\\uFF0C\\u5982\\uFF1A\\u5728\\u7EBF\\u6FC0\\u6D3B\\u3001APP\\u6FC0\\u6D3B\\u7B49\",\n                  maxLength: 100,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"recharge_link\",\n                label: \"\\u5145\\u503C\\u94FE\\u63A5\",\n                rules: [{\n                  type: 'url',\n                  message: '请输入有效的充值链接URL'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5145\\u503C\\u94FE\\u63A5\\uFF0C\\u5982\\uFF1Ahttps://example.com/recharge\",\n                  maxLength: 200,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"card_age_requirement\",\n                label: \"\\u529E\\u5361\\u5E74\\u9F84\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u529E\\u5361\\u5E74\\u9F84\\u8981\\u6C42\\uFF0C\\u5982\\uFF1A18-65\\u5C81\",\n                  maxLength: 50\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"no_delivery_areas\",\n                label: \"\\u4E0D\\u53D1\\u8D27\\u5730\\u533A\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  mode: \"tags\",\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4E0D\\u53D1\\u8D27\\u5730\\u533A\\uFF0C\\u652F\\u6301\\u591A\\u4E2A\",\n                  style: {\n                    width: '100%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"directional_scope\",\n                label: \"\\u5B9A\\u5411\\u8303\\u56F4\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 2,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B9A\\u5411\\u6D41\\u91CF\\u9002\\u7528\\u8303\\u56F4\\uFF0C\\u5982\\uFF1A\\u6296\\u97F3\\u3001\\u5FEB\\u624B\\u3001\\u5FAE\\u4FE1\\u7B49\",\n                  maxLength: 200,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"reactivation_cancellation\",\n                label: \"\\u590D\\u673A\\u53CA\\u6CE8\\u9500\\u65B9\\u5F0F\",\n                children: /*#__PURE__*/_jsxDEV(TextArea, {\n                  rows: 2,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u590D\\u673A\\u53CA\\u6CE8\\u9500\\u65B9\\u5F0F\\u8BF4\\u660E\",\n                  maxLength: 200,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5957\\u9910\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"monthly_fee\",\n                label: \"\\u5957\\u9910\\u6708\\u79DF\\uFF08\\u5143\\uFF09\",\n                rules: [{\n                  required: true,\n                  message: '请输入套餐月租'\n                }, {\n                  type: 'number',\n                  min: 0,\n                  message: '月租不能小于0'\n                }],\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6708\\u79DF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"general_traffic\",\n                label: \"\\u901A\\u7528\\u6D41\\u91CF\\uFF08G\\uFF09\",\n                rules: [{\n                  required: true,\n                  message: '请输入通用流量'\n                }, {\n                  type: 'number',\n                  min: 0,\n                  message: '流量不能小于0'\n                }],\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u901A\\u7528\\u6D41\\u91CF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"directional_traffic\",\n                label: \"\\u5B9A\\u5411\\u6D41\\u91CF\\uFF08G\\uFF09\",\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B9A\\u5411\\u6D41\\u91CF\",\n                  precision: 2,\n                  min: 0,\n                  max: 9999.99\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"free_minutes\",\n                label: \"\\u514D\\u8D39\\u901A\\u8BDD\\uFF08\\u5206\\u949F\\uFF09\",\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  style: {\n                    width: '100%'\n                  },\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u514D\\u8D39\\u901A\\u8BDD\\u65F6\\u957F\",\n                  min: 0,\n                  max: 99999\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"package_duration\",\n                label: \"\\u5957\\u9910\\u65F6\\u957F\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5957\\u9910\\u65F6\\u957F\\uFF0C\\u5982\\uFF1A1\\u4E2A\\u6708\\u30013\\u4E2A\\u6708\\u300112\\u4E2A\\u6708\\u7B49\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"product_weight\",\n                label: \"\\u4EA7\\u54C1\\u6743\\u91CD\",\n                tooltip: \"\\u6743\\u91CD\\u8D8A\\u9AD8\\uFF0C\\u4EA7\\u54C1\\u5728\\u5217\\u8868\\u4E2D\\u7684\\u6392\\u5E8F\\u8D8A\\u9760\\u524D\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u6743\\u91CD\",\n                  children: PRODUCT_WEIGHT_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8FD0\\u8425\\u5546\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"category_id\",\n                label: \"\\u4EA7\\u54C1\\u5206\\u7C7B\",\n                rules: [{\n                  required: true,\n                  message: '请选择产品分类'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u5206\\u7C7B\",\n                  showSearch: true,\n                  filterOption: (input, option) => {\n                    var _option$children;\n                    return option === null || option === void 0 ? void 0 : (_option$children = option.children) === null || _option$children === void 0 ? void 0 : _option$children.toLowerCase().includes(input.toLowerCase());\n                  },\n                  children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n                    value: category.id,\n                    children: category.name\n                  }, category.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"operator\",\n                label: \"\\u8FD0\\u8425\\u5546\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n                  children: OPERATOR_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"location\",\n                label: \"\\u5F52\\u5C5E\\u5730\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F52\\u5C5E\\u5730\",\n                  children: LOCATION_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5546\\u54C1\\u6807\\u7B7E\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag1\",\n                label: \"\\u6807\\u7B7E1\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E1\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag2\",\n                label: \"\\u6807\\u7B7E2\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E2\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tag3\",\n                label: \"\\u6807\\u7B7E3\",\n                rules: [{\n                  max: 6,\n                  message: '标签不能超过6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u7B7E3\",\n                  maxLength: 6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5176\\u4ED6\\u8BBE\\u7F6E\",\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"need_id_photo\",\n                label: \"\\u9700\\u8981\\u4E0A\\u4F20\\u8EAB\\u4EFD\\u8BC1\\u7167\\u7247\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"sms_notification\",\n                label: \"\\u5F00\\u542F\\u7528\\u6237\\u4E0B\\u5355\\u6210\\u529F\\u77ED\\u4FE1\\u63D0\\u9192\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"main_image\",\n                label: \"\\u4EA7\\u54C1\\u4E3B\\u56FE\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u4E3B\\u56FEURL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"detail_description\",\n                label: \"\\u5546\\u54C1\\u8BE6\\u60C5\\u56FE\",\n                tooltip: \"\\u8BF7\\u8F93\\u5165\\u56FE\\u7247\\u94FE\\u63A5URL\\uFF0C\\u652F\\u6301JPG\\u3001PNG\\u3001GIF\\u7B49\\u683C\\u5F0F\",\n                rules: [{\n                  type: 'url',\n                  message: '请输入有效的图片链接URL'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u8BE6\\u60C5\\u56FE\\u7247\\u94FE\\u63A5\\uFF0C\\u5982\\uFF1Ahttps://example.com/image.jpg\",\n                  maxLength: 500,\n                  showCount: true,\n                  onChange: e => {\n                    const url = e.target.value;\n                    if (url && (url.startsWith('http://') || url.startsWith('https://'))) {\n                      // 可以在这里添加图片预览逻辑\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                shouldUpdate: true,\n                children: ({\n                  getFieldValue\n                }) => {\n                  const imageUrl = getFieldValue('detail_description');\n                  if (imageUrl && (imageUrl.startsWith('http://') || imageUrl.startsWith('https://'))) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: 8,\n                          color: '#666',\n                          fontSize: '14px'\n                        },\n                        children: \"\\u56FE\\u7247\\u9884\\u89C8\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: imageUrl,\n                        alt: \"\\u5546\\u54C1\\u8BE6\\u60C5\\u56FE\\u9884\\u89C8\",\n                        style: {\n                          maxWidth: '100%',\n                          maxHeight: '300px',\n                          border: '1px solid #d9d9d9',\n                          borderRadius: '6px',\n                          objectFit: 'contain'\n                        },\n                        onError: e => {\n                          e.currentTarget.style.display = 'none';\n                        },\n                        onLoad: e => {\n                          e.currentTarget.style.display = 'block';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 25\n                    }, this);\n                  }\n                  return null;\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              size: \"large\",\n              children: initialValues ? '更新产品' : '创建产品'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"large\",\n              onClick: () => form.resetFields(),\n              children: \"\\u91CD\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductForm, \"tQ0p/TMYRMYuI2d9fAqcD5HS9Bw=\");\n_c = ProductForm;\nexport default ProductForm;\nvar _c;\n$RefreshReg$(_c, \"ProductForm\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Form", "Input", "InputNumber", "Select", "Switch", "<PERSON><PERSON>", "Row", "Col", "Card", "Space", "message", "OPERATOR_OPTIONS", "LOCATION_OPTIONS", "PRODUCT_STATUS_OPTIONS", "arrayToString", "copyToClipboard", "jsxDEV", "_jsxDEV", "PRODUCT_WEIGHT_OPTIONS", "label", "value", "Option", "TextArea", "ProductForm", "form", "initialValues", "onSubmit", "loading", "_s", "imageUrl", "setImageUrl", "uploading", "setUploading", "categories", "setCategories", "loadCategories", "savedCategories", "localStorage", "getItem", "categoriesData", "JSON", "parse", "activeCategories", "filter", "cat", "status", "error", "console", "handleStorageChange", "e", "key", "window", "addEventListener", "handleCategoriesUpdate", "removeEventListener", "formData", "monthly_fee", "parseFloat", "general_traffic", "directional_traffic", "tag1", "tags", "tag2", "tag3", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "main_image", "handleSubmit", "values", "submitData", "no_delivery_areas", "Array", "isArray", "handleCopyProduct", "getFieldsValue", "productInfo", "stringify", "success", "handleSaveDraft", "setItem", "handleLoadDraft", "draft", "draftData", "info", "handleImageUpload", "file", "setTimeout", "mockUrl", "URL", "createObjectURL", "layout", "onFinish", "need_id_photo", "sms_notification", "children", "gutter", "span", "title", "style", "marginBottom", "<PERSON><PERSON>", "name", "rules", "required", "max", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "option", "rows", "max<PERSON><PERSON><PERSON>", "showCount", "tooltip", "type", "onChange", "url", "target", "startsWith", "shouldUpdate", "getFieldValue", "marginTop", "color", "fontSize", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "border", "borderRadius", "objectFit", "onError", "currentTarget", "display", "onLoad", "mode", "width", "min", "precision", "showSearch", "filterOption", "input", "_option$children", "toLowerCase", "includes", "category", "id", "valuePropName", "htmlType", "size", "onClick", "resetFields", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/components/ProductForm.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport {\n  Form,\n  Input,\n  InputNumber,\n  Select,\n  Switch,\n  Button,\n  Row,\n  Col,\n  Card,\n  Space,\n  message,\n} from 'antd';\nimport RichTextEditor from './RichTextEditor';\n// 图标导入（预留用于未来功能）\n// import {\n//   UploadOutlined,\n//   InfoCircleOutlined,\n//   CopyOutlined,\n//   SaveOutlined,\n// } from '@ant-design/icons';\nimport type { FormInstance } from 'antd/es/form';\nimport type { UploadFile } from 'antd/es/upload/interface';\n// import { productApi } from '../services/api'; // 预留用于API调用\nimport type { Product, ProductRequest } from '../types/product';\nimport {\n  OPERATOR_OPTIONS,\n  LOCATION_OPTIONS,\n  PRODUCT_STATUS_OPTIONS,\n} from '../utils/constants';\nimport { arrayToString, copyToClipboard } from '../utils/helpers';\n\n// 分类接口定义\ninterface Category {\n  id: number;\n  name: string;\n  description: string;\n  productCount: number;\n  status: 'active' | 'inactive';\n  createdAt: string;\n}\n\n// 临时常量定义\n\n\n\nconst PRODUCT_WEIGHT_OPTIONS = [\n  { label: '1', value: 1 },\n  { label: '2', value: 2 },\n  { label: '3', value: 3 },\n  { label: '4', value: 4 },\n  { label: '5', value: 5 },\n  { label: '6', value: 6 },\n  { label: '7', value: 7 },\n  { label: '8', value: 8 },\n  { label: '9', value: 9 },\n  { label: '10', value: 10 },\n];\n\nconst { Option } = Select;\nconst { TextArea } = Input;\n\ninterface ProductFormProps {\n  form: FormInstance;\n  initialValues?: Product;\n  onSubmit: (values: ProductRequest) => Promise<void>;\n  loading?: boolean;\n}\n\nconst ProductForm: React.FC<ProductFormProps> = ({\n  form,\n  initialValues,\n  onSubmit,\n  loading = false,\n}) => {\n  const [imageUrl, setImageUrl] = useState<string>('');\n  const [uploading, setUploading] = useState(false);\n  const [categories, setCategories] = useState<Category[]>([]);\n\n  // 获取分类数据\n  const loadCategories = () => {\n    try {\n      const savedCategories = localStorage.getItem('categories');\n      if (savedCategories) {\n        const categoriesData = JSON.parse(savedCategories);\n        // 只显示激活状态的分类\n        const activeCategories = categoriesData.filter((cat: Category) => cat.status === 'active');\n        setCategories(activeCategories);\n      }\n    } catch (error) {\n      console.error('加载分类数据失败:', error);\n    }\n  };\n\n  // 组件挂载时加载分类数据\n  useEffect(() => {\n    loadCategories();\n\n    // 监听localStorage变化，实时更新分类数据\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'categories') {\n        loadCategories();\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n\n    // 也监听自定义事件，用于同一页面内的更新\n    const handleCategoriesUpdate = () => {\n      loadCategories();\n    };\n\n    window.addEventListener('categoriesUpdated', handleCategoriesUpdate);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('categoriesUpdated', handleCategoriesUpdate);\n    };\n  }, []);\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (initialValues) {\n      const formData = {\n        ...initialValues,\n        monthly_fee: parseFloat(initialValues.monthly_fee),\n        general_traffic: parseFloat(initialValues.general_traffic),\n        directional_traffic: parseFloat(initialValues.directional_traffic),\n        tag1: initialValues.tags.tag1,\n        tag2: initialValues.tags.tag2,\n        tag3: initialValues.tags.tag3,\n      };\n      form.setFieldsValue(formData);\n      setImageUrl(initialValues.main_image || '');\n    }\n  }, [initialValues, form]);\n\n  // 表单提交处理\n  const handleSubmit = async (values: any) => {\n    try {\n      const submitData: ProductRequest = {\n        ...values,\n        main_image: values.main_image, // 直接使用表单中的值\n        no_delivery_areas: Array.isArray(values.no_delivery_areas)\n          ? arrayToString(values.no_delivery_areas)\n          : values.no_delivery_areas,\n      };\n\n      await onSubmit(submitData);\n    } catch (error) {\n      console.error('表单提交失败:', error);\n    }\n  };\n\n  // 复制产品信息\n  const handleCopyProduct = async () => {\n    const values = form.getFieldsValue();\n    const productInfo = JSON.stringify(values, null, 2);\n    const success = await copyToClipboard(productInfo);\n    if (success) {\n      message.success('产品信息已复制到剪贴板');\n    } else {\n      message.error('复制失败');\n    }\n  };\n\n  // 保存草稿\n  const handleSaveDraft = () => {\n    const values = form.getFieldsValue();\n    localStorage.setItem('product_draft', JSON.stringify(values));\n    message.success('草稿已保存');\n  };\n\n  // 加载草稿\n  const handleLoadDraft = () => {\n    const draft = localStorage.getItem('product_draft');\n    if (draft) {\n      try {\n        const draftData = JSON.parse(draft);\n        form.setFieldsValue(draftData);\n        message.success('草稿已加载');\n      } catch (error) {\n        message.error('草稿格式错误');\n      }\n    } else {\n      message.info('没有找到草稿');\n    }\n  };\n\n  // 图片上传处理\n  const handleImageUpload = (file: UploadFile) => {\n    // 这里应该实现真实的图片上传逻辑\n    // 目前只是模拟\n    setUploading(true);\n    setTimeout(() => {\n      const mockUrl = URL.createObjectURL(file as any);\n      setImageUrl(mockUrl);\n      form.setFieldsValue({ main_image: mockUrl });\n      setUploading(false);\n      message.success('图片上传成功');\n    }, 1000);\n    return false; // 阻止默认上传行为\n  };\n\n  return (\n    <Form\n      form={form}\n      layout=\"vertical\"\n      onFinish={handleSubmit}\n      initialValues={{\n        status: 'online',\n        need_id_photo: true,\n        sms_notification: false,\n      }}\n    >\n      <Row gutter={24}>\n        {/* 基本信息 */}\n        <Col span={24}>\n          <Card title=\"基本信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"name\"\n                  label=\"产品名称\"\n                  rules={[\n                    { required: true, message: '请输入产品名称' },\n                    { max: 255, message: '产品名称不能超过255个字符' },\n                  ]}\n                >\n                  <Input placeholder=\"请输入产品名称\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"status\"\n                  label=\"产品状态\"\n                  rules={[{ required: true, message: '请选择产品状态' }]}\n                >\n                  <Select placeholder=\"请选择产品状态\">\n                    {PRODUCT_STATUS_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"description\"\n                  label=\"产品介绍\"\n                >\n                  <TextArea\n                    rows={3}\n                    placeholder=\"请输入产品介绍\"\n                    maxLength={500}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"discount_details\"\n                  label=\"激活流程图\"\n                  tooltip=\"请输入激活流程图片链接URL，支持JPG、PNG、GIF等格式\"\n                  rules={[\n                    {\n                      type: 'url',\n                      message: '请输入有效的激活流程图链接URL',\n                    },\n                  ]}\n                >\n                  <Input\n                    placeholder=\"请输入激活流程图片链接，如：https://example.com/activation-flow.jpg\"\n                    maxLength={500}\n                    showCount\n                    onChange={(e) => {\n                      const url = e.target.value;\n                      if (url && (url.startsWith('http://') || url.startsWith('https://'))) {\n                        // 可以在这里添加图片预览逻辑\n                      }\n                    }}\n                  />\n                </Form.Item>\n                {/* 激活流程图预览 */}\n                <Form.Item shouldUpdate>\n                  {({ getFieldValue }) => {\n                    const imageUrl = getFieldValue('discount_details');\n                    if (imageUrl && (imageUrl.startsWith('http://') || imageUrl.startsWith('https://'))) {\n                      return (\n                        <div style={{ marginTop: 8 }}>\n                          <div style={{ marginBottom: 8, color: '#666', fontSize: '14px' }}>激活流程图预览：</div>\n                          <img\n                            src={imageUrl}\n                            alt=\"激活流程图预览\"\n                            style={{\n                              maxWidth: '100%',\n                              maxHeight: '200px',\n                              border: '1px solid #d9d9d9',\n                              borderRadius: '6px',\n                              objectFit: 'contain',\n                            }}\n                            onError={(e) => {\n                              e.currentTarget.style.display = 'none';\n                            }}\n                            onLoad={(e) => {\n                              e.currentTarget.style.display = 'block';\n                            }}\n                          />\n                        </div>\n                      );\n                    }\n                    return null;\n                  }}\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"delivery_method\"\n                  label=\"快递方式\"\n                >\n                  <Input\n                    placeholder=\"请输入快递方式，如：顺丰快递、圆通快递等\"\n                    maxLength={100}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"activation_method\"\n                  label=\"激活方式\"\n                >\n                  <Input\n                    placeholder=\"请输入激活方式，如：在线激活、APP激活等\"\n                    maxLength={100}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"recharge_link\"\n                  label=\"充值链接\"\n                  rules={[\n                    {\n                      type: 'url',\n                      message: '请输入有效的充值链接URL',\n                    },\n                  ]}\n                >\n                  <Input\n                    placeholder=\"请输入充值链接，如：https://example.com/recharge\"\n                    maxLength={200}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"card_age_requirement\"\n                  label=\"办卡年龄\"\n                >\n                  <Input\n                    placeholder=\"请输入办卡年龄要求，如：18-65岁\"\n                    maxLength={50}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"no_delivery_areas\"\n                  label=\"不发货地区\"\n                >\n                  <Select\n                    mode=\"tags\"\n                    placeholder=\"请输入不发货地区，支持多个\"\n                    style={{ width: '100%' }}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"directional_scope\"\n                  label=\"定向范围\"\n                >\n                  <TextArea\n                    rows={2}\n                    placeholder=\"请输入定向流量适用范围，如：抖音、快手、微信等\"\n                    maxLength={200}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"reactivation_cancellation\"\n                  label=\"复机及注销方式\"\n                >\n                  <TextArea\n                    rows={2}\n                    placeholder=\"请输入复机及注销方式说明\"\n                    maxLength={200}\n                    showCount\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 套餐信息 */}\n        <Col span={24}>\n          <Card title=\"套餐信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"monthly_fee\"\n                  label=\"套餐月租（元）\"\n                  rules={[\n                    { required: true, message: '请输入套餐月租' },\n                    { type: 'number', min: 0, message: '月租不能小于0' },\n                  ]}\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入月租\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"general_traffic\"\n                  label=\"通用流量（G）\"\n                  rules={[\n                    { required: true, message: '请输入通用流量' },\n                    { type: 'number', min: 0, message: '流量不能小于0' },\n                  ]}\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入通用流量\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"directional_traffic\"\n                  label=\"定向流量（G）\"\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入定向流量\"\n                    precision={2}\n                    min={0}\n                    max={9999.99}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"free_minutes\"\n                  label=\"免费通话（分钟）\"\n                >\n                  <InputNumber\n                    style={{ width: '100%' }}\n                    placeholder=\"请输入免费通话时长\"\n                    min={0}\n                    max={99999}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"package_duration\"\n                  label=\"套餐时长\"\n                >\n                  <Input placeholder=\"请输入套餐时长，如：1个月、3个月、12个月等\" />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"product_weight\"\n                  label=\"产品权重\"\n                  tooltip=\"权重越高，产品在列表中的排序越靠前\"\n                >\n                  <Select placeholder=\"请选择产品权重\">\n                    {PRODUCT_WEIGHT_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 运营商信息 */}\n        <Col span={24}>\n          <Card title=\"运营商信息\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"category_id\"\n                  label=\"产品分类\"\n                  rules={[{ required: true, message: '请选择产品分类' }]}\n                >\n                  <Select\n                    placeholder=\"请选择产品分类\"\n                    showSearch\n                    filterOption={(input, option) =>\n                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                    }\n                  >\n                    {categories.map(category => (\n                      <Option key={category.id} value={category.id}>\n                        {category.name}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"operator\"\n                  label=\"运营商\"\n                >\n                  <Select placeholder=\"请选择运营商\">\n                    {OPERATOR_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"location\"\n                  label=\"归属地\"\n                >\n                  <Select placeholder=\"请选择归属地\">\n                    {LOCATION_OPTIONS.map(option => (\n                      <Option key={option.value} value={option.value}>\n                        {option.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 商品标签 */}\n        <Col span={24}>\n          <Card title=\"商品标签\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag1\"\n                  label=\"标签1\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签1\" maxLength={6} />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag2\"\n                  label=\"标签2\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签2\" maxLength={6} />\n                </Form.Item>\n              </Col>\n              <Col span={8}>\n                <Form.Item\n                  name=\"tag3\"\n                  label=\"标签3\"\n                  rules={[{ max: 6, message: '标签不能超过6个字符' }]}\n                >\n                  <Input placeholder=\"请输入标签3\" maxLength={6} />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n\n\n        {/* 其他设置 */}\n        <Col span={24}>\n          <Card title=\"其他设置\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"need_id_photo\"\n                  label=\"需要上传身份证照片\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"sms_notification\"\n                  label=\"开启用户下单成功短信提醒\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"main_image\"\n                  label=\"产品主图\"\n                >\n                  <Input placeholder=\"请输入产品主图URL\" />\n                </Form.Item>\n              </Col>\n            </Row>\n            <Row gutter={16}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"detail_description\"\n                  label=\"商品详情图\"\n                  tooltip=\"请输入图片链接URL，支持JPG、PNG、GIF等格式\"\n                  rules={[\n                    {\n                      type: 'url',\n                      message: '请输入有效的图片链接URL',\n                    },\n                  ]}\n                >\n                  <Input\n                    placeholder=\"请输入商品详情图片链接，如：https://example.com/image.jpg\"\n                    maxLength={500}\n                    showCount\n                    onChange={(e) => {\n                      const url = e.target.value;\n                      if (url && (url.startsWith('http://') || url.startsWith('https://'))) {\n                        // 可以在这里添加图片预览逻辑\n                      }\n                    }}\n                  />\n                </Form.Item>\n                {/* 图片预览 */}\n                <Form.Item shouldUpdate>\n                  {({ getFieldValue }) => {\n                    const imageUrl = getFieldValue('detail_description');\n                    if (imageUrl && (imageUrl.startsWith('http://') || imageUrl.startsWith('https://'))) {\n                      return (\n                        <div style={{ marginTop: 8 }}>\n                          <div style={{ marginBottom: 8, color: '#666', fontSize: '14px' }}>图片预览：</div>\n                          <img\n                            src={imageUrl}\n                            alt=\"商品详情图预览\"\n                            style={{\n                              maxWidth: '100%',\n                              maxHeight: '300px',\n                              border: '1px solid #d9d9d9',\n                              borderRadius: '6px',\n                              objectFit: 'contain',\n                            }}\n                            onError={(e) => {\n                              e.currentTarget.style.display = 'none';\n                            }}\n                            onLoad={(e) => {\n                              e.currentTarget.style.display = 'block';\n                            }}\n                          />\n                        </div>\n                      );\n                    }\n                    return null;\n                  }}\n                </Form.Item>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 提交按钮 */}\n        <Col span={24}>\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                size=\"large\"\n              >\n                {initialValues ? '更新产品' : '创建产品'}\n              </Button>\n              <Button\n                size=\"large\"\n                onClick={() => form.resetFields()}\n              >\n                重置\n              </Button>\n            </Space>\n          </Form.Item>\n        </Col>\n      </Row>\n    </Form>\n  );\n};\n\nexport default ProductForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,MAAM;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAEA,SACEC,gBAAgB,EAChBC,gBAAgB,EAChBC,sBAAsB,QACjB,oBAAoB;AAC3B,SAASC,aAAa,EAAEC,eAAe,QAAQ,kBAAkB;;AAEjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAUA;;AAIA,MAAMC,sBAAsB,GAAG,CAC7B;EAAEC,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAE,CAAC,EACxB;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAG,CAAC,CAC3B;AAED,MAAM;EAAEC;AAAO,CAAC,GAAGlB,MAAM;AACzB,MAAM;EAAEmB;AAAS,CAAC,GAAGrB,KAAK;AAS1B,MAAMsB,WAAuC,GAAGA,CAAC;EAC/CC,IAAI;EACJC,aAAa;EACbC,QAAQ;EACRC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAa,EAAE,CAAC;;EAE5D;EACA,MAAMoC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI;MACF,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAC1D,IAAIF,eAAe,EAAE;QACnB,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,eAAe,CAAC;QAClD;QACA,MAAMM,gBAAgB,GAAGH,cAAc,CAACI,MAAM,CAAEC,GAAa,IAAKA,GAAG,CAACC,MAAM,KAAK,QAAQ,CAAC;QAC1FX,aAAa,CAACQ,gBAAgB,CAAC;MACjC;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACAhD,SAAS,CAAC,MAAM;IACdqC,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAMa,mBAAmB,GAAIC,CAAe,IAAK;MAC/C,IAAIA,CAAC,CAACC,GAAG,KAAK,YAAY,EAAE;QAC1Bf,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IAEDgB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;;IAEvD;IACA,MAAMK,sBAAsB,GAAGA,CAAA,KAAM;MACnClB,cAAc,CAAC,CAAC;IAClB,CAAC;IAEDgB,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAEC,sBAAsB,CAAC;IAEpE,OAAO,MAAM;MACXF,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAEN,mBAAmB,CAAC;MAC1DG,MAAM,CAACG,mBAAmB,CAAC,mBAAmB,EAAED,sBAAsB,CAAC;IACzE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvD,SAAS,CAAC,MAAM;IACd,IAAI2B,aAAa,EAAE;MACjB,MAAM8B,QAAQ,GAAG;QACf,GAAG9B,aAAa;QAChB+B,WAAW,EAAEC,UAAU,CAAChC,aAAa,CAAC+B,WAAW,CAAC;QAClDE,eAAe,EAAED,UAAU,CAAChC,aAAa,CAACiC,eAAe,CAAC;QAC1DC,mBAAmB,EAAEF,UAAU,CAAChC,aAAa,CAACkC,mBAAmB,CAAC;QAClEC,IAAI,EAAEnC,aAAa,CAACoC,IAAI,CAACD,IAAI;QAC7BE,IAAI,EAAErC,aAAa,CAACoC,IAAI,CAACC,IAAI;QAC7BC,IAAI,EAAEtC,aAAa,CAACoC,IAAI,CAACE;MAC3B,CAAC;MACDvC,IAAI,CAACwC,cAAc,CAACT,QAAQ,CAAC;MAC7BzB,WAAW,CAACL,aAAa,CAACwC,UAAU,IAAI,EAAE,CAAC;IAC7C;EACF,CAAC,EAAE,CAACxC,aAAa,EAAED,IAAI,CAAC,CAAC;;EAEzB;EACA,MAAM0C,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,MAAMC,UAA0B,GAAG;QACjC,GAAGD,MAAM;QACTF,UAAU,EAAEE,MAAM,CAACF,UAAU;QAAE;QAC/BI,iBAAiB,EAAEC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACE,iBAAiB,CAAC,GACtDvD,aAAa,CAACqD,MAAM,CAACE,iBAAiB,CAAC,GACvCF,MAAM,CAACE;MACb,CAAC;MAED,MAAM3C,QAAQ,CAAC0C,UAAU,CAAC;IAC5B,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAM0B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAML,MAAM,GAAG3C,IAAI,CAACiD,cAAc,CAAC,CAAC;IACpC,MAAMC,WAAW,GAAGlC,IAAI,CAACmC,SAAS,CAACR,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMS,OAAO,GAAG,MAAM7D,eAAe,CAAC2D,WAAW,CAAC;IAClD,IAAIE,OAAO,EAAE;MACXlE,OAAO,CAACkE,OAAO,CAAC,aAAa,CAAC;IAChC,CAAC,MAAM;MACLlE,OAAO,CAACoC,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM+B,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMV,MAAM,GAAG3C,IAAI,CAACiD,cAAc,CAAC,CAAC;IACpCpC,YAAY,CAACyC,OAAO,CAAC,eAAe,EAAEtC,IAAI,CAACmC,SAAS,CAACR,MAAM,CAAC,CAAC;IAC7DzD,OAAO,CAACkE,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,KAAK,GAAG3C,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACnD,IAAI0C,KAAK,EAAE;MACT,IAAI;QACF,MAAMC,SAAS,GAAGzC,IAAI,CAACC,KAAK,CAACuC,KAAK,CAAC;QACnCxD,IAAI,CAACwC,cAAc,CAACiB,SAAS,CAAC;QAC9BvE,OAAO,CAACkE,OAAO,CAAC,OAAO,CAAC;MAC1B,CAAC,CAAC,OAAO9B,KAAK,EAAE;QACdpC,OAAO,CAACoC,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,MAAM;MACLpC,OAAO,CAACwE,IAAI,CAAC,QAAQ,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,IAAgB,IAAK;IAC9C;IACA;IACApD,YAAY,CAAC,IAAI,CAAC;IAClBqD,UAAU,CAAC,MAAM;MACf,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAW,CAAC;MAChDtD,WAAW,CAACwD,OAAO,CAAC;MACpB9D,IAAI,CAACwC,cAAc,CAAC;QAAEC,UAAU,EAAEqB;MAAQ,CAAC,CAAC;MAC5CtD,YAAY,CAAC,KAAK,CAAC;MACnBtB,OAAO,CAACkE,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,oBACE3D,OAAA,CAACjB,IAAI;IACHwB,IAAI,EAAEA,IAAK;IACXiE,MAAM,EAAC,UAAU;IACjBC,QAAQ,EAAExB,YAAa;IACvBzC,aAAa,EAAE;MACboB,MAAM,EAAE,QAAQ;MAChB8C,aAAa,EAAE,IAAI;MACnBC,gBAAgB,EAAE;IACpB,CAAE;IAAAC,QAAA,eAEF5E,OAAA,CAACX,GAAG;MAACwF,MAAM,EAAE,EAAG;MAAAD,QAAA,gBAEd5E,OAAA,CAACV,GAAG;QAACwF,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ5E,OAAA,CAACT,IAAI;UAACwF,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C5E,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXjF,KAAK,EAAC,0BAAM;gBACZkF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE5F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAE6F,GAAG,EAAE,GAAG;kBAAE7F,OAAO,EAAE;gBAAiB,CAAC,CACvC;gBAAAmF,QAAA,eAEF5E,OAAA,CAAChB,KAAK;kBAACuG,WAAW,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,QAAQ;gBACbjF,KAAK,EAAC,0BAAM;gBACZkF,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE5F,OAAO,EAAE;gBAAU,CAAC,CAAE;gBAAAmF,QAAA,eAEhD5E,OAAA,CAACd,MAAM;kBAACqG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,EAC1BhF,sBAAsB,CAACgG,GAAG,CAACC,MAAM,iBAChC7F,OAAA,CAACI,MAAM;oBAAoBD,KAAK,EAAE0F,MAAM,CAAC1F,KAAM;oBAAAyE,QAAA,EAC5CiB,MAAM,CAAC3F;kBAAK,GADF2F,MAAM,CAAC1F,KAAK;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3F,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBjF,KAAK,EAAC,0BAAM;gBAAA0E,QAAA,eAEZ5E,OAAA,CAACK,QAAQ;kBACPyF,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,4CAAS;kBACrBQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3F,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,gBACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBjF,KAAK,EAAC,gCAAO;gBACb+F,OAAO,EAAC,gIAAiC;gBACzCb,KAAK,EAAE,CACL;kBACEc,IAAI,EAAE,KAAK;kBACXzG,OAAO,EAAE;gBACX,CAAC,CACD;gBAAAmF,QAAA,eAEF5E,OAAA,CAAChB,KAAK;kBACJuG,WAAW,EAAC,6HAAuD;kBACnEQ,SAAS,EAAE,GAAI;kBACfC,SAAS;kBACTG,QAAQ,EAAGnE,CAAC,IAAK;oBACf,MAAMoE,GAAG,GAAGpE,CAAC,CAACqE,MAAM,CAAClG,KAAK;oBAC1B,IAAIiG,GAAG,KAAKA,GAAG,CAACE,UAAU,CAAC,SAAS,CAAC,IAAIF,GAAG,CAACE,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE;sBACpE;oBAAA;kBAEJ;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZ3F,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBAACqB,YAAY;gBAAA3B,QAAA,EACpBA,CAAC;kBAAE4B;gBAAc,CAAC,KAAK;kBACtB,MAAM5F,QAAQ,GAAG4F,aAAa,CAAC,kBAAkB,CAAC;kBAClD,IAAI5F,QAAQ,KAAKA,QAAQ,CAAC0F,UAAU,CAAC,SAAS,CAAC,IAAI1F,QAAQ,CAAC0F,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE;oBACnF,oBACEtG,OAAA;sBAAKgF,KAAK,EAAE;wBAAEyB,SAAS,EAAE;sBAAE,CAAE;sBAAA7B,QAAA,gBAC3B5E,OAAA;wBAAKgF,KAAK,EAAE;0BAAEC,YAAY,EAAE,CAAC;0BAAEyB,KAAK,EAAE,MAAM;0BAAEC,QAAQ,EAAE;wBAAO,CAAE;wBAAA/B,QAAA,EAAC;sBAAQ;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChF3F,OAAA;wBACE4G,GAAG,EAAEhG,QAAS;wBACdiG,GAAG,EAAC,4CAAS;wBACb7B,KAAK,EAAE;0BACL8B,QAAQ,EAAE,MAAM;0BAChBC,SAAS,EAAE,OAAO;0BAClBC,MAAM,EAAE,mBAAmB;0BAC3BC,YAAY,EAAE,KAAK;0BACnBC,SAAS,EAAE;wBACb,CAAE;wBACFC,OAAO,EAAGnF,CAAC,IAAK;0BACdA,CAAC,CAACoF,aAAa,CAACpC,KAAK,CAACqC,OAAO,GAAG,MAAM;wBACxC,CAAE;wBACFC,MAAM,EAAGtF,CAAC,IAAK;0BACbA,CAAC,CAACoF,aAAa,CAACpC,KAAK,CAACqC,OAAO,GAAG,OAAO;wBACzC;sBAAE;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAEV;kBACA,OAAO,IAAI;gBACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,iBAAiB;gBACtBjF,KAAK,EAAC,0BAAM;gBAAA0E,QAAA,eAEZ5E,OAAA,CAAChB,KAAK;kBACJuG,WAAW,EAAC,0HAAsB;kBAClCQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3F,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,mBAAmB;gBACxBjF,KAAK,EAAC,0BAAM;gBAAA0E,QAAA,eAEZ5E,OAAA,CAAChB,KAAK;kBACJuG,WAAW,EAAC,iHAAuB;kBACnCQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,eAAe;gBACpBjF,KAAK,EAAC,0BAAM;gBACZkF,KAAK,EAAE,CACL;kBACEc,IAAI,EAAE,KAAK;kBACXzG,OAAO,EAAE;gBACX,CAAC,CACD;gBAAAmF,QAAA,eAEF5E,OAAA,CAAChB,KAAK;kBACJuG,WAAW,EAAC,0FAAwC;kBACpDQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3F,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,sBAAsB;gBAC3BjF,KAAK,EAAC,0BAAM;gBAAA0E,QAAA,eAEZ5E,OAAA,CAAChB,KAAK;kBACJuG,WAAW,EAAC,qFAAoB;kBAChCQ,SAAS,EAAE;gBAAG;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,mBAAmB;gBACxBjF,KAAK,EAAC,gCAAO;gBAAA0E,QAAA,eAEb5E,OAAA,CAACd,MAAM;kBACLqI,IAAI,EAAC,MAAM;kBACXhC,WAAW,EAAC,gFAAe;kBAC3BP,KAAK,EAAE;oBAAEwC,KAAK,EAAE;kBAAO;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3F,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,mBAAmB;gBACxBjF,KAAK,EAAC,0BAAM;gBAAA0E,QAAA,eAEZ5E,OAAA,CAACK,QAAQ;kBACPyF,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,4IAAyB;kBACrCQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,2BAA2B;gBAChCjF,KAAK,EAAC,4CAAS;gBAAA0E,QAAA,eAEf5E,OAAA,CAACK,QAAQ;kBACPyF,IAAI,EAAE,CAAE;kBACRP,WAAW,EAAC,0EAAc;kBAC1BQ,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3F,OAAA,CAACV,GAAG;QAACwF,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ5E,OAAA,CAACT,IAAI;UAACwF,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C5E,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBjF,KAAK,EAAC,4CAAS;gBACfkF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE5F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAEyG,IAAI,EAAE,QAAQ;kBAAEuB,GAAG,EAAE,CAAC;kBAAEhI,OAAO,EAAE;gBAAU,CAAC,CAC9C;gBAAAmF,QAAA,eAEF5E,OAAA,CAACf,WAAW;kBACV+F,KAAK,EAAE;oBAAEwC,KAAK,EAAE;kBAAO,CAAE;kBACzBjC,WAAW,EAAC,gCAAO;kBACnBmC,SAAS,EAAE,CAAE;kBACbD,GAAG,EAAE,CAAE;kBACPnC,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,iBAAiB;gBACtBjF,KAAK,EAAC,uCAAS;gBACfkF,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE5F,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAEyG,IAAI,EAAE,QAAQ;kBAAEuB,GAAG,EAAE,CAAC;kBAAEhI,OAAO,EAAE;gBAAU,CAAC,CAC9C;gBAAAmF,QAAA,eAEF5E,OAAA,CAACf,WAAW;kBACV+F,KAAK,EAAE;oBAAEwC,KAAK,EAAE;kBAAO,CAAE;kBACzBjC,WAAW,EAAC,4CAAS;kBACrBmC,SAAS,EAAE,CAAE;kBACbD,GAAG,EAAE,CAAE;kBACPnC,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,qBAAqB;gBAC1BjF,KAAK,EAAC,uCAAS;gBAAA0E,QAAA,eAEf5E,OAAA,CAACf,WAAW;kBACV+F,KAAK,EAAE;oBAAEwC,KAAK,EAAE;kBAAO,CAAE;kBACzBjC,WAAW,EAAC,4CAAS;kBACrBmC,SAAS,EAAE,CAAE;kBACbD,GAAG,EAAE,CAAE;kBACPnC,GAAG,EAAE;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3F,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,cAAc;gBACnBjF,KAAK,EAAC,kDAAU;gBAAA0E,QAAA,eAEhB5E,OAAA,CAACf,WAAW;kBACV+F,KAAK,EAAE;oBAAEwC,KAAK,EAAE;kBAAO,CAAE;kBACzBjC,WAAW,EAAC,wDAAW;kBACvBkC,GAAG,EAAE,CAAE;kBACPnC,GAAG,EAAE;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBjF,KAAK,EAAC,0BAAM;gBAAA0E,QAAA,eAEZ5E,OAAA,CAAChB,KAAK;kBAACuG,WAAW,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,gBAAgB;gBACrBjF,KAAK,EAAC,0BAAM;gBACZ+F,OAAO,EAAC,wGAAmB;gBAAArB,QAAA,eAE3B5E,OAAA,CAACd,MAAM;kBAACqG,WAAW,EAAC,4CAAS;kBAAAX,QAAA,EAC1B3E,sBAAsB,CAAC2F,GAAG,CAACC,MAAM,iBAChC7F,OAAA,CAACI,MAAM;oBAAoBD,KAAK,EAAE0F,MAAM,CAAC1F,KAAM;oBAAAyE,QAAA,EAC5CiB,MAAM,CAAC3F;kBAAK,GADF2F,MAAM,CAAC1F,KAAK;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3F,OAAA,CAACV,GAAG;QAACwF,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ5E,OAAA,CAACT,IAAI;UAACwF,KAAK,EAAC,gCAAO;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,eAC9C5E,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,aAAa;gBAClBjF,KAAK,EAAC,0BAAM;gBACZkF,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE5F,OAAO,EAAE;gBAAU,CAAC,CAAE;gBAAAmF,QAAA,eAEhD5E,OAAA,CAACd,MAAM;kBACLqG,WAAW,EAAC,4CAAS;kBACrBoC,UAAU;kBACVC,YAAY,EAAEA,CAACC,KAAK,EAAEhC,MAAM;oBAAA,IAAAiC,gBAAA;oBAAA,OACzBjC,MAAM,aAANA,MAAM,wBAAAiC,gBAAA,GAANjC,MAAM,CAAEjB,QAAQ,cAAAkD,gBAAA,uBAAjBA,gBAAA,CAAyCC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACH,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;kBAAA,CACrF;kBAAAnD,QAAA,EAEA5D,UAAU,CAAC4E,GAAG,CAACqC,QAAQ,iBACtBjI,OAAA,CAACI,MAAM;oBAAmBD,KAAK,EAAE8H,QAAQ,CAACC,EAAG;oBAAAtD,QAAA,EAC1CqD,QAAQ,CAAC9C;kBAAI,GADH8C,QAAQ,CAACC,EAAE;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,UAAU;gBACfjF,KAAK,EAAC,oBAAK;gBAAA0E,QAAA,eAEX5E,OAAA,CAACd,MAAM;kBAACqG,WAAW,EAAC,sCAAQ;kBAAAX,QAAA,EACzBlF,gBAAgB,CAACkG,GAAG,CAACC,MAAM,iBAC1B7F,OAAA,CAACI,MAAM;oBAAoBD,KAAK,EAAE0F,MAAM,CAAC1F,KAAM;oBAAAyE,QAAA,EAC5CiB,MAAM,CAAC3F;kBAAK,GADF2F,MAAM,CAAC1F,KAAK;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,UAAU;gBACfjF,KAAK,EAAC,oBAAK;gBAAA0E,QAAA,eAEX5E,OAAA,CAACd,MAAM;kBAACqG,WAAW,EAAC,sCAAQ;kBAAAX,QAAA,EACzBjF,gBAAgB,CAACiG,GAAG,CAACC,MAAM,iBAC1B7F,OAAA,CAACI,MAAM;oBAAoBD,KAAK,EAAE0F,MAAM,CAAC1F,KAAM;oBAAAyE,QAAA,EAC5CiB,MAAM,CAAC3F;kBAAK,GADF2F,MAAM,CAAC1F,KAAK;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3F,OAAA,CAACV,GAAG;QAACwF,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ5E,OAAA,CAACT,IAAI;UAACwF,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,eAC7C5E,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXjF,KAAK,EAAC,eAAK;gBACXkF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAE7F,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAmF,QAAA,eAE3C5E,OAAA,CAAChB,KAAK;kBAACuG,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXjF,KAAK,EAAC,eAAK;gBACXkF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAE7F,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAmF,QAAA,eAE3C5E,OAAA,CAAChB,KAAK;kBAACuG,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,CAAE;cAAAF,QAAA,eACX5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXjF,KAAK,EAAC,eAAK;gBACXkF,KAAK,EAAE,CAAC;kBAAEE,GAAG,EAAE,CAAC;kBAAE7F,OAAO,EAAE;gBAAa,CAAC,CAAE;gBAAAmF,QAAA,eAE3C5E,OAAA,CAAChB,KAAK;kBAACuG,WAAW,EAAC,iCAAQ;kBAACQ,SAAS,EAAE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAKN3F,OAAA,CAACV,GAAG;QAACwF,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ5E,OAAA,CAACT,IAAI;UAACwF,KAAK,EAAC,0BAAM;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,gBAC7C5E,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,gBACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,eAAe;gBACpBjF,KAAK,EAAC,wDAAW;gBACjBiI,aAAa,EAAC,SAAS;gBAAAvD,QAAA,eAEvB5E,OAAA,CAACb,MAAM;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN3F,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBjF,KAAK,EAAC,0EAAc;gBACpBiI,aAAa,EAAC,SAAS;gBAAAvD,QAAA,eAEvB5E,OAAA,CAACb,MAAM;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3F,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,YAAY;gBACjBjF,KAAK,EAAC,0BAAM;gBAAA0E,QAAA,eAEZ5E,OAAA,CAAChB,KAAK;kBAACuG,WAAW,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3F,OAAA,CAACX,GAAG;YAACwF,MAAM,EAAE,EAAG;YAAAD,QAAA,eACd5E,OAAA,CAACV,GAAG;cAACwF,IAAI,EAAE,EAAG;cAAAF,QAAA,gBACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBACRC,IAAI,EAAC,oBAAoB;gBACzBjF,KAAK,EAAC,gCAAO;gBACb+F,OAAO,EAAC,wGAA6B;gBACrCb,KAAK,EAAE,CACL;kBACEc,IAAI,EAAE,KAAK;kBACXzG,OAAO,EAAE;gBACX,CAAC,CACD;gBAAAmF,QAAA,eAEF5E,OAAA,CAAChB,KAAK;kBACJuG,WAAW,EAAC,mHAA6C;kBACzDQ,SAAS,EAAE,GAAI;kBACfC,SAAS;kBACTG,QAAQ,EAAGnE,CAAC,IAAK;oBACf,MAAMoE,GAAG,GAAGpE,CAAC,CAACqE,MAAM,CAAClG,KAAK;oBAC1B,IAAIiG,GAAG,KAAKA,GAAG,CAACE,UAAU,CAAC,SAAS,CAAC,IAAIF,GAAG,CAACE,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE;sBACpE;oBAAA;kBAEJ;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZ3F,OAAA,CAACjB,IAAI,CAACmG,IAAI;gBAACqB,YAAY;gBAAA3B,QAAA,EACpBA,CAAC;kBAAE4B;gBAAc,CAAC,KAAK;kBACtB,MAAM5F,QAAQ,GAAG4F,aAAa,CAAC,oBAAoB,CAAC;kBACpD,IAAI5F,QAAQ,KAAKA,QAAQ,CAAC0F,UAAU,CAAC,SAAS,CAAC,IAAI1F,QAAQ,CAAC0F,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE;oBACnF,oBACEtG,OAAA;sBAAKgF,KAAK,EAAE;wBAAEyB,SAAS,EAAE;sBAAE,CAAE;sBAAA7B,QAAA,gBAC3B5E,OAAA;wBAAKgF,KAAK,EAAE;0BAAEC,YAAY,EAAE,CAAC;0BAAEyB,KAAK,EAAE,MAAM;0BAAEC,QAAQ,EAAE;wBAAO,CAAE;wBAAA/B,QAAA,EAAC;sBAAK;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7E3F,OAAA;wBACE4G,GAAG,EAAEhG,QAAS;wBACdiG,GAAG,EAAC,4CAAS;wBACb7B,KAAK,EAAE;0BACL8B,QAAQ,EAAE,MAAM;0BAChBC,SAAS,EAAE,OAAO;0BAClBC,MAAM,EAAE,mBAAmB;0BAC3BC,YAAY,EAAE,KAAK;0BACnBC,SAAS,EAAE;wBACb,CAAE;wBACFC,OAAO,EAAGnF,CAAC,IAAK;0BACdA,CAAC,CAACoF,aAAa,CAACpC,KAAK,CAACqC,OAAO,GAAG,MAAM;wBACxC,CAAE;wBACFC,MAAM,EAAGtF,CAAC,IAAK;0BACbA,CAAC,CAACoF,aAAa,CAACpC,KAAK,CAACqC,OAAO,GAAG,OAAO;wBACzC;sBAAE;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAEV;kBACA,OAAO,IAAI;gBACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3F,OAAA,CAACV,GAAG;QAACwF,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ5E,OAAA,CAACjB,IAAI,CAACmG,IAAI;UAAAN,QAAA,eACR5E,OAAA,CAACR,KAAK;YAAAoF,QAAA,gBACJ5E,OAAA,CAACZ,MAAM;cACL8G,IAAI,EAAC,SAAS;cACdkC,QAAQ,EAAC,QAAQ;cACjB1H,OAAO,EAAEA,OAAQ;cACjB2H,IAAI,EAAC,OAAO;cAAAzD,QAAA,EAEXpE,aAAa,GAAG,MAAM,GAAG;YAAM;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACT3F,OAAA,CAACZ,MAAM;cACLiJ,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAM/H,IAAI,CAACgI,WAAW,CAAC,CAAE;cAAA3D,QAAA,EACnC;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAChF,EAAA,CAppBIL,WAAuC;AAAAkI,EAAA,GAAvClI,WAAuC;AAspB7C,eAAeA,WAAW;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"title\", \"eventKey\", \"children\"];\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { MenuContext } from \"./context/MenuContext\";\nimport { useFullPath, useMeasure } from \"./context/PathContext\";\nimport { parseChildren } from \"./utils/commonUtil\";\nvar InternalMenuItemGroup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    title = props.title,\n    eventKey = props.eventKey,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls;\n  var groupPrefixCls = \"\".concat(prefixCls, \"-item-group\");\n  return /*#__PURE__*/React.createElement(\"li\", _extends({\n    ref: ref,\n    role: \"presentation\"\n  }, restProps, {\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    },\n    className: classNames(groupPrefixCls, className)\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    role: \"presentation\",\n    className: \"\".concat(groupPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), /*#__PURE__*/React.createElement(\"ul\", {\n    role: \"group\",\n    className: \"\".concat(groupPrefixCls, \"-list\")\n  }, children));\n});\nvar MenuItemGroup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var eventKey = props.eventKey,\n    children = props.children;\n  var connectedKeyPath = useFullPath(eventKey);\n  var childList = parseChildren(children, connectedKeyPath);\n  var measure = useMeasure();\n  if (measure) {\n    return childList;\n  }\n  return /*#__PURE__*/React.createElement(InternalMenuItemGroup, _extends({\n    ref: ref\n  }, omit(props, ['warnKey'])), childList);\n});\nif (process.env.NODE_ENV !== 'production') {\n  MenuItemGroup.displayName = 'MenuItemGroup';\n}\nexport default MenuItemGroup;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "classNames", "omit", "React", "MenuContext", "useFullPath", "useMeasure", "parse<PERSON><PERSON><PERSON>n", "InternalMenuItemGroup", "forwardRef", "props", "ref", "className", "title", "eventKey", "children", "restProps", "_React$useContext", "useContext", "prefixCls", "groupPrefixCls", "concat", "createElement", "role", "onClick", "e", "stopPropagation", "undefined", "MenuItemGroup", "connectedKeyPath", "childList", "measure", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-menu/es/MenuItemGroup.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"title\", \"eventKey\", \"children\"];\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { MenuContext } from \"./context/MenuContext\";\nimport { useFullPath, useMeasure } from \"./context/PathContext\";\nimport { parseChildren } from \"./utils/commonUtil\";\nvar InternalMenuItemGroup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    title = props.title,\n    eventKey = props.eventKey,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls;\n  var groupPrefixCls = \"\".concat(prefixCls, \"-item-group\");\n  return /*#__PURE__*/React.createElement(\"li\", _extends({\n    ref: ref,\n    role: \"presentation\"\n  }, restProps, {\n    onClick: function onClick(e) {\n      return e.stopPropagation();\n    },\n    className: classNames(groupPrefixCls, className)\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    role: \"presentation\",\n    className: \"\".concat(groupPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), /*#__PURE__*/React.createElement(\"ul\", {\n    role: \"group\",\n    className: \"\".concat(groupPrefixCls, \"-list\")\n  }, children));\n});\nvar MenuItemGroup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var eventKey = props.eventKey,\n    children = props.children;\n  var connectedKeyPath = useFullPath(eventKey);\n  var childList = parseChildren(children, connectedKeyPath);\n  var measure = useMeasure();\n  if (measure) {\n    return childList;\n  }\n  return /*#__PURE__*/React.createElement(InternalMenuItemGroup, _extends({\n    ref: ref\n  }, omit(props, ['warnKey'])), childList);\n});\nif (process.env.NODE_ENV !== 'production') {\n  MenuItemGroup.displayName = 'MenuItemGroup';\n}\nexport default MenuItemGroup;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;AAC9D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,WAAW,EAAEC,UAAU,QAAQ,uBAAuB;AAC/D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,qBAAqB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC9E,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,SAAS,GAAGjB,wBAAwB,CAACW,KAAK,EAAEV,SAAS,CAAC;EACxD,IAAIiB,iBAAiB,GAAGd,KAAK,CAACe,UAAU,CAACd,WAAW,CAAC;IACnDe,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EACzC,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACF,SAAS,EAAE,aAAa,CAAC;EACxD,OAAO,aAAahB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAExB,QAAQ,CAAC;IACrDa,GAAG,EAAEA,GAAG;IACRY,IAAI,EAAE;EACR,CAAC,EAAEP,SAAS,EAAE;IACZQ,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;MAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;IAC5B,CAAC;IACDd,SAAS,EAAEX,UAAU,CAACmB,cAAc,EAAER,SAAS;EACjD,CAAC,CAAC,EAAE,aAAaT,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC1CC,IAAI,EAAE,cAAc;IACpBX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACD,cAAc,EAAE,QAAQ,CAAC;IAC9CP,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGc;EAC7C,CAAC,EAAEd,KAAK,CAAC,EAAE,aAAaV,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;IAChDC,IAAI,EAAE,OAAO;IACbX,SAAS,EAAE,EAAE,CAACS,MAAM,CAACD,cAAc,EAAE,OAAO;EAC9C,CAAC,EAAEL,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACF,IAAIa,aAAa,GAAG,aAAazB,KAAK,CAACM,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIG,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IAC3BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;EAC3B,IAAIc,gBAAgB,GAAGxB,WAAW,CAACS,QAAQ,CAAC;EAC5C,IAAIgB,SAAS,GAAGvB,aAAa,CAACQ,QAAQ,EAAEc,gBAAgB,CAAC;EACzD,IAAIE,OAAO,GAAGzB,UAAU,CAAC,CAAC;EAC1B,IAAIyB,OAAO,EAAE;IACX,OAAOD,SAAS;EAClB;EACA,OAAO,aAAa3B,KAAK,CAACmB,aAAa,CAACd,qBAAqB,EAAEV,QAAQ,CAAC;IACtEa,GAAG,EAAEA;EACP,CAAC,EAAET,IAAI,CAACQ,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAAEoB,SAAS,CAAC;AAC1C,CAAC,CAAC;AACF,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCN,aAAa,CAACO,WAAW,GAAG,eAAe;AAC7C;AACA,eAAeP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
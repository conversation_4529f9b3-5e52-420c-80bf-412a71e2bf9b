{"ast": null, "code": "import * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nfunction voidFunc() {}\nconst WatermarkContext = /*#__PURE__*/React.createContext({\n  add: voidFunc,\n  remove: voidFunc\n});\nexport function usePanelRef(panelSelector) {\n  const watermark = React.useContext(WatermarkContext);\n  const panelEleRef = React.useRef(null);\n  const panelRef = useEvent(ele => {\n    if (ele) {\n      const innerContentEle = panelSelector ? ele.querySelector(panelSelector) : ele;\n      watermark.add(innerContentEle);\n      panelEleRef.current = innerContentEle;\n    } else {\n      watermark.remove(panelEleRef.current);\n    }\n  });\n  return panelRef;\n}\nexport default WatermarkContext;", "map": {"version": 3, "names": ["React", "useEvent", "voidFunc", "WatermarkContext", "createContext", "add", "remove", "usePanelRef", "panelSelector", "watermark", "useContext", "panelEleRef", "useRef", "panelRef", "ele", "innerContentEle", "querySelector", "current"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/watermark/context.js"], "sourcesContent": ["import * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nfunction voidFunc() {}\nconst WatermarkContext = /*#__PURE__*/React.createContext({\n  add: voidFunc,\n  remove: voidFunc\n});\nexport function usePanelRef(panelSelector) {\n  const watermark = React.useContext(WatermarkContext);\n  const panelEleRef = React.useRef(null);\n  const panelRef = useEvent(ele => {\n    if (ele) {\n      const innerContentEle = panelSelector ? ele.querySelector(panelSelector) : ele;\n      watermark.add(innerContentEle);\n      panelEleRef.current = innerContentEle;\n    } else {\n      watermark.remove(panelEleRef.current);\n    }\n  });\n  return panelRef;\n}\nexport default WatermarkContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,QAAQA,CAAA,EAAG,CAAC;AACrB,MAAMC,gBAAgB,GAAG,aAAaH,KAAK,CAACI,aAAa,CAAC;EACxDC,GAAG,EAAEH,QAAQ;EACbI,MAAM,EAAEJ;AACV,CAAC,CAAC;AACF,OAAO,SAASK,WAAWA,CAACC,aAAa,EAAE;EACzC,MAAMC,SAAS,GAAGT,KAAK,CAACU,UAAU,CAACP,gBAAgB,CAAC;EACpD,MAAMQ,WAAW,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMC,QAAQ,GAAGZ,QAAQ,CAACa,GAAG,IAAI;IAC/B,IAAIA,GAAG,EAAE;MACP,MAAMC,eAAe,GAAGP,aAAa,GAAGM,GAAG,CAACE,aAAa,CAACR,aAAa,CAAC,GAAGM,GAAG;MAC9EL,SAAS,CAACJ,GAAG,CAACU,eAAe,CAAC;MAC9BJ,WAAW,CAACM,OAAO,GAAGF,eAAe;IACvC,CAAC,MAAM;MACLN,SAAS,CAACH,MAAM,CAACK,WAAW,CAACM,OAAO,CAAC;IACvC;EACF,CAAC,CAAC;EACF,OAAOJ,QAAQ;AACjB;AACA,eAAeV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import defaultAlgorithm from '../default';\nimport genControlHeight from '../shared/genControlHeight';\nimport genFontMapToken from '../shared/genFontMapToken';\nimport genCompactSizeMapToken from './genCompactSizeMapToken';\nconst derivative = (token, mapToken) => {\n  const mergedMapToken = mapToken !== null && mapToken !== void 0 ? mapToken : defaultAlgorithm(token);\n  const fontSize = mergedMapToken.fontSizeSM; // Smaller size font-size as base\n  const controlHeight = mergedMapToken.controlHeight - 4;\n  return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, mergedMapToken), genCompactSizeMapToken(mapToken !== null && mapToken !== void 0 ? mapToken : token)), genFontMapToken(fontSize)), {\n    // controlHeight\n    controlHeight\n  }), genControlHeight(Object.assign(Object.assign({}, mergedMapToken), {\n    controlHeight\n  })));\n};\nexport default derivative;", "map": {"version": 3, "names": ["defaultAlgorithm", "genControlHeight", "genFontMapToken", "genCompactSizeMapToken", "derivative", "token", "mapToken", "mergedMapToken", "fontSize", "fontSizeSM", "controlHeight", "Object", "assign"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/theme/themes/compact/index.js"], "sourcesContent": ["import defaultAlgorithm from '../default';\nimport genControlHeight from '../shared/genControlHeight';\nimport genFontMapToken from '../shared/genFontMapToken';\nimport genCompactSizeMapToken from './genCompactSizeMapToken';\nconst derivative = (token, mapToken) => {\n  const mergedMapToken = mapToken !== null && mapToken !== void 0 ? mapToken : defaultAlgorithm(token);\n  const fontSize = mergedMapToken.fontSizeSM; // Smaller size font-size as base\n  const controlHeight = mergedMapToken.controlHeight - 4;\n  return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, mergedMapToken), genCompactSizeMapToken(mapToken !== null && mapToken !== void 0 ? mapToken : token)), genFontMapToken(fontSize)), {\n    // controlHeight\n    controlHeight\n  }), genControlHeight(Object.assign(Object.assign({}, mergedMapToken), {\n    controlHeight\n  })));\n};\nexport default derivative;"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,YAAY;AACzC,OAAOC,gBAAgB,MAAM,4BAA4B;AACzD,OAAOC,eAAe,MAAM,2BAA2B;AACvD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,MAAMC,UAAU,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;EACtC,MAAMC,cAAc,GAAGD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGN,gBAAgB,CAACK,KAAK,CAAC;EACpG,MAAMG,QAAQ,GAAGD,cAAc,CAACE,UAAU,CAAC,CAAC;EAC5C,MAAMC,aAAa,GAAGH,cAAc,CAACG,aAAa,GAAG,CAAC;EACtD,OAAOC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,cAAc,CAAC,EAAEJ,sBAAsB,CAACG,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGD,KAAK,CAAC,CAAC,EAAEH,eAAe,CAACM,QAAQ,CAAC,CAAC,EAAE;IAClN;IACAE;EACF,CAAC,CAAC,EAAET,gBAAgB,CAACU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,cAAc,CAAC,EAAE;IACpEG;EACF,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
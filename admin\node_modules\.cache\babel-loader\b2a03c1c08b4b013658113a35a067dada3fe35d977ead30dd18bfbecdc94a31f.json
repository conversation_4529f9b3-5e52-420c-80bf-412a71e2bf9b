{"ast": null, "code": "import React,{useState}from'react';import{Card,Table,Button,Space,Tag,Typography,Row,Col,Statistic,Input,Select,DatePicker,Modal,Progress}from'antd';import{SearchOutlined,ReloadOutlined,EyeOutlined,TruckOutlined,CheckCircleOutlined,ClockCircleOutlined,EnvironmentOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Option}=Select;const{RangePicker}=DatePicker;// 已发货订单接口定义\n// 模拟已发货订单数据\nconst mockShippedOrders=[{id:1,orderNo:'ORD202401150001',customerName:'张三',customerPhone:'13800138001',customerIdCard:'110101199001011234',productName:'中国移动5G畅享套餐',operator:'中国移动',deliveryAddress:'北京市朝阳区建国门外大街1号国贸大厦A座1001室',logisticsCompany:'顺丰速运',trackingNumber:'SF1234567890123',shippedAt:'2024-01-15 10:45:00',estimatedDelivery:'2024-01-16 18:00:00',deliveryStatus:'delivered',deliveryProgress:100,currentLocation:'已签收',deliveryDays:1},{id:2,orderNo:'ORD202401150004',customerName:'赵六',customerPhone:'13800138004',customerIdCard:'******************',productName:'中国广电智慧套餐',operator:'中国广电',deliveryAddress:'深圳市南山区深南大道9988号华润置地大厦A座3501室',logisticsCompany:'中通快递',trackingNumber:'ZTO9876543210987',shippedAt:'2024-01-15 13:45:00',estimatedDelivery:'2024-01-17 12:00:00',deliveryStatus:'out_for_delivery',deliveryProgress:85,currentLocation:'深圳南山区派送中',deliveryDays:1.5},{id:3,orderNo:'ORD202401150011',customerName:'郑十三',customerPhone:'13800138011',customerIdCard:'320101199306154444',productName:'中国电信天翼套餐',operator:'中国电信',deliveryAddress:'南京市鼓楼区中山路200号德基广场二期A座1801室',logisticsCompany:'申通快递',trackingNumber:'STO5678901234567',shippedAt:'2024-01-15 16:20:00',estimatedDelivery:'2024-01-17 15:00:00',deliveryStatus:'in_transit',deliveryProgress:60,currentLocation:'南京中转站',deliveryDays:1.2},{id:4,orderNo:'ORD202401150012',customerName:'冯十四',customerPhone:'13800138012',customerIdCard:'510101199408255555',productName:'中国联通青春套餐',operator:'中国联通',deliveryAddress:'成都市锦江区红星路三段1号IFS国际金融中心2号楼2501室',logisticsCompany:'韵达快递',trackingNumber:'YD3456789012345',shippedAt:'2024-01-15 09:15:00',estimatedDelivery:'2024-01-18 14:00:00',deliveryStatus:'exception',deliveryProgress:45,currentLocation:'成都转运中心-异常',deliveryDays:2}];const OrderShipped=()=>{const[orders,setOrders]=useState(mockShippedOrders);const[loading,setLoading]=useState(false);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[viewModalVisible,setViewModalVisible]=useState(false);const[trackingModalVisible,setTrackingModalVisible]=useState(false);const[selectedOrder,setSelectedOrder]=useState(null);// 获取配送状态颜色\nconst getDeliveryStatusColor=status=>{const colors={in_transit:'blue',out_for_delivery:'orange',delivered:'green',exception:'red'};return colors[status]||'default';};// 获取配送状态文本\nconst getDeliveryStatusText=status=>{const texts={in_transit:'运输中',out_for_delivery:'派送中',delivered:'已送达',exception:'异常'};return texts[status]||status;};// 获取配送状态图标\nconst getDeliveryStatusIcon=status=>{const icons={in_transit:/*#__PURE__*/_jsx(TruckOutlined,{}),out_for_delivery:/*#__PURE__*/_jsx(EnvironmentOutlined,{}),delivered:/*#__PURE__*/_jsx(CheckCircleOutlined,{}),exception:/*#__PURE__*/_jsx(ClockCircleOutlined,{})};return icons[status]||/*#__PURE__*/_jsx(TruckOutlined,{});};// 统计数据\nconst stats={total:orders.length,delivered:orders.filter(order=>order.deliveryStatus==='delivered').length,inTransit:orders.filter(order=>order.deliveryStatus==='in_transit').length,outForDelivery:orders.filter(order=>order.deliveryStatus==='out_for_delivery').length,exception:orders.filter(order=>order.deliveryStatus==='exception').length};// 查看订单详情\nconst handleViewOrder=order=>{setSelectedOrder(order);setViewModalVisible(true);};// 查看物流跟踪\nconst handleTrackingOrder=order=>{setSelectedOrder(order);setTrackingModalVisible(true);};// 表格列定义\nconst columns=[{title:'订单号',dataIndex:'orderNo',key:'orderNo',width:160,render:text=>/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:'12px'},children:text})},{title:'客户信息',key:'customer',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600,fontSize:'13px'},children:record.customerName}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#666'},children:record.customerPhone})]})},{title:'产品信息',key:'product',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',marginBottom:'2px'},children:record.productName}),/*#__PURE__*/_jsx(Tag,{color:\"blue\",style:{fontSize:'11px',padding:'1px 6px'},children:record.operator})]})},{title:'物流信息',key:'logistics',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600,fontSize:'12px'},children:record.logisticsCompany}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'11px',color:'#666'},children:record.trackingNumber}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'11px',color:'#999'},children:[\"\\u53D1\\u8D27: \",record.shippedAt.split(' ')[0]]})]})},{title:'配送状态',key:'deliveryStatus',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'4px'},children:/*#__PURE__*/_jsx(Tag,{color:getDeliveryStatusColor(record.deliveryStatus),icon:getDeliveryStatusIcon(record.deliveryStatus),children:getDeliveryStatusText(record.deliveryStatus)})}),/*#__PURE__*/_jsx(Progress,{percent:record.deliveryProgress,size:\"small\",status:record.deliveryStatus==='exception'?'exception':'active',showInfo:false}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'11px',color:'#666',marginTop:'2px'},children:record.currentLocation})]})},{title:'发货时长',dataIndex:'deliveryDays',key:'deliveryDays',width:100,render:days=>/*#__PURE__*/_jsx(Text,{style:{color:days>2?'#f5222d':'#52c41a'},children:days<1?\"\".concat(Math.round(days*24),\"\\u5C0F\\u65F6\"):\"\".concat(days.toFixed(1),\"\\u5929\")}),sorter:(a,b)=>a.deliveryDays-b.deliveryDays},{title:'预计送达',dataIndex:'estimatedDelivery',key:'estimatedDelivery',width:150,render:text=>/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px'},children:text})},{title:'操作',key:'action',width:180,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewOrder(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(TruckOutlined,{}),onClick:()=>handleTrackingOrder(record),children:\"\\u8DDF\\u8E2A\"})]})}];return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px'},children:[/*#__PURE__*/_jsxs(Title,{level:2,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(TruckOutlined,{style:{marginRight:'8px',color:'#1890ff'}}),\"\\u5DF2\\u53D1\\u8D27\\u8BA2\\u5355\"]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u53D1\\u8D27\\u8BA2\\u5355\",value:stats.total,prefix:/*#__PURE__*/_jsx(TruckOutlined,{}),valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u9001\\u8FBE\",value:stats.delivered,prefix:/*#__PURE__*/_jsx(CheckCircleOutlined,{}),valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8FD0\\u8F93\\u4E2D\",value:stats.inTransit,prefix:/*#__PURE__*/_jsx(TruckOutlined,{}),valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F02\\u5E38\\u8BA2\\u5355\",value:stats.exception,prefix:/*#__PURE__*/_jsx(ClockCircleOutlined,{}),valueStyle:{color:'#f5222d'}})})})]}),/*#__PURE__*/_jsx(Card,{style:{marginBottom:'16px'},children:/*#__PURE__*/_jsx(Row,{justify:\"space-between\",align:\"middle\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Input,{placeholder:\"\\u641C\\u7D22\\u8BA2\\u5355\\u53F7\\u3001\\u5BA2\\u6237\\u59D3\\u540D\\u3001\\u5FEB\\u9012\\u5355\\u53F7\",prefix:/*#__PURE__*/_jsx(SearchOutlined,{}),style:{width:280}}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u914D\\u9001\\u72B6\\u6001\",style:{width:120},children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"\\u5168\\u90E8\"}),/*#__PURE__*/_jsx(Option,{value:\"in_transit\",children:\"\\u8FD0\\u8F93\\u4E2D\"}),/*#__PURE__*/_jsx(Option,{value:\"out_for_delivery\",children:\"\\u6D3E\\u9001\\u4E2D\"}),/*#__PURE__*/_jsx(Option,{value:\"delivered\",children:\"\\u5DF2\\u9001\\u8FBE\"}),/*#__PURE__*/_jsx(Option,{value:\"exception\",children:\"\\u5F02\\u5E38\"})]}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u7269\\u6D41\\u516C\\u53F8\",style:{width:120},children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"\\u5168\\u90E8\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u987A\\u4E30\\u901F\\u8FD0\",children:\"\\u987A\\u4E30\\u901F\\u8FD0\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u4E2D\\u901A\\u5FEB\\u9012\",children:\"\\u4E2D\\u901A\\u5FEB\\u9012\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u7533\\u901A\\u5FEB\\u9012\",children:\"\\u7533\\u901A\\u5FEB\\u9012\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u97F5\\u8FBE\\u5FEB\\u9012\",children:\"\\u97F5\\u8FBE\\u5FEB\\u9012\"})]}),/*#__PURE__*/_jsx(RangePicker,{placeholder:['发货开始时间','发货结束时间']}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(SearchOutlined,{}),type:\"primary\",children:\"\\u641C\\u7D22\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),children:\"\\u91CD\\u7F6E\"})]})})})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:orders,rowKey:\"id\",loading:loading,scroll:{x:1500},pagination:{total:orders.length,pageSize:10,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u8BA2\\u5355\\u8BE6\\u60C5\",open:viewModalVisible,onCancel:()=>setViewModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setViewModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Card,{title:\"\\u5BA2\\u6237\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u59D3\\u540D\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.customerName})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u624B\\u673A\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.customerPhone})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8EAB\\u4EFD\\u8BC1\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.customerIdCard})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u4EA7\\u54C1\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4EA7\\u54C1\\u540D\\u79F0\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.productName})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8FD0\\u8425\\u5546\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u6536\\u8D27\\u5730\\u5740\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsx(\"div\",{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(Text,{children:selectedOrder.deliveryAddress})})}),/*#__PURE__*/_jsxs(Card,{title:\"\\u7269\\u6D41\\u4FE1\\u606F\",size:\"small\",children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BA2\\u5355\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.orderNo})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u7269\\u6D41\\u516C\\u53F8\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.logisticsCompany})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5FEB\\u9012\\u5355\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.trackingNumber})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u53D1\\u8D27\\u65F6\\u95F4\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.shippedAt})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9884\\u8BA1\\u9001\\u8FBE\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.estimatedDelivery})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u914D\\u9001\\u72B6\\u6001\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:getDeliveryStatusColor(selectedOrder.deliveryStatus),icon:getDeliveryStatusIcon(selectedOrder.deliveryStatus),children:getDeliveryStatusText(selectedOrder.deliveryStatus)})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5F53\\u524D\\u4F4D\\u7F6E\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.currentLocation})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u914D\\u9001\\u8FDB\\u5EA6\\uFF1A\"}),/*#__PURE__*/_jsx(Progress,{percent:selectedOrder.deliveryProgress,size:\"small\",status:selectedOrder.deliveryStatus==='exception'?'exception':'active'})]})})]})]})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u7269\\u6D41\\u8DDF\\u8E2A\",open:trackingModalVisible,onCancel:()=>setTrackingModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setTrackingModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:700,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Card,{size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u7269\\u6D41\\u516C\\u53F8\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.logisticsCompany})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5FEB\\u9012\\u5355\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.trackingNumber})]})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5F53\\u524D\\u72B6\\u6001\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:getDeliveryStatusColor(selectedOrder.deliveryStatus),icon:getDeliveryStatusIcon(selectedOrder.deliveryStatus),children:getDeliveryStatusText(selectedOrder.deliveryStatus)})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u914D\\u9001\\u8FDB\\u5EA6\\uFF1A\"}),/*#__PURE__*/_jsx(Progress,{percent:selectedOrder.deliveryProgress,size:\"small\",status:selectedOrder.deliveryStatus==='exception'?'exception':'active'})]})]})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u7269\\u6D41\\u8F68\\u8FF9\",size:\"small\",children:/*#__PURE__*/_jsxs(\"div\",{style:{padding:'16px 0'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'#52c41a',marginRight:'8px'}}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"2024-01-15 10:45:00\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:'24px',color:'#666'},children:[\"\\u3010\",selectedOrder.logisticsCompany,\"\\u3011\\u60A8\\u7684\\u5FEB\\u4EF6\\u5DF2\\u53D1\\u51FA\\uFF0C\\u5FEB\\u9012\\u5458\\u6B63\\u5728\\u914D\\u9001\\u9014\\u4E2D\"]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(TruckOutlined,{style:{color:'#1890ff',marginRight:'8px'}}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"2024-01-15 12:30:00\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginLeft:'24px',color:'#666'},children:\"\\u5FEB\\u4EF6\\u5DF2\\u5230\\u8FBE\\u3010\\u5317\\u4EAC\\u8F6C\\u8FD0\\u4E2D\\u5FC3\\u3011\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(EnvironmentOutlined,{style:{color:'#fa8c16',marginRight:'8px'}}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"2024-01-16 08:20:00\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginLeft:'24px',color:'#666'},children:\"\\u5FEB\\u4EF6\\u5DF2\\u5230\\u8FBE\\u3010\\u5317\\u4EAC\\u671D\\u9633\\u533A\\u5206\\u62E3\\u4E2D\\u5FC3\\u3011\"})]}),selectedOrder.deliveryStatus==='delivered'&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'#52c41a',marginRight:'8px'}}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"2024-01-16 15:30:00\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:'24px',color:'#666'},children:[\"\\u5FEB\\u4EF6\\u5DF2\\u7B7E\\u6536\\uFF0C\\u7B7E\\u6536\\u4EBA\\uFF1A\",selectedOrder.customerName]})]}),selectedOrder.deliveryStatus==='exception'&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(ClockCircleOutlined,{style:{color:'#f5222d',marginRight:'8px'}}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"2024-01-16 14:00:00\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginLeft:'24px',color:'#f5222d'},children:\"\\u914D\\u9001\\u5F02\\u5E38\\uFF1A\\u6536\\u4EF6\\u4EBA\\u5730\\u5740\\u4E0D\\u8BE6\\uFF0C\\u6B63\\u5728\\u8054\\u7CFB\\u6536\\u4EF6\\u4EBA\"})]})]})})]})})]});};export default OrderShipped;", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Tag", "Typography", "Row", "Col", "Statistic", "Input", "Select", "DatePicker", "Modal", "Progress", "SearchOutlined", "ReloadOutlined", "EyeOutlined", "TruckOutlined", "CheckCircleOutlined", "ClockCircleOutlined", "EnvironmentOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Option", "RangePicker", "mockShippedOrders", "id", "orderNo", "customerName", "customerPhone", "customerIdCard", "productName", "operator", "deliveryAddress", "logisticsCompany", "trackingNumber", "shippedAt", "estimatedDelivery", "deliveryStatus", "deliveryProgress", "currentLocation", "deliveryDays", "OrderShipped", "orders", "setOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "viewModalVisible", "setViewModalVisible", "trackingModalVisible", "setTrackingModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "getDeliveryStatusColor", "status", "colors", "in_transit", "out_for_delivery", "delivered", "exception", "getDeliveryStatusText", "texts", "getDeliveryStatusIcon", "icons", "stats", "total", "length", "filter", "order", "inTransit", "outForDelivery", "handleViewOrder", "handleTrackingOrder", "columns", "title", "dataIndex", "key", "width", "render", "text", "code", "style", "fontSize", "children", "_", "record", "fontWeight", "color", "marginBottom", "padding", "split", "icon", "percent", "size", "showInfo", "marginTop", "days", "concat", "Math", "round", "toFixed", "sorter", "a", "b", "type", "onClick", "level", "marginRight", "gutter", "span", "value", "prefix", "valueStyle", "justify", "align", "placeholder", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "footer", "strong", "display", "alignItems", "marginLeft"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderShipped.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Input,\n  Select,\n  DatePicker,\n  Modal,\n  Form,\n  message,\n  Descriptions,\n  Divider,\n  Progress,\n} from 'antd';\nimport {\n  SearchOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n  TruckOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  EnvironmentOutlined,\n  PhoneOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\n// 已发货订单接口定义\ninterface ShippedOrder {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  customerIdCard: string;\n  productName: string;\n  operator: string;\n  deliveryAddress: string;\n  logisticsCompany: string;\n  trackingNumber: string;\n  shippedAt: string;\n  estimatedDelivery: string;\n  deliveryStatus: 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception';\n  deliveryProgress: number; // 配送进度百分比\n  currentLocation?: string; // 当前位置\n  deliveryDays: number; // 发货天数\n}\n\n// 模拟已发货订单数据\nconst mockShippedOrders: ShippedOrder[] = [\n  {\n    id: 1,\n    orderNo: 'ORD202401150001',\n    customerName: '张三',\n    customerPhone: '13800138001',\n    customerIdCard: '110101199001011234',\n    productName: '中国移动5G畅享套餐',\n    operator: '中国移动',\n    deliveryAddress: '北京市朝阳区建国门外大街1号国贸大厦A座1001室',\n    logisticsCompany: '顺丰速运',\n    trackingNumber: 'SF1234567890123',\n    shippedAt: '2024-01-15 10:45:00',\n    estimatedDelivery: '2024-01-16 18:00:00',\n    deliveryStatus: 'delivered',\n    deliveryProgress: 100,\n    currentLocation: '已签收',\n    deliveryDays: 1,\n  },\n  {\n    id: 2,\n    orderNo: 'ORD202401150004',\n    customerName: '赵六',\n    customerPhone: '13800138004',\n    customerIdCard: '******************',\n    productName: '中国广电智慧套餐',\n    operator: '中国广电',\n    deliveryAddress: '深圳市南山区深南大道9988号华润置地大厦A座3501室',\n    logisticsCompany: '中通快递',\n    trackingNumber: 'ZTO9876543210987',\n    shippedAt: '2024-01-15 13:45:00',\n    estimatedDelivery: '2024-01-17 12:00:00',\n    deliveryStatus: 'out_for_delivery',\n    deliveryProgress: 85,\n    currentLocation: '深圳南山区派送中',\n    deliveryDays: 1.5,\n  },\n  {\n    id: 3,\n    orderNo: 'ORD202401150011',\n    customerName: '郑十三',\n    customerPhone: '13800138011',\n    customerIdCard: '320101199306154444',\n    productName: '中国电信天翼套餐',\n    operator: '中国电信',\n    deliveryAddress: '南京市鼓楼区中山路200号德基广场二期A座1801室',\n    logisticsCompany: '申通快递',\n    trackingNumber: 'STO5678901234567',\n    shippedAt: '2024-01-15 16:20:00',\n    estimatedDelivery: '2024-01-17 15:00:00',\n    deliveryStatus: 'in_transit',\n    deliveryProgress: 60,\n    currentLocation: '南京中转站',\n    deliveryDays: 1.2,\n  },\n  {\n    id: 4,\n    orderNo: 'ORD202401150012',\n    customerName: '冯十四',\n    customerPhone: '13800138012',\n    customerIdCard: '510101199408255555',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n    deliveryAddress: '成都市锦江区红星路三段1号IFS国际金融中心2号楼2501室',\n    logisticsCompany: '韵达快递',\n    trackingNumber: 'YD3456789012345',\n    shippedAt: '2024-01-15 09:15:00',\n    estimatedDelivery: '2024-01-18 14:00:00',\n    deliveryStatus: 'exception',\n    deliveryProgress: 45,\n    currentLocation: '成都转运中心-异常',\n    deliveryDays: 2,\n  },\n];\n\nconst OrderShipped: React.FC = () => {\n  const [orders, setOrders] = useState<ShippedOrder[]>(mockShippedOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n  const [trackingModalVisible, setTrackingModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<ShippedOrder | null>(null);\n\n  // 获取配送状态颜色\n  const getDeliveryStatusColor = (status: string) => {\n    const colors = {\n      in_transit: 'blue',\n      out_for_delivery: 'orange',\n      delivered: 'green',\n      exception: 'red',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n  // 获取配送状态文本\n  const getDeliveryStatusText = (status: string) => {\n    const texts = {\n      in_transit: '运输中',\n      out_for_delivery: '派送中',\n      delivered: '已送达',\n      exception: '异常',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  // 获取配送状态图标\n  const getDeliveryStatusIcon = (status: string) => {\n    const icons = {\n      in_transit: <TruckOutlined />,\n      out_for_delivery: <EnvironmentOutlined />,\n      delivered: <CheckCircleOutlined />,\n      exception: <ClockCircleOutlined />,\n    };\n    return icons[status as keyof typeof icons] || <TruckOutlined />;\n  };\n\n  // 统计数据\n  const stats = {\n    total: orders.length,\n    delivered: orders.filter(order => order.deliveryStatus === 'delivered').length,\n    inTransit: orders.filter(order => order.deliveryStatus === 'in_transit').length,\n    outForDelivery: orders.filter(order => order.deliveryStatus === 'out_for_delivery').length,\n    exception: orders.filter(order => order.deliveryStatus === 'exception').length,\n  };\n\n  // 查看订单详情\n  const handleViewOrder = (order: ShippedOrder) => {\n    setSelectedOrder(order);\n    setViewModalVisible(true);\n  };\n\n  // 查看物流跟踪\n  const handleTrackingOrder = (order: ShippedOrder) => {\n    setSelectedOrder(order);\n    setTrackingModalVisible(true);\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<ShippedOrder> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>{text}</Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600, fontSize: '13px' }}>\n            {record.customerName}\n          </div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品信息',\n      key: 'product',\n      width: 200,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontSize: '12px', marginBottom: '2px' }}>\n            {record.productName}\n          </div>\n          <Tag color=\"blue\" style={{ fontSize: '11px', padding: '1px 6px' }}>{record.operator}</Tag>\n        </div>\n      ),\n    },\n    {\n      title: '物流信息',\n      key: 'logistics',\n      width: 200,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600, fontSize: '12px' }}>\n            {record.logisticsCompany}\n          </div>\n          <div style={{ fontSize: '11px', color: '#666' }}>\n            {record.trackingNumber}\n          </div>\n          <div style={{ fontSize: '11px', color: '#999' }}>\n            发货: {record.shippedAt.split(' ')[0]}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '配送状态',\n      key: 'deliveryStatus',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ marginBottom: '4px' }}>\n            <Tag \n              color={getDeliveryStatusColor(record.deliveryStatus)}\n              icon={getDeliveryStatusIcon(record.deliveryStatus)}\n            >\n              {getDeliveryStatusText(record.deliveryStatus)}\n            </Tag>\n          </div>\n          <Progress \n            percent={record.deliveryProgress} \n            size=\"small\" \n            status={record.deliveryStatus === 'exception' ? 'exception' : 'active'}\n            showInfo={false}\n          />\n          <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>\n            {record.currentLocation}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '发货时长',\n      dataIndex: 'deliveryDays',\n      key: 'deliveryDays',\n      width: 100,\n      render: (days: number) => (\n        <Text style={{ color: days > 2 ? '#f5222d' : '#52c41a' }}>\n          {days < 1 ? `${Math.round(days * 24)}小时` : `${days.toFixed(1)}天`}\n        </Text>\n      ),\n      sorter: (a, b) => a.deliveryDays - b.deliveryDays,\n    },\n    {\n      title: '预计送达',\n      dataIndex: 'estimatedDelivery',\n      key: 'estimatedDelivery',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 180,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<TruckOutlined />}\n            onClick={() => handleTrackingOrder(record)}\n          >\n            跟踪\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2} style={{ marginBottom: '24px' }}>\n        <TruckOutlined style={{ marginRight: '8px', color: '#1890ff' }} />\n        已发货订单\n      </Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已发货订单\"\n              value={stats.total}\n              prefix={<TruckOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已送达\"\n              value={stats.delivered}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"运输中\"\n              value={stats.inTransit}\n              prefix={<TruckOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"异常订单\"\n              value={stats.exception}\n              prefix={<ClockCircleOutlined />}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: '16px' }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Input\n                placeholder=\"搜索订单号、客户姓名、快递单号\"\n                prefix={<SearchOutlined />}\n                style={{ width: 280 }}\n              />\n              <Select placeholder=\"配送状态\" style={{ width: 120 }}>\n                <Option value=\"\">全部</Option>\n                <Option value=\"in_transit\">运输中</Option>\n                <Option value=\"out_for_delivery\">派送中</Option>\n                <Option value=\"delivered\">已送达</Option>\n                <Option value=\"exception\">异常</Option>\n              </Select>\n              <Select placeholder=\"物流公司\" style={{ width: 120 }}>\n                <Option value=\"\">全部</Option>\n                <Option value=\"顺丰速运\">顺丰速运</Option>\n                <Option value=\"中通快递\">中通快递</Option>\n                <Option value=\"申通快递\">申通快递</Option>\n                <Option value=\"韵达快递\">韵达快递</Option>\n              </Select>\n              <RangePicker placeholder={['发货开始时间', '发货结束时间']} />\n              <Button icon={<SearchOutlined />} type=\"primary\">\n                搜索\n              </Button>\n              <Button icon={<ReloadOutlined />}>\n                重置\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 订单表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={orders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1500 }}\n          pagination={{\n            total: orders.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 查看订单详情弹窗 */}\n      <Modal\n        title=\"订单详情\"\n        open={viewModalVisible}\n        onCancel={() => setViewModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setViewModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={800}\n      >\n        {selectedOrder && (\n          <div>\n            {/* 客户信息 */}\n            <Card title=\"客户信息\" size=\"small\" style={{ marginBottom: 16 }}>\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>姓名：</Text>\n                    <Text>{selectedOrder.customerName}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>手机号：</Text>\n                    <Text code>{selectedOrder.customerPhone}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>身份证号：</Text>\n                    <Text code>{selectedOrder.customerIdCard}</Text>\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n\n            {/* 产品信息 */}\n            <Card title=\"产品信息\" size=\"small\" style={{ marginBottom: 16 }}>\n              <Row gutter={16}>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>产品名称：</Text>\n                    <Text>{selectedOrder.productName}</Text>\n                  </div>\n                </Col>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>运营商：</Text>\n                    <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n\n            {/* 收货地址 */}\n            <Card title=\"收货地址\" size=\"small\" style={{ marginBottom: 16 }}>\n              <div style={{ padding: '8px 0' }}>\n                <Text>{selectedOrder.deliveryAddress}</Text>\n              </div>\n            </Card>\n\n            {/* 物流信息 */}\n            <Card title=\"物流信息\" size=\"small\">\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>订单号：</Text>\n                    <Text code>{selectedOrder.orderNo}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>物流公司：</Text>\n                    <Text>{selectedOrder.logisticsCompany}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>快递单号：</Text>\n                    <Text code>{selectedOrder.trackingNumber}</Text>\n                  </div>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>发货时间：</Text>\n                    <Text>{selectedOrder.shippedAt}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>预计送达：</Text>\n                    <Text>{selectedOrder.estimatedDelivery}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>配送状态：</Text>\n                    <Tag\n                      color={getDeliveryStatusColor(selectedOrder.deliveryStatus)}\n                      icon={getDeliveryStatusIcon(selectedOrder.deliveryStatus)}\n                    >\n                      {getDeliveryStatusText(selectedOrder.deliveryStatus)}\n                    </Tag>\n                  </div>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>当前位置：</Text>\n                    <Text>{selectedOrder.currentLocation}</Text>\n                  </div>\n                </Col>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>配送进度：</Text>\n                    <Progress\n                      percent={selectedOrder.deliveryProgress}\n                      size=\"small\"\n                      status={selectedOrder.deliveryStatus === 'exception' ? 'exception' : 'active'}\n                    />\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n          </div>\n        )}\n      </Modal>\n\n      {/* 物流跟踪弹窗 */}\n      <Modal\n        title=\"物流跟踪\"\n        open={trackingModalVisible}\n        onCancel={() => setTrackingModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTrackingModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={700}\n      >\n        {selectedOrder && (\n          <div>\n            {/* 物流基本信息 */}\n            <Card size=\"small\" style={{ marginBottom: 16 }}>\n              <Row gutter={16}>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>物流公司：</Text>\n                    <Text>{selectedOrder.logisticsCompany}</Text>\n                  </div>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>快递单号：</Text>\n                    <Text code>{selectedOrder.trackingNumber}</Text>\n                  </div>\n                </Col>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>当前状态：</Text>\n                    <Tag\n                      color={getDeliveryStatusColor(selectedOrder.deliveryStatus)}\n                      icon={getDeliveryStatusIcon(selectedOrder.deliveryStatus)}\n                    >\n                      {getDeliveryStatusText(selectedOrder.deliveryStatus)}\n                    </Tag>\n                  </div>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>配送进度：</Text>\n                    <Progress\n                      percent={selectedOrder.deliveryProgress}\n                      size=\"small\"\n                      status={selectedOrder.deliveryStatus === 'exception' ? 'exception' : 'active'}\n                    />\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n\n            {/* 物流轨迹 */}\n            <Card title=\"物流轨迹\" size=\"small\">\n              <div style={{ padding: '16px 0' }}>\n                {/* 模拟物流轨迹数据 */}\n                <div style={{ marginBottom: '16px' }}>\n                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n                    <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '8px' }} />\n                    <Text strong>2024-01-15 10:45:00</Text>\n                  </div>\n                  <div style={{ marginLeft: '24px', color: '#666' }}>\n                    【{selectedOrder.logisticsCompany}】您的快件已发出，快递员正在配送途中\n                  </div>\n                </div>\n\n                <div style={{ marginBottom: '16px' }}>\n                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n                    <TruckOutlined style={{ color: '#1890ff', marginRight: '8px' }} />\n                    <Text strong>2024-01-15 12:30:00</Text>\n                  </div>\n                  <div style={{ marginLeft: '24px', color: '#666' }}>\n                    快件已到达【北京转运中心】\n                  </div>\n                </div>\n\n                <div style={{ marginBottom: '16px' }}>\n                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n                    <EnvironmentOutlined style={{ color: '#fa8c16', marginRight: '8px' }} />\n                    <Text strong>2024-01-16 08:20:00</Text>\n                  </div>\n                  <div style={{ marginLeft: '24px', color: '#666' }}>\n                    快件已到达【北京朝阳区分拣中心】\n                  </div>\n                </div>\n\n                {selectedOrder.deliveryStatus === 'delivered' && (\n                  <div style={{ marginBottom: '16px' }}>\n                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n                      <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '8px' }} />\n                      <Text strong>2024-01-16 15:30:00</Text>\n                    </div>\n                    <div style={{ marginLeft: '24px', color: '#666' }}>\n                      快件已签收，签收人：{selectedOrder.customerName}\n                    </div>\n                  </div>\n                )}\n\n                {selectedOrder.deliveryStatus === 'exception' && (\n                  <div style={{ marginBottom: '16px' }}>\n                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n                      <ClockCircleOutlined style={{ color: '#f5222d', marginRight: '8px' }} />\n                      <Text strong>2024-01-16 14:00:00</Text>\n                    </div>\n                    <div style={{ marginLeft: '24px', color: '#f5222d' }}>\n                      配送异常：收件人地址不详，正在联系收件人\n                    </div>\n                  </div>\n                )}\n              </div>\n            </Card>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderShipped;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAmB,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,GAAG,CACHC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,KAAK,CAKLC,QAAQ,KACH,MAAM,CACb,OACEC,cAAc,CACdC,cAAc,CACdC,WAAW,CACXC,aAAa,CACbC,mBAAmB,CACnBC,mBAAmB,CACnBC,mBAAmB,KAEd,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG3B,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGrB,UAAU,CAClC,KAAM,CAAEsB,MAAO,CAAC,CAAGjB,MAAM,CACzB,KAAM,CAAEkB,WAAY,CAAC,CAAGjB,UAAU,CAElC;AAoBA;AACA,KAAM,CAAAkB,iBAAiC,CAAG,CACxC,CACEC,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,aAAa,CAC5BC,cAAc,CAAE,oBAAoB,CACpCC,WAAW,CAAE,YAAY,CACzBC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,2BAA2B,CAC5CC,gBAAgB,CAAE,MAAM,CACxBC,cAAc,CAAE,iBAAiB,CACjCC,SAAS,CAAE,qBAAqB,CAChCC,iBAAiB,CAAE,qBAAqB,CACxCC,cAAc,CAAE,WAAW,CAC3BC,gBAAgB,CAAE,GAAG,CACrBC,eAAe,CAAE,KAAK,CACtBC,YAAY,CAAE,CAChB,CAAC,CACD,CACEf,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,aAAa,CAC5BC,cAAc,CAAE,oBAAoB,CACpCC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,8BAA8B,CAC/CC,gBAAgB,CAAE,MAAM,CACxBC,cAAc,CAAE,kBAAkB,CAClCC,SAAS,CAAE,qBAAqB,CAChCC,iBAAiB,CAAE,qBAAqB,CACxCC,cAAc,CAAE,kBAAkB,CAClCC,gBAAgB,CAAE,EAAE,CACpBC,eAAe,CAAE,UAAU,CAC3BC,YAAY,CAAE,GAChB,CAAC,CACD,CACEf,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,aAAa,CAC5BC,cAAc,CAAE,oBAAoB,CACpCC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,4BAA4B,CAC7CC,gBAAgB,CAAE,MAAM,CACxBC,cAAc,CAAE,kBAAkB,CAClCC,SAAS,CAAE,qBAAqB,CAChCC,iBAAiB,CAAE,qBAAqB,CACxCC,cAAc,CAAE,YAAY,CAC5BC,gBAAgB,CAAE,EAAE,CACpBC,eAAe,CAAE,OAAO,CACxBC,YAAY,CAAE,GAChB,CAAC,CACD,CACEf,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,aAAa,CAC5BC,cAAc,CAAE,oBAAoB,CACpCC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,gCAAgC,CACjDC,gBAAgB,CAAE,MAAM,CACxBC,cAAc,CAAE,iBAAiB,CACjCC,SAAS,CAAE,qBAAqB,CAChCC,iBAAiB,CAAE,qBAAqB,CACxCC,cAAc,CAAE,WAAW,CAC3BC,gBAAgB,CAAE,EAAE,CACpBC,eAAe,CAAE,WAAW,CAC5BC,YAAY,CAAE,CAChB,CAAC,CACF,CAED,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGjD,QAAQ,CAAiB8B,iBAAiB,CAAC,CACvE,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACoD,eAAe,CAAEC,kBAAkB,CAAC,CAAGrD,QAAQ,CAAc,EAAE,CAAC,CACvE,KAAM,CAACsD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACwD,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAAC0D,aAAa,CAAEC,gBAAgB,CAAC,CAAG3D,QAAQ,CAAsB,IAAI,CAAC,CAE7E;AACA,KAAM,CAAA4D,sBAAsB,CAAIC,MAAc,EAAK,CACjD,KAAM,CAAAC,MAAM,CAAG,CACbC,UAAU,CAAE,MAAM,CAClBC,gBAAgB,CAAE,QAAQ,CAC1BC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,KACb,CAAC,CACD,MAAO,CAAAJ,MAAM,CAACD,MAAM,CAAwB,EAAI,SAAS,CAC3D,CAAC,CAED;AACA,KAAM,CAAAM,qBAAqB,CAAIN,MAAc,EAAK,CAChD,KAAM,CAAAO,KAAK,CAAG,CACZL,UAAU,CAAE,KAAK,CACjBC,gBAAgB,CAAE,KAAK,CACvBC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,IACb,CAAC,CACD,MAAO,CAAAE,KAAK,CAACP,MAAM,CAAuB,EAAIA,MAAM,CACtD,CAAC,CAED;AACA,KAAM,CAAAQ,qBAAqB,CAAIR,MAAc,EAAK,CAChD,KAAM,CAAAS,KAAK,CAAG,CACZP,UAAU,cAAExC,IAAA,CAACL,aAAa,GAAE,CAAC,CAC7B8C,gBAAgB,cAAEzC,IAAA,CAACF,mBAAmB,GAAE,CAAC,CACzC4C,SAAS,cAAE1C,IAAA,CAACJ,mBAAmB,GAAE,CAAC,CAClC+C,SAAS,cAAE3C,IAAA,CAACH,mBAAmB,GAAE,CACnC,CAAC,CACD,MAAO,CAAAkD,KAAK,CAACT,MAAM,CAAuB,eAAItC,IAAA,CAACL,aAAa,GAAE,CAAC,CACjE,CAAC,CAED;AACA,KAAM,CAAAqD,KAAK,CAAG,CACZC,KAAK,CAAExB,MAAM,CAACyB,MAAM,CACpBR,SAAS,CAAEjB,MAAM,CAAC0B,MAAM,CAACC,KAAK,EAAIA,KAAK,CAAChC,cAAc,GAAK,WAAW,CAAC,CAAC8B,MAAM,CAC9EG,SAAS,CAAE5B,MAAM,CAAC0B,MAAM,CAACC,KAAK,EAAIA,KAAK,CAAChC,cAAc,GAAK,YAAY,CAAC,CAAC8B,MAAM,CAC/EI,cAAc,CAAE7B,MAAM,CAAC0B,MAAM,CAACC,KAAK,EAAIA,KAAK,CAAChC,cAAc,GAAK,kBAAkB,CAAC,CAAC8B,MAAM,CAC1FP,SAAS,CAAElB,MAAM,CAAC0B,MAAM,CAACC,KAAK,EAAIA,KAAK,CAAChC,cAAc,GAAK,WAAW,CAAC,CAAC8B,MAC1E,CAAC,CAED;AACA,KAAM,CAAAK,eAAe,CAAIH,KAAmB,EAAK,CAC/ChB,gBAAgB,CAACgB,KAAK,CAAC,CACvBpB,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAwB,mBAAmB,CAAIJ,KAAmB,EAAK,CACnDhB,gBAAgB,CAACgB,KAAK,CAAC,CACvBlB,uBAAuB,CAAC,IAAI,CAAC,CAC/B,CAAC,CAED;AACA,KAAM,CAAAuB,OAAkC,CAAG,CACzC,CACEC,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGC,IAAY,eACnB/D,IAAA,CAACI,IAAI,EAAC4D,IAAI,MAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAEJ,IAAI,CAAO,CAExD,CAAC,CACD,CACEL,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACM,CAAC,CAAEC,MAAM,gBAChBnE,KAAA,QAAAiE,QAAA,eACEnE,IAAA,QAAKiE,KAAK,CAAE,CAAEK,UAAU,CAAE,GAAG,CAAEJ,QAAQ,CAAE,MAAO,CAAE,CAAAC,QAAA,CAC/CE,MAAM,CAAC3D,YAAY,CACjB,CAAC,cACNV,IAAA,QAAKiE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAC7CE,MAAM,CAAC1D,aAAa,CAClB,CAAC,EACH,CAET,CAAC,CACD,CACE+C,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACM,CAAC,CAAEC,MAAM,gBAChBnE,KAAA,QAAAiE,QAAA,eACEnE,IAAA,QAAKiE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEM,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,CACnDE,MAAM,CAACxD,WAAW,CAChB,CAAC,cACNb,IAAA,CAAClB,GAAG,EAACyF,KAAK,CAAC,MAAM,CAACN,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEO,OAAO,CAAE,SAAU,CAAE,CAAAN,QAAA,CAAEE,MAAM,CAACvD,QAAQ,CAAM,CAAC,EACvF,CAET,CAAC,CACD,CACE4C,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACM,CAAC,CAAEC,MAAM,gBAChBnE,KAAA,QAAAiE,QAAA,eACEnE,IAAA,QAAKiE,KAAK,CAAE,CAAEK,UAAU,CAAE,GAAG,CAAEJ,QAAQ,CAAE,MAAO,CAAE,CAAAC,QAAA,CAC/CE,MAAM,CAACrD,gBAAgB,CACrB,CAAC,cACNhB,IAAA,QAAKiE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAC7CE,MAAM,CAACpD,cAAc,CACnB,CAAC,cACNf,KAAA,QAAK+D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,EAAC,gBAC3C,CAACE,MAAM,CAACnD,SAAS,CAACwD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAChC,CAAC,EACH,CAET,CAAC,CACD,CACEhB,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,gBAAgB,CACrBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACM,CAAC,CAAEC,MAAM,gBAChBnE,KAAA,QAAAiE,QAAA,eACEnE,IAAA,QAAKiE,KAAK,CAAE,CAAEO,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,cAClCnE,IAAA,CAAClB,GAAG,EACFyF,KAAK,CAAElC,sBAAsB,CAACgC,MAAM,CAACjD,cAAc,CAAE,CACrDuD,IAAI,CAAE7B,qBAAqB,CAACuB,MAAM,CAACjD,cAAc,CAAE,CAAA+C,QAAA,CAElDvB,qBAAqB,CAACyB,MAAM,CAACjD,cAAc,CAAC,CAC1C,CAAC,CACH,CAAC,cACNpB,IAAA,CAACT,QAAQ,EACPqF,OAAO,CAAEP,MAAM,CAAChD,gBAAiB,CACjCwD,IAAI,CAAC,OAAO,CACZvC,MAAM,CAAE+B,MAAM,CAACjD,cAAc,GAAK,WAAW,CAAG,WAAW,CAAG,QAAS,CACvE0D,QAAQ,CAAE,KAAM,CACjB,CAAC,cACF9E,IAAA,QAAKiE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEK,KAAK,CAAE,MAAM,CAAEQ,SAAS,CAAE,KAAM,CAAE,CAAAZ,QAAA,CAC/DE,MAAM,CAAC/C,eAAe,CACpB,CAAC,EACH,CAET,CAAC,CACD,CACEoC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGkB,IAAY,eACnBhF,IAAA,CAACI,IAAI,EAAC6D,KAAK,CAAE,CAAEM,KAAK,CAAES,IAAI,CAAG,CAAC,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAb,QAAA,CACtDa,IAAI,CAAG,CAAC,IAAAC,MAAA,CAAMC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAG,EAAE,CAAC,oBAAAC,MAAA,CAAUD,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,UAAG,CAC5D,CACP,CACDC,MAAM,CAAEA,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAC/D,YAAY,CAAGgE,CAAC,CAAChE,YACvC,CAAC,CACD,CACEmC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,mBAAmB,CAC9BC,GAAG,CAAE,mBAAmB,CACxBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGC,IAAY,eACnB/D,IAAA,QAAKiE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAC,QAAA,CAC9BJ,IAAI,CACF,CAET,CAAC,CACD,CACEL,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACM,CAAC,CAAEC,MAAM,gBAChBnE,KAAA,CAACrB,KAAK,EAACgG,IAAI,CAAC,OAAO,CAAAV,QAAA,eACjBnE,IAAA,CAACpB,MAAM,EACL4G,IAAI,CAAC,MAAM,CACXX,IAAI,CAAC,OAAO,CACZF,IAAI,cAAE3E,IAAA,CAACN,WAAW,GAAE,CAAE,CACtB+F,OAAO,CAAEA,CAAA,GAAMlC,eAAe,CAACc,MAAM,CAAE,CAAAF,QAAA,CACxC,cAED,CAAQ,CAAC,cACTnE,IAAA,CAACpB,MAAM,EACL4G,IAAI,CAAC,MAAM,CACXX,IAAI,CAAC,OAAO,CACZF,IAAI,cAAE3E,IAAA,CAACL,aAAa,GAAE,CAAE,CACxB8F,OAAO,CAAEA,CAAA,GAAMjC,mBAAmB,CAACa,MAAM,CAAE,CAAAF,QAAA,CAC5C,cAED,CAAQ,CAAC,EACJ,CAEX,CAAC,CACF,CAED,mBACEjE,KAAA,QAAK+D,KAAK,CAAE,CAAEQ,OAAO,CAAE,MAAO,CAAE,CAAAN,QAAA,eAC9BjE,KAAA,CAACC,KAAK,EAACuF,KAAK,CAAE,CAAE,CAACzB,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eAC/CnE,IAAA,CAACL,aAAa,EAACsE,KAAK,CAAE,CAAE0B,WAAW,CAAE,KAAK,CAAEpB,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,iCAEpE,EAAO,CAAC,cAGRrE,KAAA,CAAClB,GAAG,EAAC4G,MAAM,CAAE,EAAG,CAAC3B,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eAC/CnE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXnE,IAAA,CAACtB,IAAI,EAAAyF,QAAA,cACHnE,IAAA,CAACd,SAAS,EACRwE,KAAK,CAAC,gCAAO,CACboC,KAAK,CAAE9C,KAAK,CAACC,KAAM,CACnB8C,MAAM,cAAE/F,IAAA,CAACL,aAAa,GAAE,CAAE,CAC1BqG,UAAU,CAAE,CAAEzB,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNvE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXnE,IAAA,CAACtB,IAAI,EAAAyF,QAAA,cACHnE,IAAA,CAACd,SAAS,EACRwE,KAAK,CAAC,oBAAK,CACXoC,KAAK,CAAE9C,KAAK,CAACN,SAAU,CACvBqD,MAAM,cAAE/F,IAAA,CAACJ,mBAAmB,GAAE,CAAE,CAChCoG,UAAU,CAAE,CAAEzB,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNvE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXnE,IAAA,CAACtB,IAAI,EAAAyF,QAAA,cACHnE,IAAA,CAACd,SAAS,EACRwE,KAAK,CAAC,oBAAK,CACXoC,KAAK,CAAE9C,KAAK,CAACK,SAAU,CACvB0C,MAAM,cAAE/F,IAAA,CAACL,aAAa,GAAE,CAAE,CAC1BqG,UAAU,CAAE,CAAEzB,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNvE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXnE,IAAA,CAACtB,IAAI,EAAAyF,QAAA,cACHnE,IAAA,CAACd,SAAS,EACRwE,KAAK,CAAC,0BAAM,CACZoC,KAAK,CAAE9C,KAAK,CAACL,SAAU,CACvBoD,MAAM,cAAE/F,IAAA,CAACH,mBAAmB,GAAE,CAAE,CAChCmG,UAAU,CAAE,CAAEzB,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGNvE,IAAA,CAACtB,IAAI,EAACuF,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,cACpCnE,IAAA,CAAChB,GAAG,EAACiH,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAAA/B,QAAA,cACzCnE,IAAA,CAACf,GAAG,EAAAkF,QAAA,cACFjE,KAAA,CAACrB,KAAK,EAAAsF,QAAA,eACJnE,IAAA,CAACb,KAAK,EACJgH,WAAW,CAAC,4FAAiB,CAC7BJ,MAAM,cAAE/F,IAAA,CAACR,cAAc,GAAE,CAAE,CAC3ByE,KAAK,CAAE,CAAEJ,KAAK,CAAE,GAAI,CAAE,CACvB,CAAC,cACF3D,KAAA,CAACd,MAAM,EAAC+G,WAAW,CAAC,0BAAM,CAAClC,KAAK,CAAE,CAAEJ,KAAK,CAAE,GAAI,CAAE,CAAAM,QAAA,eAC/CnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,EAAE,CAAA3B,QAAA,CAAC,cAAE,CAAQ,CAAC,cAC5BnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,YAAY,CAAA3B,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACvCnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,kBAAkB,CAAA3B,QAAA,CAAC,oBAAG,CAAQ,CAAC,cAC7CnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,WAAW,CAAA3B,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACtCnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,WAAW,CAAA3B,QAAA,CAAC,cAAE,CAAQ,CAAC,EAC/B,CAAC,cACTjE,KAAA,CAACd,MAAM,EAAC+G,WAAW,CAAC,0BAAM,CAAClC,KAAK,CAAE,CAAEJ,KAAK,CAAE,GAAI,CAAE,CAAAM,QAAA,eAC/CnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,EAAE,CAAA3B,QAAA,CAAC,cAAE,CAAQ,CAAC,cAC5BnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,0BAAM,CAAA3B,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAClCnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,0BAAM,CAAA3B,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAClCnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,0BAAM,CAAA3B,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAClCnE,IAAA,CAACK,MAAM,EAACyF,KAAK,CAAC,0BAAM,CAAA3B,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC5B,CAAC,cACTnE,IAAA,CAACM,WAAW,EAAC6F,WAAW,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAAE,CAAE,CAAC,cAClDnG,IAAA,CAACpB,MAAM,EAAC+F,IAAI,cAAE3E,IAAA,CAACR,cAAc,GAAE,CAAE,CAACgG,IAAI,CAAC,SAAS,CAAArB,QAAA,CAAC,cAEjD,CAAQ,CAAC,cACTnE,IAAA,CAACpB,MAAM,EAAC+F,IAAI,cAAE3E,IAAA,CAACP,cAAc,GAAE,CAAE,CAAA0E,QAAA,CAAC,cAElC,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,CACH,CAAC,CACF,CAAC,cAGPnE,IAAA,CAACtB,IAAI,EAAAyF,QAAA,cACHnE,IAAA,CAACrB,KAAK,EACJ8E,OAAO,CAAEA,OAAQ,CACjB2C,UAAU,CAAE3E,MAAO,CACnB4E,MAAM,CAAC,IAAI,CACX1E,OAAO,CAAEA,OAAQ,CACjB2E,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVvD,KAAK,CAAExB,MAAM,CAACyB,MAAM,CACpBuD,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAAC3D,KAAK,CAAE4D,KAAK,aAAA5B,MAAA,CACjB4B,KAAK,CAAC,CAAC,CAAC,MAAA5B,MAAA,CAAI4B,KAAK,CAAC,CAAC,CAAC,oBAAA5B,MAAA,CAAQhC,KAAK,WAC1C,CAAE,CACH,CAAC,CACE,CAAC,cAGPjD,IAAA,CAACV,KAAK,EACJoE,KAAK,CAAC,0BAAM,CACZoD,IAAI,CAAE/E,gBAAiB,CACvBgF,QAAQ,CAAEA,CAAA,GAAM/E,mBAAmB,CAAC,KAAK,CAAE,CAC3CgF,MAAM,CAAE,cACNhH,IAAA,CAACpB,MAAM,EAAa6G,OAAO,CAAEA,CAAA,GAAMzD,mBAAmB,CAAC,KAAK,CAAE,CAAAmC,QAAA,CAAC,cAE/D,EAFY,OAEJ,CAAC,CACT,CACFN,KAAK,CAAE,GAAI,CAAAM,QAAA,CAEVhC,aAAa,eACZjC,KAAA,QAAAiE,QAAA,eAEEnE,IAAA,CAACtB,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACmB,IAAI,CAAC,OAAO,CAACZ,KAAK,CAAE,CAAEO,YAAY,CAAE,EAAG,CAAE,CAAAL,QAAA,cAC1DjE,KAAA,CAAClB,GAAG,EAAC4G,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACdnE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,oBAAG,CAAM,CAAC,cACvBnE,IAAA,CAACI,IAAI,EAAA+D,QAAA,CAAEhC,aAAa,CAACzB,YAAY,CAAO,CAAC,EACtC,CAAC,CACH,CAAC,cACNV,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBnE,IAAA,CAACI,IAAI,EAAC4D,IAAI,MAAAG,QAAA,CAAEhC,aAAa,CAACxB,aAAa,CAAO,CAAC,EAC5C,CAAC,CACH,CAAC,cACNX,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAC4D,IAAI,MAAAG,QAAA,CAAEhC,aAAa,CAACvB,cAAc,CAAO,CAAC,EAC7C,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAGPZ,IAAA,CAACtB,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACmB,IAAI,CAAC,OAAO,CAACZ,KAAK,CAAE,CAAEO,YAAY,CAAE,EAAG,CAAE,CAAAL,QAAA,cAC1DjE,KAAA,CAAClB,GAAG,EAAC4G,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACdnE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,EAAG,CAAA1B,QAAA,cACZjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAA+D,QAAA,CAAEhC,aAAa,CAACtB,WAAW,CAAO,CAAC,EACrC,CAAC,CACH,CAAC,cACNb,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,EAAG,CAAA1B,QAAA,cACZjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBnE,IAAA,CAAClB,GAAG,EAACyF,KAAK,CAAC,MAAM,CAAAJ,QAAA,CAAEhC,aAAa,CAACrB,QAAQ,CAAM,CAAC,EAC7C,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAGPd,IAAA,CAACtB,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACmB,IAAI,CAAC,OAAO,CAACZ,KAAK,CAAE,CAAEO,YAAY,CAAE,EAAG,CAAE,CAAAL,QAAA,cAC1DnE,IAAA,QAAKiE,KAAK,CAAE,CAAEQ,OAAO,CAAE,OAAQ,CAAE,CAAAN,QAAA,cAC/BnE,IAAA,CAACI,IAAI,EAAA+D,QAAA,CAAEhC,aAAa,CAACpB,eAAe,CAAO,CAAC,CACzC,CAAC,CACF,CAAC,cAGPb,KAAA,CAACxB,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACmB,IAAI,CAAC,OAAO,CAAAV,QAAA,eAC7BjE,KAAA,CAAClB,GAAG,EAAC4G,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACdnE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBnE,IAAA,CAACI,IAAI,EAAC4D,IAAI,MAAAG,QAAA,CAAEhC,aAAa,CAAC1B,OAAO,CAAO,CAAC,EACtC,CAAC,CACH,CAAC,cACNT,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAA+D,QAAA,CAAEhC,aAAa,CAACnB,gBAAgB,CAAO,CAAC,EAC1C,CAAC,CACH,CAAC,cACNhB,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAC4D,IAAI,MAAAG,QAAA,CAAEhC,aAAa,CAAClB,cAAc,CAAO,CAAC,EAC7C,CAAC,CACH,CAAC,EACH,CAAC,cACNf,KAAA,CAAClB,GAAG,EAAC4G,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACdnE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAA+D,QAAA,CAAEhC,aAAa,CAACjB,SAAS,CAAO,CAAC,EACnC,CAAC,CACH,CAAC,cACNlB,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAA+D,QAAA,CAAEhC,aAAa,CAAChB,iBAAiB,CAAO,CAAC,EAC3C,CAAC,CACH,CAAC,cACNnB,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,CAAE,CAAA1B,QAAA,cACXjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAAClB,GAAG,EACFyF,KAAK,CAAElC,sBAAsB,CAACF,aAAa,CAACf,cAAc,CAAE,CAC5DuD,IAAI,CAAE7B,qBAAqB,CAACX,aAAa,CAACf,cAAc,CAAE,CAAA+C,QAAA,CAEzDvB,qBAAqB,CAACT,aAAa,CAACf,cAAc,CAAC,CACjD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACNlB,KAAA,CAAClB,GAAG,EAAC4G,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACdnE,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,EAAG,CAAA1B,QAAA,cACZjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAA+D,QAAA,CAAEhC,aAAa,CAACb,eAAe,CAAO,CAAC,EACzC,CAAC,CACH,CAAC,cACNtB,IAAA,CAACf,GAAG,EAAC4G,IAAI,CAAE,EAAG,CAAA1B,QAAA,cACZjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACT,QAAQ,EACPqF,OAAO,CAAEzC,aAAa,CAACd,gBAAiB,CACxCwD,IAAI,CAAC,OAAO,CACZvC,MAAM,CAAEH,aAAa,CAACf,cAAc,GAAK,WAAW,CAAG,WAAW,CAAG,QAAS,CAC/E,CAAC,EACC,CAAC,CACH,CAAC,EACH,CAAC,EACF,CAAC,EACJ,CACN,CACI,CAAC,cAGRpB,IAAA,CAACV,KAAK,EACJoE,KAAK,CAAC,0BAAM,CACZoD,IAAI,CAAE7E,oBAAqB,CAC3B8E,QAAQ,CAAEA,CAAA,GAAM7E,uBAAuB,CAAC,KAAK,CAAE,CAC/C8E,MAAM,CAAE,cACNhH,IAAA,CAACpB,MAAM,EAAa6G,OAAO,CAAEA,CAAA,GAAMvD,uBAAuB,CAAC,KAAK,CAAE,CAAAiC,QAAA,CAAC,cAEnE,EAFY,OAEJ,CAAC,CACT,CACFN,KAAK,CAAE,GAAI,CAAAM,QAAA,CAEVhC,aAAa,eACZjC,KAAA,QAAAiE,QAAA,eAEEnE,IAAA,CAACtB,IAAI,EAACmG,IAAI,CAAC,OAAO,CAACZ,KAAK,CAAE,CAAEO,YAAY,CAAE,EAAG,CAAE,CAAAL,QAAA,cAC7CjE,KAAA,CAAClB,GAAG,EAAC4G,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACdjE,KAAA,CAACjB,GAAG,EAAC4G,IAAI,CAAE,EAAG,CAAA1B,QAAA,eACZjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAA+D,QAAA,CAAEhC,aAAa,CAACnB,gBAAgB,CAAO,CAAC,EAC1C,CAAC,cACNd,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACI,IAAI,EAAC4D,IAAI,MAAAG,QAAA,CAAEhC,aAAa,CAAClB,cAAc,CAAO,CAAC,EAC7C,CAAC,EACH,CAAC,cACNf,KAAA,CAACjB,GAAG,EAAC4G,IAAI,CAAE,EAAG,CAAA1B,QAAA,eACZjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAAClB,GAAG,EACFyF,KAAK,CAAElC,sBAAsB,CAACF,aAAa,CAACf,cAAc,CAAE,CAC5DuD,IAAI,CAAE7B,qBAAqB,CAACX,aAAa,CAACf,cAAc,CAAE,CAAA+C,QAAA,CAEzDvB,qBAAqB,CAACT,aAAa,CAACf,cAAc,CAAC,CACjD,CAAC,EACH,CAAC,cACNlB,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC9BnE,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBnE,IAAA,CAACT,QAAQ,EACPqF,OAAO,CAAEzC,aAAa,CAACd,gBAAiB,CACxCwD,IAAI,CAAC,OAAO,CACZvC,MAAM,CAAEH,aAAa,CAACf,cAAc,GAAK,WAAW,CAAG,WAAW,CAAG,QAAS,CAC/E,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,cAGPpB,IAAA,CAACtB,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACmB,IAAI,CAAC,OAAO,CAAAV,QAAA,cAC7BjE,KAAA,QAAK+D,KAAK,CAAE,CAAEQ,OAAO,CAAE,QAAS,CAAE,CAAAN,QAAA,eAEhCjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eACnCjE,KAAA,QAAK+D,KAAK,CAAE,CAAEiD,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE3C,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,eACzEnE,IAAA,CAACJ,mBAAmB,EAACqE,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEoB,WAAW,CAAE,KAAM,CAAE,CAAE,CAAC,cACxE3F,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,qBAAmB,CAAM,CAAC,EACpC,CAAC,cACNjE,KAAA,QAAK+D,KAAK,CAAE,CAAEmD,UAAU,CAAE,MAAM,CAAE7C,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,EAAC,QAChD,CAAChC,aAAa,CAACnB,gBAAgB,CAAC,8GACnC,EAAK,CAAC,EACH,CAAC,cAENd,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eACnCjE,KAAA,QAAK+D,KAAK,CAAE,CAAEiD,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE3C,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,eACzEnE,IAAA,CAACL,aAAa,EAACsE,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEoB,WAAW,CAAE,KAAM,CAAE,CAAE,CAAC,cAClE3F,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,qBAAmB,CAAM,CAAC,EACpC,CAAC,cACNnE,IAAA,QAAKiE,KAAK,CAAE,CAAEmD,UAAU,CAAE,MAAM,CAAE7C,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,gFAEnD,CAAK,CAAC,EACH,CAAC,cAENjE,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eACnCjE,KAAA,QAAK+D,KAAK,CAAE,CAAEiD,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE3C,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,eACzEnE,IAAA,CAACF,mBAAmB,EAACmE,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEoB,WAAW,CAAE,KAAM,CAAE,CAAE,CAAC,cACxE3F,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,qBAAmB,CAAM,CAAC,EACpC,CAAC,cACNnE,IAAA,QAAKiE,KAAK,CAAE,CAAEmD,UAAU,CAAE,MAAM,CAAE7C,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,kGAEnD,CAAK,CAAC,EACH,CAAC,CAELhC,aAAa,CAACf,cAAc,GAAK,WAAW,eAC3ClB,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eACnCjE,KAAA,QAAK+D,KAAK,CAAE,CAAEiD,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE3C,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,eACzEnE,IAAA,CAACJ,mBAAmB,EAACqE,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEoB,WAAW,CAAE,KAAM,CAAE,CAAE,CAAC,cACxE3F,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,qBAAmB,CAAM,CAAC,EACpC,CAAC,cACNjE,KAAA,QAAK+D,KAAK,CAAE,CAAEmD,UAAU,CAAE,MAAM,CAAE7C,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,EAAC,8DACvC,CAAChC,aAAa,CAACzB,YAAY,EAClC,CAAC,EACH,CACN,CAEAyB,aAAa,CAACf,cAAc,GAAK,WAAW,eAC3ClB,KAAA,QAAK+D,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eACnCjE,KAAA,QAAK+D,KAAK,CAAE,CAAEiD,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE3C,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,eACzEnE,IAAA,CAACH,mBAAmB,EAACoE,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEoB,WAAW,CAAE,KAAM,CAAE,CAAE,CAAC,cACxE3F,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAA9C,QAAA,CAAC,qBAAmB,CAAM,CAAC,EACpC,CAAC,cACNnE,IAAA,QAAKiE,KAAK,CAAE,CAAEmD,UAAU,CAAE,MAAM,CAAE7C,KAAK,CAAE,SAAU,CAAE,CAAAJ,QAAA,CAAC,0HAEtD,CAAK,CAAC,EACH,CACN,EACE,CAAC,CACF,CAAC,EACJ,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
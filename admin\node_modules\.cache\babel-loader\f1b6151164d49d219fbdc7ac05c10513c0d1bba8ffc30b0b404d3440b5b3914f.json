{"ast": null, "code": "import { isCheckDisabled } from \"./valueUtil\";\nexport var SHOW_ALL = 'SHOW_ALL';\nexport var SHOW_PARENT = 'SHOW_PARENT';\nexport var SHOW_CHILD = 'SHOW_CHILD';\nexport function formatStrategyValues(values, strategy, keyEntities, fieldNames) {\n  var valueSet = new Set(values);\n  if (strategy === SHOW_CHILD) {\n    return values.filter(function (key) {\n      var entity = keyEntities[key];\n      return !entity || !entity.children || !entity.children.some(function (_ref) {\n        var node = _ref.node;\n        return valueSet.has(node[fieldNames.value]);\n      }) || !entity.children.every(function (_ref2) {\n        var node = _ref2.node;\n        return isCheckDisabled(node) || valueSet.has(node[fieldNames.value]);\n      });\n    });\n  }\n  if (strategy === SHOW_PARENT) {\n    return values.filter(function (key) {\n      var entity = keyEntities[key];\n      var parent = entity ? entity.parent : null;\n      return !parent || isCheckDisabled(parent.node) || !valueSet.has(parent.key);\n    });\n  }\n  return values;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
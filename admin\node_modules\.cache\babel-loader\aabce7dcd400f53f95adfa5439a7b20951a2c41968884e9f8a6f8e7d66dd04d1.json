{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Tag,Typography,Row,Col,Statistic,Input,Select,DatePicker,Modal,Form,message,Descriptions,Alert}from'antd';import{SearchOutlined,ReloadOutlined,EyeOutlined,RedoOutlined,ExclamationCircleOutlined,CloseCircleOutlined,WarningOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Option}=Select;const{RangePicker}=DatePicker;// 失败订单接口定义\n// 模拟失败订单数据\nconst mockFailedOrders=[{id:1,orderNo:'ORD202401150006',customerName:'孙八',customerPhone:'13800138006',customerIdCard:'510101199408255555',productName:'中国电信天翼套餐',operator:'中国电信',deliveryAddress:'成都市锦江区红星路三段1号IFS国际金融中心2号楼2501室',failureReason:'客户身份信息验证失败，身份证号码与公安系统不匹配',failureType:'identity_verification',failedAt:'2024-01-15 16:30:00',failureDays:2,retryCount:1,canRetry:true,priority:'urgent',processedBy:'admin'},{id:2,orderNo:'ORD202401150008',customerName:'陈十',customerPhone:'13800138008',customerIdCard:'******************',productName:'中国移动5G尊享套餐',operator:'中国移动',deliveryAddress:'深圳市福田区深南大道1000号深圳国际贸易中心大厦A座2001室',failureReason:'信用检查未通过，客户征信记录存在问题',failureType:'credit_check',failedAt:'2024-01-15 14:20:00',failureDays:2.5,retryCount:0,canRetry:false,priority:'high',processedBy:'system'},{id:3,orderNo:'ORD202401150010',customerName:'吴十二',customerPhone:'13800138010',customerIdCard:'310101198812123333',productName:'中国联通青春套餐',operator:'中国联通',deliveryAddress:'上海市黄浦区南京东路100号黄浦中心大厦30楼',failureReason:'系统错误，运营商接口调用超时',failureType:'system_error',failedAt:'2024-01-15 18:45:00',failureDays:1.8,retryCount:2,canRetry:true,priority:'normal',processedBy:'auto_system'},{id:4,orderNo:'ORD202401150012',customerName:'冯十四',customerPhone:'13800138012',customerIdCard:'320101199205154444',productName:'中国广电智慧套餐',operator:'中国广电',deliveryAddress:'南京市建邺区江东中路369号新城科技园A座1501室',failureReason:'上传的身份证照片不清晰，无法识别关键信息',failureType:'document_invalid',failedAt:'2024-01-15 11:15:00',failureDays:3.2,retryCount:0,canRetry:true,priority:'normal',processedBy:'reviewer'}];const OrderFailed=()=>{const[orders,setOrders]=useState([]);const[loading,setLoading]=useState(false);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[viewModalVisible,setViewModalVisible]=useState(false);const[retryModalVisible,setRetryModalVisible]=useState(false);const[selectedOrder,setSelectedOrder]=useState(null);const[form]=Form.useForm();// 页面加载时获取数据\nuseEffect(()=>{handleRefresh();},[]);// 转换API数据格式为前端格式\nconst transformApiData=apiData=>{return{id:apiData.id,orderNo:apiData.order_no,customerName:apiData.customer_name,customerPhone:apiData.customer_phone,customerIdCard:apiData.customer_id_card,productName:apiData.product_name,operator:apiData.operator,deliveryAddress:apiData.delivery_address,failureReason:apiData.process_notes||apiData.reject_reason||'开卡失败',failureType:'other',// 默认其他原因，实际项目中应该根据失败原因分类\nfailedAt:apiData.updated_at?new Date(apiData.updated_at).toLocaleString('zh-CN'):'',failureDays:apiData.updated_at?Math.max(0.1,(new Date().getTime()-new Date(apiData.updated_at).getTime())/(1000*60*60*24)):0.1,retryCount:0,// 默认0次重试，实际项目中应该从数据库获取\ncanRetry:true,// 默认可重试，实际项目中应该根据失败原因判断\npriority:apiData.priority||'normal',processedBy:apiData.processed_by||'system'};};// 刷新数据\nconst handleRefresh=async()=>{setLoading(true);try{const response=await fetch('http://localhost:8000/api/v1/orders?status=failed',{method:'GET',headers:{'Accept':'application/json'}});const result=await response.json();if(result.code===200){// 转换API数据格式\nconst transformedOrders=result.data.list.map(transformApiData);setOrders(transformedOrders);message.success(\"\\u6570\\u636E\\u5DF2\\u5237\\u65B0\\uFF0C\\u83B7\\u53D6\\u5230 \".concat(transformedOrders.length,\" \\u4E2A\\u5931\\u8D25\\u8BA2\\u5355\"));}else{message.error(result.message||'刷新失败');}}catch(error){console.error('刷新失败:',error);message.error('网络错误，请稍后重试');}finally{setLoading(false);}};// 获取失败类型颜色\nconst getFailureTypeColor=type=>{const colors={identity_verification:'red',credit_check:'orange',system_error:'purple',document_invalid:'volcano',other:'default'};return colors[type]||'default';};// 获取失败类型文本\nconst getFailureTypeText=type=>{const texts={identity_verification:'身份验证失败',credit_check:'信用检查失败',system_error:'系统错误',document_invalid:'证件无效',other:'其他原因'};return texts[type]||type;};// 获取优先级颜色\nconst getPriorityColor=priority=>{const colors={low:'default',normal:'blue',high:'orange',urgent:'red'};return colors[priority]||'default';};// 获取优先级文本\nconst getPriorityText=priority=>{const texts={low:'低',normal:'普通',high:'高',urgent:'紧急'};return texts[priority]||priority;};// 统计数据\nconst stats={total:orders.length,canRetry:orders.filter(order=>order.canRetry).length,identityFailed:orders.filter(order=>order.failureType==='identity_verification').length,systemError:orders.filter(order=>order.failureType==='system_error').length,urgent:orders.filter(order=>order.priority==='urgent').length};// 查看订单详情\nconst handleViewOrder=order=>{setSelectedOrder(order);setViewModalVisible(true);};// 重试订单\nconst handleRetryOrder=order=>{if(!order.canRetry){message.warning('该订单不支持重试');return;}setSelectedOrder(order);setRetryModalVisible(true);form.resetFields();};// 提交重试\nconst handleSubmitRetry=async()=>{try{const values=await form.validateFields();if(!selectedOrder){message.error('未选择订单');return;}// 调用API将订单状态改回pending\nconst response=await fetch(\"http://localhost:8000/api/v1/orders/\".concat(selectedOrder.id,\"/status\"),{method:'PATCH',headers:{'Accept':'application/json','Content-Type':'application/json'},body:JSON.stringify({status:'pending',process_notes:\"\\u91CD\\u8BD5\\u8BA2\\u5355\\uFF1A\".concat(values.retryReason),processed_by:'admin'})});const result=await response.json();if(result.code===200){message.success('订单重试成功，已转入待处理队列');// 从失败订单列表移除\nsetOrders(orders.filter(order=>order.id!==selectedOrder.id));setRetryModalVisible(false);setSelectedOrder(null);form.resetFields();}else{message.error(result.message||'重试失败');}}catch(error){console.error('重试提交失败:',error);message.error('网络错误，请稍后重试');}};// 批量重试\nconst handleBatchRetry=()=>{if(selectedRowKeys.length===0){message.warning('请选择要重试的订单');return;}const retryableOrders=orders.filter(order=>selectedRowKeys.includes(order.id)&&order.canRetry);if(retryableOrders.length===0){message.warning('选中的订单都不支持重试');return;}Modal.confirm({title:'批量重试订单',content:\"\\u786E\\u5B9A\\u8981\\u91CD\\u8BD5\\u9009\\u4E2D\\u7684 \".concat(retryableOrders.length,\" \\u4E2A\\u8BA2\\u5355\\u5417\\uFF1F\"),onOk:()=>{setOrders(orders.filter(order=>!selectedRowKeys.includes(order.id)));setSelectedRowKeys([]);message.success(\"\\u6210\\u529F\\u91CD\\u8BD5 \".concat(retryableOrders.length,\" \\u4E2A\\u8BA2\\u5355\"));}});};// 表格列定义\nconst columns=[{title:'优先级',dataIndex:'priority',key:'priority',width:80,render:priority=>/*#__PURE__*/_jsx(Tag,{color:getPriorityColor(priority),children:getPriorityText(priority)}),sorter:(a,b)=>{const priorityOrder={urgent:4,high:3,normal:2,low:1};return priorityOrder[a.priority]-priorityOrder[b.priority];}},{title:'订单号',dataIndex:'orderNo',key:'orderNo',width:160,render:text=>/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:'12px'},children:text})},{title:'客户信息',key:'customer',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600,fontSize:'13px'},children:record.customerName}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#666'},children:record.customerPhone})]})},{title:'产品信息',key:'product',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',marginBottom:'2px'},children:record.productName}),/*#__PURE__*/_jsx(Tag,{color:\"blue\",style:{fontSize:'11px',padding:'1px 6px'},children:record.operator})]})},{title:'失败原因',key:'failure',width:250,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'4px'},children:/*#__PURE__*/_jsx(Tag,{color:getFailureTypeColor(record.failureType),icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{}),children:getFailureTypeText(record.failureType)})}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'11px',color:'#666',lineHeight:1.4},children:record.failureReason})]})},{title:'重试信息',key:'retry',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',marginBottom:'2px'},children:[\"\\u91CD\\u8BD5\\u6B21\\u6570: \",record.retryCount]}),/*#__PURE__*/_jsx(\"div\",{children:record.canRetry?/*#__PURE__*/_jsx(Tag,{color:\"green\",icon:/*#__PURE__*/_jsx(RedoOutlined,{}),children:\"\\u53EF\\u91CD\\u8BD5\"}):/*#__PURE__*/_jsx(Tag,{color:\"red\",icon:/*#__PURE__*/_jsx(CloseCircleOutlined,{}),children:\"\\u4E0D\\u53EF\\u91CD\\u8BD5\"})})]})},{title:'失败时长',dataIndex:'failureDays',key:'failureDays',width:100,render:days=>/*#__PURE__*/_jsx(Text,{style:{color:days>3?'#f5222d':'#fa8c16'},children:days<1?\"\".concat(Math.round(days*24),\"\\u5C0F\\u65F6\"):\"\".concat(days.toFixed(1),\"\\u5929\")}),sorter:(a,b)=>a.failureDays-b.failureDays},{title:'失败时间',dataIndex:'failedAt',key:'failedAt',width:150,render:text=>/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px'},children:text})},{title:'操作',key:'action',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewOrder(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(RedoOutlined,{}),disabled:!record.canRetry,onClick:()=>handleRetryOrder(record),children:\"\\u91CD\\u8BD5\"})]})}];return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px'},children:[/*#__PURE__*/_jsxs(Title,{level:2,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(CloseCircleOutlined,{style:{marginRight:'8px',color:'#f5222d'}}),\"\\u5931\\u8D25\\u8BA2\\u5355\"]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5931\\u8D25\\u8BA2\\u5355\\u603B\\u6570\",value:stats.total,prefix:/*#__PURE__*/_jsx(CloseCircleOutlined,{}),valueStyle:{color:'#f5222d'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u53EF\\u91CD\\u8BD5\\u8BA2\\u5355\",value:stats.canRetry,prefix:/*#__PURE__*/_jsx(RedoOutlined,{}),valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5931\\u8D25\",value:stats.identityFailed,prefix:/*#__PURE__*/_jsx(WarningOutlined,{}),valueStyle:{color:'#fa8c16'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7D27\\u6025\\u5904\\u7406\",value:stats.urgent,prefix:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{}),valueStyle:{color:'#722ed1'}})})})]}),/*#__PURE__*/_jsx(Card,{style:{marginBottom:'16px'},children:/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Input,{placeholder:\"\\u641C\\u7D22\\u8BA2\\u5355\\u53F7\\u3001\\u5BA2\\u6237\\u59D3\\u540D\",prefix:/*#__PURE__*/_jsx(SearchOutlined,{}),style:{width:250}}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u5931\\u8D25\\u7C7B\\u578B\",style:{width:140},children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"\\u5168\\u90E8\"}),/*#__PURE__*/_jsx(Option,{value:\"identity_verification\",children:\"\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5931\\u8D25\"}),/*#__PURE__*/_jsx(Option,{value:\"credit_check\",children:\"\\u4FE1\\u7528\\u68C0\\u67E5\\u5931\\u8D25\"}),/*#__PURE__*/_jsx(Option,{value:\"system_error\",children:\"\\u7CFB\\u7EDF\\u9519\\u8BEF\"}),/*#__PURE__*/_jsx(Option,{value:\"document_invalid\",children:\"\\u8BC1\\u4EF6\\u65E0\\u6548\"}),/*#__PURE__*/_jsx(Option,{value:\"other\",children:\"\\u5176\\u4ED6\\u539F\\u56E0\"})]}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u662F\\u5426\\u53EF\\u91CD\\u8BD5\",style:{width:120},children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"\\u5168\\u90E8\"}),/*#__PURE__*/_jsx(Option,{value:\"true\",children:\"\\u53EF\\u91CD\\u8BD5\"}),/*#__PURE__*/_jsx(Option,{value:\"false\",children:\"\\u4E0D\\u53EF\\u91CD\\u8BD5\"})]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(SearchOutlined,{}),type:\"primary\",children:\"\\u641C\\u7D22\"})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsxs(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(RedoOutlined,{}),onClick:handleBatchRetry,disabled:selectedRowKeys.length===0,children:[\"\\u6279\\u91CF\\u91CD\\u8BD5 (\",selectedRowKeys.length,\")\"]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:handleRefresh,children:\"\\u5237\\u65B0\"})]})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:orders,rowKey:\"id\",loading:loading,scroll:{x:1500},rowSelection:{selectedRowKeys,onChange:setSelectedRowKeys,getCheckboxProps:record=>({disabled:!record.canRetry// 不可重试的订单不允许选择\n})},pagination:{total:orders.length,pageSize:10,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5931\\u8D25\\u8BA2\\u5355\\u8BE6\\u60C5\",open:viewModalVisible,onCancel:()=>setViewModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setViewModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u8BA2\\u5355\\u5904\\u7406\\u5931\\u8D25\",description:selectedOrder.failureReason,type:\"error\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Card,{title:\"\\u5BA2\\u6237\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u59D3\\u540D\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.customerName})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u624B\\u673A\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.customerPhone})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8EAB\\u4EFD\\u8BC1\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.customerIdCard})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u4EA7\\u54C1\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4EA7\\u54C1\\u540D\\u79F0\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.productName})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8FD0\\u8425\\u5546\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u6536\\u8D27\\u5730\\u5740\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsx(\"div\",{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(Text,{children:selectedOrder.deliveryAddress})})}),/*#__PURE__*/_jsxs(Card,{title:\"\\u5931\\u8D25\\u4FE1\\u606F\",size:\"small\",children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BA2\\u5355\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.orderNo})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5931\\u8D25\\u7C7B\\u578B\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:getFailureTypeColor(selectedOrder.failureType),children:getFailureTypeText(selectedOrder.failureType)})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4F18\\u5148\\u7EA7\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:getPriorityColor(selectedOrder.priority),children:getPriorityText(selectedOrder.priority)})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5931\\u8D25\\u65F6\\u95F4\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.failedAt})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u91CD\\u8BD5\\u6B21\\u6570\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.retryCount})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5904\\u7406\\u4EBA\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.processedBy})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u662F\\u5426\\u53EF\\u91CD\\u8BD5\\uFF1A\"}),selectedOrder.canRetry?/*#__PURE__*/_jsx(Tag,{color:\"green\",icon:/*#__PURE__*/_jsx(RedoOutlined,{}),children:\"\\u53EF\\u91CD\\u8BD5\"}):/*#__PURE__*/_jsx(Tag,{color:\"red\",icon:/*#__PURE__*/_jsx(CloseCircleOutlined,{}),children:\"\\u4E0D\\u53EF\\u91CD\\u8BD5\"})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5931\\u8D25\\u65F6\\u957F\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{style:{color:selectedOrder.failureDays>3?'#f5222d':'#fa8c16'},children:selectedOrder.failureDays<1?\"\".concat(Math.round(selectedOrder.failureDays*24),\"\\u5C0F\\u65F6\"):\"\".concat(selectedOrder.failureDays.toFixed(1),\"\\u5929\")})]})})]}),/*#__PURE__*/_jsx(Row,{gutter:16,children:/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5931\\u8D25\\u539F\\u56E0\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4,color:'#666',lineHeight:1.5,padding:'8px',background:'#fff2f0',borderRadius:'4px'},children:selectedOrder.failureReason})]})})})]})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u91CD\\u8BD5\\u8BA2\\u5355\",open:retryModalVisible,onCancel:()=>setRetryModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setRetryModalVisible(false),children:\"\\u53D6\\u6D88\"},\"cancel\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:handleSubmitRetry,children:\"\\u786E\\u8BA4\\u91CD\\u8BD5\"},\"submit\")],width:600,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{title:\"\\u8BA2\\u5355\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Descriptions,{column:2,size:\"small\",children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u53F7\",children:selectedOrder.orderNo}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:selectedOrder.customerName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u624B\\u673A\\u53F7\",children:selectedOrder.customerPhone}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u540D\\u79F0\",children:selectedOrder.productName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u91CD\\u8BD5\\u6B21\\u6570\",children:selectedOrder.retryCount})]}),/*#__PURE__*/_jsx(Descriptions,{column:1,size:\"small\",children:/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5931\\u8D25\\u539F\\u56E0\",children:/*#__PURE__*/_jsx(\"div\",{style:{color:'#f5222d'},children:selectedOrder.failureReason})})})]}),/*#__PURE__*/_jsxs(Card,{title:\"\\u91CD\\u8BD5\\u8BF4\\u660E\",size:\"small\",children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u91CD\\u8BD5\\u6CE8\\u610F\\u4E8B\\u9879\",description:/*#__PURE__*/_jsxs(\"ul\",{style:{margin:0,paddingLeft:'20px'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u91CD\\u8BD5\\u524D\\u8BF7\\u786E\\u8BA4\\u5931\\u8D25\\u539F\\u56E0\\u5DF2\\u89E3\\u51B3\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5931\\u8D25\\uFF1A\\u8BF7\\u786E\\u8BA4\\u5BA2\\u6237\\u4FE1\\u606F\\u51C6\\u786E\\u65E0\\u8BEF\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u7CFB\\u7EDF\\u9519\\u8BEF\\uFF1A\\u7CFB\\u7EDF\\u5DF2\\u4FEE\\u590D\\uFF0C\\u53EF\\u76F4\\u63A5\\u91CD\\u8BD5\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u8BC1\\u4EF6\\u65E0\\u6548\\uFF1A\\u8BF7\\u8054\\u7CFB\\u5BA2\\u6237\\u91CD\\u65B0\\u4E0A\\u4F20\\u6E05\\u6670\\u8BC1\\u4EF6\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u91CD\\u8BD5\\u540E\\u8BA2\\u5355\\u5C06\\u91CD\\u65B0\\u8FDB\\u5165\\u5904\\u7406\\u6D41\\u7A0B\"})]}),type:\"info\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Form,{form:form,layout:\"vertical\",children:/*#__PURE__*/_jsx(Form.Item,{name:\"retryReason\",label:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u91CD\\u8BD5\\u539F\\u56E0\"}),rules:[{required:true,message:'请填写重试原因'}],children:/*#__PURE__*/_jsx(Input.TextArea,{rows:3,placeholder:\"\\u8BF7\\u8BF4\\u660E\\u91CD\\u8BD5\\u7684\\u539F\\u56E0\\u548C\\u5DF2\\u91C7\\u53D6\\u7684\\u89E3\\u51B3\\u63AA\\u65BD...\",maxLength:300,showCount:true})})})]})]})})]});};export default OrderFailed;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Tag", "Typography", "Row", "Col", "Statistic", "Input", "Select", "DatePicker", "Modal", "Form", "message", "Descriptions", "<PERSON><PERSON>", "SearchOutlined", "ReloadOutlined", "EyeOutlined", "RedoOutlined", "ExclamationCircleOutlined", "CloseCircleOutlined", "WarningOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Option", "RangePicker", "mockFailedOrders", "id", "orderNo", "customerName", "customerPhone", "customerIdCard", "productName", "operator", "deliveryAddress", "failureReason", "failureType", "failedAt", "failureDays", "retryCount", "canRetry", "priority", "processedBy", "OrderFailed", "orders", "setOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "viewModalVisible", "setViewModalVisible", "retryModalVisible", "setRetryModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "form", "useForm", "handleRefresh", "transformApiData", "apiData", "order_no", "customer_name", "customer_phone", "customer_id_card", "product_name", "delivery_address", "process_notes", "reject_reason", "updated_at", "Date", "toLocaleString", "Math", "max", "getTime", "processed_by", "response", "fetch", "method", "headers", "result", "json", "code", "transformedOrders", "data", "list", "map", "success", "concat", "length", "error", "console", "getFailureTypeColor", "type", "colors", "identity_verification", "credit_check", "system_error", "document_invalid", "other", "getFailureTypeText", "texts", "getPriorityColor", "low", "normal", "high", "urgent", "getPriorityText", "stats", "total", "filter", "order", "identityFailed", "systemError", "handleViewOrder", "handleRetryOrder", "warning", "resetFields", "handleSubmitRetry", "values", "validateFields", "body", "JSON", "stringify", "status", "retryReason", "handleBatchRetry", "retryableOrders", "includes", "confirm", "title", "content", "onOk", "columns", "dataIndex", "key", "width", "render", "color", "children", "sorter", "a", "b", "priorityOrder", "text", "style", "fontSize", "_", "record", "fontWeight", "marginBottom", "padding", "icon", "lineHeight", "days", "round", "toFixed", "size", "onClick", "disabled", "level", "marginRight", "gutter", "span", "value", "prefix", "valueStyle", "justify", "align", "placeholder", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "rowSelection", "onChange", "getCheckboxProps", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "footer", "description", "showIcon", "strong", "marginTop", "background", "borderRadius", "column", "<PERSON><PERSON>", "label", "margin", "paddingLeft", "layout", "name", "rules", "required", "TextArea", "rows", "max<PERSON><PERSON><PERSON>", "showCount"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderFailed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Input,\n  Select,\n  DatePicker,\n  Modal,\n  Form,\n  message,\n  Descriptions,\n  Divider,\n  Alert,\n} from 'antd';\nimport {\n  SearchOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n  RedoOutlined,\n  ExclamationCircleOutlined,\n  CloseCircleOutlined,\n  WarningOutlined,\n  PhoneOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\n// 失败订单接口定义\ninterface FailedOrder {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  customerIdCard: string;\n  productName: string;\n  operator: string;\n  deliveryAddress: string;\n  failureReason: string;\n  failureType: 'identity_verification' | 'credit_check' | 'system_error' | 'document_invalid' | 'other';\n  failedAt: string;\n  failureDays: number; // 失败天数\n  retryCount: number; // 重试次数\n  canRetry: boolean; // 是否可以重试\n  priority: 'low' | 'normal' | 'high' | 'urgent';\n  processedBy: string;\n}\n\n// 模拟失败订单数据\nconst mockFailedOrders: FailedOrder[] = [\n  {\n    id: 1,\n    orderNo: 'ORD202401150006',\n    customerName: '孙八',\n    customerPhone: '13800138006',\n    customerIdCard: '510101199408255555',\n    productName: '中国电信天翼套餐',\n    operator: '中国电信',\n    deliveryAddress: '成都市锦江区红星路三段1号IFS国际金融中心2号楼2501室',\n    failureReason: '客户身份信息验证失败，身份证号码与公安系统不匹配',\n    failureType: 'identity_verification',\n    failedAt: '2024-01-15 16:30:00',\n    failureDays: 2,\n    retryCount: 1,\n    canRetry: true,\n    priority: 'urgent',\n    processedBy: 'admin',\n  },\n  {\n    id: 2,\n    orderNo: 'ORD202401150008',\n    customerName: '陈十',\n    customerPhone: '13800138008',\n    customerIdCard: '******************',\n    productName: '中国移动5G尊享套餐',\n    operator: '中国移动',\n    deliveryAddress: '深圳市福田区深南大道1000号深圳国际贸易中心大厦A座2001室',\n    failureReason: '信用检查未通过，客户征信记录存在问题',\n    failureType: 'credit_check',\n    failedAt: '2024-01-15 14:20:00',\n    failureDays: 2.5,\n    retryCount: 0,\n    canRetry: false,\n    priority: 'high',\n    processedBy: 'system',\n  },\n  {\n    id: 3,\n    orderNo: 'ORD202401150010',\n    customerName: '吴十二',\n    customerPhone: '13800138010',\n    customerIdCard: '310101198812123333',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n    deliveryAddress: '上海市黄浦区南京东路100号黄浦中心大厦30楼',\n    failureReason: '系统错误，运营商接口调用超时',\n    failureType: 'system_error',\n    failedAt: '2024-01-15 18:45:00',\n    failureDays: 1.8,\n    retryCount: 2,\n    canRetry: true,\n    priority: 'normal',\n    processedBy: 'auto_system',\n  },\n  {\n    id: 4,\n    orderNo: 'ORD202401150012',\n    customerName: '冯十四',\n    customerPhone: '13800138012',\n    customerIdCard: '320101199205154444',\n    productName: '中国广电智慧套餐',\n    operator: '中国广电',\n    deliveryAddress: '南京市建邺区江东中路369号新城科技园A座1501室',\n    failureReason: '上传的身份证照片不清晰，无法识别关键信息',\n    failureType: 'document_invalid',\n    failedAt: '2024-01-15 11:15:00',\n    failureDays: 3.2,\n    retryCount: 0,\n    canRetry: true,\n    priority: 'normal',\n    processedBy: 'reviewer',\n  },\n];\n\nconst OrderFailed: React.FC = () => {\n  const [orders, setOrders] = useState<FailedOrder[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n  const [retryModalVisible, setRetryModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<FailedOrder | null>(null);\n  const [form] = Form.useForm();\n\n  // 页面加载时获取数据\n  useEffect(() => {\n    handleRefresh();\n  }, []);\n\n  // 转换API数据格式为前端格式\n  const transformApiData = (apiData: any): FailedOrder => {\n    return {\n      id: apiData.id,\n      orderNo: apiData.order_no,\n      customerName: apiData.customer_name,\n      customerPhone: apiData.customer_phone,\n      customerIdCard: apiData.customer_id_card,\n      productName: apiData.product_name,\n      operator: apiData.operator,\n      deliveryAddress: apiData.delivery_address,\n      failureReason: apiData.process_notes || apiData.reject_reason || '开卡失败',\n      failureType: 'other', // 默认其他原因，实际项目中应该根据失败原因分类\n      failedAt: apiData.updated_at ? new Date(apiData.updated_at).toLocaleString('zh-CN') : '',\n      failureDays: apiData.updated_at ?\n        Math.max(0.1, (new Date().getTime() - new Date(apiData.updated_at).getTime()) / (1000 * 60 * 60 * 24)) : 0.1,\n      retryCount: 0, // 默认0次重试，实际项目中应该从数据库获取\n      canRetry: true, // 默认可重试，实际项目中应该根据失败原因判断\n      priority: apiData.priority || 'normal',\n      processedBy: apiData.processed_by || 'system',\n    };\n  };\n\n  // 刷新数据\n  const handleRefresh = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/orders?status=failed', {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      const result = await response.json();\n\n      if (result.code === 200) {\n        // 转换API数据格式\n        const transformedOrders = result.data.list.map(transformApiData);\n        setOrders(transformedOrders);\n        message.success(`数据已刷新，获取到 ${transformedOrders.length} 个失败订单`);\n      } else {\n        message.error(result.message || '刷新失败');\n      }\n    } catch (error) {\n      console.error('刷新失败:', error);\n      message.error('网络错误，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取失败类型颜色\n  const getFailureTypeColor = (type: string) => {\n    const colors = {\n      identity_verification: 'red',\n      credit_check: 'orange',\n      system_error: 'purple',\n      document_invalid: 'volcano',\n      other: 'default',\n    };\n    return colors[type as keyof typeof colors] || 'default';\n  };\n\n  // 获取失败类型文本\n  const getFailureTypeText = (type: string) => {\n    const texts = {\n      identity_verification: '身份验证失败',\n      credit_check: '信用检查失败',\n      system_error: '系统错误',\n      document_invalid: '证件无效',\n      other: '其他原因',\n    };\n    return texts[type as keyof typeof texts] || type;\n  };\n\n  // 获取优先级颜色\n  const getPriorityColor = (priority: string) => {\n    const colors = {\n      low: 'default',\n      normal: 'blue',\n      high: 'orange',\n      urgent: 'red',\n    };\n    return colors[priority as keyof typeof colors] || 'default';\n  };\n\n  // 获取优先级文本\n  const getPriorityText = (priority: string) => {\n    const texts = {\n      low: '低',\n      normal: '普通',\n      high: '高',\n      urgent: '紧急',\n    };\n    return texts[priority as keyof typeof texts] || priority;\n  };\n\n  // 统计数据\n  const stats = {\n    total: orders.length,\n    canRetry: orders.filter(order => order.canRetry).length,\n    identityFailed: orders.filter(order => order.failureType === 'identity_verification').length,\n    systemError: orders.filter(order => order.failureType === 'system_error').length,\n    urgent: orders.filter(order => order.priority === 'urgent').length,\n  };\n\n  // 查看订单详情\n  const handleViewOrder = (order: FailedOrder) => {\n    setSelectedOrder(order);\n    setViewModalVisible(true);\n  };\n\n  // 重试订单\n  const handleRetryOrder = (order: FailedOrder) => {\n    if (!order.canRetry) {\n      message.warning('该订单不支持重试');\n      return;\n    }\n    setSelectedOrder(order);\n    setRetryModalVisible(true);\n    form.resetFields();\n  };\n\n  // 提交重试\n  const handleSubmitRetry = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (!selectedOrder) {\n        message.error('未选择订单');\n        return;\n      }\n\n      // 调用API将订单状态改回pending\n      const response = await fetch(`http://localhost:8000/api/v1/orders/${selectedOrder.id}/status`, {\n        method: 'PATCH',\n        headers: {\n          'Accept': 'application/json',\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          status: 'pending',\n          process_notes: `重试订单：${values.retryReason}`,\n          processed_by: 'admin'\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.code === 200) {\n        message.success('订单重试成功，已转入待处理队列');\n\n        // 从失败订单列表移除\n        setOrders(orders.filter(order => order.id !== selectedOrder.id));\n        setRetryModalVisible(false);\n        setSelectedOrder(null);\n        form.resetFields();\n      } else {\n        message.error(result.message || '重试失败');\n      }\n    } catch (error) {\n      console.error('重试提交失败:', error);\n      message.error('网络错误，请稍后重试');\n    }\n  };\n\n  // 批量重试\n  const handleBatchRetry = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要重试的订单');\n      return;\n    }\n\n    const retryableOrders = orders.filter(order => \n      selectedRowKeys.includes(order.id) && order.canRetry\n    );\n\n    if (retryableOrders.length === 0) {\n      message.warning('选中的订单都不支持重试');\n      return;\n    }\n\n    Modal.confirm({\n      title: '批量重试订单',\n      content: `确定要重试选中的 ${retryableOrders.length} 个订单吗？`,\n      onOk: () => {\n        setOrders(orders.filter(order => !selectedRowKeys.includes(order.id)));\n        setSelectedRowKeys([]);\n        message.success(`成功重试 ${retryableOrders.length} 个订单`);\n      },\n    });\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<FailedOrder> = [\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      width: 80,\n      render: (priority: string) => (\n        <Tag color={getPriorityColor(priority)}>\n          {getPriorityText(priority)}\n        </Tag>\n      ),\n      sorter: (a, b) => {\n        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };\n        return priorityOrder[a.priority as keyof typeof priorityOrder] - \n               priorityOrder[b.priority as keyof typeof priorityOrder];\n      },\n    },\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>{text}</Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600, fontSize: '13px' }}>\n            {record.customerName}\n          </div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品信息',\n      key: 'product',\n      width: 200,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontSize: '12px', marginBottom: '2px' }}>\n            {record.productName}\n          </div>\n          <Tag color=\"blue\" style={{ fontSize: '11px', padding: '1px 6px' }}>{record.operator}</Tag>\n        </div>\n      ),\n    },\n    {\n      title: '失败原因',\n      key: 'failure',\n      width: 250,\n      render: (_, record) => (\n        <div>\n          <div style={{ marginBottom: '4px' }}>\n            <Tag \n              color={getFailureTypeColor(record.failureType)}\n              icon={<ExclamationCircleOutlined />}\n            >\n              {getFailureTypeText(record.failureType)}\n            </Tag>\n          </div>\n          <div style={{ fontSize: '11px', color: '#666', lineHeight: 1.4 }}>\n            {record.failureReason}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '重试信息',\n      key: 'retry',\n      width: 120,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontSize: '12px', marginBottom: '2px' }}>\n            重试次数: {record.retryCount}\n          </div>\n          <div>\n            {record.canRetry ? (\n              <Tag color=\"green\" icon={<RedoOutlined />}>可重试</Tag>\n            ) : (\n              <Tag color=\"red\" icon={<CloseCircleOutlined />}>不可重试</Tag>\n            )}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '失败时长',\n      dataIndex: 'failureDays',\n      key: 'failureDays',\n      width: 100,\n      render: (days: number) => (\n        <Text style={{ color: days > 3 ? '#f5222d' : '#fa8c16' }}>\n          {days < 1 ? `${Math.round(days * 24)}小时` : `${days.toFixed(1)}天`}\n        </Text>\n      ),\n      sorter: (a, b) => a.failureDays - b.failureDays,\n    },\n    {\n      title: '失败时间',\n      dataIndex: 'failedAt',\n      key: 'failedAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<RedoOutlined />}\n            disabled={!record.canRetry}\n            onClick={() => handleRetryOrder(record)}\n          >\n            重试\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2} style={{ marginBottom: '24px' }}>\n        <CloseCircleOutlined style={{ marginRight: '8px', color: '#f5222d' }} />\n        失败订单\n      </Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"失败订单总数\"\n              value={stats.total}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"可重试订单\"\n              value={stats.canRetry}\n              prefix={<RedoOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"身份验证失败\"\n              value={stats.identityFailed}\n              prefix={<WarningOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"紧急处理\"\n              value={stats.urgent}\n              prefix={<ExclamationCircleOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: '16px' }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Input\n                placeholder=\"搜索订单号、客户姓名\"\n                prefix={<SearchOutlined />}\n                style={{ width: 250 }}\n              />\n              <Select placeholder=\"失败类型\" style={{ width: 140 }}>\n                <Option value=\"\">全部</Option>\n                <Option value=\"identity_verification\">身份验证失败</Option>\n                <Option value=\"credit_check\">信用检查失败</Option>\n                <Option value=\"system_error\">系统错误</Option>\n                <Option value=\"document_invalid\">证件无效</Option>\n                <Option value=\"other\">其他原因</Option>\n              </Select>\n              <Select placeholder=\"是否可重试\" style={{ width: 120 }}>\n                <Option value=\"\">全部</Option>\n                <Option value=\"true\">可重试</Option>\n                <Option value=\"false\">不可重试</Option>\n              </Select>\n              <Button icon={<SearchOutlined />} type=\"primary\">\n                搜索\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<RedoOutlined />}\n                onClick={handleBatchRetry}\n                disabled={selectedRowKeys.length === 0}\n              >\n                批量重试 ({selectedRowKeys.length})\n              </Button>\n              <Button icon={<ReloadOutlined />} onClick={handleRefresh}>\n                刷新\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 失败订单表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={orders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1500 }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n            getCheckboxProps: (record) => ({\n              disabled: !record.canRetry, // 不可重试的订单不允许选择\n            }),\n          }}\n          pagination={{\n            total: orders.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 查看订单详情弹窗 */}\n      <Modal\n        title=\"失败订单详情\"\n        open={viewModalVisible}\n        onCancel={() => setViewModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setViewModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={800}\n      >\n        {selectedOrder && (\n          <div>\n            {/* 失败提醒 */}\n            <Alert\n              message=\"订单处理失败\"\n              description={selectedOrder.failureReason}\n              type=\"error\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n\n            {/* 客户信息 */}\n            <Card title=\"客户信息\" size=\"small\" style={{ marginBottom: 16 }}>\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>姓名：</Text>\n                    <Text>{selectedOrder.customerName}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>手机号：</Text>\n                    <Text code>{selectedOrder.customerPhone}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>身份证号：</Text>\n                    <Text code>{selectedOrder.customerIdCard}</Text>\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n\n            {/* 产品信息 */}\n            <Card title=\"产品信息\" size=\"small\" style={{ marginBottom: 16 }}>\n              <Row gutter={16}>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>产品名称：</Text>\n                    <Text>{selectedOrder.productName}</Text>\n                  </div>\n                </Col>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>运营商：</Text>\n                    <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n\n            {/* 收货地址 */}\n            <Card title=\"收货地址\" size=\"small\" style={{ marginBottom: 16 }}>\n              <div style={{ padding: '8px 0' }}>\n                <Text>{selectedOrder.deliveryAddress}</Text>\n              </div>\n            </Card>\n\n            {/* 失败信息 */}\n            <Card title=\"失败信息\" size=\"small\">\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>订单号：</Text>\n                    <Text code>{selectedOrder.orderNo}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>失败类型：</Text>\n                    <Tag color={getFailureTypeColor(selectedOrder.failureType)}>\n                      {getFailureTypeText(selectedOrder.failureType)}\n                    </Tag>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>优先级：</Text>\n                    <Tag color={getPriorityColor(selectedOrder.priority)}>\n                      {getPriorityText(selectedOrder.priority)}\n                    </Tag>\n                  </div>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>失败时间：</Text>\n                    <Text>{selectedOrder.failedAt}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>重试次数：</Text>\n                    <Text>{selectedOrder.retryCount}</Text>\n                  </div>\n                </Col>\n                <Col span={8}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>处理人：</Text>\n                    <Text>{selectedOrder.processedBy}</Text>\n                  </div>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>是否可重试：</Text>\n                    {selectedOrder.canRetry ? (\n                      <Tag color=\"green\" icon={<RedoOutlined />}>可重试</Tag>\n                    ) : (\n                      <Tag color=\"red\" icon={<CloseCircleOutlined />}>不可重试</Tag>\n                    )}\n                  </div>\n                </Col>\n                <Col span={12}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>失败时长：</Text>\n                    <Text style={{ color: selectedOrder.failureDays > 3 ? '#f5222d' : '#fa8c16' }}>\n                      {selectedOrder.failureDays < 1\n                        ? `${Math.round(selectedOrder.failureDays * 24)}小时`\n                        : `${selectedOrder.failureDays.toFixed(1)}天`}\n                    </Text>\n                  </div>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col span={24}>\n                  <div style={{ marginBottom: 8 }}>\n                    <Text strong>失败原因：</Text>\n                    <div style={{ marginTop: 4, color: '#666', lineHeight: 1.5, padding: '8px', background: '#fff2f0', borderRadius: '4px' }}>\n                      {selectedOrder.failureReason}\n                    </div>\n                  </div>\n                </Col>\n              </Row>\n            </Card>\n          </div>\n        )}\n      </Modal>\n\n      {/* 重试订单弹窗 */}\n      <Modal\n        title=\"重试订单\"\n        open={retryModalVisible}\n        onCancel={() => setRetryModalVisible(false)}\n        footer={[\n          <Button key=\"cancel\" onClick={() => setRetryModalVisible(false)}>\n            取消\n          </Button>,\n          <Button key=\"submit\" type=\"primary\" onClick={handleSubmitRetry}>\n            确认重试\n          </Button>,\n        ]}\n        width={600}\n      >\n        {selectedOrder && (\n          <div>\n            {/* 订单基本信息 */}\n            <Card title=\"订单信息\" size=\"small\" style={{ marginBottom: 16 }}>\n              <Descriptions column={2} size=\"small\">\n                <Descriptions.Item label=\"订单号\">{selectedOrder.orderNo}</Descriptions.Item>\n                <Descriptions.Item label=\"客户姓名\">{selectedOrder.customerName}</Descriptions.Item>\n                <Descriptions.Item label=\"手机号\">{selectedOrder.customerPhone}</Descriptions.Item>\n                <Descriptions.Item label=\"产品名称\">{selectedOrder.productName}</Descriptions.Item>\n                <Descriptions.Item label=\"运营商\">\n                  <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"重试次数\">{selectedOrder.retryCount}</Descriptions.Item>\n              </Descriptions>\n              <Descriptions column={1} size=\"small\">\n                <Descriptions.Item label=\"失败原因\">\n                  <div style={{ color: '#f5222d' }}>{selectedOrder.failureReason}</div>\n                </Descriptions.Item>\n              </Descriptions>\n            </Card>\n\n            {/* 重试说明 */}\n            <Card title=\"重试说明\" size=\"small\">\n              <Alert\n                message=\"重试注意事项\"\n                description={\n                  <ul style={{ margin: 0, paddingLeft: '20px' }}>\n                    <li>重试前请确认失败原因已解决</li>\n                    <li>身份验证失败：请确认客户信息准确无误</li>\n                    <li>系统错误：系统已修复，可直接重试</li>\n                    <li>证件无效：请联系客户重新上传清晰证件</li>\n                    <li>重试后订单将重新进入处理流程</li>\n                  </ul>\n                }\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n\n              <Form form={form} layout=\"vertical\">\n                <Form.Item\n                  name=\"retryReason\"\n                  label={<Text strong>重试原因</Text>}\n                  rules={[{ required: true, message: '请填写重试原因' }]}\n                >\n                  <Input.TextArea\n                    rows={3}\n                    placeholder=\"请说明重试的原因和已采取的解决措施...\"\n                    maxLength={300}\n                    showCount\n                  />\n                </Form.Item>\n              </Form>\n            </Card>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderFailed;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,GAAG,CACHC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,OAAO,CACPC,YAAY,CAEZC,KAAK,KACA,MAAM,CACb,OACEC,cAAc,CACdC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,yBAAyB,CACzBC,mBAAmB,CACnBC,eAAe,KAEV,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG3B,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGxB,UAAU,CAClC,KAAM,CAAEyB,MAAO,CAAC,CAAGpB,MAAM,CACzB,KAAM,CAAEqB,WAAY,CAAC,CAAGpB,UAAU,CAElC;AAoBA;AACA,KAAM,CAAAqB,gBAA+B,CAAG,CACtC,CACEC,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,aAAa,CAC5BC,cAAc,CAAE,oBAAoB,CACpCC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,gCAAgC,CACjDC,aAAa,CAAE,0BAA0B,CACzCC,WAAW,CAAE,uBAAuB,CACpCC,QAAQ,CAAE,qBAAqB,CAC/BC,WAAW,CAAE,CAAC,CACdC,UAAU,CAAE,CAAC,CACbC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,QAAQ,CAClBC,WAAW,CAAE,OACf,CAAC,CACD,CACEf,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,aAAa,CAC5BC,cAAc,CAAE,oBAAoB,CACpCC,WAAW,CAAE,YAAY,CACzBC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,kCAAkC,CACnDC,aAAa,CAAE,oBAAoB,CACnCC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,qBAAqB,CAC/BC,WAAW,CAAE,GAAG,CAChBC,UAAU,CAAE,CAAC,CACbC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,MAAM,CAChBC,WAAW,CAAE,QACf,CAAC,CACD,CACEf,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,aAAa,CAC5BC,cAAc,CAAE,oBAAoB,CACpCC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,yBAAyB,CAC1CC,aAAa,CAAE,gBAAgB,CAC/BC,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,qBAAqB,CAC/BC,WAAW,CAAE,GAAG,CAChBC,UAAU,CAAE,CAAC,CACbC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,QAAQ,CAClBC,WAAW,CAAE,aACf,CAAC,CACD,CACEf,EAAE,CAAE,CAAC,CACLC,OAAO,CAAE,iBAAiB,CAC1BC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,aAAa,CAC5BC,cAAc,CAAE,oBAAoB,CACpCC,WAAW,CAAE,UAAU,CACvBC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,4BAA4B,CAC7CC,aAAa,CAAE,sBAAsB,CACrCC,WAAW,CAAE,kBAAkB,CAC/BC,QAAQ,CAAE,qBAAqB,CAC/BC,WAAW,CAAE,GAAG,CAChBC,UAAU,CAAE,CAAC,CACbC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,QAAQ,CAClBC,WAAW,CAAE,UACf,CAAC,CACF,CAED,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGrD,QAAQ,CAAgB,EAAE,CAAC,CACvD,KAAM,CAACsD,OAAO,CAAEC,UAAU,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACwD,eAAe,CAAEC,kBAAkB,CAAC,CAAGzD,QAAQ,CAAc,EAAE,CAAC,CACvE,KAAM,CAAC0D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC4D,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG7D,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC8D,aAAa,CAAEC,gBAAgB,CAAC,CAAG/D,QAAQ,CAAqB,IAAI,CAAC,CAC5E,KAAM,CAACgE,IAAI,CAAC,CAAGjD,IAAI,CAACkD,OAAO,CAAC,CAAC,CAE7B;AACAhE,SAAS,CAAC,IAAM,CACdiE,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,gBAAgB,CAAIC,OAAY,EAAkB,CACtD,MAAO,CACLjC,EAAE,CAAEiC,OAAO,CAACjC,EAAE,CACdC,OAAO,CAAEgC,OAAO,CAACC,QAAQ,CACzBhC,YAAY,CAAE+B,OAAO,CAACE,aAAa,CACnChC,aAAa,CAAE8B,OAAO,CAACG,cAAc,CACrChC,cAAc,CAAE6B,OAAO,CAACI,gBAAgB,CACxChC,WAAW,CAAE4B,OAAO,CAACK,YAAY,CACjChC,QAAQ,CAAE2B,OAAO,CAAC3B,QAAQ,CAC1BC,eAAe,CAAE0B,OAAO,CAACM,gBAAgB,CACzC/B,aAAa,CAAEyB,OAAO,CAACO,aAAa,EAAIP,OAAO,CAACQ,aAAa,EAAI,MAAM,CACvEhC,WAAW,CAAE,OAAO,CAAE;AACtBC,QAAQ,CAAEuB,OAAO,CAACS,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACV,OAAO,CAACS,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC,CAAG,EAAE,CACxFjC,WAAW,CAAEsB,OAAO,CAACS,UAAU,CAC7BG,IAAI,CAACC,GAAG,CAAC,GAAG,CAAE,CAAC,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAJ,IAAI,CAACV,OAAO,CAACS,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,GAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAAG,GAAG,CAC9GnC,UAAU,CAAE,CAAC,CAAE;AACfC,QAAQ,CAAE,IAAI,CAAE;AAChBC,QAAQ,CAAEmB,OAAO,CAACnB,QAAQ,EAAI,QAAQ,CACtCC,WAAW,CAAEkB,OAAO,CAACe,YAAY,EAAI,QACvC,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAjB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCX,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mDAAmD,CAAE,CAChFC,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,QAAQ,CAAE,kBACZ,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAEpC,GAAID,MAAM,CAACE,IAAI,GAAK,GAAG,CAAE,CACvB;AACA,KAAM,CAAAC,iBAAiB,CAAGH,MAAM,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC3B,gBAAgB,CAAC,CAChEd,SAAS,CAACsC,iBAAiB,CAAC,CAC5B3E,OAAO,CAAC+E,OAAO,2DAAAC,MAAA,CAAcL,iBAAiB,CAACM,MAAM,mCAAQ,CAAC,CAChE,CAAC,IAAM,CACLjF,OAAO,CAACkF,KAAK,CAACV,MAAM,CAACxE,OAAO,EAAI,MAAM,CAAC,CACzC,CACF,CAAE,MAAOkF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BlF,OAAO,CAACkF,KAAK,CAAC,YAAY,CAAC,CAC7B,CAAC,OAAS,CACR3C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA6C,mBAAmB,CAAIC,IAAY,EAAK,CAC5C,KAAM,CAAAC,MAAM,CAAG,CACbC,qBAAqB,CAAE,KAAK,CAC5BC,YAAY,CAAE,QAAQ,CACtBC,YAAY,CAAE,QAAQ,CACtBC,gBAAgB,CAAE,SAAS,CAC3BC,KAAK,CAAE,SACT,CAAC,CACD,MAAO,CAAAL,MAAM,CAACD,IAAI,CAAwB,EAAI,SAAS,CACzD,CAAC,CAED;AACA,KAAM,CAAAO,kBAAkB,CAAIP,IAAY,EAAK,CAC3C,KAAM,CAAAQ,KAAK,CAAG,CACZN,qBAAqB,CAAE,QAAQ,CAC/BC,YAAY,CAAE,QAAQ,CACtBC,YAAY,CAAE,MAAM,CACpBC,gBAAgB,CAAE,MAAM,CACxBC,KAAK,CAAE,MACT,CAAC,CACD,MAAO,CAAAE,KAAK,CAACR,IAAI,CAAuB,EAAIA,IAAI,CAClD,CAAC,CAED;AACA,KAAM,CAAAS,gBAAgB,CAAI7D,QAAgB,EAAK,CAC7C,KAAM,CAAAqD,MAAM,CAAG,CACbS,GAAG,CAAE,SAAS,CACdC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,QAAQ,CACdC,MAAM,CAAE,KACV,CAAC,CACD,MAAO,CAAAZ,MAAM,CAACrD,QAAQ,CAAwB,EAAI,SAAS,CAC7D,CAAC,CAED;AACA,KAAM,CAAAkE,eAAe,CAAIlE,QAAgB,EAAK,CAC5C,KAAM,CAAA4D,KAAK,CAAG,CACZE,GAAG,CAAE,GAAG,CACRC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,GAAG,CACTC,MAAM,CAAE,IACV,CAAC,CACD,MAAO,CAAAL,KAAK,CAAC5D,QAAQ,CAAuB,EAAIA,QAAQ,CAC1D,CAAC,CAED;AACA,KAAM,CAAAmE,KAAK,CAAG,CACZC,KAAK,CAAEjE,MAAM,CAAC6C,MAAM,CACpBjD,QAAQ,CAAEI,MAAM,CAACkE,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACvE,QAAQ,CAAC,CAACiD,MAAM,CACvDuB,cAAc,CAAEpE,MAAM,CAACkE,MAAM,CAACC,KAAK,EAAIA,KAAK,CAAC3E,WAAW,GAAK,uBAAuB,CAAC,CAACqD,MAAM,CAC5FwB,WAAW,CAAErE,MAAM,CAACkE,MAAM,CAACC,KAAK,EAAIA,KAAK,CAAC3E,WAAW,GAAK,cAAc,CAAC,CAACqD,MAAM,CAChFiB,MAAM,CAAE9D,MAAM,CAACkE,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACtE,QAAQ,GAAK,QAAQ,CAAC,CAACgD,MAC9D,CAAC,CAED;AACA,KAAM,CAAAyB,eAAe,CAAIH,KAAkB,EAAK,CAC9CxD,gBAAgB,CAACwD,KAAK,CAAC,CACvB5D,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAgE,gBAAgB,CAAIJ,KAAkB,EAAK,CAC/C,GAAI,CAACA,KAAK,CAACvE,QAAQ,CAAE,CACnBhC,OAAO,CAAC4G,OAAO,CAAC,UAAU,CAAC,CAC3B,OACF,CACA7D,gBAAgB,CAACwD,KAAK,CAAC,CACvB1D,oBAAoB,CAAC,IAAI,CAAC,CAC1BG,IAAI,CAAC6D,WAAW,CAAC,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA/D,IAAI,CAACgE,cAAc,CAAC,CAAC,CAE1C,GAAI,CAAClE,aAAa,CAAE,CAClB9C,OAAO,CAACkF,KAAK,CAAC,OAAO,CAAC,CACtB,OACF,CAEA;AACA,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAC,KAAK,wCAAAW,MAAA,CAAwClC,aAAa,CAAC3B,EAAE,YAAW,CAC7FmD,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,cAAc,CAAE,kBAClB,CAAC,CACD0C,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,MAAM,CAAE,SAAS,CACjBzD,aAAa,kCAAAqB,MAAA,CAAU+B,MAAM,CAACM,WAAW,CAAE,CAC3ClD,YAAY,CAAE,OAChB,CAAC,CACH,CAAC,CAAC,CAEF,KAAM,CAAAK,MAAM,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAEpC,GAAID,MAAM,CAACE,IAAI,GAAK,GAAG,CAAE,CACvB1E,OAAO,CAAC+E,OAAO,CAAC,iBAAiB,CAAC,CAElC;AACA1C,SAAS,CAACD,MAAM,CAACkE,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACpF,EAAE,GAAK2B,aAAa,CAAC3B,EAAE,CAAC,CAAC,CAChE0B,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,gBAAgB,CAAC,IAAI,CAAC,CACtBC,IAAI,CAAC6D,WAAW,CAAC,CAAC,CACpB,CAAC,IAAM,CACL7G,OAAO,CAACkF,KAAK,CAACV,MAAM,CAACxE,OAAO,EAAI,MAAM,CAAC,CACzC,CACF,CAAE,MAAOkF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BlF,OAAO,CAACkF,KAAK,CAAC,YAAY,CAAC,CAC7B,CACF,CAAC,CAED;AACA,KAAM,CAAAoC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI9E,eAAe,CAACyC,MAAM,GAAK,CAAC,CAAE,CAChCjF,OAAO,CAAC4G,OAAO,CAAC,WAAW,CAAC,CAC5B,OACF,CAEA,KAAM,CAAAW,eAAe,CAAGnF,MAAM,CAACkE,MAAM,CAACC,KAAK,EACzC/D,eAAe,CAACgF,QAAQ,CAACjB,KAAK,CAACpF,EAAE,CAAC,EAAIoF,KAAK,CAACvE,QAC9C,CAAC,CAED,GAAIuF,eAAe,CAACtC,MAAM,GAAK,CAAC,CAAE,CAChCjF,OAAO,CAAC4G,OAAO,CAAC,aAAa,CAAC,CAC9B,OACF,CAEA9G,KAAK,CAAC2H,OAAO,CAAC,CACZC,KAAK,CAAE,QAAQ,CACfC,OAAO,qDAAA3C,MAAA,CAAcuC,eAAe,CAACtC,MAAM,mCAAQ,CACnD2C,IAAI,CAAEA,CAAA,GAAM,CACVvF,SAAS,CAACD,MAAM,CAACkE,MAAM,CAACC,KAAK,EAAI,CAAC/D,eAAe,CAACgF,QAAQ,CAACjB,KAAK,CAACpF,EAAE,CAAC,CAAC,CAAC,CACtEsB,kBAAkB,CAAC,EAAE,CAAC,CACtBzC,OAAO,CAAC+E,OAAO,6BAAAC,MAAA,CAASuC,eAAe,CAACtC,MAAM,uBAAM,CAAC,CACvD,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA4C,OAAiC,CAAG,CACxC,CACEH,KAAK,CAAE,KAAK,CACZI,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAGhG,QAAgB,eACvBtB,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAEpC,gBAAgB,CAAC7D,QAAQ,CAAE,CAAAkG,QAAA,CACpChC,eAAe,CAAClE,QAAQ,CAAC,CACvB,CACN,CACDmG,MAAM,CAAEA,CAACC,CAAC,CAAEC,CAAC,GAAK,CAChB,KAAM,CAAAC,aAAa,CAAG,CAAErC,MAAM,CAAE,CAAC,CAAED,IAAI,CAAE,CAAC,CAAED,MAAM,CAAE,CAAC,CAAED,GAAG,CAAE,CAAE,CAAC,CAC/D,MAAO,CAAAwC,aAAa,CAACF,CAAC,CAACpG,QAAQ,CAA+B,CACvDsG,aAAa,CAACD,CAAC,CAACrG,QAAQ,CAA+B,CAChE,CACF,CAAC,CACD,CACEyF,KAAK,CAAE,KAAK,CACZI,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGO,IAAY,eACnB7H,IAAA,CAACI,IAAI,EAAC2D,IAAI,MAAC+D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAP,QAAA,CAAEK,IAAI,CAAO,CAExD,CAAC,CACD,CACEd,KAAK,CAAE,MAAM,CACbK,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACU,CAAC,CAAEC,MAAM,gBAChB/H,KAAA,QAAAsH,QAAA,eACExH,IAAA,QAAK8H,KAAK,CAAE,CAAEI,UAAU,CAAE,GAAG,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAP,QAAA,CAC/CS,MAAM,CAACvH,YAAY,CACjB,CAAC,cACNV,IAAA,QAAK8H,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAER,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAC7CS,MAAM,CAACtH,aAAa,CAClB,CAAC,EACH,CAET,CAAC,CACD,CACEoG,KAAK,CAAE,MAAM,CACbK,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACU,CAAC,CAAEC,MAAM,gBAChB/H,KAAA,QAAAsH,QAAA,eACExH,IAAA,QAAK8H,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEI,YAAY,CAAE,KAAM,CAAE,CAAAX,QAAA,CACnDS,MAAM,CAACpH,WAAW,CAChB,CAAC,cACNb,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAC,MAAM,CAACO,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEK,OAAO,CAAE,SAAU,CAAE,CAAAZ,QAAA,CAAES,MAAM,CAACnH,QAAQ,CAAM,CAAC,EACvF,CAET,CAAC,CACD,CACEiG,KAAK,CAAE,MAAM,CACbK,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACU,CAAC,CAAEC,MAAM,gBAChB/H,KAAA,QAAAsH,QAAA,eACExH,IAAA,QAAK8H,KAAK,CAAE,CAAEK,YAAY,CAAE,KAAM,CAAE,CAAAX,QAAA,cAClCxH,IAAA,CAACrB,GAAG,EACF4I,KAAK,CAAE9C,mBAAmB,CAACwD,MAAM,CAAChH,WAAW,CAAE,CAC/CoH,IAAI,cAAErI,IAAA,CAACJ,yBAAyB,GAAE,CAAE,CAAA4H,QAAA,CAEnCvC,kBAAkB,CAACgD,MAAM,CAAChH,WAAW,CAAC,CACpC,CAAC,CACH,CAAC,cACNjB,IAAA,QAAK8H,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAER,KAAK,CAAE,MAAM,CAAEe,UAAU,CAAE,GAAI,CAAE,CAAAd,QAAA,CAC9DS,MAAM,CAACjH,aAAa,CAClB,CAAC,EACH,CAET,CAAC,CACD,CACE+F,KAAK,CAAE,MAAM,CACbK,GAAG,CAAE,OAAO,CACZC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACU,CAAC,CAAEC,MAAM,gBAChB/H,KAAA,QAAAsH,QAAA,eACEtH,KAAA,QAAK4H,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEI,YAAY,CAAE,KAAM,CAAE,CAAAX,QAAA,EAAC,4BAC/C,CAACS,MAAM,CAAC7G,UAAU,EACrB,CAAC,cACNpB,IAAA,QAAAwH,QAAA,CACGS,MAAM,CAAC5G,QAAQ,cACdrB,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAC,OAAO,CAACc,IAAI,cAAErI,IAAA,CAACL,YAAY,GAAE,CAAE,CAAA6H,QAAA,CAAC,oBAAG,CAAK,CAAC,cAEpDxH,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAC,KAAK,CAACc,IAAI,cAAErI,IAAA,CAACH,mBAAmB,GAAE,CAAE,CAAA2H,QAAA,CAAC,0BAAI,CAAK,CAC1D,CACE,CAAC,EACH,CAET,CAAC,CACD,CACET,KAAK,CAAE,MAAM,CACbI,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGiB,IAAY,eACnBvI,IAAA,CAACI,IAAI,EAAC0H,KAAK,CAAE,CAAEP,KAAK,CAAEgB,IAAI,CAAG,CAAC,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAf,QAAA,CACtDe,IAAI,CAAG,CAAC,IAAAlE,MAAA,CAAMhB,IAAI,CAACmF,KAAK,CAACD,IAAI,CAAG,EAAE,CAAC,oBAAAlE,MAAA,CAAUkE,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,UAAG,CAC5D,CACP,CACDhB,MAAM,CAAEA,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACvG,WAAW,CAAGwG,CAAC,CAACxG,WACtC,CAAC,CACD,CACE4F,KAAK,CAAE,MAAM,CACbI,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGO,IAAY,eACnB7H,IAAA,QAAK8H,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAP,QAAA,CAC9BK,IAAI,CACF,CAET,CAAC,CACD,CACEd,KAAK,CAAE,IAAI,CACXK,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACU,CAAC,CAAEC,MAAM,gBAChB/H,KAAA,CAACxB,KAAK,EAACgK,IAAI,CAAC,OAAO,CAAAlB,QAAA,eACjBxH,IAAA,CAACvB,MAAM,EACLiG,IAAI,CAAC,MAAM,CACXgE,IAAI,CAAC,OAAO,CACZL,IAAI,cAAErI,IAAA,CAACN,WAAW,GAAE,CAAE,CACtBiJ,OAAO,CAAEA,CAAA,GAAM5C,eAAe,CAACkC,MAAM,CAAE,CAAAT,QAAA,CACxC,cAED,CAAQ,CAAC,cACTxH,IAAA,CAACvB,MAAM,EACLiG,IAAI,CAAC,MAAM,CACXgE,IAAI,CAAC,OAAO,CACZL,IAAI,cAAErI,IAAA,CAACL,YAAY,GAAE,CAAE,CACvBiJ,QAAQ,CAAE,CAACX,MAAM,CAAC5G,QAAS,CAC3BsH,OAAO,CAAEA,CAAA,GAAM3C,gBAAgB,CAACiC,MAAM,CAAE,CAAAT,QAAA,CACzC,cAED,CAAQ,CAAC,EACJ,CAEX,CAAC,CACF,CAED,mBACEtH,KAAA,QAAK4H,KAAK,CAAE,CAAEM,OAAO,CAAE,MAAO,CAAE,CAAAZ,QAAA,eAC9BtH,KAAA,CAACC,KAAK,EAAC0I,KAAK,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAX,QAAA,eAC/CxH,IAAA,CAACH,mBAAmB,EAACiI,KAAK,CAAE,CAAEgB,WAAW,CAAE,KAAK,CAAEvB,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,2BAE1E,EAAO,CAAC,cAGRrH,KAAA,CAACrB,GAAG,EAACkK,MAAM,CAAE,EAAG,CAACjB,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAX,QAAA,eAC/CxH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXxH,IAAA,CAACzB,IAAI,EAAAiJ,QAAA,cACHxH,IAAA,CAACjB,SAAS,EACRgI,KAAK,CAAC,sCAAQ,CACdkC,KAAK,CAAExD,KAAK,CAACC,KAAM,CACnBwD,MAAM,cAAElJ,IAAA,CAACH,mBAAmB,GAAE,CAAE,CAChCsJ,UAAU,CAAE,CAAE5B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNvH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXxH,IAAA,CAACzB,IAAI,EAAAiJ,QAAA,cACHxH,IAAA,CAACjB,SAAS,EACRgI,KAAK,CAAC,gCAAO,CACbkC,KAAK,CAAExD,KAAK,CAACpE,QAAS,CACtB6H,MAAM,cAAElJ,IAAA,CAACL,YAAY,GAAE,CAAE,CACzBwJ,UAAU,CAAE,CAAE5B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNvH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXxH,IAAA,CAACzB,IAAI,EAAAiJ,QAAA,cACHxH,IAAA,CAACjB,SAAS,EACRgI,KAAK,CAAC,sCAAQ,CACdkC,KAAK,CAAExD,KAAK,CAACI,cAAe,CAC5BqD,MAAM,cAAElJ,IAAA,CAACF,eAAe,GAAE,CAAE,CAC5BqJ,UAAU,CAAE,CAAE5B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNvH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXxH,IAAA,CAACzB,IAAI,EAAAiJ,QAAA,cACHxH,IAAA,CAACjB,SAAS,EACRgI,KAAK,CAAC,0BAAM,CACZkC,KAAK,CAAExD,KAAK,CAACF,MAAO,CACpB2D,MAAM,cAAElJ,IAAA,CAACJ,yBAAyB,GAAE,CAAE,CACtCuJ,UAAU,CAAE,CAAE5B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGNvH,IAAA,CAACzB,IAAI,EAACuJ,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAX,QAAA,cACpCtH,KAAA,CAACrB,GAAG,EAACuK,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAAA7B,QAAA,eACzCxH,IAAA,CAAClB,GAAG,EAAA0I,QAAA,cACFtH,KAAA,CAACxB,KAAK,EAAA8I,QAAA,eACJxH,IAAA,CAAChB,KAAK,EACJsK,WAAW,CAAC,8DAAY,CACxBJ,MAAM,cAAElJ,IAAA,CAACR,cAAc,GAAE,CAAE,CAC3BsI,KAAK,CAAE,CAAET,KAAK,CAAE,GAAI,CAAE,CACvB,CAAC,cACFnH,KAAA,CAACjB,MAAM,EAACqK,WAAW,CAAC,0BAAM,CAACxB,KAAK,CAAE,CAAET,KAAK,CAAE,GAAI,CAAE,CAAAG,QAAA,eAC/CxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,EAAE,CAAAzB,QAAA,CAAC,cAAE,CAAQ,CAAC,cAC5BxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,uBAAuB,CAAAzB,QAAA,CAAC,sCAAM,CAAQ,CAAC,cACrDxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,cAAc,CAAAzB,QAAA,CAAC,sCAAM,CAAQ,CAAC,cAC5CxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,cAAc,CAAAzB,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAC1CxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,kBAAkB,CAAAzB,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAC9CxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,OAAO,CAAAzB,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC7B,CAAC,cACTtH,KAAA,CAACjB,MAAM,EAACqK,WAAW,CAAC,gCAAO,CAACxB,KAAK,CAAE,CAAET,KAAK,CAAE,GAAI,CAAE,CAAAG,QAAA,eAChDxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,EAAE,CAAAzB,QAAA,CAAC,cAAE,CAAQ,CAAC,cAC5BxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,MAAM,CAAAzB,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACjCxH,IAAA,CAACK,MAAM,EAAC4I,KAAK,CAAC,OAAO,CAAAzB,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC7B,CAAC,cACTxH,IAAA,CAACvB,MAAM,EAAC4J,IAAI,cAAErI,IAAA,CAACR,cAAc,GAAE,CAAE,CAACkF,IAAI,CAAC,SAAS,CAAA8C,QAAA,CAAC,cAEjD,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,cACNxH,IAAA,CAAClB,GAAG,EAAA0I,QAAA,cACFtH,KAAA,CAACxB,KAAK,EAAA8I,QAAA,eACJtH,KAAA,CAACzB,MAAM,EACLiG,IAAI,CAAC,SAAS,CACd2D,IAAI,cAAErI,IAAA,CAACL,YAAY,GAAE,CAAE,CACvBgJ,OAAO,CAAEhC,gBAAiB,CAC1BiC,QAAQ,CAAE/G,eAAe,CAACyC,MAAM,GAAK,CAAE,CAAAkD,QAAA,EACxC,4BACO,CAAC3F,eAAe,CAACyC,MAAM,CAAC,GAChC,EAAQ,CAAC,cACTtE,IAAA,CAACvB,MAAM,EAAC4J,IAAI,cAAErI,IAAA,CAACP,cAAc,GAAE,CAAE,CAACkJ,OAAO,CAAEpG,aAAc,CAAAiF,QAAA,CAAC,cAE1D,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,CACF,CAAC,cAGPxH,IAAA,CAACzB,IAAI,EAAAiJ,QAAA,cACHxH,IAAA,CAACxB,KAAK,EACJ0I,OAAO,CAAEA,OAAQ,CACjBqC,UAAU,CAAE9H,MAAO,CACnB+H,MAAM,CAAC,IAAI,CACX7H,OAAO,CAAEA,OAAQ,CACjB8H,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,YAAY,CAAE,CACZ9H,eAAe,CACf+H,QAAQ,CAAE9H,kBAAkB,CAC5B+H,gBAAgB,CAAG5B,MAAM,GAAM,CAC7BW,QAAQ,CAAE,CAACX,MAAM,CAAC5G,QAAU;AAC9B,CAAC,CACH,CAAE,CACFyI,UAAU,CAAE,CACVpE,KAAK,CAAEjE,MAAM,CAAC6C,MAAM,CACpByF,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACxE,KAAK,CAAEyE,KAAK,aAAA9F,MAAA,CACjB8F,KAAK,CAAC,CAAC,CAAC,MAAA9F,MAAA,CAAI8F,KAAK,CAAC,CAAC,CAAC,oBAAA9F,MAAA,CAAQqB,KAAK,WAC1C,CAAE,CACH,CAAC,CACE,CAAC,cAGP1F,IAAA,CAACb,KAAK,EACJ4H,KAAK,CAAC,sCAAQ,CACdqD,IAAI,CAAErI,gBAAiB,CACvBsI,QAAQ,CAAEA,CAAA,GAAMrI,mBAAmB,CAAC,KAAK,CAAE,CAC3CsI,MAAM,CAAE,cACNtK,IAAA,CAACvB,MAAM,EAAakK,OAAO,CAAEA,CAAA,GAAM3G,mBAAmB,CAAC,KAAK,CAAE,CAAAwF,QAAA,CAAC,cAE/D,EAFY,OAEJ,CAAC,CACT,CACFH,KAAK,CAAE,GAAI,CAAAG,QAAA,CAEVrF,aAAa,eACZjC,KAAA,QAAAsH,QAAA,eAEExH,IAAA,CAACT,KAAK,EACJF,OAAO,CAAC,sCAAQ,CAChBkL,WAAW,CAAEpI,aAAa,CAACnB,aAAc,CACzC0D,IAAI,CAAC,OAAO,CACZ8F,QAAQ,MACR1C,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAGFnI,IAAA,CAACzB,IAAI,EAACwI,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,OAAO,CAACZ,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAAX,QAAA,cAC1DtH,KAAA,CAACrB,GAAG,EAACkK,MAAM,CAAE,EAAG,CAAAvB,QAAA,eACdxH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,oBAAG,CAAM,CAAC,cACvBxH,IAAA,CAACI,IAAI,EAAAoH,QAAA,CAAErF,aAAa,CAACzB,YAAY,CAAO,CAAC,EACtC,CAAC,CACH,CAAC,cACNV,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBxH,IAAA,CAACI,IAAI,EAAC2D,IAAI,MAAAyD,QAAA,CAAErF,aAAa,CAACxB,aAAa,CAAO,CAAC,EAC5C,CAAC,CACH,CAAC,cACNX,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBxH,IAAA,CAACI,IAAI,EAAC2D,IAAI,MAAAyD,QAAA,CAAErF,aAAa,CAACvB,cAAc,CAAO,CAAC,EAC7C,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAGPZ,IAAA,CAACzB,IAAI,EAACwI,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,OAAO,CAACZ,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAAX,QAAA,cAC1DtH,KAAA,CAACrB,GAAG,EAACkK,MAAM,CAAE,EAAG,CAAAvB,QAAA,eACdxH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,EAAG,CAAAxB,QAAA,cACZtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBxH,IAAA,CAACI,IAAI,EAAAoH,QAAA,CAAErF,aAAa,CAACtB,WAAW,CAAO,CAAC,EACrC,CAAC,CACH,CAAC,cACNb,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,EAAG,CAAAxB,QAAA,cACZtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBxH,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAC,MAAM,CAAAC,QAAA,CAAErF,aAAa,CAACrB,QAAQ,CAAM,CAAC,EAC7C,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAGPd,IAAA,CAACzB,IAAI,EAACwI,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,OAAO,CAACZ,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAAX,QAAA,cAC1DxH,IAAA,QAAK8H,KAAK,CAAE,CAAEM,OAAO,CAAE,OAAQ,CAAE,CAAAZ,QAAA,cAC/BxH,IAAA,CAACI,IAAI,EAAAoH,QAAA,CAAErF,aAAa,CAACpB,eAAe,CAAO,CAAC,CACzC,CAAC,CACF,CAAC,cAGPb,KAAA,CAAC3B,IAAI,EAACwI,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,OAAO,CAAAlB,QAAA,eAC7BtH,KAAA,CAACrB,GAAG,EAACkK,MAAM,CAAE,EAAG,CAAAvB,QAAA,eACdxH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBxH,IAAA,CAACI,IAAI,EAAC2D,IAAI,MAAAyD,QAAA,CAAErF,aAAa,CAAC1B,OAAO,CAAO,CAAC,EACtC,CAAC,CACH,CAAC,cACNT,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBxH,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAE9C,mBAAmB,CAACtC,aAAa,CAAClB,WAAW,CAAE,CAAAuG,QAAA,CACxDvC,kBAAkB,CAAC9C,aAAa,CAAClB,WAAW,CAAC,CAC3C,CAAC,EACH,CAAC,CACH,CAAC,cACNjB,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBxH,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAEpC,gBAAgB,CAAChD,aAAa,CAACb,QAAQ,CAAE,CAAAkG,QAAA,CAClDhC,eAAe,CAACrD,aAAa,CAACb,QAAQ,CAAC,CACrC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACNpB,KAAA,CAACrB,GAAG,EAACkK,MAAM,CAAE,EAAG,CAAAvB,QAAA,eACdxH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBxH,IAAA,CAACI,IAAI,EAAAoH,QAAA,CAAErF,aAAa,CAACjB,QAAQ,CAAO,CAAC,EAClC,CAAC,CACH,CAAC,cACNlB,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBxH,IAAA,CAACI,IAAI,EAAAoH,QAAA,CAAErF,aAAa,CAACf,UAAU,CAAO,CAAC,EACpC,CAAC,CACH,CAAC,cACNpB,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,CAAE,CAAAxB,QAAA,cACXtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBxH,IAAA,CAACI,IAAI,EAAAoH,QAAA,CAAErF,aAAa,CAACZ,WAAW,CAAO,CAAC,EACrC,CAAC,CACH,CAAC,EACH,CAAC,cACNrB,KAAA,CAACrB,GAAG,EAACkK,MAAM,CAAE,EAAG,CAAAvB,QAAA,eACdxH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,EAAG,CAAAxB,QAAA,cACZtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,sCAAM,CAAM,CAAC,CACzBrF,aAAa,CAACd,QAAQ,cACrBrB,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAC,OAAO,CAACc,IAAI,cAAErI,IAAA,CAACL,YAAY,GAAE,CAAE,CAAA6H,QAAA,CAAC,oBAAG,CAAK,CAAC,cAEpDxH,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAC,KAAK,CAACc,IAAI,cAAErI,IAAA,CAACH,mBAAmB,GAAE,CAAE,CAAA2H,QAAA,CAAC,0BAAI,CAAK,CAC1D,EACE,CAAC,CACH,CAAC,cACNxH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,EAAG,CAAAxB,QAAA,cACZtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBxH,IAAA,CAACI,IAAI,EAAC0H,KAAK,CAAE,CAAEP,KAAK,CAAEpF,aAAa,CAAChB,WAAW,CAAG,CAAC,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAqG,QAAA,CAC3ErF,aAAa,CAAChB,WAAW,CAAG,CAAC,IAAAkD,MAAA,CACvBhB,IAAI,CAACmF,KAAK,CAACrG,aAAa,CAAChB,WAAW,CAAG,EAAE,CAAC,oBAAAkD,MAAA,CAC1ClC,aAAa,CAAChB,WAAW,CAACsH,OAAO,CAAC,CAAC,CAAC,UAAG,CAC1C,CAAC,EACJ,CAAC,CACH,CAAC,EACH,CAAC,cACNzI,IAAA,CAACnB,GAAG,EAACkK,MAAM,CAAE,EAAG,CAAAvB,QAAA,cACdxH,IAAA,CAAClB,GAAG,EAACkK,IAAI,CAAE,EAAG,CAAAxB,QAAA,cACZtH,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAX,QAAA,eAC9BxH,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBxH,IAAA,QAAK8H,KAAK,CAAE,CAAE4C,SAAS,CAAE,CAAC,CAAEnD,KAAK,CAAE,MAAM,CAAEe,UAAU,CAAE,GAAG,CAAEF,OAAO,CAAE,KAAK,CAAEuC,UAAU,CAAE,SAAS,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAApD,QAAA,CACtHrF,aAAa,CAACnB,aAAa,CACzB,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,EACF,CAAC,EACJ,CACN,CACI,CAAC,cAGRhB,IAAA,CAACb,KAAK,EACJ4H,KAAK,CAAC,0BAAM,CACZqD,IAAI,CAAEnI,iBAAkB,CACxBoI,QAAQ,CAAEA,CAAA,GAAMnI,oBAAoB,CAAC,KAAK,CAAE,CAC5CoI,MAAM,CAAE,cACNtK,IAAA,CAACvB,MAAM,EAAckK,OAAO,CAAEA,CAAA,GAAMzG,oBAAoB,CAAC,KAAK,CAAE,CAAAsF,QAAA,CAAC,cAEjE,EAFY,QAEJ,CAAC,cACTxH,IAAA,CAACvB,MAAM,EAAciG,IAAI,CAAC,SAAS,CAACiE,OAAO,CAAExC,iBAAkB,CAAAqB,QAAA,CAAC,0BAEhE,EAFY,QAEJ,CAAC,CACT,CACFH,KAAK,CAAE,GAAI,CAAAG,QAAA,CAEVrF,aAAa,eACZjC,KAAA,QAAAsH,QAAA,eAEEtH,KAAA,CAAC3B,IAAI,EAACwI,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,OAAO,CAACZ,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAAX,QAAA,eAC1DtH,KAAA,CAACZ,YAAY,EAACuL,MAAM,CAAE,CAAE,CAACnC,IAAI,CAAC,OAAO,CAAAlB,QAAA,eACnCxH,IAAA,CAACV,YAAY,CAACwL,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAvD,QAAA,CAAErF,aAAa,CAAC1B,OAAO,CAAoB,CAAC,cAC1ET,IAAA,CAACV,YAAY,CAACwL,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvD,QAAA,CAAErF,aAAa,CAACzB,YAAY,CAAoB,CAAC,cAChFV,IAAA,CAACV,YAAY,CAACwL,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAvD,QAAA,CAAErF,aAAa,CAACxB,aAAa,CAAoB,CAAC,cAChFX,IAAA,CAACV,YAAY,CAACwL,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvD,QAAA,CAAErF,aAAa,CAACtB,WAAW,CAAoB,CAAC,cAC/Eb,IAAA,CAACV,YAAY,CAACwL,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAvD,QAAA,cAC5BxH,IAAA,CAACrB,GAAG,EAAC4I,KAAK,CAAC,MAAM,CAAAC,QAAA,CAAErF,aAAa,CAACrB,QAAQ,CAAM,CAAC,CAC/B,CAAC,cACpBd,IAAA,CAACV,YAAY,CAACwL,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvD,QAAA,CAAErF,aAAa,CAACf,UAAU,CAAoB,CAAC,EAClE,CAAC,cACfpB,IAAA,CAACV,YAAY,EAACuL,MAAM,CAAE,CAAE,CAACnC,IAAI,CAAC,OAAO,CAAAlB,QAAA,cACnCxH,IAAA,CAACV,YAAY,CAACwL,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvD,QAAA,cAC7BxH,IAAA,QAAK8H,KAAK,CAAE,CAAEP,KAAK,CAAE,SAAU,CAAE,CAAAC,QAAA,CAAErF,aAAa,CAACnB,aAAa,CAAM,CAAC,CACpD,CAAC,CACR,CAAC,EACX,CAAC,cAGPd,KAAA,CAAC3B,IAAI,EAACwI,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,OAAO,CAAAlB,QAAA,eAC7BxH,IAAA,CAACT,KAAK,EACJF,OAAO,CAAC,sCAAQ,CAChBkL,WAAW,cACTrK,KAAA,OAAI4H,KAAK,CAAE,CAAEkD,MAAM,CAAE,CAAC,CAAEC,WAAW,CAAE,MAAO,CAAE,CAAAzD,QAAA,eAC5CxH,IAAA,OAAAwH,QAAA,CAAI,gFAAa,CAAI,CAAC,cACtBxH,IAAA,OAAAwH,QAAA,CAAI,8GAAkB,CAAI,CAAC,cAC3BxH,IAAA,OAAAwH,QAAA,CAAI,kGAAgB,CAAI,CAAC,cACzBxH,IAAA,OAAAwH,QAAA,CAAI,8GAAkB,CAAI,CAAC,cAC3BxH,IAAA,OAAAwH,QAAA,CAAI,sFAAc,CAAI,CAAC,EACrB,CACL,CACD9C,IAAI,CAAC,MAAM,CACX8F,QAAQ,MACR1C,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEFnI,IAAA,CAACZ,IAAI,EAACiD,IAAI,CAAEA,IAAK,CAAC6I,MAAM,CAAC,UAAU,CAAA1D,QAAA,cACjCxH,IAAA,CAACZ,IAAI,CAAC0L,IAAI,EACRK,IAAI,CAAC,aAAa,CAClBJ,KAAK,cAAE/K,IAAA,CAACI,IAAI,EAACqK,MAAM,MAAAjD,QAAA,CAAC,0BAAI,CAAM,CAAE,CAChC4D,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEhM,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAmI,QAAA,cAEhDxH,IAAA,CAAChB,KAAK,CAACsM,QAAQ,EACbC,IAAI,CAAE,CAAE,CACRjC,WAAW,CAAC,2GAAsB,CAClCkC,SAAS,CAAE,GAAI,CACfC,SAAS,MACV,CAAC,CACO,CAAC,CACR,CAAC,EACH,CAAC,EACJ,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
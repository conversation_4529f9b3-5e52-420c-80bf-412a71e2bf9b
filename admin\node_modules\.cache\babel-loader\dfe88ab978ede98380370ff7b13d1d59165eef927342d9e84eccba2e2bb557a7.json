{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nexport function getArrowToken(token) {\n  const {\n    sizePopupArrow,\n    borderRadiusXS,\n    borderRadiusOuter\n  } = token;\n  const unitWidth = sizePopupArrow / 2;\n  const ax = 0;\n  const ay = unitWidth;\n  const bx = borderRadiusOuter * 1 / Math.sqrt(2);\n  const by = unitWidth - borderRadiusOuter * (1 - 1 / Math.sqrt(2));\n  const cx = unitWidth - borderRadiusXS * (1 / Math.sqrt(2));\n  const cy = borderRadiusOuter * (Math.sqrt(2) - 1) + borderRadiusXS * (1 / Math.sqrt(2));\n  const dx = 2 * unitWidth - cx;\n  const dy = cy;\n  const ex = 2 * unitWidth - bx;\n  const ey = by;\n  const fx = 2 * unitWidth - ax;\n  const fy = ay;\n  const shadowWidth = unitWidth * Math.sqrt(2) + borderRadiusOuter * (Math.sqrt(2) - 2);\n  const polygonOffset = borderRadiusOuter * (Math.sqrt(2) - 1);\n  const arrowPolygon = `polygon(${polygonOffset}px 100%, 50% ${polygonOffset}px, ${2 * unitWidth - polygonOffset}px 100%, ${polygonOffset}px 100%)`;\n  const arrowPath = `path('M ${ax} ${ay} A ${borderRadiusOuter} ${borderRadiusOuter} 0 0 0 ${bx} ${by} L ${cx} ${cy} A ${borderRadiusXS} ${borderRadiusXS} 0 0 1 ${dx} ${dy} L ${ex} ${ey} A ${borderRadiusOuter} ${borderRadiusOuter} 0 0 0 ${fx} ${fy} Z')`;\n  return {\n    arrowShadowWidth: shadowWidth,\n    arrowPath,\n    arrowPolygon\n  };\n}\nexport const genRoundedArrow = (token, bgColor, boxShadow) => {\n  const {\n    sizePopupArrow,\n    arrowPolygon,\n    arrowPath,\n    arrowShadowWidth,\n    borderRadiusXS,\n    calc\n  } = token;\n  return {\n    pointerEvents: 'none',\n    width: sizePopupArrow,\n    height: sizePopupArrow,\n    overflow: 'hidden',\n    '&::before': {\n      position: 'absolute',\n      bottom: 0,\n      insetInlineStart: 0,\n      width: sizePopupArrow,\n      height: calc(sizePopupArrow).div(2).equal(),\n      background: bgColor,\n      clipPath: {\n        _multi_value_: true,\n        value: [arrowPolygon, arrowPath]\n      },\n      content: '\"\"'\n    },\n    '&::after': {\n      content: '\"\"',\n      position: 'absolute',\n      width: arrowShadowWidth,\n      height: arrowShadowWidth,\n      bottom: 0,\n      insetInline: 0,\n      margin: 'auto',\n      borderRadius: {\n        _skip_check_: true,\n        value: `0 0 ${unit(borderRadiusXS)} 0`\n      },\n      transform: 'translateY(50%) rotate(-135deg)',\n      boxShadow,\n      zIndex: 0,\n      background: 'transparent'\n    }\n  };\n};", "map": {"version": 3, "names": ["unit", "getArrowToken", "token", "sizePopupArrow", "borderRadiusXS", "borderRadiusOuter", "unitWidth", "ax", "ay", "bx", "Math", "sqrt", "by", "cx", "cy", "dx", "dy", "ex", "ey", "fx", "fy", "<PERSON><PERSON><PERSON><PERSON>", "polygonOffset", "arrowPolygon", "arrow<PERSON>ath", "arrowShadowWidth", "genRoundedArrow", "bgColor", "boxShadow", "calc", "pointerEvents", "width", "height", "overflow", "position", "bottom", "insetInlineStart", "div", "equal", "background", "clipPath", "_multi_value_", "value", "content", "insetInline", "margin", "borderRadius", "_skip_check_", "transform", "zIndex"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/style/roundedArrow.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nexport function getArrowToken(token) {\n  const {\n    sizePopupArrow,\n    borderRadiusXS,\n    borderRadiusOuter\n  } = token;\n  const unitWidth = sizePopupArrow / 2;\n  const ax = 0;\n  const ay = unitWidth;\n  const bx = borderRadiusOuter * 1 / Math.sqrt(2);\n  const by = unitWidth - borderRadiusOuter * (1 - 1 / Math.sqrt(2));\n  const cx = unitWidth - borderRadiusXS * (1 / Math.sqrt(2));\n  const cy = borderRadiusOuter * (Math.sqrt(2) - 1) + borderRadiusXS * (1 / Math.sqrt(2));\n  const dx = 2 * unitWidth - cx;\n  const dy = cy;\n  const ex = 2 * unitWidth - bx;\n  const ey = by;\n  const fx = 2 * unitWidth - ax;\n  const fy = ay;\n  const shadowWidth = unitWidth * Math.sqrt(2) + borderRadiusOuter * (Math.sqrt(2) - 2);\n  const polygonOffset = borderRadiusOuter * (Math.sqrt(2) - 1);\n  const arrowPolygon = `polygon(${polygonOffset}px 100%, 50% ${polygonOffset}px, ${2 * unitWidth - polygonOffset}px 100%, ${polygonOffset}px 100%)`;\n  const arrowPath = `path('M ${ax} ${ay} A ${borderRadiusOuter} ${borderRadiusOuter} 0 0 0 ${bx} ${by} L ${cx} ${cy} A ${borderRadiusXS} ${borderRadiusXS} 0 0 1 ${dx} ${dy} L ${ex} ${ey} A ${borderRadiusOuter} ${borderRadiusOuter} 0 0 0 ${fx} ${fy} Z')`;\n  return {\n    arrowShadowWidth: shadowWidth,\n    arrowPath,\n    arrowPolygon\n  };\n}\nexport const genRoundedArrow = (token, bgColor, boxShadow) => {\n  const {\n    sizePopupArrow,\n    arrowPolygon,\n    arrowPath,\n    arrowShadowWidth,\n    borderRadiusXS,\n    calc\n  } = token;\n  return {\n    pointerEvents: 'none',\n    width: sizePopupArrow,\n    height: sizePopupArrow,\n    overflow: 'hidden',\n    '&::before': {\n      position: 'absolute',\n      bottom: 0,\n      insetInlineStart: 0,\n      width: sizePopupArrow,\n      height: calc(sizePopupArrow).div(2).equal(),\n      background: bgColor,\n      clipPath: {\n        _multi_value_: true,\n        value: [arrowPolygon, arrowPath]\n      },\n      content: '\"\"'\n    },\n    '&::after': {\n      content: '\"\"',\n      position: 'absolute',\n      width: arrowShadowWidth,\n      height: arrowShadowWidth,\n      bottom: 0,\n      insetInline: 0,\n      margin: 'auto',\n      borderRadius: {\n        _skip_check_: true,\n        value: `0 0 ${unit(borderRadiusXS)} 0`\n      },\n      transform: 'translateY(50%) rotate(-135deg)',\n      boxShadow,\n      zIndex: 0,\n      background: 'transparent'\n    }\n  };\n};"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,MAAM;IACJC,cAAc;IACdC,cAAc;IACdC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,SAAS,GAAGH,cAAc,GAAG,CAAC;EACpC,MAAMI,EAAE,GAAG,CAAC;EACZ,MAAMC,EAAE,GAAGF,SAAS;EACpB,MAAMG,EAAE,GAAGJ,iBAAiB,GAAG,CAAC,GAAGK,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;EAC/C,MAAMC,EAAE,GAAGN,SAAS,GAAGD,iBAAiB,IAAI,CAAC,GAAG,CAAC,GAAGK,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EACjE,MAAME,EAAE,GAAGP,SAAS,GAAGF,cAAc,IAAI,CAAC,GAAGM,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAMG,EAAE,GAAGT,iBAAiB,IAAIK,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGP,cAAc,IAAI,CAAC,GAAGM,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EACvF,MAAMI,EAAE,GAAG,CAAC,GAAGT,SAAS,GAAGO,EAAE;EAC7B,MAAMG,EAAE,GAAGF,EAAE;EACb,MAAMG,EAAE,GAAG,CAAC,GAAGX,SAAS,GAAGG,EAAE;EAC7B,MAAMS,EAAE,GAAGN,EAAE;EACb,MAAMO,EAAE,GAAG,CAAC,GAAGb,SAAS,GAAGC,EAAE;EAC7B,MAAMa,EAAE,GAAGZ,EAAE;EACb,MAAMa,WAAW,GAAGf,SAAS,GAAGI,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGN,iBAAiB,IAAIK,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACrF,MAAMW,aAAa,GAAGjB,iBAAiB,IAAIK,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC5D,MAAMY,YAAY,GAAG,WAAWD,aAAa,gBAAgBA,aAAa,OAAO,CAAC,GAAGhB,SAAS,GAAGgB,aAAa,YAAYA,aAAa,UAAU;EACjJ,MAAME,SAAS,GAAG,WAAWjB,EAAE,IAAIC,EAAE,MAAMH,iBAAiB,IAAIA,iBAAiB,UAAUI,EAAE,IAAIG,EAAE,MAAMC,EAAE,IAAIC,EAAE,MAAMV,cAAc,IAAIA,cAAc,UAAUW,EAAE,IAAIC,EAAE,MAAMC,EAAE,IAAIC,EAAE,MAAMb,iBAAiB,IAAIA,iBAAiB,UAAUc,EAAE,IAAIC,EAAE,MAAM;EAC3P,OAAO;IACLK,gBAAgB,EAAEJ,WAAW;IAC7BG,SAAS;IACTD;EACF,CAAC;AACH;AACA,OAAO,MAAMG,eAAe,GAAGA,CAACxB,KAAK,EAAEyB,OAAO,EAAEC,SAAS,KAAK;EAC5D,MAAM;IACJzB,cAAc;IACdoB,YAAY;IACZC,SAAS;IACTC,gBAAgB;IAChBrB,cAAc;IACdyB;EACF,CAAC,GAAG3B,KAAK;EACT,OAAO;IACL4B,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE5B,cAAc;IACrB6B,MAAM,EAAE7B,cAAc;IACtB8B,QAAQ,EAAE,QAAQ;IAClB,WAAW,EAAE;MACXC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,CAAC;MACTC,gBAAgB,EAAE,CAAC;MACnBL,KAAK,EAAE5B,cAAc;MACrB6B,MAAM,EAAEH,IAAI,CAAC1B,cAAc,CAAC,CAACkC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MAC3CC,UAAU,EAAEZ,OAAO;MACnBa,QAAQ,EAAE;QACRC,aAAa,EAAE,IAAI;QACnBC,KAAK,EAAE,CAACnB,YAAY,EAAEC,SAAS;MACjC,CAAC;MACDmB,OAAO,EAAE;IACX,CAAC;IACD,UAAU,EAAE;MACVA,OAAO,EAAE,IAAI;MACbT,QAAQ,EAAE,UAAU;MACpBH,KAAK,EAAEN,gBAAgB;MACvBO,MAAM,EAAEP,gBAAgB;MACxBU,MAAM,EAAE,CAAC;MACTS,WAAW,EAAE,CAAC;MACdC,MAAM,EAAE,MAAM;MACdC,YAAY,EAAE;QACZC,YAAY,EAAE,IAAI;QAClBL,KAAK,EAAE,OAAO1C,IAAI,CAACI,cAAc,CAAC;MACpC,CAAC;MACD4C,SAAS,EAAE,iCAAiC;MAC5CpB,SAAS;MACTqB,MAAM,EAAE,CAAC;MACTV,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
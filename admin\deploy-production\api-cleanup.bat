@echo off
chcp 65001 >nul
echo ========================================
echo     API项目清理工具
echo ========================================
echo.

set "API_DIR=G:\phpstudy_pro\WWW\api"

echo 🔍 检查API目录: %API_DIR%
if not exist "%API_DIR%" (
    echo ❌ API目录不存在
    pause
    exit /b 1
)

echo ✅ API目录存在
echo.

echo 📋 开始清理多余文件...
echo.

REM 删除文档文件（保留README.md）
echo 🗑️ 清理文档文件...
if exist "%API_DIR%\CLEANUP_REPORT.md" (
    del "%API_DIR%\CLEANUP_REPORT.md"
    echo   ✅ 删除 CLEANUP_REPORT.md
)
if exist "%API_DIR%\PROJECT_STRUCTURE.md" (
    del "%API_DIR%\PROJECT_STRUCTURE.md"
    echo   ✅ 删除 PROJECT_STRUCTURE.md
)
if exist "%API_DIR%\SWAGGER_GUIDE.md" (
    del "%API_DIR%\SWAGGER_GUIDE.md"
    echo   ✅ 删除 SWAGGER_GUIDE.md
)
if exist "%API_DIR%\SWAGGER_SUMMARY.md" (
    del "%API_DIR%\SWAGGER_SUMMARY.md"
    echo   ✅ 删除 SWAGGER_SUMMARY.md
)
if exist "%API_DIR%\MYSQL_GROUP_BY_FIX.md" (
    del "%API_DIR%\MYSQL_GROUP_BY_FIX.md"
    echo   ✅ 删除 MYSQL_GROUP_BY_FIX.md
)

REM 删除修复脚本
echo 🗑️ 清理修复脚本...
if exist "%API_DIR%\fix-group-by.bat" (
    del "%API_DIR%\fix-group-by.bat"
    echo   ✅ 删除 fix-group-by.bat
)
if exist "%API_DIR%\fix-mysql-group-by.php" (
    del "%API_DIR%\fix-mysql-group-by.php"
    echo   ✅ 删除 fix-mysql-group-by.php
)

REM 删除Swagger生成器（如果不需要）
echo 🗑️ 清理Swagger生成器...
if exist "%API_DIR%\swagger-generator.php" (
    echo   ⚠️ 发现 swagger-generator.php
    echo   是否删除Swagger生成器？(y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        del "%API_DIR%\swagger-generator.php"
        echo   ✅ 删除 swagger-generator.php
    ) else (
        echo   ⏭️ 保留 swagger-generator.php
    )
)

REM 清理public目录中的多余文件
echo 🗑️ 清理public目录...
if exist "%API_DIR%\public\api-docs" (
    rmdir /s /q "%API_DIR%\public\api-docs"
    echo   ✅ 删除 public/api-docs 目录
)

REM 检查并清理storage/logs中的旧日志
echo 🗑️ 清理旧日志文件...
if exist "%API_DIR%\storage\logs\*.log" (
    echo   发现日志文件，是否清理？(y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        del "%API_DIR%\storage\logs\*.log"
        echo   ✅ 清理日志文件
    ) else (
        echo   ⏭️ 保留日志文件
    )
)

REM 清理bootstrap/cache中的缓存文件
echo 🗑️ 清理缓存文件...
if exist "%API_DIR%\bootstrap\cache\*.php" (
    del "%API_DIR%\bootstrap\cache\*.php" 2>nul
    echo   ✅ 清理bootstrap缓存
)

REM 检查.env文件
echo 🔍 检查环境配置...
if exist "%API_DIR%\.env" (
    echo   ✅ .env 文件存在
) else (
    echo   ⚠️ .env 文件不存在，需要配置
)

echo.
echo 📊 清理完成统计...
echo.

echo 📁 保留的核心目录:
echo   ✅ app/ - 应用核心代码
echo   ✅ bootstrap/ - 启动文件
echo   ✅ config/ - 配置文件
echo   ✅ database/ - 数据库相关
echo   ✅ public/ - 公共文件
echo   ✅ resources/ - 资源文件
echo   ✅ routes/ - 路由定义
echo   ✅ storage/ - 存储目录
echo   ✅ vendor/ - Composer依赖

echo.
echo 📄 保留的核心文件:
echo   ✅ artisan - Laravel命令行工具
echo   ✅ composer.json - Composer配置
echo   ✅ composer.lock - 依赖锁定
echo   ✅ README.md - 项目说明
echo   ✅ .env - 环境配置

echo.
echo 🗑️ 已删除的文件:
echo   ❌ 文档文件 (*.md 除README.md外)
echo   ❌ 修复脚本 (fix-*.*)
echo   ❌ 多余的API文档目录
echo   ❌ 缓存文件

echo.
echo 📊 目录大小统计:
for /f "tokens=3" %%a in ('dir "%API_DIR%" /s /-c ^| find "个文件"') do set filesize=%%a
echo   总大小: %filesize% 字节

echo.
echo 🎯 API项目现在只包含运行必需的文件
echo.

echo ========================================
echo     清理完成！
echo ========================================
echo.
echo 📋 下一步建议:
echo   1. 检查 .env 配置
echo   2. 运行 composer install (如果需要)
echo   3. 运行 php artisan migrate
echo   4. 测试API功能
echo.
pause

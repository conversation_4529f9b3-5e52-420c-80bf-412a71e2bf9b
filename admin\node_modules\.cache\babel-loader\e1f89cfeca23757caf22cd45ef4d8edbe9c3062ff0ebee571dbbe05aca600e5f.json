{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport usePatchElement from '../../_util/hooks/usePatchElement';\nimport { withConfirm, withError, withInfo, withSuccess, withWarn } from '../confirm';\nimport destroyFns from '../destroyFns';\nimport HookModal from './HookModal';\nlet uuid = 0;\nconst ElementsHolder = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef((_props, ref) => {\n  const [elements, patchElement] = usePatchElement();\n  React.useImperativeHandle(ref, () => ({\n    patchElement\n  }), []);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, elements);\n}));\nfunction useModal() {\n  const holderRef = React.useRef(null);\n  // ========================== Effect ==========================\n  const [actionQueue, setActionQueue] = React.useState([]);\n  React.useEffect(() => {\n    if (actionQueue.length) {\n      const cloneQueue = _toConsumableArray(actionQueue);\n      cloneQueue.forEach(action => {\n        action();\n      });\n      setActionQueue([]);\n    }\n  }, [actionQueue]);\n  // =========================== Hook ===========================\n  const getConfirmFunc = React.useCallback(withFunc => function hookConfirm(config) {\n    var _a;\n    uuid += 1;\n    const modalRef = /*#__PURE__*/React.createRef();\n    // Proxy to promise with `onClose`\n    let resolvePromise;\n    const promise = new Promise(resolve => {\n      resolvePromise = resolve;\n    });\n    let silent = false;\n    let closeFunc;\n    const modal = /*#__PURE__*/React.createElement(HookModal, {\n      key: `modal-${uuid}`,\n      config: withFunc(config),\n      ref: modalRef,\n      afterClose: () => {\n        closeFunc === null || closeFunc === void 0 ? void 0 : closeFunc();\n      },\n      isSilent: () => silent,\n      onConfirm: confirmed => {\n        resolvePromise(confirmed);\n      }\n    });\n    closeFunc = (_a = holderRef.current) === null || _a === void 0 ? void 0 : _a.patchElement(modal);\n    if (closeFunc) {\n      destroyFns.push(closeFunc);\n    }\n    const instance = {\n      destroy: () => {\n        function destroyAction() {\n          var _a;\n          (_a = modalRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n        }\n        if (modalRef.current) {\n          destroyAction();\n        } else {\n          setActionQueue(prev => [].concat(_toConsumableArray(prev), [destroyAction]));\n        }\n      },\n      update: newConfig => {\n        function updateAction() {\n          var _a;\n          (_a = modalRef.current) === null || _a === void 0 ? void 0 : _a.update(newConfig);\n        }\n        if (modalRef.current) {\n          updateAction();\n        } else {\n          setActionQueue(prev => [].concat(_toConsumableArray(prev), [updateAction]));\n        }\n      },\n      then: resolve => {\n        silent = true;\n        return promise.then(resolve);\n      }\n    };\n    return instance;\n  }, []);\n  const fns = React.useMemo(() => ({\n    info: getConfirmFunc(withInfo),\n    success: getConfirmFunc(withSuccess),\n    error: getConfirmFunc(withError),\n    warning: getConfirmFunc(withWarn),\n    confirm: getConfirmFunc(withConfirm)\n  }), []);\n  return [fns, /*#__PURE__*/React.createElement(ElementsHolder, {\n    key: \"modal-holder\",\n    ref: holderRef\n  })];\n}\nexport default useModal;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "usePatchElement", "withConfirm", "with<PERSON><PERSON><PERSON>", "withInfo", "withSuccess", "with<PERSON><PERSON><PERSON>", "destroyFns", "HookModal", "uuid", "ElementsHolder", "memo", "forwardRef", "_props", "ref", "elements", "patchElement", "useImperativeHandle", "createElement", "Fragment", "useModal", "holder<PERSON><PERSON>", "useRef", "actionQueue", "setActionQueue", "useState", "useEffect", "length", "cloneQueue", "for<PERSON>ach", "action", "getConfirmFunc", "useCallback", "with<PERSON><PERSON><PERSON>", "hookConfirm", "config", "_a", "modalRef", "createRef", "resolvePromise", "promise", "Promise", "resolve", "silent", "closeFunc", "modal", "key", "afterClose", "isSilent", "onConfirm", "confirmed", "current", "push", "instance", "destroy", "destroyAction", "prev", "concat", "update", "newConfig", "updateAction", "then", "fns", "useMemo", "info", "success", "error", "warning", "confirm"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/modal/useModal/index.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport usePatchElement from '../../_util/hooks/usePatchElement';\nimport { withConfirm, withError, withInfo, withSuccess, withWarn } from '../confirm';\nimport destroyFns from '../destroyFns';\nimport HookModal from './HookModal';\nlet uuid = 0;\nconst ElementsHolder = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef((_props, ref) => {\n  const [elements, patchElement] = usePatchElement();\n  React.useImperativeHandle(ref, () => ({\n    patchElement\n  }), []);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, elements);\n}));\nfunction useModal() {\n  const holderRef = React.useRef(null);\n  // ========================== Effect ==========================\n  const [actionQueue, setActionQueue] = React.useState([]);\n  React.useEffect(() => {\n    if (actionQueue.length) {\n      const cloneQueue = _toConsumableArray(actionQueue);\n      cloneQueue.forEach(action => {\n        action();\n      });\n      setActionQueue([]);\n    }\n  }, [actionQueue]);\n  // =========================== Hook ===========================\n  const getConfirmFunc = React.useCallback(withFunc => function hookConfirm(config) {\n    var _a;\n    uuid += 1;\n    const modalRef = /*#__PURE__*/React.createRef();\n    // Proxy to promise with `onClose`\n    let resolvePromise;\n    const promise = new Promise(resolve => {\n      resolvePromise = resolve;\n    });\n    let silent = false;\n    let closeFunc;\n    const modal = /*#__PURE__*/React.createElement(HookModal, {\n      key: `modal-${uuid}`,\n      config: withFunc(config),\n      ref: modalRef,\n      afterClose: () => {\n        closeFunc === null || closeFunc === void 0 ? void 0 : closeFunc();\n      },\n      isSilent: () => silent,\n      onConfirm: confirmed => {\n        resolvePromise(confirmed);\n      }\n    });\n    closeFunc = (_a = holderRef.current) === null || _a === void 0 ? void 0 : _a.patchElement(modal);\n    if (closeFunc) {\n      destroyFns.push(closeFunc);\n    }\n    const instance = {\n      destroy: () => {\n        function destroyAction() {\n          var _a;\n          (_a = modalRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n        }\n        if (modalRef.current) {\n          destroyAction();\n        } else {\n          setActionQueue(prev => [].concat(_toConsumableArray(prev), [destroyAction]));\n        }\n      },\n      update: newConfig => {\n        function updateAction() {\n          var _a;\n          (_a = modalRef.current) === null || _a === void 0 ? void 0 : _a.update(newConfig);\n        }\n        if (modalRef.current) {\n          updateAction();\n        } else {\n          setActionQueue(prev => [].concat(_toConsumableArray(prev), [updateAction]));\n        }\n      },\n      then: resolve => {\n        silent = true;\n        return promise.then(resolve);\n      }\n    };\n    return instance;\n  }, []);\n  const fns = React.useMemo(() => ({\n    info: getConfirmFunc(withInfo),\n    success: getConfirmFunc(withSuccess),\n    error: getConfirmFunc(withError),\n    warning: getConfirmFunc(withWarn),\n    confirm: getConfirmFunc(withConfirm)\n  }), []);\n  return [fns, /*#__PURE__*/React.createElement(ElementsHolder, {\n    key: \"modal-holder\",\n    ref: holderRef\n  })];\n}\nexport default useModal;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,SAASC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,YAAY;AACpF,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,IAAI,GAAG,CAAC;AACZ,MAAMC,cAAc,GAAG,aAAaV,KAAK,CAACW,IAAI,CAAC,aAAaX,KAAK,CAACY,UAAU,CAAC,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC5F,MAAM,CAACC,QAAQ,EAAEC,YAAY,CAAC,GAAGf,eAAe,CAAC,CAAC;EAClDD,KAAK,CAACiB,mBAAmB,CAACH,GAAG,EAAE,OAAO;IACpCE;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,OAAO,aAAahB,KAAK,CAACkB,aAAa,CAAClB,KAAK,CAACmB,QAAQ,EAAE,IAAI,EAAEJ,QAAQ,CAAC;AACzE,CAAC,CAAC,CAAC;AACH,SAASK,QAAQA,CAAA,EAAG;EAClB,MAAMC,SAAS,GAAGrB,KAAK,CAACsB,MAAM,CAAC,IAAI,CAAC;EACpC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,KAAK,CAACyB,QAAQ,CAAC,EAAE,CAAC;EACxDzB,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpB,IAAIH,WAAW,CAACI,MAAM,EAAE;MACtB,MAAMC,UAAU,GAAG7B,kBAAkB,CAACwB,WAAW,CAAC;MAClDK,UAAU,CAACC,OAAO,CAACC,MAAM,IAAI;QAC3BA,MAAM,CAAC,CAAC;MACV,CAAC,CAAC;MACFN,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC,EAAE,CAACD,WAAW,CAAC,CAAC;EACjB;EACA,MAAMQ,cAAc,GAAG/B,KAAK,CAACgC,WAAW,CAACC,QAAQ,IAAI,SAASC,WAAWA,CAACC,MAAM,EAAE;IAChF,IAAIC,EAAE;IACN3B,IAAI,IAAI,CAAC;IACT,MAAM4B,QAAQ,GAAG,aAAarC,KAAK,CAACsC,SAAS,CAAC,CAAC;IAC/C;IACA,IAAIC,cAAc;IAClB,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;MACrCH,cAAc,GAAGG,OAAO;IAC1B,CAAC,CAAC;IACF,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIC,SAAS;IACb,MAAMC,KAAK,GAAG,aAAa7C,KAAK,CAACkB,aAAa,CAACV,SAAS,EAAE;MACxDsC,GAAG,EAAE,SAASrC,IAAI,EAAE;MACpB0B,MAAM,EAAEF,QAAQ,CAACE,MAAM,CAAC;MACxBrB,GAAG,EAAEuB,QAAQ;MACbU,UAAU,EAAEA,CAAA,KAAM;QAChBH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC,CAAC;MACnE,CAAC;MACDI,QAAQ,EAAEA,CAAA,KAAML,MAAM;MACtBM,SAAS,EAAEC,SAAS,IAAI;QACtBX,cAAc,CAACW,SAAS,CAAC;MAC3B;IACF,CAAC,CAAC;IACFN,SAAS,GAAG,CAACR,EAAE,GAAGf,SAAS,CAAC8B,OAAO,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpB,YAAY,CAAC6B,KAAK,CAAC;IAChG,IAAID,SAAS,EAAE;MACbrC,UAAU,CAAC6C,IAAI,CAACR,SAAS,CAAC;IAC5B;IACA,MAAMS,QAAQ,GAAG;MACfC,OAAO,EAAEA,CAAA,KAAM;QACb,SAASC,aAAaA,CAAA,EAAG;UACvB,IAAInB,EAAE;UACN,CAACA,EAAE,GAAGC,QAAQ,CAACc,OAAO,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,OAAO,CAAC,CAAC;QAC3E;QACA,IAAIjB,QAAQ,CAACc,OAAO,EAAE;UACpBI,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM;UACL/B,cAAc,CAACgC,IAAI,IAAI,EAAE,CAACC,MAAM,CAAC1D,kBAAkB,CAACyD,IAAI,CAAC,EAAE,CAACD,aAAa,CAAC,CAAC,CAAC;QAC9E;MACF,CAAC;MACDG,MAAM,EAAEC,SAAS,IAAI;QACnB,SAASC,YAAYA,CAAA,EAAG;UACtB,IAAIxB,EAAE;UACN,CAACA,EAAE,GAAGC,QAAQ,CAACc,OAAO,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,MAAM,CAACC,SAAS,CAAC;QACnF;QACA,IAAItB,QAAQ,CAACc,OAAO,EAAE;UACpBS,YAAY,CAAC,CAAC;QAChB,CAAC,MAAM;UACLpC,cAAc,CAACgC,IAAI,IAAI,EAAE,CAACC,MAAM,CAAC1D,kBAAkB,CAACyD,IAAI,CAAC,EAAE,CAACI,YAAY,CAAC,CAAC,CAAC;QAC7E;MACF,CAAC;MACDC,IAAI,EAAEnB,OAAO,IAAI;QACfC,MAAM,GAAG,IAAI;QACb,OAAOH,OAAO,CAACqB,IAAI,CAACnB,OAAO,CAAC;MAC9B;IACF,CAAC;IACD,OAAOW,QAAQ;EACjB,CAAC,EAAE,EAAE,CAAC;EACN,MAAMS,GAAG,GAAG9D,KAAK,CAAC+D,OAAO,CAAC,OAAO;IAC/BC,IAAI,EAAEjC,cAAc,CAAC3B,QAAQ,CAAC;IAC9B6D,OAAO,EAAElC,cAAc,CAAC1B,WAAW,CAAC;IACpC6D,KAAK,EAAEnC,cAAc,CAAC5B,SAAS,CAAC;IAChCgE,OAAO,EAAEpC,cAAc,CAACzB,QAAQ,CAAC;IACjC8D,OAAO,EAAErC,cAAc,CAAC7B,WAAW;EACrC,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,OAAO,CAAC4D,GAAG,EAAE,aAAa9D,KAAK,CAACkB,aAAa,CAACR,cAAc,EAAE;IAC5DoC,GAAG,EAAE,cAAc;IACnBhC,GAAG,EAAEO;EACP,CAAC,CAAC,CAAC;AACL;AACA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
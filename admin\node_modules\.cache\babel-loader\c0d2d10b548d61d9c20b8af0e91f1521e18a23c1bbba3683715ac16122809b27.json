{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorRgbInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorRgbInputPrefixCls = \"\".concat(prefixCls, \"-rgb-input\");\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const rgbValue = value || internalValue;\n  const handleRgbChange = (step, type) => {\n    const rgb = rgbValue.toRgb();\n    rgb[type] = step || 0;\n    const genColor = generateColor(rgb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorRgbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().r),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'r')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().g),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'g')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().b),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'b')\n  }));\n};\nexport default ColorRgbInput;", "map": {"version": 3, "names": ["React", "useState", "generateColor", "ColorSteppers", "ColorRgbInput", "_ref", "prefixCls", "value", "onChange", "colorRgbInputPrefixCls", "concat", "internalValue", "setInternalValue", "rgbValue", "handleRgbChange", "step", "type", "rgb", "toRgb", "genColor", "createElement", "className", "max", "min", "Number", "r", "g", "b"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/color-picker/components/ColorRgbInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorRgbInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorRgbInputPrefixCls = `${prefixCls}-rgb-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const rgbValue = value || internalValue;\n  const handleRgbChange = (step, type) => {\n    const rgb = rgbValue.toRgb();\n    rgb[type] = step || 0;\n    const genColor = generateColor(rgb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorRgbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().r),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'r')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().g),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'g')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().b),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'b')\n  }));\n};\nexport default ColorRgbInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,aAAa,GAAGC,IAAA,IAIhB;EAAA,IAJiB;IACrBC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,sBAAsB,MAAAC,MAAA,CAAMJ,SAAS,eAAY;EACvD,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,MAAMC,aAAa,CAACK,KAAK,IAAI,MAAM,CAAC,CAAC;EACxF,MAAMM,QAAQ,GAAGN,KAAK,IAAII,aAAa;EACvC,MAAMG,eAAe,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACtC,MAAMC,GAAG,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC;IAC5BD,GAAG,CAACD,IAAI,CAAC,GAAGD,IAAI,IAAI,CAAC;IACrB,MAAMI,QAAQ,GAAGjB,aAAa,CAACe,GAAG,CAAC;IACnCL,gBAAgB,CAACO,QAAQ,CAAC;IAC1BX,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACW,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAanB,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEZ;EACb,CAAC,EAAE,aAAaT,KAAK,CAACoB,aAAa,CAACjB,aAAa,EAAE;IACjDmB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNhB,KAAK,EAAEiB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACO,CAAC,CAAC;IACjCnB,SAAS,EAAEA,SAAS;IACpBe,SAAS,EAAEZ,sBAAsB;IACjCD,QAAQ,EAAEO,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAaf,KAAK,CAACoB,aAAa,CAACjB,aAAa,EAAE;IAClDmB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNhB,KAAK,EAAEiB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACQ,CAAC,CAAC;IACjCpB,SAAS,EAAEA,SAAS;IACpBe,SAAS,EAAEZ,sBAAsB;IACjCD,QAAQ,EAAEO,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAaf,KAAK,CAACoB,aAAa,CAACjB,aAAa,EAAE;IAClDmB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNhB,KAAK,EAAEiB,MAAM,CAACX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAACS,CAAC,CAAC;IACjCrB,SAAS,EAAEA,SAAS;IACpBe,SAAS,EAAEZ,sBAAsB;IACjCD,QAAQ,EAAEO,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import React,{useState,useEffect}from'react';import{orderApi}from'../services/api';import{Card,Table,Button,Space,Tag,Typography,Row,Col,Statistic,Input,Select,DatePicker,Modal,Form,message,Descriptions,Alert}from'antd';import{SearchOutlined,ReloadOutlined,EyeOutlined,RedoOutlined,ExclamationCircleOutlined,CloseCircleOutlined,WarningOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Option}=Select;const{RangePicker}=DatePicker;// 失败订单接口定义\n// 失败订单数据将从API获取，不再使用模拟数据\nconst OrderFailed=()=>{const[orders,setOrders]=useState([]);const[loading,setLoading]=useState(false);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[viewModalVisible,setViewModalVisible]=useState(false);const[retryModalVisible,setRetryModalVisible]=useState(false);const[selectedOrder,setSelectedOrder]=useState(null);const[form]=Form.useForm();// 页面加载时获取数据\nuseEffect(()=>{handleRefresh();},[]);// 转换API数据格式为前端格式\nconst transformApiData=apiData=>{return{id:apiData.id,orderNo:apiData.order_no,customerName:apiData.customer_name,customerPhone:apiData.customer_phone,customerIdCard:apiData.customer_id_card,productName:apiData.product_name,operator:apiData.operator,deliveryAddress:apiData.delivery_address,failureReason:apiData.process_notes||apiData.reject_reason||'开卡失败',failureType:'other',// 默认其他原因，实际项目中应该根据失败原因分类\nfailedAt:apiData.updated_at?new Date(apiData.updated_at).toLocaleString('zh-CN'):'',failureDays:apiData.updated_at?Math.max(0.1,(new Date().getTime()-new Date(apiData.updated_at).getTime())/(1000*60*60*24)):0.1,retryCount:0,// 默认0次重试，实际项目中应该从数据库获取\ncanRetry:true,// 默认可重试，实际项目中应该根据失败原因判断\npriority:apiData.priority||'normal',processedBy:apiData.processed_by||'system'};};// 刷新数据\nconst handleRefresh=async()=>{setLoading(true);try{const result=await orderApi.getList({status:'failed'});if(result.code===200){// 转换API数据格式\nconst transformedOrders=result.data.list.map(transformApiData);setOrders(transformedOrders);message.success(\"\\u6570\\u636E\\u5DF2\\u5237\\u65B0\\uFF0C\\u83B7\\u53D6\\u5230 \".concat(transformedOrders.length,\" \\u4E2A\\u5931\\u8D25\\u8BA2\\u5355\"));}else{message.error(result.message||'刷新失败');}}catch(error){console.error('刷新失败:',error);message.error('网络错误，请稍后重试');}finally{setLoading(false);}};// 获取失败类型颜色\nconst getFailureTypeColor=type=>{const colors={identity_verification:'red',credit_check:'orange',system_error:'purple',document_invalid:'volcano',other:'default'};return colors[type]||'default';};// 获取失败类型文本\nconst getFailureTypeText=type=>{const texts={identity_verification:'身份验证失败',credit_check:'信用检查失败',system_error:'系统错误',document_invalid:'证件无效',other:'其他原因'};return texts[type]||type;};// 获取优先级颜色\nconst getPriorityColor=priority=>{const colors={low:'default',normal:'blue',high:'orange',urgent:'red'};return colors[priority]||'default';};// 获取优先级文本\nconst getPriorityText=priority=>{const texts={low:'低',normal:'普通',high:'高',urgent:'紧急'};return texts[priority]||priority;};// 统计数据\nconst stats={total:orders.length,canRetry:orders.filter(order=>order.canRetry).length,identityFailed:orders.filter(order=>order.failureType==='identity_verification').length,systemError:orders.filter(order=>order.failureType==='system_error').length,urgent:orders.filter(order=>order.priority==='urgent').length};// 查看订单详情\nconst handleViewOrder=order=>{setSelectedOrder(order);setViewModalVisible(true);};// 重试订单\nconst handleRetryOrder=order=>{if(!order.canRetry){message.warning('该订单不支持重试');return;}setSelectedOrder(order);setRetryModalVisible(true);form.resetFields();};// 提交重试\nconst handleSubmitRetry=async()=>{try{const values=await form.validateFields();if(!selectedOrder){message.error('未选择订单');return;}// 调用API将订单状态改回pending\nconst result=await orderApi.updateStatus(selectedOrder.id,{status:'pending',process_notes:\"\\u91CD\\u8BD5\\u8BA2\\u5355\\uFF1A\".concat(values.retryReason),processed_by:'admin'});if(result.code===200){message.success('订单重试成功，已转入待处理队列');// 从失败订单列表移除\nsetOrders(orders.filter(order=>order.id!==selectedOrder.id));setRetryModalVisible(false);setSelectedOrder(null);form.resetFields();}else{message.error(result.message||'重试失败');}}catch(error){console.error('重试提交失败:',error);message.error('网络错误，请稍后重试');}};// 批量重试\nconst handleBatchRetry=()=>{if(selectedRowKeys.length===0){message.warning('请选择要重试的订单');return;}const retryableOrders=orders.filter(order=>selectedRowKeys.includes(order.id)&&order.canRetry);if(retryableOrders.length===0){message.warning('选中的订单都不支持重试');return;}Modal.confirm({title:'批量重试订单',content:\"\\u786E\\u5B9A\\u8981\\u91CD\\u8BD5\\u9009\\u4E2D\\u7684 \".concat(retryableOrders.length,\" \\u4E2A\\u8BA2\\u5355\\u5417\\uFF1F\"),onOk:()=>{setOrders(orders.filter(order=>!selectedRowKeys.includes(order.id)));setSelectedRowKeys([]);message.success(\"\\u6210\\u529F\\u91CD\\u8BD5 \".concat(retryableOrders.length,\" \\u4E2A\\u8BA2\\u5355\"));}});};// 表格列定义\nconst columns=[{title:'优先级',dataIndex:'priority',key:'priority',width:80,render:priority=>/*#__PURE__*/_jsx(Tag,{color:getPriorityColor(priority),children:getPriorityText(priority)}),sorter:(a,b)=>{const priorityOrder={urgent:4,high:3,normal:2,low:1};return priorityOrder[a.priority]-priorityOrder[b.priority];}},{title:'订单号',dataIndex:'orderNo',key:'orderNo',width:160,render:text=>/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:'12px'},children:text})},{title:'客户信息',key:'customer',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:600,fontSize:'13px'},children:record.customerName}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#666'},children:record.customerPhone})]})},{title:'产品信息',key:'product',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',marginBottom:'2px'},children:record.productName}),/*#__PURE__*/_jsx(Tag,{color:\"blue\",style:{fontSize:'11px',padding:'1px 6px'},children:record.operator})]})},{title:'失败原因',key:'failure',width:250,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'4px'},children:/*#__PURE__*/_jsx(Tag,{color:getFailureTypeColor(record.failureType),icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{}),children:getFailureTypeText(record.failureType)})}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'11px',color:'#666',lineHeight:1.4},children:record.failureReason})]})},{title:'重试信息',key:'retry',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',marginBottom:'2px'},children:[\"\\u91CD\\u8BD5\\u6B21\\u6570: \",record.retryCount]}),/*#__PURE__*/_jsx(\"div\",{children:record.canRetry?/*#__PURE__*/_jsx(Tag,{color:\"green\",icon:/*#__PURE__*/_jsx(RedoOutlined,{}),children:\"\\u53EF\\u91CD\\u8BD5\"}):/*#__PURE__*/_jsx(Tag,{color:\"red\",icon:/*#__PURE__*/_jsx(CloseCircleOutlined,{}),children:\"\\u4E0D\\u53EF\\u91CD\\u8BD5\"})})]})},{title:'失败时长',dataIndex:'failureDays',key:'failureDays',width:100,render:days=>/*#__PURE__*/_jsx(Text,{style:{color:days>3?'#f5222d':'#fa8c16'},children:days<1?\"\".concat(Math.round(days*24),\"\\u5C0F\\u65F6\"):\"\".concat(days.toFixed(1),\"\\u5929\")}),sorter:(a,b)=>a.failureDays-b.failureDays},{title:'失败时间',dataIndex:'failedAt',key:'failedAt',width:150,render:text=>/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px'},children:text})},{title:'操作',key:'action',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewOrder(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(RedoOutlined,{}),disabled:!record.canRetry,onClick:()=>handleRetryOrder(record),children:\"\\u91CD\\u8BD5\"})]})}];return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px'},children:[/*#__PURE__*/_jsxs(Title,{level:2,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(CloseCircleOutlined,{style:{marginRight:'8px',color:'#f5222d'}}),\"\\u5931\\u8D25\\u8BA2\\u5355\"]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5931\\u8D25\\u8BA2\\u5355\\u603B\\u6570\",value:stats.total,prefix:/*#__PURE__*/_jsx(CloseCircleOutlined,{}),valueStyle:{color:'#f5222d'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u53EF\\u91CD\\u8BD5\\u8BA2\\u5355\",value:stats.canRetry,prefix:/*#__PURE__*/_jsx(RedoOutlined,{}),valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5931\\u8D25\",value:stats.identityFailed,prefix:/*#__PURE__*/_jsx(WarningOutlined,{}),valueStyle:{color:'#fa8c16'}})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7D27\\u6025\\u5904\\u7406\",value:stats.urgent,prefix:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{}),valueStyle:{color:'#722ed1'}})})})]}),/*#__PURE__*/_jsx(Card,{style:{marginBottom:'16px'},children:/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Input,{placeholder:\"\\u641C\\u7D22\\u8BA2\\u5355\\u53F7\\u3001\\u5BA2\\u6237\\u59D3\\u540D\",prefix:/*#__PURE__*/_jsx(SearchOutlined,{}),style:{width:250}}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u5931\\u8D25\\u7C7B\\u578B\",style:{width:140},children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"\\u5168\\u90E8\"}),/*#__PURE__*/_jsx(Option,{value:\"identity_verification\",children:\"\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5931\\u8D25\"}),/*#__PURE__*/_jsx(Option,{value:\"credit_check\",children:\"\\u4FE1\\u7528\\u68C0\\u67E5\\u5931\\u8D25\"}),/*#__PURE__*/_jsx(Option,{value:\"system_error\",children:\"\\u7CFB\\u7EDF\\u9519\\u8BEF\"}),/*#__PURE__*/_jsx(Option,{value:\"document_invalid\",children:\"\\u8BC1\\u4EF6\\u65E0\\u6548\"}),/*#__PURE__*/_jsx(Option,{value:\"other\",children:\"\\u5176\\u4ED6\\u539F\\u56E0\"})]}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u662F\\u5426\\u53EF\\u91CD\\u8BD5\",style:{width:120},children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"\\u5168\\u90E8\"}),/*#__PURE__*/_jsx(Option,{value:\"true\",children:\"\\u53EF\\u91CD\\u8BD5\"}),/*#__PURE__*/_jsx(Option,{value:\"false\",children:\"\\u4E0D\\u53EF\\u91CD\\u8BD5\"})]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(SearchOutlined,{}),type:\"primary\",children:\"\\u641C\\u7D22\"})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsxs(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(RedoOutlined,{}),onClick:handleBatchRetry,disabled:selectedRowKeys.length===0,children:[\"\\u6279\\u91CF\\u91CD\\u8BD5 (\",selectedRowKeys.length,\")\"]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:handleRefresh,children:\"\\u5237\\u65B0\"})]})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:orders,rowKey:\"id\",loading:loading,scroll:{x:1500},rowSelection:{selectedRowKeys,onChange:setSelectedRowKeys,getCheckboxProps:record=>({disabled:!record.canRetry// 不可重试的订单不允许选择\n})},pagination:{total:orders.length,pageSize:10,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5931\\u8D25\\u8BA2\\u5355\\u8BE6\\u60C5\",open:viewModalVisible,onCancel:()=>setViewModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setViewModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u8BA2\\u5355\\u5904\\u7406\\u5931\\u8D25\",description:selectedOrder.failureReason,type:\"error\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Card,{title:\"\\u5BA2\\u6237\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u59D3\\u540D\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.customerName})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u624B\\u673A\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.customerPhone})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8EAB\\u4EFD\\u8BC1\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.customerIdCard})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u4EA7\\u54C1\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4EA7\\u54C1\\u540D\\u79F0\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.productName})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8FD0\\u8425\\u5546\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u6536\\u8D27\\u5730\\u5740\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsx(\"div\",{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(Text,{children:selectedOrder.deliveryAddress})})}),/*#__PURE__*/_jsxs(Card,{title:\"\\u5931\\u8D25\\u4FE1\\u606F\",size:\"small\",children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BA2\\u5355\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{code:true,children:selectedOrder.orderNo})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5931\\u8D25\\u7C7B\\u578B\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:getFailureTypeColor(selectedOrder.failureType),children:getFailureTypeText(selectedOrder.failureType)})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4F18\\u5148\\u7EA7\\uFF1A\"}),/*#__PURE__*/_jsx(Tag,{color:getPriorityColor(selectedOrder.priority),children:getPriorityText(selectedOrder.priority)})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5931\\u8D25\\u65F6\\u95F4\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.failedAt})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u91CD\\u8BD5\\u6B21\\u6570\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.retryCount})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5904\\u7406\\u4EBA\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:selectedOrder.processedBy})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u662F\\u5426\\u53EF\\u91CD\\u8BD5\\uFF1A\"}),selectedOrder.canRetry?/*#__PURE__*/_jsx(Tag,{color:\"green\",icon:/*#__PURE__*/_jsx(RedoOutlined,{}),children:\"\\u53EF\\u91CD\\u8BD5\"}):/*#__PURE__*/_jsx(Tag,{color:\"red\",icon:/*#__PURE__*/_jsx(CloseCircleOutlined,{}),children:\"\\u4E0D\\u53EF\\u91CD\\u8BD5\"})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5931\\u8D25\\u65F6\\u957F\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{style:{color:selectedOrder.failureDays>3?'#f5222d':'#fa8c16'},children:selectedOrder.failureDays<1?\"\".concat(Math.round(selectedOrder.failureDays*24),\"\\u5C0F\\u65F6\"):\"\".concat(selectedOrder.failureDays.toFixed(1),\"\\u5929\")})]})})]}),/*#__PURE__*/_jsx(Row,{gutter:16,children:/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5931\\u8D25\\u539F\\u56E0\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4,color:'#666',lineHeight:1.5,padding:'8px',background:'#fff2f0',borderRadius:'4px'},children:selectedOrder.failureReason})]})})})]})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u91CD\\u8BD5\\u8BA2\\u5355\",open:retryModalVisible,onCancel:()=>setRetryModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setRetryModalVisible(false),children:\"\\u53D6\\u6D88\"},\"cancel\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:handleSubmitRetry,children:\"\\u786E\\u8BA4\\u91CD\\u8BD5\"},\"submit\")],width:600,children:selectedOrder&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{title:\"\\u8BA2\\u5355\\u4FE1\\u606F\",size:\"small\",style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Descriptions,{column:2,size:\"small\",children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u53F7\",children:selectedOrder.orderNo}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u59D3\\u540D\",children:selectedOrder.customerName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u624B\\u673A\\u53F7\",children:selectedOrder.customerPhone}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u540D\\u79F0\",children:selectedOrder.productName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8FD0\\u8425\\u5546\",children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:selectedOrder.operator})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u91CD\\u8BD5\\u6B21\\u6570\",children:selectedOrder.retryCount})]}),/*#__PURE__*/_jsx(Descriptions,{column:1,size:\"small\",children:/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5931\\u8D25\\u539F\\u56E0\",children:/*#__PURE__*/_jsx(\"div\",{style:{color:'#f5222d'},children:selectedOrder.failureReason})})})]}),/*#__PURE__*/_jsxs(Card,{title:\"\\u91CD\\u8BD5\\u8BF4\\u660E\",size:\"small\",children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u91CD\\u8BD5\\u6CE8\\u610F\\u4E8B\\u9879\",description:/*#__PURE__*/_jsxs(\"ul\",{style:{margin:0,paddingLeft:'20px'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u91CD\\u8BD5\\u524D\\u8BF7\\u786E\\u8BA4\\u5931\\u8D25\\u539F\\u56E0\\u5DF2\\u89E3\\u51B3\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5931\\u8D25\\uFF1A\\u8BF7\\u786E\\u8BA4\\u5BA2\\u6237\\u4FE1\\u606F\\u51C6\\u786E\\u65E0\\u8BEF\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u7CFB\\u7EDF\\u9519\\u8BEF\\uFF1A\\u7CFB\\u7EDF\\u5DF2\\u4FEE\\u590D\\uFF0C\\u53EF\\u76F4\\u63A5\\u91CD\\u8BD5\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u8BC1\\u4EF6\\u65E0\\u6548\\uFF1A\\u8BF7\\u8054\\u7CFB\\u5BA2\\u6237\\u91CD\\u65B0\\u4E0A\\u4F20\\u6E05\\u6670\\u8BC1\\u4EF6\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u91CD\\u8BD5\\u540E\\u8BA2\\u5355\\u5C06\\u91CD\\u65B0\\u8FDB\\u5165\\u5904\\u7406\\u6D41\\u7A0B\"})]}),type:\"info\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Form,{form:form,layout:\"vertical\",children:/*#__PURE__*/_jsx(Form.Item,{name:\"retryReason\",label:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u91CD\\u8BD5\\u539F\\u56E0\"}),rules:[{required:true,message:'请填写重试原因'}],children:/*#__PURE__*/_jsx(Input.TextArea,{rows:3,placeholder:\"\\u8BF7\\u8BF4\\u660E\\u91CD\\u8BD5\\u7684\\u539F\\u56E0\\u548C\\u5DF2\\u91C7\\u53D6\\u7684\\u89E3\\u51B3\\u63AA\\u65BD...\",maxLength:300,showCount:true})})})]})]})})]});};export default OrderFailed;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
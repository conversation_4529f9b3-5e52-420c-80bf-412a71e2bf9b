{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useClosable, { pickClosable } from '../_util/hooks/useClosable';\nimport { useComponentConfig } from '../config-provider/context';\nimport Skeleton from '../skeleton';\nconst DrawerPanel = props => {\n  var _a, _b;\n  const {\n    prefixCls,\n    title,\n    footer,\n    extra,\n    loading,\n    onClose,\n    headerStyle,\n    bodyStyle,\n    footerStyle,\n    children,\n    classNames: drawerClassNames,\n    styles: drawerStyles\n  } = props;\n  const drawerContext = useComponentConfig('drawer');\n  const customCloseIconRender = React.useCallback(icon => (/*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onClose,\n    className: \"\".concat(prefixCls, \"-close\")\n  }, icon)), [onClose]);\n  const [mergedClosable, mergedCloseIcon] = useClosable(pickClosable(props), pickClosable(drawerContext), {\n    closable: true,\n    closeIconRender: customCloseIconRender\n  });\n  const headerNode = React.useMemo(() => {\n    var _a, _b;\n    if (!title && !mergedClosable) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: Object.assign(Object.assign(Object.assign({}, (_a = drawerContext.styles) === null || _a === void 0 ? void 0 : _a.header), headerStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.header),\n      className: classNames(\"\".concat(prefixCls, \"-header\"), {\n        [\"\".concat(prefixCls, \"-header-close-only\")]: mergedClosable && !title && !extra\n      }, (_b = drawerContext.classNames) === null || _b === void 0 ? void 0 : _b.header, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.header)\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-header-title\")\n    }, mergedCloseIcon, title && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-title\")\n    }, title)), extra && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-extra\")\n    }, extra));\n  }, [mergedClosable, mergedCloseIcon, extra, headerStyle, prefixCls, title]);\n  const footerNode = React.useMemo(() => {\n    var _a, _b;\n    if (!footer) {\n      return null;\n    }\n    const footerClassName = \"\".concat(prefixCls, \"-footer\");\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(footerClassName, (_a = drawerContext.classNames) === null || _a === void 0 ? void 0 : _a.footer, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.footer),\n      style: Object.assign(Object.assign(Object.assign({}, (_b = drawerContext.styles) === null || _b === void 0 ? void 0 : _b.footer), footerStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.footer)\n    }, footer);\n  }, [footer, footerStyle, prefixCls]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, headerNode, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-body\"), drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.body, (_a = drawerContext.classNames) === null || _a === void 0 ? void 0 : _a.body),\n    style: Object.assign(Object.assign(Object.assign({}, (_b = drawerContext.styles) === null || _b === void 0 ? void 0 : _b.body), bodyStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.body)\n  }, loading ? (/*#__PURE__*/React.createElement(Skeleton, {\n    active: true,\n    title: false,\n    paragraph: {\n      rows: 5\n    },\n    className: \"\".concat(prefixCls, \"-body-skeleton\")\n  })) : children), footerNode);\n};\nexport default DrawerPanel;", "map": {"version": 3, "names": ["React", "classNames", "useClosable", "pickClosable", "useComponentConfig", "Skeleton", "<PERSON>er<PERSON><PERSON><PERSON>", "props", "_a", "_b", "prefixCls", "title", "footer", "extra", "loading", "onClose", "headerStyle", "bodyStyle", "footerStyle", "children", "drawerClassNames", "styles", "drawerStyles", "drawerContext", "customCloseIconRender", "useCallback", "icon", "createElement", "type", "onClick", "className", "concat", "mergedClosable", "mergedCloseIcon", "closable", "closeIconRender", "headerNode", "useMemo", "style", "Object", "assign", "header", "footerNode", "footerClassName", "Fragment", "body", "active", "paragraph", "rows"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/drawer/DrawerPanel.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useClosable, { pickClosable } from '../_util/hooks/useClosable';\nimport { useComponentConfig } from '../config-provider/context';\nimport Skeleton from '../skeleton';\nconst DrawerPanel = props => {\n  var _a, _b;\n  const {\n    prefixCls,\n    title,\n    footer,\n    extra,\n    loading,\n    onClose,\n    headerStyle,\n    bodyStyle,\n    footerStyle,\n    children,\n    classNames: drawerClassNames,\n    styles: drawerStyles\n  } = props;\n  const drawerContext = useComponentConfig('drawer');\n  const customCloseIconRender = React.useCallback(icon => (/*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onClose,\n    className: `${prefixCls}-close`\n  }, icon)), [onClose]);\n  const [mergedClosable, mergedCloseIcon] = useClosable(pickClosable(props), pickClosable(drawerContext), {\n    closable: true,\n    closeIconRender: customCloseIconRender\n  });\n  const headerNode = React.useMemo(() => {\n    var _a, _b;\n    if (!title && !mergedClosable) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: Object.assign(Object.assign(Object.assign({}, (_a = drawerContext.styles) === null || _a === void 0 ? void 0 : _a.header), headerStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.header),\n      className: classNames(`${prefixCls}-header`, {\n        [`${prefixCls}-header-close-only`]: mergedClosable && !title && !extra\n      }, (_b = drawerContext.classNames) === null || _b === void 0 ? void 0 : _b.header, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.header)\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-header-title`\n    }, mergedCloseIcon, title && /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-title`\n    }, title)), extra && /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-extra`\n    }, extra));\n  }, [mergedClosable, mergedCloseIcon, extra, headerStyle, prefixCls, title]);\n  const footerNode = React.useMemo(() => {\n    var _a, _b;\n    if (!footer) {\n      return null;\n    }\n    const footerClassName = `${prefixCls}-footer`;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(footerClassName, (_a = drawerContext.classNames) === null || _a === void 0 ? void 0 : _a.footer, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.footer),\n      style: Object.assign(Object.assign(Object.assign({}, (_b = drawerContext.styles) === null || _b === void 0 ? void 0 : _b.footer), footerStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.footer)\n    }, footer);\n  }, [footer, footerStyle, prefixCls]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, headerNode, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-body`, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.body, (_a = drawerContext.classNames) === null || _a === void 0 ? void 0 : _a.body),\n    style: Object.assign(Object.assign(Object.assign({}, (_b = drawerContext.styles) === null || _b === void 0 ? void 0 : _b.body), bodyStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.body)\n  }, loading ? (/*#__PURE__*/React.createElement(Skeleton, {\n    active: true,\n    title: false,\n    paragraph: {\n      rows: 5\n    },\n    className: `${prefixCls}-body-skeleton`\n  })) : children), footerNode);\n};\nexport default DrawerPanel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,IAAIC,YAAY,QAAQ,4BAA4B;AACtE,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,QAAQ,MAAM,aAAa;AAClC,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAC3B,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IACJC,SAAS;IACTC,KAAK;IACLC,MAAM;IACNC,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC,QAAQ;IACRlB,UAAU,EAAEmB,gBAAgB;IAC5BC,MAAM,EAAEC;EACV,CAAC,GAAGf,KAAK;EACT,MAAMgB,aAAa,GAAGnB,kBAAkB,CAAC,QAAQ,CAAC;EAClD,MAAMoB,qBAAqB,GAAGxB,KAAK,CAACyB,WAAW,CAACC,IAAI,KAAK,aAAa1B,KAAK,CAAC2B,aAAa,CAAC,QAAQ,EAAE;IAClGC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAEd,OAAO;IAChBe,SAAS,KAAAC,MAAA,CAAKrB,SAAS;EACzB,CAAC,EAAEgB,IAAI,CAAC,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;EACrB,MAAM,CAACiB,cAAc,EAAEC,eAAe,CAAC,GAAG/B,WAAW,CAACC,YAAY,CAACI,KAAK,CAAC,EAAEJ,YAAY,CAACoB,aAAa,CAAC,EAAE;IACtGW,QAAQ,EAAE,IAAI;IACdC,eAAe,EAAEX;EACnB,CAAC,CAAC;EACF,MAAMY,UAAU,GAAGpC,KAAK,CAACqC,OAAO,CAAC,MAAM;IACrC,IAAI7B,EAAE,EAAEC,EAAE;IACV,IAAI,CAACE,KAAK,IAAI,CAACqB,cAAc,EAAE;MAC7B,OAAO,IAAI;IACb;IACA,OAAO,aAAahC,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;MAC7CW,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAChC,EAAE,GAAGe,aAAa,CAACF,MAAM,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiC,MAAM,CAAC,EAAEzB,WAAW,CAAC,EAAEM,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACmB,MAAM,CAAC;MAChOX,SAAS,EAAE7B,UAAU,IAAA8B,MAAA,CAAIrB,SAAS,cAAW;QAC3C,IAAAqB,MAAA,CAAIrB,SAAS,0BAAuBsB,cAAc,IAAI,CAACrB,KAAK,IAAI,CAACE;MACnE,CAAC,EAAE,CAACJ,EAAE,GAAGc,aAAa,CAACtB,UAAU,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgC,MAAM,EAAErB,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACqB,MAAM;IAChL,CAAC,EAAE,aAAazC,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;MACzCG,SAAS,KAAAC,MAAA,CAAKrB,SAAS;IACzB,CAAC,EAAEuB,eAAe,EAAEtB,KAAK,IAAI,aAAaX,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;MACnEG,SAAS,KAAAC,MAAA,CAAKrB,SAAS;IACzB,CAAC,EAAEC,KAAK,CAAC,CAAC,EAAEE,KAAK,IAAI,aAAab,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;MAC3DG,SAAS,KAAAC,MAAA,CAAKrB,SAAS;IACzB,CAAC,EAAEG,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACmB,cAAc,EAAEC,eAAe,EAAEpB,KAAK,EAAEG,WAAW,EAAEN,SAAS,EAAEC,KAAK,CAAC,CAAC;EAC3E,MAAM+B,UAAU,GAAG1C,KAAK,CAACqC,OAAO,CAAC,MAAM;IACrC,IAAI7B,EAAE,EAAEC,EAAE;IACV,IAAI,CAACG,MAAM,EAAE;MACX,OAAO,IAAI;IACb;IACA,MAAM+B,eAAe,MAAAZ,MAAA,CAAMrB,SAAS,YAAS;IAC7C,OAAO,aAAaV,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;MAC7CG,SAAS,EAAE7B,UAAU,CAAC0C,eAAe,EAAE,CAACnC,EAAE,GAAGe,aAAa,CAACtB,UAAU,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,MAAM,EAAEQ,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACR,MAAM,CAAC;MACnN0B,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC/B,EAAE,GAAGc,aAAa,CAACF,MAAM,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,MAAM,CAAC,EAAEM,WAAW,CAAC,EAAEI,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACV,MAAM;IACjO,CAAC,EAAEA,MAAM,CAAC;EACZ,CAAC,EAAE,CAACA,MAAM,EAAEM,WAAW,EAAER,SAAS,CAAC,CAAC;EACpC,OAAO,aAAaV,KAAK,CAAC2B,aAAa,CAAC3B,KAAK,CAAC4C,QAAQ,EAAE,IAAI,EAAER,UAAU,EAAE,aAAapC,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;IAChHG,SAAS,EAAE7B,UAAU,IAAA8B,MAAA,CAAIrB,SAAS,YAASU,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACyB,IAAI,EAAE,CAACrC,EAAE,GAAGe,aAAa,CAACtB,UAAU,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,IAAI,CAAC;IACnNP,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC/B,EAAE,GAAGc,aAAa,CAACF,MAAM,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoC,IAAI,CAAC,EAAE5B,SAAS,CAAC,EAAEK,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACuB,IAAI;EAC3N,CAAC,EAAE/B,OAAO,IAAI,aAAad,KAAK,CAAC2B,aAAa,CAACtB,QAAQ,EAAE;IACvDyC,MAAM,EAAE,IAAI;IACZnC,KAAK,EAAE,KAAK;IACZoC,SAAS,EAAE;MACTC,IAAI,EAAE;IACR,CAAC;IACDlB,SAAS,KAAAC,MAAA,CAAKrB,SAAS;EACzB,CAAC,CAAC,IAAIS,QAAQ,CAAC,EAAEuB,UAAU,CAAC;AAC9B,CAAC;AACD,eAAepC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ApartmentOutlinedSvg from \"@ant-design/icons-svg/es/asn/ApartmentOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ApartmentOutlined = function ApartmentOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ApartmentOutlinedSvg\n  }));\n};\n\n/**![apartment](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwOCA2NDBIODA0VjQ4OGMwLTQuNC0zLjYtOC04LThINTQ4di05NmgxMDhjOC44IDAgMTYtNy4yIDE2LTE2VjgwYzAtOC44LTcuMi0xNi0xNi0xNkgzNjhjLTguOCAwLTE2IDcuMi0xNiAxNnYyODhjMCA4LjggNy4yIDE2IDE2IDE2aDEwOHY5NkgyMjhjLTQuNCAwLTggMy42LTggOHYxNTJIMTE2Yy04LjggMC0xNiA3LjItMTYgMTZ2Mjg4YzAgOC44IDcuMiAxNiAxNiAxNmgyODhjOC44IDAgMTYtNy4yIDE2LTE2VjY1NmMwLTguOC03LjItMTYtMTYtMTZIMjkydi04OGg0NDB2ODhINjIwYy04LjggMC0xNiA3LjItMTYgMTZ2Mjg4YzAgOC44IDcuMiAxNiAxNiAxNmgyODhjOC44IDAgMTYtNy4yIDE2LTE2VjY1NmMwLTguOC03LjItMTYtMTYtMTZ6bS01NjQgNzZ2MTY4SDE3NlY3MTZoMTY4em04NC00MDhWMTQwaDE2OHYxNjhINDI4em00MjAgNTc2SDY4MFY3MTZoMTY4djE2OHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ApartmentOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ApartmentOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "ApartmentOutlinedSvg", "AntdIcon", "ApartmentOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/ApartmentOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ApartmentOutlinedSvg from \"@ant-design/icons-svg/es/asn/ApartmentOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ApartmentOutlined = function ApartmentOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ApartmentOutlinedSvg\n  }));\n};\n\n/**![apartment](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwOCA2NDBIODA0VjQ4OGMwLTQuNC0zLjYtOC04LThINTQ4di05NmgxMDhjOC44IDAgMTYtNy4yIDE2LTE2VjgwYzAtOC44LTcuMi0xNi0xNi0xNkgzNjhjLTguOCAwLTE2IDcuMi0xNiAxNnYyODhjMCA4LjggNy4yIDE2IDE2IDE2aDEwOHY5NkgyMjhjLTQuNCAwLTggMy42LTggOHYxNTJIMTE2Yy04LjggMC0xNiA3LjItMTYgMTZ2Mjg4YzAgOC44IDcuMiAxNiAxNiAxNmgyODhjOC44IDAgMTYtNy4yIDE2LTE2VjY1NmMwLTguOC03LjItMTYtMTYtMTZIMjkydi04OGg0NDB2ODhINjIwYy04LjggMC0xNiA3LjItMTYgMTZ2Mjg4YzAgOC44IDcuMiAxNiAxNiAxNmgyODhjOC44IDAgMTYtNy4yIDE2LTE2VjY1NmMwLTguOC03LjItMTYtMTYtMTZ6bS01NjQgNzZ2MTY4SDE3NlY3MTZoMTY4em04NC00MDhWMTQwaDE2OHYxNjhINDI4em00MjAgNTc2SDY4MFY3MTZoMTY4djE2OHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ApartmentOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ApartmentOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC;AAC9D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    controlSize,\n    dotSize,\n    marginFull,\n    marginPart,\n    colorFillContentHover,\n    handleColorDisabled,\n    calc,\n    handleSize,\n    handleSizeHover,\n    handleActiveColor,\n    handleActiveOutlineColor,\n    handleLineWidth,\n    handleLineWidthHover,\n    motionDurationMid\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      height: controlSize,\n      margin: \"\".concat(unit(marginPart), \" \").concat(unit(marginFull)),\n      padding: 0,\n      cursor: 'pointer',\n      touchAction: 'none',\n      '&-vertical': {\n        margin: \"\".concat(unit(marginFull), \" \").concat(unit(marginPart))\n      },\n      [\"\".concat(componentCls, \"-rail\")]: {\n        position: 'absolute',\n        backgroundColor: token.railBg,\n        borderRadius: token.borderRadiusXS,\n        transition: \"background-color \".concat(motionDurationMid)\n      },\n      [\"\".concat(componentCls, \"-track,\").concat(componentCls, \"-tracks\")]: {\n        position: 'absolute',\n        transition: \"background-color \".concat(motionDurationMid)\n      },\n      [\"\".concat(componentCls, \"-track\")]: {\n        backgroundColor: token.trackBg,\n        borderRadius: token.borderRadiusXS\n      },\n      [\"\".concat(componentCls, \"-track-draggable\")]: {\n        boxSizing: 'content-box',\n        backgroundClip: 'content-box',\n        border: 'solid rgba(0,0,0,0)'\n      },\n      '&:hover': {\n        [\"\".concat(componentCls, \"-rail\")]: {\n          backgroundColor: token.railHoverBg\n        },\n        [\"\".concat(componentCls, \"-track\")]: {\n          backgroundColor: token.trackHoverBg\n        },\n        [\"\".concat(componentCls, \"-dot\")]: {\n          borderColor: colorFillContentHover\n        },\n        [\"\".concat(componentCls, \"-handle::after\")]: {\n          boxShadow: \"0 0 0 \".concat(unit(handleLineWidth), \" \").concat(token.colorPrimaryBorderHover)\n        },\n        [\"\".concat(componentCls, \"-dot-active\")]: {\n          borderColor: token.dotActiveBorderColor\n        }\n      },\n      [\"\".concat(componentCls, \"-handle\")]: {\n        position: 'absolute',\n        width: handleSize,\n        height: handleSize,\n        outline: 'none',\n        userSelect: 'none',\n        // Dragging status\n        '&-dragging-delete': {\n          opacity: 0\n        },\n        // 扩大选区\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          insetInlineStart: calc(handleLineWidth).mul(-1).equal(),\n          insetBlockStart: calc(handleLineWidth).mul(-1).equal(),\n          width: calc(handleSize).add(calc(handleLineWidth).mul(2)).equal(),\n          height: calc(handleSize).add(calc(handleLineWidth).mul(2)).equal(),\n          backgroundColor: 'transparent'\n        },\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          insetBlockStart: 0,\n          insetInlineStart: 0,\n          width: handleSize,\n          height: handleSize,\n          backgroundColor: token.colorBgElevated,\n          boxShadow: \"0 0 0 \".concat(unit(handleLineWidth), \" \").concat(token.handleColor),\n          outline: \"0px solid transparent\",\n          borderRadius: '50%',\n          cursor: 'pointer',\n          transition: \"\\n            inset-inline-start \".concat(motionDurationMid, \",\\n            inset-block-start \").concat(motionDurationMid, \",\\n            width \").concat(motionDurationMid, \",\\n            height \").concat(motionDurationMid, \",\\n            box-shadow \").concat(motionDurationMid, \",\\n            outline \").concat(motionDurationMid, \"\\n          \")\n        },\n        '&:hover, &:active, &:focus': {\n          '&::before': {\n            insetInlineStart: calc(handleSizeHover).sub(handleSize).div(2).add(handleLineWidthHover).mul(-1).equal(),\n            insetBlockStart: calc(handleSizeHover).sub(handleSize).div(2).add(handleLineWidthHover).mul(-1).equal(),\n            width: calc(handleSizeHover).add(calc(handleLineWidthHover).mul(2)).equal(),\n            height: calc(handleSizeHover).add(calc(handleLineWidthHover).mul(2)).equal()\n          },\n          '&::after': {\n            boxShadow: \"0 0 0 \".concat(unit(handleLineWidthHover), \" \").concat(handleActiveColor),\n            outline: \"6px solid \".concat(handleActiveOutlineColor),\n            width: handleSizeHover,\n            height: handleSizeHover,\n            insetInlineStart: token.calc(handleSize).sub(handleSizeHover).div(2).equal(),\n            insetBlockStart: token.calc(handleSize).sub(handleSizeHover).div(2).equal()\n          }\n        }\n      },\n      [\"&-lock \".concat(componentCls, \"-handle\")]: {\n        '&::before, &::after': {\n          transition: 'none'\n        }\n      },\n      [\"\".concat(componentCls, \"-mark\")]: {\n        position: 'absolute',\n        fontSize: token.fontSize\n      },\n      [\"\".concat(componentCls, \"-mark-text\")]: {\n        position: 'absolute',\n        display: 'inline-block',\n        color: token.colorTextDescription,\n        textAlign: 'center',\n        wordBreak: 'keep-all',\n        cursor: 'pointer',\n        userSelect: 'none',\n        '&-active': {\n          color: token.colorText\n        }\n      },\n      [\"\".concat(componentCls, \"-step\")]: {\n        position: 'absolute',\n        background: 'transparent',\n        pointerEvents: 'none'\n      },\n      [\"\".concat(componentCls, \"-dot\")]: {\n        position: 'absolute',\n        width: dotSize,\n        height: dotSize,\n        backgroundColor: token.colorBgElevated,\n        border: \"\".concat(unit(handleLineWidth), \" solid \").concat(token.dotBorderColor),\n        borderRadius: '50%',\n        cursor: 'pointer',\n        transition: \"border-color \".concat(token.motionDurationSlow),\n        pointerEvents: 'auto',\n        '&-active': {\n          borderColor: token.dotActiveBorderColor\n        }\n      },\n      [\"&\".concat(componentCls, \"-disabled\")]: {\n        cursor: 'not-allowed',\n        [\"\".concat(componentCls, \"-rail\")]: {\n          backgroundColor: \"\".concat(token.railBg, \" !important\")\n        },\n        [\"\".concat(componentCls, \"-track\")]: {\n          backgroundColor: \"\".concat(token.trackBgDisabled, \" !important\")\n        },\n        [\"\\n          \".concat(componentCls, \"-dot\\n        \")]: {\n          backgroundColor: token.colorBgElevated,\n          borderColor: token.trackBgDisabled,\n          boxShadow: 'none',\n          cursor: 'not-allowed'\n        },\n        [\"\".concat(componentCls, \"-handle::after\")]: {\n          backgroundColor: token.colorBgElevated,\n          cursor: 'not-allowed',\n          width: handleSize,\n          height: handleSize,\n          boxShadow: \"0 0 0 \".concat(unit(handleLineWidth), \" \").concat(handleColorDisabled),\n          insetInlineStart: 0,\n          insetBlockStart: 0\n        },\n        [\"\\n          \".concat(componentCls, \"-mark-text,\\n          \").concat(componentCls, \"-dot\\n        \")]: {\n          cursor: \"not-allowed !important\"\n        }\n      },\n      [\"&-tooltip \".concat(antCls, \"-tooltip-inner\")]: {\n        minWidth: 'unset'\n      }\n    })\n  };\n};\n// ============================ Horizontal ============================\nconst genDirectionStyle = (token, horizontal) => {\n  const {\n    componentCls,\n    railSize,\n    handleSize,\n    dotSize,\n    marginFull,\n    calc\n  } = token;\n  const railPadding = horizontal ? 'paddingBlock' : 'paddingInline';\n  const full = horizontal ? 'width' : 'height';\n  const part = horizontal ? 'height' : 'width';\n  const handlePos = horizontal ? 'insetBlockStart' : 'insetInlineStart';\n  const markInset = horizontal ? 'top' : 'insetInlineStart';\n  const handlePosSize = calc(railSize).mul(3).sub(handleSize).div(2).equal();\n  const draggableBorderSize = calc(handleSize).sub(railSize).div(2).equal();\n  const draggableBorder = horizontal ? {\n    borderWidth: \"\".concat(unit(draggableBorderSize), \" 0\"),\n    transform: \"translateY(\".concat(unit(calc(draggableBorderSize).mul(-1).equal()), \")\")\n  } : {\n    borderWidth: \"0 \".concat(unit(draggableBorderSize)),\n    transform: \"translateX(\".concat(unit(token.calc(draggableBorderSize).mul(-1).equal()), \")\")\n  };\n  return {\n    [railPadding]: railSize,\n    [part]: calc(railSize).mul(3).equal(),\n    [\"\".concat(componentCls, \"-rail\")]: {\n      [full]: '100%',\n      [part]: railSize\n    },\n    [\"\".concat(componentCls, \"-track,\").concat(componentCls, \"-tracks\")]: {\n      [part]: railSize\n    },\n    [\"\".concat(componentCls, \"-track-draggable\")]: Object.assign({}, draggableBorder),\n    [\"\".concat(componentCls, \"-handle\")]: {\n      [handlePos]: handlePosSize\n    },\n    [\"\".concat(componentCls, \"-mark\")]: {\n      // Reset all\n      insetInlineStart: 0,\n      top: 0,\n      // https://github.com/ant-design/ant-design/issues/43731\n      [markInset]: calc(railSize).mul(3).add(horizontal ? 0 : marginFull).equal(),\n      [full]: '100%'\n    },\n    [\"\".concat(componentCls, \"-step\")]: {\n      // Reset all\n      insetInlineStart: 0,\n      top: 0,\n      [markInset]: railSize,\n      [full]: '100%',\n      [part]: railSize\n    },\n    [\"\".concat(componentCls, \"-dot\")]: {\n      position: 'absolute',\n      [handlePos]: calc(railSize).sub(dotSize).div(2).equal()\n    }\n  };\n};\n// ============================ Horizontal ============================\nconst genHorizontalStyle = token => {\n  const {\n    componentCls,\n    marginPartWithMark\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-horizontal\")]: Object.assign(Object.assign({}, genDirectionStyle(token, true)), {\n      [\"&\".concat(componentCls, \"-with-marks\")]: {\n        marginBottom: marginPartWithMark\n      }\n    })\n  };\n};\n// ============================= Vertical =============================\nconst genVerticalStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-vertical\")]: Object.assign(Object.assign({}, genDirectionStyle(token, false)), {\n      height: '100%'\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  // Handle line width is always width-er 1px\n  const increaseHandleWidth = 1;\n  const controlSize = token.controlHeightLG / 4;\n  const controlSizeHover = token.controlHeightSM / 2;\n  const handleLineWidth = token.lineWidth + increaseHandleWidth;\n  const handleLineWidthHover = token.lineWidth + increaseHandleWidth * 1.5;\n  const handleActiveColor = token.colorPrimary;\n  const handleActiveOutlineColor = new FastColor(handleActiveColor).setA(0.2).toRgbString();\n  return {\n    controlSize,\n    railSize: 4,\n    handleSize: controlSize,\n    handleSizeHover: controlSizeHover,\n    dotSize: 8,\n    handleLineWidth,\n    handleLineWidthHover,\n    railBg: token.colorFillTertiary,\n    railHoverBg: token.colorFillSecondary,\n    trackBg: token.colorPrimaryBorder,\n    trackHoverBg: token.colorPrimaryBorderHover,\n    handleColor: token.colorPrimaryBorder,\n    handleActiveColor,\n    handleActiveOutlineColor,\n    handleColorDisabled: new FastColor(token.colorTextDisabled).onBackground(token.colorBgContainer).toHexString(),\n    dotBorderColor: token.colorBorderSecondary,\n    dotActiveBorderColor: token.colorPrimaryBorder,\n    trackBgDisabled: token.colorBgContainerDisabled\n  };\n};\nexport default genStyleHooks('Slider', token => {\n  const sliderToken = mergeToken(token, {\n    marginPart: token.calc(token.controlHeight).sub(token.controlSize).div(2).equal(),\n    marginFull: token.calc(token.controlSize).div(2).equal(),\n    marginPartWithMark: token.calc(token.controlHeightLG).sub(token.controlSize).equal()\n  });\n  return [genBaseStyle(sliderToken), genHorizontalStyle(sliderToken), genVerticalStyle(sliderToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst CheckableTag = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      style,\n      className,\n      checked,\n      onChange,\n      onClick\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"style\", \"className\", \"checked\", \"onChange\", \"onClick\"]);\n  const {\n    getPrefixCls,\n    tag\n  } = React.useContext(ConfigContext);\n  const handleClick = e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(!checked);\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const prefixCls = getPrefixCls('tag', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, \"\".concat(prefixCls, \"-checkable\"), {\n    [\"\".concat(prefixCls, \"-checkable-checked\")]: checked\n  }, tag === null || tag === void 0 ? void 0 : tag.className, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, restProps, {\n    ref: ref,\n    style: Object.assign(Object.assign({}, style), tag === null || tag === void 0 ? void 0 : tag.style),\n    className: cls,\n    onClick: handleClick\n  })));\n});\nexport default CheckableTag;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ConfigContext", "useStyle", "CheckableTag", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "style", "className", "checked", "onChange", "onClick", "restProps", "getPrefixCls", "tag", "useContext", "handleClick", "wrapCSSVar", "hashId", "cssVarCls", "cls", "concat", "createElement", "assign"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/tag/CheckableTag.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst CheckableTag = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      style,\n      className,\n      checked,\n      onChange,\n      onClick\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"style\", \"className\", \"checked\", \"onChange\", \"onClick\"]);\n  const {\n    getPrefixCls,\n    tag\n  } = React.useContext(ConfigContext);\n  const handleClick = e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(!checked);\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const prefixCls = getPrefixCls('tag', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-checkable`, {\n    [`${prefixCls}-checkable-checked`]: checked\n  }, tag === null || tag === void 0 ? void 0 : tag.className, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, restProps, {\n    ref: ref,\n    style: Object.assign(Object.assign({}, style), tag === null || tag === void 0 ? void 0 : tag.style),\n    className: cls,\n    onClick: handleClick\n  })));\n});\nexport default CheckableTag;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,YAAY,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,KAAK;MACLC,SAAS;MACTC,OAAO;MACPC,QAAQ;MACRC;IACF,CAAC,GAAGR,KAAK;IACTS,SAAS,GAAG7B,MAAM,CAACoB,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;EAClG,MAAM;IACJU,YAAY;IACZC;EACF,CAAC,GAAGjB,KAAK,CAACkB,UAAU,CAAChB,aAAa,CAAC;EACnC,MAAMiB,WAAW,GAAG/B,CAAC,IAAI;IACvByB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAACD,OAAO,CAAC;IACtEE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC1B,CAAC,CAAC;EAC9D,CAAC;EACD,MAAMoB,SAAS,GAAGQ,YAAY,CAAC,KAAK,EAAEP,kBAAkB,CAAC;EACzD;EACA,MAAM,CAACW,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAACK,SAAS,CAAC;EAC3D,MAAMe,GAAG,GAAGtB,UAAU,CAACO,SAAS,KAAAgB,MAAA,CAAKhB,SAAS,iBAAc;IAC1D,IAAAgB,MAAA,CAAIhB,SAAS,0BAAuBI;EACtC,CAAC,EAAEK,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACN,SAAS,EAAEA,SAAS,EAAEU,MAAM,EAAEC,SAAS,CAAC;EACzF,OAAOF,UAAU,CAAC,aAAapB,KAAK,CAACyB,aAAa,CAAC,MAAM,EAAElC,MAAM,CAACmC,MAAM,CAAC,CAAC,CAAC,EAAEX,SAAS,EAAE;IACtFR,GAAG,EAAEA,GAAG;IACRG,KAAK,EAAEnB,MAAM,CAACmC,MAAM,CAACnC,MAAM,CAACmC,MAAM,CAAC,CAAC,CAAC,EAAEhB,KAAK,CAAC,EAAEO,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACP,KAAK,CAAC;IACnGC,SAAS,EAAEY,GAAG;IACdT,OAAO,EAAEK;EACX,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,eAAef,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ManOutlinedSvg from \"@ant-design/icons-svg/es/asn/ManOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ManOutlined = function ManOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ManOutlinedSvg\n  }));\n};\n\n/**![man](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3NCAxMjBINjIyYy0zLjMgMC02IDIuNy02IDZ2NTZjMCAzLjMgMi43IDYgNiA2aDE2MC40TDU4My4xIDM4Ny4zYy01MC0zOC41LTExMS01OS4zLTE3NS4xLTU5LjMtNzYuOSAwLTE0OS4zIDMwLTIwMy42IDg0LjRTMTIwIDUzOS4xIDEyMCA2MTZzMzAgMTQ5LjMgODQuNCAyMDMuNkMyNTguNyA4NzQgMzMxLjEgOTA0IDQwOCA5MDRzMTQ5LjMtMzAgMjAzLjYtODQuNEM2NjYgNzY1LjMgNjk2IDY5Mi45IDY5NiA2MTZjMC02NC4xLTIwLjgtMTI0LjktNTkuMi0xNzQuOUw4MzYgMjQxLjlWNDAyYzAgMy4zIDIuNyA2IDYgNmg1NmMzLjMgMCA2LTIuNyA2LTZWMTUwYzAtMTYuNS0xMy41LTMwLTMwLTMwek00MDggODI4Yy0xMTYuOSAwLTIxMi05NS4xLTIxMi0yMTJzOTUuMS0yMTIgMjEyLTIxMiAyMTIgOTUuMSAyMTIgMjEyLTk1LjEgMjEyLTIxMiAyMTJ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ManOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ManOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "ManOutlinedSvg", "AntdIcon", "ManOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/ManOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ManOutlinedSvg from \"@ant-design/icons-svg/es/asn/ManOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ManOutlined = function ManOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ManOutlinedSvg\n  }));\n};\n\n/**![man](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3NCAxMjBINjIyYy0zLjMgMC02IDIuNy02IDZ2NTZjMCAzLjMgMi43IDYgNiA2aDE2MC40TDU4My4xIDM4Ny4zYy01MC0zOC41LTExMS01OS4zLTE3NS4xLTU5LjMtNzYuOSAwLTE0OS4zIDMwLTIwMy42IDg0LjRTMTIwIDUzOS4xIDEyMCA2MTZzMzAgMTQ5LjMgODQuNCAyMDMuNkMyNTguNyA4NzQgMzMxLjEgOTA0IDQwOCA5MDRzMTQ5LjMtMzAgMjAzLjYtODQuNEM2NjYgNzY1LjMgNjk2IDY5Mi45IDY5NiA2MTZjMC02NC4xLTIwLjgtMTI0LjktNTkuMi0xNzQuOUw4MzYgMjQxLjlWNDAyYzAgMy4zIDIuNyA2IDYgNmg1NmMzLjMgMCA2LTIuNyA2LTZWMTUwYzAtMTYuNS0xMy41LTMwLTMwLTMwek00MDggODI4Yy0xMTYuOSAwLTIxMi05NS4xLTIxMi0yMTJzOTUuMS0yMTIgMjEyLTIxMiAyMTIgOTUuMSAyMTIgMjEyLTk1LjEgMjEyLTIxMiAyMTJ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ManOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ManOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
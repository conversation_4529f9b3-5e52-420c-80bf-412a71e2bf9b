{"ast": null, "code": "import classNames from 'classnames';\nconst _InputStatuses = ['warning', 'error', ''];\nexport function getStatusClassNames(prefixCls, status, hasFeedback) {\n  return classNames({\n    [\"\".concat(prefixCls, \"-status-success\")]: status === 'success',\n    [\"\".concat(prefixCls, \"-status-warning\")]: status === 'warning',\n    [\"\".concat(prefixCls, \"-status-error\")]: status === 'error',\n    [\"\".concat(prefixCls, \"-status-validating\")]: status === 'validating',\n    [\"\".concat(prefixCls, \"-has-feedback\")]: hasFeedback\n  });\n}\nexport const getMergedStatus = (contextStatus, customStatus) => customStatus || contextStatus;", "map": {"version": 3, "names": ["classNames", "_InputStatuses", "getStatusClassNames", "prefixCls", "status", "hasFeedback", "concat", "getMergedStatus", "contextStatus", "customStatus"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/_util/statusUtils.js"], "sourcesContent": ["import classNames from 'classnames';\nconst _InputStatuses = ['warning', 'error', ''];\nexport function getStatusClassNames(prefixCls, status, hasFeedback) {\n  return classNames({\n    [`${prefixCls}-status-success`]: status === 'success',\n    [`${prefixCls}-status-warning`]: status === 'warning',\n    [`${prefixCls}-status-error`]: status === 'error',\n    [`${prefixCls}-status-validating`]: status === 'validating',\n    [`${prefixCls}-has-feedback`]: hasFeedback\n  });\n}\nexport const getMergedStatus = (contextStatus, customStatus) => customStatus || contextStatus;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,MAAMC,cAAc,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,CAAC;AAC/C,OAAO,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAE;EAClE,OAAOL,UAAU,CAAC;IAChB,IAAAM,MAAA,CAAIH,SAAS,uBAAoBC,MAAM,KAAK,SAAS;IACrD,IAAAE,MAAA,CAAIH,SAAS,uBAAoBC,MAAM,KAAK,SAAS;IACrD,IAAAE,MAAA,CAAIH,SAAS,qBAAkBC,MAAM,KAAK,OAAO;IACjD,IAAAE,MAAA,CAAIH,SAAS,0BAAuBC,MAAM,KAAK,YAAY;IAC3D,IAAAE,MAAA,CAAIH,SAAS,qBAAkBE;EACjC,CAAC,CAAC;AACJ;AACA,OAAO,MAAME,eAAe,GAAGA,CAACC,aAAa,EAAEC,YAAY,KAAKA,YAAY,IAAID,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import React, { forwardRef } from 'react';\nvar Transform = /*#__PURE__*/forwardRef(function (props, ref) {\n  var children = props.children,\n    x = props.x,\n    y = props.y;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    style: {\n      position: 'absolute',\n      left: \"\".concat(x, \"%\"),\n      top: \"\".concat(y, \"%\"),\n      zIndex: 1,\n      transform: 'translate(-50%, -50%)'\n    }\n  }, children);\n});\nexport default Transform;", "map": {"version": 3, "names": ["React", "forwardRef", "Transform", "props", "ref", "children", "x", "y", "createElement", "style", "position", "left", "concat", "top", "zIndex", "transform"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@rc-component/color-picker/es/components/Transform.js"], "sourcesContent": ["import React, { forwardRef } from 'react';\nvar Transform = /*#__PURE__*/forwardRef(function (props, ref) {\n  var children = props.children,\n    x = props.x,\n    y = props.y;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    style: {\n      position: 'absolute',\n      left: \"\".concat(x, \"%\"),\n      top: \"\".concat(y, \"%\"),\n      zIndex: 1,\n      transform: 'translate(-50%, -50%)'\n    }\n  }, children);\n});\nexport default Transform;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,IAAIC,SAAS,GAAG,aAAaD,UAAU,CAAC,UAAUE,KAAK,EAAEC,GAAG,EAAE;EAC5D,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,CAAC,GAAGH,KAAK,CAACG,CAAC;IACXC,CAAC,GAAGJ,KAAK,CAACI,CAAC;EACb,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;IAC7CJ,GAAG,EAAEA,GAAG;IACRK,KAAK,EAAE;MACLC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE,EAAE,CAACC,MAAM,CAACN,CAAC,EAAE,GAAG,CAAC;MACvBO,GAAG,EAAE,EAAE,CAACD,MAAM,CAACL,CAAC,EAAE,GAAG,CAAC;MACtBO,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE;IACb;EACF,CAAC,EAAEV,QAAQ,CAAC;AACd,CAAC,CAAC;AACF,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
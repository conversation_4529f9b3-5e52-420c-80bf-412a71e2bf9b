{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PoweroffOutlinedSvg from \"@ant-design/icons-svg/es/asn/PoweroffOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PoweroffOutlined = function PoweroffOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PoweroffOutlinedSvg\n  }));\n};\n\n/**![poweroff](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNS42IDEyNC45YTggOCAwIDAwLTExLjYgNy4ydjY0LjJjMCA1LjUgMi45IDEwLjYgNy41IDEzLjZhMzUyLjIgMzUyLjIgMCAwMTYyLjIgNDkuOGMzMi43IDMyLjggNTguNCA3MC45IDc2LjMgMTEzLjNhMzU1IDM1NSAwIDAxMjcuOSAxMzguN2MwIDQ4LjEtOS40IDk0LjgtMjcuOSAxMzguN2EzNTUuOTIgMzU1LjkyIDAgMDEtNzYuMyAxMTMuMyAzNTMuMDYgMzUzLjA2IDAgMDEtMTEzLjIgNzYuNGMtNDMuOCAxOC42LTkwLjUgMjgtMTM4LjUgMjhzLTk0LjctOS40LTEzOC41LTI4YTM1My4wNiAzNTMuMDYgMCAwMS0xMTMuMi03Ni40QTM1NS45MiAzNTUuOTIgMCAwMTE4NCA2NTAuNGEzNTUgMzU1IDAgMDEtMjcuOS0xMzguN2MwLTQ4LjEgOS40LTk0LjggMjcuOS0xMzguNyAxNy45LTQyLjQgNDMuNi04MC41IDc2LjMtMTEzLjMgMTktMTkgMzkuOC0zNS42IDYyLjItNDkuOCA0LjctMi45IDcuNS04LjEgNy41LTEzLjZWMTMyYzAtNi02LjMtOS44LTExLjYtNy4yQzE3OC41IDE5NS4yIDgyIDMzOS4zIDgwIDUwNi4zIDc3LjIgNzQ1LjEgMjcyLjUgOTQzLjUgNTExLjIgOTQ0YzIzOSAuNSA0MzIuOC0xOTMuMyA0MzIuOC00MzIuNCAwLTE2OS4yLTk3LTMxNS43LTIzOC40LTM4Ni43ek00ODAgNTYwaDY0YzQuNCAwIDgtMy42IDgtOFY4OGMwLTQuNC0zLjYtOC04LThoLTY0Yy00LjQgMC04IDMuNi04IDh2NDY0YzAgNC40IDMuNiA4IDggOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PoweroffOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PoweroffOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "PoweroffOutlinedSvg", "AntdIcon", "PoweroffOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/PoweroffOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PoweroffOutlinedSvg from \"@ant-design/icons-svg/es/asn/PoweroffOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PoweroffOutlined = function PoweroffOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PoweroffOutlinedSvg\n  }));\n};\n\n/**![poweroff](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNS42IDEyNC45YTggOCAwIDAwLTExLjYgNy4ydjY0LjJjMCA1LjUgMi45IDEwLjYgNy41IDEzLjZhMzUyLjIgMzUyLjIgMCAwMTYyLjIgNDkuOGMzMi43IDMyLjggNTguNCA3MC45IDc2LjMgMTEzLjNhMzU1IDM1NSAwIDAxMjcuOSAxMzguN2MwIDQ4LjEtOS40IDk0LjgtMjcuOSAxMzguN2EzNTUuOTIgMzU1LjkyIDAgMDEtNzYuMyAxMTMuMyAzNTMuMDYgMzUzLjA2IDAgMDEtMTEzLjIgNzYuNGMtNDMuOCAxOC42LTkwLjUgMjgtMTM4LjUgMjhzLTk0LjctOS40LTEzOC41LTI4YTM1My4wNiAzNTMuMDYgMCAwMS0xMTMuMi03Ni40QTM1NS45MiAzNTUuOTIgMCAwMTE4NCA2NTAuNGEzNTUgMzU1IDAgMDEtMjcuOS0xMzguN2MwLTQ4LjEgOS40LTk0LjggMjcuOS0xMzguNyAxNy45LTQyLjQgNDMuNi04MC41IDc2LjMtMTEzLjMgMTktMTkgMzkuOC0zNS42IDYyLjItNDkuOCA0LjctMi45IDcuNS04LjEgNy41LTEzLjZWMTMyYzAtNi02LjMtOS44LTExLjYtNy4yQzE3OC41IDE5NS4yIDgyIDMzOS4zIDgwIDUwNi4zIDc3LjIgNzQ1LjEgMjcyLjUgOTQzLjUgNTExLjIgOTQ0YzIzOSAuNSA0MzIuOC0xOTMuMyA0MzIuOC00MzIuNCAwLTE2OS4yLTk3LTMxNS43LTIzOC40LTM4Ni43ek00ODAgNTYwaDY0YzQuNCAwIDgtMy42IDgtOFY4OGMwLTQuNC0zLjYtOC04LThoLTY0Yy00LjQgMC04IDMuNi04IDh2NDY0YzAgNC40IDMuNiA4IDggOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PoweroffOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PoweroffOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,gBAAgB,CAAC;AAC7D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,kBAAkB;AAC1C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
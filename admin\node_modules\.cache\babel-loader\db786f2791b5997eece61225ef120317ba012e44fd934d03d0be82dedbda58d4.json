{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport LayoutTwoToneSvg from \"@ant-design/icons-svg/es/asn/LayoutTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar LayoutTwoTone = function LayoutTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: LayoutTwoToneSvg\n  }));\n};\n\n/**![layout](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM4NCAxODVoNDU2djEzNkgzODR6bS0yMDAgMGgxMzZ2NjU2SDE4NHptNjk2LTczSDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MWMwLTE3LjcgMTQuMy0zMiAzMi0zMmg3MzZjMTcuNyAwIDMyIDE0LjMgMzIgMzJ2LTFjMC0xNy43LTE0LjMtMzItMzItMzJ6TTM4NCAzODVoNDU2djQ1NkgzODR6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04ODAgMTEzSDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYxNDVjMC0xNy43LTE0LjMtMzItMzItMzJ6TTMyMCA4NDFIMTg0VjE4NWgxMzZ2NjU2em01MjAgMEgzODRWMzg1aDQ1NnY0NTZ6bTAtNTIwSDM4NFYxODVoNDU2djEzNnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(LayoutTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LayoutTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "LayoutTwoToneSvg", "AntdIcon", "LayoutTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/icons/LayoutTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport LayoutTwoToneSvg from \"@ant-design/icons-svg/es/asn/LayoutTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar LayoutTwoTone = function LayoutTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: LayoutTwoToneSvg\n  }));\n};\n\n/**![layout](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM4NCAxODVoNDU2djEzNkgzODR6bS0yMDAgMGgxMzZ2NjU2SDE4NHptNjk2LTczSDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MWMwLTE3LjcgMTQuMy0zMiAzMi0zMmg3MzZjMTcuNyAwIDMyIDE0LjMgMzIgMzJ2LTFjMC0xNy43LTE0LjMtMzItMzItMzJ6TTM4NCAzODVoNDU2djQ1NkgzODR6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04ODAgMTEzSDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYxNDVjMC0xNy43LTE0LjMtMzItMzItMzJ6TTMyMCA4NDFIMTg0VjE4NWgxMzZ2NjU2em01MjAgMEgzODRWMzg1aDQ1NnY0NTZ6bTAtNTIwSDM4NFYxODVoNDU2djEzNnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(LayoutTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LayoutTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
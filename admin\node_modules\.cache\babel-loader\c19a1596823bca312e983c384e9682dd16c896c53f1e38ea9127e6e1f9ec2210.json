{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { SpaceContext } from './context';\nconst Item = ({\n  className,\n  index,\n  children,\n  split,\n  style\n}) => {\n  const {\n    latestIndex\n  } = React.useContext(SpaceContext);\n  if (children === null || children === undefined) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, children), index < latestIndex && split && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${className}-split`\n  }, split));\n};\nexport default Item;", "map": {"version": 3, "names": ["React", "SpaceContext", "<PERSON><PERSON>", "className", "index", "children", "split", "style", "latestIndex", "useContext", "undefined", "createElement", "Fragment"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/space/Item.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { SpaceContext } from './context';\nconst Item = ({\n  className,\n  index,\n  children,\n  split,\n  style\n}) => {\n  const {\n    latestIndex\n  } = React.useContext(SpaceContext);\n  if (children === null || children === undefined) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, children), index < latestIndex && split && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${className}-split`\n  }, split));\n};\nexport default Item;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,IAAI,GAAGA,CAAC;EACZC,SAAS;EACTC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC;EACF,CAAC,GAAGR,KAAK,CAACS,UAAU,CAACR,YAAY,CAAC;EAClC,IAAII,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKK,SAAS,EAAE;IAC/C,OAAO,IAAI;EACb;EACA,OAAO,aAAaV,KAAK,CAACW,aAAa,CAACX,KAAK,CAACY,QAAQ,EAAE,IAAI,EAAE,aAAaZ,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IACpGR,SAAS,EAAEA,SAAS;IACpBI,KAAK,EAAEA;EACT,CAAC,EAAEF,QAAQ,CAAC,EAAED,KAAK,GAAGI,WAAW,IAAIF,KAAK,IAAI,aAAaN,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IACrFR,SAAS,EAAE,GAAGA,SAAS;EACzB,CAAC,EAAEG,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
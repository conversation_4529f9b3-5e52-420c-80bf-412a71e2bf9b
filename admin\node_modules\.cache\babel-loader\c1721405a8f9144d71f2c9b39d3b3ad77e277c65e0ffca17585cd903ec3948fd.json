{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport { useToken } from '../theme/internal';\nexport const GroupSizeContext = /*#__PURE__*/React.createContext(undefined);\nconst ButtonGroup = props => {\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const {\n      prefixCls: customizePrefixCls,\n      size,\n      className\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"size\", \"className\"]);\n  const prefixCls = getPrefixCls('btn-group', customizePrefixCls);\n  const [,, hashId] = useToken();\n  const sizeCls = React.useMemo(() => {\n    switch (size) {\n      case 'large':\n        return 'lg';\n      case 'small':\n        return 'sm';\n      default:\n        return '';\n    }\n  }, [size]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Button.Group');\n    warning.deprecated(false, 'Button.Group', 'Space.Compact');\n    process.env.NODE_ENV !== \"production\" ? warning(!size || ['large', 'small', 'middle'].includes(size), 'usage', 'Invalid prop `size`.') : void 0;\n  }\n  const classes = classNames(prefixCls, {\n    [\"\".concat(prefixCls, \"-\").concat(sizeCls)]: sizeCls,\n    [\"\".concat(prefixCls, \"-rtl\")]: direction === 'rtl'\n  }, className, hashId);\n  return /*#__PURE__*/React.createElement(GroupSizeContext.Provider, {\n    value: size\n  }, /*#__PURE__*/React.createElement(\"div\", Object.assign({}, others, {\n    className: classes\n  })));\n};\nexport default ButtonGroup;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
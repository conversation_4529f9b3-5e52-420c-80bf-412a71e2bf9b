{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { initFadeMotion } from '../../style/motion/fade';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport getOffset from '../util';\nimport floatButtonGroupMotion from './keyframes';\n// ============================== Group ==============================\nconst floatButtonGroupStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    floatButtonSize,\n    margin,\n    borderRadiusLG,\n    borderRadiusSM,\n    badgeOffset,\n    floatButtonBodyPadding,\n    zIndexPopupBase,\n    calc\n  } = token;\n  const groupPrefixCls = \"\".concat(componentCls, \"-group\");\n  return {\n    [groupPrefixCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      zIndex: zIndexPopupBase,\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      border: 'none',\n      position: 'fixed',\n      height: 'auto',\n      boxShadow: 'none',\n      minWidth: floatButtonSize,\n      minHeight: floatButtonSize,\n      insetInlineEnd: token.floatButtonInsetInlineEnd,\n      bottom: token.floatButtonInsetBlockEnd,\n      borderRadius: borderRadiusLG,\n      [\"\".concat(groupPrefixCls, \"-wrap\")]: {\n        zIndex: -1,\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        position: 'absolute'\n      },\n      [\"&\".concat(groupPrefixCls, \"-rtl\")]: {\n        direction: 'rtl'\n      },\n      [componentCls]: {\n        position: 'static'\n      }\n    }),\n    [\"\".concat(groupPrefixCls, \"-top > \").concat(groupPrefixCls, \"-wrap\")]: {\n      flexDirection: 'column',\n      top: 'auto',\n      bottom: calc(floatButtonSize).add(margin).equal(),\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: '100%',\n        height: margin,\n        bottom: calc(margin).mul(-1).equal()\n      }\n    },\n    [\"\".concat(groupPrefixCls, \"-bottom > \").concat(groupPrefixCls, \"-wrap\")]: {\n      flexDirection: 'column',\n      top: calc(floatButtonSize).add(margin).equal(),\n      bottom: 'auto',\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: '100%',\n        height: margin,\n        top: calc(margin).mul(-1).equal()\n      }\n    },\n    [\"\".concat(groupPrefixCls, \"-right > \").concat(groupPrefixCls, \"-wrap\")]: {\n      flexDirection: 'row',\n      left: {\n        _skip_check_: true,\n        value: calc(floatButtonSize).add(margin).equal()\n      },\n      right: {\n        _skip_check_: true,\n        value: 'auto'\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: margin,\n        height: '100%',\n        left: {\n          _skip_check_: true,\n          value: calc(margin).mul(-1).equal()\n        }\n      }\n    },\n    [\"\".concat(groupPrefixCls, \"-left > \").concat(groupPrefixCls, \"-wrap\")]: {\n      flexDirection: 'row',\n      left: {\n        _skip_check_: true,\n        value: 'auto'\n      },\n      right: {\n        _skip_check_: true,\n        value: calc(floatButtonSize).add(margin).equal()\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: margin,\n        height: '100%',\n        right: {\n          _skip_check_: true,\n          value: calc(margin).mul(-1).equal()\n        }\n      }\n    },\n    [\"\".concat(groupPrefixCls, \"-circle\")]: {\n      gap: margin,\n      [\"\".concat(groupPrefixCls, \"-wrap\")]: {\n        gap: margin\n      }\n    },\n    [\"\".concat(groupPrefixCls, \"-square\")]: {\n      [\"\".concat(componentCls, \"-square\")]: {\n        padding: 0,\n        borderRadius: 0,\n        [\"&\".concat(groupPrefixCls, \"-trigger\")]: {\n          borderRadius: borderRadiusLG\n        },\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderStartEndRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderEndStartRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderBottom: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorSplit)\n        },\n        [\"\".concat(antCls, \"-badge\")]: {\n          [\"\".concat(antCls, \"-badge-count\")]: {\n            top: calc(calc(floatButtonBodyPadding).add(badgeOffset)).mul(-1).equal(),\n            insetInlineEnd: calc(calc(floatButtonBodyPadding).add(badgeOffset)).mul(-1).equal()\n          }\n        }\n      },\n      [\"\".concat(groupPrefixCls, \"-wrap\")]: {\n        borderRadius: borderRadiusLG,\n        boxShadow: token.boxShadowSecondary,\n        [\"\".concat(componentCls, \"-square\")]: {\n          boxShadow: 'none',\n          borderRadius: 0,\n          padding: floatButtonBodyPadding,\n          [\"\".concat(componentCls, \"-body\")]: {\n            width: token.floatButtonBodySize,\n            height: token.floatButtonBodySize,\n            borderRadius: borderRadiusSM\n          }\n        }\n      }\n    },\n    [\"\".concat(groupPrefixCls, \"-top > \").concat(groupPrefixCls, \"-wrap, \").concat(groupPrefixCls, \"-bottom > \").concat(groupPrefixCls, \"-wrap\")]: {\n      [\"> \".concat(componentCls, \"-square\")]: {\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderStartEndRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderEndStartRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderBottom: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorSplit)\n        }\n      }\n    },\n    [\"\".concat(groupPrefixCls, \"-left > \").concat(groupPrefixCls, \"-wrap, \").concat(groupPrefixCls, \"-right > \").concat(groupPrefixCls, \"-wrap\")]: {\n      [\"> \".concat(componentCls, \"-square\")]: {\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderEndStartRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderStartEndRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderInlineEnd: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorSplit)\n        }\n      }\n    },\n    [\"\".concat(groupPrefixCls, \"-circle-shadow\")]: {\n      boxShadow: 'none'\n    },\n    [\"\".concat(groupPrefixCls, \"-square-shadow\")]: {\n      boxShadow: token.boxShadowSecondary,\n      [\"\".concat(componentCls, \"-square\")]: {\n        boxShadow: 'none',\n        padding: floatButtonBodyPadding,\n        [\"\".concat(componentCls, \"-body\")]: {\n          width: token.floatButtonBodySize,\n          height: token.floatButtonBodySize,\n          borderRadius: borderRadiusSM\n        }\n      }\n    }\n  };\n};\n// ============================== Shared ==============================\nconst sharedFloatButtonStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    floatButtonBodyPadding,\n    floatButtonIconSize,\n    floatButtonSize,\n    borderRadiusLG,\n    badgeOffset,\n    dotOffsetInSquare,\n    dotOffsetInCircle,\n    zIndexPopupBase,\n    calc\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      border: 'none',\n      position: 'fixed',\n      cursor: 'pointer',\n      zIndex: zIndexPopupBase,\n      // Do not remove the 'display: block' here.\n      // Deleting it will cause marginBottom to become ineffective.\n      // Ref: https://github.com/ant-design/ant-design/issues/44700\n      display: 'block',\n      width: floatButtonSize,\n      height: floatButtonSize,\n      insetInlineEnd: token.floatButtonInsetInlineEnd,\n      bottom: token.floatButtonInsetBlockEnd,\n      boxShadow: token.boxShadowSecondary,\n      // Pure Panel\n      '&-pure': {\n        position: 'relative',\n        inset: 'auto'\n      },\n      '&:empty': {\n        display: 'none'\n      },\n      [\"\".concat(antCls, \"-badge\")]: {\n        width: '100%',\n        height: '100%',\n        [\"\".concat(antCls, \"-badge-count\")]: {\n          transform: 'translate(0, 0)',\n          transformOrigin: 'center',\n          top: calc(badgeOffset).mul(-1).equal(),\n          insetInlineEnd: calc(badgeOffset).mul(-1).equal()\n        }\n      },\n      [\"\".concat(componentCls, \"-body\")]: {\n        width: '100%',\n        height: '100%',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        transition: \"all \".concat(token.motionDurationMid),\n        [\"\".concat(componentCls, \"-content\")]: {\n          overflow: 'hidden',\n          textAlign: 'center',\n          minHeight: floatButtonSize,\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'center',\n          alignItems: 'center',\n          padding: \"\".concat(unit(calc(floatButtonBodyPadding).div(2).equal()), \" \").concat(unit(floatButtonBodyPadding)),\n          [\"\".concat(componentCls, \"-icon\")]: {\n            textAlign: 'center',\n            margin: 'auto',\n            width: floatButtonIconSize,\n            fontSize: floatButtonIconSize,\n            lineHeight: 1\n          }\n        }\n      }\n    }),\n    [\"\".concat(componentCls, \"-rtl\")]: {\n      direction: 'rtl'\n    },\n    [\"\".concat(componentCls, \"-circle\")]: {\n      height: floatButtonSize,\n      borderRadius: '50%',\n      [\"\".concat(antCls, \"-badge\")]: {\n        [\"\".concat(antCls, \"-badge-dot\")]: {\n          top: dotOffsetInCircle,\n          insetInlineEnd: dotOffsetInCircle\n        }\n      },\n      [\"\".concat(componentCls, \"-body\")]: {\n        borderRadius: '50%'\n      }\n    },\n    [\"\".concat(componentCls, \"-square\")]: {\n      height: 'auto',\n      minHeight: floatButtonSize,\n      borderRadius: borderRadiusLG,\n      [\"\".concat(antCls, \"-badge\")]: {\n        [\"\".concat(antCls, \"-badge-dot\")]: {\n          top: dotOffsetInSquare,\n          insetInlineEnd: dotOffsetInSquare\n        }\n      },\n      [\"\".concat(componentCls, \"-body\")]: {\n        height: 'auto',\n        borderRadius: borderRadiusLG\n      }\n    },\n    [\"\".concat(componentCls, \"-default\")]: {\n      backgroundColor: token.floatButtonBackgroundColor,\n      transition: \"background-color \".concat(token.motionDurationMid),\n      [\"\".concat(componentCls, \"-body\")]: {\n        backgroundColor: token.floatButtonBackgroundColor,\n        transition: \"background-color \".concat(token.motionDurationMid),\n        '&:hover': {\n          backgroundColor: token.colorFillContent\n        },\n        [\"\".concat(componentCls, \"-content\")]: {\n          [\"\".concat(componentCls, \"-icon\")]: {\n            color: token.colorText\n          },\n          [\"\".concat(componentCls, \"-description\")]: {\n            display: 'flex',\n            alignItems: 'center',\n            lineHeight: unit(token.fontSizeLG),\n            color: token.colorText,\n            fontSize: token.fontSizeSM\n          }\n        }\n      }\n    },\n    [\"\".concat(componentCls, \"-primary\")]: {\n      backgroundColor: token.colorPrimary,\n      [\"\".concat(componentCls, \"-body\")]: {\n        backgroundColor: token.colorPrimary,\n        transition: \"background-color \".concat(token.motionDurationMid),\n        '&:hover': {\n          backgroundColor: token.colorPrimaryHover\n        },\n        [\"\".concat(componentCls, \"-content\")]: {\n          [\"\".concat(componentCls, \"-icon\")]: {\n            color: token.colorTextLightSolid\n          },\n          [\"\".concat(componentCls, \"-description\")]: {\n            display: 'flex',\n            alignItems: 'center',\n            lineHeight: unit(token.fontSizeLG),\n            color: token.colorTextLightSolid,\n            fontSize: token.fontSizeSM\n          }\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  dotOffsetInCircle: getOffset(token.controlHeightLG / 2),\n  dotOffsetInSquare: getOffset(token.borderRadiusLG)\n});\nexport default genStyleHooks('FloatButton', token => {\n  const {\n    colorTextLightSolid,\n    colorBgElevated,\n    controlHeightLG,\n    marginXXL,\n    marginLG,\n    fontSize,\n    fontSizeIcon,\n    controlItemBgHover,\n    paddingXXS,\n    calc\n  } = token;\n  const floatButtonToken = mergeToken(token, {\n    floatButtonBackgroundColor: colorBgElevated,\n    floatButtonColor: colorTextLightSolid,\n    floatButtonHoverBackgroundColor: controlItemBgHover,\n    floatButtonFontSize: fontSize,\n    floatButtonIconSize: calc(fontSizeIcon).mul(1.5).equal(),\n    floatButtonSize: controlHeightLG,\n    floatButtonInsetBlockEnd: marginXXL,\n    floatButtonInsetInlineEnd: marginLG,\n    floatButtonBodySize: calc(controlHeightLG).sub(calc(paddingXXS).mul(2)).equal(),\n    // 这里的 paddingXXS 是简写，完整逻辑是 (controlHeightLG - (controlHeightLG - paddingXXS * 2)) / 2,\n    floatButtonBodyPadding: paddingXXS,\n    badgeOffset: calc(paddingXXS).mul(1.5).equal()\n  });\n  return [floatButtonGroupStyle(floatButtonToken), sharedFloatButtonStyle(floatButtonToken), initFadeMotion(token), floatButtonGroupMotion(floatButtonToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
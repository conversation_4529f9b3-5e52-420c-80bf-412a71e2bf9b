{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { generate as generateColor } from '@ant-design/colors';\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport warn from \"rc-util/es/warning\";\nimport React, { useContext, useEffect } from 'react';\nimport IconContext from \"./components/Context\";\nfunction camelCase(input) {\n  return input.replace(/-(.)/g, function (match, g) {\n    return g.toUpperCase();\n  });\n}\nexport function warning(valid, message) {\n  warn(valid, \"[@ant-design/icons] \".concat(message));\n}\nexport function isIconDefinition(target) {\n  return _typeof(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && (_typeof(target.icon) === 'object' || typeof target.icon === 'function');\n}\nexport function normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n      default:\n        delete acc[key];\n        acc[camelCase(key)] = val;\n    }\n    return acc;\n  }, {});\n}\nexport function generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n  return /*#__PURE__*/React.createElement(node.tag, _objectSpread(_objectSpread({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nexport function getSecondaryColor(primaryColor) {\n  // choose the second color\n  return generateColor(primaryColor)[0];\n}\nexport function normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n}\n\n// These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\nexport var svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexport var iconStyles = \"\\n.anticon {\\n  display: inline-flex;\\n  align-items: center;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexport var useInsertStyles = function useInsertStyles(eleRef) {\n  var _useContext = useContext(IconContext),\n    csp = _useContext.csp,\n    prefixCls = _useContext.prefixCls,\n    layer = _useContext.layer;\n  var mergedStyleStr = iconStyles;\n  if (prefixCls) {\n    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);\n  }\n  if (layer) {\n    mergedStyleStr = \"@layer \".concat(layer, \" {\\n\").concat(mergedStyleStr, \"\\n}\");\n  }\n  useEffect(function () {\n    var ele = eleRef.current;\n    var shadowRoot = getShadowRoot(ele);\n    updateCSS(mergedStyleStr, '@ant-design-icons', {\n      prepend: !layer,\n      csp: csp,\n      attachTo: shadowRoot\n    });\n  }, []);\n};", "map": {"version": 3, "names": ["_objectSpread", "_typeof", "generate", "generateColor", "updateCSS", "getShadowRoot", "warn", "React", "useContext", "useEffect", "IconContext", "camelCase", "input", "replace", "match", "g", "toUpperCase", "warning", "valid", "message", "concat", "isIconDefinition", "target", "name", "theme", "icon", "normalizeAttrs", "attrs", "arguments", "length", "undefined", "Object", "keys", "reduce", "acc", "key", "val", "className", "class", "node", "rootProps", "createElement", "tag", "children", "map", "child", "index", "getSecondaryColor", "primaryColor", "normalizeTwoToneColors", "twoToneColor", "Array", "isArray", "svgBaseProps", "width", "height", "fill", "focusable", "iconStyles", "useInsertStyles", "eleRef", "_useContext", "csp", "prefixCls", "layer", "mergedStyleStr", "ele", "current", "shadowRoot", "prepend", "attachTo"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons/es/utils.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { generate as generateColor } from '@ant-design/colors';\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport warn from \"rc-util/es/warning\";\nimport React, { useContext, useEffect } from 'react';\nimport IconContext from \"./components/Context\";\nfunction camelCase(input) {\n  return input.replace(/-(.)/g, function (match, g) {\n    return g.toUpperCase();\n  });\n}\nexport function warning(valid, message) {\n  warn(valid, \"[@ant-design/icons] \".concat(message));\n}\nexport function isIconDefinition(target) {\n  return _typeof(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && (_typeof(target.icon) === 'object' || typeof target.icon === 'function');\n}\nexport function normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n      default:\n        delete acc[key];\n        acc[camelCase(key)] = val;\n    }\n    return acc;\n  }, {});\n}\nexport function generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n  return /*#__PURE__*/React.createElement(node.tag, _objectSpread(_objectSpread({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nexport function getSecondaryColor(primaryColor) {\n  // choose the second color\n  return generateColor(primaryColor)[0];\n}\nexport function normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n}\n\n// These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\nexport var svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexport var iconStyles = \"\\n.anticon {\\n  display: inline-flex;\\n  align-items: center;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexport var useInsertStyles = function useInsertStyles(eleRef) {\n  var _useContext = useContext(IconContext),\n    csp = _useContext.csp,\n    prefixCls = _useContext.prefixCls,\n    layer = _useContext.layer;\n  var mergedStyleStr = iconStyles;\n  if (prefixCls) {\n    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);\n  }\n  if (layer) {\n    mergedStyleStr = \"@layer \".concat(layer, \" {\\n\").concat(mergedStyleStr, \"\\n}\");\n  }\n  useEffect(function () {\n    var ele = eleRef.current;\n    var shadowRoot = getShadowRoot(ele);\n    updateCSS(mergedStyleStr, '@ant-design-icons', {\n      prepend: !layer,\n      csp: csp,\n      attachTo: shadowRoot\n    });\n  }, []);\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,SAASC,QAAQ,IAAIC,aAAa,QAAQ,oBAAoB;AAC9D,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACpD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOA,KAAK,CAACC,OAAO,CAAC,OAAO,EAAE,UAAUC,KAAK,EAAEC,CAAC,EAAE;IAChD,OAAOA,CAAC,CAACC,WAAW,CAAC,CAAC;EACxB,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACtCb,IAAI,CAACY,KAAK,EAAE,sBAAsB,CAACE,MAAM,CAACD,OAAO,CAAC,CAAC;AACrD;AACA,OAAO,SAASE,gBAAgBA,CAACC,MAAM,EAAE;EACvC,OAAOrB,OAAO,CAACqB,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,QAAQ,IAAI,OAAOD,MAAM,CAACE,KAAK,KAAK,QAAQ,KAAKvB,OAAO,CAACqB,MAAM,CAACG,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOH,MAAM,CAACG,IAAI,KAAK,UAAU,CAAC;AACxL;AACA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,OAAOG,MAAM,CAACC,IAAI,CAACL,KAAK,CAAC,CAACM,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACnD,IAAIC,GAAG,GAAGT,KAAK,CAACQ,GAAG,CAAC;IACpB,QAAQA,GAAG;MACT,KAAK,OAAO;QACVD,GAAG,CAACG,SAAS,GAAGD,GAAG;QACnB,OAAOF,GAAG,CAACI,KAAK;QAChB;MACF;QACE,OAAOJ,GAAG,CAACC,GAAG,CAAC;QACfD,GAAG,CAACvB,SAAS,CAACwB,GAAG,CAAC,CAAC,GAAGC,GAAG;IAC7B;IACA,OAAOF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,OAAO,SAAShC,QAAQA,CAACqC,IAAI,EAAEJ,GAAG,EAAEK,SAAS,EAAE;EAC7C,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,aAAajC,KAAK,CAACkC,aAAa,CAACF,IAAI,CAACG,GAAG,EAAE1C,aAAa,CAAC;MAC9DmC,GAAG,EAAEA;IACP,CAAC,EAAET,cAAc,CAACa,IAAI,CAACZ,KAAK,CAAC,CAAC,EAAE,CAACY,IAAI,CAACI,QAAQ,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MAChF,OAAO5C,QAAQ,CAAC2C,KAAK,EAAE,EAAE,CAACzB,MAAM,CAACe,GAAG,EAAE,GAAG,CAAC,CAACf,MAAM,CAACmB,IAAI,CAACG,GAAG,EAAE,GAAG,CAAC,CAACtB,MAAM,CAAC0B,KAAK,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAavC,KAAK,CAACkC,aAAa,CAACF,IAAI,CAACG,GAAG,EAAE1C,aAAa,CAACA,aAAa,CAAC;IAC5EmC,GAAG,EAAEA;EACP,CAAC,EAAET,cAAc,CAACa,IAAI,CAACZ,KAAK,CAAC,CAAC,EAAEa,SAAS,CAAC,EAAE,CAACD,IAAI,CAACI,QAAQ,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC5F,OAAO5C,QAAQ,CAAC2C,KAAK,EAAE,EAAE,CAACzB,MAAM,CAACe,GAAG,EAAE,GAAG,CAAC,CAACf,MAAM,CAACmB,IAAI,CAACG,GAAG,EAAE,GAAG,CAAC,CAACtB,MAAM,CAAC0B,KAAK,CAAC,CAAC;EACjF,CAAC,CAAC,CAAC;AACL;AACA,OAAO,SAASC,iBAAiBA,CAACC,YAAY,EAAE;EAC9C;EACA,OAAO7C,aAAa,CAAC6C,YAAY,CAAC,CAAC,CAAC,CAAC;AACvC;AACA,OAAO,SAASC,sBAAsBA,CAACC,YAAY,EAAE;EACnD,IAAI,CAACA,YAAY,EAAE;IACjB,OAAO,EAAE;EACX;EACA,OAAOC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;AACpE;;AAEA;AACA;AACA,OAAO,IAAIG,YAAY,GAAG;EACxBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,cAAc;EACpB,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG,+8BAA+8B;AACv+B,OAAO,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,MAAM,EAAE;EAC5D,IAAIC,WAAW,GAAGrD,UAAU,CAACE,WAAW,CAAC;IACvCoD,GAAG,GAAGD,WAAW,CAACC,GAAG;IACrBC,SAAS,GAAGF,WAAW,CAACE,SAAS;IACjCC,KAAK,GAAGH,WAAW,CAACG,KAAK;EAC3B,IAAIC,cAAc,GAAGP,UAAU;EAC/B,IAAIK,SAAS,EAAE;IACbE,cAAc,GAAGA,cAAc,CAACpD,OAAO,CAAC,UAAU,EAAEkD,SAAS,CAAC;EAChE;EACA,IAAIC,KAAK,EAAE;IACTC,cAAc,GAAG,SAAS,CAAC7C,MAAM,CAAC4C,KAAK,EAAE,MAAM,CAAC,CAAC5C,MAAM,CAAC6C,cAAc,EAAE,KAAK,CAAC;EAChF;EACAxD,SAAS,CAAC,YAAY;IACpB,IAAIyD,GAAG,GAAGN,MAAM,CAACO,OAAO;IACxB,IAAIC,UAAU,GAAG/D,aAAa,CAAC6D,GAAG,CAAC;IACnC9D,SAAS,CAAC6D,cAAc,EAAE,mBAAmB,EAAE;MAC7CI,OAAO,EAAE,CAACL,KAAK;MACfF,GAAG,EAAEA,GAAG;MACRQ,QAAQ,EAAEF;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
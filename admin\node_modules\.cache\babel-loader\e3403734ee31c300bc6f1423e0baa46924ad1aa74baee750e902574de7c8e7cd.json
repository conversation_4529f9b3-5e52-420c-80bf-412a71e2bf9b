{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { FormItemInputContext } from '../form/context';\nimport { Button, Group } from '../radio';\nimport Select from '../select';\nconst YEAR_SELECT_OFFSET = 10;\nconst YEAR_SELECT_TOTAL = 20;\nfunction YearSelect(props) {\n  const {\n    fullscreen,\n    validRange,\n    generateConfig,\n    locale,\n    prefixCls,\n    value,\n    onChange,\n    divRef\n  } = props;\n  const year = generateConfig.getYear(value || generateConfig.getNow());\n  let start = year - YEAR_SELECT_OFFSET;\n  let end = start + YEAR_SELECT_TOTAL;\n  if (validRange) {\n    start = generateConfig.getYear(validRange[0]);\n    end = generateConfig.getYear(validRange[1]) + 1;\n  }\n  const suffix = locale && locale.year === '年' ? '年' : '';\n  const options = [];\n  for (let index = start; index < end; index++) {\n    options.push({\n      label: \"\".concat(index).concat(suffix),\n      value: index\n    });\n  }\n  return /*#__PURE__*/React.createElement(Select, {\n    size: fullscreen ? undefined : 'small',\n    options: options,\n    value: year,\n    className: \"\".concat(prefixCls, \"-year-select\"),\n    onChange: numYear => {\n      let newDate = generateConfig.setYear(value, numYear);\n      if (validRange) {\n        const [startDate, endDate] = validRange;\n        const newYear = generateConfig.getYear(newDate);\n        const newMonth = generateConfig.getMonth(newDate);\n        if (newYear === generateConfig.getYear(endDate) && newMonth > generateConfig.getMonth(endDate)) {\n          newDate = generateConfig.setMonth(newDate, generateConfig.getMonth(endDate));\n        }\n        if (newYear === generateConfig.getYear(startDate) && newMonth < generateConfig.getMonth(startDate)) {\n          newDate = generateConfig.setMonth(newDate, generateConfig.getMonth(startDate));\n        }\n      }\n      onChange(newDate);\n    },\n    getPopupContainer: () => divRef.current\n  });\n}\nfunction MonthSelect(props) {\n  const {\n    prefixCls,\n    fullscreen,\n    validRange,\n    value,\n    generateConfig,\n    locale,\n    onChange,\n    divRef\n  } = props;\n  const month = generateConfig.getMonth(value || generateConfig.getNow());\n  let start = 0;\n  let end = 11;\n  if (validRange) {\n    const [rangeStart, rangeEnd] = validRange;\n    const currentYear = generateConfig.getYear(value);\n    if (generateConfig.getYear(rangeEnd) === currentYear) {\n      end = generateConfig.getMonth(rangeEnd);\n    }\n    if (generateConfig.getYear(rangeStart) === currentYear) {\n      start = generateConfig.getMonth(rangeStart);\n    }\n  }\n  const months = locale.shortMonths || generateConfig.locale.getShortMonths(locale.locale);\n  const options = [];\n  for (let index = start; index <= end; index += 1) {\n    options.push({\n      label: months[index],\n      value: index\n    });\n  }\n  return /*#__PURE__*/React.createElement(Select, {\n    size: fullscreen ? undefined : 'small',\n    className: \"\".concat(prefixCls, \"-month-select\"),\n    value: month,\n    options: options,\n    onChange: newMonth => {\n      onChange(generateConfig.setMonth(value, newMonth));\n    },\n    getPopupContainer: () => divRef.current\n  });\n}\nfunction ModeSwitch(props) {\n  const {\n    prefixCls,\n    locale,\n    mode,\n    fullscreen,\n    onModeChange\n  } = props;\n  return /*#__PURE__*/React.createElement(Group, {\n    onChange: _ref => {\n      let {\n        target: {\n          value\n        }\n      } = _ref;\n      onModeChange(value);\n    },\n    value: mode,\n    size: fullscreen ? undefined : 'small',\n    className: \"\".concat(prefixCls, \"-mode-switch\")\n  }, /*#__PURE__*/React.createElement(Button, {\n    value: \"month\"\n  }, locale.month), /*#__PURE__*/React.createElement(Button, {\n    value: \"year\"\n  }, locale.year));\n}\nfunction CalendarHeader(props) {\n  const {\n    prefixCls,\n    fullscreen,\n    mode,\n    onChange,\n    onModeChange\n  } = props;\n  const divRef = React.useRef(null);\n  const formItemInputContext = useContext(FormItemInputContext);\n  const mergedFormItemInputContext = useMemo(() => Object.assign(Object.assign({}, formItemInputContext), {\n    isFormItemInput: false\n  }), [formItemInputContext]);\n  const sharedProps = Object.assign(Object.assign({}, props), {\n    fullscreen,\n    divRef\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-header\"),\n    ref: divRef\n  }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: mergedFormItemInputContext\n  }, /*#__PURE__*/React.createElement(YearSelect, Object.assign({}, sharedProps, {\n    onChange: v => {\n      onChange(v, 'year');\n    }\n  })), mode === 'month' && (/*#__PURE__*/React.createElement(MonthSelect, Object.assign({}, sharedProps, {\n    onChange: v => {\n      onChange(v, 'month');\n    }\n  })))), /*#__PURE__*/React.createElement(ModeSwitch, Object.assign({}, sharedProps, {\n    onModeChange: onModeChange\n  })));\n}\nexport default CalendarHeader;", "map": {"version": 3, "names": ["React", "useContext", "useMemo", "FormItemInputContext", "<PERSON><PERSON>", "Group", "Select", "YEAR_SELECT_OFFSET", "YEAR_SELECT_TOTAL", "YearSelect", "props", "fullscreen", "validRange", "generateConfig", "locale", "prefixCls", "value", "onChange", "divRef", "year", "getYear", "getNow", "start", "end", "suffix", "options", "index", "push", "label", "concat", "createElement", "size", "undefined", "className", "numYear", "newDate", "setYear", "startDate", "endDate", "newYear", "newMonth", "getMonth", "setMonth", "getPopupContainer", "current", "MonthSelect", "month", "rangeStart", "rangeEnd", "currentYear", "months", "shortMonths", "getShortMonths", "ModeSwitch", "mode", "onModeChange", "_ref", "target", "CalendarHeader", "useRef", "formItemInputContext", "mergedFormItemInputContext", "Object", "assign", "isFormItemInput", "sharedProps", "ref", "Provider", "v"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/calendar/Header.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { FormItemInputContext } from '../form/context';\nimport { Button, Group } from '../radio';\nimport Select from '../select';\nconst YEAR_SELECT_OFFSET = 10;\nconst YEAR_SELECT_TOTAL = 20;\nfunction YearSelect(props) {\n  const {\n    fullscreen,\n    validRange,\n    generateConfig,\n    locale,\n    prefixCls,\n    value,\n    onChange,\n    divRef\n  } = props;\n  const year = generateConfig.getYear(value || generateConfig.getNow());\n  let start = year - YEAR_SELECT_OFFSET;\n  let end = start + YEAR_SELECT_TOTAL;\n  if (validRange) {\n    start = generateConfig.getYear(validRange[0]);\n    end = generateConfig.getYear(validRange[1]) + 1;\n  }\n  const suffix = locale && locale.year === '年' ? '年' : '';\n  const options = [];\n  for (let index = start; index < end; index++) {\n    options.push({\n      label: `${index}${suffix}`,\n      value: index\n    });\n  }\n  return /*#__PURE__*/React.createElement(Select, {\n    size: fullscreen ? undefined : 'small',\n    options: options,\n    value: year,\n    className: `${prefixCls}-year-select`,\n    onChange: numYear => {\n      let newDate = generateConfig.setYear(value, numYear);\n      if (validRange) {\n        const [startDate, endDate] = validRange;\n        const newYear = generateConfig.getYear(newDate);\n        const newMonth = generateConfig.getMonth(newDate);\n        if (newYear === generateConfig.getYear(endDate) && newMonth > generateConfig.getMonth(endDate)) {\n          newDate = generateConfig.setMonth(newDate, generateConfig.getMonth(endDate));\n        }\n        if (newYear === generateConfig.getYear(startDate) && newMonth < generateConfig.getMonth(startDate)) {\n          newDate = generateConfig.setMonth(newDate, generateConfig.getMonth(startDate));\n        }\n      }\n      onChange(newDate);\n    },\n    getPopupContainer: () => divRef.current\n  });\n}\nfunction MonthSelect(props) {\n  const {\n    prefixCls,\n    fullscreen,\n    validRange,\n    value,\n    generateConfig,\n    locale,\n    onChange,\n    divRef\n  } = props;\n  const month = generateConfig.getMonth(value || generateConfig.getNow());\n  let start = 0;\n  let end = 11;\n  if (validRange) {\n    const [rangeStart, rangeEnd] = validRange;\n    const currentYear = generateConfig.getYear(value);\n    if (generateConfig.getYear(rangeEnd) === currentYear) {\n      end = generateConfig.getMonth(rangeEnd);\n    }\n    if (generateConfig.getYear(rangeStart) === currentYear) {\n      start = generateConfig.getMonth(rangeStart);\n    }\n  }\n  const months = locale.shortMonths || generateConfig.locale.getShortMonths(locale.locale);\n  const options = [];\n  for (let index = start; index <= end; index += 1) {\n    options.push({\n      label: months[index],\n      value: index\n    });\n  }\n  return /*#__PURE__*/React.createElement(Select, {\n    size: fullscreen ? undefined : 'small',\n    className: `${prefixCls}-month-select`,\n    value: month,\n    options: options,\n    onChange: newMonth => {\n      onChange(generateConfig.setMonth(value, newMonth));\n    },\n    getPopupContainer: () => divRef.current\n  });\n}\nfunction ModeSwitch(props) {\n  const {\n    prefixCls,\n    locale,\n    mode,\n    fullscreen,\n    onModeChange\n  } = props;\n  return /*#__PURE__*/React.createElement(Group, {\n    onChange: ({\n      target: {\n        value\n      }\n    }) => {\n      onModeChange(value);\n    },\n    value: mode,\n    size: fullscreen ? undefined : 'small',\n    className: `${prefixCls}-mode-switch`\n  }, /*#__PURE__*/React.createElement(Button, {\n    value: \"month\"\n  }, locale.month), /*#__PURE__*/React.createElement(Button, {\n    value: \"year\"\n  }, locale.year));\n}\nfunction CalendarHeader(props) {\n  const {\n    prefixCls,\n    fullscreen,\n    mode,\n    onChange,\n    onModeChange\n  } = props;\n  const divRef = React.useRef(null);\n  const formItemInputContext = useContext(FormItemInputContext);\n  const mergedFormItemInputContext = useMemo(() => Object.assign(Object.assign({}, formItemInputContext), {\n    isFormItemInput: false\n  }), [formItemInputContext]);\n  const sharedProps = Object.assign(Object.assign({}, props), {\n    fullscreen,\n    divRef\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-header`,\n    ref: divRef\n  }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: mergedFormItemInputContext\n  }, /*#__PURE__*/React.createElement(YearSelect, Object.assign({}, sharedProps, {\n    onChange: v => {\n      onChange(v, 'year');\n    }\n  })), mode === 'month' && (/*#__PURE__*/React.createElement(MonthSelect, Object.assign({}, sharedProps, {\n    onChange: v => {\n      onChange(v, 'month');\n    }\n  })))), /*#__PURE__*/React.createElement(ModeSwitch, Object.assign({}, sharedProps, {\n    onModeChange: onModeChange\n  })));\n}\nexport default CalendarHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,MAAM,EAAEC,KAAK,QAAQ,UAAU;AACxC,OAAOC,MAAM,MAAM,WAAW;AAC9B,MAAMC,kBAAkB,GAAG,EAAE;AAC7B,MAAMC,iBAAiB,GAAG,EAAE;AAC5B,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,MAAM;IACJC,UAAU;IACVC,UAAU;IACVC,cAAc;IACdC,MAAM;IACNC,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,IAAI,GAAGN,cAAc,CAACO,OAAO,CAACJ,KAAK,IAAIH,cAAc,CAACQ,MAAM,CAAC,CAAC,CAAC;EACrE,IAAIC,KAAK,GAAGH,IAAI,GAAGZ,kBAAkB;EACrC,IAAIgB,GAAG,GAAGD,KAAK,GAAGd,iBAAiB;EACnC,IAAII,UAAU,EAAE;IACdU,KAAK,GAAGT,cAAc,CAACO,OAAO,CAACR,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7CW,GAAG,GAAGV,cAAc,CAACO,OAAO,CAACR,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACjD;EACA,MAAMY,MAAM,GAAGV,MAAM,IAAIA,MAAM,CAACK,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;EACvD,MAAMM,OAAO,GAAG,EAAE;EAClB,KAAK,IAAIC,KAAK,GAAGJ,KAAK,EAAEI,KAAK,GAAGH,GAAG,EAAEG,KAAK,EAAE,EAAE;IAC5CD,OAAO,CAACE,IAAI,CAAC;MACXC,KAAK,KAAAC,MAAA,CAAKH,KAAK,EAAAG,MAAA,CAAGL,MAAM,CAAE;MAC1BR,KAAK,EAAEU;IACT,CAAC,CAAC;EACJ;EACA,OAAO,aAAa1B,KAAK,CAAC8B,aAAa,CAACxB,MAAM,EAAE;IAC9CyB,IAAI,EAAEpB,UAAU,GAAGqB,SAAS,GAAG,OAAO;IACtCP,OAAO,EAAEA,OAAO;IAChBT,KAAK,EAAEG,IAAI;IACXc,SAAS,KAAAJ,MAAA,CAAKd,SAAS,iBAAc;IACrCE,QAAQ,EAAEiB,OAAO,IAAI;MACnB,IAAIC,OAAO,GAAGtB,cAAc,CAACuB,OAAO,CAACpB,KAAK,EAAEkB,OAAO,CAAC;MACpD,IAAItB,UAAU,EAAE;QACd,MAAM,CAACyB,SAAS,EAAEC,OAAO,CAAC,GAAG1B,UAAU;QACvC,MAAM2B,OAAO,GAAG1B,cAAc,CAACO,OAAO,CAACe,OAAO,CAAC;QAC/C,MAAMK,QAAQ,GAAG3B,cAAc,CAAC4B,QAAQ,CAACN,OAAO,CAAC;QACjD,IAAII,OAAO,KAAK1B,cAAc,CAACO,OAAO,CAACkB,OAAO,CAAC,IAAIE,QAAQ,GAAG3B,cAAc,CAAC4B,QAAQ,CAACH,OAAO,CAAC,EAAE;UAC9FH,OAAO,GAAGtB,cAAc,CAAC6B,QAAQ,CAACP,OAAO,EAAEtB,cAAc,CAAC4B,QAAQ,CAACH,OAAO,CAAC,CAAC;QAC9E;QACA,IAAIC,OAAO,KAAK1B,cAAc,CAACO,OAAO,CAACiB,SAAS,CAAC,IAAIG,QAAQ,GAAG3B,cAAc,CAAC4B,QAAQ,CAACJ,SAAS,CAAC,EAAE;UAClGF,OAAO,GAAGtB,cAAc,CAAC6B,QAAQ,CAACP,OAAO,EAAEtB,cAAc,CAAC4B,QAAQ,CAACJ,SAAS,CAAC,CAAC;QAChF;MACF;MACApB,QAAQ,CAACkB,OAAO,CAAC;IACnB,CAAC;IACDQ,iBAAiB,EAAEA,CAAA,KAAMzB,MAAM,CAAC0B;EAClC,CAAC,CAAC;AACJ;AACA,SAASC,WAAWA,CAACnC,KAAK,EAAE;EAC1B,MAAM;IACJK,SAAS;IACTJ,UAAU;IACVC,UAAU;IACVI,KAAK;IACLH,cAAc;IACdC,MAAM;IACNG,QAAQ;IACRC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMoC,KAAK,GAAGjC,cAAc,CAAC4B,QAAQ,CAACzB,KAAK,IAAIH,cAAc,CAACQ,MAAM,CAAC,CAAC,CAAC;EACvE,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIX,UAAU,EAAE;IACd,MAAM,CAACmC,UAAU,EAAEC,QAAQ,CAAC,GAAGpC,UAAU;IACzC,MAAMqC,WAAW,GAAGpC,cAAc,CAACO,OAAO,CAACJ,KAAK,CAAC;IACjD,IAAIH,cAAc,CAACO,OAAO,CAAC4B,QAAQ,CAAC,KAAKC,WAAW,EAAE;MACpD1B,GAAG,GAAGV,cAAc,CAAC4B,QAAQ,CAACO,QAAQ,CAAC;IACzC;IACA,IAAInC,cAAc,CAACO,OAAO,CAAC2B,UAAU,CAAC,KAAKE,WAAW,EAAE;MACtD3B,KAAK,GAAGT,cAAc,CAAC4B,QAAQ,CAACM,UAAU,CAAC;IAC7C;EACF;EACA,MAAMG,MAAM,GAAGpC,MAAM,CAACqC,WAAW,IAAItC,cAAc,CAACC,MAAM,CAACsC,cAAc,CAACtC,MAAM,CAACA,MAAM,CAAC;EACxF,MAAMW,OAAO,GAAG,EAAE;EAClB,KAAK,IAAIC,KAAK,GAAGJ,KAAK,EAAEI,KAAK,IAAIH,GAAG,EAAEG,KAAK,IAAI,CAAC,EAAE;IAChDD,OAAO,CAACE,IAAI,CAAC;MACXC,KAAK,EAAEsB,MAAM,CAACxB,KAAK,CAAC;MACpBV,KAAK,EAAEU;IACT,CAAC,CAAC;EACJ;EACA,OAAO,aAAa1B,KAAK,CAAC8B,aAAa,CAACxB,MAAM,EAAE;IAC9CyB,IAAI,EAAEpB,UAAU,GAAGqB,SAAS,GAAG,OAAO;IACtCC,SAAS,KAAAJ,MAAA,CAAKd,SAAS,kBAAe;IACtCC,KAAK,EAAE8B,KAAK;IACZrB,OAAO,EAAEA,OAAO;IAChBR,QAAQ,EAAEuB,QAAQ,IAAI;MACpBvB,QAAQ,CAACJ,cAAc,CAAC6B,QAAQ,CAAC1B,KAAK,EAAEwB,QAAQ,CAAC,CAAC;IACpD,CAAC;IACDG,iBAAiB,EAAEA,CAAA,KAAMzB,MAAM,CAAC0B;EAClC,CAAC,CAAC;AACJ;AACA,SAASS,UAAUA,CAAC3C,KAAK,EAAE;EACzB,MAAM;IACJK,SAAS;IACTD,MAAM;IACNwC,IAAI;IACJ3C,UAAU;IACV4C;EACF,CAAC,GAAG7C,KAAK;EACT,OAAO,aAAaV,KAAK,CAAC8B,aAAa,CAACzB,KAAK,EAAE;IAC7CY,QAAQ,EAAEuC,IAAA,IAIJ;MAAA,IAJK;QACTC,MAAM,EAAE;UACNzC;QACF;MACF,CAAC,GAAAwC,IAAA;MACCD,YAAY,CAACvC,KAAK,CAAC;IACrB,CAAC;IACDA,KAAK,EAAEsC,IAAI;IACXvB,IAAI,EAAEpB,UAAU,GAAGqB,SAAS,GAAG,OAAO;IACtCC,SAAS,KAAAJ,MAAA,CAAKd,SAAS;EACzB,CAAC,EAAE,aAAaf,KAAK,CAAC8B,aAAa,CAAC1B,MAAM,EAAE;IAC1CY,KAAK,EAAE;EACT,CAAC,EAAEF,MAAM,CAACgC,KAAK,CAAC,EAAE,aAAa9C,KAAK,CAAC8B,aAAa,CAAC1B,MAAM,EAAE;IACzDY,KAAK,EAAE;EACT,CAAC,EAAEF,MAAM,CAACK,IAAI,CAAC,CAAC;AAClB;AACA,SAASuC,cAAcA,CAAChD,KAAK,EAAE;EAC7B,MAAM;IACJK,SAAS;IACTJ,UAAU;IACV2C,IAAI;IACJrC,QAAQ;IACRsC;EACF,CAAC,GAAG7C,KAAK;EACT,MAAMQ,MAAM,GAAGlB,KAAK,CAAC2D,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,oBAAoB,GAAG3D,UAAU,CAACE,oBAAoB,CAAC;EAC7D,MAAM0D,0BAA0B,GAAG3D,OAAO,CAAC,MAAM4D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,oBAAoB,CAAC,EAAE;IACtGI,eAAe,EAAE;EACnB,CAAC,CAAC,EAAE,CAACJ,oBAAoB,CAAC,CAAC;EAC3B,MAAMK,WAAW,GAAGH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErD,KAAK,CAAC,EAAE;IAC1DC,UAAU;IACVO;EACF,CAAC,CAAC;EACF,OAAO,aAAalB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC7CG,SAAS,KAAAJ,MAAA,CAAKd,SAAS,YAAS;IAChCmD,GAAG,EAAEhD;EACP,CAAC,EAAE,aAAalB,KAAK,CAAC8B,aAAa,CAAC3B,oBAAoB,CAACgE,QAAQ,EAAE;IACjEnD,KAAK,EAAE6C;EACT,CAAC,EAAE,aAAa7D,KAAK,CAAC8B,aAAa,CAACrB,UAAU,EAAEqD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEE,WAAW,EAAE;IAC7EhD,QAAQ,EAAEmD,CAAC,IAAI;MACbnD,QAAQ,CAACmD,CAAC,EAAE,MAAM,CAAC;IACrB;EACF,CAAC,CAAC,CAAC,EAAEd,IAAI,KAAK,OAAO,KAAK,aAAatD,KAAK,CAAC8B,aAAa,CAACe,WAAW,EAAEiB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEE,WAAW,EAAE;IACrGhD,QAAQ,EAAEmD,CAAC,IAAI;MACbnD,QAAQ,CAACmD,CAAC,EAAE,OAAO,CAAC;IACtB;EACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAapE,KAAK,CAAC8B,aAAa,CAACuB,UAAU,EAAES,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEE,WAAW,EAAE;IACjFV,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC,CAAC;AACN;AACA,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport classNames from 'classnames';\nimport { NotificationProvider, useNotification as useRcNotification } from 'rc-notification';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { PureContent } from './PurePanel';\nimport useStyle from './style';\nimport { getMotion, wrapPromiseFn } from './util';\nconst DEFAULT_OFFSET = 8;\nconst DEFAULT_DURATION = 3;\nconst Wrapper = _ref => {\n  let {\n    children,\n    prefixCls\n  } = _ref;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(NotificationProvider, {\n    classNames: {\n      list: classNames(hashId, cssVarCls, rootCls)\n    }\n  }, children));\n};\nconst renderNotifications = (node, _ref2) => {\n  let {\n    prefixCls,\n    key\n  } = _ref2;\n  return /*#__PURE__*/React.createElement(Wrapper, {\n    prefixCls: prefixCls,\n    key: key\n  }, node);\n};\nconst Holder = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    top,\n    prefixCls: staticPrefixCls,\n    getContainer: staticGetContainer,\n    maxCount,\n    duration = DEFAULT_DURATION,\n    rtl,\n    transitionName,\n    onAllRemoved\n  } = props;\n  const {\n    getPrefixCls,\n    getPopupContainer,\n    message,\n    direction\n  } = React.useContext(ConfigContext);\n  const prefixCls = staticPrefixCls || getPrefixCls('message');\n  // =============================== Style ===============================\n  const getStyle = () => ({\n    left: '50%',\n    transform: 'translateX(-50%)',\n    top: top !== null && top !== void 0 ? top : DEFAULT_OFFSET\n  });\n  const getClassName = () => classNames({\n    [\"\".concat(prefixCls, \"-rtl\")]: rtl !== null && rtl !== void 0 ? rtl : direction === 'rtl'\n  });\n  // ============================== Motion ===============================\n  const getNotificationMotion = () => getMotion(prefixCls, transitionName);\n  // ============================ Close Icon =============================\n  const mergedCloseIcon = /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-close-x\")\n  }, /*#__PURE__*/React.createElement(CloseOutlined, {\n    className: \"\".concat(prefixCls, \"-close-icon\")\n  }));\n  // ============================== Origin ===============================\n  const [api, holder] = useRcNotification({\n    prefixCls,\n    style: getStyle,\n    className: getClassName,\n    motion: getNotificationMotion,\n    closable: false,\n    closeIcon: mergedCloseIcon,\n    duration,\n    getContainer: () => (staticGetContainer === null || staticGetContainer === void 0 ? void 0 : staticGetContainer()) || (getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer()) || document.body,\n    maxCount,\n    onAllRemoved,\n    renderNotifications\n  });\n  // ================================ Ref ================================\n  React.useImperativeHandle(ref, () => Object.assign(Object.assign({}, api), {\n    prefixCls,\n    message\n  }));\n  return holder;\n});\n// ==============================================================================\n// ==                                   Hook                                   ==\n// ==============================================================================\nlet keyIndex = 0;\nexport function useInternalMessage(messageConfig) {\n  const holderRef = React.useRef(null);\n  const warning = devUseWarning('Message');\n  // ================================ API ================================\n  const wrapAPI = React.useMemo(() => {\n    // Wrap with notification content\n    // >>> close\n    const close = key => {\n      var _a;\n      (_a = holderRef.current) === null || _a === void 0 ? void 0 : _a.close(key);\n    };\n    // >>> Open\n    const open = config => {\n      if (!holderRef.current) {\n        process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', 'You are calling notice in render which will break in React 18 concurrent mode. Please trigger in effect instead.') : void 0;\n        const fakeResult = () => {};\n        fakeResult.then = () => {};\n        return fakeResult;\n      }\n      const {\n        open: originOpen,\n        prefixCls,\n        message\n      } = holderRef.current;\n      const noticePrefixCls = \"\".concat(prefixCls, \"-notice\");\n      const {\n          content,\n          icon,\n          type,\n          key,\n          className,\n          style,\n          onClose\n        } = config,\n        restConfig = __rest(config, [\"content\", \"icon\", \"type\", \"key\", \"className\", \"style\", \"onClose\"]);\n      let mergedKey = key;\n      if (mergedKey === undefined || mergedKey === null) {\n        keyIndex += 1;\n        mergedKey = \"antd-message-\".concat(keyIndex);\n      }\n      return wrapPromiseFn(resolve => {\n        originOpen(Object.assign(Object.assign({}, restConfig), {\n          key: mergedKey,\n          content: (/*#__PURE__*/React.createElement(PureContent, {\n            prefixCls: prefixCls,\n            type: type,\n            icon: icon\n          }, content)),\n          placement: 'top',\n          className: classNames(type && \"\".concat(noticePrefixCls, \"-\").concat(type), className, message === null || message === void 0 ? void 0 : message.className),\n          style: Object.assign(Object.assign({}, message === null || message === void 0 ? void 0 : message.style), style),\n          onClose: () => {\n            onClose === null || onClose === void 0 ? void 0 : onClose();\n            resolve();\n          }\n        }));\n        // Return close function\n        return () => {\n          close(mergedKey);\n        };\n      });\n    };\n    // >>> destroy\n    const destroy = key => {\n      var _a;\n      if (key !== undefined) {\n        close(key);\n      } else {\n        (_a = holderRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n      }\n    };\n    const clone = {\n      open,\n      destroy\n    };\n    const keys = ['info', 'success', 'warning', 'error', 'loading'];\n    keys.forEach(type => {\n      const typeOpen = (jointContent, duration, onClose) => {\n        let config;\n        if (jointContent && typeof jointContent === 'object' && 'content' in jointContent) {\n          config = jointContent;\n        } else {\n          config = {\n            content: jointContent\n          };\n        }\n        // Params\n        let mergedDuration;\n        let mergedOnClose;\n        if (typeof duration === 'function') {\n          mergedOnClose = duration;\n        } else {\n          mergedDuration = duration;\n          mergedOnClose = onClose;\n        }\n        const mergedConfig = Object.assign(Object.assign({\n          onClose: mergedOnClose,\n          duration: mergedDuration\n        }, config), {\n          type\n        });\n        return open(mergedConfig);\n      };\n      clone[type] = typeOpen;\n    });\n    return clone;\n  }, []);\n  // ============================== Return ===============================\n  return [wrapAPI, /*#__PURE__*/React.createElement(Holder, Object.assign({\n    key: \"message-holder\"\n  }, messageConfig, {\n    ref: holderRef\n  }))];\n}\nexport default function useMessage(messageConfig) {\n  return useInternalMessage(messageConfig);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
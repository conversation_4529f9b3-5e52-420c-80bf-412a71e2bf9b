{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, Form, Input, Select, DatePicker, Row, Col, Statistic, message, Modal, Descriptions } from 'antd';\nimport { SearchOutlined, EyeOutlined, EditOutlined, ExportOutlined, ReloadOutlined, ClearOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\n// 模拟订单数据\nconst mockOrders = [{\n  id: 1,\n  orderNo: 'ORD202401150001',\n  customerName: '张三',\n  customerPhone: '13800138001',\n  productName: '中国移动5G畅享套餐',\n  operator: '中国移动',\n  status: 'activated',\n  logisticsCompany: '顺丰速运',\n  trackingNumber: 'SF1234567890123',\n  shippedAt: '2024-01-15 10:45:00',\n  estimatedDelivery: '2024-01-16 18:00:00',\n  createdAt: '2024-01-15 10:30:00',\n  updatedAt: '2024-01-15 11:00:00'\n}, {\n  id: 2,\n  orderNo: 'ORD202401150002',\n  customerName: '李四',\n  customerPhone: '13800138002',\n  productName: '中国电信5G精选套餐',\n  operator: '中国电信',\n  status: 'processing',\n  createdAt: '2024-01-15 11:15:00',\n  updatedAt: '2024-01-15 11:15:00'\n}, {\n  id: 3,\n  orderNo: 'ORD202401150003',\n  customerName: '王五',\n  customerPhone: '13800138003',\n  productName: '中国联通青春套餐',\n  operator: '中国联通',\n  status: 'pending',\n  createdAt: '2024-01-15 12:00:00',\n  updatedAt: '2024-01-15 12:00:00'\n}, {\n  id: 4,\n  orderNo: 'ORD202401150004',\n  customerName: '赵六',\n  customerPhone: '13800138004',\n  productName: '中国广电智慧套餐',\n  operator: '中国广电',\n  status: 'shipped',\n  logisticsCompany: '中通快递',\n  trackingNumber: 'ZTO9876543210987',\n  shippedAt: '2024-01-15 13:45:00',\n  estimatedDelivery: '2024-01-17 12:00:00',\n  createdAt: '2024-01-15 13:30:00',\n  updatedAt: '2024-01-15 14:00:00'\n}];\nconst OrderList = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState(mockOrders);\n  const [filteredOrders, setFilteredOrders] = useState(mockOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const navigate = useNavigate();\n\n  // 状态颜色映射\n  const getStatusColor = status => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      shipped: 'cyan',\n      activated: 'green',\n      cancelled: 'red'\n    };\n    return colors[status] || 'default';\n  };\n\n  // 状态文本映射\n  const getStatusText = status => {\n    const texts = {\n      pending: '待处理',\n      processing: '处理中',\n      shipped: '已发货',\n      activated: '已激活',\n      cancelled: '已取消'\n    };\n    return texts[status] || status;\n  };\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '订单状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物流信息',\n    key: 'logistics',\n    width: 200,\n    render: (_, record) => {\n      if (record.status === 'shipped' || record.status === 'activated') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 600,\n              fontSize: '12px'\n            },\n            children: record.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#666'\n            },\n            children: record.trackingNumber || '暂无快递单号'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), record.shippedAt && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '11px',\n              color: '#999'\n            },\n            children: [\"\\u53D1\\u8D27: \", record.shippedAt.split(' ')[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: \"\\u672A\\u53D1\\u8D27\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditOrder(record.id),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleSearch = values => {\n    setLoading(true);\n    setTimeout(() => {\n      let filtered = [...orders];\n      if (values.orderNo) {\n        filtered = filtered.filter(order => order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase()));\n      }\n      if (values.customerName) {\n        filtered = filtered.filter(order => order.customerName.toLowerCase().includes(values.customerName.toLowerCase()));\n      }\n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      if (values.status) {\n        filtered = filtered.filter(order => order.status === values.status);\n      }\n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n  const handleReset = () => {\n    form.resetFields();\n    setFilteredOrders(orders);\n  };\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockOrders);\n      setFilteredOrders(mockOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n  const handleViewOrder = order => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n  const handleEditOrder = id => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#1890ff'\n        },\n        children: \"\\u8BA2\\u5355\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u7BA1\\u7406\\u6240\\u6709\\u8BA2\\u5355\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA2\\u5355\\u6570\",\n            value: stats.total,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\",\n            value: stats.pending,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u6FC0\\u6D3B\",\n            value: stats.activated,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u53D1\\u8D27\",\n            value: stats.shipped,\n            valueStyle: {\n              color: '#13c2c2'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"orderNo\",\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",\n            style: {\n              width: 150\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"customerName\",\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",\n            style: {\n              width: 120\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"operator\",\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u79FB\\u52A8\",\n              children: \"\\u4E2D\\u56FD\\u79FB\\u52A8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u7535\\u4FE1\",\n              children: \"\\u4E2D\\u56FD\\u7535\\u4FE1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u8054\\u901A\",\n              children: \"\\u4E2D\\u56FD\\u8054\\u901A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u5E7F\\u7535\",\n              children: \"\\u4E2D\\u56FD\\u5E7F\\u7535\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending\",\n              children: \"\\u5F85\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"processing\",\n              children: \"\\u5904\\u7406\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"shipped\",\n              children: \"\\u5DF2\\u53D1\\u8D27\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"activated\",\n              children: \"\\u5DF2\\u6FC0\\u6D3B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"cancelled\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 62\n              }, this),\n              children: \"\\u641C\\u7D22\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleReset,\n              icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 51\n              }, this),\n              children: \"\\u91CD\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", filteredOrders.length, \" \\u6761\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleExport,\n            icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 50\n            }, this),\n            children: \"\\u5BFC\\u51FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredOrders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BA2\\u5355\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          span: 2,\n          children: selectedOrder.orderNo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: selectedOrder.customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8054\\u7CFB\\u7535\\u8BDD\",\n          children: selectedOrder.customerPhone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n          span: 2,\n          children: selectedOrder.productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: selectedOrder.operator\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(selectedOrder.status),\n            children: getStatusText(selectedOrder.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this), (selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7269\\u6D41\\u516C\\u53F8\",\n            children: selectedOrder.logisticsCompany || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5FEB\\u9012\\u5355\\u53F7\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              code: true,\n              children: selectedOrder.trackingNumber || '暂无'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u53D1\\u8D27\\u65F6\\u95F4\",\n            children: selectedOrder.shippedAt || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u9884\\u8BA1\\u9001\\u8FBE\",\n            children: selectedOrder.estimatedDelivery || '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: selectedOrder.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: selectedOrder.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderList, \"2scKhS/yQSUqS+yPfN1bc24iLLU=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = OrderList;\nexport default OrderList;\nvar _c;\n$RefreshReg$(_c, \"OrderList\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Form", "Input", "Select", "DatePicker", "Row", "Col", "Statistic", "message", "Modal", "Descriptions", "SearchOutlined", "EyeOutlined", "EditOutlined", "ExportOutlined", "ReloadOutlined", "ClearOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "RangePicker", "mockOrders", "id", "orderNo", "customerName", "customerPhone", "productName", "operator", "status", "logisticsCompany", "trackingNumber", "shippedAt", "estimatedDelivery", "createdAt", "updatedAt", "OrderList", "_s", "form", "useForm", "orders", "setOrders", "filteredOrders", "setFilteredOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "detailModalVisible", "setDetailModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "navigate", "getStatusColor", "colors", "pending", "processing", "shipped", "activated", "cancelled", "getStatusText", "texts", "stats", "total", "length", "filter", "order", "columns", "title", "dataIndex", "key", "width", "render", "text", "code", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "record", "fontWeight", "color", "ellipsis", "split", "type", "size", "icon", "onClick", "handleViewOrder", "handleEditOrder", "handleSearch", "values", "setTimeout", "filtered", "toLowerCase", "includes", "handleReset", "resetFields", "handleRefresh", "success", "info", "handleExport", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "placeholder", "Option", "htmlType", "display", "justifyContent", "alignItems", "strong", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "footer", "column", "bordered", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Modal,\n  Descriptions,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  FilterOutlined,\n  ClearOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { useNavigate } from 'react-router-dom';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\ninterface Order {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  productName: string;\n  operator: string;\n  status: 'pending' | 'processing' | 'shipped' | 'activated' | 'cancelled';\n  logisticsCompany?: string; // 物流公司\n  trackingNumber?: string; // 快递单号\n  shippedAt?: string; // 发货时间\n  estimatedDelivery?: string; // 预计送达时间\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 模拟订单数据\nconst mockOrders: Order[] = [\n  {\n    id: 1,\n    orderNo: 'ORD202401150001',\n    customerName: '张三',\n    customerPhone: '13800138001',\n    productName: '中国移动5G畅享套餐',\n    operator: '中国移动',\n    status: 'activated',\n    logisticsCompany: '顺丰速运',\n    trackingNumber: 'SF1234567890123',\n    shippedAt: '2024-01-15 10:45:00',\n    estimatedDelivery: '2024-01-16 18:00:00',\n    createdAt: '2024-01-15 10:30:00',\n    updatedAt: '2024-01-15 11:00:00',\n  },\n  {\n    id: 2,\n    orderNo: 'ORD202401150002',\n    customerName: '李四',\n    customerPhone: '13800138002',\n    productName: '中国电信5G精选套餐',\n    operator: '中国电信',\n    status: 'processing',\n    createdAt: '2024-01-15 11:15:00',\n    updatedAt: '2024-01-15 11:15:00',\n  },\n  {\n    id: 3,\n    orderNo: 'ORD202401150003',\n    customerName: '王五',\n    customerPhone: '13800138003',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n    status: 'pending',\n    createdAt: '2024-01-15 12:00:00',\n    updatedAt: '2024-01-15 12:00:00',\n  },\n  {\n    id: 4,\n    orderNo: 'ORD202401150004',\n    customerName: '赵六',\n    customerPhone: '13800138004',\n    productName: '中国广电智慧套餐',\n    operator: '中国广电',\n    status: 'shipped',\n    logisticsCompany: '中通快递',\n    trackingNumber: 'ZTO9876543210987',\n    shippedAt: '2024-01-15 13:45:00',\n    estimatedDelivery: '2024-01-17 12:00:00',\n    createdAt: '2024-01-15 13:30:00',\n    updatedAt: '2024-01-15 14:00:00',\n  },\n];\n\nconst OrderList: React.FC = () => {\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState<Order[]>(mockOrders);\n  const [filteredOrders, setFilteredOrders] = useState<Order[]>(mockOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const navigate = useNavigate();\n\n  // 状态颜色映射\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      shipped: 'cyan',\n      activated: 'green',\n      cancelled: 'red',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n\n\n  // 状态文本映射\n  const getStatusText = (status: string) => {\n    const texts = {\n      pending: '待处理',\n      processing: '处理中',\n      shipped: '已发货',\n      activated: '已激活',\n      cancelled: '已取消',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    shipped: filteredOrders.filter(order => order.status === 'shipped').length,\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Order> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n    {\n      title: '订单状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '物流信息',\n      key: 'logistics',\n      width: 200,\n      render: (_, record) => {\n        if (record.status === 'shipped' || record.status === 'activated') {\n          return (\n            <div>\n              <div style={{ fontWeight: 600, fontSize: '12px' }}>\n                {record.logisticsCompany || '暂无'}\n              </div>\n              <div style={{ fontSize: '11px', color: '#666' }}>\n                {record.trackingNumber || '暂无快递单号'}\n              </div>\n              {record.shippedAt && (\n                <div style={{ fontSize: '11px', color: '#999' }}>\n                  发货: {record.shippedAt.split(' ')[0]}\n                </div>\n              )}\n            </div>\n          );\n        }\n        return <Text type=\"secondary\" style={{ fontSize: '12px' }}>未发货</Text>;\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditOrder(record.id)}\n          >\n            编辑\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleSearch = (values: any) => {\n    setLoading(true);\n    \n    setTimeout(() => {\n      let filtered = [...orders];\n      \n      if (values.orderNo) {\n        filtered = filtered.filter(order => \n          order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase())\n        );\n      }\n      \n      if (values.customerName) {\n        filtered = filtered.filter(order => \n          order.customerName.toLowerCase().includes(values.customerName.toLowerCase())\n        );\n      }\n      \n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      \n      if (values.status) {\n        filtered = filtered.filter(order => order.status === values.status);\n      }\n      \n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n\n  const handleReset = () => {\n    form.resetFields();\n    setFilteredOrders(orders);\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockOrders);\n      setFilteredOrders(mockOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n\n  const handleViewOrder = (order: Order) => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n\n  const handleEditOrder = (id: number) => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n          订单列表\n        </Title>\n        <Text type=\"secondary\">\n          管理所有订单信息\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={stats.total}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待处理\"\n              value={stats.pending}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已激活\"\n              value={stats.activated}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已发货\"\n              value={stats.shipped}\n              valueStyle={{ color: '#13c2c2' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 搜索表单 */}\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ marginBottom: 16 }}\n        >\n          <Form.Item name=\"orderNo\" label=\"订单号\">\n            <Input placeholder=\"请输入订单号\" style={{ width: 150 }} />\n          </Form.Item>\n          <Form.Item name=\"customerName\" label=\"客户姓名\">\n            <Input placeholder=\"请输入客户姓名\" style={{ width: 120 }} />\n          </Form.Item>\n          <Form.Item name=\"operator\" label=\"运营商\">\n            <Select placeholder=\"请选择运营商\" style={{ width: 120 }}>\n              <Select.Option value=\"中国移动\">中国移动</Select.Option>\n              <Select.Option value=\"中国电信\">中国电信</Select.Option>\n              <Select.Option value=\"中国联通\">中国联通</Select.Option>\n              <Select.Option value=\"中国广电\">中国广电</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"status\" label=\"订单状态\">\n            <Select placeholder=\"请选择状态\" style={{ width: 120 }}>\n              <Select.Option value=\"pending\">待处理</Select.Option>\n              <Select.Option value=\"processing\">处理中</Select.Option>\n              <Select.Option value=\"shipped\">已发货</Select.Option>\n              <Select.Option value=\"activated\">已激活</Select.Option>\n              <Select.Option value=\"cancelled\">已取消</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                搜索\n              </Button>\n              <Button onClick={handleReset} icon={<ClearOutlined />}>\n                重置\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {filteredOrders.length} 条订单\n            </Text>\n          </div>\n          <Space>\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n            <Button onClick={handleExport} icon={<ExportOutlined />}>\n              导出\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={filteredOrders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 订单详情弹窗 */}\n      <Modal\n        title=\"订单详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={600}\n      >\n        {selectedOrder && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"订单号\" span={2}>\n              {selectedOrder.orderNo}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"客户姓名\">\n              {selectedOrder.customerName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"联系电话\">\n              {selectedOrder.customerPhone}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"产品名称\" span={2}>\n              {selectedOrder.productName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"运营商\">\n              <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"订单状态\">\n              <Tag color={getStatusColor(selectedOrder.status)}>\n                {getStatusText(selectedOrder.status)}\n              </Tag>\n            </Descriptions.Item>\n            {(selectedOrder.status === 'shipped' || selectedOrder.status === 'activated') && (\n              <>\n                <Descriptions.Item label=\"物流公司\">\n                  {selectedOrder.logisticsCompany || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"快递单号\">\n                  <Text code>{selectedOrder.trackingNumber || '暂无'}</Text>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"发货时间\">\n                  {selectedOrder.shippedAt || '暂无'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"预计送达\">\n                  {selectedOrder.estimatedDelivery || '暂无'}\n                </Descriptions.Item>\n              </>\n            )}\n            <Descriptions.Item label=\"创建时间\">\n              {selectedOrder.createdAt}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"更新时间\">\n              {selectedOrder.updatedAt}\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,QACP,MAAM;AACb,SACEC,cAAc,EACdC,WAAW,EACXC,YAAY,EAEZC,cAAc,EACdC,cAAc,EAEdC,aAAa,QACR,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGxB,UAAU;AAClC,MAAM;EAAEyB;AAAY,CAAC,GAAGpB,UAAU;AAkBlC;AACA,MAAMqB,UAAmB,GAAG,CAC1B;EACEC,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,WAAW;EACnBC,gBAAgB,EAAE,MAAM;EACxBC,cAAc,EAAE,iBAAiB;EACjCC,SAAS,EAAE,qBAAqB;EAChCC,iBAAiB,EAAE,qBAAqB;EACxCC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,EACD;EACEZ,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,YAAY;EACpBK,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,EACD;EACEZ,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,SAAS;EACjBK,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,EACD;EACEZ,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,SAAS;EACjBC,gBAAgB,EAAE,MAAM;EACxBC,cAAc,EAAE,kBAAkB;EAClCC,SAAS,EAAE,qBAAqB;EAChCC,iBAAiB,EAAE,qBAAqB;EACxCC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,CACF;AAED,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAGxC,IAAI,CAACyC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAU+B,UAAU,CAAC;EACzD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAU+B,UAAU,CAAC;EACzE,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAACyD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAM6D,QAAQ,GAAGtC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMuC,cAAc,GAAIxB,MAAc,IAAK;IACzC,MAAMyB,MAAM,GAAG;MACbC,OAAO,EAAE,QAAQ;MACjBC,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAACzB,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;;EAID;EACA,MAAM+B,aAAa,GAAI/B,MAAc,IAAK;IACxC,MAAMgC,KAAK,GAAG;MACZN,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAAChC,MAAM,CAAuB,IAAIA,MAAM;EACtD,CAAC;;EAID;EACA,MAAMiC,KAAK,GAAG;IACZC,KAAK,EAAErB,cAAc,CAACsB,MAAM;IAC5BT,OAAO,EAAEb,cAAc,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAK,SAAS,CAAC,CAACmC,MAAM;IAC1ER,UAAU,EAAEd,cAAc,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAK,YAAY,CAAC,CAACmC,MAAM;IAChFN,SAAS,EAAEhB,cAAc,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAK,WAAW,CAAC,CAACmC,MAAM;IAC9EP,OAAO,EAAEf,cAAc,CAACuB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAK,SAAS,CAAC,CAACmC;EACtE,CAAC;;EAED;EACA,MAAMG,OAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBzD,OAAA,CAACI,IAAI;MAACsD,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EACpCJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChBnE,OAAA;MAAA6D,QAAA,gBACE7D,OAAA;QAAK2D,KAAK,EAAE;UAAES,UAAU,EAAE;QAAI,CAAE;QAAAP,QAAA,EAAEM,MAAM,CAAC1D;MAAY;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5DjE,OAAA;QAAK2D,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAES,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,EAC7CM,MAAM,CAACzD;MAAa;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBgB,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBzD,OAAA,CAACnB,GAAG;MAACwF,KAAK,EAAC,MAAM;MAAAR,QAAA,EAAEJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG3C,MAAc,iBACrBb,OAAA,CAACnB,GAAG;MAACwF,KAAK,EAAEhC,cAAc,CAACxB,MAAM,CAAE;MAAAgD,QAAA,EAChCjB,aAAa,CAAC/B,MAAM;IAAC;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,KAAK;MACrB,IAAIA,MAAM,CAACtD,MAAM,KAAK,SAAS,IAAIsD,MAAM,CAACtD,MAAM,KAAK,WAAW,EAAE;QAChE,oBACEb,OAAA;UAAA6D,QAAA,gBACE7D,OAAA;YAAK2D,KAAK,EAAE;cAAES,UAAU,EAAE,GAAG;cAAER,QAAQ,EAAE;YAAO,CAAE;YAAAC,QAAA,EAC/CM,MAAM,CAACrD,gBAAgB,IAAI;UAAI;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNjE,OAAA;YAAK2D,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,EAC7CM,MAAM,CAACpD,cAAc,IAAI;UAAQ;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACLE,MAAM,CAACnD,SAAS,iBACfhB,OAAA;YAAK2D,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAC,gBAC3C,EAACM,MAAM,CAACnD,SAAS,CAACuD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEV;MACA,oBAAOjE,OAAA,CAACI,IAAI;QAACoE,IAAI,EAAC,WAAW;QAACb,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAC,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACvE;EACF,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBzD,OAAA;MAAK2D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EAC9BJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChBnE,OAAA,CAACrB,KAAK;MAAC8F,IAAI,EAAC,OAAO;MAAAZ,QAAA,gBACjB7D,OAAA,CAACtB,MAAM;QACL8F,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE1E,OAAA,CAACP,WAAW;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBU,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACT,MAAM,CAAE;QAAAN,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjE,OAAA,CAACtB,MAAM;QACL8F,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE1E,OAAA,CAACN,YAAY;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBU,OAAO,EAAEA,CAAA,KAAME,eAAe,CAACV,MAAM,CAAC5D,EAAE,CAAE;QAAAsD,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMa,YAAY,GAAIC,MAAW,IAAK;IACpClD,UAAU,CAAC,IAAI,CAAC;IAEhBmD,UAAU,CAAC,MAAM;MACf,IAAIC,QAAQ,GAAG,CAAC,GAAGzD,MAAM,CAAC;MAE1B,IAAIuD,MAAM,CAACvE,OAAO,EAAE;QAClByE,QAAQ,GAAGA,QAAQ,CAAChC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAAC1C,OAAO,CAAC0E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAACvE,OAAO,CAAC0E,WAAW,CAAC,CAAC,CACnE,CAAC;MACH;MAEA,IAAIH,MAAM,CAACtE,YAAY,EAAE;QACvBwE,QAAQ,GAAGA,QAAQ,CAAChC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAACzC,YAAY,CAACyE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAACtE,YAAY,CAACyE,WAAW,CAAC,CAAC,CAC7E,CAAC;MACH;MAEA,IAAIH,MAAM,CAACnE,QAAQ,EAAE;QACnBqE,QAAQ,GAAGA,QAAQ,CAAChC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtC,QAAQ,KAAKmE,MAAM,CAACnE,QAAQ,CAAC;MACzE;MAEA,IAAImE,MAAM,CAAClE,MAAM,EAAE;QACjBoE,QAAQ,GAAGA,QAAQ,CAAChC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAKkE,MAAM,CAAClE,MAAM,CAAC;MACrE;MAEAc,iBAAiB,CAACsD,QAAQ,CAAC;MAC3BpD,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMuD,WAAW,GAAGA,CAAA,KAAM;IACxB9D,IAAI,CAAC+D,WAAW,CAAC,CAAC;IAClB1D,iBAAiB,CAACH,MAAM,CAAC;EAC3B,CAAC;EAED,MAAM8D,aAAa,GAAGA,CAAA,KAAM;IAC1BzD,UAAU,CAAC,IAAI,CAAC;IAChBmD,UAAU,CAAC,MAAM;MACfvD,SAAS,CAACnB,UAAU,CAAC;MACrBqB,iBAAiB,CAACrB,UAAU,CAAC;MAC7BuB,UAAU,CAAC,KAAK,CAAC;MACjBxC,OAAO,CAACkG,OAAO,CAAC,OAAO,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMX,eAAe,GAAI1B,KAAY,IAAK;IACxCf,gBAAgB,CAACe,KAAK,CAAC;IACvBjB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM4C,eAAe,GAAItE,EAAU,IAAK;IACtClB,OAAO,CAACmG,IAAI,CAAC,QAAQjF,EAAE,WAAW,CAAC;EACrC,CAAC;EAED,MAAMkF,YAAY,GAAGA,CAAA,KAAM;IACzBpG,OAAO,CAACmG,IAAI,CAAC,YAAY,CAAC;EAC5B,CAAC;EAED,oBACExF,OAAA;IAAA6D,QAAA,gBACE7D,OAAA;MAAK2D,KAAK,EAAE;QAAE+B,YAAY,EAAE;MAAO,CAAE;MAAA7B,QAAA,gBACnC7D,OAAA,CAACG,KAAK;QAACwF,KAAK,EAAE,CAAE;QAAChC,KAAK,EAAE;UAAEiC,MAAM,EAAE,CAAC;UAAEvB,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjE,OAAA,CAACI,IAAI;QAACoE,IAAI,EAAC,WAAW;QAAAX,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNjE,OAAA,CAACd,GAAG;MAAC2G,MAAM,EAAE,EAAG;MAAClC,KAAK,EAAE;QAAE+B,YAAY,EAAE;MAAO,CAAE;MAAA7B,QAAA,gBAC/C7D,OAAA,CAACb,GAAG;QAAC2G,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACX7D,OAAA,CAACxB,IAAI;UAAAqF,QAAA,eACH7D,OAAA,CAACZ,SAAS;YACRgE,KAAK,EAAC,0BAAM;YACZ2C,KAAK,EAAEjD,KAAK,CAACC,KAAM;YACnBiD,UAAU,EAAE;cAAE3B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjE,OAAA,CAACb,GAAG;QAAC2G,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACX7D,OAAA,CAACxB,IAAI;UAAAqF,QAAA,eACH7D,OAAA,CAACZ,SAAS;YACRgE,KAAK,EAAC,oBAAK;YACX2C,KAAK,EAAEjD,KAAK,CAACP,OAAQ;YACrByD,UAAU,EAAE;cAAE3B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjE,OAAA,CAACb,GAAG;QAAC2G,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACX7D,OAAA,CAACxB,IAAI;UAAAqF,QAAA,eACH7D,OAAA,CAACZ,SAAS;YACRgE,KAAK,EAAC,oBAAK;YACX2C,KAAK,EAAEjD,KAAK,CAACJ,SAAU;YACvBsD,UAAU,EAAE;cAAE3B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjE,OAAA,CAACb,GAAG;QAAC2G,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACX7D,OAAA,CAACxB,IAAI;UAAAqF,QAAA,eACH7D,OAAA,CAACZ,SAAS;YACRgE,KAAK,EAAC,oBAAK;YACX2C,KAAK,EAAEjD,KAAK,CAACL,OAAQ;YACrBuD,UAAU,EAAE;cAAE3B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjE,OAAA,CAACxB,IAAI;MAAAqF,QAAA,gBAEH7D,OAAA,CAAClB,IAAI;QACHwC,IAAI,EAAEA,IAAK;QACX2E,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEpB,YAAa;QACvBnB,KAAK,EAAE;UAAE+B,YAAY,EAAE;QAAG,CAAE;QAAA7B,QAAA,gBAE5B7D,OAAA,CAAClB,IAAI,CAACqH,IAAI;UAACC,IAAI,EAAC,SAAS;UAACC,KAAK,EAAC,oBAAK;UAAAxC,QAAA,eACnC7D,OAAA,CAACjB,KAAK;YAACuH,WAAW,EAAC,sCAAQ;YAAC3C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACZjE,OAAA,CAAClB,IAAI,CAACqH,IAAI;UAACC,IAAI,EAAC,cAAc;UAACC,KAAK,EAAC,0BAAM;UAAAxC,QAAA,eACzC7D,OAAA,CAACjB,KAAK;YAACuH,WAAW,EAAC,4CAAS;YAAC3C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACZjE,OAAA,CAAClB,IAAI,CAACqH,IAAI;UAACC,IAAI,EAAC,UAAU;UAACC,KAAK,EAAC,oBAAK;UAAAxC,QAAA,eACpC7D,OAAA,CAAChB,MAAM;YAACsH,WAAW,EAAC,sCAAQ;YAAC3C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACjD7D,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAAlC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDjE,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAAlC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDjE,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAAlC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDjE,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,0BAAM;cAAAlC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZjE,OAAA,CAAClB,IAAI,CAACqH,IAAI;UAACC,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC,0BAAM;UAAAxC,QAAA,eACnC7D,OAAA,CAAChB,MAAM;YAACsH,WAAW,EAAC,gCAAO;YAAC3C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBAChD7D,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,SAAS;cAAAlC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDjE,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,YAAY;cAAAlC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACrDjE,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,SAAS;cAAAlC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDjE,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,WAAW;cAAAlC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACpDjE,OAAA,CAAChB,MAAM,CAACuH,MAAM;cAACR,KAAK,EAAC,WAAW;cAAAlC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZjE,OAAA,CAAClB,IAAI,CAACqH,IAAI;UAAAtC,QAAA,eACR7D,OAAA,CAACrB,KAAK;YAAAkF,QAAA,gBACJ7D,OAAA,CAACtB,MAAM;cAAC8F,IAAI,EAAC,SAAS;cAACgC,QAAQ,EAAC,QAAQ;cAAC9B,IAAI,eAAE1E,OAAA,CAACR,cAAc;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAAC;YAEnE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA,CAACtB,MAAM;cAACiG,OAAO,EAAES,WAAY;cAACV,IAAI,eAAE1E,OAAA,CAACH,aAAa;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGPjE,OAAA;QAAK2D,KAAK,EAAE;UACV8C,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBjB,YAAY,EAAE;QAChB,CAAE;QAAA7B,QAAA,gBACA7D,OAAA;UAAA6D,QAAA,eACE7D,OAAA,CAACI,IAAI;YAACwG,MAAM;YAAA/C,QAAA,GAAC,SACT,EAACnC,cAAc,CAACsB,MAAM,EAAC,qBAC3B;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNjE,OAAA,CAACrB,KAAK;UAAAkF,QAAA,gBACJ7D,OAAA,CAACtB,MAAM;YAACiG,OAAO,EAAEW,aAAc;YAACZ,IAAI,eAAE1E,OAAA,CAACJ,cAAc;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjE,OAAA,CAACtB,MAAM;YAACiG,OAAO,EAAEc,YAAa;YAACf,IAAI,eAAE1E,OAAA,CAACL,cAAc;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNjE,OAAA,CAACvB,KAAK;QACJ0E,OAAO,EAAEA,OAAQ;QACjB0D,UAAU,EAAEnF,cAAe;QAC3BoF,MAAM,EAAC,IAAI;QACXlF,OAAO,EAAEA,OAAQ;QACjBmF,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACrE,KAAK,EAAEsE,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAAStE,KAAK;QAC3C,CAAE;QACFuE,YAAY,EAAE;UACZxF,eAAe;UACfyF,QAAQ,EAAExF;QACZ;MAAE;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPjE,OAAA,CAACV,KAAK;MACJ8D,KAAK,EAAC,0BAAM;MACZoE,IAAI,EAAExF,kBAAmB;MACzByF,QAAQ,EAAEA,CAAA,KAAMxF,qBAAqB,CAAC,KAAK,CAAE;MAC7CyF,MAAM,EAAE,cACN1H,OAAA,CAACtB,MAAM;QAAaiG,OAAO,EAAEA,CAAA,KAAM1C,qBAAqB,CAAC,KAAK,CAAE;QAAA4B,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFV,KAAK,EAAE,GAAI;MAAAM,QAAA,EAEV3B,aAAa,iBACZlC,OAAA,CAACT,YAAY;QAACoI,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAA/D,QAAA,gBAC/B7D,OAAA,CAACT,YAAY,CAAC4G,IAAI;UAACE,KAAK,EAAC,oBAAK;UAACP,IAAI,EAAE,CAAE;UAAAjC,QAAA,EACpC3B,aAAa,CAAC1B;QAAO;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5B3B,aAAa,CAACzB;QAAY;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5B3B,aAAa,CAACxB;QAAa;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAACP,IAAI,EAAE,CAAE;UAAAjC,QAAA,EACrC3B,aAAa,CAACvB;QAAW;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;UAACE,KAAK,EAAC,oBAAK;UAAAxC,QAAA,eAC5B7D,OAAA,CAACnB,GAAG;YAACwF,KAAK,EAAC,MAAM;YAAAR,QAAA,EAAE3B,aAAa,CAACtB;UAAQ;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAxC,QAAA,eAC7B7D,OAAA,CAACnB,GAAG;YAACwF,KAAK,EAAEhC,cAAc,CAACH,aAAa,CAACrB,MAAM,CAAE;YAAAgD,QAAA,EAC9CjB,aAAa,CAACV,aAAa,CAACrB,MAAM;UAAC;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,EACnB,CAAC/B,aAAa,CAACrB,MAAM,KAAK,SAAS,IAAIqB,aAAa,CAACrB,MAAM,KAAK,WAAW,kBAC1Eb,OAAA,CAAAE,SAAA;UAAA2D,QAAA,gBACE7D,OAAA,CAACT,YAAY,CAAC4G,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAxC,QAAA,EAC5B3B,aAAa,CAACpB,gBAAgB,IAAI;UAAI;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAxC,QAAA,eAC7B7D,OAAA,CAACI,IAAI;cAACsD,IAAI;cAAAG,QAAA,EAAE3B,aAAa,CAACnB,cAAc,IAAI;YAAI;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAxC,QAAA,EAC5B3B,aAAa,CAAClB,SAAS,IAAI;UAAI;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAxC,QAAA,EAC5B3B,aAAa,CAACjB,iBAAiB,IAAI;UAAI;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA,eACpB,CACH,eACDjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5B3B,aAAa,CAAChB;QAAS;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACpBjE,OAAA,CAACT,YAAY,CAAC4G,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5B3B,aAAa,CAACf;QAAS;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAnaID,SAAmB;EAAA,QACRtC,IAAI,CAACyC,OAAO,EAOVzB,WAAW;AAAA;AAAA+H,EAAA,GARxBzG,SAAmB;AAqazB,eAAeA,SAAS;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
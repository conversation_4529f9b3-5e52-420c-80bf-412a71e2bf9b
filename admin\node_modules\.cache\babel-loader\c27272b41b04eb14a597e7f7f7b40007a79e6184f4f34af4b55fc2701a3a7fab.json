{"ast": null, "code": "// This icon file is generated automatically.\nvar ProductOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"fill-rule\": \"evenodd\",\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M464 144a16 16 0 0116 16v304a16 16 0 01-16 16H160a16 16 0 01-16-16V160a16 16 0 0116-16zm-52 68H212v200h200zm493.33 87.69a16 16 0 010 22.62L724.31 503.33a16 16 0 01-22.62 0L520.67 322.31a16 16 0 010-22.62l181.02-181.02a16 16 0 0122.62 0zm-84.85 11.3L713 203.53 605.52 311 713 418.48zM464 544a16 16 0 0116 16v304a16 16 0 01-16 16H160a16 16 0 01-16-16V560a16 16 0 0116-16zm-52 68H212v200h200zm452-68a16 16 0 0116 16v304a16 16 0 01-16 16H560a16 16 0 01-16-16V560a16 16 0 0116-16zm-52 68H612v200h200z\"\n      }\n    }]\n  },\n  \"name\": \"product\",\n  \"theme\": \"outlined\"\n};\nexport default ProductOutlined;", "map": {"version": 3, "names": ["ProductOutlined"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/@ant-design/icons-svg/es/asn/ProductOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar ProductOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"fill-rule\": \"evenodd\", \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M464 144a16 16 0 0116 16v304a16 16 0 01-16 16H160a16 16 0 01-16-16V160a16 16 0 0116-16zm-52 68H212v200h200zm493.33 87.69a16 16 0 010 22.62L724.31 503.33a16 16 0 01-22.62 0L520.67 322.31a16 16 0 010-22.62l181.02-181.02a16 16 0 0122.62 0zm-84.85 11.3L713 203.53 605.52 311 713 418.48zM464 544a16 16 0 0116 16v304a16 16 0 01-16 16H160a16 16 0 01-16-16V560a16 16 0 0116-16zm-52 68H212v200h200zm452-68a16 16 0 0116 16v304a16 16 0 01-16 16H560a16 16 0 01-16-16V560a16 16 0 0116-16zm-52 68H612v200h200z\" } }] }, \"name\": \"product\", \"theme\": \"outlined\" };\nexport default ProductOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,eAAe,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,WAAW,EAAE,SAAS;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAkf;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,SAAS;EAAE,OAAO,EAAE;AAAW,CAAC;AACvtB,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
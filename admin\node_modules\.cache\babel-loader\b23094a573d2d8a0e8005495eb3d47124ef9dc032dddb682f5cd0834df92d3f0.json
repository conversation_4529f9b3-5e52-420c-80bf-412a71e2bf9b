{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { getMergedStatus } from '../../_util/statusUtils';\nimport { devUseWarning } from '../../_util/warning';\nimport { ConfigContext } from '../../config-provider';\nimport useSize from '../../config-provider/hooks/useSize';\nimport { FormItemInputContext } from '../../form/context';\nimport useStyle from '../style/otp';\nimport OTPInput from './OTPInput';\nfunction strToArr(str) {\n  return (str || '').split('');\n}\nconst Separator = props => {\n  const {\n    index,\n    prefixCls,\n    separator\n  } = props;\n  const separatorNode = typeof separator === 'function' ? separator(index) : separator;\n  if (!separatorNode) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-separator\")\n  }, separatorNode);\n};\nconst OTP = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      length = 6,\n      size: customSize,\n      defaultValue,\n      value,\n      onChange,\n      formatter,\n      separator,\n      variant,\n      disabled,\n      status: customStatus,\n      autoFocus,\n      mask,\n      type,\n      onInput,\n      inputMode\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"length\", \"size\", \"defaultValue\", \"value\", \"onChange\", \"formatter\", \"separator\", \"variant\", \"disabled\", \"status\", \"autoFocus\", \"mask\", \"type\", \"onInput\", \"inputMode\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Input.OTP');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof mask === 'string' && mask.length > 1), 'usage', '`mask` prop should be a single character.') : void 0;\n  }\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('otp', customizePrefixCls);\n  const domAttrs = pickAttrs(restProps, {\n    aria: true,\n    data: true,\n    attr: true\n  });\n  // ========================= Root =========================\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // ========================= Size =========================\n  const mergedSize = useSize(ctx => customSize !== null && customSize !== void 0 ? customSize : ctx);\n  // ======================== Status ========================\n  const formContext = React.useContext(FormItemInputContext);\n  const mergedStatus = getMergedStatus(formContext.status, customStatus);\n  const proxyFormContext = React.useMemo(() => Object.assign(Object.assign({}, formContext), {\n    status: mergedStatus,\n    hasFeedback: false,\n    feedbackIcon: null\n  }), [formContext, mergedStatus]);\n  // ========================= Refs =========================\n  const containerRef = React.useRef(null);\n  const refs = React.useRef({});\n  React.useImperativeHandle(ref, () => ({\n    focus: () => {\n      var _a;\n      (_a = refs.current[0]) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      for (let i = 0; i < length; i += 1) {\n        (_a = refs.current[i]) === null || _a === void 0 ? void 0 : _a.blur();\n      }\n    },\n    nativeElement: containerRef.current\n  }));\n  // ======================= Formatter ======================\n  const internalFormatter = txt => formatter ? formatter(txt) : txt;\n  // ======================== Values ========================\n  const [valueCells, setValueCells] = React.useState(() => strToArr(internalFormatter(defaultValue || '')));\n  React.useEffect(() => {\n    if (value !== undefined) {\n      setValueCells(strToArr(value));\n    }\n  }, [value]);\n  const triggerValueCellsChange = useEvent(nextValueCells => {\n    setValueCells(nextValueCells);\n    if (onInput) {\n      onInput(nextValueCells);\n    }\n    // Trigger if all cells are filled\n    if (onChange && nextValueCells.length === length && nextValueCells.every(c => c) && nextValueCells.some((c, index) => valueCells[index] !== c)) {\n      onChange(nextValueCells.join(''));\n    }\n  });\n  const patchValue = useEvent((index, txt) => {\n    let nextCells = _toConsumableArray(valueCells);\n    // Fill cells till index\n    for (let i = 0; i < index; i += 1) {\n      if (!nextCells[i]) {\n        nextCells[i] = '';\n      }\n    }\n    if (txt.length <= 1) {\n      nextCells[index] = txt;\n    } else {\n      nextCells = nextCells.slice(0, index).concat(strToArr(txt));\n    }\n    nextCells = nextCells.slice(0, length);\n    // Clean the last empty cell\n    for (let i = nextCells.length - 1; i >= 0; i -= 1) {\n      if (nextCells[i]) {\n        break;\n      }\n      nextCells.pop();\n    }\n    // Format if needed\n    const formattedValue = internalFormatter(nextCells.map(c => c || ' ').join(''));\n    nextCells = strToArr(formattedValue).map((c, i) => {\n      if (c === ' ' && !nextCells[i]) {\n        return nextCells[i];\n      }\n      return c;\n    });\n    return nextCells;\n  });\n  // ======================== Change ========================\n  const onInputChange = (index, txt) => {\n    var _a;\n    const nextCells = patchValue(index, txt);\n    const nextIndex = Math.min(index + txt.length, length - 1);\n    if (nextIndex !== index && nextCells[index] !== undefined) {\n      (_a = refs.current[nextIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n    triggerValueCellsChange(nextCells);\n  };\n  const onInputActiveChange = nextIndex => {\n    var _a;\n    (_a = refs.current[nextIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n  };\n  // ======================== Render ========================\n  const inputSharedProps = {\n    variant,\n    disabled,\n    status: mergedStatus,\n    mask,\n    type,\n    inputMode\n  };\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({}, domAttrs, {\n    ref: containerRef,\n    className: classNames(prefixCls, {\n      [\"\".concat(prefixCls, \"-sm\")]: mergedSize === 'small',\n      [\"\".concat(prefixCls, \"-lg\")]: mergedSize === 'large',\n      [\"\".concat(prefixCls, \"-rtl\")]: direction === 'rtl'\n    }, cssVarCls, hashId),\n    role: \"group\"\n  }), /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: proxyFormContext\n  }, Array.from({\n    length\n  }).map((_, index) => {\n    const key = \"otp-\".concat(index);\n    const singleValue = valueCells[index] || '';\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: key\n    }, /*#__PURE__*/React.createElement(OTPInput, Object.assign({\n      ref: inputEle => {\n        refs.current[index] = inputEle;\n      },\n      index: index,\n      size: mergedSize,\n      htmlSize: 1,\n      className: \"\".concat(prefixCls, \"-input\"),\n      onChange: onInputChange,\n      value: singleValue,\n      onActiveChange: onInputActiveChange,\n      autoFocus: index === 0 && autoFocus\n    }, inputSharedProps)), index < length - 1 && (/*#__PURE__*/React.createElement(Separator, {\n      separator: separator,\n      index: index,\n      prefixCls: prefixCls\n    })));\n  }))));\n});\nexport default OTP;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
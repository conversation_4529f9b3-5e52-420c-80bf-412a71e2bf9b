{"ast": null, "code": "var _jsxFileName = \"G:\\\\phpstudy_pro\\\\WWW\\\\admin\\\\src\\\\pages\\\\OrderList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Table, Button, Space, Typography, Tag, Form, Input, Select, DatePicker, Row, Col, Statistic, message, Modal, Descriptions } from 'antd';\nimport { SearchOutlined, EyeOutlined, EditOutlined, ExportOutlined, ReloadOutlined, ClearOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\n// 模拟订单数据\nconst mockOrders = [{\n  id: 1,\n  orderNo: 'ORD202401150001',\n  customerName: '张三',\n  customerPhone: '13800138001',\n  productName: '中国移动5G畅享套餐',\n  operator: '中国移动',\n  status: 'activated',\n  createdAt: '2024-01-15 10:30:00',\n  updatedAt: '2024-01-15 11:00:00'\n}, {\n  id: 2,\n  orderNo: 'ORD202401150002',\n  customerName: '李四',\n  customerPhone: '13800138002',\n  productName: '中国电信5G精选套餐',\n  operator: '中国电信',\n  status: 'processing',\n  createdAt: '2024-01-15 11:15:00',\n  updatedAt: '2024-01-15 11:15:00'\n}, {\n  id: 3,\n  orderNo: 'ORD202401150003',\n  customerName: '王五',\n  customerPhone: '13800138003',\n  productName: '中国联通青春套餐',\n  operator: '中国联通',\n  status: 'pending',\n  createdAt: '2024-01-15 12:00:00',\n  updatedAt: '2024-01-15 12:00:00'\n}, {\n  id: 4,\n  orderNo: 'ORD202401150004',\n  customerName: '赵六',\n  customerPhone: '13800138004',\n  productName: '中国广电智慧套餐',\n  operator: '中国广电',\n  status: 'shipped',\n  createdAt: '2024-01-15 13:30:00',\n  updatedAt: '2024-01-15 14:00:00'\n}];\nconst OrderList = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState(mockOrders);\n  const [filteredOrders, setFilteredOrders] = useState(mockOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const navigate = useNavigate();\n\n  // 状态颜色映射\n  const getStatusColor = status => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      shipped: 'cyan',\n      activated: 'green',\n      cancelled: 'red'\n    };\n    return colors[status] || 'default';\n  };\n\n  // 状态文本映射\n  const getStatusText = status => {\n    const texts = {\n      pending: '待处理',\n      processing: '处理中',\n      shipped: '已发货',\n      activated: '已激活',\n      cancelled: '已取消'\n    };\n    return texts[status] || status;\n  };\n\n  // 支付状态文本映射\n  const getPaymentStatusText = status => {\n    const texts = {\n      unpaid: '未支付',\n      paid: '已支付',\n      refunded: '已退款'\n    };\n    return texts[status] || status;\n  };\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    totalAmount: filteredOrders.reduce((sum, order) => sum + order.amount, 0)\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户信息',\n    key: 'customer',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 600\n        },\n        children: record.customerName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.customerPhone\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '产品名称',\n    dataIndex: 'productName',\n    key: 'productName',\n    ellipsis: true\n  }, {\n    title: '运营商',\n    dataIndex: 'operator',\n    key: 'operator',\n    width: 100,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '金额',\n    dataIndex: 'amount',\n    key: 'amount',\n    width: 100,\n    render: amount => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#f5222d'\n      },\n      children: [\"\\xA5\", amount.toFixed(2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '订单状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '支付状态',\n    dataIndex: 'paymentStatus',\n    key: 'paymentStatus',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getPaymentStatusColor(status),\n      children: getPaymentStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewOrder(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditOrder(record.id),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 事件处理函数\n  const handleSearch = values => {\n    setLoading(true);\n    setTimeout(() => {\n      let filtered = [...orders];\n      if (values.orderNo) {\n        filtered = filtered.filter(order => order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase()));\n      }\n      if (values.customerName) {\n        filtered = filtered.filter(order => order.customerName.toLowerCase().includes(values.customerName.toLowerCase()));\n      }\n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      if (values.status) {\n        filtered = filtered.filter(order => order.status === values.status);\n      }\n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n  const handleReset = () => {\n    form.resetFields();\n    setFilteredOrders(orders);\n  };\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockOrders);\n      setFilteredOrders(mockOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n  const handleViewOrder = order => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n  const handleEditOrder = id => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0,\n          color: '#1890ff'\n        },\n        children: \"\\u8BA2\\u5355\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u7BA1\\u7406\\u6240\\u6709\\u8BA2\\u5355\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA2\\u5355\\u6570\",\n            value: stats.total,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5904\\u7406\",\n            value: stats.pending,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u6FC0\\u6D3B\",\n            value: stats.activated,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u91D1\\u989D\",\n            value: stats.totalAmount,\n            precision: 2,\n            prefix: \"\\xA5\",\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"orderNo\",\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\",\n            style: {\n              width: 150\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"customerName\",\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u59D3\\u540D\",\n            style: {\n              width: 120\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"operator\",\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8FD0\\u8425\\u5546\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u79FB\\u52A8\",\n              children: \"\\u4E2D\\u56FD\\u79FB\\u52A8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u7535\\u4FE1\",\n              children: \"\\u4E2D\\u56FD\\u7535\\u4FE1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u8054\\u901A\",\n              children: \"\\u4E2D\\u56FD\\u8054\\u901A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"\\u4E2D\\u56FD\\u5E7F\\u7535\",\n              children: \"\\u4E2D\\u56FD\\u5E7F\\u7535\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"pending\",\n              children: \"\\u5F85\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"processing\",\n              children: \"\\u5904\\u7406\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"shipped\",\n              children: \"\\u5DF2\\u53D1\\u8D27\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"activated\",\n              children: \"\\u5DF2\\u6FC0\\u6D3B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"cancelled\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 62\n              }, this),\n              children: \"\\u641C\\u7D22\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleReset,\n              icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 51\n              }, this),\n              children: \"\\u91CD\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u5171 \", filteredOrders.length, \" \\u6761\\u8BA2\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleExport,\n            icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 50\n            }, this),\n            children: \"\\u5BFC\\u51FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredOrders,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`\n        },\n        rowSelection: {\n          selectedRowKeys,\n          onChange: setSelectedRowKeys\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BA2\\u5355\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: selectedOrder && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          span: 2,\n          children: selectedOrder.orderNo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u59D3\\u540D\",\n          children: selectedOrder.customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8054\\u7CFB\\u7535\\u8BDD\",\n          children: selectedOrder.customerPhone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n          span: 2,\n          children: selectedOrder.productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8FD0\\u8425\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: selectedOrder.operator\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u91D1\\u989D\",\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            style: {\n              color: '#f5222d'\n            },\n            children: [\"\\xA5\", selectedOrder.amount.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(selectedOrder.status),\n            children: getStatusText(selectedOrder.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u652F\\u4ED8\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getPaymentStatusColor(selectedOrder.paymentStatus),\n            children: getPaymentStatusText(selectedOrder.paymentStatus)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: selectedOrder.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: selectedOrder.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 324,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderList, \"2scKhS/yQSUqS+yPfN1bc24iLLU=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = OrderList;\nexport default OrderList;\nvar _c;\n$RefreshReg$(_c, \"OrderList\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Form", "Input", "Select", "DatePicker", "Row", "Col", "Statistic", "message", "Modal", "Descriptions", "SearchOutlined", "EyeOutlined", "EditOutlined", "ExportOutlined", "ReloadOutlined", "ClearOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Title", "Text", "RangePicker", "mockOrders", "id", "orderNo", "customerName", "customerPhone", "productName", "operator", "status", "createdAt", "updatedAt", "OrderList", "_s", "form", "useForm", "orders", "setOrders", "filteredOrders", "setFilteredOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "detailModalVisible", "setDetailModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "navigate", "getStatusColor", "colors", "pending", "processing", "shipped", "activated", "cancelled", "getStatusText", "texts", "getPaymentStatusText", "unpaid", "paid", "refunded", "stats", "total", "length", "filter", "order", "totalAmount", "reduce", "sum", "amount", "columns", "title", "dataIndex", "key", "width", "render", "text", "code", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "record", "fontWeight", "color", "ellipsis", "strong", "toFixed", "getPaymentStatusColor", "size", "type", "icon", "onClick", "handleViewOrder", "handleEditOrder", "handleSearch", "values", "setTimeout", "filtered", "toLowerCase", "includes", "handleReset", "resetFields", "handleRefresh", "success", "info", "handleExport", "marginBottom", "level", "margin", "gutter", "span", "value", "valueStyle", "precision", "prefix", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "placeholder", "Option", "htmlType", "display", "justifyContent", "alignItems", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "range", "rowSelection", "onChange", "open", "onCancel", "footer", "column", "bordered", "paymentStatus", "_c", "$RefreshReg$"], "sources": ["G:/phpstudy_pro/WWW/admin/src/pages/OrderList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Modal,\n  Descriptions,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  FilterOutlined,\n  ClearOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { useNavigate } from 'react-router-dom';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\ninterface Order {\n  id: number;\n  orderNo: string;\n  customerName: string;\n  customerPhone: string;\n  productName: string;\n  operator: string;\n  status: 'pending' | 'processing' | 'shipped' | 'activated' | 'cancelled';\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 模拟订单数据\nconst mockOrders: Order[] = [\n  {\n    id: 1,\n    orderNo: 'ORD202401150001',\n    customerName: '张三',\n    customerPhone: '13800138001',\n    productName: '中国移动5G畅享套餐',\n    operator: '中国移动',\n    status: 'activated',\n    createdAt: '2024-01-15 10:30:00',\n    updatedAt: '2024-01-15 11:00:00',\n  },\n  {\n    id: 2,\n    orderNo: 'ORD202401150002',\n    customerName: '李四',\n    customerPhone: '13800138002',\n    productName: '中国电信5G精选套餐',\n    operator: '中国电信',\n    status: 'processing',\n    createdAt: '2024-01-15 11:15:00',\n    updatedAt: '2024-01-15 11:15:00',\n  },\n  {\n    id: 3,\n    orderNo: 'ORD202401150003',\n    customerName: '王五',\n    customerPhone: '13800138003',\n    productName: '中国联通青春套餐',\n    operator: '中国联通',\n    status: 'pending',\n    createdAt: '2024-01-15 12:00:00',\n    updatedAt: '2024-01-15 12:00:00',\n  },\n  {\n    id: 4,\n    orderNo: 'ORD202401150004',\n    customerName: '赵六',\n    customerPhone: '13800138004',\n    productName: '中国广电智慧套餐',\n    operator: '中国广电',\n    status: 'shipped',\n    createdAt: '2024-01-15 13:30:00',\n    updatedAt: '2024-01-15 14:00:00',\n  },\n];\n\nconst OrderList: React.FC = () => {\n  const [form] = Form.useForm();\n  const [orders, setOrders] = useState<Order[]>(mockOrders);\n  const [filteredOrders, setFilteredOrders] = useState<Order[]>(mockOrders);\n  const [loading, setLoading] = useState(false);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const navigate = useNavigate();\n\n  // 状态颜色映射\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'orange',\n      processing: 'blue',\n      shipped: 'cyan',\n      activated: 'green',\n      cancelled: 'red',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n\n\n  // 状态文本映射\n  const getStatusText = (status: string) => {\n    const texts = {\n      pending: '待处理',\n      processing: '处理中',\n      shipped: '已发货',\n      activated: '已激活',\n      cancelled: '已取消',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  // 支付状态文本映射\n  const getPaymentStatusText = (status: string) => {\n    const texts = {\n      unpaid: '未支付',\n      paid: '已支付',\n      refunded: '已退款',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  // 统计数据\n  const stats = {\n    total: filteredOrders.length,\n    pending: filteredOrders.filter(order => order.status === 'pending').length,\n    processing: filteredOrders.filter(order => order.status === 'processing').length,\n    activated: filteredOrders.filter(order => order.status === 'activated').length,\n    totalAmount: filteredOrders.reduce((sum, order) => sum + order.amount, 0),\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Order> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 160,\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {text}\n        </Text>\n      ),\n    },\n    {\n      title: '客户信息',\n      key: 'customer',\n      width: 150,\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 600 }}>{record.customerName}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.customerPhone}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '产品名称',\n      dataIndex: 'productName',\n      key: 'productName',\n      ellipsis: true,\n    },\n    {\n      title: '运营商',\n      dataIndex: 'operator',\n      key: 'operator',\n      width: 100,\n      render: (text: string) => (\n        <Tag color=\"blue\">{text}</Tag>\n      ),\n    },\n    {\n      title: '金额',\n      dataIndex: 'amount',\n      key: 'amount',\n      width: 100,\n      render: (amount: number) => (\n        <Text strong style={{ color: '#f5222d' }}>\n          ¥{amount.toFixed(2)}\n        </Text>\n      ),\n    },\n    {\n      title: '订单状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '支付状态',\n      dataIndex: 'paymentStatus',\n      key: 'paymentStatus',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getPaymentStatusColor(status)}>\n          {getPaymentStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontSize: '12px' }}>\n          {text}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewOrder(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditOrder(record.id)}\n          >\n            编辑\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 事件处理函数\n  const handleSearch = (values: any) => {\n    setLoading(true);\n    \n    setTimeout(() => {\n      let filtered = [...orders];\n      \n      if (values.orderNo) {\n        filtered = filtered.filter(order => \n          order.orderNo.toLowerCase().includes(values.orderNo.toLowerCase())\n        );\n      }\n      \n      if (values.customerName) {\n        filtered = filtered.filter(order => \n          order.customerName.toLowerCase().includes(values.customerName.toLowerCase())\n        );\n      }\n      \n      if (values.operator) {\n        filtered = filtered.filter(order => order.operator === values.operator);\n      }\n      \n      if (values.status) {\n        filtered = filtered.filter(order => order.status === values.status);\n      }\n      \n      setFilteredOrders(filtered);\n      setLoading(false);\n    }, 500);\n  };\n\n  const handleReset = () => {\n    form.resetFields();\n    setFilteredOrders(orders);\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setOrders(mockOrders);\n      setFilteredOrders(mockOrders);\n      setLoading(false);\n      message.success('数据已刷新');\n    }, 1000);\n  };\n\n  const handleViewOrder = (order: Order) => {\n    setSelectedOrder(order);\n    setDetailModalVisible(true);\n  };\n\n  const handleEditOrder = (id: number) => {\n    message.info(`编辑订单 ${id} 功能开发中...`);\n  };\n\n  const handleExport = () => {\n    message.info('导出功能开发中...');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>\n          订单列表\n        </Title>\n        <Text type=\"secondary\">\n          管理所有订单信息\n        </Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={stats.total}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待处理\"\n              value={stats.pending}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已激活\"\n              value={stats.activated}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总金额\"\n              value={stats.totalAmount}\n              precision={2}\n              prefix=\"¥\"\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        {/* 搜索表单 */}\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ marginBottom: 16 }}\n        >\n          <Form.Item name=\"orderNo\" label=\"订单号\">\n            <Input placeholder=\"请输入订单号\" style={{ width: 150 }} />\n          </Form.Item>\n          <Form.Item name=\"customerName\" label=\"客户姓名\">\n            <Input placeholder=\"请输入客户姓名\" style={{ width: 120 }} />\n          </Form.Item>\n          <Form.Item name=\"operator\" label=\"运营商\">\n            <Select placeholder=\"请选择运营商\" style={{ width: 120 }}>\n              <Select.Option value=\"中国移动\">中国移动</Select.Option>\n              <Select.Option value=\"中国电信\">中国电信</Select.Option>\n              <Select.Option value=\"中国联通\">中国联通</Select.Option>\n              <Select.Option value=\"中国广电\">中国广电</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item name=\"status\" label=\"订单状态\">\n            <Select placeholder=\"请选择状态\" style={{ width: 120 }}>\n              <Select.Option value=\"pending\">待处理</Select.Option>\n              <Select.Option value=\"processing\">处理中</Select.Option>\n              <Select.Option value=\"shipped\">已发货</Select.Option>\n              <Select.Option value=\"activated\">已激活</Select.Option>\n              <Select.Option value=\"cancelled\">已取消</Select.Option>\n            </Select>\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                搜索\n              </Button>\n              <Button onClick={handleReset} icon={<ClearOutlined />}>\n                重置\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n\n        {/* 操作按钮 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          marginBottom: 16 \n        }}>\n          <div>\n            <Text strong>\n              共 {filteredOrders.length} 条订单\n            </Text>\n          </div>\n          <Space>\n            <Button onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n            <Button onClick={handleExport} icon={<ExportOutlined />}>\n              导出\n            </Button>\n          </Space>\n        </div>\n\n        {/* 订单表格 */}\n        <Table\n          columns={columns}\n          dataSource={filteredOrders}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\n          }}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: setSelectedRowKeys,\n          }}\n        />\n      </Card>\n\n      {/* 订单详情弹窗 */}\n      <Modal\n        title=\"订单详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={600}\n      >\n        {selectedOrder && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"订单号\" span={2}>\n              {selectedOrder.orderNo}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"客户姓名\">\n              {selectedOrder.customerName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"联系电话\">\n              {selectedOrder.customerPhone}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"产品名称\" span={2}>\n              {selectedOrder.productName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"运营商\">\n              <Tag color=\"blue\">{selectedOrder.operator}</Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"订单金额\">\n              <Text strong style={{ color: '#f5222d' }}>\n                ¥{selectedOrder.amount.toFixed(2)}\n              </Text>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"订单状态\">\n              <Tag color={getStatusColor(selectedOrder.status)}>\n                {getStatusText(selectedOrder.status)}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"支付状态\">\n              <Tag color={getPaymentStatusColor(selectedOrder.paymentStatus)}>\n                {getPaymentStatusText(selectedOrder.paymentStatus)}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"创建时间\">\n              {selectedOrder.createdAt}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"更新时间\">\n              {selectedOrder.updatedAt}\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,YAAY,QACP,MAAM;AACb,SACEC,cAAc,EACdC,WAAW,EACXC,YAAY,EAEZC,cAAc,EACdC,cAAc,EAEdC,aAAa,QACR,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGtB,UAAU;AAClC,MAAM;EAAEuB;AAAY,CAAC,GAAGlB,UAAU;AAclC;AACA,MAAMmB,UAAmB,GAAG,CAC1B;EACEC,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,WAAW;EACnBC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,YAAY;EACpBC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,SAAS;EACjBC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,EACD;EACER,EAAE,EAAE,CAAC;EACLC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,aAAa;EAC5BC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,SAAS;EACjBC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE;AACb,CAAC,CACF;AAED,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAGlC,IAAI,CAACmC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAU6B,UAAU,CAAC;EACzD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAU6B,UAAU,CAAC;EACzE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAMuD,QAAQ,GAAGhC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMiC,cAAc,GAAIpB,MAAc,IAAK;IACzC,MAAMqB,MAAM,GAAG;MACbC,OAAO,EAAE,QAAQ;MACjBC,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAACrB,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;;EAID;EACA,MAAM2B,aAAa,GAAI3B,MAAc,IAAK;IACxC,MAAM4B,KAAK,GAAG;MACZN,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAAC5B,MAAM,CAAuB,IAAIA,MAAM;EACtD,CAAC;;EAED;EACA,MAAM6B,oBAAoB,GAAI7B,MAAc,IAAK;IAC/C,MAAM4B,KAAK,GAAG;MACZE,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOJ,KAAK,CAAC5B,MAAM,CAAuB,IAAIA,MAAM;EACtD,CAAC;;EAED;EACA,MAAMiC,KAAK,GAAG;IACZC,KAAK,EAAEzB,cAAc,CAAC0B,MAAM;IAC5Bb,OAAO,EAAEb,cAAc,CAAC2B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAK,SAAS,CAAC,CAACmC,MAAM;IAC1EZ,UAAU,EAAEd,cAAc,CAAC2B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAK,YAAY,CAAC,CAACmC,MAAM;IAChFV,SAAS,EAAEhB,cAAc,CAAC2B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAK,WAAW,CAAC,CAACmC,MAAM;IAC9EG,WAAW,EAAE7B,cAAc,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAEH,KAAK,KAAKG,GAAG,GAAGH,KAAK,CAACI,MAAM,EAAE,CAAC;EAC1E,CAAC;;EAED;EACA,MAAMC,OAA2B,GAAG,CAClC;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnB3D,OAAA,CAACE,IAAI;MAAC0D,IAAI;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EACpCJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEV,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChBrE,OAAA;MAAA+D,QAAA,gBACE/D,OAAA;QAAK6D,KAAK,EAAE;UAAES,UAAU,EAAE;QAAI,CAAE;QAAAP,QAAA,EAAEM,MAAM,CAAC9D;MAAY;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5DnE,OAAA;QAAK6D,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAES,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,EAC7CM,MAAM,CAAC7D;MAAa;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBgB,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnB3D,OAAA,CAACnB,GAAG;MAAC0F,KAAK,EAAC,MAAM;MAAAR,QAAA,EAAEJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEjC,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGN,MAAc,iBACrBpD,OAAA,CAACE,IAAI;MAACuE,MAAM;MAACZ,KAAK,EAAE;QAAEU,KAAK,EAAE;MAAU,CAAE;MAAAR,QAAA,GAAC,MACvC,EAACX,MAAM,CAACsB,OAAO,CAAC,CAAC,CAAC;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAEV,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG/C,MAAc,iBACrBX,OAAA,CAACnB,GAAG;MAAC0F,KAAK,EAAExC,cAAc,CAACpB,MAAM,CAAE;MAAAoD,QAAA,EAChCzB,aAAa,CAAC3B,MAAM;IAAC;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG/C,MAAc,iBACrBX,OAAA,CAACnB,GAAG;MAAC0F,KAAK,EAAEI,qBAAqB,CAAChE,MAAM,CAAE;MAAAoD,QAAA,EACvCvB,oBAAoB,CAAC7B,MAAM;IAAC;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnB3D,OAAA;MAAK6D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,EAC9BJ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACU,CAAC,EAAEC,MAAM,kBAChBrE,OAAA,CAACrB,KAAK;MAACiG,IAAI,EAAC,OAAO;MAAAb,QAAA,gBACjB/D,OAAA,CAACtB,MAAM;QACLmG,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE9E,OAAA,CAACP,WAAW;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBY,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACX,MAAM,CAAE;QAAAN,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnE,OAAA,CAACtB,MAAM;QACLmG,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE9E,OAAA,CAACN,YAAY;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBY,OAAO,EAAEA,CAAA,KAAME,eAAe,CAACZ,MAAM,CAAChE,EAAE,CAAE;QAAA0D,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMe,YAAY,GAAIC,MAAW,IAAK;IACpC5D,UAAU,CAAC,IAAI,CAAC;IAEhB6D,UAAU,CAAC,MAAM;MACf,IAAIC,QAAQ,GAAG,CAAC,GAAGnE,MAAM,CAAC;MAE1B,IAAIiE,MAAM,CAAC7E,OAAO,EAAE;QAClB+E,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAAC1C,OAAO,CAACgF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAAC7E,OAAO,CAACgF,WAAW,CAAC,CAAC,CACnE,CAAC;MACH;MAEA,IAAIH,MAAM,CAAC5E,YAAY,EAAE;QACvB8E,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAACzC,YAAY,CAAC+E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAAC5E,YAAY,CAAC+E,WAAW,CAAC,CAAC,CAC7E,CAAC;MACH;MAEA,IAAIH,MAAM,CAACzE,QAAQ,EAAE;QACnB2E,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACtC,QAAQ,KAAKyE,MAAM,CAACzE,QAAQ,CAAC;MACzE;MAEA,IAAIyE,MAAM,CAACxE,MAAM,EAAE;QACjB0E,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACrC,MAAM,KAAKwE,MAAM,CAACxE,MAAM,CAAC;MACrE;MAEAU,iBAAiB,CAACgE,QAAQ,CAAC;MAC3B9D,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMiE,WAAW,GAAGA,CAAA,KAAM;IACxBxE,IAAI,CAACyE,WAAW,CAAC,CAAC;IAClBpE,iBAAiB,CAACH,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMwE,aAAa,GAAGA,CAAA,KAAM;IAC1BnE,UAAU,CAAC,IAAI,CAAC;IAChB6D,UAAU,CAAC,MAAM;MACfjE,SAAS,CAACf,UAAU,CAAC;MACrBiB,iBAAiB,CAACjB,UAAU,CAAC;MAC7BmB,UAAU,CAAC,KAAK,CAAC;MACjBlC,OAAO,CAACsG,OAAO,CAAC,OAAO,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMX,eAAe,GAAIhC,KAAY,IAAK;IACxCnB,gBAAgB,CAACmB,KAAK,CAAC;IACvBrB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMsD,eAAe,GAAI5E,EAAU,IAAK;IACtChB,OAAO,CAACuG,IAAI,CAAC,QAAQvF,EAAE,WAAW,CAAC;EACrC,CAAC;EAED,MAAMwF,YAAY,GAAGA,CAAA,KAAM;IACzBxG,OAAO,CAACuG,IAAI,CAAC,YAAY,CAAC;EAC5B,CAAC;EAED,oBACE5F,OAAA;IAAA+D,QAAA,gBACE/D,OAAA;MAAK6D,KAAK,EAAE;QAAEiC,YAAY,EAAE;MAAO,CAAE;MAAA/B,QAAA,gBACnC/D,OAAA,CAACC,KAAK;QAAC8F,KAAK,EAAE,CAAE;QAAClC,KAAK,EAAE;UAAEmC,MAAM,EAAE,CAAC;UAAEzB,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRnE,OAAA,CAACE,IAAI;QAAC2E,IAAI,EAAC,WAAW;QAAAd,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNnE,OAAA,CAACd,GAAG;MAAC+G,MAAM,EAAE,EAAG;MAACpC,KAAK,EAAE;QAAEiC,YAAY,EAAE;MAAO,CAAE;MAAA/B,QAAA,gBAC/C/D,OAAA,CAACb,GAAG;QAAC+G,IAAI,EAAE,CAAE;QAAAnC,QAAA,eACX/D,OAAA,CAACxB,IAAI;UAAAuF,QAAA,eACH/D,OAAA,CAACZ,SAAS;YACRkE,KAAK,EAAC,0BAAM;YACZ6C,KAAK,EAAEvD,KAAK,CAACC,KAAM;YACnBuD,UAAU,EAAE;cAAE7B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAACb,GAAG;QAAC+G,IAAI,EAAE,CAAE;QAAAnC,QAAA,eACX/D,OAAA,CAACxB,IAAI;UAAAuF,QAAA,eACH/D,OAAA,CAACZ,SAAS;YACRkE,KAAK,EAAC,oBAAK;YACX6C,KAAK,EAAEvD,KAAK,CAACX,OAAQ;YACrBmE,UAAU,EAAE;cAAE7B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAACb,GAAG;QAAC+G,IAAI,EAAE,CAAE;QAAAnC,QAAA,eACX/D,OAAA,CAACxB,IAAI;UAAAuF,QAAA,eACH/D,OAAA,CAACZ,SAAS;YACRkE,KAAK,EAAC,oBAAK;YACX6C,KAAK,EAAEvD,KAAK,CAACR,SAAU;YACvBgE,UAAU,EAAE;cAAE7B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAACb,GAAG;QAAC+G,IAAI,EAAE,CAAE;QAAAnC,QAAA,eACX/D,OAAA,CAACxB,IAAI;UAAAuF,QAAA,eACH/D,OAAA,CAACZ,SAAS;YACRkE,KAAK,EAAC,oBAAK;YACX6C,KAAK,EAAEvD,KAAK,CAACK,WAAY;YACzBoD,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC,MAAG;YACVF,UAAU,EAAE;cAAE7B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnE,OAAA,CAACxB,IAAI;MAAAuF,QAAA,gBAEH/D,OAAA,CAAClB,IAAI;QACHkC,IAAI,EAAEA,IAAK;QACXuF,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEtB,YAAa;QACvBrB,KAAK,EAAE;UAAEiC,YAAY,EAAE;QAAG,CAAE;QAAA/B,QAAA,gBAE5B/D,OAAA,CAAClB,IAAI,CAAC2H,IAAI;UAACC,IAAI,EAAC,SAAS;UAACC,KAAK,EAAC,oBAAK;UAAA5C,QAAA,eACnC/D,OAAA,CAACjB,KAAK;YAAC6H,WAAW,EAAC,sCAAQ;YAAC/C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACZnE,OAAA,CAAClB,IAAI,CAAC2H,IAAI;UAACC,IAAI,EAAC,cAAc;UAACC,KAAK,EAAC,0BAAM;UAAA5C,QAAA,eACzC/D,OAAA,CAACjB,KAAK;YAAC6H,WAAW,EAAC,4CAAS;YAAC/C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACZnE,OAAA,CAAClB,IAAI,CAAC2H,IAAI;UAACC,IAAI,EAAC,UAAU;UAACC,KAAK,EAAC,oBAAK;UAAA5C,QAAA,eACpC/D,OAAA,CAAChB,MAAM;YAAC4H,WAAW,EAAC,sCAAQ;YAAC/C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACjD/D,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,0BAAM;cAAApC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDnE,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,0BAAM;cAAApC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDnE,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,0BAAM;cAAApC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChDnE,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,0BAAM;cAAApC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZnE,OAAA,CAAClB,IAAI,CAAC2H,IAAI;UAACC,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC,0BAAM;UAAA5C,QAAA,eACnC/D,OAAA,CAAChB,MAAM;YAAC4H,WAAW,EAAC,gCAAO;YAAC/C,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAI,CAAE;YAAAM,QAAA,gBAChD/D,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,SAAS;cAAApC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDnE,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,YAAY;cAAApC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACrDnE,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,SAAS;cAAApC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDnE,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,WAAW;cAAApC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACpDnE,OAAA,CAAChB,MAAM,CAAC6H,MAAM;cAACV,KAAK,EAAC,WAAW;cAAApC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZnE,OAAA,CAAClB,IAAI,CAAC2H,IAAI;UAAA1C,QAAA,eACR/D,OAAA,CAACrB,KAAK;YAAAoF,QAAA,gBACJ/D,OAAA,CAACtB,MAAM;cAACmG,IAAI,EAAC,SAAS;cAACiC,QAAQ,EAAC,QAAQ;cAAChC,IAAI,eAAE9E,OAAA,CAACR,cAAc;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAAC;YAEnE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnE,OAAA,CAACtB,MAAM;cAACqG,OAAO,EAAES,WAAY;cAACV,IAAI,eAAE9E,OAAA,CAACH,aAAa;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGPnE,OAAA;QAAK6D,KAAK,EAAE;UACVkD,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBnB,YAAY,EAAE;QAChB,CAAE;QAAA/B,QAAA,gBACA/D,OAAA;UAAA+D,QAAA,eACE/D,OAAA,CAACE,IAAI;YAACuE,MAAM;YAAAV,QAAA,GAAC,SACT,EAAC3C,cAAc,CAAC0B,MAAM,EAAC,qBAC3B;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNnE,OAAA,CAACrB,KAAK;UAAAoF,QAAA,gBACJ/D,OAAA,CAACtB,MAAM;YAACqG,OAAO,EAAEW,aAAc;YAACZ,IAAI,eAAE9E,OAAA,CAACJ,cAAc;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnE,OAAA,CAACtB,MAAM;YAACqG,OAAO,EAAEc,YAAa;YAACf,IAAI,eAAE9E,OAAA,CAACL,cAAc;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNnE,OAAA,CAACvB,KAAK;QACJ4E,OAAO,EAAEA,OAAQ;QACjB6D,UAAU,EAAE9F,cAAe;QAC3B+F,MAAM,EAAC,IAAI;QACX7F,OAAO,EAAEA,OAAQ;QACjB8F,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC5E,KAAK,EAAE6E,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,SAAS7E,KAAK;QAC3C,CAAE;QACF8E,YAAY,EAAE;UACZnG,eAAe;UACfoG,QAAQ,EAAEnG;QACZ;MAAE;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnE,OAAA,CAACV,KAAK;MACJgE,KAAK,EAAC,0BAAM;MACZuE,IAAI,EAAEnG,kBAAmB;MACzBoG,QAAQ,EAAEA,CAAA,KAAMnG,qBAAqB,CAAC,KAAK,CAAE;MAC7CoG,MAAM,EAAE,cACN/H,OAAA,CAACtB,MAAM;QAAaqG,OAAO,EAAEA,CAAA,KAAMpD,qBAAqB,CAAC,KAAK,CAAE;QAAAoC,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFV,KAAK,EAAE,GAAI;MAAAM,QAAA,EAEVnC,aAAa,iBACZ5B,OAAA,CAACT,YAAY;QAACyI,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAAlE,QAAA,gBAC/B/D,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,oBAAK;UAACT,IAAI,EAAE,CAAE;UAAAnC,QAAA,EACpCnC,aAAa,CAACtB;QAAO;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA5C,QAAA,EAC5BnC,aAAa,CAACrB;QAAY;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA5C,QAAA,EAC5BnC,aAAa,CAACpB;QAAa;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAACT,IAAI,EAAE,CAAE;UAAAnC,QAAA,EACrCnC,aAAa,CAACnB;QAAW;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,oBAAK;UAAA5C,QAAA,eAC5B/D,OAAA,CAACnB,GAAG;YAAC0F,KAAK,EAAC,MAAM;YAAAR,QAAA,EAAEnC,aAAa,CAAClB;UAAQ;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA5C,QAAA,eAC7B/D,OAAA,CAACE,IAAI;YAACuE,MAAM;YAACZ,KAAK,EAAE;cAAEU,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,GAAC,MACvC,EAACnC,aAAa,CAACwB,MAAM,CAACsB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA5C,QAAA,eAC7B/D,OAAA,CAACnB,GAAG;YAAC0F,KAAK,EAAExC,cAAc,CAACH,aAAa,CAACjB,MAAM,CAAE;YAAAoD,QAAA,EAC9CzB,aAAa,CAACV,aAAa,CAACjB,MAAM;UAAC;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA5C,QAAA,eAC7B/D,OAAA,CAACnB,GAAG;YAAC0F,KAAK,EAAEI,qBAAqB,CAAC/C,aAAa,CAACsG,aAAa,CAAE;YAAAnE,QAAA,EAC5DvB,oBAAoB,CAACZ,aAAa,CAACsG,aAAa;UAAC;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA5C,QAAA,EAC5BnC,aAAa,CAAChB;QAAS;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACpBnE,OAAA,CAACT,YAAY,CAACkH,IAAI;UAACE,KAAK,EAAC,0BAAM;UAAA5C,QAAA,EAC5BnC,aAAa,CAACf;QAAS;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpD,EAAA,CApaID,SAAmB;EAAA,QACRhC,IAAI,CAACmC,OAAO,EAOVnB,WAAW;AAAA;AAAAqI,EAAA,GARxBrH,SAAmB;AAsazB,eAAeA,SAAS;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genFocusOutline, genFocusStyle, resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genMotionStyle from './motion';\nconst genCardStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardBg,\n    cardGutter,\n    colorBorderSecondary,\n    itemSelectedColor\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-card\")]: {\n      [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n        [\"\".concat(componentCls, \"-tab\")]: {\n          margin: 0,\n          padding: tabsCardPadding,\n          background: cardBg,\n          border: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(colorBorderSecondary),\n          transition: \"all \".concat(token.motionDurationSlow, \" \").concat(token.motionEaseInOut)\n        },\n        [\"\".concat(componentCls, \"-tab-active\")]: {\n          color: itemSelectedColor,\n          background: token.colorBgContainer\n        },\n        [\"\".concat(componentCls, \"-tab-focus:has(\").concat(componentCls, \"-tab-btn:focus-visible)\")]: genFocusOutline(token, -3),\n        [\"& \".concat(componentCls, \"-tab\").concat(componentCls, \"-tab-focus \").concat(componentCls, \"-tab-btn:focus-visible\")]: {\n          outline: 'none'\n        },\n        [\"\".concat(componentCls, \"-ink-bar\")]: {\n          visibility: 'hidden'\n        }\n      },\n      // ========================== Top & Bottom ==========================\n      [\"&\".concat(componentCls, \"-top, &\").concat(componentCls, \"-bottom\")]: {\n        [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab + \").concat(componentCls, \"-tab\")]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(cardGutter)\n            }\n          }\n        }\n      },\n      [\"&\".concat(componentCls, \"-top\")]: {\n        [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab\")]: {\n            borderRadius: \"\".concat(unit(token.borderRadiusLG), \" \").concat(unit(token.borderRadiusLG), \" 0 0\")\n          },\n          [\"\".concat(componentCls, \"-tab-active\")]: {\n            borderBottomColor: token.colorBgContainer\n          }\n        }\n      },\n      [\"&\".concat(componentCls, \"-bottom\")]: {\n        [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab\")]: {\n            borderRadius: \"0 0 \".concat(unit(token.borderRadiusLG), \" \").concat(unit(token.borderRadiusLG))\n          },\n          [\"\".concat(componentCls, \"-tab-active\")]: {\n            borderTopColor: token.colorBgContainer\n          }\n        }\n      },\n      // ========================== Left & Right ==========================\n      [\"&\".concat(componentCls, \"-left, &\").concat(componentCls, \"-right\")]: {\n        [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab + \").concat(componentCls, \"-tab\")]: {\n            marginTop: unit(cardGutter)\n          }\n        }\n      },\n      [\"&\".concat(componentCls, \"-left\")]: {\n        [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab\")]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: \"\".concat(unit(token.borderRadiusLG), \" 0 0 \").concat(unit(token.borderRadiusLG))\n            }\n          },\n          [\"\".concat(componentCls, \"-tab-active\")]: {\n            borderRightColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      },\n      [\"&\".concat(componentCls, \"-right\")]: {\n        [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab\")]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: \"0 \".concat(unit(token.borderRadiusLG), \" \").concat(unit(token.borderRadiusLG), \" 0\")\n            }\n          },\n          [\"\".concat(componentCls, \"-tab-active\")]: {\n            borderLeftColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genDropdownStyle = token => {\n  const {\n    componentCls,\n    itemHoverColor,\n    dropdownEdgeChildVerticalPadding\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-dropdown\")]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: -9999,\n      left: {\n        _skip_check_: true,\n        value: -9999\n      },\n      zIndex: token.zIndexPopup,\n      display: 'block',\n      '&-hidden': {\n        display: 'none'\n      },\n      [\"\".concat(componentCls, \"-dropdown-menu\")]: {\n        maxHeight: token.tabsDropdownHeight,\n        margin: 0,\n        padding: \"\".concat(unit(dropdownEdgeChildVerticalPadding), \" 0\"),\n        overflowX: 'hidden',\n        overflowY: 'auto',\n        textAlign: {\n          _skip_check_: true,\n          value: 'left'\n        },\n        listStyleType: 'none',\n        backgroundColor: token.colorBgContainer,\n        backgroundClip: 'padding-box',\n        borderRadius: token.borderRadiusLG,\n        outline: 'none',\n        boxShadow: token.boxShadowSecondary,\n        '&-item': Object.assign(Object.assign({}, textEllipsis), {\n          display: 'flex',\n          alignItems: 'center',\n          minWidth: token.tabsDropdownWidth,\n          margin: 0,\n          padding: \"\".concat(unit(token.paddingXXS), \" \").concat(unit(token.paddingSM)),\n          color: token.colorText,\n          fontWeight: 'normal',\n          fontSize: token.fontSize,\n          lineHeight: token.lineHeight,\n          cursor: 'pointer',\n          transition: \"all \".concat(token.motionDurationSlow),\n          '> span': {\n            flex: 1,\n            whiteSpace: 'nowrap'\n          },\n          '&-remove': {\n            flex: 'none',\n            marginLeft: {\n              _skip_check_: true,\n              value: token.marginSM\n            },\n            color: token.colorIcon,\n            fontSize: token.fontSizeSM,\n            background: 'transparent',\n            border: 0,\n            cursor: 'pointer',\n            '&:hover': {\n              color: itemHoverColor\n            }\n          },\n          '&:hover': {\n            background: token.controlItemBgHover\n          },\n          '&-disabled': {\n            '&, &:hover': {\n              color: token.colorTextDisabled,\n              background: 'transparent',\n              cursor: 'not-allowed'\n            }\n          }\n        })\n      }\n    })\n  };\n};\nconst genPositionStyle = token => {\n  const {\n    componentCls,\n    margin,\n    colorBorderSecondary,\n    horizontalMargin,\n    verticalItemPadding,\n    verticalItemMargin,\n    calc\n  } = token;\n  return {\n    // ========================== Top & Bottom ==========================\n    [\"\".concat(componentCls, \"-top, \").concat(componentCls, \"-bottom\")]: {\n      flexDirection: 'column',\n      [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n        margin: horizontalMargin,\n        '&::before': {\n          position: 'absolute',\n          right: {\n            _skip_check_: true,\n            value: 0\n          },\n          left: {\n            _skip_check_: true,\n            value: 0\n          },\n          borderBottom: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(colorBorderSecondary),\n          content: \"''\"\n        },\n        [\"\".concat(componentCls, \"-ink-bar\")]: {\n          height: token.lineWidthBold,\n          '&-animated': {\n            transition: \"width \".concat(token.motionDurationSlow, \", left \").concat(token.motionDurationSlow, \",\\n            right \").concat(token.motionDurationSlow)\n          }\n        },\n        [\"\".concat(componentCls, \"-nav-wrap\")]: {\n          '&::before, &::after': {\n            top: 0,\n            bottom: 0,\n            width: token.controlHeight\n          },\n          '&::before': {\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowLeft\n          },\n          '&::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowRight\n          },\n          [\"&\".concat(componentCls, \"-nav-wrap-ping-left::before\")]: {\n            opacity: 1\n          },\n          [\"&\".concat(componentCls, \"-nav-wrap-ping-right::after\")]: {\n            opacity: 1\n          }\n        }\n      }\n    },\n    [\"\".concat(componentCls, \"-top\")]: {\n      [\"> \".concat(componentCls, \"-nav,\\n        > div > \").concat(componentCls, \"-nav\")]: {\n        '&::before': {\n          bottom: 0\n        },\n        [\"\".concat(componentCls, \"-ink-bar\")]: {\n          bottom: 0\n        }\n      }\n    },\n    [\"\".concat(componentCls, \"-bottom\")]: {\n      [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n        order: 1,\n        marginTop: margin,\n        marginBottom: 0,\n        '&::before': {\n          top: 0\n        },\n        [\"\".concat(componentCls, \"-ink-bar\")]: {\n          top: 0\n        }\n      },\n      [\"> \".concat(componentCls, \"-content-holder, > div > \").concat(componentCls, \"-content-holder\")]: {\n        order: 0\n      }\n    },\n    // ========================== Left & Right ==========================\n    [\"\".concat(componentCls, \"-left, \").concat(componentCls, \"-right\")]: {\n      [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n        flexDirection: 'column',\n        minWidth: calc(token.controlHeight).mul(1.25).equal(),\n        // >>>>>>>>>>> Tab\n        [\"\".concat(componentCls, \"-tab\")]: {\n          padding: verticalItemPadding,\n          textAlign: 'center'\n        },\n        [\"\".concat(componentCls, \"-tab + \").concat(componentCls, \"-tab\")]: {\n          margin: verticalItemMargin\n        },\n        // >>>>>>>>>>> Nav\n        [\"\".concat(componentCls, \"-nav-wrap\")]: {\n          flexDirection: 'column',\n          '&::before, &::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.controlHeight\n          },\n          '&::before': {\n            top: 0,\n            boxShadow: token.boxShadowTabsOverflowTop\n          },\n          '&::after': {\n            bottom: 0,\n            boxShadow: token.boxShadowTabsOverflowBottom\n          },\n          [\"&\".concat(componentCls, \"-nav-wrap-ping-top::before\")]: {\n            opacity: 1\n          },\n          [\"&\".concat(componentCls, \"-nav-wrap-ping-bottom::after\")]: {\n            opacity: 1\n          }\n        },\n        // >>>>>>>>>>> Ink Bar\n        [\"\".concat(componentCls, \"-ink-bar\")]: {\n          width: token.lineWidthBold,\n          '&-animated': {\n            transition: \"height \".concat(token.motionDurationSlow, \", top \").concat(token.motionDurationSlow)\n          }\n        },\n        [\"\".concat(componentCls, \"-nav-list, \").concat(componentCls, \"-nav-operations\")]: {\n          flex: '1 0 auto',\n          // fix safari scroll problem\n          flexDirection: 'column'\n        }\n      }\n    },\n    [\"\".concat(componentCls, \"-left\")]: {\n      [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n        [\"\".concat(componentCls, \"-ink-bar\")]: {\n          right: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [\"> \".concat(componentCls, \"-content-holder, > div > \").concat(componentCls, \"-content-holder\")]: {\n        marginLeft: {\n          _skip_check_: true,\n          value: unit(calc(token.lineWidth).mul(-1).equal())\n        },\n        borderLeft: {\n          _skip_check_: true,\n          value: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorBorder)\n        },\n        [\"> \".concat(componentCls, \"-content > \").concat(componentCls, \"-tabpane\")]: {\n          paddingLeft: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    },\n    [\"\".concat(componentCls, \"-right\")]: {\n      [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n        order: 1,\n        [\"\".concat(componentCls, \"-ink-bar\")]: {\n          left: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [\"> \".concat(componentCls, \"-content-holder, > div > \").concat(componentCls, \"-content-holder\")]: {\n        order: 0,\n        marginRight: {\n          _skip_check_: true,\n          value: calc(token.lineWidth).mul(-1).equal()\n        },\n        borderRight: {\n          _skip_check_: true,\n          value: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorBorder)\n        },\n        [\"> \".concat(componentCls, \"-content > \").concat(componentCls, \"-tabpane\")]: {\n          paddingRight: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    cardPaddingSM,\n    cardPaddingLG,\n    cardHeightSM,\n    cardHeightLG,\n    horizontalItemPaddingSM,\n    horizontalItemPaddingLG\n  } = token;\n  return {\n    // >>>>> shared\n    [componentCls]: {\n      '&-small': {\n        [\"> \".concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab\")]: {\n            padding: horizontalItemPaddingSM,\n            fontSize: token.titleFontSizeSM\n          }\n        }\n      },\n      '&-large': {\n        [\"> \".concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab\")]: {\n            padding: horizontalItemPaddingLG,\n            fontSize: token.titleFontSizeLG,\n            lineHeight: token.lineHeightLG\n          }\n        }\n      }\n    },\n    // >>>>> card\n    [\"\".concat(componentCls, \"-card\")]: {\n      // Small\n      [\"&\".concat(componentCls, \"-small\")]: {\n        [\"> \".concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab\")]: {\n            padding: cardPaddingSM\n          },\n          [\"\".concat(componentCls, \"-nav-add\")]: {\n            minWidth: cardHeightSM,\n            minHeight: cardHeightSM\n          }\n        },\n        [\"&\".concat(componentCls, \"-bottom\")]: {\n          [\"> \".concat(componentCls, \"-nav \").concat(componentCls, \"-tab\")]: {\n            borderRadius: \"0 0 \".concat(unit(token.borderRadius), \" \").concat(unit(token.borderRadius))\n          }\n        },\n        [\"&\".concat(componentCls, \"-top\")]: {\n          [\"> \".concat(componentCls, \"-nav \").concat(componentCls, \"-tab\")]: {\n            borderRadius: \"\".concat(unit(token.borderRadius), \" \").concat(unit(token.borderRadius), \" 0 0\")\n          }\n        },\n        [\"&\".concat(componentCls, \"-right\")]: {\n          [\"> \".concat(componentCls, \"-nav \").concat(componentCls, \"-tab\")]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: \"0 \".concat(unit(token.borderRadius), \" \").concat(unit(token.borderRadius), \" 0\")\n            }\n          }\n        },\n        [\"&\".concat(componentCls, \"-left\")]: {\n          [\"> \".concat(componentCls, \"-nav \").concat(componentCls, \"-tab\")]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: \"\".concat(unit(token.borderRadius), \" 0 0 \").concat(unit(token.borderRadius))\n            }\n          }\n        }\n      },\n      // Large\n      [\"&\".concat(componentCls, \"-large\")]: {\n        [\"> \".concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab\")]: {\n            padding: cardPaddingLG\n          },\n          [\"\".concat(componentCls, \"-nav-add\")]: {\n            minWidth: cardHeightLG,\n            minHeight: cardHeightLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genTabStyle = token => {\n  const {\n    componentCls,\n    itemActiveColor,\n    itemHoverColor,\n    iconCls,\n    tabsHorizontalItemMargin,\n    horizontalItemPadding,\n    itemSelectedColor,\n    itemColor\n  } = token;\n  const tabCls = \"\".concat(componentCls, \"-tab\");\n  return {\n    [tabCls]: {\n      position: 'relative',\n      WebkitTouchCallout: 'none',\n      WebkitTapHighlightColor: 'transparent',\n      display: 'inline-flex',\n      alignItems: 'center',\n      padding: horizontalItemPadding,\n      fontSize: token.titleFontSize,\n      background: 'transparent',\n      border: 0,\n      outline: 'none',\n      cursor: 'pointer',\n      color: itemColor,\n      '&-btn, &-remove': {\n        '&:focus:not(:focus-visible), &:active': {\n          color: itemActiveColor\n        }\n      },\n      '&-btn': {\n        outline: 'none',\n        transition: \"all \".concat(token.motionDurationSlow),\n        [\"\".concat(tabCls, \"-icon:not(:last-child)\")]: {\n          marginInlineEnd: token.marginSM\n        }\n      },\n      '&-remove': Object.assign({\n        flex: 'none',\n        marginRight: {\n          _skip_check_: true,\n          value: token.calc(token.marginXXS).mul(-1).equal()\n        },\n        marginLeft: {\n          _skip_check_: true,\n          value: token.marginXS\n        },\n        color: token.colorIcon,\n        fontSize: token.fontSizeSM,\n        background: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        transition: \"all \".concat(token.motionDurationSlow),\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      }, genFocusStyle(token)),\n      '&:hover': {\n        color: itemHoverColor\n      },\n      [\"&\".concat(tabCls, \"-active \").concat(tabCls, \"-btn\")]: {\n        color: itemSelectedColor,\n        textShadow: token.tabsActiveTextShadow\n      },\n      [\"&\".concat(tabCls, \"-focus \").concat(tabCls, \"-btn:focus-visible\")]: genFocusOutline(token),\n      [\"&\".concat(tabCls, \"-disabled\")]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed'\n      },\n      [\"&\".concat(tabCls, \"-disabled \").concat(tabCls, \"-btn, &\").concat(tabCls, \"-disabled \").concat(componentCls, \"-remove\")]: {\n        '&:focus, &:active': {\n          color: token.colorTextDisabled\n        }\n      },\n      [\"& \".concat(tabCls, \"-remove \").concat(iconCls)]: {\n        margin: 0\n      },\n      [\"\".concat(iconCls, \":not(:last-child)\")]: {\n        marginRight: {\n          _skip_check_: true,\n          value: token.marginSM\n        }\n      }\n    },\n    [\"\".concat(tabCls, \" + \").concat(tabCls)]: {\n      margin: {\n        _skip_check_: true,\n        value: tabsHorizontalItemMargin\n      }\n    }\n  };\n};\nconst genRtlStyle = token => {\n  const {\n    componentCls,\n    tabsHorizontalItemMarginRTL,\n    iconCls,\n    cardGutter,\n    calc\n  } = token;\n  const rtlCls = \"\".concat(componentCls, \"-rtl\");\n  return {\n    [rtlCls]: {\n      direction: 'rtl',\n      [\"\".concat(componentCls, \"-nav\")]: {\n        [\"\".concat(componentCls, \"-tab\")]: {\n          margin: {\n            _skip_check_: true,\n            value: tabsHorizontalItemMarginRTL\n          },\n          [\"\".concat(componentCls, \"-tab:last-of-type\")]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          },\n          [iconCls]: {\n            marginRight: {\n              _skip_check_: true,\n              value: 0\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(token.marginSM)\n            }\n          },\n          [\"\".concat(componentCls, \"-tab-remove\")]: {\n            marginRight: {\n              _skip_check_: true,\n              value: unit(token.marginXS)\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(calc(token.marginXXS).mul(-1).equal())\n            },\n            [iconCls]: {\n              margin: 0\n            }\n          }\n        }\n      },\n      [\"&\".concat(componentCls, \"-left\")]: {\n        [\"> \".concat(componentCls, \"-nav\")]: {\n          order: 1\n        },\n        [\"> \".concat(componentCls, \"-content-holder\")]: {\n          order: 0\n        }\n      },\n      [\"&\".concat(componentCls, \"-right\")]: {\n        [\"> \".concat(componentCls, \"-nav\")]: {\n          order: 0\n        },\n        [\"> \".concat(componentCls, \"-content-holder\")]: {\n          order: 1\n        }\n      },\n      // ====================== Card ======================\n      [\"&\".concat(componentCls, \"-card\").concat(componentCls, \"-top, &\").concat(componentCls, \"-card\").concat(componentCls, \"-bottom\")]: {\n        [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n          [\"\".concat(componentCls, \"-tab + \").concat(componentCls, \"-tab\")]: {\n            marginRight: {\n              _skip_check_: true,\n              value: cardGutter\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          }\n        }\n      }\n    },\n    [\"\".concat(componentCls, \"-dropdown-rtl\")]: {\n      direction: 'rtl'\n    },\n    [\"\".concat(componentCls, \"-menu-item\")]: {\n      [\"\".concat(componentCls, \"-dropdown-rtl\")]: {\n        textAlign: {\n          _skip_check_: true,\n          value: 'right'\n        }\n      }\n    }\n  };\n};\nconst genTabsStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardHeight,\n    cardGutter,\n    itemHoverColor,\n    itemActiveColor,\n    colorBorderSecondary\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      // ========================== Navigation ==========================\n      [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n        position: 'relative',\n        display: 'flex',\n        flex: 'none',\n        alignItems: 'center',\n        [\"\".concat(componentCls, \"-nav-wrap\")]: {\n          position: 'relative',\n          display: 'flex',\n          flex: 'auto',\n          alignSelf: 'stretch',\n          overflow: 'hidden',\n          whiteSpace: 'nowrap',\n          transform: 'translate(0)',\n          // Fix chrome render bug\n          // >>>>> Ping shadow\n          '&::before, &::after': {\n            position: 'absolute',\n            zIndex: 1,\n            opacity: 0,\n            transition: \"opacity \".concat(token.motionDurationSlow),\n            content: \"''\",\n            pointerEvents: 'none'\n          }\n        },\n        [\"\".concat(componentCls, \"-nav-list\")]: {\n          position: 'relative',\n          display: 'flex',\n          transition: \"opacity \".concat(token.motionDurationSlow)\n        },\n        // >>>>>>>> Operations\n        [\"\".concat(componentCls, \"-nav-operations\")]: {\n          display: 'flex',\n          alignSelf: 'stretch'\n        },\n        [\"\".concat(componentCls, \"-nav-operations-hidden\")]: {\n          position: 'absolute',\n          visibility: 'hidden',\n          pointerEvents: 'none'\n        },\n        [\"\".concat(componentCls, \"-nav-more\")]: {\n          position: 'relative',\n          padding: tabsCardPadding,\n          background: 'transparent',\n          border: 0,\n          color: token.colorText,\n          '&::after': {\n            position: 'absolute',\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            bottom: 0,\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.calc(token.controlHeightLG).div(8).equal(),\n            transform: 'translateY(100%)',\n            content: \"''\"\n          }\n        },\n        [\"\".concat(componentCls, \"-nav-add\")]: Object.assign({\n          minWidth: cardHeight,\n          minHeight: cardHeight,\n          marginLeft: {\n            _skip_check_: true,\n            value: cardGutter\n          },\n          background: 'transparent',\n          border: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(colorBorderSecondary),\n          borderRadius: \"\".concat(unit(token.borderRadiusLG), \" \").concat(unit(token.borderRadiusLG), \" 0 0\"),\n          outline: 'none',\n          cursor: 'pointer',\n          color: token.colorText,\n          transition: \"all \".concat(token.motionDurationSlow, \" \").concat(token.motionEaseInOut),\n          '&:hover': {\n            color: itemHoverColor\n          },\n          '&:active, &:focus:not(:focus-visible)': {\n            color: itemActiveColor\n          }\n        }, genFocusStyle(token, -3))\n      },\n      [\"\".concat(componentCls, \"-extra-content\")]: {\n        flex: 'none'\n      },\n      // ============================ InkBar ============================\n      [\"\".concat(componentCls, \"-ink-bar\")]: {\n        position: 'absolute',\n        background: token.inkBarColor,\n        pointerEvents: 'none'\n      }\n    }), genTabStyle(token)), {\n      // =========================== TabPanes ===========================\n      [\"\".concat(componentCls, \"-content\")]: {\n        position: 'relative',\n        width: '100%'\n      },\n      [\"\".concat(componentCls, \"-content-holder\")]: {\n        flex: 'auto',\n        minWidth: 0,\n        minHeight: 0\n      },\n      [\"\".concat(componentCls, \"-tabpane\")]: Object.assign(Object.assign({}, genFocusStyle(token)), {\n        '&-hidden': {\n          display: 'none'\n        }\n      })\n    }),\n    [\"\".concat(componentCls, \"-centered\")]: {\n      [\"> \".concat(componentCls, \"-nav, > div > \").concat(componentCls, \"-nav\")]: {\n        [\"\".concat(componentCls, \"-nav-wrap\")]: {\n          [\"&:not([class*='\".concat(componentCls, \"-nav-wrap-ping']) > \").concat(componentCls, \"-nav-list\")]: {\n            margin: 'auto'\n          }\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    cardHeight,\n    cardHeightSM,\n    cardHeightLG,\n    controlHeight,\n    controlHeightLG\n  } = token;\n  const mergedCardHeight = cardHeight || controlHeightLG;\n  const mergedCardHeightSM = cardHeightSM || controlHeight;\n  // `controlHeight` missing XL variable, so we directly write it here:\n  const mergedCardHeightLG = cardHeightLG || controlHeightLG + 8;\n  return {\n    zIndexPopup: token.zIndexPopupBase + 50,\n    cardBg: token.colorFillAlter,\n    // We can not pass this as valid value,\n    // Since `cardHeight` will lock nav add button height.\n    cardHeight: mergedCardHeight,\n    cardHeightSM: mergedCardHeightSM,\n    cardHeightLG: mergedCardHeightLG,\n    // Initialize with empty string, because cardPadding will be calculated with cardHeight by default.\n    cardPadding: \"\".concat((mergedCardHeight - token.fontHeight) / 2 - token.lineWidth, \"px \").concat(token.padding, \"px\"),\n    cardPaddingSM: \"\".concat((mergedCardHeightSM - token.fontHeight) / 2 - token.lineWidth, \"px \").concat(token.paddingXS, \"px\"),\n    cardPaddingLG: \"\".concat((mergedCardHeightLG - token.fontHeightLG) / 2 - token.lineWidth, \"px \").concat(token.padding, \"px\"),\n    titleFontSize: token.fontSize,\n    titleFontSizeLG: token.fontSizeLG,\n    titleFontSizeSM: token.fontSize,\n    inkBarColor: token.colorPrimary,\n    horizontalMargin: \"0 0 \".concat(token.margin, \"px 0\"),\n    horizontalItemGutter: 32,\n    // Fixed Value\n    // Initialize with empty string, because horizontalItemMargin will be calculated with horizontalItemGutter by default.\n    horizontalItemMargin: \"\",\n    horizontalItemMarginRTL: \"\",\n    horizontalItemPadding: \"\".concat(token.paddingSM, \"px 0\"),\n    horizontalItemPaddingSM: \"\".concat(token.paddingXS, \"px 0\"),\n    horizontalItemPaddingLG: \"\".concat(token.padding, \"px 0\"),\n    verticalItemPadding: \"\".concat(token.paddingXS, \"px \").concat(token.paddingLG, \"px\"),\n    verticalItemMargin: \"\".concat(token.margin, \"px 0 0 0\"),\n    itemColor: token.colorText,\n    itemSelectedColor: token.colorPrimary,\n    itemHoverColor: token.colorPrimaryHover,\n    itemActiveColor: token.colorPrimaryActive,\n    cardGutter: token.marginXXS / 2\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Tabs', token => {\n  const tabsToken = mergeToken(token, {\n    // `cardPadding` is empty by default, so we could calculate with dynamic `cardHeight`\n    tabsCardPadding: token.cardPadding,\n    dropdownEdgeChildVerticalPadding: token.paddingXXS,\n    tabsActiveTextShadow: '0 0 0.25px currentcolor',\n    tabsDropdownHeight: 200,\n    tabsDropdownWidth: 120,\n    tabsHorizontalItemMargin: \"0 0 0 \".concat(unit(token.horizontalItemGutter)),\n    tabsHorizontalItemMarginRTL: \"0 0 0 \".concat(unit(token.horizontalItemGutter))\n  });\n  return [genSizeStyle(tabsToken), genRtlStyle(tabsToken), genPositionStyle(tabsToken), genDropdownStyle(tabsToken), genCardStyle(tabsToken), genTabsStyle(tabsToken), genMotionStyle(tabsToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "genFocusOutline", "genFocusStyle", "resetComponent", "textEllipsis", "genStyleHooks", "mergeToken", "genMotionStyle", "genCardStyle", "token", "componentCls", "tabsCardPadding", "cardBg", "cardGutter", "colorBorderSecondary", "itemSelectedColor", "concat", "margin", "padding", "background", "border", "lineWidth", "lineType", "transition", "motionDurationSlow", "motionEaseInOut", "color", "colorBgContainer", "outline", "visibility", "marginLeft", "_skip_check_", "value", "borderRadius", "borderRadiusLG", "borderBottomColor", "borderTopColor", "marginTop", "borderRightColor", "borderLeftColor", "genDropdownStyle", "itemHoverColor", "dropdownEdgeChildVerticalPadding", "Object", "assign", "position", "top", "left", "zIndex", "zIndexPopup", "display", "maxHeight", "tabsDropdownHeight", "overflowX", "overflowY", "textAlign", "listStyleType", "backgroundColor", "backgroundClip", "boxShadow", "boxShadowSecondary", "alignItems", "min<PERSON><PERSON><PERSON>", "tabsDropdownWidth", "paddingXXS", "paddingSM", "colorText", "fontWeight", "fontSize", "lineHeight", "cursor", "flex", "whiteSpace", "marginSM", "colorIcon", "fontSizeSM", "controlItemBgHover", "colorTextDisabled", "genPositionStyle", "<PERSON><PERSON><PERSON><PERSON>", "verticalItemPadding", "verticalItemMargin", "calc", "flexDirection", "right", "borderBottom", "content", "height", "lineWidthBold", "bottom", "width", "controlHeight", "boxShadowTabsOverflowLeft", "boxShadowTabsOverflowRight", "opacity", "order", "marginBottom", "mul", "equal", "boxShadowTabsOverflowTop", "boxShadowTabsOverflowBottom", "borderLeft", "colorBorder", "paddingLeft", "paddingLG", "marginRight", "borderRight", "paddingRight", "genSizeStyle", "cardPaddingSM", "cardPaddingLG", "cardHeightSM", "cardHeightLG", "horizontalItemPaddingSM", "horizontalItemPaddingLG", "titleFontSizeSM", "titleFontSizeLG", "lineHeightLG", "minHeight", "genTabStyle", "itemActiveColor", "iconCls", "tabsHorizontalItemMargin", "horizontalItemPadding", "itemColor", "tabCls", "WebkitTouchCallout", "WebkitTapHighlightColor", "titleFontSize", "marginInlineEnd", "marginXXS", "marginXS", "colorTextHeading", "textShadow", "tabsActiveTextShadow", "genRtlStyle", "tabsHorizontalItemMarginRTL", "rtlCls", "direction", "genTabsStyle", "cardHeight", "alignSelf", "overflow", "transform", "pointerEvents", "controlHeightLG", "div", "inkBarColor", "prepareComponentToken", "mergedCardHeight", "mergedCardHeightSM", "mergedCardHeightLG", "zIndexPopupBase", "colorFillAlter", "cardPadding", "fontHeight", "paddingXS", "fontHeightLG", "fontSizeLG", "colorPrimary", "horizontalItemGutter", "horizontalItemMargin", "horizontalItemMarginRTL", "colorPrimaryHover", "colorPrimaryActive", "tabsToken"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/tabs/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { genFocusOutline, genFocusStyle, resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genMotionStyle from './motion';\nconst genCardStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardBg,\n    cardGutter,\n    colorBorderSecondary,\n    itemSelectedColor\n  } = token;\n  return {\n    [`${componentCls}-card`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-tab`]: {\n          margin: 0,\n          padding: tabsCardPadding,\n          background: cardBg,\n          border: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseInOut}`\n        },\n        [`${componentCls}-tab-active`]: {\n          color: itemSelectedColor,\n          background: token.colorBgContainer\n        },\n        [`${componentCls}-tab-focus:has(${componentCls}-tab-btn:focus-visible)`]: genFocusOutline(token, -3),\n        [`& ${componentCls}-tab${componentCls}-tab-focus ${componentCls}-tab-btn:focus-visible`]: {\n          outline: 'none'\n        },\n        [`${componentCls}-ink-bar`]: {\n          visibility: 'hidden'\n        }\n      },\n      // ========================== Top & Bottom ==========================\n      [`&${componentCls}-top, &${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(cardGutter)\n            }\n          }\n        }\n      },\n      [`&${componentCls}-top`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: `${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0 0`\n          },\n          [`${componentCls}-tab-active`]: {\n            borderBottomColor: token.colorBgContainer\n          }\n        }\n      },\n      [`&${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: `0 0 ${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)}`\n          },\n          [`${componentCls}-tab-active`]: {\n            borderTopColor: token.colorBgContainer\n          }\n        }\n      },\n      // ========================== Left & Right ==========================\n      [`&${componentCls}-left, &${componentCls}-right`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginTop: unit(cardGutter)\n          }\n        }\n      },\n      [`&${componentCls}-left`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `${unit(token.borderRadiusLG)} 0 0 ${unit(token.borderRadiusLG)}`\n            }\n          },\n          [`${componentCls}-tab-active`]: {\n            borderRightColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `0 ${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0`\n            }\n          },\n          [`${componentCls}-tab-active`]: {\n            borderLeftColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genDropdownStyle = token => {\n  const {\n    componentCls,\n    itemHoverColor,\n    dropdownEdgeChildVerticalPadding\n  } = token;\n  return {\n    [`${componentCls}-dropdown`]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: -9999,\n      left: {\n        _skip_check_: true,\n        value: -9999\n      },\n      zIndex: token.zIndexPopup,\n      display: 'block',\n      '&-hidden': {\n        display: 'none'\n      },\n      [`${componentCls}-dropdown-menu`]: {\n        maxHeight: token.tabsDropdownHeight,\n        margin: 0,\n        padding: `${unit(dropdownEdgeChildVerticalPadding)} 0`,\n        overflowX: 'hidden',\n        overflowY: 'auto',\n        textAlign: {\n          _skip_check_: true,\n          value: 'left'\n        },\n        listStyleType: 'none',\n        backgroundColor: token.colorBgContainer,\n        backgroundClip: 'padding-box',\n        borderRadius: token.borderRadiusLG,\n        outline: 'none',\n        boxShadow: token.boxShadowSecondary,\n        '&-item': Object.assign(Object.assign({}, textEllipsis), {\n          display: 'flex',\n          alignItems: 'center',\n          minWidth: token.tabsDropdownWidth,\n          margin: 0,\n          padding: `${unit(token.paddingXXS)} ${unit(token.paddingSM)}`,\n          color: token.colorText,\n          fontWeight: 'normal',\n          fontSize: token.fontSize,\n          lineHeight: token.lineHeight,\n          cursor: 'pointer',\n          transition: `all ${token.motionDurationSlow}`,\n          '> span': {\n            flex: 1,\n            whiteSpace: 'nowrap'\n          },\n          '&-remove': {\n            flex: 'none',\n            marginLeft: {\n              _skip_check_: true,\n              value: token.marginSM\n            },\n            color: token.colorIcon,\n            fontSize: token.fontSizeSM,\n            background: 'transparent',\n            border: 0,\n            cursor: 'pointer',\n            '&:hover': {\n              color: itemHoverColor\n            }\n          },\n          '&:hover': {\n            background: token.controlItemBgHover\n          },\n          '&-disabled': {\n            '&, &:hover': {\n              color: token.colorTextDisabled,\n              background: 'transparent',\n              cursor: 'not-allowed'\n            }\n          }\n        })\n      }\n    })\n  };\n};\nconst genPositionStyle = token => {\n  const {\n    componentCls,\n    margin,\n    colorBorderSecondary,\n    horizontalMargin,\n    verticalItemPadding,\n    verticalItemMargin,\n    calc\n  } = token;\n  return {\n    // ========================== Top & Bottom ==========================\n    [`${componentCls}-top, ${componentCls}-bottom`]: {\n      flexDirection: 'column',\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        margin: horizontalMargin,\n        '&::before': {\n          position: 'absolute',\n          right: {\n            _skip_check_: true,\n            value: 0\n          },\n          left: {\n            _skip_check_: true,\n            value: 0\n          },\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          content: \"''\"\n        },\n        [`${componentCls}-ink-bar`]: {\n          height: token.lineWidthBold,\n          '&-animated': {\n            transition: `width ${token.motionDurationSlow}, left ${token.motionDurationSlow},\n            right ${token.motionDurationSlow}`\n          }\n        },\n        [`${componentCls}-nav-wrap`]: {\n          '&::before, &::after': {\n            top: 0,\n            bottom: 0,\n            width: token.controlHeight\n          },\n          '&::before': {\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowLeft\n          },\n          '&::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowRight\n          },\n          [`&${componentCls}-nav-wrap-ping-left::before`]: {\n            opacity: 1\n          },\n          [`&${componentCls}-nav-wrap-ping-right::after`]: {\n            opacity: 1\n          }\n        }\n      }\n    },\n    [`${componentCls}-top`]: {\n      [`> ${componentCls}-nav,\n        > div > ${componentCls}-nav`]: {\n        '&::before': {\n          bottom: 0\n        },\n        [`${componentCls}-ink-bar`]: {\n          bottom: 0\n        }\n      }\n    },\n    [`${componentCls}-bottom`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        order: 1,\n        marginTop: margin,\n        marginBottom: 0,\n        '&::before': {\n          top: 0\n        },\n        [`${componentCls}-ink-bar`]: {\n          top: 0\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        order: 0\n      }\n    },\n    // ========================== Left & Right ==========================\n    [`${componentCls}-left, ${componentCls}-right`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        flexDirection: 'column',\n        minWidth: calc(token.controlHeight).mul(1.25).equal(),\n        // >>>>>>>>>>> Tab\n        [`${componentCls}-tab`]: {\n          padding: verticalItemPadding,\n          textAlign: 'center'\n        },\n        [`${componentCls}-tab + ${componentCls}-tab`]: {\n          margin: verticalItemMargin\n        },\n        // >>>>>>>>>>> Nav\n        [`${componentCls}-nav-wrap`]: {\n          flexDirection: 'column',\n          '&::before, &::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.controlHeight\n          },\n          '&::before': {\n            top: 0,\n            boxShadow: token.boxShadowTabsOverflowTop\n          },\n          '&::after': {\n            bottom: 0,\n            boxShadow: token.boxShadowTabsOverflowBottom\n          },\n          [`&${componentCls}-nav-wrap-ping-top::before`]: {\n            opacity: 1\n          },\n          [`&${componentCls}-nav-wrap-ping-bottom::after`]: {\n            opacity: 1\n          }\n        },\n        // >>>>>>>>>>> Ink Bar\n        [`${componentCls}-ink-bar`]: {\n          width: token.lineWidthBold,\n          '&-animated': {\n            transition: `height ${token.motionDurationSlow}, top ${token.motionDurationSlow}`\n          }\n        },\n        [`${componentCls}-nav-list, ${componentCls}-nav-operations`]: {\n          flex: '1 0 auto',\n          // fix safari scroll problem\n          flexDirection: 'column'\n        }\n      }\n    },\n    [`${componentCls}-left`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-ink-bar`]: {\n          right: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        marginLeft: {\n          _skip_check_: true,\n          value: unit(calc(token.lineWidth).mul(-1).equal())\n        },\n        borderLeft: {\n          _skip_check_: true,\n          value: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n        },\n        [`> ${componentCls}-content > ${componentCls}-tabpane`]: {\n          paddingLeft: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    },\n    [`${componentCls}-right`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        order: 1,\n        [`${componentCls}-ink-bar`]: {\n          left: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        order: 0,\n        marginRight: {\n          _skip_check_: true,\n          value: calc(token.lineWidth).mul(-1).equal()\n        },\n        borderRight: {\n          _skip_check_: true,\n          value: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n        },\n        [`> ${componentCls}-content > ${componentCls}-tabpane`]: {\n          paddingRight: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    cardPaddingSM,\n    cardPaddingLG,\n    cardHeightSM,\n    cardHeightLG,\n    horizontalItemPaddingSM,\n    horizontalItemPaddingLG\n  } = token;\n  return {\n    // >>>>> shared\n    [componentCls]: {\n      '&-small': {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: horizontalItemPaddingSM,\n            fontSize: token.titleFontSizeSM\n          }\n        }\n      },\n      '&-large': {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: horizontalItemPaddingLG,\n            fontSize: token.titleFontSizeLG,\n            lineHeight: token.lineHeightLG\n          }\n        }\n      }\n    },\n    // >>>>> card\n    [`${componentCls}-card`]: {\n      // Small\n      [`&${componentCls}-small`]: {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: cardPaddingSM\n          },\n          [`${componentCls}-nav-add`]: {\n            minWidth: cardHeightSM,\n            minHeight: cardHeightSM\n          }\n        },\n        [`&${componentCls}-bottom`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: `0 0 ${unit(token.borderRadius)} ${unit(token.borderRadius)}`\n          }\n        },\n        [`&${componentCls}-top`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: `${unit(token.borderRadius)} ${unit(token.borderRadius)} 0 0`\n          }\n        },\n        [`&${componentCls}-right`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `0 ${unit(token.borderRadius)} ${unit(token.borderRadius)} 0`\n            }\n          }\n        },\n        [`&${componentCls}-left`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `${unit(token.borderRadius)} 0 0 ${unit(token.borderRadius)}`\n            }\n          }\n        }\n      },\n      // Large\n      [`&${componentCls}-large`]: {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: cardPaddingLG\n          },\n          [`${componentCls}-nav-add`]: {\n            minWidth: cardHeightLG,\n            minHeight: cardHeightLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genTabStyle = token => {\n  const {\n    componentCls,\n    itemActiveColor,\n    itemHoverColor,\n    iconCls,\n    tabsHorizontalItemMargin,\n    horizontalItemPadding,\n    itemSelectedColor,\n    itemColor\n  } = token;\n  const tabCls = `${componentCls}-tab`;\n  return {\n    [tabCls]: {\n      position: 'relative',\n      WebkitTouchCallout: 'none',\n      WebkitTapHighlightColor: 'transparent',\n      display: 'inline-flex',\n      alignItems: 'center',\n      padding: horizontalItemPadding,\n      fontSize: token.titleFontSize,\n      background: 'transparent',\n      border: 0,\n      outline: 'none',\n      cursor: 'pointer',\n      color: itemColor,\n      '&-btn, &-remove': {\n        '&:focus:not(:focus-visible), &:active': {\n          color: itemActiveColor\n        }\n      },\n      '&-btn': {\n        outline: 'none',\n        transition: `all ${token.motionDurationSlow}`,\n        [`${tabCls}-icon:not(:last-child)`]: {\n          marginInlineEnd: token.marginSM\n        }\n      },\n      '&-remove': Object.assign({\n        flex: 'none',\n        marginRight: {\n          _skip_check_: true,\n          value: token.calc(token.marginXXS).mul(-1).equal()\n        },\n        marginLeft: {\n          _skip_check_: true,\n          value: token.marginXS\n        },\n        color: token.colorIcon,\n        fontSize: token.fontSizeSM,\n        background: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      }, genFocusStyle(token)),\n      '&:hover': {\n        color: itemHoverColor\n      },\n      [`&${tabCls}-active ${tabCls}-btn`]: {\n        color: itemSelectedColor,\n        textShadow: token.tabsActiveTextShadow\n      },\n      [`&${tabCls}-focus ${tabCls}-btn:focus-visible`]: genFocusOutline(token),\n      [`&${tabCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed'\n      },\n      [`&${tabCls}-disabled ${tabCls}-btn, &${tabCls}-disabled ${componentCls}-remove`]: {\n        '&:focus, &:active': {\n          color: token.colorTextDisabled\n        }\n      },\n      [`& ${tabCls}-remove ${iconCls}`]: {\n        margin: 0\n      },\n      [`${iconCls}:not(:last-child)`]: {\n        marginRight: {\n          _skip_check_: true,\n          value: token.marginSM\n        }\n      }\n    },\n    [`${tabCls} + ${tabCls}`]: {\n      margin: {\n        _skip_check_: true,\n        value: tabsHorizontalItemMargin\n      }\n    }\n  };\n};\nconst genRtlStyle = token => {\n  const {\n    componentCls,\n    tabsHorizontalItemMarginRTL,\n    iconCls,\n    cardGutter,\n    calc\n  } = token;\n  const rtlCls = `${componentCls}-rtl`;\n  return {\n    [rtlCls]: {\n      direction: 'rtl',\n      [`${componentCls}-nav`]: {\n        [`${componentCls}-tab`]: {\n          margin: {\n            _skip_check_: true,\n            value: tabsHorizontalItemMarginRTL\n          },\n          [`${componentCls}-tab:last-of-type`]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          },\n          [iconCls]: {\n            marginRight: {\n              _skip_check_: true,\n              value: 0\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(token.marginSM)\n            }\n          },\n          [`${componentCls}-tab-remove`]: {\n            marginRight: {\n              _skip_check_: true,\n              value: unit(token.marginXS)\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(calc(token.marginXXS).mul(-1).equal())\n            },\n            [iconCls]: {\n              margin: 0\n            }\n          }\n        }\n      },\n      [`&${componentCls}-left`]: {\n        [`> ${componentCls}-nav`]: {\n          order: 1\n        },\n        [`> ${componentCls}-content-holder`]: {\n          order: 0\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`> ${componentCls}-nav`]: {\n          order: 0\n        },\n        [`> ${componentCls}-content-holder`]: {\n          order: 1\n        }\n      },\n      // ====================== Card ======================\n      [`&${componentCls}-card${componentCls}-top, &${componentCls}-card${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginRight: {\n              _skip_check_: true,\n              value: cardGutter\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          }\n        }\n      }\n    },\n    [`${componentCls}-dropdown-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-menu-item`]: {\n      [`${componentCls}-dropdown-rtl`]: {\n        textAlign: {\n          _skip_check_: true,\n          value: 'right'\n        }\n      }\n    }\n  };\n};\nconst genTabsStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardHeight,\n    cardGutter,\n    itemHoverColor,\n    itemActiveColor,\n    colorBorderSecondary\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      // ========================== Navigation ==========================\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        position: 'relative',\n        display: 'flex',\n        flex: 'none',\n        alignItems: 'center',\n        [`${componentCls}-nav-wrap`]: {\n          position: 'relative',\n          display: 'flex',\n          flex: 'auto',\n          alignSelf: 'stretch',\n          overflow: 'hidden',\n          whiteSpace: 'nowrap',\n          transform: 'translate(0)',\n          // Fix chrome render bug\n          // >>>>> Ping shadow\n          '&::before, &::after': {\n            position: 'absolute',\n            zIndex: 1,\n            opacity: 0,\n            transition: `opacity ${token.motionDurationSlow}`,\n            content: \"''\",\n            pointerEvents: 'none'\n          }\n        },\n        [`${componentCls}-nav-list`]: {\n          position: 'relative',\n          display: 'flex',\n          transition: `opacity ${token.motionDurationSlow}`\n        },\n        // >>>>>>>> Operations\n        [`${componentCls}-nav-operations`]: {\n          display: 'flex',\n          alignSelf: 'stretch'\n        },\n        [`${componentCls}-nav-operations-hidden`]: {\n          position: 'absolute',\n          visibility: 'hidden',\n          pointerEvents: 'none'\n        },\n        [`${componentCls}-nav-more`]: {\n          position: 'relative',\n          padding: tabsCardPadding,\n          background: 'transparent',\n          border: 0,\n          color: token.colorText,\n          '&::after': {\n            position: 'absolute',\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            bottom: 0,\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.calc(token.controlHeightLG).div(8).equal(),\n            transform: 'translateY(100%)',\n            content: \"''\"\n          }\n        },\n        [`${componentCls}-nav-add`]: Object.assign({\n          minWidth: cardHeight,\n          minHeight: cardHeight,\n          marginLeft: {\n            _skip_check_: true,\n            value: cardGutter\n          },\n          background: 'transparent',\n          border: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          borderRadius: `${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0 0`,\n          outline: 'none',\n          cursor: 'pointer',\n          color: token.colorText,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseInOut}`,\n          '&:hover': {\n            color: itemHoverColor\n          },\n          '&:active, &:focus:not(:focus-visible)': {\n            color: itemActiveColor\n          }\n        }, genFocusStyle(token, -3))\n      },\n      [`${componentCls}-extra-content`]: {\n        flex: 'none'\n      },\n      // ============================ InkBar ============================\n      [`${componentCls}-ink-bar`]: {\n        position: 'absolute',\n        background: token.inkBarColor,\n        pointerEvents: 'none'\n      }\n    }), genTabStyle(token)), {\n      // =========================== TabPanes ===========================\n      [`${componentCls}-content`]: {\n        position: 'relative',\n        width: '100%'\n      },\n      [`${componentCls}-content-holder`]: {\n        flex: 'auto',\n        minWidth: 0,\n        minHeight: 0\n      },\n      [`${componentCls}-tabpane`]: Object.assign(Object.assign({}, genFocusStyle(token)), {\n        '&-hidden': {\n          display: 'none'\n        }\n      })\n    }),\n    [`${componentCls}-centered`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-nav-wrap`]: {\n          [`&:not([class*='${componentCls}-nav-wrap-ping']) > ${componentCls}-nav-list`]: {\n            margin: 'auto'\n          }\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    cardHeight,\n    cardHeightSM,\n    cardHeightLG,\n    controlHeight,\n    controlHeightLG\n  } = token;\n  const mergedCardHeight = cardHeight || controlHeightLG;\n  const mergedCardHeightSM = cardHeightSM || controlHeight;\n  // `controlHeight` missing XL variable, so we directly write it here:\n  const mergedCardHeightLG = cardHeightLG || controlHeightLG + 8;\n  return {\n    zIndexPopup: token.zIndexPopupBase + 50,\n    cardBg: token.colorFillAlter,\n    // We can not pass this as valid value,\n    // Since `cardHeight` will lock nav add button height.\n    cardHeight: mergedCardHeight,\n    cardHeightSM: mergedCardHeightSM,\n    cardHeightLG: mergedCardHeightLG,\n    // Initialize with empty string, because cardPadding will be calculated with cardHeight by default.\n    cardPadding: `${(mergedCardHeight - token.fontHeight) / 2 - token.lineWidth}px ${token.padding}px`,\n    cardPaddingSM: `${(mergedCardHeightSM - token.fontHeight) / 2 - token.lineWidth}px ${token.paddingXS}px`,\n    cardPaddingLG: `${(mergedCardHeightLG - token.fontHeightLG) / 2 - token.lineWidth}px ${token.padding}px`,\n    titleFontSize: token.fontSize,\n    titleFontSizeLG: token.fontSizeLG,\n    titleFontSizeSM: token.fontSize,\n    inkBarColor: token.colorPrimary,\n    horizontalMargin: `0 0 ${token.margin}px 0`,\n    horizontalItemGutter: 32,\n    // Fixed Value\n    // Initialize with empty string, because horizontalItemMargin will be calculated with horizontalItemGutter by default.\n    horizontalItemMargin: ``,\n    horizontalItemMarginRTL: ``,\n    horizontalItemPadding: `${token.paddingSM}px 0`,\n    horizontalItemPaddingSM: `${token.paddingXS}px 0`,\n    horizontalItemPaddingLG: `${token.padding}px 0`,\n    verticalItemPadding: `${token.paddingXS}px ${token.paddingLG}px`,\n    verticalItemMargin: `${token.margin}px 0 0 0`,\n    itemColor: token.colorText,\n    itemSelectedColor: token.colorPrimary,\n    itemHoverColor: token.colorPrimaryHover,\n    itemActiveColor: token.colorPrimaryActive,\n    cardGutter: token.marginXXS / 2\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Tabs', token => {\n  const tabsToken = mergeToken(token, {\n    // `cardPadding` is empty by default, so we could calculate with dynamic `cardHeight`\n    tabsCardPadding: token.cardPadding,\n    dropdownEdgeChildVerticalPadding: token.paddingXXS,\n    tabsActiveTextShadow: '0 0 0.25px currentcolor',\n    tabsDropdownHeight: 200,\n    tabsDropdownWidth: 120,\n    tabsHorizontalItemMargin: `0 0 0 ${unit(token.horizontalItemGutter)}`,\n    tabsHorizontalItemMarginRTL: `0 0 0 ${unit(token.horizontalItemGutter)}`\n  });\n  return [genSizeStyle(tabsToken), genRtlStyle(tabsToken), genPositionStyle(tabsToken), genDropdownStyle(tabsToken), genCardStyle(tabsToken), genTabsStyle(tabsToken), genMotionStyle(tabsToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,eAAe,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,QAAQ,aAAa;AAC1F,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,OAAOC,cAAc,MAAM,UAAU;AACrC,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,MAAM;IACNC,UAAU;IACVC,oBAAoB;IACpBC;EACF,CAAC,GAAGN,KAAK;EACT,OAAO;IACL,IAAAO,MAAA,CAAIN,YAAY,aAAU;MACxB,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;QACtD,IAAAM,MAAA,CAAIN,YAAY,YAAS;UACvBO,MAAM,EAAE,CAAC;UACTC,OAAO,EAAEP,eAAe;UACxBQ,UAAU,EAAEP,MAAM;UAClBQ,MAAM,KAAAJ,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACY,SAAS,CAAC,OAAAL,MAAA,CAAIP,KAAK,CAACa,QAAQ,OAAAN,MAAA,CAAIF,oBAAoB,CAAE;UAC5ES,UAAU,SAAAP,MAAA,CAASP,KAAK,CAACe,kBAAkB,OAAAR,MAAA,CAAIP,KAAK,CAACgB,eAAe;QACtE,CAAC;QACD,IAAAT,MAAA,CAAIN,YAAY,mBAAgB;UAC9BgB,KAAK,EAAEX,iBAAiB;UACxBI,UAAU,EAAEV,KAAK,CAACkB;QACpB,CAAC;QACD,IAAAX,MAAA,CAAIN,YAAY,qBAAAM,MAAA,CAAkBN,YAAY,+BAA4BT,eAAe,CAACQ,KAAK,EAAE,CAAC,CAAC,CAAC;QACpG,MAAAO,MAAA,CAAMN,YAAY,UAAAM,MAAA,CAAON,YAAY,iBAAAM,MAAA,CAAcN,YAAY,8BAA2B;UACxFkB,OAAO,EAAE;QACX,CAAC;QACD,IAAAZ,MAAA,CAAIN,YAAY,gBAAa;UAC3BmB,UAAU,EAAE;QACd;MACF,CAAC;MACD;MACA,KAAAb,MAAA,CAAKN,YAAY,aAAAM,MAAA,CAAUN,YAAY,eAAY;QACjD,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;UACtD,IAAAM,MAAA,CAAIN,YAAY,aAAAM,MAAA,CAAUN,YAAY,YAAS;YAC7CoB,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEhC,IAAI,CAACa,UAAU;YACxB;UACF;QACF;MACF,CAAC;MACD,KAAAG,MAAA,CAAKN,YAAY,YAAS;QACxB,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;UACtD,IAAAM,MAAA,CAAIN,YAAY,YAAS;YACvBuB,YAAY,KAAAjB,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC,OAAAlB,MAAA,CAAIhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC;UAC3E,CAAC;UACD,IAAAlB,MAAA,CAAIN,YAAY,mBAAgB;YAC9ByB,iBAAiB,EAAE1B,KAAK,CAACkB;UAC3B;QACF;MACF,CAAC;MACD,KAAAX,MAAA,CAAKN,YAAY,eAAY;QAC3B,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;UACtD,IAAAM,MAAA,CAAIN,YAAY,YAAS;YACvBuB,YAAY,SAAAjB,MAAA,CAAShB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC,OAAAlB,MAAA,CAAIhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC;UAC/E,CAAC;UACD,IAAAlB,MAAA,CAAIN,YAAY,mBAAgB;YAC9B0B,cAAc,EAAE3B,KAAK,CAACkB;UACxB;QACF;MACF,CAAC;MACD;MACA,KAAAX,MAAA,CAAKN,YAAY,cAAAM,MAAA,CAAWN,YAAY,cAAW;QACjD,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;UACtD,IAAAM,MAAA,CAAIN,YAAY,aAAAM,MAAA,CAAUN,YAAY,YAAS;YAC7C2B,SAAS,EAAErC,IAAI,CAACa,UAAU;UAC5B;QACF;MACF,CAAC;MACD,KAAAG,MAAA,CAAKN,YAAY,aAAU;QACzB,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;UACtD,IAAAM,MAAA,CAAIN,YAAY,YAAS;YACvBuB,YAAY,EAAE;cACZF,YAAY,EAAE,IAAI;cAClBC,KAAK,KAAAhB,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC,WAAAlB,MAAA,CAAQhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC;YACxE;UACF,CAAC;UACD,IAAAlB,MAAA,CAAIN,YAAY,mBAAgB;YAC9B4B,gBAAgB,EAAE;cAChBP,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEvB,KAAK,CAACkB;YACf;UACF;QACF;MACF,CAAC;MACD,KAAAX,MAAA,CAAKN,YAAY,cAAW;QAC1B,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;UACtD,IAAAM,MAAA,CAAIN,YAAY,YAAS;YACvBuB,YAAY,EAAE;cACZF,YAAY,EAAE,IAAI;cAClBC,KAAK,OAAAhB,MAAA,CAAOhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC,OAAAlB,MAAA,CAAIhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC;YACtE;UACF,CAAC;UACD,IAAAlB,MAAA,CAAIN,YAAY,mBAAgB;YAC9B6B,eAAe,EAAE;cACfR,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEvB,KAAK,CAACkB;YACf;UACF;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMa,gBAAgB,GAAG/B,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZ+B,cAAc;IACdC;EACF,CAAC,GAAGjC,KAAK;EACT,OAAO;IACL,IAAAO,MAAA,CAAIN,YAAY,iBAAciC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzC,cAAc,CAACM,KAAK,CAAC,CAAC,EAAE;MACpFoC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC,IAAI;MACVC,IAAI,EAAE;QACJhB,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE,CAAC;MACV,CAAC;MACDgB,MAAM,EAAEvC,KAAK,CAACwC,WAAW;MACzBC,OAAO,EAAE,OAAO;MAChB,UAAU,EAAE;QACVA,OAAO,EAAE;MACX,CAAC;MACD,IAAAlC,MAAA,CAAIN,YAAY,sBAAmB;QACjCyC,SAAS,EAAE1C,KAAK,CAAC2C,kBAAkB;QACnCnC,MAAM,EAAE,CAAC;QACTC,OAAO,KAAAF,MAAA,CAAKhB,IAAI,CAAC0C,gCAAgC,CAAC,OAAI;QACtDW,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;UACTxB,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDwB,aAAa,EAAE,MAAM;QACrBC,eAAe,EAAEhD,KAAK,CAACkB,gBAAgB;QACvC+B,cAAc,EAAE,aAAa;QAC7BzB,YAAY,EAAExB,KAAK,CAACyB,cAAc;QAClCN,OAAO,EAAE,MAAM;QACf+B,SAAS,EAAElD,KAAK,CAACmD,kBAAkB;QACnC,QAAQ,EAAEjB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExC,YAAY,CAAC,EAAE;UACvD8C,OAAO,EAAE,MAAM;UACfW,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAErD,KAAK,CAACsD,iBAAiB;UACjC9C,MAAM,EAAE,CAAC;UACTC,OAAO,KAAAF,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACuD,UAAU,CAAC,OAAAhD,MAAA,CAAIhB,IAAI,CAACS,KAAK,CAACwD,SAAS,CAAC,CAAE;UAC7DvC,KAAK,EAAEjB,KAAK,CAACyD,SAAS;UACtBC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE3D,KAAK,CAAC2D,QAAQ;UACxBC,UAAU,EAAE5D,KAAK,CAAC4D,UAAU;UAC5BC,MAAM,EAAE,SAAS;UACjB/C,UAAU,SAAAP,MAAA,CAASP,KAAK,CAACe,kBAAkB,CAAE;UAC7C,QAAQ,EAAE;YACR+C,IAAI,EAAE,CAAC;YACPC,UAAU,EAAE;UACd,CAAC;UACD,UAAU,EAAE;YACVD,IAAI,EAAE,MAAM;YACZzC,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEvB,KAAK,CAACgE;YACf,CAAC;YACD/C,KAAK,EAAEjB,KAAK,CAACiE,SAAS;YACtBN,QAAQ,EAAE3D,KAAK,CAACkE,UAAU;YAC1BxD,UAAU,EAAE,aAAa;YACzBC,MAAM,EAAE,CAAC;YACTkD,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE;cACT5C,KAAK,EAAEe;YACT;UACF,CAAC;UACD,SAAS,EAAE;YACTtB,UAAU,EAAEV,KAAK,CAACmE;UACpB,CAAC;UACD,YAAY,EAAE;YACZ,YAAY,EAAE;cACZlD,KAAK,EAAEjB,KAAK,CAACoE,iBAAiB;cAC9B1D,UAAU,EAAE,aAAa;cACzBmD,MAAM,EAAE;YACV;UACF;QACF,CAAC;MACH;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,MAAMQ,gBAAgB,GAAGrE,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZO,MAAM;IACNH,oBAAoB;IACpBiE,gBAAgB;IAChBC,mBAAmB;IACnBC,kBAAkB;IAClBC;EACF,CAAC,GAAGzE,KAAK;EACT,OAAO;IACL;IACA,IAAAO,MAAA,CAAIN,YAAY,YAAAM,MAAA,CAASN,YAAY,eAAY;MAC/CyE,aAAa,EAAE,QAAQ;MACvB,MAAAnE,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;QACtDO,MAAM,EAAE8D,gBAAgB;QACxB,WAAW,EAAE;UACXlC,QAAQ,EAAE,UAAU;UACpBuC,KAAK,EAAE;YACLrD,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT,CAAC;UACDe,IAAI,EAAE;YACJhB,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT,CAAC;UACDqD,YAAY,KAAArE,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACY,SAAS,CAAC,OAAAL,MAAA,CAAIP,KAAK,CAACa,QAAQ,OAAAN,MAAA,CAAIF,oBAAoB,CAAE;UAClFwE,OAAO,EAAE;QACX,CAAC;QACD,IAAAtE,MAAA,CAAIN,YAAY,gBAAa;UAC3B6E,MAAM,EAAE9E,KAAK,CAAC+E,aAAa;UAC3B,YAAY,EAAE;YACZjE,UAAU,WAAAP,MAAA,CAAWP,KAAK,CAACe,kBAAkB,aAAAR,MAAA,CAAUP,KAAK,CAACe,kBAAkB,2BAAAR,MAAA,CACvEP,KAAK,CAACe,kBAAkB;UAClC;QACF,CAAC;QACD,IAAAR,MAAA,CAAIN,YAAY,iBAAc;UAC5B,qBAAqB,EAAE;YACrBoC,GAAG,EAAE,CAAC;YACN2C,MAAM,EAAE,CAAC;YACTC,KAAK,EAAEjF,KAAK,CAACkF;UACf,CAAC;UACD,WAAW,EAAE;YACX5C,IAAI,EAAE;cACJhB,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACD2B,SAAS,EAAElD,KAAK,CAACmF;UACnB,CAAC;UACD,UAAU,EAAE;YACVR,KAAK,EAAE;cACLrD,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACD2B,SAAS,EAAElD,KAAK,CAACoF;UACnB,CAAC;UACD,KAAA7E,MAAA,CAAKN,YAAY,mCAAgC;YAC/CoF,OAAO,EAAE;UACX,CAAC;UACD,KAAA9E,MAAA,CAAKN,YAAY,mCAAgC;YAC/CoF,OAAO,EAAE;UACX;QACF;MACF;IACF,CAAC;IACD,IAAA9E,MAAA,CAAIN,YAAY,YAAS;MACvB,MAAAM,MAAA,CAAMN,YAAY,6BAAAM,MAAA,CACNN,YAAY,YAAS;QAC/B,WAAW,EAAE;UACX+E,MAAM,EAAE;QACV,CAAC;QACD,IAAAzE,MAAA,CAAIN,YAAY,gBAAa;UAC3B+E,MAAM,EAAE;QACV;MACF;IACF,CAAC;IACD,IAAAzE,MAAA,CAAIN,YAAY,eAAY;MAC1B,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;QACtDqF,KAAK,EAAE,CAAC;QACR1D,SAAS,EAAEpB,MAAM;QACjB+E,YAAY,EAAE,CAAC;QACf,WAAW,EAAE;UACXlD,GAAG,EAAE;QACP,CAAC;QACD,IAAA9B,MAAA,CAAIN,YAAY,gBAAa;UAC3BoC,GAAG,EAAE;QACP;MACF,CAAC;MACD,MAAA9B,MAAA,CAAMN,YAAY,+BAAAM,MAAA,CAA4BN,YAAY,uBAAoB;QAC5EqF,KAAK,EAAE;MACT;IACF,CAAC;IACD;IACA,IAAA/E,MAAA,CAAIN,YAAY,aAAAM,MAAA,CAAUN,YAAY,cAAW;MAC/C,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;QACtDyE,aAAa,EAAE,QAAQ;QACvBrB,QAAQ,EAAEoB,IAAI,CAACzE,KAAK,CAACkF,aAAa,CAAC,CAACM,GAAG,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;QACrD;QACA,IAAAlF,MAAA,CAAIN,YAAY,YAAS;UACvBQ,OAAO,EAAE8D,mBAAmB;UAC5BzB,SAAS,EAAE;QACb,CAAC;QACD,IAAAvC,MAAA,CAAIN,YAAY,aAAAM,MAAA,CAAUN,YAAY,YAAS;UAC7CO,MAAM,EAAEgE;QACV,CAAC;QACD;QACA,IAAAjE,MAAA,CAAIN,YAAY,iBAAc;UAC5ByE,aAAa,EAAE,QAAQ;UACvB,qBAAqB,EAAE;YACrBC,KAAK,EAAE;cACLrD,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDe,IAAI,EAAE;cACJhB,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDuD,MAAM,EAAE9E,KAAK,CAACkF;UAChB,CAAC;UACD,WAAW,EAAE;YACX7C,GAAG,EAAE,CAAC;YACNa,SAAS,EAAElD,KAAK,CAAC0F;UACnB,CAAC;UACD,UAAU,EAAE;YACVV,MAAM,EAAE,CAAC;YACT9B,SAAS,EAAElD,KAAK,CAAC2F;UACnB,CAAC;UACD,KAAApF,MAAA,CAAKN,YAAY,kCAA+B;YAC9CoF,OAAO,EAAE;UACX,CAAC;UACD,KAAA9E,MAAA,CAAKN,YAAY,oCAAiC;YAChDoF,OAAO,EAAE;UACX;QACF,CAAC;QACD;QACA,IAAA9E,MAAA,CAAIN,YAAY,gBAAa;UAC3BgF,KAAK,EAAEjF,KAAK,CAAC+E,aAAa;UAC1B,YAAY,EAAE;YACZjE,UAAU,YAAAP,MAAA,CAAYP,KAAK,CAACe,kBAAkB,YAAAR,MAAA,CAASP,KAAK,CAACe,kBAAkB;UACjF;QACF,CAAC;QACD,IAAAR,MAAA,CAAIN,YAAY,iBAAAM,MAAA,CAAcN,YAAY,uBAAoB;UAC5D6D,IAAI,EAAE,UAAU;UAChB;UACAY,aAAa,EAAE;QACjB;MACF;IACF,CAAC;IACD,IAAAnE,MAAA,CAAIN,YAAY,aAAU;MACxB,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;QACtD,IAAAM,MAAA,CAAIN,YAAY,gBAAa;UAC3B0E,KAAK,EAAE;YACLrD,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD,MAAAhB,MAAA,CAAMN,YAAY,+BAAAM,MAAA,CAA4BN,YAAY,uBAAoB;QAC5EoB,UAAU,EAAE;UACVC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEhC,IAAI,CAACkF,IAAI,CAACzE,KAAK,CAACY,SAAS,CAAC,CAAC4E,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACnD,CAAC;QACDG,UAAU,EAAE;UACVtE,YAAY,EAAE,IAAI;UAClBC,KAAK,KAAAhB,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACY,SAAS,CAAC,OAAAL,MAAA,CAAIP,KAAK,CAACa,QAAQ,OAAAN,MAAA,CAAIP,KAAK,CAAC6F,WAAW;QACxE,CAAC;QACD,MAAAtF,MAAA,CAAMN,YAAY,iBAAAM,MAAA,CAAcN,YAAY,gBAAa;UACvD6F,WAAW,EAAE;YACXxE,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAEvB,KAAK,CAAC+F;UACf;QACF;MACF;IACF,CAAC;IACD,IAAAxF,MAAA,CAAIN,YAAY,cAAW;MACzB,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;QACtDqF,KAAK,EAAE,CAAC;QACR,IAAA/E,MAAA,CAAIN,YAAY,gBAAa;UAC3BqC,IAAI,EAAE;YACJhB,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD,MAAAhB,MAAA,CAAMN,YAAY,+BAAAM,MAAA,CAA4BN,YAAY,uBAAoB;QAC5EqF,KAAK,EAAE,CAAC;QACRU,WAAW,EAAE;UACX1E,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEkD,IAAI,CAACzE,KAAK,CAACY,SAAS,CAAC,CAAC4E,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;QAC7C,CAAC;QACDQ,WAAW,EAAE;UACX3E,YAAY,EAAE,IAAI;UAClBC,KAAK,KAAAhB,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACY,SAAS,CAAC,OAAAL,MAAA,CAAIP,KAAK,CAACa,QAAQ,OAAAN,MAAA,CAAIP,KAAK,CAAC6F,WAAW;QACxE,CAAC;QACD,MAAAtF,MAAA,CAAMN,YAAY,iBAAAM,MAAA,CAAcN,YAAY,gBAAa;UACvDiG,YAAY,EAAE;YACZ5E,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAEvB,KAAK,CAAC+F;UACf;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMI,YAAY,GAAGnG,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZmG,aAAa;IACbC,aAAa;IACbC,YAAY;IACZC,YAAY;IACZC,uBAAuB;IACvBC;EACF,CAAC,GAAGzG,KAAK;EACT,OAAO;IACL;IACA,CAACC,YAAY,GAAG;MACd,SAAS,EAAE;QACT,MAAAM,MAAA,CAAMN,YAAY,YAAS;UACzB,IAAAM,MAAA,CAAIN,YAAY,YAAS;YACvBQ,OAAO,EAAE+F,uBAAuB;YAChC7C,QAAQ,EAAE3D,KAAK,CAAC0G;UAClB;QACF;MACF,CAAC;MACD,SAAS,EAAE;QACT,MAAAnG,MAAA,CAAMN,YAAY,YAAS;UACzB,IAAAM,MAAA,CAAIN,YAAY,YAAS;YACvBQ,OAAO,EAAEgG,uBAAuB;YAChC9C,QAAQ,EAAE3D,KAAK,CAAC2G,eAAe;YAC/B/C,UAAU,EAAE5D,KAAK,CAAC4G;UACpB;QACF;MACF;IACF,CAAC;IACD;IACA,IAAArG,MAAA,CAAIN,YAAY,aAAU;MACxB;MACA,KAAAM,MAAA,CAAKN,YAAY,cAAW;QAC1B,MAAAM,MAAA,CAAMN,YAAY,YAAS;UACzB,IAAAM,MAAA,CAAIN,YAAY,YAAS;YACvBQ,OAAO,EAAE2F;UACX,CAAC;UACD,IAAA7F,MAAA,CAAIN,YAAY,gBAAa;YAC3BoD,QAAQ,EAAEiD,YAAY;YACtBO,SAAS,EAAEP;UACb;QACF,CAAC;QACD,KAAA/F,MAAA,CAAKN,YAAY,eAAY;UAC3B,MAAAM,MAAA,CAAMN,YAAY,WAAAM,MAAA,CAAQN,YAAY,YAAS;YAC7CuB,YAAY,SAAAjB,MAAA,CAAShB,IAAI,CAACS,KAAK,CAACwB,YAAY,CAAC,OAAAjB,MAAA,CAAIhB,IAAI,CAACS,KAAK,CAACwB,YAAY,CAAC;UAC3E;QACF,CAAC;QACD,KAAAjB,MAAA,CAAKN,YAAY,YAAS;UACxB,MAAAM,MAAA,CAAMN,YAAY,WAAAM,MAAA,CAAQN,YAAY,YAAS;YAC7CuB,YAAY,KAAAjB,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACwB,YAAY,CAAC,OAAAjB,MAAA,CAAIhB,IAAI,CAACS,KAAK,CAACwB,YAAY,CAAC;UACvE;QACF,CAAC;QACD,KAAAjB,MAAA,CAAKN,YAAY,cAAW;UAC1B,MAAAM,MAAA,CAAMN,YAAY,WAAAM,MAAA,CAAQN,YAAY,YAAS;YAC7CuB,YAAY,EAAE;cACZF,YAAY,EAAE,IAAI;cAClBC,KAAK,OAAAhB,MAAA,CAAOhB,IAAI,CAACS,KAAK,CAACwB,YAAY,CAAC,OAAAjB,MAAA,CAAIhB,IAAI,CAACS,KAAK,CAACwB,YAAY,CAAC;YAClE;UACF;QACF,CAAC;QACD,KAAAjB,MAAA,CAAKN,YAAY,aAAU;UACzB,MAAAM,MAAA,CAAMN,YAAY,WAAAM,MAAA,CAAQN,YAAY,YAAS;YAC7CuB,YAAY,EAAE;cACZF,YAAY,EAAE,IAAI;cAClBC,KAAK,KAAAhB,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACwB,YAAY,CAAC,WAAAjB,MAAA,CAAQhB,IAAI,CAACS,KAAK,CAACwB,YAAY,CAAC;YACpE;UACF;QACF;MACF,CAAC;MACD;MACA,KAAAjB,MAAA,CAAKN,YAAY,cAAW;QAC1B,MAAAM,MAAA,CAAMN,YAAY,YAAS;UACzB,IAAAM,MAAA,CAAIN,YAAY,YAAS;YACvBQ,OAAO,EAAE4F;UACX,CAAC;UACD,IAAA9F,MAAA,CAAIN,YAAY,gBAAa;YAC3BoD,QAAQ,EAAEkD,YAAY;YACtBM,SAAS,EAAEN;UACb;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMO,WAAW,GAAG9G,KAAK,IAAI;EAC3B,MAAM;IACJC,YAAY;IACZ8G,eAAe;IACf/E,cAAc;IACdgF,OAAO;IACPC,wBAAwB;IACxBC,qBAAqB;IACrB5G,iBAAiB;IACjB6G;EACF,CAAC,GAAGnH,KAAK;EACT,MAAMoH,MAAM,MAAA7G,MAAA,CAAMN,YAAY,SAAM;EACpC,OAAO;IACL,CAACmH,MAAM,GAAG;MACRhF,QAAQ,EAAE,UAAU;MACpBiF,kBAAkB,EAAE,MAAM;MAC1BC,uBAAuB,EAAE,aAAa;MACtC7E,OAAO,EAAE,aAAa;MACtBW,UAAU,EAAE,QAAQ;MACpB3C,OAAO,EAAEyG,qBAAqB;MAC9BvD,QAAQ,EAAE3D,KAAK,CAACuH,aAAa;MAC7B7G,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE,CAAC;MACTQ,OAAO,EAAE,MAAM;MACf0C,MAAM,EAAE,SAAS;MACjB5C,KAAK,EAAEkG,SAAS;MAChB,iBAAiB,EAAE;QACjB,uCAAuC,EAAE;UACvClG,KAAK,EAAE8F;QACT;MACF,CAAC;MACD,OAAO,EAAE;QACP5F,OAAO,EAAE,MAAM;QACfL,UAAU,SAAAP,MAAA,CAASP,KAAK,CAACe,kBAAkB,CAAE;QAC7C,IAAAR,MAAA,CAAI6G,MAAM,8BAA2B;UACnCI,eAAe,EAAExH,KAAK,CAACgE;QACzB;MACF,CAAC;MACD,UAAU,EAAE9B,MAAM,CAACC,MAAM,CAAC;QACxB2B,IAAI,EAAE,MAAM;QACZkC,WAAW,EAAE;UACX1E,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEvB,KAAK,CAACyE,IAAI,CAACzE,KAAK,CAACyH,SAAS,CAAC,CAACjC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;QACnD,CAAC;QACDpE,UAAU,EAAE;UACVC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEvB,KAAK,CAAC0H;QACf,CAAC;QACDzG,KAAK,EAAEjB,KAAK,CAACiE,SAAS;QACtBN,QAAQ,EAAE3D,KAAK,CAACkE,UAAU;QAC1BxD,UAAU,EAAE,aAAa;QACzBC,MAAM,EAAE,MAAM;QACdQ,OAAO,EAAE,MAAM;QACf0C,MAAM,EAAE,SAAS;QACjB/C,UAAU,SAAAP,MAAA,CAASP,KAAK,CAACe,kBAAkB,CAAE;QAC7C,SAAS,EAAE;UACTE,KAAK,EAAEjB,KAAK,CAAC2H;QACf;MACF,CAAC,EAAElI,aAAa,CAACO,KAAK,CAAC,CAAC;MACxB,SAAS,EAAE;QACTiB,KAAK,EAAEe;MACT,CAAC;MACD,KAAAzB,MAAA,CAAK6G,MAAM,cAAA7G,MAAA,CAAW6G,MAAM,YAAS;QACnCnG,KAAK,EAAEX,iBAAiB;QACxBsH,UAAU,EAAE5H,KAAK,CAAC6H;MACpB,CAAC;MACD,KAAAtH,MAAA,CAAK6G,MAAM,aAAA7G,MAAA,CAAU6G,MAAM,0BAAuB5H,eAAe,CAACQ,KAAK,CAAC;MACxE,KAAAO,MAAA,CAAK6G,MAAM,iBAAc;QACvBnG,KAAK,EAAEjB,KAAK,CAACoE,iBAAiB;QAC9BP,MAAM,EAAE;MACV,CAAC;MACD,KAAAtD,MAAA,CAAK6G,MAAM,gBAAA7G,MAAA,CAAa6G,MAAM,aAAA7G,MAAA,CAAU6G,MAAM,gBAAA7G,MAAA,CAAaN,YAAY,eAAY;QACjF,mBAAmB,EAAE;UACnBgB,KAAK,EAAEjB,KAAK,CAACoE;QACf;MACF,CAAC;MACD,MAAA7D,MAAA,CAAM6G,MAAM,cAAA7G,MAAA,CAAWyG,OAAO,IAAK;QACjCxG,MAAM,EAAE;MACV,CAAC;MACD,IAAAD,MAAA,CAAIyG,OAAO,yBAAsB;QAC/BhB,WAAW,EAAE;UACX1E,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEvB,KAAK,CAACgE;QACf;MACF;IACF,CAAC;IACD,IAAAzD,MAAA,CAAI6G,MAAM,SAAA7G,MAAA,CAAM6G,MAAM,IAAK;MACzB5G,MAAM,EAAE;QACNc,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE0F;MACT;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMa,WAAW,GAAG9H,KAAK,IAAI;EAC3B,MAAM;IACJC,YAAY;IACZ8H,2BAA2B;IAC3Bf,OAAO;IACP5G,UAAU;IACVqE;EACF,CAAC,GAAGzE,KAAK;EACT,MAAMgI,MAAM,MAAAzH,MAAA,CAAMN,YAAY,SAAM;EACpC,OAAO;IACL,CAAC+H,MAAM,GAAG;MACRC,SAAS,EAAE,KAAK;MAChB,IAAA1H,MAAA,CAAIN,YAAY,YAAS;QACvB,IAAAM,MAAA,CAAIN,YAAY,YAAS;UACvBO,MAAM,EAAE;YACNc,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAEwG;UACT,CAAC;UACD,IAAAxH,MAAA,CAAIN,YAAY,yBAAsB;YACpCoB,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT;UACF,CAAC;UACD,CAACyF,OAAO,GAAG;YACThB,WAAW,EAAE;cACX1E,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDF,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEhC,IAAI,CAACS,KAAK,CAACgE,QAAQ;YAC5B;UACF,CAAC;UACD,IAAAzD,MAAA,CAAIN,YAAY,mBAAgB;YAC9B+F,WAAW,EAAE;cACX1E,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEhC,IAAI,CAACS,KAAK,CAAC0H,QAAQ;YAC5B,CAAC;YACDrG,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEhC,IAAI,CAACkF,IAAI,CAACzE,KAAK,CAACyH,SAAS,CAAC,CAACjC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACnD,CAAC;YACD,CAACuB,OAAO,GAAG;cACTxG,MAAM,EAAE;YACV;UACF;QACF;MACF,CAAC;MACD,KAAAD,MAAA,CAAKN,YAAY,aAAU;QACzB,MAAAM,MAAA,CAAMN,YAAY,YAAS;UACzBqF,KAAK,EAAE;QACT,CAAC;QACD,MAAA/E,MAAA,CAAMN,YAAY,uBAAoB;UACpCqF,KAAK,EAAE;QACT;MACF,CAAC;MACD,KAAA/E,MAAA,CAAKN,YAAY,cAAW;QAC1B,MAAAM,MAAA,CAAMN,YAAY,YAAS;UACzBqF,KAAK,EAAE;QACT,CAAC;QACD,MAAA/E,MAAA,CAAMN,YAAY,uBAAoB;UACpCqF,KAAK,EAAE;QACT;MACF,CAAC;MACD;MACA,KAAA/E,MAAA,CAAKN,YAAY,WAAAM,MAAA,CAAQN,YAAY,aAAAM,MAAA,CAAUN,YAAY,WAAAM,MAAA,CAAQN,YAAY,eAAY;QACzF,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;UACtD,IAAAM,MAAA,CAAIN,YAAY,aAAAM,MAAA,CAAUN,YAAY,YAAS;YAC7C+F,WAAW,EAAE;cACX1E,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEnB;YACT,CAAC;YACDiB,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT;UACF;QACF;MACF;IACF,CAAC;IACD,IAAAhB,MAAA,CAAIN,YAAY,qBAAkB;MAChCgI,SAAS,EAAE;IACb,CAAC;IACD,IAAA1H,MAAA,CAAIN,YAAY,kBAAe;MAC7B,IAAAM,MAAA,CAAIN,YAAY,qBAAkB;QAChC6C,SAAS,EAAE;UACTxB,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAM2G,YAAY,GAAGlI,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,eAAe;IACfiI,UAAU;IACV/H,UAAU;IACV4B,cAAc;IACd+E,eAAe;IACf1G;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAGiC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzC,cAAc,CAACM,KAAK,CAAC,CAAC,EAAE;MAClGyC,OAAO,EAAE,MAAM;MACf;MACA,MAAAlC,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;QACtDmC,QAAQ,EAAE,UAAU;QACpBK,OAAO,EAAE,MAAM;QACfqB,IAAI,EAAE,MAAM;QACZV,UAAU,EAAE,QAAQ;QACpB,IAAA7C,MAAA,CAAIN,YAAY,iBAAc;UAC5BmC,QAAQ,EAAE,UAAU;UACpBK,OAAO,EAAE,MAAM;UACfqB,IAAI,EAAE,MAAM;UACZsE,SAAS,EAAE,SAAS;UACpBC,QAAQ,EAAE,QAAQ;UAClBtE,UAAU,EAAE,QAAQ;UACpBuE,SAAS,EAAE,cAAc;UACzB;UACA;UACA,qBAAqB,EAAE;YACrBlG,QAAQ,EAAE,UAAU;YACpBG,MAAM,EAAE,CAAC;YACT8C,OAAO,EAAE,CAAC;YACVvE,UAAU,aAAAP,MAAA,CAAaP,KAAK,CAACe,kBAAkB,CAAE;YACjD8D,OAAO,EAAE,IAAI;YACb0D,aAAa,EAAE;UACjB;QACF,CAAC;QACD,IAAAhI,MAAA,CAAIN,YAAY,iBAAc;UAC5BmC,QAAQ,EAAE,UAAU;UACpBK,OAAO,EAAE,MAAM;UACf3B,UAAU,aAAAP,MAAA,CAAaP,KAAK,CAACe,kBAAkB;QACjD,CAAC;QACD;QACA,IAAAR,MAAA,CAAIN,YAAY,uBAAoB;UAClCwC,OAAO,EAAE,MAAM;UACf2F,SAAS,EAAE;QACb,CAAC;QACD,IAAA7H,MAAA,CAAIN,YAAY,8BAA2B;UACzCmC,QAAQ,EAAE,UAAU;UACpBhB,UAAU,EAAE,QAAQ;UACpBmH,aAAa,EAAE;QACjB,CAAC;QACD,IAAAhI,MAAA,CAAIN,YAAY,iBAAc;UAC5BmC,QAAQ,EAAE,UAAU;UACpB3B,OAAO,EAAEP,eAAe;UACxBQ,UAAU,EAAE,aAAa;UACzBC,MAAM,EAAE,CAAC;UACTM,KAAK,EAAEjB,KAAK,CAACyD,SAAS;UACtB,UAAU,EAAE;YACVrB,QAAQ,EAAE,UAAU;YACpBuC,KAAK,EAAE;cACLrD,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDyD,MAAM,EAAE,CAAC;YACT1C,IAAI,EAAE;cACJhB,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDuD,MAAM,EAAE9E,KAAK,CAACyE,IAAI,CAACzE,KAAK,CAACwI,eAAe,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAChD,KAAK,CAAC,CAAC;YACxD6C,SAAS,EAAE,kBAAkB;YAC7BzD,OAAO,EAAE;UACX;QACF,CAAC;QACD,IAAAtE,MAAA,CAAIN,YAAY,gBAAaiC,MAAM,CAACC,MAAM,CAAC;UACzCkB,QAAQ,EAAE8E,UAAU;UACpBtB,SAAS,EAAEsB,UAAU;UACrB9G,UAAU,EAAE;YACVC,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAEnB;UACT,CAAC;UACDM,UAAU,EAAE,aAAa;UACzBC,MAAM,KAAAJ,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACY,SAAS,CAAC,OAAAL,MAAA,CAAIP,KAAK,CAACa,QAAQ,OAAAN,MAAA,CAAIF,oBAAoB,CAAE;UAC5EmB,YAAY,KAAAjB,MAAA,CAAKhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC,OAAAlB,MAAA,CAAIhB,IAAI,CAACS,KAAK,CAACyB,cAAc,CAAC,SAAM;UAC/EN,OAAO,EAAE,MAAM;UACf0C,MAAM,EAAE,SAAS;UACjB5C,KAAK,EAAEjB,KAAK,CAACyD,SAAS;UACtB3C,UAAU,SAAAP,MAAA,CAASP,KAAK,CAACe,kBAAkB,OAAAR,MAAA,CAAIP,KAAK,CAACgB,eAAe,CAAE;UACtE,SAAS,EAAE;YACTC,KAAK,EAAEe;UACT,CAAC;UACD,uCAAuC,EAAE;YACvCf,KAAK,EAAE8F;UACT;QACF,CAAC,EAAEtH,aAAa,CAACO,KAAK,EAAE,CAAC,CAAC,CAAC;MAC7B,CAAC;MACD,IAAAO,MAAA,CAAIN,YAAY,sBAAmB;QACjC6D,IAAI,EAAE;MACR,CAAC;MACD;MACA,IAAAvD,MAAA,CAAIN,YAAY,gBAAa;QAC3BmC,QAAQ,EAAE,UAAU;QACpB1B,UAAU,EAAEV,KAAK,CAAC0I,WAAW;QAC7BH,aAAa,EAAE;MACjB;IACF,CAAC,CAAC,EAAEzB,WAAW,CAAC9G,KAAK,CAAC,CAAC,EAAE;MACvB;MACA,IAAAO,MAAA,CAAIN,YAAY,gBAAa;QAC3BmC,QAAQ,EAAE,UAAU;QACpB6C,KAAK,EAAE;MACT,CAAC;MACD,IAAA1E,MAAA,CAAIN,YAAY,uBAAoB;QAClC6D,IAAI,EAAE,MAAM;QACZT,QAAQ,EAAE,CAAC;QACXwD,SAAS,EAAE;MACb,CAAC;MACD,IAAAtG,MAAA,CAAIN,YAAY,gBAAaiC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1C,aAAa,CAACO,KAAK,CAAC,CAAC,EAAE;QAClF,UAAU,EAAE;UACVyC,OAAO,EAAE;QACX;MACF,CAAC;IACH,CAAC,CAAC;IACF,IAAAlC,MAAA,CAAIN,YAAY,iBAAc;MAC5B,MAAAM,MAAA,CAAMN,YAAY,oBAAAM,MAAA,CAAiBN,YAAY,YAAS;QACtD,IAAAM,MAAA,CAAIN,YAAY,iBAAc;UAC5B,mBAAAM,MAAA,CAAmBN,YAAY,0BAAAM,MAAA,CAAuBN,YAAY,iBAAc;YAC9EO,MAAM,EAAE;UACV;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMmI,qBAAqB,GAAG3I,KAAK,IAAI;EAC5C,MAAM;IACJmI,UAAU;IACV7B,YAAY;IACZC,YAAY;IACZrB,aAAa;IACbsD;EACF,CAAC,GAAGxI,KAAK;EACT,MAAM4I,gBAAgB,GAAGT,UAAU,IAAIK,eAAe;EACtD,MAAMK,kBAAkB,GAAGvC,YAAY,IAAIpB,aAAa;EACxD;EACA,MAAM4D,kBAAkB,GAAGvC,YAAY,IAAIiC,eAAe,GAAG,CAAC;EAC9D,OAAO;IACLhG,WAAW,EAAExC,KAAK,CAAC+I,eAAe,GAAG,EAAE;IACvC5I,MAAM,EAAEH,KAAK,CAACgJ,cAAc;IAC5B;IACA;IACAb,UAAU,EAAES,gBAAgB;IAC5BtC,YAAY,EAAEuC,kBAAkB;IAChCtC,YAAY,EAAEuC,kBAAkB;IAChC;IACAG,WAAW,KAAA1I,MAAA,CAAK,CAACqI,gBAAgB,GAAG5I,KAAK,CAACkJ,UAAU,IAAI,CAAC,GAAGlJ,KAAK,CAACY,SAAS,SAAAL,MAAA,CAAMP,KAAK,CAACS,OAAO,OAAI;IAClG2F,aAAa,KAAA7F,MAAA,CAAK,CAACsI,kBAAkB,GAAG7I,KAAK,CAACkJ,UAAU,IAAI,CAAC,GAAGlJ,KAAK,CAACY,SAAS,SAAAL,MAAA,CAAMP,KAAK,CAACmJ,SAAS,OAAI;IACxG9C,aAAa,KAAA9F,MAAA,CAAK,CAACuI,kBAAkB,GAAG9I,KAAK,CAACoJ,YAAY,IAAI,CAAC,GAAGpJ,KAAK,CAACY,SAAS,SAAAL,MAAA,CAAMP,KAAK,CAACS,OAAO,OAAI;IACxG8G,aAAa,EAAEvH,KAAK,CAAC2D,QAAQ;IAC7BgD,eAAe,EAAE3G,KAAK,CAACqJ,UAAU;IACjC3C,eAAe,EAAE1G,KAAK,CAAC2D,QAAQ;IAC/B+E,WAAW,EAAE1I,KAAK,CAACsJ,YAAY;IAC/BhF,gBAAgB,SAAA/D,MAAA,CAASP,KAAK,CAACQ,MAAM,SAAM;IAC3C+I,oBAAoB,EAAE,EAAE;IACxB;IACA;IACAC,oBAAoB,IAAI;IACxBC,uBAAuB,IAAI;IAC3BvC,qBAAqB,KAAA3G,MAAA,CAAKP,KAAK,CAACwD,SAAS,SAAM;IAC/CgD,uBAAuB,KAAAjG,MAAA,CAAKP,KAAK,CAACmJ,SAAS,SAAM;IACjD1C,uBAAuB,KAAAlG,MAAA,CAAKP,KAAK,CAACS,OAAO,SAAM;IAC/C8D,mBAAmB,KAAAhE,MAAA,CAAKP,KAAK,CAACmJ,SAAS,SAAA5I,MAAA,CAAMP,KAAK,CAAC+F,SAAS,OAAI;IAChEvB,kBAAkB,KAAAjE,MAAA,CAAKP,KAAK,CAACQ,MAAM,aAAU;IAC7C2G,SAAS,EAAEnH,KAAK,CAACyD,SAAS;IAC1BnD,iBAAiB,EAAEN,KAAK,CAACsJ,YAAY;IACrCtH,cAAc,EAAEhC,KAAK,CAAC0J,iBAAiB;IACvC3C,eAAe,EAAE/G,KAAK,CAAC2J,kBAAkB;IACzCvJ,UAAU,EAAEJ,KAAK,CAACyH,SAAS,GAAG;EAChC,CAAC;AACH,CAAC;AACD;AACA,eAAe7H,aAAa,CAAC,MAAM,EAAEI,KAAK,IAAI;EAC5C,MAAM4J,SAAS,GAAG/J,UAAU,CAACG,KAAK,EAAE;IAClC;IACAE,eAAe,EAAEF,KAAK,CAACiJ,WAAW;IAClChH,gCAAgC,EAAEjC,KAAK,CAACuD,UAAU;IAClDsE,oBAAoB,EAAE,yBAAyB;IAC/ClF,kBAAkB,EAAE,GAAG;IACvBW,iBAAiB,EAAE,GAAG;IACtB2D,wBAAwB,WAAA1G,MAAA,CAAWhB,IAAI,CAACS,KAAK,CAACuJ,oBAAoB,CAAC,CAAE;IACrExB,2BAA2B,WAAAxH,MAAA,CAAWhB,IAAI,CAACS,KAAK,CAACuJ,oBAAoB,CAAC;EACxE,CAAC,CAAC;EACF,OAAO,CAACpD,YAAY,CAACyD,SAAS,CAAC,EAAE9B,WAAW,CAAC8B,SAAS,CAAC,EAAEvF,gBAAgB,CAACuF,SAAS,CAAC,EAAE7H,gBAAgB,CAAC6H,SAAS,CAAC,EAAE7J,YAAY,CAAC6J,SAAS,CAAC,EAAE1B,YAAY,CAAC0B,SAAS,CAAC,EAAE9J,cAAc,CAAC8J,SAAS,CAAC,CAAC;AACjM,CAAC,EAAEjB,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
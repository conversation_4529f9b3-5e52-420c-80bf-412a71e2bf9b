{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"value\", \"valueIndex\", \"onStartMove\", \"onDelete\", \"style\", \"render\", \"dragging\", \"draggingDelete\", \"onOffsetChange\", \"onChangeComplete\", \"onFocus\", \"onMouseEnter\"];\nimport cls from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getDirectionStyle, getIndex } from \"../util\";\nvar Handle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    valueIndex = props.valueIndex,\n    onStartMove = props.onStartMove,\n    onDelete = props.onDelete,\n    style = props.style,\n    render = props.render,\n    dragging = props.dragging,\n    draggingDelete = props.draggingDelete,\n    onOffsetChange = props.onOffsetChange,\n    onChangeComplete = props.onChangeComplete,\n    onFocus = props.onFocus,\n    onMouseEnter = props.onMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    disabled = _React$useContext.disabled,\n    keyboard = _React$useContext.keyboard,\n    range = _React$useContext.range,\n    tabIndex = _React$useContext.tabIndex,\n    ariaLabelForHandle = _React$useContext.ariaLabelForHandle,\n    ariaLabelledByForHandle = _React$useContext.ariaLabelledByForHandle,\n    ariaRequired = _React$useContext.ariaRequired,\n    ariaValueTextFormatterForHandle = _React$useContext.ariaValueTextFormatterForHandle,\n    styles = _React$useContext.styles,\n    classNames = _React$useContext.classNames;\n  var handlePrefixCls = \"\".concat(prefixCls, \"-handle\");\n\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled) {\n      onStartMove(e, valueIndex);\n    }\n  };\n  var onInternalFocus = function onInternalFocus(e) {\n    onFocus === null || onFocus === void 0 || onFocus(e, valueIndex);\n  };\n  var onInternalMouseEnter = function onInternalMouseEnter(e) {\n    onMouseEnter(e, valueIndex);\n  };\n\n  // =========================== Keyboard ===========================\n  var onKeyDown = function onKeyDown(e) {\n    if (!disabled && keyboard) {\n      var offset = null;\n\n      // Change the value\n      switch (e.which || e.keyCode) {\n        case KeyCode.LEFT:\n          offset = direction === 'ltr' || direction === 'btt' ? -1 : 1;\n          break;\n        case KeyCode.RIGHT:\n          offset = direction === 'ltr' || direction === 'btt' ? 1 : -1;\n          break;\n\n        // Up is plus\n        case KeyCode.UP:\n          offset = direction !== 'ttb' ? 1 : -1;\n          break;\n\n        // Down is minus\n        case KeyCode.DOWN:\n          offset = direction !== 'ttb' ? -1 : 1;\n          break;\n        case KeyCode.HOME:\n          offset = 'min';\n          break;\n        case KeyCode.END:\n          offset = 'max';\n          break;\n        case KeyCode.PAGE_UP:\n          offset = 2;\n          break;\n        case KeyCode.PAGE_DOWN:\n          offset = -2;\n          break;\n        case KeyCode.BACKSPACE:\n        case KeyCode.DELETE:\n          onDelete(valueIndex);\n          break;\n      }\n      if (offset !== null) {\n        e.preventDefault();\n        onOffsetChange(offset, valueIndex);\n      }\n    }\n  };\n  var handleKeyUp = function handleKeyUp(e) {\n    switch (e.which || e.keyCode) {\n      case KeyCode.LEFT:\n      case KeyCode.RIGHT:\n      case KeyCode.UP:\n      case KeyCode.DOWN:\n      case KeyCode.HOME:\n      case KeyCode.END:\n      case KeyCode.PAGE_UP:\n      case KeyCode.PAGE_DOWN:\n        onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete();\n        break;\n    }\n  };\n\n  // ============================ Offset ============================\n  var positionStyle = getDirectionStyle(direction, value, min, max);\n\n  // ============================ Render ============================\n  var divProps = {};\n  if (valueIndex !== null) {\n    var _getIndex;\n    divProps = {\n      tabIndex: disabled ? null : getIndex(tabIndex, valueIndex),\n      role: 'slider',\n      'aria-valuemin': min,\n      'aria-valuemax': max,\n      'aria-valuenow': value,\n      'aria-disabled': disabled,\n      'aria-label': getIndex(ariaLabelForHandle, valueIndex),\n      'aria-labelledby': getIndex(ariaLabelledByForHandle, valueIndex),\n      'aria-required': getIndex(ariaRequired, valueIndex),\n      'aria-valuetext': (_getIndex = getIndex(ariaValueTextFormatterForHandle, valueIndex)) === null || _getIndex === void 0 ? void 0 : _getIndex(value),\n      'aria-orientation': direction === 'ltr' || direction === 'rtl' ? 'horizontal' : 'vertical',\n      onMouseDown: onInternalStartMove,\n      onTouchStart: onInternalStartMove,\n      onFocus: onInternalFocus,\n      onMouseEnter: onInternalMouseEnter,\n      onKeyDown: onKeyDown,\n      onKeyUp: handleKeyUp\n    };\n  }\n  var handleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: cls(handlePrefixCls, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(handlePrefixCls, \"-\").concat(valueIndex + 1), valueIndex !== null && range), \"\".concat(handlePrefixCls, \"-dragging\"), dragging), \"\".concat(handlePrefixCls, \"-dragging-delete\"), draggingDelete), classNames.handle),\n    style: _objectSpread(_objectSpread(_objectSpread({}, positionStyle), style), styles.handle)\n  }, divProps, restProps));\n\n  // Customize\n  if (render) {\n    handleNode = render(handleNode, {\n      index: valueIndex,\n      prefixCls: prefixCls,\n      value: value,\n      dragging: dragging,\n      draggingDelete: draggingDelete\n    });\n  }\n  return handleNode;\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handle.displayName = 'Handle';\n}\nexport default Handle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * What's new?\n * - Common\n *  - [Break] Support special year format, all the year will follow the locale config.\n *  - Blur all of field will trigger `onChange` if validate\n *  - Support `preserveInvalidOnBlur` to not to clean input if invalid and remove `changeOnBlur`\n *  - `pickerValue` is now full controlled\n *    - `defaultPickerValue` will take effect on every field active with popup opening.\n *  - [Break] clear button return the event with `onClick`\n *\n * - Locale\n *  - Remove `dateFormat` since it's never used\n *  - Remove `dateTimeFormat` since it's never used\n *\n * - Picker\n *  - TimePicker support `changeOnScroll`\n *  - TimePicker support `millisecond`\n *  - Support cellMeridiemFormat for AM/PM\n *  - Get correct `disabledHours` when set `use12Hours`\n *  - Support `showWeek`\n *\n * - RangePicker\n *  - [Break] RangePicker is now not limit the range of clicked field.\n *  - Trigger `onCalendarChange` when type correct\n *  - [Break] Not order `value` if given `value` is wrong order.\n *  - Hover `presets` will show date in input field.\n *  - [Break] RangePicker go to end field, `pickerValue` will follow the start field if not controlled.\n */\n\nimport RangePicker from \"./PickerInput/RangePicker\";\nimport Picker from \"./PickerInput/SinglePicker\";\nimport PickerPanel from \"./PickerPanel\";\nexport { Picker, RangePicker, PickerPanel };\nexport default Picker;", "map": {"version": 3, "names": ["RangePicker", "Picker", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/rc-picker/es/index.js"], "sourcesContent": ["/**\n * What's new?\n * - Common\n *  - [Break] Support special year format, all the year will follow the locale config.\n *  - Blur all of field will trigger `onChange` if validate\n *  - Support `preserveInvalidOnBlur` to not to clean input if invalid and remove `changeOnBlur`\n *  - `pickerValue` is now full controlled\n *    - `defaultPickerValue` will take effect on every field active with popup opening.\n *  - [Break] clear button return the event with `onClick`\n *\n * - Locale\n *  - Remove `dateFormat` since it's never used\n *  - Remove `dateTimeFormat` since it's never used\n *\n * - Picker\n *  - TimePicker support `changeOnScroll`\n *  - TimePicker support `millisecond`\n *  - Support cellMeridiemFormat for AM/PM\n *  - Get correct `disabledHours` when set `use12Hours`\n *  - Support `showWeek`\n *\n * - RangePicker\n *  - [Break] RangePicker is now not limit the range of clicked field.\n *  - Trigger `onCalendarChange` when type correct\n *  - [Break] Not order `value` if given `value` is wrong order.\n *  - Hover `presets` will show date in input field.\n *  - [Break] RangePicker go to end field, `pickerValue` will follow the start field if not controlled.\n */\n\nimport RangePicker from \"./PickerInput/RangePicker\";\nimport Picker from \"./PickerInput/SinglePicker\";\nimport PickerPanel from \"./PickerPanel\";\nexport { Picker, RangePicker, PickerPanel };\nexport default Picker;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,WAAW,MAAM,2BAA2B;AACnD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASD,MAAM,EAAED,WAAW,EAAEE,WAAW;AACzC,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
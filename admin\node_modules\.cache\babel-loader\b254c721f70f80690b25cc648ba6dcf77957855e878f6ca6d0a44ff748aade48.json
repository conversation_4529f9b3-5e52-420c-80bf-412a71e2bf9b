{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genCollapseMotion, zoomIn } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genFormValidateMotionStyle from './explain';\nconst resetForm = token => ({\n  legend: {\n    display: 'block',\n    width: '100%',\n    marginBottom: token.marginLG,\n    padding: 0,\n    color: token.colorTextDescription,\n    fontSize: token.fontSizeLG,\n    lineHeight: 'inherit',\n    border: 0,\n    borderBottom: \"\".concat(unit(token.lineWidth), \" \").concat(token.lineType, \" \").concat(token.colorBorder)\n  },\n  'input[type=\"search\"]': {\n    boxSizing: 'border-box'\n  },\n  // Position radios and checkboxes better\n  'input[type=\"radio\"], input[type=\"checkbox\"]': {\n    lineHeight: 'normal'\n  },\n  'input[type=\"file\"]': {\n    display: 'block'\n  },\n  // Make range inputs behave like textual form controls\n  'input[type=\"range\"]': {\n    display: 'block',\n    width: '100%'\n  },\n  // Make multiple select elements height not fixed\n  'select[multiple], select[size]': {\n    height: 'auto'\n  },\n  // Focus for file, radio, and checkbox\n  [\"input[type='file']:focus,\\n  input[type='radio']:focus,\\n  input[type='checkbox']:focus\"]: {\n    outline: 0,\n    boxShadow: \"0 0 0 \".concat(unit(token.controlOutlineWidth), \" \").concat(token.controlOutline)\n  },\n  // Adjust output element\n  output: {\n    display: 'block',\n    paddingTop: 15,\n    color: token.colorText,\n    fontSize: token.fontSize,\n    lineHeight: token.lineHeight\n  }\n});\nconst genFormSize = (token, height) => {\n  const {\n    formItemCls\n  } = token;\n  return {\n    [formItemCls]: {\n      [\"\".concat(formItemCls, \"-label > label\")]: {\n        height\n      },\n      [\"\".concat(formItemCls, \"-control-input\")]: {\n        minHeight: height\n      }\n    }\n  };\n};\nconst genFormStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [token.componentCls]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), resetForm(token)), {\n      [\"\".concat(componentCls, \"-text\")]: {\n        display: 'inline-block',\n        paddingInlineEnd: token.paddingSM\n      },\n      // ================================================================\n      // =                             Size                             =\n      // ================================================================\n      '&-small': Object.assign({}, genFormSize(token, token.controlHeightSM)),\n      '&-large': Object.assign({}, genFormSize(token, token.controlHeightLG))\n    })\n  };\n};\nconst genFormItemStyle = token => {\n  const {\n    formItemCls,\n    iconCls,\n    rootPrefixCls,\n    antCls,\n    labelRequiredMarkColor,\n    labelColor,\n    labelFontSize,\n    labelHeight,\n    labelColonMarginInlineStart,\n    labelColonMarginInlineEnd,\n    itemMarginBottom\n  } = token;\n  return {\n    [formItemCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      marginBottom: itemMarginBottom,\n      verticalAlign: 'top',\n      '&-with-help': {\n        transition: 'none'\n      },\n      [\"&-hidden,\\n        &-hidden\".concat(antCls, \"-row\")]: {\n        // https://github.com/ant-design/ant-design/issues/26141\n        display: 'none'\n      },\n      '&-has-warning': {\n        [\"\".concat(formItemCls, \"-split\")]: {\n          color: token.colorError\n        }\n      },\n      '&-has-error': {\n        [\"\".concat(formItemCls, \"-split\")]: {\n          color: token.colorWarning\n        }\n      },\n      // ==============================================================\n      // =                            Label                           =\n      // ==============================================================\n      [\"\".concat(formItemCls, \"-label\")]: {\n        flexGrow: 0,\n        overflow: 'hidden',\n        whiteSpace: 'nowrap',\n        textAlign: 'end',\n        verticalAlign: 'middle',\n        '&-left': {\n          textAlign: 'start'\n        },\n        '&-wrap': {\n          overflow: 'unset',\n          lineHeight: token.lineHeight,\n          whiteSpace: 'unset',\n          '> label': {\n            verticalAlign: 'middle',\n            textWrap: 'balance'\n          }\n        },\n        '> label': {\n          position: 'relative',\n          display: 'inline-flex',\n          alignItems: 'center',\n          maxWidth: '100%',\n          height: labelHeight,\n          color: labelColor,\n          fontSize: labelFontSize,\n          [\"> \".concat(iconCls)]: {\n            fontSize: token.fontSize,\n            verticalAlign: 'top'\n          },\n          [\"&\".concat(formItemCls, \"-required\")]: {\n            '&::before': {\n              display: 'inline-block',\n              marginInlineEnd: token.marginXXS,\n              color: labelRequiredMarkColor,\n              fontSize: token.fontSize,\n              fontFamily: 'SimSun, sans-serif',\n              lineHeight: 1,\n              content: '\"*\"'\n            },\n            [\"&\".concat(formItemCls, \"-required-mark-hidden, &\").concat(formItemCls, \"-required-mark-optional\")]: {\n              '&::before': {\n                display: 'none'\n              }\n            }\n          },\n          // Optional mark\n          [\"\".concat(formItemCls, \"-optional\")]: {\n            display: 'inline-block',\n            marginInlineStart: token.marginXXS,\n            color: token.colorTextDescription,\n            [\"&\".concat(formItemCls, \"-required-mark-hidden\")]: {\n              display: 'none'\n            }\n          },\n          // Optional mark\n          [\"\".concat(formItemCls, \"-tooltip\")]: {\n            color: token.colorTextDescription,\n            cursor: 'help',\n            writingMode: 'horizontal-tb',\n            marginInlineStart: token.marginXXS\n          },\n          '&::after': {\n            content: '\":\"',\n            position: 'relative',\n            marginBlock: 0,\n            marginInlineStart: labelColonMarginInlineStart,\n            marginInlineEnd: labelColonMarginInlineEnd\n          },\n          [\"&\".concat(formItemCls, \"-no-colon::after\")]: {\n            content: '\"\\\\a0\"'\n          }\n        }\n      },\n      // ==============================================================\n      // =                            Input                           =\n      // ==============================================================\n      [\"\".concat(formItemCls, \"-control\")]: {\n        ['--ant-display']: 'flex',\n        flexDirection: 'column',\n        flexGrow: 1,\n        [\"&:first-child:not([class^=\\\"'\".concat(rootPrefixCls, \"-col-'\\\"]):not([class*=\\\"' \").concat(rootPrefixCls, \"-col-'\\\"])\")]: {\n          width: '100%'\n        },\n        '&-input': {\n          position: 'relative',\n          display: 'flex',\n          alignItems: 'center',\n          minHeight: token.controlHeight,\n          '&-content': {\n            flex: 'auto',\n            maxWidth: '100%'\n          }\n        }\n      },\n      // ==============================================================\n      // =                           Explain                          =\n      // ==============================================================\n      [formItemCls]: {\n        '&-additional': {\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        '&-explain, &-extra': {\n          clear: 'both',\n          color: token.colorTextDescription,\n          fontSize: token.fontSize,\n          lineHeight: token.lineHeight\n        },\n        '&-explain-connected': {\n          width: '100%'\n        },\n        '&-extra': {\n          minHeight: token.controlHeightSM,\n          transition: \"color \".concat(token.motionDurationMid, \" \").concat(token.motionEaseOut) // sync input color transition\n        },\n        '&-explain': {\n          '&-error': {\n            color: token.colorError\n          },\n          '&-warning': {\n            color: token.colorWarning\n          }\n        }\n      },\n      [\"&-with-help \".concat(formItemCls, \"-explain\")]: {\n        height: 'auto',\n        opacity: 1\n      },\n      // ==============================================================\n      // =                        Feedback Icon                       =\n      // ==============================================================\n      [\"\".concat(formItemCls, \"-feedback-icon\")]: {\n        fontSize: token.fontSize,\n        textAlign: 'center',\n        visibility: 'visible',\n        animationName: zoomIn,\n        animationDuration: token.motionDurationMid,\n        animationTimingFunction: token.motionEaseOutBack,\n        pointerEvents: 'none',\n        '&-success': {\n          color: token.colorSuccess\n        },\n        '&-error': {\n          color: token.colorError\n        },\n        '&-warning': {\n          color: token.colorWarning\n        },\n        '&-validating': {\n          color: token.colorPrimary\n        }\n      }\n    })\n  };\n};\nconst genHorizontalStyle = (token, className) => {\n  const {\n    formItemCls\n  } = token;\n  return {\n    [\"\".concat(className, \"-horizontal\")]: {\n      [\"\".concat(formItemCls, \"-label\")]: {\n        flexGrow: 0\n      },\n      [\"\".concat(formItemCls, \"-control\")]: {\n        flex: '1 1 0',\n        // https://github.com/ant-design/ant-design/issues/32777\n        // https://github.com/ant-design/ant-design/issues/33773\n        minWidth: 0\n      },\n      // Do not change this to `ant-col-24`! `-24` match all the responsive rules\n      // https://github.com/ant-design/ant-design/issues/32980\n      // https://github.com/ant-design/ant-design/issues/34903\n      // https://github.com/ant-design/ant-design/issues/44538\n      [\"\".concat(formItemCls, \"-label[class$='-24'], \").concat(formItemCls, \"-label[class*='-24 ']\")]: {\n        [\"& + \".concat(formItemCls, \"-control\")]: {\n          minWidth: 'unset'\n        }\n      }\n    }\n  };\n};\nconst genInlineStyle = token => {\n  const {\n    componentCls,\n    formItemCls,\n    inlineItemMarginBottom\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-inline\")]: {\n      display: 'flex',\n      flexWrap: 'wrap',\n      [formItemCls]: {\n        flex: 'none',\n        marginInlineEnd: token.margin,\n        marginBottom: inlineItemMarginBottom,\n        '&-row': {\n          flexWrap: 'nowrap'\n        },\n        [\"> \".concat(formItemCls, \"-label,\\n        > \").concat(formItemCls, \"-control\")]: {\n          display: 'inline-block',\n          verticalAlign: 'top'\n        },\n        [\"> \".concat(formItemCls, \"-label\")]: {\n          flex: 'none'\n        },\n        [\"\".concat(componentCls, \"-text\")]: {\n          display: 'inline-block'\n        },\n        [\"\".concat(formItemCls, \"-has-feedback\")]: {\n          display: 'inline-block'\n        }\n      }\n    }\n  };\n};\nconst makeVerticalLayoutLabel = token => ({\n  padding: token.verticalLabelPadding,\n  margin: token.verticalLabelMargin,\n  whiteSpace: 'initial',\n  textAlign: 'start',\n  '> label': {\n    margin: 0,\n    '&::after': {\n      // https://github.com/ant-design/ant-design/issues/43538\n      visibility: 'hidden'\n    }\n  }\n});\nconst makeVerticalLayout = token => {\n  const {\n    componentCls,\n    formItemCls,\n    rootPrefixCls\n  } = token;\n  return {\n    [\"\".concat(formItemCls, \" \").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token),\n    // ref: https://github.com/ant-design/ant-design/issues/45122\n    [\"\".concat(componentCls, \":not(\").concat(componentCls, \"-inline)\")]: {\n      [formItemCls]: {\n        flexWrap: 'wrap',\n        [\"\".concat(formItemCls, \"-label, \").concat(formItemCls, \"-control\")]: {\n          // When developer pass `xs: { span }`,\n          // It should follow the `xs` screen config\n          // ref: https://github.com/ant-design/ant-design/issues/44386\n          [\"&:not([class*=\\\" \".concat(rootPrefixCls, \"-col-xs\\\"])\")]: {\n            flex: '0 0 100%',\n            maxWidth: '100%'\n          }\n        }\n      }\n    }\n  };\n};\nconst genVerticalStyle = token => {\n  const {\n    componentCls,\n    formItemCls,\n    antCls\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-vertical\")]: {\n      [\"\".concat(formItemCls, \":not(\").concat(formItemCls, \"-horizontal)\")]: {\n        [\"\".concat(formItemCls, \"-row\")]: {\n          flexDirection: 'column'\n        },\n        [\"\".concat(formItemCls, \"-label > label\")]: {\n          height: 'auto'\n        },\n        [\"\".concat(formItemCls, \"-control\")]: {\n          width: '100%'\n        },\n        [\"\".concat(formItemCls, \"-label,\\n        \").concat(antCls, \"-col-24\").concat(formItemCls, \"-label,\\n        \").concat(antCls, \"-col-xl-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n      }\n    },\n    [\"@media (max-width: \".concat(unit(token.screenXSMax), \")\")]: [makeVerticalLayout(token), {\n      [componentCls]: {\n        [\"\".concat(formItemCls, \":not(\").concat(formItemCls, \"-horizontal)\")]: {\n          [\"\".concat(antCls, \"-col-xs-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n        }\n      }\n    }],\n    [\"@media (max-width: \".concat(unit(token.screenSMMax), \")\")]: {\n      [componentCls]: {\n        [\"\".concat(formItemCls, \":not(\").concat(formItemCls, \"-horizontal)\")]: {\n          [\"\".concat(antCls, \"-col-sm-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n        }\n      }\n    },\n    [\"@media (max-width: \".concat(unit(token.screenMDMax), \")\")]: {\n      [componentCls]: {\n        [\"\".concat(formItemCls, \":not(\").concat(formItemCls, \"-horizontal)\")]: {\n          [\"\".concat(antCls, \"-col-md-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n        }\n      }\n    },\n    [\"@media (max-width: \".concat(unit(token.screenLGMax), \")\")]: {\n      [componentCls]: {\n        [\"\".concat(formItemCls, \":not(\").concat(formItemCls, \"-horizontal)\")]: {\n          [\"\".concat(antCls, \"-col-lg-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n        }\n      }\n    }\n  };\n};\nconst genItemVerticalStyle = token => {\n  const {\n    formItemCls,\n    antCls\n  } = token;\n  return {\n    [\"\".concat(formItemCls, \"-vertical\")]: {\n      [\"\".concat(formItemCls, \"-row\")]: {\n        flexDirection: 'column'\n      },\n      [\"\".concat(formItemCls, \"-label > label\")]: {\n        height: 'auto'\n      },\n      [\"\".concat(formItemCls, \"-control\")]: {\n        width: '100%'\n      }\n    },\n    [\"\".concat(formItemCls, \"-vertical \").concat(formItemCls, \"-label,\\n      \").concat(antCls, \"-col-24\").concat(formItemCls, \"-label,\\n      \").concat(antCls, \"-col-xl-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token),\n    [\"@media (max-width: \".concat(unit(token.screenXSMax), \")\")]: [makeVerticalLayout(token), {\n      [formItemCls]: {\n        [\"\".concat(antCls, \"-col-xs-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n      }\n    }],\n    [\"@media (max-width: \".concat(unit(token.screenSMMax), \")\")]: {\n      [formItemCls]: {\n        [\"\".concat(antCls, \"-col-sm-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n      }\n    },\n    [\"@media (max-width: \".concat(unit(token.screenMDMax), \")\")]: {\n      [formItemCls]: {\n        [\"\".concat(antCls, \"-col-md-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n      }\n    },\n    [\"@media (max-width: \".concat(unit(token.screenLGMax), \")\")]: {\n      [formItemCls]: {\n        [\"\".concat(antCls, \"-col-lg-24\").concat(formItemCls, \"-label\")]: makeVerticalLayoutLabel(token)\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  labelRequiredMarkColor: token.colorError,\n  labelColor: token.colorTextHeading,\n  labelFontSize: token.fontSize,\n  labelHeight: token.controlHeight,\n  labelColonMarginInlineStart: token.marginXXS / 2,\n  labelColonMarginInlineEnd: token.marginXS,\n  itemMarginBottom: token.marginLG,\n  verticalLabelPadding: \"0 0 \".concat(token.paddingXS, \"px\"),\n  verticalLabelMargin: 0,\n  inlineItemMarginBottom: 0\n});\nexport const prepareToken = (token, rootPrefixCls) => {\n  const formToken = mergeToken(token, {\n    formItemCls: \"\".concat(token.componentCls, \"-item\"),\n    rootPrefixCls\n  });\n  return formToken;\n};\nexport default genStyleHooks('Form', (token, _ref) => {\n  let {\n    rootPrefixCls\n  } = _ref;\n  const formToken = prepareToken(token, rootPrefixCls);\n  return [genFormStyle(formToken), genFormItemStyle(formToken), genFormValidateMotionStyle(formToken), genHorizontalStyle(formToken, formToken.componentCls), genHorizontalStyle(formToken, formToken.formItemCls), genInlineStyle(formToken), genVerticalStyle(formToken), genItemVerticalStyle(formToken), genCollapseMotion(formToken), zoomIn];\n}, prepareComponentToken, {\n  // Let From style before the Grid\n  // ref https://github.com/ant-design/ant-design/issues/44386\n  order: -1000\n});", "map": {"version": 3, "names": ["unit", "resetComponent", "genCollapseMotion", "zoomIn", "genStyleHooks", "mergeToken", "genFormValidateMotionStyle", "resetForm", "token", "legend", "display", "width", "marginBottom", "marginLG", "padding", "color", "colorTextDescription", "fontSize", "fontSizeLG", "lineHeight", "border", "borderBottom", "concat", "lineWidth", "lineType", "colorBorder", "boxSizing", "height", "outline", "boxShadow", "controlOutlineWidth", "controlOutline", "output", "paddingTop", "colorText", "genFormSize", "formItemCls", "minHeight", "genFormStyle", "componentCls", "Object", "assign", "paddingInlineEnd", "paddingSM", "controlHeightSM", "controlHeightLG", "genFormItemStyle", "iconCls", "rootPrefixCls", "antCls", "labelRequiredMarkColor", "labelColor", "labelFontSize", "labelHeight", "labelColonMarginInlineStart", "labelColonMarginInlineEnd", "itemMarginBottom", "verticalAlign", "transition", "colorError", "colorWarning", "flexGrow", "overflow", "whiteSpace", "textAlign", "textWrap", "position", "alignItems", "max<PERSON><PERSON><PERSON>", "marginInlineEnd", "marginXXS", "fontFamily", "content", "marginInlineStart", "cursor", "writingMode", "marginBlock", "flexDirection", "controlHeight", "flex", "clear", "motionDurationMid", "motionEaseOut", "opacity", "visibility", "animationName", "animationDuration", "animationTimingFunction", "motionEaseOutBack", "pointerEvents", "colorSuccess", "colorPrimary", "genHorizontalStyle", "className", "min<PERSON><PERSON><PERSON>", "genInlineStyle", "inlineItemMarginBottom", "flexWrap", "margin", "makeVerticalLayoutLabel", "verticalLabelPadding", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeVerticalLayout", "genVerticalStyle", "screenXSMax", "screenSMMax", "screenMDMax", "screenLGMax", "genItemVerticalStyle", "prepareComponentToken", "colorTextHeading", "marginXS", "paddingXS", "prepareToken", "formToken", "_ref", "order"], "sources": ["G:/phpstudy_pro/WWW/admin/node_modules/antd/es/form/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genCollapseMotion, zoomIn } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genFormValidateMotionStyle from './explain';\nconst resetForm = token => ({\n  legend: {\n    display: 'block',\n    width: '100%',\n    marginBottom: token.marginLG,\n    padding: 0,\n    color: token.colorTextDescription,\n    fontSize: token.fontSizeLG,\n    lineHeight: 'inherit',\n    border: 0,\n    borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n  },\n  'input[type=\"search\"]': {\n    boxSizing: 'border-box'\n  },\n  // Position radios and checkboxes better\n  'input[type=\"radio\"], input[type=\"checkbox\"]': {\n    lineHeight: 'normal'\n  },\n  'input[type=\"file\"]': {\n    display: 'block'\n  },\n  // Make range inputs behave like textual form controls\n  'input[type=\"range\"]': {\n    display: 'block',\n    width: '100%'\n  },\n  // Make multiple select elements height not fixed\n  'select[multiple], select[size]': {\n    height: 'auto'\n  },\n  // Focus for file, radio, and checkbox\n  [`input[type='file']:focus,\n  input[type='radio']:focus,\n  input[type='checkbox']:focus`]: {\n    outline: 0,\n    boxShadow: `0 0 0 ${unit(token.controlOutlineWidth)} ${token.controlOutline}`\n  },\n  // Adjust output element\n  output: {\n    display: 'block',\n    paddingTop: 15,\n    color: token.colorText,\n    fontSize: token.fontSize,\n    lineHeight: token.lineHeight\n  }\n});\nconst genFormSize = (token, height) => {\n  const {\n    formItemCls\n  } = token;\n  return {\n    [formItemCls]: {\n      [`${formItemCls}-label > label`]: {\n        height\n      },\n      [`${formItemCls}-control-input`]: {\n        minHeight: height\n      }\n    }\n  };\n};\nconst genFormStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [token.componentCls]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), resetForm(token)), {\n      [`${componentCls}-text`]: {\n        display: 'inline-block',\n        paddingInlineEnd: token.paddingSM\n      },\n      // ================================================================\n      // =                             Size                             =\n      // ================================================================\n      '&-small': Object.assign({}, genFormSize(token, token.controlHeightSM)),\n      '&-large': Object.assign({}, genFormSize(token, token.controlHeightLG))\n    })\n  };\n};\nconst genFormItemStyle = token => {\n  const {\n    formItemCls,\n    iconCls,\n    rootPrefixCls,\n    antCls,\n    labelRequiredMarkColor,\n    labelColor,\n    labelFontSize,\n    labelHeight,\n    labelColonMarginInlineStart,\n    labelColonMarginInlineEnd,\n    itemMarginBottom\n  } = token;\n  return {\n    [formItemCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      marginBottom: itemMarginBottom,\n      verticalAlign: 'top',\n      '&-with-help': {\n        transition: 'none'\n      },\n      [`&-hidden,\n        &-hidden${antCls}-row`]: {\n        // https://github.com/ant-design/ant-design/issues/26141\n        display: 'none'\n      },\n      '&-has-warning': {\n        [`${formItemCls}-split`]: {\n          color: token.colorError\n        }\n      },\n      '&-has-error': {\n        [`${formItemCls}-split`]: {\n          color: token.colorWarning\n        }\n      },\n      // ==============================================================\n      // =                            Label                           =\n      // ==============================================================\n      [`${formItemCls}-label`]: {\n        flexGrow: 0,\n        overflow: 'hidden',\n        whiteSpace: 'nowrap',\n        textAlign: 'end',\n        verticalAlign: 'middle',\n        '&-left': {\n          textAlign: 'start'\n        },\n        '&-wrap': {\n          overflow: 'unset',\n          lineHeight: token.lineHeight,\n          whiteSpace: 'unset',\n          '> label': {\n            verticalAlign: 'middle',\n            textWrap: 'balance'\n          }\n        },\n        '> label': {\n          position: 'relative',\n          display: 'inline-flex',\n          alignItems: 'center',\n          maxWidth: '100%',\n          height: labelHeight,\n          color: labelColor,\n          fontSize: labelFontSize,\n          [`> ${iconCls}`]: {\n            fontSize: token.fontSize,\n            verticalAlign: 'top'\n          },\n          [`&${formItemCls}-required`]: {\n            '&::before': {\n              display: 'inline-block',\n              marginInlineEnd: token.marginXXS,\n              color: labelRequiredMarkColor,\n              fontSize: token.fontSize,\n              fontFamily: 'SimSun, sans-serif',\n              lineHeight: 1,\n              content: '\"*\"'\n            },\n            [`&${formItemCls}-required-mark-hidden, &${formItemCls}-required-mark-optional`]: {\n              '&::before': {\n                display: 'none'\n              }\n            }\n          },\n          // Optional mark\n          [`${formItemCls}-optional`]: {\n            display: 'inline-block',\n            marginInlineStart: token.marginXXS,\n            color: token.colorTextDescription,\n            [`&${formItemCls}-required-mark-hidden`]: {\n              display: 'none'\n            }\n          },\n          // Optional mark\n          [`${formItemCls}-tooltip`]: {\n            color: token.colorTextDescription,\n            cursor: 'help',\n            writingMode: 'horizontal-tb',\n            marginInlineStart: token.marginXXS\n          },\n          '&::after': {\n            content: '\":\"',\n            position: 'relative',\n            marginBlock: 0,\n            marginInlineStart: labelColonMarginInlineStart,\n            marginInlineEnd: labelColonMarginInlineEnd\n          },\n          [`&${formItemCls}-no-colon::after`]: {\n            content: '\"\\\\a0\"'\n          }\n        }\n      },\n      // ==============================================================\n      // =                            Input                           =\n      // ==============================================================\n      [`${formItemCls}-control`]: {\n        ['--ant-display']: 'flex',\n        flexDirection: 'column',\n        flexGrow: 1,\n        [`&:first-child:not([class^=\"'${rootPrefixCls}-col-'\"]):not([class*=\"' ${rootPrefixCls}-col-'\"])`]: {\n          width: '100%'\n        },\n        '&-input': {\n          position: 'relative',\n          display: 'flex',\n          alignItems: 'center',\n          minHeight: token.controlHeight,\n          '&-content': {\n            flex: 'auto',\n            maxWidth: '100%'\n          }\n        }\n      },\n      // ==============================================================\n      // =                           Explain                          =\n      // ==============================================================\n      [formItemCls]: {\n        '&-additional': {\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        '&-explain, &-extra': {\n          clear: 'both',\n          color: token.colorTextDescription,\n          fontSize: token.fontSize,\n          lineHeight: token.lineHeight\n        },\n        '&-explain-connected': {\n          width: '100%'\n        },\n        '&-extra': {\n          minHeight: token.controlHeightSM,\n          transition: `color ${token.motionDurationMid} ${token.motionEaseOut}` // sync input color transition\n        },\n        '&-explain': {\n          '&-error': {\n            color: token.colorError\n          },\n          '&-warning': {\n            color: token.colorWarning\n          }\n        }\n      },\n      [`&-with-help ${formItemCls}-explain`]: {\n        height: 'auto',\n        opacity: 1\n      },\n      // ==============================================================\n      // =                        Feedback Icon                       =\n      // ==============================================================\n      [`${formItemCls}-feedback-icon`]: {\n        fontSize: token.fontSize,\n        textAlign: 'center',\n        visibility: 'visible',\n        animationName: zoomIn,\n        animationDuration: token.motionDurationMid,\n        animationTimingFunction: token.motionEaseOutBack,\n        pointerEvents: 'none',\n        '&-success': {\n          color: token.colorSuccess\n        },\n        '&-error': {\n          color: token.colorError\n        },\n        '&-warning': {\n          color: token.colorWarning\n        },\n        '&-validating': {\n          color: token.colorPrimary\n        }\n      }\n    })\n  };\n};\nconst genHorizontalStyle = (token, className) => {\n  const {\n    formItemCls\n  } = token;\n  return {\n    [`${className}-horizontal`]: {\n      [`${formItemCls}-label`]: {\n        flexGrow: 0\n      },\n      [`${formItemCls}-control`]: {\n        flex: '1 1 0',\n        // https://github.com/ant-design/ant-design/issues/32777\n        // https://github.com/ant-design/ant-design/issues/33773\n        minWidth: 0\n      },\n      // Do not change this to `ant-col-24`! `-24` match all the responsive rules\n      // https://github.com/ant-design/ant-design/issues/32980\n      // https://github.com/ant-design/ant-design/issues/34903\n      // https://github.com/ant-design/ant-design/issues/44538\n      [`${formItemCls}-label[class$='-24'], ${formItemCls}-label[class*='-24 ']`]: {\n        [`& + ${formItemCls}-control`]: {\n          minWidth: 'unset'\n        }\n      }\n    }\n  };\n};\nconst genInlineStyle = token => {\n  const {\n    componentCls,\n    formItemCls,\n    inlineItemMarginBottom\n  } = token;\n  return {\n    [`${componentCls}-inline`]: {\n      display: 'flex',\n      flexWrap: 'wrap',\n      [formItemCls]: {\n        flex: 'none',\n        marginInlineEnd: token.margin,\n        marginBottom: inlineItemMarginBottom,\n        '&-row': {\n          flexWrap: 'nowrap'\n        },\n        [`> ${formItemCls}-label,\n        > ${formItemCls}-control`]: {\n          display: 'inline-block',\n          verticalAlign: 'top'\n        },\n        [`> ${formItemCls}-label`]: {\n          flex: 'none'\n        },\n        [`${componentCls}-text`]: {\n          display: 'inline-block'\n        },\n        [`${formItemCls}-has-feedback`]: {\n          display: 'inline-block'\n        }\n      }\n    }\n  };\n};\nconst makeVerticalLayoutLabel = token => ({\n  padding: token.verticalLabelPadding,\n  margin: token.verticalLabelMargin,\n  whiteSpace: 'initial',\n  textAlign: 'start',\n  '> label': {\n    margin: 0,\n    '&::after': {\n      // https://github.com/ant-design/ant-design/issues/43538\n      visibility: 'hidden'\n    }\n  }\n});\nconst makeVerticalLayout = token => {\n  const {\n    componentCls,\n    formItemCls,\n    rootPrefixCls\n  } = token;\n  return {\n    [`${formItemCls} ${formItemCls}-label`]: makeVerticalLayoutLabel(token),\n    // ref: https://github.com/ant-design/ant-design/issues/45122\n    [`${componentCls}:not(${componentCls}-inline)`]: {\n      [formItemCls]: {\n        flexWrap: 'wrap',\n        [`${formItemCls}-label, ${formItemCls}-control`]: {\n          // When developer pass `xs: { span }`,\n          // It should follow the `xs` screen config\n          // ref: https://github.com/ant-design/ant-design/issues/44386\n          [`&:not([class*=\" ${rootPrefixCls}-col-xs\"])`]: {\n            flex: '0 0 100%',\n            maxWidth: '100%'\n          }\n        }\n      }\n    }\n  };\n};\nconst genVerticalStyle = token => {\n  const {\n    componentCls,\n    formItemCls,\n    antCls\n  } = token;\n  return {\n    [`${componentCls}-vertical`]: {\n      [`${formItemCls}:not(${formItemCls}-horizontal)`]: {\n        [`${formItemCls}-row`]: {\n          flexDirection: 'column'\n        },\n        [`${formItemCls}-label > label`]: {\n          height: 'auto'\n        },\n        [`${formItemCls}-control`]: {\n          width: '100%'\n        },\n        [`${formItemCls}-label,\n        ${antCls}-col-24${formItemCls}-label,\n        ${antCls}-col-xl-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n      }\n    },\n    [`@media (max-width: ${unit(token.screenXSMax)})`]: [makeVerticalLayout(token), {\n      [componentCls]: {\n        [`${formItemCls}:not(${formItemCls}-horizontal)`]: {\n          [`${antCls}-col-xs-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n        }\n      }\n    }],\n    [`@media (max-width: ${unit(token.screenSMMax)})`]: {\n      [componentCls]: {\n        [`${formItemCls}:not(${formItemCls}-horizontal)`]: {\n          [`${antCls}-col-sm-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n        }\n      }\n    },\n    [`@media (max-width: ${unit(token.screenMDMax)})`]: {\n      [componentCls]: {\n        [`${formItemCls}:not(${formItemCls}-horizontal)`]: {\n          [`${antCls}-col-md-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n        }\n      }\n    },\n    [`@media (max-width: ${unit(token.screenLGMax)})`]: {\n      [componentCls]: {\n        [`${formItemCls}:not(${formItemCls}-horizontal)`]: {\n          [`${antCls}-col-lg-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n        }\n      }\n    }\n  };\n};\nconst genItemVerticalStyle = token => {\n  const {\n    formItemCls,\n    antCls\n  } = token;\n  return {\n    [`${formItemCls}-vertical`]: {\n      [`${formItemCls}-row`]: {\n        flexDirection: 'column'\n      },\n      [`${formItemCls}-label > label`]: {\n        height: 'auto'\n      },\n      [`${formItemCls}-control`]: {\n        width: '100%'\n      }\n    },\n    [`${formItemCls}-vertical ${formItemCls}-label,\n      ${antCls}-col-24${formItemCls}-label,\n      ${antCls}-col-xl-24${formItemCls}-label`]: makeVerticalLayoutLabel(token),\n    [`@media (max-width: ${unit(token.screenXSMax)})`]: [makeVerticalLayout(token), {\n      [formItemCls]: {\n        [`${antCls}-col-xs-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n      }\n    }],\n    [`@media (max-width: ${unit(token.screenSMMax)})`]: {\n      [formItemCls]: {\n        [`${antCls}-col-sm-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n      }\n    },\n    [`@media (max-width: ${unit(token.screenMDMax)})`]: {\n      [formItemCls]: {\n        [`${antCls}-col-md-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n      }\n    },\n    [`@media (max-width: ${unit(token.screenLGMax)})`]: {\n      [formItemCls]: {\n        [`${antCls}-col-lg-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  labelRequiredMarkColor: token.colorError,\n  labelColor: token.colorTextHeading,\n  labelFontSize: token.fontSize,\n  labelHeight: token.controlHeight,\n  labelColonMarginInlineStart: token.marginXXS / 2,\n  labelColonMarginInlineEnd: token.marginXS,\n  itemMarginBottom: token.marginLG,\n  verticalLabelPadding: `0 0 ${token.paddingXS}px`,\n  verticalLabelMargin: 0,\n  inlineItemMarginBottom: 0\n});\nexport const prepareToken = (token, rootPrefixCls) => {\n  const formToken = mergeToken(token, {\n    formItemCls: `${token.componentCls}-item`,\n    rootPrefixCls\n  });\n  return formToken;\n};\nexport default genStyleHooks('Form', (token, {\n  rootPrefixCls\n}) => {\n  const formToken = prepareToken(token, rootPrefixCls);\n  return [genFormStyle(formToken), genFormItemStyle(formToken), genFormValidateMotionStyle(formToken), genHorizontalStyle(formToken, formToken.componentCls), genHorizontalStyle(formToken, formToken.formItemCls), genInlineStyle(formToken), genVerticalStyle(formToken), genItemVerticalStyle(formToken), genCollapseMotion(formToken), zoomIn];\n}, prepareComponentToken, {\n  // Let From style before the Grid\n  // ref https://github.com/ant-design/ant-design/issues/44386\n  order: -1000\n});"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,iBAAiB,EAAEC,MAAM,QAAQ,oBAAoB;AAC9D,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,OAAOC,0BAA0B,MAAM,WAAW;AAClD,MAAMC,SAAS,GAAGC,KAAK,KAAK;EAC1BC,MAAM,EAAE;IACNC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,MAAM;IACbC,YAAY,EAAEJ,KAAK,CAACK,QAAQ;IAC5BC,OAAO,EAAE,CAAC;IACVC,KAAK,EAAEP,KAAK,CAACQ,oBAAoB;IACjCC,QAAQ,EAAET,KAAK,CAACU,UAAU;IAC1BC,UAAU,EAAE,SAAS;IACrBC,MAAM,EAAE,CAAC;IACTC,YAAY,KAAAC,MAAA,CAAKtB,IAAI,CAACQ,KAAK,CAACe,SAAS,CAAC,OAAAD,MAAA,CAAId,KAAK,CAACgB,QAAQ,OAAAF,MAAA,CAAId,KAAK,CAACiB,WAAW;EAC/E,CAAC;EACD,sBAAsB,EAAE;IACtBC,SAAS,EAAE;EACb,CAAC;EACD;EACA,6CAA6C,EAAE;IAC7CP,UAAU,EAAE;EACd,CAAC;EACD,oBAAoB,EAAE;IACpBT,OAAO,EAAE;EACX,CAAC;EACD;EACA,qBAAqB,EAAE;IACrBA,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACD;EACA,gCAAgC,EAAE;IAChCgB,MAAM,EAAE;EACV,CAAC;EACD;EACA,6FAEgC;IAC9BC,OAAO,EAAE,CAAC;IACVC,SAAS,WAAAP,MAAA,CAAWtB,IAAI,CAACQ,KAAK,CAACsB,mBAAmB,CAAC,OAAAR,MAAA,CAAId,KAAK,CAACuB,cAAc;EAC7E,CAAC;EACD;EACAC,MAAM,EAAE;IACNtB,OAAO,EAAE,OAAO;IAChBuB,UAAU,EAAE,EAAE;IACdlB,KAAK,EAAEP,KAAK,CAAC0B,SAAS;IACtBjB,QAAQ,EAAET,KAAK,CAACS,QAAQ;IACxBE,UAAU,EAAEX,KAAK,CAACW;EACpB;AACF,CAAC,CAAC;AACF,MAAMgB,WAAW,GAAGA,CAAC3B,KAAK,EAAEmB,MAAM,KAAK;EACrC,MAAM;IACJS;EACF,CAAC,GAAG5B,KAAK;EACT,OAAO;IACL,CAAC4B,WAAW,GAAG;MACb,IAAAd,MAAA,CAAIc,WAAW,sBAAmB;QAChCT;MACF,CAAC;MACD,IAAAL,MAAA,CAAIc,WAAW,sBAAmB;QAChCC,SAAS,EAAEV;MACb;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMW,YAAY,GAAG9B,KAAK,IAAI;EAC5B,MAAM;IACJ+B;EACF,CAAC,GAAG/B,KAAK;EACT,OAAO;IACL,CAACA,KAAK,CAAC+B,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExC,cAAc,CAACO,KAAK,CAAC,CAAC,EAAED,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE;MAC7G,IAAAc,MAAA,CAAIiB,YAAY,aAAU;QACxB7B,OAAO,EAAE,cAAc;QACvBgC,gBAAgB,EAAElC,KAAK,CAACmC;MAC1B,CAAC;MACD;MACA;MACA;MACA,SAAS,EAAEH,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,WAAW,CAAC3B,KAAK,EAAEA,KAAK,CAACoC,eAAe,CAAC,CAAC;MACvE,SAAS,EAAEJ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,WAAW,CAAC3B,KAAK,EAAEA,KAAK,CAACqC,eAAe,CAAC;IACxE,CAAC;EACH,CAAC;AACH,CAAC;AACD,MAAMC,gBAAgB,GAAGtC,KAAK,IAAI;EAChC,MAAM;IACJ4B,WAAW;IACXW,OAAO;IACPC,aAAa;IACbC,MAAM;IACNC,sBAAsB;IACtBC,UAAU;IACVC,aAAa;IACbC,WAAW;IACXC,2BAA2B;IAC3BC,yBAAyB;IACzBC;EACF,CAAC,GAAGhD,KAAK;EACT,OAAO;IACL,CAAC4B,WAAW,GAAGI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExC,cAAc,CAACO,KAAK,CAAC,CAAC,EAAE;MACrEI,YAAY,EAAE4C,gBAAgB;MAC9BC,aAAa,EAAE,KAAK;MACpB,aAAa,EAAE;QACbC,UAAU,EAAE;MACd,CAAC;MACD,+BAAApC,MAAA,CACY2B,MAAM,YAAS;QACzB;QACAvC,OAAO,EAAE;MACX,CAAC;MACD,eAAe,EAAE;QACf,IAAAY,MAAA,CAAIc,WAAW,cAAW;UACxBrB,KAAK,EAAEP,KAAK,CAACmD;QACf;MACF,CAAC;MACD,aAAa,EAAE;QACb,IAAArC,MAAA,CAAIc,WAAW,cAAW;UACxBrB,KAAK,EAAEP,KAAK,CAACoD;QACf;MACF,CAAC;MACD;MACA;MACA;MACA,IAAAtC,MAAA,CAAIc,WAAW,cAAW;QACxByB,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,KAAK;QAChBP,aAAa,EAAE,QAAQ;QACvB,QAAQ,EAAE;UACRO,SAAS,EAAE;QACb,CAAC;QACD,QAAQ,EAAE;UACRF,QAAQ,EAAE,OAAO;UACjB3C,UAAU,EAAEX,KAAK,CAACW,UAAU;UAC5B4C,UAAU,EAAE,OAAO;UACnB,SAAS,EAAE;YACTN,aAAa,EAAE,QAAQ;YACvBQ,QAAQ,EAAE;UACZ;QACF,CAAC;QACD,SAAS,EAAE;UACTC,QAAQ,EAAE,UAAU;UACpBxD,OAAO,EAAE,aAAa;UACtByD,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,MAAM;UAChBzC,MAAM,EAAE0B,WAAW;UACnBtC,KAAK,EAAEoC,UAAU;UACjBlC,QAAQ,EAAEmC,aAAa;UACvB,MAAA9B,MAAA,CAAMyB,OAAO,IAAK;YAChB9B,QAAQ,EAAET,KAAK,CAACS,QAAQ;YACxBwC,aAAa,EAAE;UACjB,CAAC;UACD,KAAAnC,MAAA,CAAKc,WAAW,iBAAc;YAC5B,WAAW,EAAE;cACX1B,OAAO,EAAE,cAAc;cACvB2D,eAAe,EAAE7D,KAAK,CAAC8D,SAAS;cAChCvD,KAAK,EAAEmC,sBAAsB;cAC7BjC,QAAQ,EAAET,KAAK,CAACS,QAAQ;cACxBsD,UAAU,EAAE,oBAAoB;cAChCpD,UAAU,EAAE,CAAC;cACbqD,OAAO,EAAE;YACX,CAAC;YACD,KAAAlD,MAAA,CAAKc,WAAW,8BAAAd,MAAA,CAA2Bc,WAAW,+BAA4B;cAChF,WAAW,EAAE;gBACX1B,OAAO,EAAE;cACX;YACF;UACF,CAAC;UACD;UACA,IAAAY,MAAA,CAAIc,WAAW,iBAAc;YAC3B1B,OAAO,EAAE,cAAc;YACvB+D,iBAAiB,EAAEjE,KAAK,CAAC8D,SAAS;YAClCvD,KAAK,EAAEP,KAAK,CAACQ,oBAAoB;YACjC,KAAAM,MAAA,CAAKc,WAAW,6BAA0B;cACxC1B,OAAO,EAAE;YACX;UACF,CAAC;UACD;UACA,IAAAY,MAAA,CAAIc,WAAW,gBAAa;YAC1BrB,KAAK,EAAEP,KAAK,CAACQ,oBAAoB;YACjC0D,MAAM,EAAE,MAAM;YACdC,WAAW,EAAE,eAAe;YAC5BF,iBAAiB,EAAEjE,KAAK,CAAC8D;UAC3B,CAAC;UACD,UAAU,EAAE;YACVE,OAAO,EAAE,KAAK;YACdN,QAAQ,EAAE,UAAU;YACpBU,WAAW,EAAE,CAAC;YACdH,iBAAiB,EAAEnB,2BAA2B;YAC9Ce,eAAe,EAAEd;UACnB,CAAC;UACD,KAAAjC,MAAA,CAAKc,WAAW,wBAAqB;YACnCoC,OAAO,EAAE;UACX;QACF;MACF,CAAC;MACD;MACA;MACA;MACA,IAAAlD,MAAA,CAAIc,WAAW,gBAAa;QAC1B,CAAC,eAAe,GAAG,MAAM;QACzByC,aAAa,EAAE,QAAQ;QACvBhB,QAAQ,EAAE,CAAC;QACX,iCAAAvC,MAAA,CAAgC0B,aAAa,iCAAA1B,MAAA,CAA4B0B,aAAa,kBAAc;UAClGrC,KAAK,EAAE;QACT,CAAC;QACD,SAAS,EAAE;UACTuD,QAAQ,EAAE,UAAU;UACpBxD,OAAO,EAAE,MAAM;UACfyD,UAAU,EAAE,QAAQ;UACpB9B,SAAS,EAAE7B,KAAK,CAACsE,aAAa;UAC9B,WAAW,EAAE;YACXC,IAAI,EAAE,MAAM;YACZX,QAAQ,EAAE;UACZ;QACF;MACF,CAAC;MACD;MACA;MACA;MACA,CAAChC,WAAW,GAAG;QACb,cAAc,EAAE;UACd1B,OAAO,EAAE,MAAM;UACfmE,aAAa,EAAE;QACjB,CAAC;QACD,oBAAoB,EAAE;UACpBG,KAAK,EAAE,MAAM;UACbjE,KAAK,EAAEP,KAAK,CAACQ,oBAAoB;UACjCC,QAAQ,EAAET,KAAK,CAACS,QAAQ;UACxBE,UAAU,EAAEX,KAAK,CAACW;QACpB,CAAC;QACD,qBAAqB,EAAE;UACrBR,KAAK,EAAE;QACT,CAAC;QACD,SAAS,EAAE;UACT0B,SAAS,EAAE7B,KAAK,CAACoC,eAAe;UAChCc,UAAU,WAAApC,MAAA,CAAWd,KAAK,CAACyE,iBAAiB,OAAA3D,MAAA,CAAId,KAAK,CAAC0E,aAAa,CAAE,CAAC;QACxE,CAAC;QACD,WAAW,EAAE;UACX,SAAS,EAAE;YACTnE,KAAK,EAAEP,KAAK,CAACmD;UACf,CAAC;UACD,WAAW,EAAE;YACX5C,KAAK,EAAEP,KAAK,CAACoD;UACf;QACF;MACF,CAAC;MACD,gBAAAtC,MAAA,CAAgBc,WAAW,gBAAa;QACtCT,MAAM,EAAE,MAAM;QACdwD,OAAO,EAAE;MACX,CAAC;MACD;MACA;MACA;MACA,IAAA7D,MAAA,CAAIc,WAAW,sBAAmB;QAChCnB,QAAQ,EAAET,KAAK,CAACS,QAAQ;QACxB+C,SAAS,EAAE,QAAQ;QACnBoB,UAAU,EAAE,SAAS;QACrBC,aAAa,EAAElF,MAAM;QACrBmF,iBAAiB,EAAE9E,KAAK,CAACyE,iBAAiB;QAC1CM,uBAAuB,EAAE/E,KAAK,CAACgF,iBAAiB;QAChDC,aAAa,EAAE,MAAM;QACrB,WAAW,EAAE;UACX1E,KAAK,EAAEP,KAAK,CAACkF;QACf,CAAC;QACD,SAAS,EAAE;UACT3E,KAAK,EAAEP,KAAK,CAACmD;QACf,CAAC;QACD,WAAW,EAAE;UACX5C,KAAK,EAAEP,KAAK,CAACoD;QACf,CAAC;QACD,cAAc,EAAE;UACd7C,KAAK,EAAEP,KAAK,CAACmF;QACf;MACF;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,MAAMC,kBAAkB,GAAGA,CAACpF,KAAK,EAAEqF,SAAS,KAAK;EAC/C,MAAM;IACJzD;EACF,CAAC,GAAG5B,KAAK;EACT,OAAO;IACL,IAAAc,MAAA,CAAIuE,SAAS,mBAAgB;MAC3B,IAAAvE,MAAA,CAAIc,WAAW,cAAW;QACxByB,QAAQ,EAAE;MACZ,CAAC;MACD,IAAAvC,MAAA,CAAIc,WAAW,gBAAa;QAC1B2C,IAAI,EAAE,OAAO;QACb;QACA;QACAe,QAAQ,EAAE;MACZ,CAAC;MACD;MACA;MACA;MACA;MACA,IAAAxE,MAAA,CAAIc,WAAW,4BAAAd,MAAA,CAAyBc,WAAW,6BAA0B;QAC3E,QAAAd,MAAA,CAAQc,WAAW,gBAAa;UAC9B0D,QAAQ,EAAE;QACZ;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMC,cAAc,GAAGvF,KAAK,IAAI;EAC9B,MAAM;IACJ+B,YAAY;IACZH,WAAW;IACX4D;EACF,CAAC,GAAGxF,KAAK;EACT,OAAO;IACL,IAAAc,MAAA,CAAIiB,YAAY,eAAY;MAC1B7B,OAAO,EAAE,MAAM;MACfuF,QAAQ,EAAE,MAAM;MAChB,CAAC7D,WAAW,GAAG;QACb2C,IAAI,EAAE,MAAM;QACZV,eAAe,EAAE7D,KAAK,CAAC0F,MAAM;QAC7BtF,YAAY,EAAEoF,sBAAsB;QACpC,OAAO,EAAE;UACPC,QAAQ,EAAE;QACZ,CAAC;QACD,MAAA3E,MAAA,CAAMc,WAAW,yBAAAd,MAAA,CACbc,WAAW,gBAAa;UAC1B1B,OAAO,EAAE,cAAc;UACvB+C,aAAa,EAAE;QACjB,CAAC;QACD,MAAAnC,MAAA,CAAMc,WAAW,cAAW;UAC1B2C,IAAI,EAAE;QACR,CAAC;QACD,IAAAzD,MAAA,CAAIiB,YAAY,aAAU;UACxB7B,OAAO,EAAE;QACX,CAAC;QACD,IAAAY,MAAA,CAAIc,WAAW,qBAAkB;UAC/B1B,OAAO,EAAE;QACX;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMyF,uBAAuB,GAAG3F,KAAK,KAAK;EACxCM,OAAO,EAAEN,KAAK,CAAC4F,oBAAoB;EACnCF,MAAM,EAAE1F,KAAK,CAAC6F,mBAAmB;EACjCtC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,OAAO;EAClB,SAAS,EAAE;IACTkC,MAAM,EAAE,CAAC;IACT,UAAU,EAAE;MACV;MACAd,UAAU,EAAE;IACd;EACF;AACF,CAAC,CAAC;AACF,MAAMkB,kBAAkB,GAAG9F,KAAK,IAAI;EAClC,MAAM;IACJ+B,YAAY;IACZH,WAAW;IACXY;EACF,CAAC,GAAGxC,KAAK;EACT,OAAO;IACL,IAAAc,MAAA,CAAIc,WAAW,OAAAd,MAAA,CAAIc,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK,CAAC;IACvE;IACA,IAAAc,MAAA,CAAIiB,YAAY,WAAAjB,MAAA,CAAQiB,YAAY,gBAAa;MAC/C,CAACH,WAAW,GAAG;QACb6D,QAAQ,EAAE,MAAM;QAChB,IAAA3E,MAAA,CAAIc,WAAW,cAAAd,MAAA,CAAWc,WAAW,gBAAa;UAChD;UACA;UACA;UACA,qBAAAd,MAAA,CAAoB0B,aAAa,mBAAe;YAC9C+B,IAAI,EAAE,UAAU;YAChBX,QAAQ,EAAE;UACZ;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMmC,gBAAgB,GAAG/F,KAAK,IAAI;EAChC,MAAM;IACJ+B,YAAY;IACZH,WAAW;IACXa;EACF,CAAC,GAAGzC,KAAK;EACT,OAAO;IACL,IAAAc,MAAA,CAAIiB,YAAY,iBAAc;MAC5B,IAAAjB,MAAA,CAAIc,WAAW,WAAAd,MAAA,CAAQc,WAAW,oBAAiB;QACjD,IAAAd,MAAA,CAAIc,WAAW,YAAS;UACtByC,aAAa,EAAE;QACjB,CAAC;QACD,IAAAvD,MAAA,CAAIc,WAAW,sBAAmB;UAChCT,MAAM,EAAE;QACV,CAAC;QACD,IAAAL,MAAA,CAAIc,WAAW,gBAAa;UAC1BzB,KAAK,EAAE;QACT,CAAC;QACD,IAAAW,MAAA,CAAIc,WAAW,uBAAAd,MAAA,CACb2B,MAAM,aAAA3B,MAAA,CAAUc,WAAW,uBAAAd,MAAA,CAC3B2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;MAC1E;IACF,CAAC;IACD,uBAAAc,MAAA,CAAuBtB,IAAI,CAACQ,KAAK,CAACgG,WAAW,CAAC,SAAM,CAACF,kBAAkB,CAAC9F,KAAK,CAAC,EAAE;MAC9E,CAAC+B,YAAY,GAAG;QACd,IAAAjB,MAAA,CAAIc,WAAW,WAAAd,MAAA,CAAQc,WAAW,oBAAiB;UACjD,IAAAd,MAAA,CAAI2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;QAC5E;MACF;IACF,CAAC,CAAC;IACF,uBAAAc,MAAA,CAAuBtB,IAAI,CAACQ,KAAK,CAACiG,WAAW,CAAC,SAAM;MAClD,CAAClE,YAAY,GAAG;QACd,IAAAjB,MAAA,CAAIc,WAAW,WAAAd,MAAA,CAAQc,WAAW,oBAAiB;UACjD,IAAAd,MAAA,CAAI2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;QAC5E;MACF;IACF,CAAC;IACD,uBAAAc,MAAA,CAAuBtB,IAAI,CAACQ,KAAK,CAACkG,WAAW,CAAC,SAAM;MAClD,CAACnE,YAAY,GAAG;QACd,IAAAjB,MAAA,CAAIc,WAAW,WAAAd,MAAA,CAAQc,WAAW,oBAAiB;UACjD,IAAAd,MAAA,CAAI2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;QAC5E;MACF;IACF,CAAC;IACD,uBAAAc,MAAA,CAAuBtB,IAAI,CAACQ,KAAK,CAACmG,WAAW,CAAC,SAAM;MAClD,CAACpE,YAAY,GAAG;QACd,IAAAjB,MAAA,CAAIc,WAAW,WAAAd,MAAA,CAAQc,WAAW,oBAAiB;UACjD,IAAAd,MAAA,CAAI2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;QAC5E;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMoG,oBAAoB,GAAGpG,KAAK,IAAI;EACpC,MAAM;IACJ4B,WAAW;IACXa;EACF,CAAC,GAAGzC,KAAK;EACT,OAAO;IACL,IAAAc,MAAA,CAAIc,WAAW,iBAAc;MAC3B,IAAAd,MAAA,CAAIc,WAAW,YAAS;QACtByC,aAAa,EAAE;MACjB,CAAC;MACD,IAAAvD,MAAA,CAAIc,WAAW,sBAAmB;QAChCT,MAAM,EAAE;MACV,CAAC;MACD,IAAAL,MAAA,CAAIc,WAAW,gBAAa;QAC1BzB,KAAK,EAAE;MACT;IACF,CAAC;IACD,IAAAW,MAAA,CAAIc,WAAW,gBAAAd,MAAA,CAAac,WAAW,qBAAAd,MAAA,CACnC2B,MAAM,aAAA3B,MAAA,CAAUc,WAAW,qBAAAd,MAAA,CAC3B2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK,CAAC;IAC3E,uBAAAc,MAAA,CAAuBtB,IAAI,CAACQ,KAAK,CAACgG,WAAW,CAAC,SAAM,CAACF,kBAAkB,CAAC9F,KAAK,CAAC,EAAE;MAC9E,CAAC4B,WAAW,GAAG;QACb,IAAAd,MAAA,CAAI2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;MAC5E;IACF,CAAC,CAAC;IACF,uBAAAc,MAAA,CAAuBtB,IAAI,CAACQ,KAAK,CAACiG,WAAW,CAAC,SAAM;MAClD,CAACrE,WAAW,GAAG;QACb,IAAAd,MAAA,CAAI2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;MAC5E;IACF,CAAC;IACD,uBAAAc,MAAA,CAAuBtB,IAAI,CAACQ,KAAK,CAACkG,WAAW,CAAC,SAAM;MAClD,CAACtE,WAAW,GAAG;QACb,IAAAd,MAAA,CAAI2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;MAC5E;IACF,CAAC;IACD,uBAAAc,MAAA,CAAuBtB,IAAI,CAACQ,KAAK,CAACmG,WAAW,CAAC,SAAM;MAClD,CAACvE,WAAW,GAAG;QACb,IAAAd,MAAA,CAAI2B,MAAM,gBAAA3B,MAAA,CAAac,WAAW,cAAW+D,uBAAuB,CAAC3F,KAAK;MAC5E;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMqG,qBAAqB,GAAGrG,KAAK,KAAK;EAC7C0C,sBAAsB,EAAE1C,KAAK,CAACmD,UAAU;EACxCR,UAAU,EAAE3C,KAAK,CAACsG,gBAAgB;EAClC1D,aAAa,EAAE5C,KAAK,CAACS,QAAQ;EAC7BoC,WAAW,EAAE7C,KAAK,CAACsE,aAAa;EAChCxB,2BAA2B,EAAE9C,KAAK,CAAC8D,SAAS,GAAG,CAAC;EAChDf,yBAAyB,EAAE/C,KAAK,CAACuG,QAAQ;EACzCvD,gBAAgB,EAAEhD,KAAK,CAACK,QAAQ;EAChCuF,oBAAoB,SAAA9E,MAAA,CAASd,KAAK,CAACwG,SAAS,OAAI;EAChDX,mBAAmB,EAAE,CAAC;EACtBL,sBAAsB,EAAE;AAC1B,CAAC,CAAC;AACF,OAAO,MAAMiB,YAAY,GAAGA,CAACzG,KAAK,EAAEwC,aAAa,KAAK;EACpD,MAAMkE,SAAS,GAAG7G,UAAU,CAACG,KAAK,EAAE;IAClC4B,WAAW,KAAAd,MAAA,CAAKd,KAAK,CAAC+B,YAAY,UAAO;IACzCS;EACF,CAAC,CAAC;EACF,OAAOkE,SAAS;AAClB,CAAC;AACD,eAAe9G,aAAa,CAAC,MAAM,EAAE,CAACI,KAAK,EAAA2G,IAAA,KAErC;EAAA,IAFuC;IAC3CnE;EACF,CAAC,GAAAmE,IAAA;EACC,MAAMD,SAAS,GAAGD,YAAY,CAACzG,KAAK,EAAEwC,aAAa,CAAC;EACpD,OAAO,CAACV,YAAY,CAAC4E,SAAS,CAAC,EAAEpE,gBAAgB,CAACoE,SAAS,CAAC,EAAE5G,0BAA0B,CAAC4G,SAAS,CAAC,EAAEtB,kBAAkB,CAACsB,SAAS,EAAEA,SAAS,CAAC3E,YAAY,CAAC,EAAEqD,kBAAkB,CAACsB,SAAS,EAAEA,SAAS,CAAC9E,WAAW,CAAC,EAAE2D,cAAc,CAACmB,SAAS,CAAC,EAAEX,gBAAgB,CAACW,SAAS,CAAC,EAAEN,oBAAoB,CAACM,SAAS,CAAC,EAAEhH,iBAAiB,CAACgH,SAAS,CAAC,EAAE/G,MAAM,CAAC;AAClV,CAAC,EAAE0G,qBAAqB,EAAE;EACxB;EACA;EACAO,KAAK,EAAE,CAAC;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}